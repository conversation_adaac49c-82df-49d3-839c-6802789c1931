"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[7975],{82887:(t,n,e)=>{e.d(n,{A:()=>i});function i(t,n){return t==null||n==null?NaN:t<n?-1:t>n?1:t>=n?0:NaN}},71363:(t,n,e)=>{e.d(n,{Ay:()=>c,Jj:()=>s,ah:()=>u});var i=e(82887);var r=e(9791);var a=e(40168);const o=(0,r.A)(i.A);const s=o.right;const u=o.left;const h=(0,r.A)(a.A).center;const c=s},9791:(t,n,e)=>{e.d(n,{A:()=>a});var i=e(82887);function r(t,n){return t==null||n==null?NaN:n<t?-1:n>t?1:n>=t?0:NaN}function a(t){let n,e,a;if(t.length!==2){n=i.A;e=(n,e)=>(0,i.A)(t(n),e);a=(n,e)=>t(n)-e}else{n=t===i.A||t===r?t:o;e=t;a=t}function s(t,i,r=0,a=t.length){if(r<a){if(n(i,i)!==0)return a;do{const n=r+a>>>1;if(e(t[n],i)<0)r=n+1;else a=n}while(r<a)}return r}function u(t,i,r=0,a=t.length){if(r<a){if(n(i,i)!==0)return a;do{const n=r+a>>>1;if(e(t[n],i)<=0)r=n+1;else a=n}while(r<a)}return r}function h(t,n,e=0,i=t.length){const r=s(t,n,e,i-1);return r>e&&a(t[r-1],n)>-a(t[r],n)?r-1:r}return{left:s,center:h,right:u}}function o(){return 0}},21671:(t,n,e)=>{e.d(n,{A:()=>i});function i(t,n){let e;if(n===undefined){for(const n of t){if(n!=null&&(e<n||e===undefined&&n>=n)){e=n}}}else{let i=-1;for(let r of t){if((r=n(r,++i,t))!=null&&(e<r||e===undefined&&r>=r)){e=r}}}return e}},44317:(t,n,e)=>{e.d(n,{A:()=>i});function i(t,n){let e;if(n===undefined){for(const n of t){if(n!=null&&(e>n||e===undefined&&n>=n)){e=n}}}else{let i=-1;for(let r of t){if((r=n(r,++i,t))!=null&&(e>r||e===undefined&&r>=r)){e=r}}}return e}},40168:(t,n,e)=>{e.d(n,{A:()=>i,n:()=>r});function i(t){return t===null?NaN:+t}function*r(t,n){if(n===undefined){for(let n of t){if(n!=null&&(n=+n)>=n){yield n}}}else{let e=-1;for(let i of t){if((i=n(i,++e,t))!=null&&(i=+i)>=i){yield i}}}}},18312:(t,n,e)=>{e.d(n,{A:()=>i});function i(t,n,e){t=+t,n=+n,e=(r=arguments.length)<2?(n=t,t=0,1):r<3?1:+e;var i=-1,r=Math.max(0,Math.ceil((n-t)/e))|0,a=new Array(r);while(++i<r){a[i]=t+i*e}return a}},97119:(t,n,e)=>{e.d(n,{Ay:()=>s,lq:()=>u,sG:()=>h});const i=Math.sqrt(50),r=Math.sqrt(10),a=Math.sqrt(2);function o(t,n,e){const s=(n-t)/Math.max(0,e),u=Math.floor(Math.log10(s)),h=s/Math.pow(10,u),c=h>=i?10:h>=r?5:h>=a?2:1;let l,f,_;if(u<0){_=Math.pow(10,-u)/c;l=Math.round(t*_);f=Math.round(n*_);if(l/_<t)++l;if(f/_>n)--f;_=-_}else{_=Math.pow(10,u)*c;l=Math.round(t/_);f=Math.round(n/_);if(l*_<t)++l;if(f*_>n)--f}if(f<l&&.5<=e&&e<2)return o(t,n,e*2);return[l,f,_]}function s(t,n,e){n=+n,t=+t,e=+e;if(!(e>0))return[];if(t===n)return[t];const i=n<t,[r,a,s]=i?o(n,t,e):o(t,n,e);if(!(a>=r))return[];const u=a-r+1,h=new Array(u);if(i){if(s<0)for(let t=0;t<u;++t)h[t]=(a-t)/-s;else for(let t=0;t<u;++t)h[t]=(a-t)*s}else{if(s<0)for(let t=0;t<u;++t)h[t]=(r+t)/-s;else for(let t=0;t<u;++t)h[t]=(r+t)*s}return h}function u(t,n,e){n=+n,t=+t,e=+e;return o(t,n,e)[2]}function h(t,n,e){n=+n,t=+t,e=+e;const i=n<t,r=i?u(n,t,e):u(t,n,e);return(i?-1:1)*(r<0?1/-r:r)}},33844:(t,n,e)=>{e.d(n,{Ay:()=>A,Gw:()=>N,KI:()=>R,Q1:()=>r,Qh:()=>k,Uw:()=>o,b:()=>T,ef:()=>a});var i=e(47592);function r(){}var a=.7;var o=1/a;var s="\\s*([+-]?\\d+)\\s*",u="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",h="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",c=/^#([0-9a-f]{3,8})$/,l=new RegExp(`^rgb\\(${s},${s},${s}\\)$`),f=new RegExp(`^rgb\\(${h},${h},${h}\\)$`),_=new RegExp(`^rgba\\(${s},${s},${s},${u}\\)$`),p=new RegExp(`^rgba\\(${h},${h},${h},${u}\\)$`),y=new RegExp(`^hsl\\(${u},${h},${h}\\)$`),d=new RegExp(`^hsla\\(${u},${h},${h},${u}\\)$`);var g={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};(0,i.A)(r,A,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:v,formatHex:v,formatHex8:x,formatHsl:w,formatRgb:m,toString:m});function v(){return this.rgb().formatHex()}function x(){return this.rgb().formatHex8()}function w(){return E(this).formatHsl()}function m(){return this.rgb().formatRgb()}function A(t){var n,e;t=(t+"").trim().toLowerCase();return(n=c.exec(t))?(e=n[1].length,n=parseInt(n[1],16),e===6?b(n):e===3?new N(n>>8&15|n>>4&240,n>>4&15|n&240,(n&15)<<4|n&15,1):e===8?M(n>>24&255,n>>16&255,n>>8&255,(n&255)/255):e===4?M(n>>12&15|n>>8&240,n>>8&15|n>>4&240,n>>4&15|n&240,((n&15)<<4|n&15)/255):null):(n=l.exec(t))?new N(n[1],n[2],n[3],1):(n=f.exec(t))?new N(n[1]*255/100,n[2]*255/100,n[3]*255/100,1):(n=_.exec(t))?M(n[1],n[2],n[3],n[4]):(n=p.exec(t))?M(n[1]*255/100,n[2]*255/100,n[3]*255/100,n[4]):(n=y.exec(t))?P(n[1],n[2]/100,n[3]/100,1):(n=d.exec(t))?P(n[1],n[2]/100,n[3]/100,n[4]):g.hasOwnProperty(t)?b(g[t]):t==="transparent"?new N(NaN,NaN,NaN,0):null}function b(t){return new N(t>>16&255,t>>8&255,t&255,1)}function M(t,n,e,i){if(i<=0)t=n=e=NaN;return new N(t,n,e,i)}function T(t){if(!(t instanceof r))t=A(t);if(!t)return new N;t=t.rgb();return new N(t.r,t.g,t.b,t.opacity)}function k(t,n,e,i){return arguments.length===1?T(t):new N(t,n,e,i==null?1:i)}function N(t,n,e,i){this.r=+t;this.g=+n;this.b=+e;this.opacity=+i}(0,i.A)(N,k,(0,i.X)(r,{brighter(t){t=t==null?o:Math.pow(o,t);return new N(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){t=t==null?a:Math.pow(a,t);return new N(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new N(D(this.r),D(this.g),D(this.b),F(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&(-.5<=this.g&&this.g<255.5)&&(-.5<=this.b&&this.b<255.5)&&(0<=this.opacity&&this.opacity<=1)},hex:C,formatHex:C,formatHex8:$,formatRgb:U,toString:U}));function C(){return`#${S(this.r)}${S(this.g)}${S(this.b)}`}function $(){return`#${S(this.r)}${S(this.g)}${S(this.b)}${S((isNaN(this.opacity)?1:this.opacity)*255)}`}function U(){const t=F(this.opacity);return`${t===1?"rgb(":"rgba("}${D(this.r)}, ${D(this.g)}, ${D(this.b)}${t===1?")":`, ${t})`}`}function F(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function D(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function S(t){t=D(t);return(t<16?"0":"")+t.toString(16)}function P(t,n,e,i){if(i<=0)t=n=e=NaN;else if(e<=0||e>=1)t=n=NaN;else if(n<=0)t=NaN;return new H(t,n,e,i)}function E(t){if(t instanceof H)return new H(t.h,t.s,t.l,t.opacity);if(!(t instanceof r))t=A(t);if(!t)return new H;if(t instanceof H)return t;t=t.rgb();var n=t.r/255,e=t.g/255,i=t.b/255,a=Math.min(n,e,i),o=Math.max(n,e,i),s=NaN,u=o-a,h=(o+a)/2;if(u){if(n===o)s=(e-i)/u+(e<i)*6;else if(e===o)s=(i-n)/u+2;else s=(n-e)/u+4;u/=h<.5?o+a:2-o-a;s*=60}else{u=h>0&&h<1?0:s}return new H(s,u,h,t.opacity)}function R(t,n,e,i){return arguments.length===1?E(t):new H(t,n,e,i==null?1:i)}function H(t,n,e,i){this.h=+t;this.s=+n;this.l=+e;this.opacity=+i}(0,i.A)(H,R,(0,i.X)(r,{brighter(t){t=t==null?o:Math.pow(o,t);return new H(this.h,this.s,this.l*t,this.opacity)},darker(t){t=t==null?a:Math.pow(a,t);return new H(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,i=e+(e<.5?e:1-e)*n,r=2*e-i;return new N(L(t>=240?t-240:t+120,r,i),L(t,r,i),L(t<120?t+240:t-120,r,i),this.opacity)},clamp(){return new H(Y(this.h),q(this.s),q(this.l),F(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&(0<=this.l&&this.l<=1)&&(0<=this.opacity&&this.opacity<=1)},formatHsl(){const t=F(this.opacity);return`${t===1?"hsl(":"hsla("}${Y(this.h)}, ${q(this.s)*100}%, ${q(this.l)*100}%${t===1?")":`, ${t})`}`}}));function Y(t){t=(t||0)%360;return t<0?t+360:t}function q(t){return Math.max(0,Math.min(1,t||0))}function L(t,n,e){return(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)*255}},47592:(t,n,e)=>{e.d(n,{A:()=>i,X:()=>r});function i(t,n,e){t.prototype=n.prototype=e;e.constructor=t}function r(t,n){var e=Object.create(t.prototype);for(var i in n)e[i]=n[i];return e}},14180:(t,n,e)=>{e.d(n,{Ay:()=>d,aq:()=>M});var i=e(47592);var r=e(33844);var a=e(77689);const o=18,s=.96422,u=1,h=.82521,c=4/29,l=6/29,f=3*l*l,_=l*l*l;function p(t){if(t instanceof g)return new g(t.l,t.a,t.b,t.opacity);if(t instanceof T)return k(t);if(!(t instanceof r.Gw))t=(0,r.b)(t);var n=m(t.r),e=m(t.g),i=m(t.b),a=v((.2225045*n+.7168786*e+.0606169*i)/u),o,c;if(n===e&&e===i)o=c=a;else{o=v((.4360747*n+.3850649*e+.1430804*i)/s);c=v((.0139322*n+.0971045*e+.7141733*i)/h)}return new g(116*a-16,500*(o-a),200*(a-c),t.opacity)}function y(t,n){return new g(t,0,0,n==null?1:n)}function d(t,n,e,i){return arguments.length===1?p(t):new g(t,n,e,i==null?1:i)}function g(t,n,e,i){this.l=+t;this.a=+n;this.b=+e;this.opacity=+i}(0,i.A)(g,d,(0,i.X)(r.Q1,{brighter(t){return new g(this.l+o*(t==null?1:t),this.a,this.b,this.opacity)},darker(t){return new g(this.l-o*(t==null?1:t),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,n=isNaN(this.a)?t:t+this.a/500,e=isNaN(this.b)?t:t-this.b/200;n=s*x(n);t=u*x(t);e=h*x(e);return new r.Gw(w(3.1338561*n-1.6168667*t-.4906146*e),w(-.9787684*n+1.9161415*t+.033454*e),w(.0719453*n-.2289914*t+1.4052427*e),this.opacity)}}));function v(t){return t>_?Math.pow(t,1/3):t/f+c}function x(t){return t>l?t*t*t:f*(t-c)}function w(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function m(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function A(t){if(t instanceof T)return new T(t.h,t.c,t.l,t.opacity);if(!(t instanceof g))t=p(t);if(t.a===0&&t.b===0)return new T(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var n=Math.atan2(t.b,t.a)*a.u;return new T(n<0?n+360:n,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}function b(t,n,e,i){return arguments.length===1?A(t):new T(e,n,t,i==null?1:i)}function M(t,n,e,i){return arguments.length===1?A(t):new T(t,n,e,i==null?1:i)}function T(t,n,e,i){this.h=+t;this.c=+n;this.l=+e;this.opacity=+i}function k(t){if(isNaN(t.h))return new g(t.l,0,0,t.opacity);var n=t.h*a.F;return new g(t.l,Math.cos(n)*t.c,Math.sin(n)*t.c,t.opacity)}(0,i.A)(T,M,(0,i.X)(r.Q1,{brighter(t){return new T(this.h,this.c,this.l+o*(t==null?1:t),this.opacity)},darker(t){return new T(this.h,this.c,this.l-o*(t==null?1:t),this.opacity)},rgb(){return k(this).rgb()}}))},77689:(t,n,e)=>{e.d(n,{F:()=>i,u:()=>r});const i=Math.PI/180;const r=180/Math.PI},62996:(t,n,e)=>{e.d(n,{A:()=>h});var i={value:()=>{}};function r(){for(var t=0,n=arguments.length,e={},i;t<n;++t){if(!(i=arguments[t]+"")||i in e||/[\s.]/.test(i))throw new Error("illegal type: "+i);e[i]=[]}return new a(e)}function a(t){this._=t}function o(t,n){return t.trim().split(/^|\s+/).map((function(t){var e="",i=t.indexOf(".");if(i>=0)e=t.slice(i+1),t=t.slice(0,i);if(t&&!n.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:e}}))}a.prototype=r.prototype={constructor:a,on:function(t,n){var e=this._,i=o(t+"",e),r,a=-1,h=i.length;if(arguments.length<2){while(++a<h)if((r=(t=i[a]).type)&&(r=s(e[r],t.name)))return r;return}if(n!=null&&typeof n!=="function")throw new Error("invalid callback: "+n);while(++a<h){if(r=(t=i[a]).type)e[r]=u(e[r],t.name,n);else if(n==null)for(r in e)e[r]=u(e[r],t.name,null)}return this},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new a(t)},call:function(t,n){if((r=arguments.length-2)>0)for(var e=new Array(r),i=0,r,a;i<r;++i)e[i]=arguments[i+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(a=this._[t],i=0,r=a.length;i<r;++i)a[i].value.apply(n,e)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var i=this._[t],r=0,a=i.length;r<a;++r)i[r].value.apply(n,e)}};function s(t,n){for(var e=0,i=t.length,r;e<i;++e){if((r=t[e]).name===n){return r.value}}}function u(t,n,e){for(var r=0,a=t.length;r<a;++r){if(t[r].name===n){t[r]=i,t=t.slice(0,r).concat(t.slice(r+1));break}}if(e!=null)t.push({name:n,value:e});return t}const h=r},24626:(t,n,e)=>{e.d(n,{GP:()=>a,s:()=>o});var i=e(25216);var r;var a;var o;s({thousands:",",grouping:[3],currency:["$",""]});function s(t){r=(0,i.A)(t);a=r.format;o=r.formatPrefix;return r}},40886:(t,n,e)=>{e.d(n,{A:()=>r});var i=e(23735);function r(t){return t=(0,i.f)(Math.abs(t)),t?t[1]:NaN}},23735:(t,n,e)=>{e.d(n,{A:()=>i,f:()=>r});function i(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)}function r(t,n){if((e=(t=n?t.toExponential(n-1):t.toExponential()).indexOf("e"))<0)return null;var e,i=t.slice(0,e);return[i.length>1?i[0]+i.slice(2):i,+t.slice(e+1)]}},71688:(t,n,e)=>{e.d(n,{A:()=>r});var i=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function r(t){if(!(n=i.exec(t)))throw new Error("invalid format: "+t);var n;return new a({fill:n[1],align:n[2],sign:n[3],symbol:n[4],zero:n[5],width:n[6],comma:n[7],precision:n[8]&&n[8].slice(1),trim:n[9],type:n[10]})}r.prototype=a.prototype;function a(t){this.fill=t.fill===undefined?" ":t.fill+"";this.align=t.align===undefined?">":t.align+"";this.sign=t.sign===undefined?"-":t.sign+"";this.symbol=t.symbol===undefined?"":t.symbol+"";this.zero=!!t.zero;this.width=t.width===undefined?undefined:+t.width;this.comma=!!t.comma;this.precision=t.precision===undefined?undefined:+t.precision;this.trim=!!t.trim;this.type=t.type===undefined?"":t.type+""}a.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===undefined?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===undefined?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type}},25216:(t,n,e)=>{e.d(n,{A:()=>d});var i=e(40886);function r(t,n){return function(e,i){var r=e.length,a=[],o=0,s=t[0],u=0;while(r>0&&s>0){if(u+s+1>i)s=Math.max(1,i-u);a.push(e.substring(r-=s,r+s));if((u+=s+1)>i)break;s=t[o=(o+1)%t.length]}return a.reverse().join(n)}}function a(t){return function(n){return n.replace(/[0-9]/g,(function(n){return t[+n]}))}}var o=e(71688);function s(t){t:for(var n=t.length,e=1,i=-1,r;e<n;++e){switch(t[e]){case".":i=r=e;break;case"0":if(i===0)i=e;r=e;break;default:if(!+t[e])break t;if(i>0)i=0;break}}return i>0?t.slice(0,i)+t.slice(r+1):t}var u=e(23735);var h;function c(t,n){var e=(0,u.f)(t,n);if(!e)return t+"";var i=e[0],r=e[1],a=r-(h=Math.max(-8,Math.min(8,Math.floor(r/3)))*3)+1,o=i.length;return a===o?i:a>o?i+new Array(a-o+1).join("0"):a>0?i.slice(0,a)+"."+i.slice(a):"0."+new Array(1-a).join("0")+(0,u.f)(t,Math.max(0,n+a-1))[0]}function l(t,n){var e=(0,u.f)(t,n);if(!e)return t+"";var i=e[0],r=e[1];return r<0?"0."+new Array(-r).join("0")+i:i.length>r+1?i.slice(0,r+1)+"."+i.slice(r+1):i+new Array(r-i.length+2).join("0")}const f={"%":(t,n)=>(t*100).toFixed(n),b:t=>Math.round(t).toString(2),c:t=>t+"",d:u.A,e:(t,n)=>t.toExponential(n),f:(t,n)=>t.toFixed(n),g:(t,n)=>t.toPrecision(n),o:t=>Math.round(t).toString(8),p:(t,n)=>l(t*100,n),r:l,s:c,X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function _(t){return t}var p=Array.prototype.map,y=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function d(t){var n=t.grouping===undefined||t.thousands===undefined?_:r(p.call(t.grouping,Number),t.thousands+""),e=t.currency===undefined?"":t.currency[0]+"",u=t.currency===undefined?"":t.currency[1]+"",c=t.decimal===undefined?".":t.decimal+"",l=t.numerals===undefined?_:a(p.call(t.numerals,String)),d=t.percent===undefined?"%":t.percent+"",g=t.minus===undefined?"−":t.minus+"",v=t.nan===undefined?"NaN":t.nan+"";function x(t){t=(0,o.A)(t);var i=t.fill,r=t.align,a=t.sign,_=t.symbol,p=t.zero,x=t.width,w=t.comma,m=t.precision,A=t.trim,b=t.type;if(b==="n")w=true,b="g";else if(!f[b])m===undefined&&(m=12),A=true,b="g";if(p||i==="0"&&r==="=")p=true,i="0",r="=";var M=_==="$"?e:_==="#"&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",T=_==="$"?u:/[%p]/.test(b)?d:"";var k=f[b],N=/[defgprs%]/.test(b);m=m===undefined?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m));function C(t){var e=M,o=T,u,f,_;if(b==="c"){o=k(t)+o;t=""}else{t=+t;var d=t<0||1/t<0;t=isNaN(t)?v:k(Math.abs(t),m);if(A)t=s(t);if(d&&+t===0&&a!=="+")d=false;e=(d?a==="("?a:g:a==="-"||a==="("?"":a)+e;o=(b==="s"?y[8+h/3]:"")+o+(d&&a==="("?")":"");if(N){u=-1,f=t.length;while(++u<f){if(_=t.charCodeAt(u),48>_||_>57){o=(_===46?c+t.slice(u+1):t.slice(u))+o;t=t.slice(0,u);break}}}}if(w&&!p)t=n(t,Infinity);var C=e.length+t.length+o.length,$=C<x?new Array(x-C+1).join(i):"";if(w&&p)t=n($+t,$.length?x-o.length:Infinity),$="";switch(r){case"<":t=e+t+o+$;break;case"=":t=e+$+t+o;break;case"^":t=$.slice(0,C=$.length>>1)+e+t+o+$.slice(C);break;default:t=$+e+t+o;break}return l(t)}C.toString=function(){return t+""};return C}function w(t,n){var e=x((t=(0,o.A)(t),t.type="f",t)),r=Math.max(-8,Math.min(8,Math.floor((0,i.A)(n)/3)))*3,a=Math.pow(10,-r),s=y[8+r/3];return function(t){return e(a*t)+s}}return{format:x,formatPrefix:w}}},93391:(t,n,e)=>{e.d(n,{A:()=>r});var i=e(40886);function r(t){return Math.max(0,-(0,i.A)(Math.abs(t)))}},86093:(t,n,e)=>{e.d(n,{A:()=>r});var i=e(40886);function r(t,n){return Math.max(0,Math.max(-8,Math.min(8,Math.floor((0,i.A)(n)/3)))*3-(0,i.A)(Math.abs(t)))}},78209:(t,n,e)=>{e.d(n,{A:()=>r});var i=e(40886);function r(t,n){t=Math.abs(t),n=Math.abs(n)-t;return Math.max(0,(0,i.A)(n)-(0,i.A)(t))+1}},431:(t,n,e)=>{e.d(n,{l:()=>i,m:()=>r});function i(t){return t==null?null:r(t)}function r(t){if(typeof t!=="function")throw new Error;return t}},90751:(t,n,e)=>{e.d(n,{A:()=>r,P:()=>i});function i(){return 0}function r(t){return function(){return t}}},59459:(t,n,e)=>{e.d(n,{bP:()=>M,lW:()=>b,Ay:()=>v});function i(t){var n=0,e=t.children,i=e&&e.length;if(!i)n=1;else while(--i>=0)n+=e[i].value;t.value=n}function r(){return this.eachAfter(i)}function a(t,n){let e=-1;for(const i of this){t.call(n,i,++e,this)}return this}function o(t,n){var e=this,i=[e],r,a,o=-1;while(e=i.pop()){t.call(n,e,++o,this);if(r=e.children){for(a=r.length-1;a>=0;--a){i.push(r[a])}}}return this}function s(t,n){var e=this,i=[e],r=[],a,o,s,u=-1;while(e=i.pop()){r.push(e);if(a=e.children){for(o=0,s=a.length;o<s;++o){i.push(a[o])}}}while(e=r.pop()){t.call(n,e,++u,this)}return this}function u(t,n){let e=-1;for(const i of this){if(t.call(n,i,++e,this)){return i}}}function h(t){return this.eachAfter((function(n){var e=+t(n.data)||0,i=n.children,r=i&&i.length;while(--r>=0)e+=i[r].value;n.value=e}))}function c(t){return this.eachBefore((function(n){if(n.children){n.children.sort(t)}}))}function l(t){var n=this,e=f(n,t),i=[n];while(n!==e){n=n.parent;i.push(n)}var r=i.length;while(t!==e){i.splice(r,0,t);t=t.parent}return i}function f(t,n){if(t===n)return t;var e=t.ancestors(),i=n.ancestors(),r=null;t=e.pop();n=i.pop();while(t===n){r=t;t=e.pop();n=i.pop()}return r}function _(){var t=this,n=[t];while(t=t.parent){n.push(t)}return n}function p(){return Array.from(this)}function y(){var t=[];this.eachBefore((function(n){if(!n.children){t.push(n)}}));return t}function d(){var t=this,n=[];t.each((function(e){if(e!==t){n.push({source:e.parent,target:e})}}));return n}function*g(){var t=this,n,e=[t],i,r,a;do{n=e.reverse(),e=[];while(t=n.pop()){yield t;if(i=t.children){for(r=0,a=i.length;r<a;++r){e.push(i[r])}}}}while(e.length)}function v(t,n){if(t instanceof Map){t=[undefined,t];if(n===undefined)n=m}else if(n===undefined){n=w}var e=new M(t),i,r=[e],a,o,s,u;while(i=r.pop()){if((o=n(i.data))&&(u=(o=Array.from(o)).length)){i.children=o;for(s=u-1;s>=0;--s){r.push(a=o[s]=new M(o[s]));a.parent=i;a.depth=i.depth+1}}}return e.eachBefore(b)}function x(){return v(this).eachBefore(A)}function w(t){return t.children}function m(t){return Array.isArray(t)?t[1]:null}function A(t){if(t.data.value!==undefined)t.value=t.data.value;t.data=t.data.data}function b(t){var n=0;do{t.height=n}while((t=t.parent)&&t.height<++n)}function M(t){this.data=t;this.depth=this.height=0;this.parent=null}M.prototype=v.prototype={constructor:M,count:r,each:a,eachAfter:s,eachBefore:o,find:u,sum:h,sort:c,path:l,ancestors:_,descendants:p,leaves:y,links:d,copy:x,[Symbol.iterator]:g}},34227:(t,n,e)=>{e.d(n,{A:()=>i});function i(t,n,e,i,r){var a=t.children,o,s=-1,u=a.length,h=t.value&&(i-n)/t.value;while(++s<u){o=a[s],o.y0=e,o.y1=r;o.x0=n,o.x1=n+=o.value*h}}},93926:(t,n,e)=>{e.d(n,{A:()=>s});var i=e(7705);var r=e(63462);var a=e(431);var o=e(90751);function s(){var t=r.Ay,n=false,e=1,s=1,u=[0],h=o.P,c=o.P,l=o.P,f=o.P,_=o.P;function p(t){t.x0=t.y0=0;t.x1=e;t.y1=s;t.eachBefore(y);u=[0];if(n)t.eachBefore(i.A);return t}function y(n){var e=u[n.depth],i=n.x0+e,r=n.y0+e,a=n.x1-e,o=n.y1-e;if(a<i)i=a=(i+a)/2;if(o<r)r=o=(r+o)/2;n.x0=i;n.y0=r;n.x1=a;n.y1=o;if(n.children){e=u[n.depth+1]=h(n)/2;i+=_(n)-e;r+=c(n)-e;a-=l(n)-e;o-=f(n)-e;if(a<i)i=a=(i+a)/2;if(o<r)r=o=(r+o)/2;t(n,i,r,a,o)}}p.round=function(t){return arguments.length?(n=!!t,p):n};p.size=function(t){return arguments.length?(e=+t[0],s=+t[1],p):[e,s]};p.tile=function(n){return arguments.length?(t=(0,a.m)(n),p):t};p.padding=function(t){return arguments.length?p.paddingInner(t).paddingOuter(t):p.paddingInner()};p.paddingInner=function(t){return arguments.length?(h=typeof t==="function"?t:(0,o.A)(+t),p):h};p.paddingOuter=function(t){return arguments.length?p.paddingTop(t).paddingRight(t).paddingBottom(t).paddingLeft(t):p.paddingTop()};p.paddingTop=function(t){return arguments.length?(c=typeof t==="function"?t:(0,o.A)(+t),p):c};p.paddingRight=function(t){return arguments.length?(l=typeof t==="function"?t:(0,o.A)(+t),p):l};p.paddingBottom=function(t){return arguments.length?(f=typeof t==="function"?t:(0,o.A)(+t),p):f};p.paddingLeft=function(t){return arguments.length?(_=typeof t==="function"?t:(0,o.A)(+t),p):_};return p}},7705:(t,n,e)=>{e.d(n,{A:()=>i});function i(t){t.x0=Math.round(t.x0);t.y0=Math.round(t.y0);t.x1=Math.round(t.x1);t.y1=Math.round(t.y1)}},48420:(t,n,e)=>{e.d(n,{A:()=>i});function i(t,n,e,i,r){var a=t.children,o,s=-1,u=a.length,h=t.value&&(r-e)/t.value;while(++s<u){o=a[s],o.x0=n,o.x1=i;o.y0=e,o.y1=e+=o.value*h}}},63462:(t,n,e)=>{e.d(n,{AI:()=>o,Ay:()=>s,U1:()=>a});var i=e(34227);var r=e(48420);var a=(1+Math.sqrt(5))/2;function o(t,n,e,a,o,s){var u=[],h=n.children,c,l,f=0,_=0,p=h.length,y,d,g=n.value,v,x,w,m,A,b,M;while(f<p){y=o-e,d=s-a;do{v=h[_++].value}while(!v&&_<p);x=w=v;b=Math.max(d/y,y/d)/(g*t);M=v*v*b;A=Math.max(w/M,M/x);for(;_<p;++_){v+=l=h[_].value;if(l<x)x=l;if(l>w)w=l;M=v*v*b;m=Math.max(w/M,M/x);if(m>A){v-=l;break}A=m}u.push(c={value:v,dice:y<d,children:h.slice(f,_)});if(c.dice)(0,i.A)(c,e,a,o,g?a+=d*v/g:s);else(0,r.A)(c,e,a,g?e+=y*v/g:o,s);g-=v,f=_}return u}const s=function t(n){function e(t,e,i,r,a){o(n,t,e,i,r,a)}e.ratio=function(n){return t((n=+n)>1?n:1)};return e}(a)},69266:(t,n,e)=>{e.d(n,{$:()=>o,A:()=>a});var i=e(21406);var r=e(48561);function a(t,n){return((0,r.p)(n)?r.A:o)(t,n)}function o(t,n){var e=n?n.length:0,r=t?Math.min(e,t.length):0,a=new Array(r),o=new Array(e),s;for(s=0;s<r;++s)a[s]=(0,i.A)(t[s],n[s]);for(;s<e;++s)o[s]=n[s];return function(t){for(s=0;s<r;++s)o[s]=a[s](t);return o}}},13029:(t,n,e)=>{e.d(n,{A:()=>r,H:()=>i});function i(t,n,e,i,r){var a=t*t,o=a*t;return((1-3*t+3*a-o)*n+(4-6*a+3*o)*e+(1+3*t+3*a-3*o)*i+o*r)/6}function r(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),a=t[r],o=t[r+1],s=r>0?t[r-1]:2*a-o,u=r<n-1?t[r+2]:2*o-a;return i((e-r/n)*n,s,a,o,u)}}},64425:(t,n,e)=>{e.d(n,{A:()=>r});var i=e(13029);function r(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),a=t[(r+n-1)%n],o=t[r%n],s=t[(r+1)%n],u=t[(r+2)%n];return(0,i.H)((e-r/n)*n,a,o,s,u)}}},6504:(t,n,e)=>{e.d(n,{Ay:()=>u,lG:()=>o,uN:()=>s});var i=e(80319);function r(t,n){return function(e){return t+e*n}}function a(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(i){return Math.pow(t+i*n,e)}}function o(t,n){var e=n-t;return e?r(t,e>180||e<-180?e-360*Math.round(e/360):e):(0,i.A)(isNaN(t)?n:t)}function s(t){return(t=+t)===1?u:function(n,e){return e-n?a(n,e,t):(0,i.A)(isNaN(n)?e:n)}}function u(t,n){var e=n-t;return e?r(t,e):(0,i.A)(isNaN(t)?n:t)}},80319:(t,n,e)=>{e.d(n,{A:()=>i});const i=t=>()=>t},57007:(t,n,e)=>{e.d(n,{A:()=>i});function i(t,n){var e=new Date;return t=+t,n=+n,function(i){return e.setTime(t*(1-i)+n*i),e}}},67360:(t,n,e)=>{e.r(n);e.d(n,{interpolate:()=>i.A,interpolateArray:()=>r.A,interpolateBasis:()=>a.A,interpolateBasisClosed:()=>o.A,interpolateCubehelix:()=>B,interpolateCubehelixLong:()=>G,interpolateDate:()=>s.A,interpolateDiscrete:()=>u,interpolateHcl:()=>U,interpolateHclLong:()=>F,interpolateHsl:()=>T,interpolateHslLong:()=>k,interpolateHue:()=>c,interpolateLab:()=>C,interpolateNumber:()=>l.A,interpolateNumberArray:()=>f.A,interpolateObject:()=>_.A,interpolateRgb:()=>A.Ay,interpolateRgbBasis:()=>A.Ik,interpolateRgbBasisClosed:()=>A.uL,interpolateRound:()=>p.A,interpolateString:()=>y.A,interpolateTransformCss:()=>d.T,interpolateTransformSvg:()=>d.I,interpolateZoom:()=>m,piecewise:()=>J.A,quantize:()=>Z});var i=e(21406);var r=e(69266);var a=e(13029);var o=e(64425);var s=e(57007);function u(t){var n=t.length;return function(e){return t[Math.max(0,Math.min(n-1,Math.floor(e*n)))]}}var h=e(6504);function c(t,n){var e=(0,h.lG)(+t,+n);return function(t){var n=e(t);return n-360*Math.floor(n/360)}}var l=e(85566);var f=e(48561);var _=e(86088);var p=e(15307);var y=e(23318);var d=e(39480);var g=1e-12;function v(t){return((t=Math.exp(t))+1/t)/2}function x(t){return((t=Math.exp(t))-1/t)/2}function w(t){return((t=Math.exp(2*t))-1)/(t+1)}const m=function t(n,e,i){function r(t,r){var a=t[0],o=t[1],s=t[2],u=r[0],h=r[1],c=r[2],l=u-a,f=h-o,_=l*l+f*f,p,y;if(_<g){y=Math.log(c/s)/n;p=function(t){return[a+t*l,o+t*f,s*Math.exp(n*t*y)]}}else{var d=Math.sqrt(_),m=(c*c-s*s+i*_)/(2*s*e*d),A=(c*c-s*s-i*_)/(2*c*e*d),b=Math.log(Math.sqrt(m*m+1)-m),M=Math.log(Math.sqrt(A*A+1)-A);y=(M-b)/n;p=function(t){var i=t*y,r=v(b),u=s/(e*d)*(r*w(n*i+b)-x(b));return[a+u*l,o+u*f,s*r/v(n*i+b)]}}p.duration=y*1e3*n/Math.SQRT2;return p}r.rho=function(n){var e=Math.max(.001,+n),i=e*e,r=i*i;return t(e,i,r)};return r}(Math.SQRT2,2,4);var A=e(79948);var b=e(33844);function M(t){return function(n,e){var i=t((n=(0,b.KI)(n)).h,(e=(0,b.KI)(e)).h),r=(0,h.Ay)(n.s,e.s),a=(0,h.Ay)(n.l,e.l),o=(0,h.Ay)(n.opacity,e.opacity);return function(t){n.h=i(t);n.s=r(t);n.l=a(t);n.opacity=o(t);return n+""}}}const T=M(h.lG);var k=M(h.Ay);var N=e(14180);function C(t,n){var e=(0,h.Ay)((t=(0,N.Ay)(t)).l,(n=(0,N.Ay)(n)).l),i=(0,h.Ay)(t.a,n.a),r=(0,h.Ay)(t.b,n.b),a=(0,h.Ay)(t.opacity,n.opacity);return function(n){t.l=e(n);t.a=i(n);t.b=r(n);t.opacity=a(n);return t+""}}function $(t){return function(n,e){var i=t((n=(0,N.aq)(n)).h,(e=(0,N.aq)(e)).h),r=(0,h.Ay)(n.c,e.c),a=(0,h.Ay)(n.l,e.l),o=(0,h.Ay)(n.opacity,e.opacity);return function(t){n.h=i(t);n.c=r(t);n.l=a(t);n.opacity=o(t);return n+""}}}const U=$(h.lG);var F=$(h.Ay);var D=e(47592);var S=e(77689);var P=-.14861,E=+1.78277,R=-.29227,H=-.90649,Y=+1.97294,q=Y*H,L=Y*E,j=E*R-H*P;function z(t){if(t instanceof X)return new X(t.h,t.s,t.l,t.opacity);if(!(t instanceof b.Gw))t=(0,b.b)(t);var n=t.r/255,e=t.g/255,i=t.b/255,r=(j*i+q*n-L*e)/(j+q-L),a=i-r,o=(Y*(e-r)-R*a)/H,s=Math.sqrt(o*o+a*a)/(Y*r*(1-r)),u=s?Math.atan2(o,a)*S.u-120:NaN;return new X(u<0?u+360:u,s,r,t.opacity)}function I(t,n,e,i){return arguments.length===1?z(t):new X(t,n,e,i==null?1:i)}function X(t,n,e,i){this.h=+t;this.s=+n;this.l=+e;this.opacity=+i}(0,D.A)(X,I,(0,D.X)(b.Q1,{brighter(t){t=t==null?b.Uw:Math.pow(b.Uw,t);return new X(this.h,this.s,this.l*t,this.opacity)},darker(t){t=t==null?b.ef:Math.pow(b.ef,t);return new X(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=isNaN(this.h)?0:(this.h+120)*S.F,n=+this.l,e=isNaN(this.s)?0:this.s*n*(1-n),i=Math.cos(t),r=Math.sin(t);return new b.Gw(255*(n+e*(P*i+E*r)),255*(n+e*(R*i+H*r)),255*(n+e*(Y*i)),this.opacity)}}));function O(t){return function n(e){e=+e;function i(n,i){var r=t((n=I(n)).h,(i=I(i)).h),a=(0,h.Ay)(n.s,i.s),o=(0,h.Ay)(n.l,i.l),s=(0,h.Ay)(n.opacity,i.opacity);return function(t){n.h=r(t);n.s=a(t);n.l=o(Math.pow(t,e));n.opacity=s(t);return n+""}}i.gamma=n;return i}(1)}const B=O(h.lG);var G=O(h.Ay);var J=e(99793);function Z(t,n){var e=new Array(n);for(var i=0;i<n;++i)e[i]=t(i/(n-1));return e}},85566:(t,n,e)=>{e.d(n,{A:()=>i});function i(t,n){return t=+t,n=+n,function(e){return t*(1-e)+n*e}}},48561:(t,n,e)=>{e.d(n,{A:()=>i,p:()=>r});function i(t,n){if(!n)n=[];var e=t?Math.min(n.length,t.length):0,i=n.slice(),r;return function(a){for(r=0;r<e;++r)i[r]=t[r]*(1-a)+n[r]*a;return i}}function r(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)}},86088:(t,n,e)=>{e.d(n,{A:()=>r});var i=e(21406);function r(t,n){var e={},r={},a;if(t===null||typeof t!=="object")t={};if(n===null||typeof n!=="object")n={};for(a in n){if(a in t){e[a]=(0,i.A)(t[a],n[a])}else{r[a]=n[a]}}return function(t){for(a in e)r[a]=e[a](t);return r}}},99793:(t,n,e)=>{e.d(n,{A:()=>r});var i=e(21406);function r(t,n){if(n===undefined)n=t,t=i.A;var e=0,r=n.length-1,a=n[0],o=new Array(r<0?0:r);while(e<r)o[e]=t(a,a=n[++e]);return function(t){var n=Math.max(0,Math.min(r-1,Math.floor(t*=r)));return o[n](t-n)}}},79948:(t,n,e)=>{e.d(n,{Ay:()=>s,Ik:()=>h,uL:()=>c});var i=e(33844);var r=e(13029);var a=e(64425);var o=e(6504);const s=function t(n){var e=(0,o.uN)(n);function r(t,n){var r=e((t=(0,i.Qh)(t)).r,(n=(0,i.Qh)(n)).r),a=e(t.g,n.g),s=e(t.b,n.b),u=(0,o.Ay)(t.opacity,n.opacity);return function(n){t.r=r(n);t.g=a(n);t.b=s(n);t.opacity=u(n);return t+""}}r.gamma=t;return r}(1);function u(t){return function(n){var e=n.length,r=new Array(e),a=new Array(e),o=new Array(e),s,u;for(s=0;s<e;++s){u=(0,i.Qh)(n[s]);r[s]=u.r||0;a[s]=u.g||0;o[s]=u.b||0}r=t(r);a=t(a);o=t(o);u.opacity=1;return function(t){u.r=r(t);u.g=a(t);u.b=o(t);return u+""}}}var h=u(r.A);var c=u(a.A)},15307:(t,n,e)=>{e.d(n,{A:()=>i});function i(t,n){return t=+t,n=+n,function(e){return Math.round(t*(1-e)+n*e)}}},23318:(t,n,e)=>{e.d(n,{A:()=>u});var i=e(85566);var r=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,a=new RegExp(r.source,"g");function o(t){return function(){return t}}function s(t){return function(n){return t(n)+""}}function u(t,n){var e=r.lastIndex=a.lastIndex=0,u,h,c,l=-1,f=[],_=[];t=t+"",n=n+"";while((u=r.exec(t))&&(h=a.exec(n))){if((c=h.index)>e){c=n.slice(e,c);if(f[l])f[l]+=c;else f[++l]=c}if((u=u[0])===(h=h[0])){if(f[l])f[l]+=h;else f[++l]=h}else{f[++l]=null;_.push({i:l,x:(0,i.A)(u,h)})}e=a.lastIndex}if(e<n.length){c=n.slice(e);if(f[l])f[l]+=c;else f[++l]=c}return f.length<2?_[0]?s(_[0].x):o(n):(n=_.length,function(t){for(var e=0,i;e<n;++e)f[(i=_[e]).i]=i.x(t);return f.join("")})}},39480:(t,n,e)=>{e.d(n,{T:()=>l,I:()=>f});var i=e(85566);var r=180/Math.PI;var a={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function o(t,n,e,i,a,o){var s,u,h;if(s=Math.sqrt(t*t+n*n))t/=s,n/=s;if(h=t*e+n*i)e-=t*h,i-=n*h;if(u=Math.sqrt(e*e+i*i))e/=u,i/=u,h/=u;if(t*i<n*e)t=-t,n=-n,h=-h,s=-s;return{translateX:a,translateY:o,rotate:Math.atan2(n,t)*r,skewX:Math.atan(h)*r,scaleX:s,scaleY:u}}var s;function u(t){const n=new(typeof DOMMatrix==="function"?DOMMatrix:WebKitCSSMatrix)(t+"");return n.isIdentity?a:o(n.a,n.b,n.c,n.d,n.e,n.f)}function h(t){if(t==null)return a;if(!s)s=document.createElementNS("http://www.w3.org/2000/svg","g");s.setAttribute("transform",t);if(!(t=s.transform.baseVal.consolidate()))return a;t=t.matrix;return o(t.a,t.b,t.c,t.d,t.e,t.f)}function c(t,n,e,r){function a(t){return t.length?t.pop()+" ":""}function o(t,r,a,o,s,u){if(t!==a||r!==o){var h=s.push("translate(",null,n,null,e);u.push({i:h-4,x:(0,i.A)(t,a)},{i:h-2,x:(0,i.A)(r,o)})}else if(a||o){s.push("translate("+a+n+o+e)}}function s(t,n,e,o){if(t!==n){if(t-n>180)n+=360;else if(n-t>180)t+=360;o.push({i:e.push(a(e)+"rotate(",null,r)-2,x:(0,i.A)(t,n)})}else if(n){e.push(a(e)+"rotate("+n+r)}}function u(t,n,e,o){if(t!==n){o.push({i:e.push(a(e)+"skewX(",null,r)-2,x:(0,i.A)(t,n)})}else if(n){e.push(a(e)+"skewX("+n+r)}}function h(t,n,e,r,o,s){if(t!==e||n!==r){var u=o.push(a(o)+"scale(",null,",",null,")");s.push({i:u-4,x:(0,i.A)(t,e)},{i:u-2,x:(0,i.A)(n,r)})}else if(e!==1||r!==1){o.push(a(o)+"scale("+e+","+r+")")}}return function(n,e){var i=[],r=[];n=t(n),e=t(e);o(n.translateX,n.translateY,e.translateX,e.translateY,i,r);s(n.rotate,e.rotate,i,r);u(n.skewX,e.skewX,i,r);h(n.scaleX,n.scaleY,e.scaleX,e.scaleY,i,r);n=e=null;return function(t){var n=-1,e=r.length,a;while(++n<e)i[(a=r[n]).i]=a.x(t);return i.join("")}}}var l=c(u,"px, ","px)","deg)");var f=c(h,", ",")",")")},21406:(t,n,e)=>{e.d(n,{A:()=>f});var i=e(33844);var r=e(79948);var a=e(69266);var o=e(57007);var s=e(85566);var u=e(86088);var h=e(23318);var c=e(80319);var l=e(48561);function f(t,n){var e=typeof n,f;return n==null||e==="boolean"?(0,c.A)(n):(e==="number"?s.A:e==="string"?(f=(0,i.Ay)(n))?(n=f,r.Ay):h.A:n instanceof i.Ay?r.Ay:n instanceof Date?o.A:(0,l.p)(n)?l.A:Array.isArray(n)?a.$:typeof n.valueOf!=="function"&&typeof n.toString!=="function"||isNaN(n)?u.A:s.A)(t,n)}},69450:(t,n,e)=>{e.d(n,{Ae:()=>c,wA:()=>h});const i=Math.PI,r=2*i,a=1e-6,o=r-a;function s(t){this._+=t[0];for(let n=1,e=t.length;n<e;++n){this._+=arguments[n]+t[n]}}function u(t){let n=Math.floor(t);if(!(n>=0))throw new Error(`invalid digits: ${t}`);if(n>15)return s;const e=10**n;return function(t){this._+=t[0];for(let n=1,i=t.length;n<i;++n){this._+=Math.round(arguments[n]*e)/e+t[n]}}}class h{constructor(t){this._x0=this._y0=this._x1=this._y1=null;this._="";this._append=t==null?s:u(t)}moveTo(t,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}`}closePath(){if(this._x1!==null){this._x1=this._x0,this._y1=this._y0;this._append`Z`}}lineTo(t,n){this._append`L${this._x1=+t},${this._y1=+n}`}quadraticCurveTo(t,n,e,i){this._append`Q${+t},${+n},${this._x1=+e},${this._y1=+i}`}bezierCurveTo(t,n,e,i,r,a){this._append`C${+t},${+n},${+e},${+i},${this._x1=+r},${this._y1=+a}`}arcTo(t,n,e,r,o){t=+t,n=+n,e=+e,r=+r,o=+o;if(o<0)throw new Error(`negative radius: ${o}`);let s=this._x1,u=this._y1,h=e-t,c=r-n,l=s-t,f=u-n,_=l*l+f*f;if(this._x1===null){this._append`M${this._x1=t},${this._y1=n}`}else if(!(_>a));else if(!(Math.abs(f*h-c*l)>a)||!o){this._append`L${this._x1=t},${this._y1=n}`}else{let p=e-s,y=r-u,d=h*h+c*c,g=p*p+y*y,v=Math.sqrt(d),x=Math.sqrt(_),w=o*Math.tan((i-Math.acos((d+_-g)/(2*v*x)))/2),m=w/x,A=w/v;if(Math.abs(m-1)>a){this._append`L${t+m*l},${n+m*f}`}this._append`A${o},${o},0,0,${+(f*p>l*y)},${this._x1=t+A*h},${this._y1=n+A*c}`}}arc(t,n,e,s,u,h){t=+t,n=+n,e=+e,h=!!h;if(e<0)throw new Error(`negative radius: ${e}`);let c=e*Math.cos(s),l=e*Math.sin(s),f=t+c,_=n+l,p=1^h,y=h?s-u:u-s;if(this._x1===null){this._append`M${f},${_}`}else if(Math.abs(this._x1-f)>a||Math.abs(this._y1-_)>a){this._append`L${f},${_}`}if(!e)return;if(y<0)y=y%r+r;if(y>o){this._append`A${e},${e},0,1,${p},${t-c},${n-l}A${e},${e},0,1,${p},${this._x1=f},${this._y1=_}`}else if(y>a){this._append`A${e},${e},0,${+(y>=i)},${p},${this._x1=t+e*Math.cos(u)},${this._y1=n+e*Math.sin(u)}`}}rect(t,n,e,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}h${e=+e}v${+i}h${-e}Z`}toString(){return this._}}function c(){return new h}c.prototype=h.prototype;function l(t=3){return new h(+t)}},58177:(t,n,e)=>{e.d(n,{A:()=>i});function i(t){var n=t.length/6|0,e=new Array(n),i=0;while(i<n)e[i]="#"+t.slice(i*6,++i*6);return e}},52178:(t,n,e)=>{e.d(n,{C:()=>y,Ay:()=>g,D_:()=>c,Gu:()=>d});var i=e(71363);var r=e(21406);var a=e(85566);var o=e(15307);function s(t){return function(){return t}}var u=e(60117);var h=[0,1];function c(t){return t}function l(t,n){return(n-=t=+t)?function(e){return(e-t)/n}:s(isNaN(n)?NaN:.5)}function f(t,n){var e;if(t>n)e=t,t=n,n=e;return function(e){return Math.max(t,Math.min(n,e))}}function _(t,n,e){var i=t[0],r=t[1],a=n[0],o=n[1];if(r<i)i=l(r,i),a=e(o,a);else i=l(i,r),a=e(a,o);return function(t){return a(i(t))}}function p(t,n,e){var r=Math.min(t.length,n.length)-1,a=new Array(r),o=new Array(r),s=-1;if(t[r]<t[0]){t=t.slice().reverse();n=n.slice().reverse()}while(++s<r){a[s]=l(t[s],t[s+1]);o[s]=e(n[s],n[s+1])}return function(n){var e=(0,i.Ay)(t,n,1,r)-1;return o[e](a[e](n))}}function y(t,n){return n.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function d(){var t=h,n=h,e=r.A,i,s,l,y=c,d,g,v;function x(){var e=Math.min(t.length,n.length);if(y!==c)y=f(t[0],t[e-1]);d=e>2?p:_;g=v=null;return w}function w(r){return r==null||isNaN(r=+r)?l:(g||(g=d(t.map(i),n,e)))(i(y(r)))}w.invert=function(e){return y(s((v||(v=d(n,t.map(i),a.A)))(e)))};w.domain=function(n){return arguments.length?(t=Array.from(n,u.A),x()):t.slice()};w.range=function(t){return arguments.length?(n=Array.from(t),x()):n.slice()};w.rangeRound=function(t){return n=Array.from(t),e=o.A,x()};w.clamp=function(t){return arguments.length?(y=t?true:c,x()):y!==c};w.interpolate=function(t){return arguments.length?(e=t,x()):e};w.unknown=function(t){return arguments.length?(l=t,w):l};return function(t,n){i=t,s=n;return x()}}function g(){return d()(c,c)}},25758:(t,n,e)=>{e.d(n,{C:()=>i,K:()=>r});function i(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t);break}return this}function r(t,n){switch(arguments.length){case 0:break;case 1:{if(typeof t==="function")this.interpolator(t);else this.range(t);break}default:{this.domain(t);if(typeof n==="function")this.interpolator(n);else this.range(n);break}}return this}},20481:(t,n,e)=>{e.d(n,{A:()=>u,C:()=>s});var i=e(97119);var r=e(52178);var a=e(25758);var o=e(26698);function s(t){var n=t.domain;t.ticks=function(t){var e=n();return(0,i.Ay)(e[0],e[e.length-1],t==null?10:t)};t.tickFormat=function(t,e){var i=n();return(0,o.A)(i[0],i[i.length-1],t==null?10:t,e)};t.nice=function(e){if(e==null)e=10;var r=n();var a=0;var o=r.length-1;var s=r[a];var u=r[o];var h;var c;var l=10;if(u<s){c=s,s=u,u=c;c=a,a=o,o=c}while(l-- >0){c=(0,i.lq)(s,u,e);if(c===h){r[a]=s;r[o]=u;return n(r)}else if(c>0){s=Math.floor(s/c)*c;u=Math.ceil(u/c)*c}else if(c<0){s=Math.ceil(s*c)/c;u=Math.floor(u*c)/c}else{break}h=c}return t};return t}function u(){var t=(0,r.Ay)();t.copy=function(){return(0,r.C)(t,u())};a.C.apply(t,arguments);return s(t)}},60125:(t,n,e)=>{e.d(n,{A:()=>i});function i(t,n){t=t.slice();var e=0,i=t.length-1,r=t[e],a=t[i],o;if(a<r){o=e,e=i,i=o;o=r,r=a,a=o}t[e]=n.floor(r);t[i]=n.ceil(a);return t}},60117:(t,n,e)=>{e.d(n,{A:()=>i});function i(t){return+t}},16527:(t,n,e)=>{e.d(n,{A:()=>o,h:()=>a});var i=e(30352);var r=e(25758);const a=Symbol("implicit");function o(){var t=new i.B,n=[],e=[],s=a;function u(i){let r=t.get(i);if(r===undefined){if(s!==a)return s;t.set(i,r=n.push(i)-1)}return e[r%e.length]}u.domain=function(e){if(!arguments.length)return n.slice();n=[],t=new i.B;for(const i of e){if(t.has(i))continue;t.set(i,n.push(i)-1)}return u};u.range=function(t){return arguments.length?(e=Array.from(t),u):e.slice()};u.unknown=function(t){return arguments.length?(s=t,u):s};u.copy=function(){return o(n,e).unknown(s)};r.C.apply(u,arguments);return u}},26698:(t,n,e)=>{e.d(n,{A:()=>h});var i=e(97119);var r=e(71688);var a=e(86093);var o=e(24626);var s=e(78209);var u=e(93391);function h(t,n,e,h){var c=(0,i.sG)(t,n,e),l;h=(0,r.A)(h==null?",f":h);switch(h.type){case"s":{var f=Math.max(Math.abs(t),Math.abs(n));if(h.precision==null&&!isNaN(l=(0,a.A)(c,f)))h.precision=l;return(0,o.s)(h,f)}case"":case"e":case"g":case"p":case"r":{if(h.precision==null&&!isNaN(l=(0,s.A)(c,Math.max(Math.abs(t),Math.abs(n)))))h.precision=l-(h.type==="e");break}case"f":case"%":{if(h.precision==null&&!isNaN(l=(0,u.A)(c)))h.precision=l-(h.type==="%")*2;break}}return(0,o.GP)(h)}},74725:(t,n,e)=>{e.d(n,{A:()=>v,B:()=>g});var i=e(20421);var r=e(42706);var a=e(77849);var o=e(61779);var s=e(20293);var u=e(9017);var h=e(23383);var c=e(61147);var l=e(82692);var f=e(52178);var _=e(25758);var p=e(60125);function y(t){return new Date(t)}function d(t){return t instanceof Date?+t:+new Date(+t)}function g(t,n,e,i,r,a,o,s,u,h){var c=(0,f.Ay)(),l=c.invert,_=c.domain;var v=h(".%L"),x=h(":%S"),w=h("%I:%M"),m=h("%I %p"),A=h("%a %d"),b=h("%b %d"),M=h("%B"),T=h("%Y");function k(t){return(u(t)<t?v:s(t)<t?x:o(t)<t?w:a(t)<t?m:i(t)<t?r(t)<t?A:b:e(t)<t?M:T)(t)}c.invert=function(t){return new Date(l(t))};c.domain=function(t){return arguments.length?_(Array.from(t,d)):_().map(y)};c.ticks=function(n){var e=_();return t(e[0],e[e.length-1],n==null?10:n)};c.tickFormat=function(t,n){return n==null?k:h(n)};c.nice=function(t){var e=_();if(!t||typeof t.range!=="function")t=n(e[0],e[e.length-1],t==null?10:t);return t?_((0,p.A)(e,t)):c};c.copy=function(){return(0,f.C)(c,g(t,n,e,i,r,a,o,s,u,h))};return c}function v(){return _.C.apply(g(i.Cf,i.yE,r.he,a.Ui,o.YP,s.UA,u.Ag,h.wX,c.R,l.DC).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}},16681:(t,n,e)=>{e.d(n,{A:()=>_});var i=e(84653);var r=e(98247);var a=e(18226);function o(t){return t.innerRadius}function s(t){return t.outerRadius}function u(t){return t.startAngle}function h(t){return t.endAngle}function c(t){return t&&t.padAngle}function l(t,n,e,i,a,o,s,u){var h=e-t,c=i-n,l=s-a,f=u-o,_=f*h-l*c;if(_*_<r.Ni)return;_=(l*(n-o)-f*(t-a))/_;return[t+_*h,n+_*c]}function f(t,n,e,i,a,o,s){var u=t-e,h=n-i,c=(s?o:-o)/(0,r.RZ)(u*u+h*h),l=c*h,f=-c*u,_=t+l,p=n+f,y=e+l,d=i+f,g=(_+y)/2,v=(p+d)/2,x=y-_,w=d-p,m=x*x+w*w,A=a-o,b=_*d-y*p,M=(w<0?-1:1)*(0,r.RZ)((0,r.T9)(0,A*A*m-b*b)),T=(b*w-x*M)/m,k=(-b*x-w*M)/m,N=(b*w+x*M)/m,C=(-b*x+w*M)/m,$=T-g,U=k-v,F=N-g,D=C-v;if($*$+U*U>F*F+D*D)T=N,k=C;return{cx:T,cy:k,x01:-l,y01:-f,x11:T*(a/A-1),y11:k*(a/A-1)}}function _(){var t=o,n=s,e=(0,i.A)(0),_=null,p=u,y=h,d=c,g=null,v=(0,a.i)(x);function x(){var i,a,o=+t.apply(this,arguments),s=+n.apply(this,arguments),u=p.apply(this,arguments)-r.TW,h=y.apply(this,arguments)-r.TW,c=(0,r.tn)(h-u),x=h>u;if(!g)g=i=v();if(s<o)a=s,s=o,o=a;if(!(s>r.Ni))g.moveTo(0,0);else if(c>r.FA-r.Ni){g.moveTo(s*(0,r.gn)(u),s*(0,r.F8)(u));g.arc(0,0,s,u,h,!x);if(o>r.Ni){g.moveTo(o*(0,r.gn)(h),o*(0,r.F8)(h));g.arc(0,0,o,h,u,x)}}else{var w=u,m=h,A=u,b=h,M=c,T=c,k=d.apply(this,arguments)/2,N=k>r.Ni&&(_?+_.apply(this,arguments):(0,r.RZ)(o*o+s*s)),C=(0,r.jk)((0,r.tn)(s-o)/2,+e.apply(this,arguments)),$=C,U=C,F,D;if(N>r.Ni){var S=(0,r.qR)(N/o*(0,r.F8)(k)),P=(0,r.qR)(N/s*(0,r.F8)(k));if((M-=S*2)>r.Ni)S*=x?1:-1,A+=S,b-=S;else M=0,A=b=(u+h)/2;if((T-=P*2)>r.Ni)P*=x?1:-1,w+=P,m-=P;else T=0,w=m=(u+h)/2}var E=s*(0,r.gn)(w),R=s*(0,r.F8)(w),H=o*(0,r.gn)(b),Y=o*(0,r.F8)(b);if(C>r.Ni){var q=s*(0,r.gn)(m),L=s*(0,r.F8)(m),j=o*(0,r.gn)(A),z=o*(0,r.F8)(A),I;if(c<r.pi){if(I=l(E,R,j,z,q,L,H,Y)){var X=E-I[0],O=R-I[1],B=q-I[0],G=L-I[1],J=1/(0,r.F8)((0,r.HQ)((X*B+O*G)/((0,r.RZ)(X*X+O*O)*(0,r.RZ)(B*B+G*G)))/2),Z=(0,r.RZ)(I[0]*I[0]+I[1]*I[1]);$=(0,r.jk)(C,(o-Z)/(J-1));U=(0,r.jk)(C,(s-Z)/(J+1))}else{$=U=0}}}if(!(T>r.Ni))g.moveTo(E,R);else if(U>r.Ni){F=f(j,z,E,R,s,U,x);D=f(q,L,H,Y,s,U,x);g.moveTo(F.cx+F.x01,F.cy+F.y01);if(U<C)g.arc(F.cx,F.cy,U,(0,r.FP)(F.y01,F.x01),(0,r.FP)(D.y01,D.x01),!x);else{g.arc(F.cx,F.cy,U,(0,r.FP)(F.y01,F.x01),(0,r.FP)(F.y11,F.x11),!x);g.arc(0,0,s,(0,r.FP)(F.cy+F.y11,F.cx+F.x11),(0,r.FP)(D.cy+D.y11,D.cx+D.x11),!x);g.arc(D.cx,D.cy,U,(0,r.FP)(D.y11,D.x11),(0,r.FP)(D.y01,D.x01),!x)}}else g.moveTo(E,R),g.arc(0,0,s,w,m,!x);if(!(o>r.Ni)||!(M>r.Ni))g.lineTo(H,Y);else if($>r.Ni){F=f(H,Y,q,L,o,-$,x);D=f(E,R,j,z,o,-$,x);g.lineTo(F.cx+F.x01,F.cy+F.y01);if($<C)g.arc(F.cx,F.cy,$,(0,r.FP)(F.y01,F.x01),(0,r.FP)(D.y01,D.x01),!x);else{g.arc(F.cx,F.cy,$,(0,r.FP)(F.y01,F.x01),(0,r.FP)(F.y11,F.x11),!x);g.arc(0,0,o,(0,r.FP)(F.cy+F.y11,F.cx+F.x11),(0,r.FP)(D.cy+D.y11,D.cx+D.x11),x);g.arc(D.cx,D.cy,$,(0,r.FP)(D.y11,D.x11),(0,r.FP)(D.y01,D.x01),!x)}}else g.arc(0,0,o,b,A,x)}g.closePath();if(i)return g=null,i+""||null}x.centroid=function(){var e=(+t.apply(this,arguments)+ +n.apply(this,arguments))/2,i=(+p.apply(this,arguments)+ +y.apply(this,arguments))/2-r.pi/2;return[(0,r.gn)(i)*e,(0,r.F8)(i)*e]};x.innerRadius=function(n){return arguments.length?(t=typeof n==="function"?n:(0,i.A)(+n),x):t};x.outerRadius=function(t){return arguments.length?(n=typeof t==="function"?t:(0,i.A)(+t),x):n};x.cornerRadius=function(t){return arguments.length?(e=typeof t==="function"?t:(0,i.A)(+t),x):e};x.padRadius=function(t){return arguments.length?(_=t==null?null:typeof t==="function"?t:(0,i.A)(+t),x):_};x.startAngle=function(t){return arguments.length?(p=typeof t==="function"?t:(0,i.A)(+t),x):p};x.endAngle=function(t){return arguments.length?(y=typeof t==="function"?t:(0,i.A)(+t),x):y};x.padAngle=function(t){return arguments.length?(d=typeof t==="function"?t:(0,i.A)(+t),x):d};x.context=function(t){return arguments.length?(g=t==null?null:t,x):g};return x}},12736:(t,n,e)=>{e.d(n,{A:()=>r});var i=Array.prototype.slice;function r(t){return typeof t==="object"&&"length"in t?t:Array.from(t)}},84653:(t,n,e)=>{e.d(n,{A:()=>i});function i(t){return function n(){return t}}},24363:(t,n,e)=>{e.d(n,{Ay:()=>a,xO:()=>r,zx:()=>i});function i(t,n,e){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+n)/6,(t._y0+4*t._y1+e)/6)}function r(t){this._context=t}r.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN;this._point=0},lineEnd:function(){switch(this._point){case 3:i(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}if(this._line||this._line!==0&&this._point===1)this._context.closePath();this._line=1-this._line},point:function(t,n){t=+t,n=+n;switch(this._point){case 0:this._point=1;this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3;this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:i(this,t,n);break}this._x0=this._x1,this._x1=t;this._y0=this._y1,this._y1=n}};function a(t){return new r(t)}},60075:(t,n,e)=>{e.d(n,{A:()=>o});var i=e(71649);var r=e(24363);function a(t){this._context=t}a.prototype={areaStart:i.A,areaEnd:i.A,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN;this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2);this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3);this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3);this._context.closePath();break}case 3:{this.point(this._x2,this._y2);this.point(this._x3,this._y3);this.point(this._x4,this._y4);break}}},point:function(t,n){t=+t,n=+n;switch(this._point){case 0:this._point=1;this._x2=t,this._y2=n;break;case 1:this._point=2;this._x3=t,this._y3=n;break;case 2:this._point=3;this._x4=t,this._y4=n;this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+n)/6);break;default:(0,r.zx)(this,t,n);break}this._x0=this._x1,this._x1=t;this._y0=this._y1,this._y1=n}};function o(t){return new a(t)}},69683:(t,n,e)=>{e.d(n,{A:()=>a});var i=e(24363);function r(t){this._context=t}r.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN;this._point=0},lineEnd:function(){if(this._line||this._line!==0&&this._point===3)this._context.closePath();this._line=1-this._line},point:function(t,n){t=+t,n=+n;switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var e=(this._x0+4*this._x1+t)/6,r=(this._y0+4*this._y1+n)/6;this._line?this._context.lineTo(e,r):this._context.moveTo(e,r);break;case 3:this._point=4;default:(0,i.zx)(this,t,n);break}this._x0=this._x1,this._x1=t;this._y0=this._y1,this._y1=n}};function a(t){return new r(t)}},54545:(t,n,e)=>{e.d(n,{A:()=>a});var i=e(24363);function r(t,n){this._basis=new i.xO(t);this._beta=n}r.prototype={lineStart:function(){this._x=[];this._y=[];this._basis.lineStart()},lineEnd:function(){var t=this._x,n=this._y,e=t.length-1;if(e>0){var i=t[0],r=n[0],a=t[e]-i,o=n[e]-r,s=-1,u;while(++s<=e){u=s/e;this._basis.point(this._beta*t[s]+(1-this._beta)*(i+u*a),this._beta*n[s]+(1-this._beta)*(r+u*o))}}this._x=this._y=null;this._basis.lineEnd()},point:function(t,n){this._x.push(+t);this._y.push(+n)}};const a=function t(n){function e(t){return n===1?new i.xO(t):new r(t,n)}e.beta=function(n){return t(+n)};return e}(.85)},43793:(t,n,e)=>{e.d(n,{Ay:()=>a,vP:()=>r,zx:()=>i});function i(t,n,e){t._context.bezierCurveTo(t._x1+t._k*(t._x2-t._x0),t._y1+t._k*(t._y2-t._y0),t._x2+t._k*(t._x1-n),t._y2+t._k*(t._y1-e),t._x2,t._y2)}function r(t,n){this._context=t;this._k=(1-n)/6}r.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN;this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:i(this,this._x1,this._y1);break}if(this._line||this._line!==0&&this._point===1)this._context.closePath();this._line=1-this._line},point:function(t,n){t=+t,n=+n;switch(this._point){case 0:this._point=1;this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;this._x1=t,this._y1=n;break;case 2:this._point=3;default:i(this,t,n);break}this._x0=this._x1,this._x1=this._x2,this._x2=t;this._y0=this._y1,this._y1=this._y2,this._y2=n}};const a=function t(n){function e(t){return new r(t,n)}e.tension=function(n){return t(+n)};return e}(0)},13893:(t,n,e)=>{e.d(n,{A:()=>o,L:()=>a});var i=e(71649);var r=e(43793);function a(t,n){this._context=t;this._k=(1-n)/6}a.prototype={areaStart:i.A,areaEnd:i.A,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN;this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3);this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3);this._context.closePath();break}case 3:{this.point(this._x3,this._y3);this.point(this._x4,this._y4);this.point(this._x5,this._y5);break}}},point:function(t,n){t=+t,n=+n;switch(this._point){case 0:this._point=1;this._x3=t,this._y3=n;break;case 1:this._point=2;this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3;this._x5=t,this._y5=n;break;default:(0,r.zx)(this,t,n);break}this._x0=this._x1,this._x1=this._x2,this._x2=t;this._y0=this._y1,this._y1=this._y2,this._y2=n}};const o=function t(n){function e(t){return new a(t,n)}e.tension=function(n){return t(+n)};return e}(0)},46457:(t,n,e)=>{e.d(n,{A:()=>a,H:()=>r});var i=e(43793);function r(t,n){this._context=t;this._k=(1-n)/6}r.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN;this._point=0},lineEnd:function(){if(this._line||this._line!==0&&this._point===3)this._context.closePath();this._line=1-this._line},point:function(t,n){t=+t,n=+n;switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:(0,i.zx)(this,t,n);break}this._x0=this._x1,this._x1=this._x2,this._x2=t;this._y0=this._y1,this._y1=this._y2,this._y2=n}};const a=function t(n){function e(t){return new r(t,n)}e.tension=function(n){return t(+n)};return e}(0)},76413:(t,n,e)=>{e.d(n,{A:()=>s,z:()=>a});var i=e(98247);var r=e(43793);function a(t,n,e){var r=t._x1,a=t._y1,o=t._x2,s=t._y2;if(t._l01_a>i.Ni){var u=2*t._l01_2a+3*t._l01_a*t._l12_a+t._l12_2a,h=3*t._l01_a*(t._l01_a+t._l12_a);r=(r*u-t._x0*t._l12_2a+t._x2*t._l01_2a)/h;a=(a*u-t._y0*t._l12_2a+t._y2*t._l01_2a)/h}if(t._l23_a>i.Ni){var c=2*t._l23_2a+3*t._l23_a*t._l12_a+t._l12_2a,l=3*t._l23_a*(t._l23_a+t._l12_a);o=(o*c+t._x1*t._l23_2a-n*t._l12_2a)/l;s=(s*c+t._y1*t._l23_2a-e*t._l12_2a)/l}t._context.bezierCurveTo(r,a,o,s,t._x2,t._y2)}function o(t,n){this._context=t;this._alpha=n}o.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN;this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2);break}if(this._line||this._line!==0&&this._point===1)this._context.closePath();this._line=1-this._line},point:function(t,n){t=+t,n=+n;if(this._point){var e=this._x2-t,i=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+i*i,this._alpha))}switch(this._point){case 0:this._point=1;this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3;default:a(this,t,n);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a;this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a;this._x0=this._x1,this._x1=this._x2,this._x2=t;this._y0=this._y1,this._y1=this._y2,this._y2=n}};const s=function t(n){function e(t){return n?new o(t,n):new r.vP(t,0)}e.alpha=function(n){return t(+n)};return e}(.5)},25633:(t,n,e)=>{e.d(n,{A:()=>s});var i=e(13893);var r=e(71649);var a=e(76413);function o(t,n){this._context=t;this._alpha=n}o.prototype={areaStart:r.A,areaEnd:r.A,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN;this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3);this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3);this._context.closePath();break}case 3:{this.point(this._x3,this._y3);this.point(this._x4,this._y4);this.point(this._x5,this._y5);break}}},point:function(t,n){t=+t,n=+n;if(this._point){var e=this._x2-t,i=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+i*i,this._alpha))}switch(this._point){case 0:this._point=1;this._x3=t,this._y3=n;break;case 1:this._point=2;this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3;this._x5=t,this._y5=n;break;default:(0,a.z)(this,t,n);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a;this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a;this._x0=this._x1,this._x1=this._x2,this._x2=t;this._y0=this._y1,this._y1=this._y2,this._y2=n}};const s=function t(n){function e(t){return n?new o(t,n):new i.L(t,0)}e.alpha=function(n){return t(+n)};return e}(.5)},13309:(t,n,e)=>{e.d(n,{A:()=>o});var i=e(46457);var r=e(76413);function a(t,n){this._context=t;this._alpha=n}a.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN;this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){if(this._line||this._line!==0&&this._point===3)this._context.closePath();this._line=1-this._line},point:function(t,n){t=+t,n=+n;if(this._point){var e=this._x2-t,i=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+i*i,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:(0,r.z)(this,t,n);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a;this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a;this._x0=this._x1,this._x1=this._x2,this._x2=t;this._y0=this._y1,this._y1=this._y2,this._y2=n}};const o=function t(n){function e(t){return n?new a(t,n):new i.H(t,0)}e.alpha=function(n){return t(+n)};return e}(.5)},71228:(t,n,e)=>{e.d(n,{A:()=>r});function i(t){this._context=t}i.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){if(this._line||this._line!==0&&this._point===1)this._context.closePath();this._line=1-this._line},point:function(t,n){t=+t,n=+n;switch(this._point){case 0:this._point=1;this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._context.lineTo(t,n);break}}};function r(t){return new i(t)}},43272:(t,n,e)=>{e.d(n,{A:()=>a});var i=e(71649);function r(t){this._context=t}r.prototype={areaStart:i.A,areaEnd:i.A,lineStart:function(){this._point=0},lineEnd:function(){if(this._point)this._context.closePath()},point:function(t,n){t=+t,n=+n;if(this._point)this._context.lineTo(t,n);else this._point=1,this._context.moveTo(t,n)}};function a(t){return new r(t)}},67694:(t,n,e)=>{e.d(n,{G:()=>c,N:()=>l});function i(t){return t<0?-1:1}function r(t,n,e){var r=t._x1-t._x0,a=n-t._x1,o=(t._y1-t._y0)/(r||a<0&&-0),s=(e-t._y1)/(a||r<0&&-0),u=(o*a+s*r)/(r+a);return(i(o)+i(s))*Math.min(Math.abs(o),Math.abs(s),.5*Math.abs(u))||0}function a(t,n){var e=t._x1-t._x0;return e?(3*(t._y1-t._y0)/e-n)/2:n}function o(t,n,e){var i=t._x0,r=t._y0,a=t._x1,o=t._y1,s=(a-i)/3;t._context.bezierCurveTo(i+s,r+s*n,a-s,o-s*e,a,o)}function s(t){this._context=t}s.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN;this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:o(this,this._t0,a(this,this._t0));break}if(this._line||this._line!==0&&this._point===1)this._context.closePath();this._line=1-this._line},point:function(t,n){var e=NaN;t=+t,n=+n;if(t===this._x1&&n===this._y1)return;switch(this._point){case 0:this._point=1;this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3;o(this,a(this,e=r(this,t,n)),e);break;default:o(this,this._t0,e=r(this,t,n));break}this._x0=this._x1,this._x1=t;this._y0=this._y1,this._y1=n;this._t0=e}};function u(t){this._context=new h(t)}(u.prototype=Object.create(s.prototype)).point=function(t,n){s.prototype.point.call(this,n,t)};function h(t){this._context=t}h.prototype={moveTo:function(t,n){this._context.moveTo(n,t)},closePath:function(){this._context.closePath()},lineTo:function(t,n){this._context.lineTo(n,t)},bezierCurveTo:function(t,n,e,i,r,a){this._context.bezierCurveTo(n,t,i,e,a,r)}};function c(t){return new s(t)}function l(t){return new u(t)}},29944:(t,n,e)=>{e.d(n,{A:()=>a});function i(t){this._context=t}i.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[];this._y=[]},lineEnd:function(){var t=this._x,n=this._y,e=t.length;if(e){this._line?this._context.lineTo(t[0],n[0]):this._context.moveTo(t[0],n[0]);if(e===2){this._context.lineTo(t[1],n[1])}else{var i=r(t),a=r(n);for(var o=0,s=1;s<e;++o,++s){this._context.bezierCurveTo(i[0][o],a[0][o],i[1][o],a[1][o],t[s],n[s])}}}if(this._line||this._line!==0&&e===1)this._context.closePath();this._line=1-this._line;this._x=this._y=null},point:function(t,n){this._x.push(+t);this._y.push(+n)}};function r(t){var n,e=t.length-1,i,r=new Array(e),a=new Array(e),o=new Array(e);r[0]=0,a[0]=2,o[0]=t[0]+2*t[1];for(n=1;n<e-1;++n)r[n]=1,a[n]=4,o[n]=4*t[n]+2*t[n+1];r[e-1]=2,a[e-1]=7,o[e-1]=8*t[e-1]+t[e];for(n=1;n<e;++n)i=r[n]/a[n-1],a[n]-=i,o[n]-=i*o[n-1];r[e-1]=o[e-1]/a[e-1];for(n=e-2;n>=0;--n)r[n]=(o[n]-r[n+1])/a[n];a[e-1]=(t[e]+r[e-1])/2;for(n=0;n<e-1;++n)a[n]=2*t[n+1]-r[n+1];return[r,a]}function a(t){return new i(t)}},79011:(t,n,e)=>{e.d(n,{Ay:()=>r,Ko:()=>a,Ps:()=>o});function i(t,n){this._context=t;this._t=n}i.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN;this._point=0},lineEnd:function(){if(0<this._t&&this._t<1&&this._point===2)this._context.lineTo(this._x,this._y);if(this._line||this._line!==0&&this._point===1)this._context.closePath();if(this._line>=0)this._t=1-this._t,this._line=1-this._line},point:function(t,n){t=+t,n=+n;switch(this._point){case 0:this._point=1;this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:{if(this._t<=0){this._context.lineTo(this._x,n);this._context.lineTo(t,n)}else{var e=this._x*(1-this._t)+t*this._t;this._context.lineTo(e,this._y);this._context.lineTo(e,n)}break}}this._x=t,this._y=n}};function r(t){return new i(t,.5)}function a(t){return new i(t,0)}function o(t){return new i(t,1)}},58679:(t,n,e)=>{e.d(n,{A:()=>u});var i=e(12736);var r=e(84653);var a=e(71228);var o=e(18226);var s=e(59835);function u(t,n){var e=(0,r.A)(true),u=null,h=a.A,c=null,l=(0,o.i)(f);t=typeof t==="function"?t:t===undefined?s.x:(0,r.A)(t);n=typeof n==="function"?n:n===undefined?s.y:(0,r.A)(n);function f(r){var a,o=(r=(0,i.A)(r)).length,s,f=false,_;if(u==null)c=h(_=l());for(a=0;a<=o;++a){if(!(a<o&&e(s=r[a],a,r))===f){if(f=!f)c.lineStart();else c.lineEnd()}if(f)c.point(+t(s,a,r),+n(s,a,r))}if(_)return c=null,_+""||null}f.x=function(n){return arguments.length?(t=typeof n==="function"?n:(0,r.A)(+n),f):t};f.y=function(t){return arguments.length?(n=typeof t==="function"?t:(0,r.A)(+t),f):n};f.defined=function(t){return arguments.length?(e=typeof t==="function"?t:(0,r.A)(!!t),f):e};f.curve=function(t){return arguments.length?(h=t,u!=null&&(c=h(u)),f):h};f.context=function(t){return arguments.length?(t==null?u=c=null:c=h(u=t),f):u};return f}},98247:(t,n,e)=>{e.d(n,{F8:()=>u,FA:()=>_,FP:()=>r,HQ:()=>p,Ni:()=>c,RZ:()=>h,T9:()=>o,TW:()=>f,gn:()=>a,jk:()=>s,pi:()=>l,qR:()=>y,tn:()=>i});const i=Math.abs;const r=Math.atan2;const a=Math.cos;const o=Math.max;const s=Math.min;const u=Math.sin;const h=Math.sqrt;const c=1e-12;const l=Math.PI;const f=l/2;const _=2*l;function p(t){return t>1?0:t<-1?l:Math.acos(t)}function y(t){return t>=1?f:t<=-1?-f:Math.asin(t)}},71649:(t,n,e)=>{e.d(n,{A:()=>i});function i(){}},18226:(t,n,e)=>{e.d(n,{i:()=>r});var i=e(69450);function r(t){let n=3;t.digits=function(e){if(!arguments.length)return n;if(e==null){n=null}else{const t=Math.floor(e);if(!(t>=0))throw new RangeError(`invalid digits: ${e}`);n=t}return t};return()=>new i.wA(n)}},59835:(t,n,e)=>{e.d(n,{x:()=>i,y:()=>r});function i(t){return t[0]}function r(t){return t[1]}},82692:(t,n,e)=>{e.d(n,{DC:()=>a,GY:()=>u,T6:()=>o,aL:()=>s});var i=e(77613);var r;var a;var o;var s;var u;h({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function h(t){r=(0,i.A)(t);a=r.format;o=r.parse;s=r.utcFormat;u=r.utcParse;return r}},77613:(t,n,e)=>{e.d(n,{A:()=>h});var i=e(61779);var r=e(20293);var a=e(42706);function o(t){if(0<=t.y&&t.y<100){var n=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);n.setFullYear(t.y);return n}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function s(t){if(0<=t.y&&t.y<100){var n=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));n.setUTCFullYear(t.y);return n}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function u(t,n,e){return{y:t,m:n,d:e,H:0,M:0,S:0,L:0}}function h(t){var n=t.dateTime,e=t.date,a=t.time,h=t.periods,l=t.days,f=t.shortDays,_=t.months,p=t.shortMonths;var y=d(h),Z=g(h),yt=d(l),Nt=g(l),Ct=d(f),$t=g(f),Ut=d(_),Ft=g(_),Dt=d(p),St=g(p);var Pt={a:Jt,A:Zt,b:Qt,B:Wt,c:null,d:Y,e:Y,f:I,g:tt,G:et,H:q,I:L,j,L:z,m:X,M:O,p:Vt,q:Kt,Q:Tt,s:kt,S:B,u:G,U:J,V:Q,w:W,W:V,x:null,X:null,y:K,Y:nt,Z:it,"%":Mt};var Et={a:tn,A:nn,b:en,B:rn,c:null,d:rt,e:rt,f:ht,g:wt,G:At,H:at,I:ot,j:st,L:ut,m:ct,M:lt,p:an,q:on,Q:Tt,s:kt,S:ft,u:_t,U:pt,V:dt,w:gt,W:vt,x:null,X:null,y:xt,Y:mt,Z:bt,"%":Mt};var Rt={a:jt,A:zt,b:It,B:Xt,c:Ot,d:C,e:C,f:P,g:M,G:b,H:U,I:U,j:$,L:S,m:N,M:F,p:Lt,q:k,Q:R,s:H,S:D,u:x,U:w,V:m,w:v,W:A,x:Bt,X:Gt,y:M,Y:b,Z:T,"%":E};Pt.x=Ht(e,Pt);Pt.X=Ht(a,Pt);Pt.c=Ht(n,Pt);Et.x=Ht(e,Et);Et.X=Ht(a,Et);Et.c=Ht(n,Et);function Ht(t,n){return function(e){var i=[],r=-1,a=0,o=t.length,s,u,h;if(!(e instanceof Date))e=new Date(+e);while(++r<o){if(t.charCodeAt(r)===37){i.push(t.slice(a,r));if((u=c[s=t.charAt(++r)])!=null)s=t.charAt(++r);else u=s==="e"?" ":"0";if(h=n[s])s=h(e,u);i.push(s);a=r+1}}i.push(t.slice(a,r));return i.join("")}}function Yt(t,n){return function(e){var a=u(1900,undefined,1),h=qt(a,t,e+="",0),c,l;if(h!=e.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(a.s*1e3+("L"in a?a.L:0));if(n&&!("Z"in a))a.Z=0;if("p"in a)a.H=a.H%12+a.p*12;if(a.m===undefined)a.m="q"in a?a.q:0;if("V"in a){if(a.V<1||a.V>53)return null;if(!("w"in a))a.w=1;if("Z"in a){c=s(u(a.y,0,1)),l=c.getUTCDay();c=l>4||l===0?i.rt.ceil(c):(0,i.rt)(c);c=r.dA.offset(c,(a.V-1)*7);a.y=c.getUTCFullYear();a.m=c.getUTCMonth();a.d=c.getUTCDate()+(a.w+6)%7}else{c=o(u(a.y,0,1)),l=c.getDay();c=l>4||l===0?i.AB.ceil(c):(0,i.AB)(c);c=r.UA.offset(c,(a.V-1)*7);a.y=c.getFullYear();a.m=c.getMonth();a.d=c.getDate()+(a.w+6)%7}}else if("W"in a||"U"in a){if(!("w"in a))a.w="u"in a?a.u%7:"W"in a?1:0;l="Z"in a?s(u(a.y,0,1)).getUTCDay():o(u(a.y,0,1)).getDay();a.m=0;a.d="W"in a?(a.w+6)%7+a.W*7-(l+5)%7:a.w+a.U*7-(l+6)%7}if("Z"in a){a.H+=a.Z/100|0;a.M+=a.Z%100;return s(a)}return o(a)}}function qt(t,n,e,i){var r=0,a=n.length,o=e.length,s,u;while(r<a){if(i>=o)return-1;s=n.charCodeAt(r++);if(s===37){s=n.charAt(r++);u=Rt[s in c?n.charAt(r++):s];if(!u||(i=u(t,e,i))<0)return-1}else if(s!=e.charCodeAt(i++)){return-1}}return i}function Lt(t,n,e){var i=y.exec(n.slice(e));return i?(t.p=Z.get(i[0].toLowerCase()),e+i[0].length):-1}function jt(t,n,e){var i=Ct.exec(n.slice(e));return i?(t.w=$t.get(i[0].toLowerCase()),e+i[0].length):-1}function zt(t,n,e){var i=yt.exec(n.slice(e));return i?(t.w=Nt.get(i[0].toLowerCase()),e+i[0].length):-1}function It(t,n,e){var i=Dt.exec(n.slice(e));return i?(t.m=St.get(i[0].toLowerCase()),e+i[0].length):-1}function Xt(t,n,e){var i=Ut.exec(n.slice(e));return i?(t.m=Ft.get(i[0].toLowerCase()),e+i[0].length):-1}function Ot(t,e,i){return qt(t,n,e,i)}function Bt(t,n,i){return qt(t,e,n,i)}function Gt(t,n,e){return qt(t,a,n,e)}function Jt(t){return f[t.getDay()]}function Zt(t){return l[t.getDay()]}function Qt(t){return p[t.getMonth()]}function Wt(t){return _[t.getMonth()]}function Vt(t){return h[+(t.getHours()>=12)]}function Kt(t){return 1+~~(t.getMonth()/3)}function tn(t){return f[t.getUTCDay()]}function nn(t){return l[t.getUTCDay()]}function en(t){return p[t.getUTCMonth()]}function rn(t){return _[t.getUTCMonth()]}function an(t){return h[+(t.getUTCHours()>=12)]}function on(t){return 1+~~(t.getUTCMonth()/3)}return{format:function(t){var n=Ht(t+="",Pt);n.toString=function(){return t};return n},parse:function(t){var n=Yt(t+="",false);n.toString=function(){return t};return n},utcFormat:function(t){var n=Ht(t+="",Et);n.toString=function(){return t};return n},utcParse:function(t){var n=Yt(t+="",true);n.toString=function(){return t};return n}}}var c={"-":"",_:" ",0:"0"},l=/^\s*\d+/,f=/^%/,_=/[\\^$*+?|[\]().{}]/g;function p(t,n,e){var i=t<0?"-":"",r=(i?-t:t)+"",a=r.length;return i+(a<e?new Array(e-a+1).join(n)+r:r)}function y(t){return t.replace(_,"\\$&")}function d(t){return new RegExp("^(?:"+t.map(y).join("|")+")","i")}function g(t){return new Map(t.map(((t,n)=>[t.toLowerCase(),n])))}function v(t,n,e){var i=l.exec(n.slice(e,e+1));return i?(t.w=+i[0],e+i[0].length):-1}function x(t,n,e){var i=l.exec(n.slice(e,e+1));return i?(t.u=+i[0],e+i[0].length):-1}function w(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.U=+i[0],e+i[0].length):-1}function m(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.V=+i[0],e+i[0].length):-1}function A(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.W=+i[0],e+i[0].length):-1}function b(t,n,e){var i=l.exec(n.slice(e,e+4));return i?(t.y=+i[0],e+i[0].length):-1}function M(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.y=+i[0]+(+i[0]>68?1900:2e3),e+i[0].length):-1}function T(t,n,e){var i=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(n.slice(e,e+6));return i?(t.Z=i[1]?0:-(i[2]+(i[3]||"00")),e+i[0].length):-1}function k(t,n,e){var i=l.exec(n.slice(e,e+1));return i?(t.q=i[0]*3-3,e+i[0].length):-1}function N(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.m=i[0]-1,e+i[0].length):-1}function C(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.d=+i[0],e+i[0].length):-1}function $(t,n,e){var i=l.exec(n.slice(e,e+3));return i?(t.m=0,t.d=+i[0],e+i[0].length):-1}function U(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.H=+i[0],e+i[0].length):-1}function F(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.M=+i[0],e+i[0].length):-1}function D(t,n,e){var i=l.exec(n.slice(e,e+2));return i?(t.S=+i[0],e+i[0].length):-1}function S(t,n,e){var i=l.exec(n.slice(e,e+3));return i?(t.L=+i[0],e+i[0].length):-1}function P(t,n,e){var i=l.exec(n.slice(e,e+6));return i?(t.L=Math.floor(i[0]/1e3),e+i[0].length):-1}function E(t,n,e){var i=f.exec(n.slice(e,e+1));return i?e+i[0].length:-1}function R(t,n,e){var i=l.exec(n.slice(e));return i?(t.Q=+i[0],e+i[0].length):-1}function H(t,n,e){var i=l.exec(n.slice(e));return i?(t.s=+i[0],e+i[0].length):-1}function Y(t,n){return p(t.getDate(),n,2)}function q(t,n){return p(t.getHours(),n,2)}function L(t,n){return p(t.getHours()%12||12,n,2)}function j(t,n){return p(1+r.UA.count((0,a.he)(t),t),n,3)}function z(t,n){return p(t.getMilliseconds(),n,3)}function I(t,n){return z(t,n)+"000"}function X(t,n){return p(t.getMonth()+1,n,2)}function O(t,n){return p(t.getMinutes(),n,2)}function B(t,n){return p(t.getSeconds(),n,2)}function G(t){var n=t.getDay();return n===0?7:n}function J(t,n){return p(i.YP.count((0,a.he)(t)-1,t),n,2)}function Z(t){var n=t.getDay();return n>=4||n===0?(0,i.Mo)(t):i.Mo.ceil(t)}function Q(t,n){t=Z(t);return p(i.Mo.count((0,a.he)(t),t)+((0,a.he)(t).getDay()===4),n,2)}function W(t){return t.getDay()}function V(t,n){return p(i.AB.count((0,a.he)(t)-1,t),n,2)}function K(t,n){return p(t.getFullYear()%100,n,2)}function tt(t,n){t=Z(t);return p(t.getFullYear()%100,n,2)}function nt(t,n){return p(t.getFullYear()%1e4,n,4)}function et(t,n){var e=t.getDay();t=e>=4||e===0?(0,i.Mo)(t):i.Mo.ceil(t);return p(t.getFullYear()%1e4,n,4)}function it(t){var n=t.getTimezoneOffset();return(n>0?"-":(n*=-1,"+"))+p(n/60|0,"0",2)+p(n%60,"0",2)}function rt(t,n){return p(t.getUTCDate(),n,2)}function at(t,n){return p(t.getUTCHours(),n,2)}function ot(t,n){return p(t.getUTCHours()%12||12,n,2)}function st(t,n){return p(1+r.dA.count((0,a.Mb)(t),t),n,3)}function ut(t,n){return p(t.getUTCMilliseconds(),n,3)}function ht(t,n){return ut(t,n)+"000"}function ct(t,n){return p(t.getUTCMonth()+1,n,2)}function lt(t,n){return p(t.getUTCMinutes(),n,2)}function ft(t,n){return p(t.getUTCSeconds(),n,2)}function _t(t){var n=t.getUTCDay();return n===0?7:n}function pt(t,n){return p(i.Hl.count((0,a.Mb)(t)-1,t),n,2)}function yt(t){var n=t.getUTCDay();return n>=4||n===0?(0,i.pT)(t):i.pT.ceil(t)}function dt(t,n){t=yt(t);return p(i.pT.count((0,a.Mb)(t),t)+((0,a.Mb)(t).getUTCDay()===4),n,2)}function gt(t){return t.getUTCDay()}function vt(t,n){return p(i.rt.count((0,a.Mb)(t)-1,t),n,2)}function xt(t,n){return p(t.getUTCFullYear()%100,n,2)}function wt(t,n){t=yt(t);return p(t.getUTCFullYear()%100,n,2)}function mt(t,n){return p(t.getUTCFullYear()%1e4,n,4)}function At(t,n){var e=t.getUTCDay();t=e>=4||e===0?(0,i.pT)(t):i.pT.ceil(t);return p(t.getUTCFullYear()%1e4,n,4)}function bt(){return"+0000"}function Mt(){return"%"}function Tt(t){return+t}function kt(t){return Math.floor(+t/1e3)}},20293:(t,n,e)=>{e.d(n,{TW:()=>h,UA:()=>a,dA:()=>s});var i=e(12834);var r=e(29551);const a=(0,i.f)((t=>t.setHours(0,0,0,0)),((t,n)=>t.setDate(t.getDate()+n)),((t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*r.rR)/r.Nm),(t=>t.getDate()-1));const o=a.range;const s=(0,i.f)((t=>{t.setUTCHours(0,0,0,0)}),((t,n)=>{t.setUTCDate(t.getUTCDate()+n)}),((t,n)=>(n-t)/r.Nm),(t=>t.getUTCDate()-1));const u=s.range;const h=(0,i.f)((t=>{t.setUTCHours(0,0,0,0)}),((t,n)=>{t.setUTCDate(t.getUTCDate()+n)}),((t,n)=>(n-t)/r.Nm),(t=>Math.floor(t/r.Nm)));const c=h.range},29551:(t,n,e)=>{e.d(n,{Fq:()=>s,JJ:()=>a,MP:()=>h,Nm:()=>o,Pv:()=>u,Tt:()=>i,rR:()=>r});const i=1e3;const r=i*60;const a=r*60;const o=a*24;const s=o*7;const u=o*30;const h=o*365},9017:(t,n,e)=>{e.d(n,{Ag:()=>a,pz:()=>s});var i=e(12834);var r=e(29551);const a=(0,i.f)((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*r.Tt-t.getMinutes()*r.rR)}),((t,n)=>{t.setTime(+t+n*r.JJ)}),((t,n)=>(n-t)/r.JJ),(t=>t.getHours()));const o=a.range;const s=(0,i.f)((t=>{t.setUTCMinutes(0,0,0)}),((t,n)=>{t.setTime(+t+n*r.JJ)}),((t,n)=>(n-t)/r.JJ),(t=>t.getUTCHours()));const u=s.range},12834:(t,n,e)=>{e.d(n,{f:()=>a});const i=new Date,r=new Date;function a(t,n,e,o){function s(n){return t(n=arguments.length===0?new Date:new Date(+n)),n}s.floor=n=>(t(n=new Date(+n)),n);s.ceil=e=>(t(e=new Date(e-1)),n(e,1),t(e),e);s.round=t=>{const n=s(t),e=s.ceil(t);return t-n<e-t?n:e};s.offset=(t,e)=>(n(t=new Date(+t),e==null?1:Math.floor(e)),t);s.range=(e,i,r)=>{const a=[];e=s.ceil(e);r=r==null?1:Math.floor(r);if(!(e<i)||!(r>0))return a;let o;do{a.push(o=new Date(+e)),n(e,r),t(e)}while(o<e&&e<i);return a};s.filter=e=>a((n=>{if(n>=n)while(t(n),!e(n))n.setTime(n-1)}),((t,i)=>{if(t>=t){if(i<0)while(++i<=0){while(n(t,-1),!e(t)){}}else while(--i>=0){while(n(t,+1),!e(t)){}}}}));if(e){s.count=(n,a)=>{i.setTime(+n),r.setTime(+a);t(i),t(r);return Math.floor(e(i,r))};s.every=t=>{t=Math.floor(t);return!isFinite(t)||!(t>0)?null:!(t>1)?s:s.filter(o?n=>o(n)%t===0:n=>s.count(0,n)%t===0)}}return s}},26530:(t,n,e)=>{e.d(n,{y:()=>r});var i=e(12834);const r=(0,i.f)((()=>{}),((t,n)=>{t.setTime(+t+n)}),((t,n)=>n-t));r.every=t=>{t=Math.floor(t);if(!isFinite(t)||!(t>0))return null;if(!(t>1))return r;return(0,i.f)((n=>{n.setTime(Math.floor(n/t)*t)}),((n,e)=>{n.setTime(+n+e*t)}),((n,e)=>(e-n)/t))};const a=r.range},23383:(t,n,e)=>{e.d(n,{vD:()=>s,wX:()=>a});var i=e(12834);var r=e(29551);const a=(0,i.f)((t=>{t.setTime(t-t.getMilliseconds()-t.getSeconds()*r.Tt)}),((t,n)=>{t.setTime(+t+n*r.rR)}),((t,n)=>(n-t)/r.rR),(t=>t.getMinutes()));const o=a.range;const s=(0,i.f)((t=>{t.setUTCSeconds(0,0)}),((t,n)=>{t.setTime(+t+n*r.rR)}),((t,n)=>(n-t)/r.rR),(t=>t.getUTCMinutes()));const u=s.range},77849:(t,n,e)=>{e.d(n,{R6:()=>o,Ui:()=>r});var i=e(12834);const r=(0,i.f)((t=>{t.setDate(1);t.setHours(0,0,0,0)}),((t,n)=>{t.setMonth(t.getMonth()+n)}),((t,n)=>n.getMonth()-t.getMonth()+(n.getFullYear()-t.getFullYear())*12),(t=>t.getMonth()));const a=r.range;const o=(0,i.f)((t=>{t.setUTCDate(1);t.setUTCHours(0,0,0,0)}),((t,n)=>{t.setUTCMonth(t.getUTCMonth()+n)}),((t,n)=>n.getUTCMonth()-t.getUTCMonth()+(n.getUTCFullYear()-t.getUTCFullYear())*12),(t=>t.getUTCMonth()));const s=o.range},61147:(t,n,e)=>{e.d(n,{R:()=>a});var i=e(12834);var r=e(29551);const a=(0,i.f)((t=>{t.setTime(t-t.getMilliseconds())}),((t,n)=>{t.setTime(+t+n*r.Tt)}),((t,n)=>(n-t)/r.Tt),(t=>t.getUTCSeconds()));const o=a.range},20421:(t,n,e)=>{e.d(n,{$Z:()=>y,Cf:()=>g,lk:()=>d,yE:()=>v});var i=e(9791);var r=e(97119);var a=e(29551);var o=e(26530);var s=e(61147);var u=e(23383);var h=e(9017);var c=e(20293);var l=e(61779);var f=e(77849);var _=e(42706);function p(t,n,e,u,h,c){const l=[[s.R,1,a.Tt],[s.R,5,5*a.Tt],[s.R,15,15*a.Tt],[s.R,30,30*a.Tt],[c,1,a.rR],[c,5,5*a.rR],[c,15,15*a.rR],[c,30,30*a.rR],[h,1,a.JJ],[h,3,3*a.JJ],[h,6,6*a.JJ],[h,12,12*a.JJ],[u,1,a.Nm],[u,2,2*a.Nm],[e,1,a.Fq],[n,1,a.Pv],[n,3,3*a.Pv],[t,1,a.MP]];function f(t,n,e){const i=n<t;if(i)[t,n]=[n,t];const r=e&&typeof e.range==="function"?e:_(t,n,e);const a=r?r.range(t,+n+1):[];return i?a.reverse():a}function _(n,e,s){const u=Math.abs(e-n)/s;const h=(0,i.A)((([,,t])=>t)).right(l,u);if(h===l.length)return t.every((0,r.sG)(n/a.MP,e/a.MP,s));if(h===0)return o.y.every(Math.max((0,r.sG)(n,e,s),1));const[c,f]=l[u/l[h-1][2]<l[h][2]/u?h-1:h];return c.every(f)}return[f,_]}const[y,d]=p(_.Mb,f.R6,l.Hl,c.TW,h.pz,u.vD);const[g,v]=p(_.he,f.Ui,l.YP,c.UA,h.Ag,u.wX)},61779:(t,n,e)=>{e.d(n,{AB:()=>s,Gu:()=>h,Hl:()=>m,Mo:()=>c,PG:()=>u,TU:()=>l,YP:()=>o,pT:()=>T,rG:()=>f,rt:()=>A});var i=e(12834);var r=e(29551);function a(t){return(0,i.f)((n=>{n.setDate(n.getDate()-(n.getDay()+7-t)%7);n.setHours(0,0,0,0)}),((t,n)=>{t.setDate(t.getDate()+n*7)}),((t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*r.rR)/r.Fq))}const o=a(0);const s=a(1);const u=a(2);const h=a(3);const c=a(4);const l=a(5);const f=a(6);const _=o.range;const p=s.range;const y=u.range;const d=h.range;const g=c.range;const v=l.range;const x=f.range;function w(t){return(0,i.f)((n=>{n.setUTCDate(n.getUTCDate()-(n.getUTCDay()+7-t)%7);n.setUTCHours(0,0,0,0)}),((t,n)=>{t.setUTCDate(t.getUTCDate()+n*7)}),((t,n)=>(n-t)/r.Fq))}const m=w(0);const A=w(1);const b=w(2);const M=w(3);const T=w(4);const k=w(5);const N=w(6);const C=m.range;const $=A.range;const U=b.range;const F=M.range;const D=T.range;const S=k.range;const P=N.range},42706:(t,n,e)=>{e.d(n,{Mb:()=>o,he:()=>r});var i=e(12834);const r=(0,i.f)((t=>{t.setMonth(0,1);t.setHours(0,0,0,0)}),((t,n)=>{t.setFullYear(t.getFullYear()+n)}),((t,n)=>n.getFullYear()-t.getFullYear()),(t=>t.getFullYear()));r.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:(0,i.f)((n=>{n.setFullYear(Math.floor(n.getFullYear()/t)*t);n.setMonth(0,1);n.setHours(0,0,0,0)}),((n,e)=>{n.setFullYear(n.getFullYear()+e*t)}));const a=r.range;const o=(0,i.f)((t=>{t.setUTCMonth(0,1);t.setUTCHours(0,0,0,0)}),((t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n)}),((t,n)=>n.getUTCFullYear()-t.getUTCFullYear()),(t=>t.getUTCFullYear()));o.every=t=>!isFinite(t=Math.floor(t))||!(t>0)?null:(0,i.f)((n=>{n.setUTCFullYear(Math.floor(n.getUTCFullYear()/t)*t);n.setUTCMonth(0,1);n.setUTCHours(0,0,0,0)}),((n,e)=>{n.setUTCFullYear(n.getUTCFullYear()+e*t)}));const s=o.range},14036:(t,n,e)=>{e.d(n,{M4:()=>d,O1:()=>g,tB:()=>p});var i=0,r=0,a=0,o=1e3,s,u,h=0,c=0,l=0,f=typeof performance==="object"&&performance.now?performance:Date,_=typeof window==="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function p(){return c||(_(y),c=f.now()+l)}function y(){c=0}function d(){this._call=this._time=this._next=null}d.prototype=g.prototype={constructor:d,restart:function(t,n,e){if(typeof t!=="function")throw new TypeError("callback is not a function");e=(e==null?p():+e)+(n==null?0:+n);if(!this._next&&u!==this){if(u)u._next=this;else s=this;u=this}this._call=t;this._time=e;A()},stop:function(){if(this._call){this._call=null;this._time=Infinity;A()}}};function g(t,n,e){var i=new d;i.restart(t,n,e);return i}function v(){p();++i;var t=s,n;while(t){if((n=c-t._time)>=0)t._call.call(undefined,n);t=t._next}--i}function x(){c=(h=f.now())+l;i=r=0;try{v()}finally{i=0;m();c=0}}function w(){var t=f.now(),n=t-h;if(n>o)l-=n,h=t}function m(){var t,n=s,e,i=Infinity;while(n){if(n._call){if(i>n._time)i=n._time;t=n,n=n._next}else{e=n._next,n._next=null;n=t?t._next=e:s=e}}u=t;A(i)}function A(t){if(i)return;if(r)r=clearTimeout(r);var n=t-c;if(n>24){if(t<Infinity)r=setTimeout(x,t-f.now()-l);if(a)a=clearInterval(a)}else{if(!a)h=f.now(),a=setInterval(w,o);i=1,_(x)}}},30352:(t,n,e)=>{e.d(n,{B:()=>i,v:()=>r});class i extends Map{constructor(t,n=u){super();Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}});if(t!=null)for(const[e,i]of t)this.set(e,i)}get(t){return super.get(a(this,t))}has(t){return super.has(a(this,t))}set(t,n){return super.set(o(this,t),n)}delete(t){return super.delete(s(this,t))}}class r extends Set{constructor(t,n=u){super();Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}});if(t!=null)for(const e of t)this.add(e)}has(t){return super.has(a(this,t))}add(t){return super.add(o(this,t))}delete(t){return super.delete(s(this,t))}}function a({_intern:t,_key:n},e){const i=n(e);return t.has(i)?t.get(i):e}function o({_intern:t,_key:n},e){const i=n(e);if(t.has(i))return t.get(i);t.set(i,e);return e}function s({_intern:t,_key:n},e){const i=n(e);if(t.has(i)){e=t.get(e);t.delete(i)}return e}function u(t){return t!==null&&typeof t==="object"?t.valueOf():t}}}]);