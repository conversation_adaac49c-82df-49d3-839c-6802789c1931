{"build": "h75589b3_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13", "libcxx >=17", "libevent >=2.1.12,<2.1.13.0a0", "libzlib >=1.3.1,<2.0a0", "openssl >=3.3.2,<4.0a0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libthrift-0.21.0-h75589b3_0", "files": ["include/thrift/TApplicationException.h", "include/thrift/TBase.h", "include/thrift/TConfiguration.h", "include/thrift/TDispatchProcessor.h", "include/thrift/TLogging.h", "include/thrift/TNonCopyable.h", "include/thrift/TOutput.h", "include/thrift/TProcessor.h", "include/thrift/TToString.h", "include/thrift/TUuid.h", "include/thrift/Thrift.h", "include/thrift/async/TAsyncBufferProcessor.h", "include/thrift/async/TAsyncChannel.h", "include/thrift/async/TAsyncDispatchProcessor.h", "include/thrift/async/TAsyncProcessor.h", "include/thrift/async/TAsyncProtocolProcessor.h", "include/thrift/async/TConcurrentClientSyncInfo.h", "include/thrift/async/TEvhttpClientChannel.h", "include/thrift/async/TEvhttpServer.h", "include/thrift/concurrency/Exception.h", "include/thrift/concurrency/FunctionRunner.h", "include/thrift/concurrency/Monitor.h", "include/thrift/concurrency/Mutex.h", "include/thrift/concurrency/Thread.h", "include/thrift/concurrency/ThreadFactory.h", "include/thrift/concurrency/ThreadManager.h", "include/thrift/concurrency/TimerManager.h", "include/thrift/config.h", "include/thrift/processor/PeekProcessor.h", "include/thrift/processor/StatsProcessor.h", "include/thrift/processor/TMultiplexedProcessor.h", "include/thrift/protocol/TBase64Utils.h", "include/thrift/protocol/TBinaryProtocol.h", "include/thrift/protocol/TBinaryProtocol.tcc", "include/thrift/protocol/TCompactProtocol.h", "include/thrift/protocol/TCompactProtocol.tcc", "include/thrift/protocol/TDebugProtocol.h", "include/thrift/protocol/TEnum.h", "include/thrift/protocol/THeaderProtocol.h", "include/thrift/protocol/TJSONProtocol.h", "include/thrift/protocol/TList.h", "include/thrift/protocol/TMap.h", "include/thrift/protocol/TMultiplexedProtocol.h", "include/thrift/protocol/TProtocol.h", "include/thrift/protocol/TProtocolDecorator.h", "include/thrift/protocol/TProtocolException.h", "include/thrift/protocol/TProtocolTap.h", "include/thrift/protocol/TProtocolTypes.h", "include/thrift/protocol/TSet.h", "include/thrift/protocol/TVirtualProtocol.h", "include/thrift/qt/TQIODeviceTransport.h", "include/thrift/qt/TQTcpServer.h", "include/thrift/server/TConnectedClient.h", "include/thrift/server/TNonblockingServer.h", "include/thrift/server/TServer.h", "include/thrift/server/TServerFramework.h", "include/thrift/server/TSimpleServer.h", "include/thrift/server/TThreadPoolServer.h", "include/thrift/server/TThreadedServer.h", "include/thrift/thrift-config.h", "include/thrift/thrift_export.h", "include/thrift/transport/PlatformSocket.h", "include/thrift/transport/SocketCommon.h", "include/thrift/transport/TBufferTransports.h", "include/thrift/transport/TFDTransport.h", "include/thrift/transport/TFileTransport.h", "include/thrift/transport/THeaderTransport.h", "include/thrift/transport/THttpClient.h", "include/thrift/transport/THttpServer.h", "include/thrift/transport/THttpTransport.h", "include/thrift/transport/TNonblockingSSLServerSocket.h", "include/thrift/transport/TNonblockingServerSocket.h", "include/thrift/transport/TNonblockingServerTransport.h", "include/thrift/transport/TPipe.h", "include/thrift/transport/TPipeServer.h", "include/thrift/transport/TSSLServerSocket.h", "include/thrift/transport/TSSLSocket.h", "include/thrift/transport/TServerSocket.h", "include/thrift/transport/TServerTransport.h", "include/thrift/transport/TShortReadTransport.h", "include/thrift/transport/TSimpleFileTransport.h", "include/thrift/transport/TSocket.h", "include/thrift/transport/TSocketPool.h", "include/thrift/transport/TSocketUtils.h", "include/thrift/transport/TTransport.h", "include/thrift/transport/TTransportException.h", "include/thrift/transport/TTransportUtils.h", "include/thrift/transport/TVirtualTransport.h", "include/thrift/transport/TWebSocketServer.h", "include/thrift/transport/TZlibTransport.h", "include/thrift/windows/GetTimeOfDay.h", "include/thrift/windows/Operators.h", "include/thrift/windows/OverlappedSubmissionThread.h", "include/thrift/windows/SocketPair.h", "include/thrift/windows/Sync.h", "include/thrift/windows/TWinsockSingleton.h", "include/thrift/windows/WinFcntl.h", "include/thrift/windows/config.h", "lib/cmake/thrift/FindLibevent.cmake", "lib/cmake/thrift/ThriftConfig.cmake", "lib/cmake/thrift/ThriftConfigVersion.cmake", "lib/cmake/thrift/thriftTargets-release.cmake", "lib/cmake/thrift/thriftTargets.cmake", "lib/cmake/thrift/thriftnbTargets-release.cmake", "lib/cmake/thrift/thriftnbTargets.cmake", "lib/cmake/thrift/thriftzTargets-release.cmake", "lib/cmake/thrift/thriftzTargets.cmake", "lib/libthrift.0.21.0.dylib", "lib/libthrift.dylib", "lib/libthriftnb.0.21.0.dylib", "lib/libthriftnb.dylib", "lib/libthriftz.0.21.0.dylib", "lib/libthriftz.dylib", "lib/pkgconfig/thrift-nb.pc", "lib/pkgconfig/thrift-z.pc", "lib/pkgconfig/thrift.pc"], "fn": "libthrift-0.21.0-h75589b3_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libthrift-0.21.0-h75589b3_0", "type": 1}, "md5": "7a472cd20d9ae866aeb6e292b33381d6", "name": "libthrift", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libthrift-0.21.0-h75589b3_0.conda", "paths_data": {"paths": [{"_path": "include/thrift/TApplicationException.h", "path_type": "hardlink", "sha256": "82fe2df360b32d325ab350af5149c249c505ec540441690ae64fc519d2c3a0d3", "sha256_in_prefix": "82fe2df360b32d325ab350af5149c249c505ec540441690ae64fc519d2c3a0d3", "size_in_bytes": 3545}, {"_path": "include/thrift/TBase.h", "path_type": "hardlink", "sha256": "6c6535eb0a2ab61b310851770aecaa528f87e64073d5c0255937044de226acfa", "sha256_in_prefix": "6c6535eb0a2ab61b310851770aecaa528f87e64073d5c0255937044de226acfa", "size_in_bytes": 1198}, {"_path": "include/thrift/TConfiguration.h", "path_type": "hardlink", "sha256": "3184e8d9298396f0a947504dd3a0d00b7d9074462dd43f65fd14ee6675a0d8a8", "sha256_in_prefix": "3184e8d9298396f0a947504dd3a0d00b7d9074462dd43f65fd14ee6675a0d8a8", "size_in_bytes": 2172}, {"_path": "include/thrift/TDispatchProcessor.h", "path_type": "hardlink", "sha256": "d75f223c548db44507ed33d7efbfbd33f9d45563cbf2b013fa9c2ec0abe0a91b", "sha256_in_prefix": "d75f223c548db44507ed33d7efbfbd33f9d45563cbf2b013fa9c2ec0abe0a91b", "size_in_bytes": 5147}, {"_path": "include/thrift/TLogging.h", "path_type": "hardlink", "sha256": "75ed1d3f5f44e19ce647644c57688b58670ac0159035e1ef2988617ffc7b587a", "sha256_in_prefix": "75ed1d3f5f44e19ce647644c57688b58670ac0159035e1ef2988617ffc7b587a", "size_in_bytes": 10429}, {"_path": "include/thrift/TNonCopyable.h", "path_type": "hardlink", "sha256": "fd943b2be342b2044894f42f87299b9427683313c679562683fc5916850851cc", "sha256_in_prefix": "fd943b2be342b2044894f42f87299b9427683313c679562683fc5916850851cc", "size_in_bytes": 1272}, {"_path": "include/thrift/TOutput.h", "path_type": "hardlink", "sha256": "da4eb18659969f9289e7c2c721fd0c92db1ea230a99760376e8ef13fe23a5847", "sha256_in_prefix": "da4eb18659969f9289e7c2c721fd0c92db1ea230a99760376e8ef13fe23a5847", "size_in_bytes": 1848}, {"_path": "include/thrift/TProcessor.h", "path_type": "hardlink", "sha256": "c1aff04ebc3892d60b227fd8789d1886ad4ab80c1e9bb723c2f13be69b92f061", "sha256_in_prefix": "c1aff04ebc3892d60b227fd8789d1886ad4ab80c1e9bb723c2f13be69b92f061", "size_in_bytes": 6786}, {"_path": "include/thrift/TToString.h", "path_type": "hardlink", "sha256": "735862f4419c38abaebc2e32c83c2bf63f91fa811a6fbe31861900eef54a7cf3", "sha256_in_prefix": "735862f4419c38abaebc2e32c83c2bf63f91fa811a6fbe31861900eef54a7cf3", "size_in_bytes": 3660}, {"_path": "include/thrift/TUuid.h", "path_type": "hardlink", "sha256": "fa57365dd59f14cb34eae4c09592fe125a3176fa8d9cbf76df5a4c50cf575a8f", "sha256_in_prefix": "fa57365dd59f14cb34eae4c09592fe125a3176fa8d9cbf76df5a4c50cf575a8f", "size_in_bytes": 4852}, {"_path": "include/thrift/Thrift.h", "path_type": "hardlink", "sha256": "c3bcc564a743135d2c835d006be64d1e923879b6ec92e87a147bf01d2e3625b2", "sha256_in_prefix": "c3bcc564a743135d2c835d006be64d1e923879b6ec92e87a147bf01d2e3625b2", "size_in_bytes": 3286}, {"_path": "include/thrift/async/TAsyncBufferProcessor.h", "path_type": "hardlink", "sha256": "1ec0d280bbafb27260518c5854262de99656e17eb8cab57a6f2ba19ad9ce0181", "sha256_in_prefix": "1ec0d280bbafb27260518c5854262de99656e17eb8cab57a6f2ba19ad9ce0181", "size_in_bytes": 1679}, {"_path": "include/thrift/async/TAsyncChannel.h", "path_type": "hardlink", "sha256": "f4e1ee9ba562fe44258052cf196889e9535f2d03f0ab1efbbb2d1b1133730974", "sha256_in_prefix": "f4e1ee9ba562fe44258052cf196889e9535f2d03f0ab1efbbb2d1b1133730974", "size_in_bytes": 2167}, {"_path": "include/thrift/async/TAsyncDispatchProcessor.h", "path_type": "hardlink", "sha256": "a22ab630cc47920d61d8b1b5f23fa095ffe3b71c2eb3a5d0863e3fcfd2906e8a", "sha256_in_prefix": "a22ab630cc47920d61d8b1b5f23fa095ffe3b71c2eb3a5d0863e3fcfd2906e8a", "size_in_bytes": 5680}, {"_path": "include/thrift/async/TAsyncProcessor.h", "path_type": "hardlink", "sha256": "cca11167cbe1008ef518ca45bd53c0592717df68bad89e8f0e2798a381a4343d", "sha256_in_prefix": "cca11167cbe1008ef518ca45bd53c0592717df68bad89e8f0e2798a381a4343d", "size_in_bytes": 2616}, {"_path": "include/thrift/async/TAsyncProtocolProcessor.h", "path_type": "hardlink", "sha256": "b1ed60a49e93412ad11c3fe9753475574651a0b94aad3dfa7cbc4c6c63b0796e", "sha256_in_prefix": "b1ed60a49e93412ad11c3fe9753475574651a0b94aad3dfa7cbc4c6c63b0796e", "size_in_bytes": 2006}, {"_path": "include/thrift/async/TConcurrentClientSyncInfo.h", "path_type": "hardlink", "sha256": "cca7dde69599073cc131312f3eacffc1c4e27e62c26d241f58ea531ab7991d77", "sha256_in_prefix": "cca7dde69599073cc131312f3eacffc1c4e27e62c26d241f58ea531ab7991d77", "size_in_bytes": 3871}, {"_path": "include/thrift/async/TEvhttpClientChannel.h", "path_type": "hardlink", "sha256": "17eaf81c17b8fddcac524859a2ac8cf8e443fdce6d0859a483a0df14b0dd5b6f", "sha256_in_prefix": "17eaf81c17b8fddcac524859a2ac8cf8e443fdce6d0859a483a0df14b0dd5b6f", "size_in_bytes": 2762}, {"_path": "include/thrift/async/TEvhttpServer.h", "path_type": "hardlink", "sha256": "455610976737fab7367672b765a52d30da81a432f5e71070377bb7e26e2799bb", "sha256_in_prefix": "455610976737fab7367672b765a52d30da81a432f5e71070377bb7e26e2799bb", "size_in_bytes": 2142}, {"_path": "include/thrift/concurrency/Exception.h", "path_type": "hardlink", "sha256": "4e9a1083efe162e09f7b5bc9e62bcdeafb126a85d3f43af59aa542a77e6b575b", "sha256_in_prefix": "4e9a1083efe162e09f7b5bc9e62bcdeafb126a85d3f43af59aa542a77e6b575b", "size_in_bytes": 2150}, {"_path": "include/thrift/concurrency/FunctionRunner.h", "path_type": "hardlink", "sha256": "57b81186019c52524f2666828664ffeb6462f8fa3778a7f66a3f47ae385c5165", "sha256_in_prefix": "57b81186019c52524f2666828664ffeb6462f8fa3778a7f66a3f47ae385c5165", "size_in_bytes": 3802}, {"_path": "include/thrift/concurrency/Monitor.h", "path_type": "hardlink", "sha256": "1aecfc835a196bf47c605fab44e638ae708b0b4c68d603ae10ce45c8d7a94c74", "sha256_in_prefix": "1aecfc835a196bf47c605fab44e638ae708b0b4c68d603ae10ce45c8d7a94c74", "size_in_bytes": 4186}, {"_path": "include/thrift/concurrency/Mutex.h", "path_type": "hardlink", "sha256": "089be62f9cb584df52a871cb23c7c7e2a620f7d6cf6d3e06122cf0c64268a6d8", "sha256_in_prefix": "089be62f9cb584df52a871cb23c7c7e2a620f7d6cf6d3e06122cf0c64268a6d8", "size_in_bytes": 2115}, {"_path": "include/thrift/concurrency/Thread.h", "path_type": "hardlink", "sha256": "f790d5e1489f8e3609831b14faab455fb3f0aae104e618493bec86486116c442", "sha256_in_prefix": "f790d5e1489f8e3609831b14faab455fb3f0aae104e618493bec86486116c442", "size_in_bytes": 4954}, {"_path": "include/thrift/concurrency/ThreadFactory.h", "path_type": "hardlink", "sha256": "692127d0900a9278c7d42bcbfbf79fcfc2e5f23bd4cc3fd19398518a27ac8596", "sha256_in_prefix": "692127d0900a9278c7d42bcbfbf79fcfc2e5f23bd4cc3fd19398518a27ac8596", "size_in_bytes": 2182}, {"_path": "include/thrift/concurrency/ThreadManager.h", "path_type": "hardlink", "sha256": "7114b9f3e59de800dc5a88617d6d2569add300ded4c385f5f8e745165bdd31e6", "sha256_in_prefix": "7114b9f3e59de800dc5a88617d6d2569add300ded4c385f5f8e745165bdd31e6", "size_in_bytes": 6903}, {"_path": "include/thrift/concurrency/TimerManager.h", "path_type": "hardlink", "sha256": "33468aabeab03cee275cbe7d4e0e22a49328e23e7c7848af09b8b5ec4edb5d75", "sha256_in_prefix": "33468aabeab03cee275cbe7d4e0e22a49328e23e7c7848af09b8b5ec4edb5d75", "size_in_bytes": 4447}, {"_path": "include/thrift/config.h", "path_type": "hardlink", "sha256": "643856335658af16dc8c84cfe392c418766431445d74498af7b5bfbc3274eb94", "sha256_in_prefix": "643856335658af16dc8c84cfe392c418766431445d74498af7b5bfbc3274eb94", "size_in_bytes": 4878}, {"_path": "include/thrift/processor/PeekProcessor.h", "path_type": "hardlink", "sha256": "8c6c063f6c8800f298aa64910cea1b3c1693904eb409c27b081d808f19993b22", "sha256_in_prefix": "8c6c063f6c8800f298aa64910cea1b3c1693904eb409c27b081d808f19993b22", "size_in_bytes": 3278}, {"_path": "include/thrift/processor/StatsProcessor.h", "path_type": "hardlink", "sha256": "f7e56466dbfc67bc32310830e95017b50cf24ed90935145a00b63f7c746e5b74", "sha256_in_prefix": "f7e56466dbfc67bc32310830e95017b50cf24ed90935145a00b63f7c746e5b74", "size_in_bytes": 6220}, {"_path": "include/thrift/processor/TMultiplexedProcessor.h", "path_type": "hardlink", "sha256": "8c303eac81148ba276639fee8b82e039bee63d39870f84d33014199704f334ca", "sha256_in_prefix": "8c303eac81148ba276639fee8b82e039bee63d39870f84d33014199704f334ca", "size_in_bytes": 8471}, {"_path": "include/thrift/protocol/TBase64Utils.h", "path_type": "hardlink", "sha256": "7d2407d4454ce55e6efd80709024468da2b3b305ab099d86c92d59b07fa20750", "sha256_in_prefix": "7d2407d4454ce55e6efd80709024468da2b3b305ab099d86c92d59b07fa20750", "size_in_bytes": 1629}, {"_path": "include/thrift/protocol/TBinaryProtocol.h", "path_type": "hardlink", "sha256": "a99ba72437e4330ccdfbf2d5981fd644b28106553946ada78eab702fd94118e6", "sha256_in_prefix": "a99ba72437e4330ccdfbf2d5981fd644b28106553946ada78eab702fd94118e6", "size_in_bytes": 8633}, {"_path": "include/thrift/protocol/TBinaryProtocol.tcc", "path_type": "hardlink", "sha256": "4372c23db86b61d6f8c78aa4b00dec1d951e54ff0230e490dff9e70f894c2d30", "sha256_in_prefix": "4372c23db86b61d6f8c78aa4b00dec1d951e54ff0230e490dff9e70f894c2d30", "size_in_bytes": 16017}, {"_path": "include/thrift/protocol/TCompactProtocol.h", "path_type": "hardlink", "sha256": "fe4eeed083b41facfebd60f303af47a58ae96fd6299732e7e1f861dda31f655e", "sha256_in_prefix": "fe4eeed083b41facfebd60f303af47a58ae96fd6299732e7e1f861dda31f655e", "size_in_bytes": 8486}, {"_path": "include/thrift/protocol/TCompactProtocol.tcc", "path_type": "hardlink", "sha256": "4398377f3c83d59babc4a8bc61c1d583c88765e9696716960a2b8ed685fac4b2", "sha256_in_prefix": "4398377f3c83d59babc4a8bc61c1d583c88765e9696716960a2b8ed685fac4b2", "size_in_bytes": 25266}, {"_path": "include/thrift/protocol/TDebugProtocol.h", "path_type": "hardlink", "sha256": "a33b16fdf4ec6ba28180ef2a26091247fd3df8a7724688241646896216c75746", "sha256_in_prefix": "a33b16fdf4ec6ba28180ef2a26091247fd3df8a7724688241646896216c75746", "size_in_bytes": 5665}, {"_path": "include/thrift/protocol/TEnum.h", "path_type": "hardlink", "sha256": "62127c9bd0b868efd5081bf035d3e14e7c7c2fd31965226ddf1ac0e52d8ef277", "sha256_in_prefix": "62127c9bd0b868efd5081bf035d3e14e7c7c2fd31965226ddf1ac0e52d8ef277", "size_in_bytes": 1732}, {"_path": "include/thrift/protocol/THeaderProtocol.h", "path_type": "hardlink", "sha256": "04efe4a681bfea4c312f61b919b185dd9988fe91a1b7028cfb695d7a5e1a2d9c", "sha256_in_prefix": "04efe4a681bfea4c312f61b919b185dd9988fe91a1b7028cfb695d7a5e1a2d9c", "size_in_bytes": 5992}, {"_path": "include/thrift/protocol/TJSONProtocol.h", "path_type": "hardlink", "sha256": "964175455db4f92518b1d5fec55b8376b00a440458f291f264e0972551cc030a", "sha256_in_prefix": "964175455db4f92518b1d5fec55b8376b00a440458f291f264e0972551cc030a", "size_in_bytes": 9988}, {"_path": "include/thrift/protocol/TList.h", "path_type": "hardlink", "sha256": "030c6d25f3475f291905c2c6f92fe667c48fd4d3eeea39cdb168239a6bc32e93", "sha256_in_prefix": "030c6d25f3475f291905c2c6f92fe667c48fd4d3eeea39cdb168239a6bc32e93", "size_in_bytes": 1280}, {"_path": "include/thrift/protocol/TMap.h", "path_type": "hardlink", "sha256": "188445d56146d5cd8801e0b69d50bb05acf8f518b3dfd247c82b76775ede838b", "sha256_in_prefix": "188445d56146d5cd8801e0b69d50bb05acf8f518b3dfd247c82b76775ede838b", "size_in_bytes": 1335}, {"_path": "include/thrift/protocol/TMultiplexedProtocol.h", "path_type": "hardlink", "sha256": "cea5efef544e4ba2b86051b16076c9331b1d2ed4c39d7b5510b1b0ea3797643b", "sha256_in_prefix": "cea5efef544e4ba2b86051b16076c9331b1d2ed4c39d7b5510b1b0ea3797643b", "size_in_bytes": 3700}, {"_path": "include/thrift/protocol/TProtocol.h", "path_type": "hardlink", "sha256": "e1a04f27f75514c5ca62c27e9da3cf1e8ca276f1e750f6f9f66f7dd833691dc6", "sha256_in_prefix": "e1a04f27f75514c5ca62c27e9da3cf1e8ca276f1e750f6f9f66f7dd833691dc6", "size_in_bytes": 22370}, {"_path": "include/thrift/protocol/TProtocolDecorator.h", "path_type": "hardlink", "sha256": "0abd2cfbc248cd76be99b49992dfe11a159d49d9b1ff80124df6cb04c1d14e1d", "sha256_in_prefix": "0abd2cfbc248cd76be99b49992dfe11a159d49d9b1ff80124df6cb04c1d14e1d", "size_in_bytes": 6540}, {"_path": "include/thrift/protocol/TProtocolException.h", "path_type": "hardlink", "sha256": "03dba217759a6859724edaf12f6dd82d4bdd392634deaa5026d6839cb022b9c8", "sha256_in_prefix": "03dba217759a6859724edaf12f6dd82d4bdd392634deaa5026d6839cb022b9c8", "size_in_bytes": 3264}, {"_path": "include/thrift/protocol/TProtocolTap.h", "path_type": "hardlink", "sha256": "ab2a2eceb7456fc2a0f873f40ee56d4c139ae7490653c728bf44db9bf0421654", "sha256_in_prefix": "ab2a2eceb7456fc2a0f873f40ee56d4c139ae7490653c728bf44db9bf0421654", "size_in_bytes": 4791}, {"_path": "include/thrift/protocol/TProtocolTypes.h", "path_type": "hardlink", "sha256": "5df4e5349a2ec53281d1fbf224378cb3b1d3b23c24ade5c18234d19864ff4d96", "sha256_in_prefix": "5df4e5349a2ec53281d1fbf224378cb3b1d3b23c24ade5c18234d19864ff4d96", "size_in_bytes": 1141}, {"_path": "include/thrift/protocol/TSet.h", "path_type": "hardlink", "sha256": "989311eed80daab99b70dd2f1352da2b8fc3e106dd682488aa8ed17d179a1e57", "sha256_in_prefix": "989311eed80daab99b70dd2f1352da2b8fc3e106dd682488aa8ed17d179a1e57", "size_in_bytes": 1385}, {"_path": "include/thrift/protocol/TVirtualProtocol.h", "path_type": "hardlink", "sha256": "4a11954a8f74252c53f7c1ed8d57762f4e4203d6cd713b6fa0f8a2522209411c", "sha256_in_prefix": "4a11954a8f74252c53f7c1ed8d57762f4e4203d6cd713b6fa0f8a2522209411c", "size_in_bytes": 18473}, {"_path": "include/thrift/qt/TQIODeviceTransport.h", "path_type": "hardlink", "sha256": "734f378656bacb9f367c57ce3ffddf212380d867a05bc8caf5c2edb84d99ac20", "sha256_in_prefix": "734f378656bacb9f367c57ce3ffddf212380d867a05bc8caf5c2edb84d99ac20", "size_in_bytes": 2001}, {"_path": "include/thrift/qt/TQTcpServer.h", "path_type": "hardlink", "sha256": "f97494310e73f6b378258f74ce6f62b256f2aeecc5d59a0e46d3d32434327b65", "sha256_in_prefix": "f97494310e73f6b378258f74ce6f62b256f2aeecc5d59a0e46d3d32434327b65", "size_in_bytes": 2378}, {"_path": "include/thrift/server/TConnectedClient.h", "path_type": "hardlink", "sha256": "5902b0b01ed6127ba24973be34e007efe9461ee687f6a3a9f05a24962b3b7a30", "sha256_in_prefix": "5902b0b01ed6127ba24973be34e007efe9461ee687f6a3a9f05a24962b3b7a30", "size_in_bytes": 3874}, {"_path": "include/thrift/server/TNonblockingServer.h", "path_type": "hardlink", "sha256": "bfa1e4895e5767720acfdb1021ba3db94e38fb84a407c9fd0c7cc848dca1f760", "sha256_in_prefix": "bfa1e4895e5767720acfdb1021ba3db94e38fb84a407c9fd0c7cc848dca1f760", "size_in_bytes": 30189}, {"_path": "include/thrift/server/TServer.h", "path_type": "hardlink", "sha256": "2309d320a7fed7bb433f84ee6fb1f1c45f0af967bdb560f2cf5bdc640c43e6ab", "sha256_in_prefix": "2309d320a7fed7bb433f84ee6fb1f1c45f0af967bdb560f2cf5bdc640c43e6ab", "size_in_bytes": 10832}, {"_path": "include/thrift/server/TServerFramework.h", "path_type": "hardlink", "sha256": "c1a85c3e0409dd243ce85073893361385c2464c9ddd20d86301513c1593094e4", "sha256_in_prefix": "c1a85c3e0409dd243ce85073893361385c2464c9ddd20d86301513c1593094e4", "size_in_bytes": 6934}, {"_path": "include/thrift/server/TSimpleServer.h", "path_type": "hardlink", "sha256": "5a80e7ca530615071945cb7dd0385883668bcec5c887b1255082c33539335e74", "sha256_in_prefix": "5a80e7ca530615071945cb7dd0385883668bcec5c887b1255082c33539335e74", "size_in_bytes": 3528}, {"_path": "include/thrift/server/TThreadPoolServer.h", "path_type": "hardlink", "sha256": "8125f707845895c095d28199e6c17e371f16ef403d6ff1c0b79d413e7e170b04", "sha256_in_prefix": "8125f707845895c095d28199e6c17e371f16ef403d6ff1c0b79d413e7e170b04", "size_in_bytes": 4592}, {"_path": "include/thrift/server/TThreadedServer.h", "path_type": "hardlink", "sha256": "b666a18e88dd63a1f35f4bf6c8f25f5caaedc09ec137fd1748f11ac4d1dade3e", "sha256_in_prefix": "b666a18e88dd63a1f35f4bf6c8f25f5caaedc09ec137fd1748f11ac4d1dade3e", "size_in_bytes": 6134}, {"_path": "include/thrift/thrift-config.h", "path_type": "hardlink", "sha256": "6838f773a3c82e9855f502df0b9098e24e34129124ad31acbf4bed24e48cc724", "sha256_in_prefix": "6838f773a3c82e9855f502df0b9098e24e34129124ad31acbf4bed24e48cc724", "size_in_bytes": 888}, {"_path": "include/thrift/thrift_export.h", "path_type": "hardlink", "sha256": "49b0641ef3099d882116be7f671467bf138666d6aedf9716e584a68eaa9aa23c", "sha256_in_prefix": "49b0641ef3099d882116be7f671467bf138666d6aedf9716e584a68eaa9aa23c", "size_in_bytes": 460}, {"_path": "include/thrift/transport/PlatformSocket.h", "path_type": "hardlink", "sha256": "d62deed7c1a2fda664e916ef80481c585905244b9d3c2008accd4f24c101bbef", "sha256_in_prefix": "d62deed7c1a2fda664e916ef80481c585905244b9d3c2008accd4f24c101bbef", "size_in_bytes": 4422}, {"_path": "include/thrift/transport/SocketCommon.h", "path_type": "hardlink", "sha256": "f726f43a50b6cedfd9acff98ce099638e3651b045503f67c485ac98935564bcf", "sha256_in_prefix": "f726f43a50b6cedfd9acff98ce099638e3651b045503f67c485ac98935564bcf", "size_in_bytes": 1385}, {"_path": "include/thrift/transport/TBufferTransports.h", "path_type": "hardlink", "sha256": "24541925cbda2aa52a61ce139148be903939d77592996bc63ab52198fa29ab6f", "sha256_in_prefix": "24541925cbda2aa52a61ce139148be903939d77592996bc63ab52198fa29ab6f", "size_in_bytes": 23381}, {"_path": "include/thrift/transport/TFDTransport.h", "path_type": "hardlink", "sha256": "ed7fbde1f4dc70a4311512653395db9764f2a951bc6a55cfe3fa85bd76e176f5", "sha256_in_prefix": "ed7fbde1f4dc70a4311512653395db9764f2a951bc6a55cfe3fa85bd76e176f5", "size_in_bytes": 2185}, {"_path": "include/thrift/transport/TFileTransport.h", "path_type": "hardlink", "sha256": "e738316750cc8caad17e70bb77df2b1401a9e56ce4aa557280b7fa2c1c780d44", "sha256_in_prefix": "e738316750cc8caad17e70bb77df2b1401a9e56ce4aa557280b7fa2c1c780d44", "size_in_bytes": 12909}, {"_path": "include/thrift/transport/THeaderTransport.h", "path_type": "hardlink", "sha256": "7a854d06e9515ce88f4fe755d0b3dd8e104ee0b532518e9c01fb68cfe1ac2dbc", "sha256_in_prefix": "7a854d06e9515ce88f4fe755d0b3dd8e104ee0b532518e9c01fb68cfe1ac2dbc", "size_in_bytes": 8241}, {"_path": "include/thrift/transport/THttpClient.h", "path_type": "hardlink", "sha256": "37dfffb87d47535bad9922a2dc24fd546146ec8034ea4823300f333fc64e6a51", "sha256_in_prefix": "37dfffb87d47535bad9922a2dc24fd546146ec8034ea4823300f333fc64e6a51", "size_in_bytes": 2270}, {"_path": "include/thrift/transport/THttpServer.h", "path_type": "hardlink", "sha256": "507db683cad280c1b74eed6ce2fb8753ff6ed917043fc11ef6c60b7bafb851ad", "sha256_in_prefix": "507db683cad280c1b74eed6ce2fb8753ff6ed917043fc11ef6c60b7bafb851ad", "size_in_bytes": 1936}, {"_path": "include/thrift/transport/THttpTransport.h", "path_type": "hardlink", "sha256": "d0205f8873b379bbca1f718578739637f96669398ae5e5b51b0bd9b8e6cb8ccb", "sha256_in_prefix": "d0205f8873b379bbca1f718578739637f96669398ae5e5b51b0bd9b8e6cb8ccb", "size_in_bytes": 2934}, {"_path": "include/thrift/transport/TNonblockingSSLServerSocket.h", "path_type": "hardlink", "sha256": "7d6489e3683d3303b09525e40af3bf5115521e9923b8dbe0f19cca695dc22237", "sha256_in_prefix": "7d6489e3683d3303b09525e40af3bf5115521e9923b8dbe0f19cca695dc22237", "size_in_bytes": 2367}, {"_path": "include/thrift/transport/TNonblockingServerSocket.h", "path_type": "hardlink", "sha256": "82e29235331cf36f5776d62e2c4b11b9726aa735763c4c7b3ee7adda184d3dad", "sha256_in_prefix": "82e29235331cf36f5776d62e2c4b11b9726aa735763c4c7b3ee7adda184d3dad", "size_in_bytes": 4191}, {"_path": "include/thrift/transport/TNonblockingServerTransport.h", "path_type": "hardlink", "sha256": "7f51ca7158e638c9c16a8f75f06b83b15ef8fb4f9eafd30da57db8b87721cb88", "sha256_in_prefix": "7f51ca7158e638c9c16a8f75f06b83b15ef8fb4f9eafd30da57db8b87721cb88", "size_in_bytes": 3074}, {"_path": "include/thrift/transport/TPipe.h", "path_type": "hardlink", "sha256": "7d3542ca668a3751ab94abf001da27b6cd8c0fa58a1b1ede5a10ca0f08bff739", "sha256_in_prefix": "7d3542ca668a3751ab94abf001da27b6cd8c0fa58a1b1ede5a10ca0f08bff739", "size_in_bytes": 3599}, {"_path": "include/thrift/transport/TPipeServer.h", "path_type": "hardlink", "sha256": "eb8bbcd2a7c659bab696f2806d0580c3b359a6484bc751ab80c8ce057ef1cb84", "sha256_in_prefix": "eb8bbcd2a7c659bab696f2806d0580c3b359a6484bc751ab80c8ce057ef1cb84", "size_in_bytes": 3448}, {"_path": "include/thrift/transport/TSSLServerSocket.h", "path_type": "hardlink", "sha256": "9d35067928d4c069451f33480021a11840659eaa8e5988ef32c9db5582568d1a", "sha256_in_prefix": "9d35067928d4c069451f33480021a11840659eaa8e5988ef32c9db5582568d1a", "size_in_bytes": 2267}, {"_path": "include/thrift/transport/TSSLSocket.h", "path_type": "hardlink", "sha256": "787fc61feb120ffeea179172ec523f2e272cf3d6b0e05edce2097f706a74dd4f", "sha256_in_prefix": "787fc61feb120ffeea179172ec523f2e272cf3d6b0e05edce2097f706a74dd4f", "size_in_bytes": 14473}, {"_path": "include/thrift/transport/TServerSocket.h", "path_type": "hardlink", "sha256": "c89bd46e5e1e2ea297f901ef524290c13e11642f21a82ae023fadd3a75980ee8", "sha256_in_prefix": "c89bd46e5e1e2ea297f901ef524290c13e11642f21a82ae023fadd3a75980ee8", "size_in_bytes": 5635}, {"_path": "include/thrift/transport/TServerTransport.h", "path_type": "hardlink", "sha256": "e18d43c787c9ad5c948abeedbaf9c1dd4205f09d0c8715e47cfe1259a111380f", "sha256_in_prefix": "e18d43c787c9ad5c948abeedbaf9c1dd4205f09d0c8715e47cfe1259a111380f", "size_in_bytes": 3734}, {"_path": "include/thrift/transport/TShortReadTransport.h", "path_type": "hardlink", "sha256": "51f509bd662a5853614a30aea9ef417a927e1ec78c4da03f78fc356337d2bdbd", "sha256_in_prefix": "51f509bd662a5853614a30aea9ef417a927e1ec78c4da03f78fc356337d2bdbd", "size_in_bytes": 2740}, {"_path": "include/thrift/transport/TSimpleFileTransport.h", "path_type": "hardlink", "sha256": "ad72dafe0ce5cc29fe1d061c10928ae8c61356b8b07670cf2346e1245ccc40fb", "sha256_in_prefix": "ad72dafe0ce5cc29fe1d061c10928ae8c61356b8b07670cf2346e1245ccc40fb", "size_in_bytes": 1443}, {"_path": "include/thrift/transport/TSocket.h", "path_type": "hardlink", "sha256": "6ba57b76434debb46dbf2da11141e866fa8df2438381a3d5c5292b9b3c5cbf82", "sha256_in_prefix": "6ba57b76434debb46dbf2da11141e866fa8df2438381a3d5c5292b9b3c5cbf82", "size_in_bytes": 9232}, {"_path": "include/thrift/transport/TSocketPool.h", "path_type": "hardlink", "sha256": "19aedd26bc9979c8c35f1093909a7b4a93fcd5d50555f98d02693b9a1f39b5f7", "sha256_in_prefix": "19aedd26bc9979c8c35f1093909a7b4a93fcd5d50555f98d02693b9a1f39b5f7", "size_in_bytes": 4582}, {"_path": "include/thrift/transport/TSocketUtils.h", "path_type": "hardlink", "sha256": "ddfdeae227472e5fe840e02772b314cf282ec41502231fe40daf46ac5cc9df95", "sha256_in_prefix": "ddfdeae227472e5fe840e02772b314cf282ec41502231fe40daf46ac5cc9df95", "size_in_bytes": 4828}, {"_path": "include/thrift/transport/TTransport.h", "path_type": "hardlink", "sha256": "a888a325abd104b63cbef8aaa3e7c18171d32e17911724b73b892582ba5feb76", "sha256_in_prefix": "a888a325abd104b63cbef8aaa3e7c18171d32e17911724b73b892582ba5feb76", "size_in_bytes": 11825}, {"_path": "include/thrift/transport/TTransportException.h", "path_type": "hardlink", "sha256": "403ce488148935624eb8ed36ddc1157c4b41f78680a388dfb53e18e8eace6ec4", "sha256_in_prefix": "403ce488148935624eb8ed36ddc1157c4b41f78680a388dfb53e18e8eace6ec4", "size_in_bytes": 3321}, {"_path": "include/thrift/transport/TTransportUtils.h", "path_type": "hardlink", "sha256": "b58910fb641e36341ecfc5b5d88804435894418018f42f687c8e2fdd8efb90d4", "sha256_in_prefix": "b58910fb641e36341ecfc5b5d88804435894418018f42f687c8e2fdd8efb90d4", "size_in_bytes": 9563}, {"_path": "include/thrift/transport/TVirtualTransport.h", "path_type": "hardlink", "sha256": "6ee51734e053b7a3aea1fd0f23a85735efce3bae2feff04a181c21f2d85a9666", "sha256_in_prefix": "6ee51734e053b7a3aea1fd0f23a85735efce3bae2feff04a181c21f2d85a9666", "size_in_bytes": 5216}, {"_path": "include/thrift/transport/TWebSocketServer.h", "path_type": "hardlink", "sha256": "7e422a5fe5e09bb201c27c358c856275de28d78ad2ae80c9d598594d10f4ea16", "sha256_in_prefix": "7e422a5fe5e09bb201c27c358c856275de28d78ad2ae80c9d598594d10f4ea16", "size_in_bytes": 12512}, {"_path": "include/thrift/transport/TZlibTransport.h", "path_type": "hardlink", "sha256": "1b1cf60153f097ac67924428a2e5fb5704642db5eb6fee07ed7ccd7e013844d7", "sha256_in_prefix": "1b1cf60153f097ac67924428a2e5fb5704642db5eb6fee07ed7ccd7e013844d7", "size_in_bytes": 7374}, {"_path": "include/thrift/windows/GetTimeOfDay.h", "path_type": "hardlink", "sha256": "3bf5a5b3f93680fa3f34cd103b0a7e4f13bbafa1e68f67fed42f2e991a16b3a1", "sha256_in_prefix": "3bf5a5b3f93680fa3f34cd103b0a7e4f13bbafa1e68f67fed42f2e991a16b3a1", "size_in_bytes": 1391}, {"_path": "include/thrift/windows/Operators.h", "path_type": "hardlink", "sha256": "f9d9a87234913f07e2dfccd1e20146092cd969534795fbe523248bdd32ef12a7", "sha256_in_prefix": "f9d9a87234913f07e2dfccd1e20146092cd969534795fbe523248bdd32ef12a7", "size_in_bytes": 1296}, {"_path": "include/thrift/windows/OverlappedSubmissionThread.h", "path_type": "hardlink", "sha256": "46387042a707e32e1abe53247278ea3076f32bfc4cda6c4bd71d735d2bb010c2", "sha256_in_prefix": "46387042a707e32e1abe53247278ea3076f32bfc4cda6c4bd71d735d2bb010c2", "size_in_bytes": 4747}, {"_path": "include/thrift/windows/SocketPair.h", "path_type": "hardlink", "sha256": "689561642a08c43075c555b40a571e443dd0d86901bcb0720b460c3e1202f34a", "sha256_in_prefix": "689561642a08c43075c555b40a571e443dd0d86901bcb0720b460c3e1202f34a", "size_in_bytes": 1200}, {"_path": "include/thrift/windows/Sync.h", "path_type": "hardlink", "sha256": "c06b2033e68d3e9c0c05e89a8caa7fc6df6ba4938ea3af940e0c9f5e0d02d02d", "sha256_in_prefix": "c06b2033e68d3e9c0c05e89a8caa7fc6df6ba4938ea3af940e0c9f5e0d02d02d", "size_in_bytes": 3514}, {"_path": "include/thrift/windows/TWinsockSingleton.h", "path_type": "hardlink", "sha256": "e26fdbf356ab178a15b02f09d69d5fe898973bdb103bc4e51f159778ebe09ac2", "sha256_in_prefix": "e26fdbf356ab178a15b02f09d69d5fe898973bdb103bc4e51f159778ebe09ac2", "size_in_bytes": 1840}, {"_path": "include/thrift/windows/WinFcntl.h", "path_type": "hardlink", "sha256": "66fef73c0f3065e677a3a41d0748aac60c9c7b15fab7e350ca3174e95955a02c", "sha256_in_prefix": "66fef73c0f3065e677a3a41d0748aac60c9c7b15fab7e350ca3174e95955a02c", "size_in_bytes": 1308}, {"_path": "include/thrift/windows/config.h", "path_type": "hardlink", "sha256": "5fbd227d91c8b35d46a50a3b9e1dcf06a86b26b28cb97dc0be0139e8643c8688", "sha256_in_prefix": "5fbd227d91c8b35d46a50a3b9e1dcf06a86b26b28cb97dc0be0139e8643c8688", "size_in_bytes": 2614}, {"_path": "lib/cmake/thrift/FindLibevent.cmake", "path_type": "hardlink", "sha256": "f6df8488f300236f7ca5c4add80201d8d5892c0bbeeec216caf0ad1431b11c38", "sha256_in_prefix": "f6df8488f300236f7ca5c4add80201d8d5892c0bbeeec216caf0ad1431b11c38", "size_in_bytes": 1517}, {"_path": "lib/cmake/thrift/ThriftConfig.cmake", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/thrift-split_1727205665368/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "8442137d0b15e26995568f1f57a9c91e04c05a7c2704dbe709d8351deea50b9d", "sha256_in_prefix": "676ef958e6365d0bbb8367dfe904118b4f0c0708c656ce1a7c918b088ec7d1ea", "size_in_bytes": 4040}, {"_path": "lib/cmake/thrift/ThriftConfigVersion.cmake", "path_type": "hardlink", "sha256": "0c319d40a24676db9ea79a5bb6d4cf2eaa83b2530398712a3ba6bdd64e76c567", "sha256_in_prefix": "0c319d40a24676db9ea79a5bb6d4cf2eaa83b2530398712a3ba6bdd64e76c567", "size_in_bytes": 2765}, {"_path": "lib/cmake/thrift/thriftTargets-release.cmake", "path_type": "hardlink", "sha256": "d0b7512969a30fd3f0f1c16594406c43adb57a6ad12d7918718caf1e3d896ee3", "sha256_in_prefix": "d0b7512969a30fd3f0f1c16594406c43adb57a6ad12d7918718caf1e3d896ee3", "size_in_bytes": 873}, {"_path": "lib/cmake/thrift/thriftTargets.cmake", "path_type": "hardlink", "sha256": "52527fe76965d3511022c69be92ecd8e73e93528ed62631ad827f2b50cc4e5fd", "sha256_in_prefix": "52527fe76965d3511022c69be92ecd8e73e93528ed62631ad827f2b50cc4e5fd", "size_in_bytes": 4185}, {"_path": "lib/cmake/thrift/thriftnbTargets-release.cmake", "path_type": "hardlink", "sha256": "8d07073b1cb042a82568017a2121d664ebecd68cf468e4088af80ca5fb3251fe", "sha256_in_prefix": "8d07073b1cb042a82568017a2121d664ebecd68cf468e4088af80ca5fb3251fe", "size_in_bytes": 899}, {"_path": "lib/cmake/thrift/thriftnbTargets.cmake", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/thrift-split_1727205665368/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "35172698d369987f37e38ca9b578a3f3f986aa3c0601c6015b5e25a478ca976f", "sha256_in_prefix": "1485564df23d6372bfe31ea1668556ffe9bf20a611fe22690a859d039ef69dda", "size_in_bytes": 5242}, {"_path": "lib/cmake/thrift/thriftzTargets-release.cmake", "path_type": "hardlink", "sha256": "d063efa66cac0a0999cdd8fe95dd800e257133663dbf0be4646717ca87f5cb4d", "sha256_in_prefix": "d063efa66cac0a0999cdd8fe95dd800e257133663dbf0be4646717ca87f5cb4d", "size_in_bytes": 886}, {"_path": "lib/cmake/thrift/thriftzTargets.cmake", "path_type": "hardlink", "sha256": "220018102c3f2357a18dd5744afa7c82951012be2889dcb7621c5635002280df", "sha256_in_prefix": "220018102c3f2357a18dd5744afa7c82951012be2889dcb7621c5635002280df", "size_in_bytes": 4969}, {"_path": "lib/libthrift.0.21.0.dylib", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/thrift-split_1727205665368/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "df47634bf431cb313aa182d1addf44b8b44ed186ad84ffeb94f6c1b23ef76ba3", "sha256_in_prefix": "88de57b03c68983c1c6109fc60a1078145ccc7fa68e0e5641246ac47717d575c", "size_in_bytes": 648320}, {"_path": "lib/libthrift.dylib", "path_type": "softlink", "sha256": "df47634bf431cb313aa182d1addf44b8b44ed186ad84ffeb94f6c1b23ef76ba3", "size_in_bytes": 648320}, {"_path": "lib/libthriftnb.0.21.0.dylib", "path_type": "hardlink", "sha256": "529f9a2a5e7b765a35762b55e19bdf0463823571be91273b8d3bd03020e4b86d", "sha256_in_prefix": "529f9a2a5e7b765a35762b55e19bdf0463823571be91273b8d3bd03020e4b86d", "size_in_bytes": 175296}, {"_path": "lib/libthriftnb.dylib", "path_type": "softlink", "sha256": "529f9a2a5e7b765a35762b55e19bdf0463823571be91273b8d3bd03020e4b86d", "size_in_bytes": 175296}, {"_path": "lib/libthriftz.0.21.0.dylib", "path_type": "hardlink", "sha256": "9572856efddfa261d959bebe864145f2216990c742d45fbae136059f7563a8c8", "sha256_in_prefix": "9572856efddfa261d959bebe864145f2216990c742d45fbae136059f7563a8c8", "size_in_bytes": 159504}, {"_path": "lib/libthriftz.dylib", "path_type": "softlink", "sha256": "9572856efddfa261d959bebe864145f2216990c742d45fbae136059f7563a8c8", "size_in_bytes": 159504}, {"_path": "lib/pkgconfig/thrift-nb.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/thrift-split_1727205665368/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "0d1093d0217b9968230fd97438cfe416d579e468227993ff04470acd70337cec", "sha256_in_prefix": "2e8d6e04a929974a3feb03c7effb010d381f77fda3d93863493a8c52ccc268b0", "size_in_bytes": 2008}, {"_path": "lib/pkgconfig/thrift-z.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/thrift-split_1727205665368/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "cb567e8cb3a53720a2edbb4f278e379a7a114b5da3b7df82952f708052bf0882", "sha256_in_prefix": "be0b5f07f7452118229d5e8404292b67b43e431ec88eabd440e201b5c0e667d5", "size_in_bytes": 2000}, {"_path": "lib/pkgconfig/thrift.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/thrift-split_1727205665368/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "0f90fa980336165c5fe0d33ef17ed0ea9c8e85373702c7870640af197bb3aea9", "sha256_in_prefix": "b694a60b7892be77c84b26f35f3ee636c60dcdb549f086428ffa670cc67388ba", "size_in_bytes": 1972}], "paths_version": 1}, "requested_spec": "None", "sha256": "3f82eddd6de435a408538ac81a7a2c0c155877534761ec9cd7a2906c005cece2", "size": 332651, "subdir": "osx-64", "timestamp": 1727206546000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libthrift-0.21.0-h75589b3_0.conda", "version": "0.21.0"}