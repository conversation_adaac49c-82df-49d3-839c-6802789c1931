{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["filelock", "fsspec >=2023.5.0", "hf-xet >=1.1.3,<2.0.0", "packaging >=20.9", "python >=3.10", "pyyaml >=5.1", "requests", "tqdm >=4.42.1", "typing-extensions >=3.7.4.3", "typing_extensions >=3.7.4.3"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/huggingface_hub-0.35.0-pyhd8ed1ab_0", "files": ["lib/python3.11/site-packages/huggingface_hub-0.35.0.dist-info/INSTALLER", "lib/python3.11/site-packages/huggingface_hub-0.35.0.dist-info/METADATA", "lib/python3.11/site-packages/huggingface_hub-0.35.0.dist-info/RECORD", "lib/python3.11/site-packages/huggingface_hub-0.35.0.dist-info/REQUESTED", "lib/python3.11/site-packages/huggingface_hub-0.35.0.dist-info/WHEEL", "lib/python3.11/site-packages/huggingface_hub-0.35.0.dist-info/direct_url.json", "lib/python3.11/site-packages/huggingface_hub-0.35.0.dist-info/entry_points.txt", "lib/python3.11/site-packages/huggingface_hub-0.35.0.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/huggingface_hub-0.35.0.dist-info/top_level.txt", "lib/python3.11/site-packages/huggingface_hub/__init__.py", "lib/python3.11/site-packages/huggingface_hub/_commit_api.py", "lib/python3.11/site-packages/huggingface_hub/_commit_scheduler.py", "lib/python3.11/site-packages/huggingface_hub/_inference_endpoints.py", "lib/python3.11/site-packages/huggingface_hub/_jobs_api.py", "lib/python3.11/site-packages/huggingface_hub/_local_folder.py", "lib/python3.11/site-packages/huggingface_hub/_login.py", "lib/python3.11/site-packages/huggingface_hub/_oauth.py", "lib/python3.11/site-packages/huggingface_hub/_snapshot_download.py", "lib/python3.11/site-packages/huggingface_hub/_space_api.py", "lib/python3.11/site-packages/huggingface_hub/_tensorboard_logger.py", "lib/python3.11/site-packages/huggingface_hub/_upload_large_folder.py", "lib/python3.11/site-packages/huggingface_hub/_webhooks_payload.py", "lib/python3.11/site-packages/huggingface_hub/_webhooks_server.py", "lib/python3.11/site-packages/huggingface_hub/cli/__init__.py", "lib/python3.11/site-packages/huggingface_hub/cli/_cli_utils.py", "lib/python3.11/site-packages/huggingface_hub/cli/auth.py", "lib/python3.11/site-packages/huggingface_hub/cli/cache.py", "lib/python3.11/site-packages/huggingface_hub/cli/download.py", "lib/python3.11/site-packages/huggingface_hub/cli/hf.py", "lib/python3.11/site-packages/huggingface_hub/cli/jobs.py", "lib/python3.11/site-packages/huggingface_hub/cli/lfs.py", "lib/python3.11/site-packages/huggingface_hub/cli/repo.py", "lib/python3.11/site-packages/huggingface_hub/cli/repo_files.py", "lib/python3.11/site-packages/huggingface_hub/cli/system.py", "lib/python3.11/site-packages/huggingface_hub/cli/upload.py", "lib/python3.11/site-packages/huggingface_hub/cli/upload_large_folder.py", "lib/python3.11/site-packages/huggingface_hub/commands/__init__.py", "lib/python3.11/site-packages/huggingface_hub/commands/_cli_utils.py", "lib/python3.11/site-packages/huggingface_hub/commands/delete_cache.py", "lib/python3.11/site-packages/huggingface_hub/commands/download.py", "lib/python3.11/site-packages/huggingface_hub/commands/env.py", "lib/python3.11/site-packages/huggingface_hub/commands/huggingface_cli.py", "lib/python3.11/site-packages/huggingface_hub/commands/lfs.py", "lib/python3.11/site-packages/huggingface_hub/commands/repo.py", "lib/python3.11/site-packages/huggingface_hub/commands/repo_files.py", "lib/python3.11/site-packages/huggingface_hub/commands/scan_cache.py", "lib/python3.11/site-packages/huggingface_hub/commands/tag.py", "lib/python3.11/site-packages/huggingface_hub/commands/upload.py", "lib/python3.11/site-packages/huggingface_hub/commands/upload_large_folder.py", "lib/python3.11/site-packages/huggingface_hub/commands/user.py", "lib/python3.11/site-packages/huggingface_hub/commands/version.py", "lib/python3.11/site-packages/huggingface_hub/community.py", "lib/python3.11/site-packages/huggingface_hub/constants.py", "lib/python3.11/site-packages/huggingface_hub/dataclasses.py", "lib/python3.11/site-packages/huggingface_hub/errors.py", "lib/python3.11/site-packages/huggingface_hub/fastai_utils.py", "lib/python3.11/site-packages/huggingface_hub/file_download.py", "lib/python3.11/site-packages/huggingface_hub/hf_api.py", "lib/python3.11/site-packages/huggingface_hub/hf_file_system.py", "lib/python3.11/site-packages/huggingface_hub/hub_mixin.py", "lib/python3.11/site-packages/huggingface_hub/inference/__init__.py", "lib/python3.11/site-packages/huggingface_hub/inference/_client.py", "lib/python3.11/site-packages/huggingface_hub/inference/_common.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/__init__.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/_async_client.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__init__.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/audio_classification.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/audio_to_audio.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/automatic_speech_recognition.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/base.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/chat_completion.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/depth_estimation.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/document_question_answering.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/feature_extraction.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/fill_mask.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/image_classification.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/image_segmentation.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/image_to_image.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/image_to_text.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/image_to_video.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/object_detection.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/question_answering.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/sentence_similarity.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/summarization.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/table_question_answering.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/text2text_generation.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/text_classification.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/text_generation.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/text_to_audio.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/text_to_image.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/text_to_speech.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/text_to_video.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/token_classification.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/translation.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/video_classification.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/visual_question_answering.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/zero_shot_classification.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/zero_shot_image_classification.py", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/zero_shot_object_detection.py", "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/__init__.py", "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/_cli_hacks.py", "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/agent.py", "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/cli.py", "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/constants.py", "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/mcp_client.py", "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/types.py", "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/utils.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__init__.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/_common.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/black_forest_labs.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/cerebras.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/cohere.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/fal_ai.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/featherless_ai.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/fireworks_ai.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/groq.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/hf_inference.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/hyperbolic.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/nebius.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/novita.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/nscale.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/openai.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/publicai.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/replicate.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/sambanova.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/scaleway.py", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/together.py", "lib/python3.11/site-packages/huggingface_hub/inference_api.py", "lib/python3.11/site-packages/huggingface_hub/keras_mixin.py", "lib/python3.11/site-packages/huggingface_hub/lfs.py", "lib/python3.11/site-packages/huggingface_hub/py.typed", "lib/python3.11/site-packages/huggingface_hub/repocard.py", "lib/python3.11/site-packages/huggingface_hub/repocard_data.py", "lib/python3.11/site-packages/huggingface_hub/repository.py", "lib/python3.11/site-packages/huggingface_hub/serialization/__init__.py", "lib/python3.11/site-packages/huggingface_hub/serialization/_base.py", "lib/python3.11/site-packages/huggingface_hub/serialization/_dduf.py", "lib/python3.11/site-packages/huggingface_hub/serialization/_tensorflow.py", "lib/python3.11/site-packages/huggingface_hub/serialization/_torch.py", "lib/python3.11/site-packages/huggingface_hub/templates/datasetcard_template.md", "lib/python3.11/site-packages/huggingface_hub/templates/modelcard_template.md", "lib/python3.11/site-packages/huggingface_hub/utils/__init__.py", "lib/python3.11/site-packages/huggingface_hub/utils/_auth.py", "lib/python3.11/site-packages/huggingface_hub/utils/_cache_assets.py", "lib/python3.11/site-packages/huggingface_hub/utils/_cache_manager.py", "lib/python3.11/site-packages/huggingface_hub/utils/_chunk_utils.py", "lib/python3.11/site-packages/huggingface_hub/utils/_datetime.py", "lib/python3.11/site-packages/huggingface_hub/utils/_deprecation.py", "lib/python3.11/site-packages/huggingface_hub/utils/_dotenv.py", "lib/python3.11/site-packages/huggingface_hub/utils/_experimental.py", "lib/python3.11/site-packages/huggingface_hub/utils/_fixes.py", "lib/python3.11/site-packages/huggingface_hub/utils/_git_credential.py", "lib/python3.11/site-packages/huggingface_hub/utils/_headers.py", "lib/python3.11/site-packages/huggingface_hub/utils/_hf_folder.py", "lib/python3.11/site-packages/huggingface_hub/utils/_http.py", "lib/python3.11/site-packages/huggingface_hub/utils/_lfs.py", "lib/python3.11/site-packages/huggingface_hub/utils/_pagination.py", "lib/python3.11/site-packages/huggingface_hub/utils/_paths.py", "lib/python3.11/site-packages/huggingface_hub/utils/_runtime.py", "lib/python3.11/site-packages/huggingface_hub/utils/_safetensors.py", "lib/python3.11/site-packages/huggingface_hub/utils/_subprocess.py", "lib/python3.11/site-packages/huggingface_hub/utils/_telemetry.py", "lib/python3.11/site-packages/huggingface_hub/utils/_typing.py", "lib/python3.11/site-packages/huggingface_hub/utils/_validators.py", "lib/python3.11/site-packages/huggingface_hub/utils/_xet.py", "lib/python3.11/site-packages/huggingface_hub/utils/_xet_progress_reporting.py", "lib/python3.11/site-packages/huggingface_hub/utils/endpoint_helpers.py", "lib/python3.11/site-packages/huggingface_hub/utils/insecure_hashlib.py", "lib/python3.11/site-packages/huggingface_hub/utils/logging.py", "lib/python3.11/site-packages/huggingface_hub/utils/sha.py", "lib/python3.11/site-packages/huggingface_hub/utils/tqdm.py", "lib/python3.11/site-packages/huggingface_hub/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/_commit_api.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/_commit_scheduler.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/_inference_endpoints.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/_jobs_api.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/_local_folder.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/_login.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/_oauth.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/_snapshot_download.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/_space_api.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/_tensorboard_logger.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/_upload_large_folder.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/_webhooks_payload.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/_webhooks_server.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/_cli_utils.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/auth.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/cache.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/download.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/hf.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/jobs.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/lfs.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/repo.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/repo_files.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/system.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/upload.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/upload_large_folder.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/_cli_utils.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/delete_cache.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/download.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/env.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/huggingface_cli.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/lfs.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/repo.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/repo_files.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/scan_cache.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/tag.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/upload.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/upload_large_folder.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/user.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/version.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/community.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/constants.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/dataclasses.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/errors.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/fastai_utils.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/file_download.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/hf_api.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/hf_file_system.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/hub_mixin.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/__pycache__/_client.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/__pycache__/_common.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/__pycache__/_async_client.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/audio_classification.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/audio_to_audio.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/automatic_speech_recognition.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/base.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/chat_completion.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/depth_estimation.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/document_question_answering.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/feature_extraction.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/fill_mask.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_classification.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_segmentation.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_to_image.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_to_text.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_to_video.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/object_detection.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/question_answering.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/sentence_similarity.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/summarization.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/table_question_answering.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text2text_generation.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_classification.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_generation.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_to_audio.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_to_image.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_to_speech.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_to_video.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/token_classification.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/translation.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/video_classification.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/visual_question_answering.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/zero_shot_classification.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/zero_shot_image_classification.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/zero_shot_object_detection.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/__pycache__/_cli_hacks.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/__pycache__/agent.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/__pycache__/cli.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/__pycache__/constants.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/__pycache__/mcp_client.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/__pycache__/types.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/_common.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/black_forest_labs.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/cerebras.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/cohere.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/fal_ai.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/featherless_ai.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/fireworks_ai.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/groq.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/hf_inference.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/hyperbolic.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/nebius.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/novita.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/nscale.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/openai.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/publicai.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/replicate.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/sambanova.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/scaleway.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/together.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/inference_api.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/keras_mixin.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/lfs.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/repocard.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/repocard_data.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/__pycache__/repository.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/serialization/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/serialization/__pycache__/_base.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/serialization/__pycache__/_dduf.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/serialization/__pycache__/_tensorflow.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/serialization/__pycache__/_torch.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_auth.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_cache_assets.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_cache_manager.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_chunk_utils.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_datetime.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_deprecation.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_dotenv.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_experimental.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_fixes.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_git_credential.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_headers.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_hf_folder.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_http.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_lfs.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_pagination.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_paths.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_runtime.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_safetensors.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_subprocess.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_telemetry.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_typing.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_validators.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_xet.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_xet_progress_reporting.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/endpoint_helpers.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/insecure_hashlib.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/logging.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/sha.cpython-311.pyc", "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/tqdm.cpython-311.pyc", "bin/huggingface-cli", "bin/hf", "bin/tiny-agents"], "fn": "huggingface_hub-0.35.0-pyhd8ed1ab_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/huggingface_hub-0.35.0-pyhd8ed1ab_0", "type": 1}, "md5": "85850428fc5aefaf0029ff30d25f1e62", "name": "huggingface_hub", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/huggingface_hub-0.35.0-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/huggingface_hub-0.35.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/huggingface_hub-0.35.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "03ab8b7403060ef22a801137b632dd14fabcb234967cb93514b145ec9545ca32", "sha256_in_prefix": "03ab8b7403060ef22a801137b632dd14fabcb234967cb93514b145ec9545ca32", "size_in_bytes": 15199}, {"_path": "site-packages/huggingface_hub-0.35.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "adb801a600cdf421ee1059a1197f28cbceafa94ea6b6e8e22e92cde50f844446", "sha256_in_prefix": "adb801a600cdf421ee1059a1197f28cbceafa94ea6b6e8e22e92cde50f844446", "size_in_bytes": 28683}, {"_path": "site-packages/huggingface_hub-0.35.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/huggingface_hub-0.35.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "ff309ddcdd65ebd02bc724dbf2bcc4a0ff53a5b61792a44548d383e4ebb19d3b", "sha256_in_prefix": "ff309ddcdd65ebd02bc724dbf2bcc4a0ff53a5b61792a44548d383e4ebb19d3b", "size_in_bytes": 91}, {"_path": "site-packages/huggingface_hub-0.35.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "891a9c002a024a8b1445d9894a70155ea9e3d5d8b3f0846fe3d0ac6cab78b32f", "sha256_in_prefix": "891a9c002a024a8b1445d9894a70155ea9e3d5d8b3f0846fe3d0ac6cab78b32f", "size_in_bytes": 111}, {"_path": "site-packages/huggingface_hub-0.35.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "14651dbeef33fb1ee5be8278b9dba685c83702dce280fada0a7fd96e312122da", "sha256_in_prefix": "14651dbeef33fb1ee5be8278b9dba685c83702dce280fada0a7fd96e312122da", "size_in_bytes": 218}, {"_path": "site-packages/huggingface_hub-0.35.0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "sha256_in_prefix": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "size_in_bytes": 11357}, {"_path": "site-packages/huggingface_hub-0.35.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "f0ace5409018e26894be302cb0e009a1da8a3b0de16ab373ba2c0643da8b4929", "sha256_in_prefix": "f0ace5409018e26894be302cb0e009a1da8a3b0de16ab373ba2c0643da8b4929", "size_in_bytes": 16}, {"_path": "site-packages/huggingface_hub/__init__.py", "path_type": "hardlink", "sha256": "4cb35267fd19ff652283b139f9fd99aa50acbc5cc8c1a11ad66420bd550eea85", "sha256_in_prefix": "4cb35267fd19ff652283b139f9fd99aa50acbc5cc8c1a11ad66420bd550eea85", "size_in_bytes": 52476}, {"_path": "site-packages/huggingface_hub/_commit_api.py", "path_type": "hardlink", "sha256": "ebc1f1167244dacf909866511d06afe6438c4ec79857f6508b4e000da4266549", "sha256_in_prefix": "ebc1f1167244dacf909866511d06afe6438c4ec79857f6508b4e000da4266549", "size_in_bytes": 38979}, {"_path": "site-packages/huggingface_hub/_commit_scheduler.py", "path_type": "hardlink", "sha256": "b5f2283b5c561e34c9eaacba552e87228ca60f27053e0d1dea9052669aeb5365", "sha256_in_prefix": "b5f2283b5c561e34c9eaacba552e87228ca60f27053e0d1dea9052669aeb5365", "size_in_bytes": 14679}, {"_path": "site-packages/huggingface_hub/_inference_endpoints.py", "path_type": "hardlink", "sha256": "6a199b3dc117b09fc970c6fd4c381d903f19db3f6ecad9051b7ff5a3a7539bc8", "sha256_in_prefix": "6a199b3dc117b09fc970c6fd4c381d903f19db3f6ecad9051b7ff5a3a7539bc8", "size_in_bytes": 17598}, {"_path": "site-packages/huggingface_hub/_jobs_api.py", "path_type": "hardlink", "sha256": "903e38ad13297e9004326072da73fc62e0544220b78033c59509ac8d1ac552ad", "sha256_in_prefix": "903e38ad13297e9004326072da73fc62e0544220b78033c59509ac8d1ac552ad", "size_in_bytes": 10803}, {"_path": "site-packages/huggingface_hub/_local_folder.py", "path_type": "hardlink", "sha256": "da21d7360213dd4752b763ef0a8bddd0dce05714d1ca929bfabbc014b2be8195", "sha256_in_prefix": "da21d7360213dd4752b763ef0a8bddd0dce05714d1ca929bfabbc014b2be8195", "size_in_bytes": 17305}, {"_path": "site-packages/huggingface_hub/_login.py", "path_type": "hardlink", "sha256": "adcc31f4465d154077beea300b9401892612e089949c1ce8d388a047567c978d", "sha256_in_prefix": "adcc31f4465d154077beea300b9401892612e089949c1ce8d388a047567c978d", "size_in_bytes": 20256}, {"_path": "site-packages/huggingface_hub/_oauth.py", "path_type": "hardlink", "sha256": "ef9c9af6da07c42d1644ab0b380236d760abb2c4634d2c6cd7a98759634c6f7c", "sha256_in_prefix": "ef9c9af6da07c42d1644ab0b380236d760abb2c4634d2c6cd7a98759634c6f7c", "size_in_bytes": 18714}, {"_path": "site-packages/huggingface_hub/_snapshot_download.py", "path_type": "hardlink", "sha256": "6fe37361072f92db008ab21f1902a0cd0c2ef30d12ea58414ef9c9e52eac6b0f", "sha256_in_prefix": "6fe37361072f92db008ab21f1902a0cd0c2ef30d12ea58414ef9c9e52eac6b0f", "size_in_bytes": 16166}, {"_path": "site-packages/huggingface_hub/_space_api.py", "path_type": "hardlink", "sha256": "8dbeab17ca8bb6368d535d83fbcca000f336eb10e21c2bbc0875c7a308464e68", "sha256_in_prefix": "8dbeab17ca8bb6368d535d83fbcca000f336eb10e21c2bbc0875c7a308464e68", "size_in_bytes": 5470}, {"_path": "site-packages/huggingface_hub/_tensorboard_logger.py", "path_type": "hardlink", "sha256": "afb23d52cf40d1d071381168b3db7c8c9dedf7927173a438fd7f30b7b0e88d61", "sha256_in_prefix": "afb23d52cf40d1d071381168b3db7c8c9dedf7927173a438fd7f30b7b0e88d61", "size_in_bytes": 8439}, {"_path": "site-packages/huggingface_hub/_upload_large_folder.py", "path_type": "hardlink", "sha256": "9766162d9b6d3b0ebd106762853df2fcd86be66c1e2c6a28646f4bf2c98da076", "sha256_in_prefix": "9766162d9b6d3b0ebd106762853df2fcd86be66c1e2c6a28646f4bf2c98da076", "size_in_bytes": 30066}, {"_path": "site-packages/huggingface_hub/_webhooks_payload.py", "path_type": "hardlink", "sha256": "5e6dca68aeed08e18195792e66f6f29bace31d7ad3d570ab6d4156b9788198d6", "sha256_in_prefix": "5e6dca68aeed08e18195792e66f6f29bace31d7ad3d570ab6d4156b9788198d6", "size_in_bytes": 3617}, {"_path": "site-packages/huggingface_hub/_webhooks_server.py", "path_type": "hardlink", "sha256": "e49eb7c24f4c50628134956c383f62eb4989f95329d1862695ff3bbd0b25f8bf", "sha256_in_prefix": "e49eb7c24f4c50628134956c383f62eb4989f95329d1862695ff3bbd0b25f8bf", "size_in_bytes": 15767}, {"_path": "site-packages/huggingface_hub/cli/__init__.py", "path_type": "hardlink", "sha256": "c735f5aa002faed017e203f9f56acf96f39914bbb3b9381c8ef6a742f7298077", "sha256_in_prefix": "c735f5aa002faed017e203f9f56acf96f39914bbb3b9381c8ef6a742f7298077", "size_in_bytes": 928}, {"_path": "site-packages/huggingface_hub/cli/_cli_utils.py", "path_type": "hardlink", "sha256": "36de828db918a90411ba1ef46d45d503aad9a5b66dfd26b55aa054c6340bbba8", "sha256_in_prefix": "36de828db918a90411ba1ef46d45d503aad9a5b66dfd26b55aa054c6340bbba8", "size_in_bytes": 2095}, {"_path": "site-packages/huggingface_hub/cli/auth.py", "path_type": "hardlink", "sha256": "5d2b1b53bfbf4d2e485dd0129205027507a85d51bcd95532198bce4b8a0b2d1b", "sha256_in_prefix": "5d2b1b53bfbf4d2e485dd0129205027507a85d51bcd95532198bce4b8a0b2d1b", "size_in_bytes": 7317}, {"_path": "site-packages/huggingface_hub/cli/cache.py", "path_type": "hardlink", "sha256": "7d08d87db4546a9787b0ad7463ac3f231bbd24acae67233ba49cc4c4919dff67", "sha256_in_prefix": "7d08d87db4546a9787b0ad7463ac3f231bbd24acae67233ba49cc4c4919dff67", "size_in_bytes": 15855}, {"_path": "site-packages/huggingface_hub/cli/download.py", "path_type": "hardlink", "sha256": "3d4a56fa76eee9900fe8ff43a558658802924a5c6f5d6915874536299a2e9214", "sha256_in_prefix": "3d4a56fa76eee9900fe8ff43a558658802924a5c6f5d6915874536299a2e9214", "size_in_bytes": 7115}, {"_path": "site-packages/huggingface_hub/cli/hf.py", "path_type": "hardlink", "sha256": "490ef7fd25c442759526484a4ffe9bc0d4011d05c639d239aaa9534ed2345c7d", "sha256_in_prefix": "490ef7fd25c442759526484a4ffe9bc0d4011d05c639d239aaa9534ed2345c7d", "size_in_bytes": 2328}, {"_path": "site-packages/huggingface_hub/cli/jobs.py", "path_type": "hardlink", "sha256": "780e90ee2cbffbbbe35384a360fbe7ef56f6695a36aaddeafa95712f25c25aa8", "sha256_in_prefix": "780e90ee2cbffbbbe35384a360fbe7ef56f6695a36aaddeafa95712f25c25aa8", "size_in_bytes": 44317}, {"_path": "site-packages/huggingface_hub/cli/lfs.py", "path_type": "hardlink", "sha256": "27d32428e1945ba1a306b2acdb365408e6801b1a5ab71b04a126c18ee843255f", "sha256_in_prefix": "27d32428e1945ba1a306b2acdb365408e6801b1a5ab71b04a126c18ee843255f", "size_in_bytes": 7230}, {"_path": "site-packages/huggingface_hub/cli/repo.py", "path_type": "hardlink", "sha256": "0ae3aa419ed610b2e4f5169fded9f25c82edf5ef773ab952f03cb1c7706a7500", "sha256_in_prefix": "0ae3aa419ed610b2e4f5169fded9f25c82edf5ef773ab952f03cb1c7706a7500", "size_in_bytes": 10618}, {"_path": "site-packages/huggingface_hub/cli/repo_files.py", "path_type": "hardlink", "sha256": "2fe2aee76976bd9d3818269ba7f3a1557a8bcc4f5db0a42a6902ae746ce35a0e", "sha256_in_prefix": "2fe2aee76976bd9d3818269ba7f3a1557a8bcc4f5db0a42a6902ae746ce35a0e", "size_in_bytes": 4831}, {"_path": "site-packages/huggingface_hub/cli/system.py", "path_type": "hardlink", "sha256": "78b498304ef2c2de407b7b584274b8dd36a2da947624bb40d4a1889b33ede693", "sha256_in_prefix": "78b498304ef2c2de407b7b584274b8dd36a2da947624bb40d4a1889b33ede693", "size_in_bytes": 1707}, {"_path": "site-packages/huggingface_hub/cli/upload.py", "path_type": "hardlink", "sha256": "a8e19c708701609b68766950945c867d5fd91876215a500046bddfc6060b0e71", "sha256_in_prefix": "a8e19c708701609b68766950945c867d5fd91876215a500046bddfc6060b0e71", "size_in_bytes": 14349}, {"_path": "site-packages/huggingface_hub/cli/upload_large_folder.py", "path_type": "hardlink", "sha256": "7446f510a6cf8b69d1a454be833e0fd03e3df9195fdba300b3a92097b976bc99", "sha256_in_prefix": "7446f510a6cf8b69d1a454be833e0fd03e3df9195fdba300b3a92097b976bc99", "size_in_bytes": 6151}, {"_path": "site-packages/huggingface_hub/commands/__init__.py", "path_type": "hardlink", "sha256": "0246ccd9afa21a1d15abfc405a12b79aedee678e249bcf97e6e5a329cbdcad44", "sha256_in_prefix": "0246ccd9afa21a1d15abfc405a12b79aedee678e249bcf97e6e5a329cbdcad44", "size_in_bytes": 928}, {"_path": "site-packages/huggingface_hub/commands/_cli_utils.py", "path_type": "hardlink", "sha256": "78f6132045a753aefb9cfbdd342e40758704075e34d0beaa604531324554ccc1", "sha256_in_prefix": "78f6132045a753aefb9cfbdd342e40758704075e34d0beaa604531324554ccc1", "size_in_bytes": 2329}, {"_path": "site-packages/huggingface_hub/commands/delete_cache.py", "path_type": "hardlink", "sha256": "d37e7200252d554206f2d12d739bdec43a05169873764e485e4153943e16322c", "sha256_in_prefix": "d37e7200252d554206f2d12d739bdec43a05169873764e485e4153943e16322c", "size_in_bytes": 17738}, {"_path": "site-packages/huggingface_hub/commands/download.py", "path_type": "hardlink", "sha256": "d1063d868ede8803ef1677413edb46b47eaf5cd937afd022a25b4dc1cf21d59e", "sha256_in_prefix": "d1063d868ede8803ef1677413edb46b47eaf5cd937afd022a25b4dc1cf21d59e", "size_in_bytes": 8310}, {"_path": "site-packages/huggingface_hub/commands/env.py", "path_type": "hardlink", "sha256": "aafe129a3bb3533f5ec68e110cc6361ea69b2c2284d6845be7970103a2cdf51e", "sha256_in_prefix": "aafe129a3bb3533f5ec68e110cc6361ea69b2c2284d6845be7970103a2cdf51e", "size_in_bytes": 1342}, {"_path": "site-packages/huggingface_hub/commands/huggingface_cli.py", "path_type": "hardlink", "sha256": "8038bb26e7b288b0f46c67254c461f1cf416a4063f58174f7c74fbbe4a9ae6fd", "sha256_in_prefix": "8038bb26e7b288b0f46c67254c461f1cf416a4063f58174f7c74fbbe4a9ae6fd", "size_in_bytes": 2654}, {"_path": "site-packages/huggingface_hub/commands/lfs.py", "path_type": "hardlink", "sha256": "c5d6e73513b4e14b907a61215064fcd3d8c58109fd463f929f24ffd0f87e5588", "sha256_in_prefix": "c5d6e73513b4e14b907a61215064fcd3d8c58109fd463f929f24ffd0f87e5588", "size_in_bytes": 7342}, {"_path": "site-packages/huggingface_hub/commands/repo.py", "path_type": "hardlink", "sha256": "59c44316a518281d0acf4cc5a297a0886eb5e28b7a54e61301fea386dd0132cb", "sha256_in_prefix": "59c44316a518281d0acf4cc5a297a0886eb5e28b7a54e61301fea386dd0132cb", "size_in_bytes": 6042}, {"_path": "site-packages/huggingface_hub/commands/repo_files.py", "path_type": "hardlink", "sha256": "7ed8cb082dd7098f803268988993c875132622a65ba95670f814a304b719ba9e", "sha256_in_prefix": "7ed8cb082dd7098f803268988993c875132622a65ba95670f814a304b719ba9e", "size_in_bytes": 5054}, {"_path": "site-packages/huggingface_hub/commands/scan_cache.py", "path_type": "hardlink", "sha256": "810961059816914cc7e30ac8627be057b080e02eebbd5d92b98d31d89081ee0d", "sha256_in_prefix": "810961059816914cc7e30ac8627be057b080e02eebbd5d92b98d31d89081ee0d", "size_in_bytes": 8675}, {"_path": "site-packages/huggingface_hub/commands/tag.py", "path_type": "hardlink", "sha256": "e1f810b972471b9f654d5c8e8c85198f17490cbe09656e2ad745c33d2a3e82cb", "sha256_in_prefix": "e1f810b972471b9f654d5c8e8c85198f17490cbe09656e2ad745c33d2a3e82cb", "size_in_bytes": 6382}, {"_path": "site-packages/huggingface_hub/commands/upload.py", "path_type": "hardlink", "sha256": "7802488a0e258ed3bd151c868e2cfa1db1d2f90e0c390ce2460ce342b9792a8a", "sha256_in_prefix": "7802488a0e258ed3bd151c868e2cfa1db1d2f90e0c390ce2460ce342b9792a8a", "size_in_bytes": 14576}, {"_path": "site-packages/huggingface_hub/commands/upload_large_folder.py", "path_type": "hardlink", "sha256": "ff589df38045b5b2fc1e0151299f9e97fb8fae28da9b3d6a5a5cced7a29b5007", "sha256_in_prefix": "ff589df38045b5b2fc1e0151299f9e97fb8fae28da9b3d6a5a5cced7a29b5007", "size_in_bytes": 6254}, {"_path": "site-packages/huggingface_hub/commands/user.py", "path_type": "hardlink", "sha256": "743a62d262d8bd37987f47e13d5432109b27ed6ae4ea05af4796070ba46079b5", "sha256_in_prefix": "743a62d262d8bd37987f47e13d5432109b27ed6ae4ea05af4796070ba46079b5", "size_in_bytes": 7516}, {"_path": "site-packages/huggingface_hub/commands/version.py", "path_type": "hardlink", "sha256": "ac6a426efc48998f5e42a5ebb2162deb4f48c2cdbb47be5601198a42b228e8e9", "sha256_in_prefix": "ac6a426efc48998f5e42a5ebb2162deb4f48c2cdbb47be5601198a42b228e8e9", "size_in_bytes": 1390}, {"_path": "site-packages/huggingface_hub/community.py", "path_type": "hardlink", "sha256": "e0cb5ca31108f7fd259a68a50449ef5048bcfced48bdf6bca7a78ac58539fadb", "sha256_in_prefix": "e0cb5ca31108f7fd259a68a50449ef5048bcfced48bdf6bca7a78ac58539fadb", "size_in_bytes": 12198}, {"_path": "site-packages/huggingface_hub/constants.py", "path_type": "hardlink", "sha256": "9c82ec780a78aea2eefca413643a4f18e85ea5500f6a70fb6b36e8980be8be3d", "sha256_in_prefix": "9c82ec780a78aea2eefca413643a4f18e85ea5500f6a70fb6b36e8980be8be3d", "size_in_bytes": 10313}, {"_path": "site-packages/huggingface_hub/dataclasses.py", "path_type": "hardlink", "sha256": "b203dd122d940e9ae134f3f63cf9224a5cec1dd0b559ca705532f094700472bd", "sha256_in_prefix": "b203dd122d940e9ae134f3f63cf9224a5cec1dd0b559ca705532f094700472bd", "size_in_bytes": 17224}, {"_path": "site-packages/huggingface_hub/errors.py", "path_type": "hardlink", "sha256": "0fb2f0d098eb7fcbdf983d01dba2c4be0f895a453c66ad0a0cf24ecc563840bc", "sha256_in_prefix": "0fb2f0d098eb7fcbdf983d01dba2c4be0f895a453c66ad0a0cf24ecc563840bc", "size_in_bytes": 11201}, {"_path": "site-packages/huggingface_hub/fastai_utils.py", "path_type": "hardlink", "sha256": "0e9787f5dfbabadda4fe7080030825339d579919a07db45ed923e27e954be589", "sha256_in_prefix": "0e9787f5dfbabadda4fe7080030825339d579919a07db45ed923e27e954be589", "size_in_bytes": 16745}, {"_path": "site-packages/huggingface_hub/file_download.py", "path_type": "hardlink", "sha256": "13e35638dd35a69adb02cc3b2b3e3bec95fa7fc1d35ac76911d400b40dfbb797", "sha256_in_prefix": "13e35638dd35a69adb02cc3b2b3e3bec95fa7fc1d35ac76911d400b40dfbb797", "size_in_bytes": 78974}, {"_path": "site-packages/huggingface_hub/hf_api.py", "path_type": "hardlink", "sha256": "634ac0e77be5d29cfc4af44c043286b9a3377a15157f20806bd9b9072344f37d", "sha256_in_prefix": "634ac0e77be5d29cfc4af44c043286b9a3377a15157f20806bd9b9072344f37d", "size_in_bytes": 483625}, {"_path": "site-packages/huggingface_hub/hf_file_system.py", "path_type": "hardlink", "sha256": "aa035f10a2f82556c689ce2a0597658b53a7657b6df73b5a2500e1a83211426f", "sha256_in_prefix": "aa035f10a2f82556c689ce2a0597658b53a7657b6df73b5a2500e1a83211426f", "size_in_bytes": 47033}, {"_path": "site-packages/huggingface_hub/hub_mixin.py", "path_type": "hardlink", "sha256": "222df0f68ed78066e3e9434f9e2796e480df68277c3842a9207d614649dc4434", "sha256_in_prefix": "222df0f68ed78066e3e9434f9e2796e480df68277c3842a9207d614649dc4434", "size_in_bytes": 38208}, {"_path": "site-packages/huggingface_hub/inference/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/huggingface_hub/inference/_client.py", "path_type": "hardlink", "sha256": "0f3b82678fa1c131faf09ef422babd21db21080b1f76d1da1a0ebd65f0f3b26d", "sha256_in_prefix": "0f3b82678fa1c131faf09ef422babd21db21080b1f76d1da1a0ebd65f0f3b26d", "size_in_bytes": 157486}, {"_path": "site-packages/huggingface_hub/inference/_common.py", "path_type": "hardlink", "sha256": "748dce3e0d37db438e074151cbf92a7ed5bd1778211270550391a2e1569272d8", "sha256_in_prefix": "748dce3e0d37db438e074151cbf92a7ed5bd1778211270550391a2e1569272d8", "size_in_bytes": 15778}, {"_path": "site-packages/huggingface_hub/inference/_generated/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/huggingface_hub/inference/_generated/_async_client.py", "path_type": "hardlink", "sha256": "92a60b5ee86f8a1313e68cab4d074b671347f1dc5543f373e9836c1ee7c01893", "sha256_in_prefix": "92a60b5ee86f8a1313e68cab4d074b671347f1dc5543f373e9836c1ee7c01893", "size_in_bytes": 163395}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/__init__.py", "path_type": "hardlink", "sha256": "f56beb190f1a4e1b4a48dcd9174ea3f82204d99b88b677bc1459de6b5a75bb7f", "sha256_in_prefix": "f56beb190f1a4e1b4a48dcd9174ea3f82204d99b88b677bc1459de6b5a75bb7f", "size_in_bytes": 6557}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/audio_classification.py", "path_type": "hardlink", "sha256": "260de6cdf1a10921fa09fbd5be0252885a64cfabf89cd0341b82c95da70480d7", "sha256_in_prefix": "260de6cdf1a10921fa09fbd5be0252885a64cfabf89cd0341b82c95da70480d7", "size_in_bytes": 1573}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/audio_to_audio.py", "path_type": "hardlink", "sha256": "d84a785a478f2fba09c1ca799d126a029c2f8ae9861db7edf4785713d5cb1e3e", "sha256_in_prefix": "d84a785a478f2fba09c1ca799d126a029c2f8ae9861db7edf4785713d5cb1e3e", "size_in_bytes": 891}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/automatic_speech_recognition.py", "path_type": "hardlink", "sha256": "f0212986beabbd11e0ab52f931ddedab5e15d2d1009b32647a6875ffb812b30a", "sha256_in_prefix": "f0212986beabbd11e0ab52f931ddedab5e15d2d1009b32647a6875ffb812b30a", "size_in_bytes": 5515}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/base.py", "path_type": "hardlink", "sha256": "e171b8f6ad3ed9239fb5843c1d742758bc62264b68bbe6bb2281b791d3affa48", "sha256_in_prefix": "e171b8f6ad3ed9239fb5843c1d742758bc62264b68bbe6bb2281b791d3affa48", "size_in_bytes": 6751}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/chat_completion.py", "path_type": "hardlink", "sha256": "8f563c1b8839c86b3883b378b1759b8a917c4f09101b427e7ed2fd3b17a3901c", "sha256_in_prefix": "8f563c1b8839c86b3883b378b1759b8a917c4f09101b427e7ed2fd3b17a3901c", "size_in_bytes": 11254}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/depth_estimation.py", "path_type": "hardlink", "sha256": "adca5ef4c85831e2e37e53b006cdca3193ebe968ce1f7158113852b46f852773", "sha256_in_prefix": "adca5ef4c85831e2e37e53b006cdca3193ebe968ce1f7158113852b46f852773", "size_in_bytes": 929}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/document_question_answering.py", "path_type": "hardlink", "sha256": "e811181b025ca8695a87844124302f5854c45e43b49a8b0188ccbcdb8df69c03", "sha256_in_prefix": "e811181b025ca8695a87844124302f5854c45e43b49a8b0188ccbcdb8df69c03", "size_in_bytes": 3202}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/feature_extraction.py", "path_type": "hardlink", "sha256": "34c5952ff4cb486e524b96ddb75f9f7e5919ef950c94c29e4ccb667675130037", "sha256_in_prefix": "34c5952ff4cb486e524b96ddb75f9f7e5919ef950c94c29e4ccb667675130037", "size_in_bytes": 1537}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/fill_mask.py", "path_type": "hardlink", "sha256": "3ab4e043b35d9f4fdd58ae6d850859c133876d09e2f23d2225cc7d965ca145db", "sha256_in_prefix": "3ab4e043b35d9f4fdd58ae6d850859c133876d09e2f23d2225cc7d965ca145db", "size_in_bytes": 1708}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/image_classification.py", "path_type": "hardlink", "sha256": "03e634db8a3cef6dff9fc986568b384f07409152fad8c70678bd62228e15ccdb", "sha256_in_prefix": "03e634db8a3cef6dff9fc986568b384f07409152fad8c70678bd62228e15ccdb", "size_in_bytes": 1585}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/image_segmentation.py", "path_type": "hardlink", "sha256": "beb908e12b8fd48abf88b5dcfb6a50858637487378833bc506866a6d41f153ba", "sha256_in_prefix": "beb908e12b8fd48abf88b5dcfb6a50858637487378833bc506866a6d41f153ba", "size_in_bytes": 1950}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/image_to_image.py", "path_type": "hardlink", "sha256": "1cfcf5b8a5e4ffdc6f80d522dc657a9f8970f89dc6e9c7464dc5b73aefcdd25f", "sha256_in_prefix": "1cfcf5b8a5e4ffdc6f80d522dc657a9f8970f89dc6e9c7464dc5b73aefcdd25f", "size_in_bytes": 2044}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/image_to_text.py", "path_type": "hardlink", "sha256": "39a1440407e04fe7ce57327bc557ab9867fb54e0e1adcf7e260dfc5ab33bfb6a", "sha256_in_prefix": "39a1440407e04fe7ce57327bc557ab9867fb54e0e1adcf7e260dfc5ab33bfb6a", "size_in_bytes": 4810}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/image_to_video.py", "path_type": "hardlink", "sha256": "6c2f8bfdc36c0e1938b3f21d4a2a6b27d77535e30678f2dc529ed43e9728db5c", "sha256_in_prefix": "6c2f8bfdc36c0e1938b3f21d4a2a6b27d77535e30678f2dc529ed43e9728db5c", "size_in_bytes": 2240}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/object_detection.py", "path_type": "hardlink", "sha256": "56e1656f5dbcd6a4d7a128090e6aae1b3f9535f1192e8d87d1187f17a305eadb", "sha256_in_prefix": "56e1656f5dbcd6a4d7a128090e6aae1b3f9535f1192e8d87d1187f17a305eadb", "size_in_bytes": 2000}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/question_answering.py", "path_type": "hardlink", "sha256": "cf0dfc6bdffd97693589f61979f8e48a8a99e1ab1f49133d3389d4de04829804", "sha256_in_prefix": "cf0dfc6bdffd97693589f61979f8e48a8a99e1ab1f49133d3389d4de04829804", "size_in_bytes": 2898}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/sentence_similarity.py", "path_type": "hardlink", "sha256": "c39363d60d7c781ce8a59c31b832c8f9f12cc9a08ad8aac7039c9ffd77d28e0a", "sha256_in_prefix": "c39363d60d7c781ce8a59c31b832c8f9f12cc9a08ad8aac7039c9ffd77d28e0a", "size_in_bytes": 1052}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/summarization.py", "path_type": "hardlink", "sha256": "5861abf2e0cbad983c250805f593143fd7aec3ab99a3acf091567e21fbc2148d", "sha256_in_prefix": "5861abf2e0cbad983c250805f593143fd7aec3ab99a3acf091567e21fbc2148d", "size_in_bytes": 1487}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/table_question_answering.py", "path_type": "hardlink", "sha256": "7099c83c0d9f21b40fd848e7ed7fdeb18e3ca865a85e0df47e734ea825e23954", "sha256_in_prefix": "7099c83c0d9f21b40fd848e7ed7fdeb18e3ca865a85e0df47e734ea825e23954", "size_in_bytes": 2293}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/text2text_generation.py", "path_type": "hardlink", "sha256": "bfee35f30d4934d499dadb96f43525e9adfa4d04020036b8dfc037b9fbdc6cec", "sha256_in_prefix": "bfee35f30d4934d499dadb96f43525e9adfa4d04020036b8dfc037b9fbdc6cec", "size_in_bytes": 1609}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/text_classification.py", "path_type": "hardlink", "sha256": "15aac08f280b11f3e87cb7ca79a6f327b3ca10122d9471a850d50ecf22d1a4be", "sha256_in_prefix": "15aac08f280b11f3e87cb7ca79a6f327b3ca10122d9471a850d50ecf22d1a4be", "size_in_bytes": 1445}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/text_generation.py", "path_type": "hardlink", "sha256": "dbcbbed7353b7a5936b5e3f7cb8bb5540b430c7cd8d096762841097b9779baf8", "sha256_in_prefix": "dbcbbed7353b7a5936b5e3f7cb8bb5540b430c7cd8d096762841097b9779baf8", "size_in_bytes": 5922}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/text_to_audio.py", "path_type": "hardlink", "sha256": "d4747d43ab3d317aad2864ef1cf2c654cba6e7e7a0ecef8f82fe8d774bfcfc75", "sha256_in_prefix": "d4747d43ab3d317aad2864ef1cf2c654cba6e7e7a0ecef8f82fe8d774bfcfc75", "size_in_bytes": 4741}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/text_to_image.py", "path_type": "hardlink", "sha256": "b061a2d456b49f93e677a1b723e17648125c27533b1a6aa79e0eac7e2d00573b", "sha256_in_prefix": "b061a2d456b49f93e677a1b723e17648125c27533b1a6aa79e0eac7e2d00573b", "size_in_bytes": 1903}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/text_to_speech.py", "path_type": "hardlink", "sha256": "44e16e477da28d13827aa6eff35268b34966680f12456c8852c5ab743e325a8c", "sha256_in_prefix": "44e16e477da28d13827aa6eff35268b34966680f12456c8852c5ab743e325a8c", "size_in_bytes": 4760}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/text_to_video.py", "path_type": "hardlink", "sha256": "c875d536cdede9a60eeef8acac1941e5c1fb923a32b3117de75d1aa1f71fff5f", "sha256_in_prefix": "c875d536cdede9a60eeef8acac1941e5c1fb923a32b3117de75d1aa1f71fff5f", "size_in_bytes": 1790}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/token_classification.py", "path_type": "hardlink", "sha256": "89b9407207f15de68b609d7835d8a208c210b8195aad49272e45d492586f70b2", "sha256_in_prefix": "89b9407207f15de68b609d7835d8a208c210b8195aad49272e45d492586f70b2", "size_in_bytes": 1915}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/translation.py", "path_type": "hardlink", "sha256": "c70c385f971f098bff174a083562f0a8944f093e92577541009b8f8d3b3f8fba", "sha256_in_prefix": "c70c385f971f098bff174a083562f0a8944f093e92577541009b8f8d3b3f8fba", "size_in_bytes": 1763}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/video_classification.py", "path_type": "hardlink", "sha256": "4f2c9d8d0c363512caf6c0c6cc95159e40dea3ce3c79b982c79f3c52bf08f6ad", "sha256_in_prefix": "4f2c9d8d0c363512caf6c0c6cc95159e40dea3ce3c79b982c79f3c52bf08f6ad", "size_in_bytes": 1680}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/visual_question_answering.py", "path_type": "hardlink", "sha256": "016ad0eaaa388196b73c679d68da730c5ab1e72398ca3867501ea2b99123feea", "sha256_in_prefix": "016ad0eaaa388196b73c679d68da730c5ab1e72398ca3867501ea2b99123feea", "size_in_bytes": 1673}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/zero_shot_classification.py", "path_type": "hardlink", "sha256": "04089e6cf8ecaa835af04537e43c74a5f22ff16d9ce064a5f9325c915d4c6b14", "sha256_in_prefix": "04089e6cf8ecaa835af04537e43c74a5f22ff16d9ce064a5f9325c915d4c6b14", "size_in_bytes": 1738}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/zero_shot_image_classification.py", "path_type": "hardlink", "sha256": "f09f67e95a85011916bcf7c064d5841bbd0096b30695d53de441904309f4eb32", "sha256_in_prefix": "f09f67e95a85011916bcf7c064d5841bbd0096b30695d53de441904309f4eb32", "size_in_bytes": 1487}, {"_path": "site-packages/huggingface_hub/inference/_generated/types/zero_shot_object_detection.py", "path_type": "hardlink", "sha256": "19477cd4b215ee811b4566b20e50158322e6639f7aaf5337016d235c3a75c930", "sha256_in_prefix": "19477cd4b215ee811b4566b20e50158322e6639f7aaf5337016d235c3a75c930", "size_in_bytes": 1630}, {"_path": "site-packages/huggingface_hub/inference/_mcp/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/huggingface_hub/inference/_mcp/_cli_hacks.py", "path_type": "hardlink", "sha256": "297f476493dad69f27818de6b581869555177e0e2f61b6c146cfbc1cb4e83f4e", "sha256_in_prefix": "297f476493dad69f27818de6b581869555177e0e2f61b6c146cfbc1cb4e83f4e", "size_in_bytes": 3284}, {"_path": "site-packages/huggingface_hub/inference/_mcp/agent.py", "path_type": "hardlink", "sha256": "55a86f4aa95d882d51ef60851f84f4e65f34b845e5e4e8cbc1ba164055096ecc", "sha256_in_prefix": "55a86f4aa95d882d51ef60851f84f4e65f34b845e5e4e8cbc1ba164055096ecc", "size_in_bytes": 4281}, {"_path": "site-packages/huggingface_hub/inference/_mcp/cli.py", "path_type": "hardlink", "sha256": "0264944fac17944e845a623449f4204d66272f4ef7db6cc6c30936c8c6599417", "sha256_in_prefix": "0264944fac17944e845a623449f4204d66272f4ef7db6cc6c30936c8c6599417", "size_in_bytes": 9640}, {"_path": "site-packages/huggingface_hub/inference/_mcp/constants.py", "path_type": "hardlink", "sha256": "9257517da89d5cc772325fe32e8b1a4289a0583bf8b21be715eddd9d03705d25", "sha256_in_prefix": "9257517da89d5cc772325fe32e8b1a4289a0583bf8b21be715eddd9d03705d25", "size_in_bytes": 2511}, {"_path": "site-packages/huggingface_hub/inference/_mcp/mcp_client.py", "path_type": "hardlink", "sha256": "88a18e63a7c3d1ca61f30a7dad79687c98250cb71f5c6c5e76c27718b575cfe8", "sha256_in_prefix": "88a18e63a7c3d1ca61f30a7dad79687c98250cb71f5c6c5e76c27718b575cfe8", "size_in_bytes": 16745}, {"_path": "site-packages/huggingface_hub/inference/_mcp/types.py", "path_type": "hardlink", "sha256": "de0abe3ff9ab9af3c8e8a581aa30b16afb4c3e21b3d74617a20ef0838a09600a", "sha256_in_prefix": "de0abe3ff9ab9af3c8e8a581aa30b16afb4c3e21b3d74617a20ef0838a09600a", "size_in_bytes": 941}, {"_path": "site-packages/huggingface_hub/inference/_mcp/utils.py", "path_type": "hardlink", "sha256": "285b06382f1dcad4b75606ae801ce26ddb5e59ab19f42027a7cdd4d92c8894cc", "sha256_in_prefix": "285b06382f1dcad4b75606ae801ce26ddb5e59ab19f42027a7cdd4d92c8894cc", "size_in_bytes": 4188}, {"_path": "site-packages/huggingface_hub/inference/_providers/__init__.py", "path_type": "hardlink", "sha256": "d2d24861a7a0d6f3b46a222c75dbe49d9b01d007ffd0068dfea62d764f61192c", "sha256_in_prefix": "d2d24861a7a0d6f3b46a222c75dbe49d9b01d007ffd0068dfea62d764f61192c", "size_in_bytes": 8740}, {"_path": "site-packages/huggingface_hub/inference/_providers/_common.py", "path_type": "hardlink", "sha256": "270bf1ad0623b11c18903f1a382af13d74afecb9adbf85df9f442341b41d005d", "sha256_in_prefix": "270bf1ad0623b11c18903f1a382af13d74afecb9adbf85df9f442341b41d005d", "size_in_bytes": 12334}, {"_path": "site-packages/huggingface_hub/inference/_providers/black_forest_labs.py", "path_type": "hardlink", "sha256": "148ba4668205b7f143ad34c37e917e564a39b179e61f442f548b0cb55d89ce6f", "sha256_in_prefix": "148ba4668205b7f143ad34c37e917e564a39b179e61f442f548b0cb55d89ce6f", "size_in_bytes": 2852}, {"_path": "site-packages/huggingface_hub/inference/_providers/cerebras.py", "path_type": "hardlink", "sha256": "40e27ed54fa8b3bb84ee9e9e5279ff3ff00fabec90871dbc6deedcdd3ab612e0", "sha256_in_prefix": "40e27ed54fa8b3bb84ee9e9e5279ff3ff00fabec90871dbc6deedcdd3ab612e0", "size_in_bytes": 210}, {"_path": "site-packages/huggingface_hub/inference/_providers/cohere.py", "path_type": "hardlink", "sha256": "3b7b42faa2142fdd66c7f984f1b3870ad0d6710b8a3946ae854a17494054099f", "sha256_in_prefix": "3b7b42faa2142fdd66c7f984f1b3870ad0d6710b8a3946ae854a17494054099f", "size_in_bytes": 1253}, {"_path": "site-packages/huggingface_hub/inference/_providers/fal_ai.py", "path_type": "hardlink", "sha256": "0ac3021d0e3ab55537ba4ad9e2a4172b6dbbe47aa310c89e081b627c2dfbd1e5", "sha256_in_prefix": "0ac3021d0e3ab55537ba4ad9e2a4172b6dbbe47aa310c89e081b627c2dfbd1e5", "size_in_bytes": 9880}, {"_path": "site-packages/huggingface_hub/inference/_providers/featherless_ai.py", "path_type": "hardlink", "sha256": "431073fb7d8ee0fcedc62c6b223adf2ae4cecef7eac948be715b30d077ffce56", "sha256_in_prefix": "431073fb7d8ee0fcedc62c6b223adf2ae4cecef7eac948be715b30d077ffce56", "size_in_bytes": 1382}, {"_path": "site-packages/huggingface_hub/inference/_providers/fireworks_ai.py", "path_type": "hardlink", "sha256": "21ddb6e884df3e439c14c173972dcc5bd97e75997d978aa2ccbe091075a40459", "sha256_in_prefix": "21ddb6e884df3e439c14c173972dcc5bd97e75997d978aa2ccbe091075a40459", "size_in_bytes": 1215}, {"_path": "site-packages/huggingface_hub/inference/_providers/groq.py", "path_type": "hardlink", "sha256": "253936255e193a56a8861a3bccb00542d93dda4195b0f98b275866cdcc2caaf4", "sha256_in_prefix": "253936255e193a56a8861a3bccb00542d93dda4195b0f98b275866cdcc2caaf4", "size_in_bytes": 315}, {"_path": "site-packages/huggingface_hub/inference/_providers/hf_inference.py", "path_type": "hardlink", "sha256": "d328b7711f842781d8c77992cceb0c393566bd506469a8d3cd37ca07c2574299", "sha256_in_prefix": "d328b7711f842781d8c77992cceb0c393566bd506469a8d3cd37ca07c2574299", "size_in_bytes": 9540}, {"_path": "site-packages/huggingface_hub/inference/_providers/hyperbolic.py", "path_type": "hardlink", "sha256": "3902018b68f768dbee69243c0542b52b53d57915ddaf173cd06fba62605afa7b", "sha256_in_prefix": "3902018b68f768dbee69243c0542b52b53d57915ddaf173cd06fba62605afa7b", "size_in_bytes": 1985}, {"_path": "site-packages/huggingface_hub/inference/_providers/nebius.py", "path_type": "hardlink", "sha256": "549a53176259e7caf39dcf70c5d93ee7bbf017cb15daf112c3f5a45e35ea0a1a", "sha256_in_prefix": "549a53176259e7caf39dcf70c5d93ee7bbf017cb15daf112c3f5a45e35ea0a1a", "size_in_bytes": 3580}, {"_path": "site-packages/huggingface_hub/inference/_providers/novita.py", "path_type": "hardlink", "sha256": "1c6542f303eb691414b88e6e068c9ed58e16a9ee17d75e81ef51a185b5b2e723", "sha256_in_prefix": "1c6542f303eb691414b88e6e068c9ed58e16a9ee17d75e81ef51a185b5b2e723", "size_in_bytes": 2514}, {"_path": "site-packages/huggingface_hub/inference/_providers/nscale.py", "path_type": "hardlink", "sha256": "a9652c5a29d09946cd52a7a1c8a9f7e2d56859e86ef2077e399d85e2e8f64963", "sha256_in_prefix": "a9652c5a29d09946cd52a7a1c8a9f7e2d56859e86ef2077e399d85e2e8f64963", "size_in_bytes": 1802}, {"_path": "site-packages/huggingface_hub/inference/_providers/openai.py", "path_type": "hardlink", "sha256": "18255878d763588829410ec4fd7bfc21e6e67614e2d12e967c5a2ccf79cbb69b", "sha256_in_prefix": "18255878d763588829410ec4fd7bfc21e6e67614e2d12e967c5a2ccf79cbb69b", "size_in_bytes": 1089}, {"_path": "site-packages/huggingface_hub/inference/_providers/publicai.py", "path_type": "hardlink", "sha256": "d48d96eab3919680794074af932e278d93b65ca2d3c00fa43dd3686aea13e6b8", "sha256_in_prefix": "d48d96eab3919680794074af932e278d93b65ca2d3c00fa43dd3686aea13e6b8", "size_in_bytes": 210}, {"_path": "site-packages/huggingface_hub/inference/_providers/replicate.py", "path_type": "hardlink", "sha256": "a2d55f3e47c1b655aba6342e6f857ffedee0ff0f04c1ced953779f88e6ae5be2", "sha256_in_prefix": "a2d55f3e47c1b655aba6342e6f857ffedee0ff0f04c1ced953779f88e6ae5be2", "size_in_bytes": 3820}, {"_path": "site-packages/huggingface_hub/inference/_providers/sambanova.py", "path_type": "hardlink", "sha256": "527b771f78ebfe4808f6fcd18e6996d43172a04b8f90a09c808225a223a3de3f", "sha256_in_prefix": "527b771f78ebfe4808f6fcd18e6996d43172a04b8f90a09c808225a223a3de3f", "size_in_bytes": 2037}, {"_path": "site-packages/huggingface_hub/inference/_providers/scaleway.py", "path_type": "hardlink", "sha256": "272f3591759b5c21c1a71eb19b2cdd11f5c64b28547e328e2c7b834af847586a", "sha256_in_prefix": "272f3591759b5c21c1a71eb19b2cdd11f5c64b28547e328e2c7b834af847586a", "size_in_bytes": 1209}, {"_path": "site-packages/huggingface_hub/inference/_providers/together.py", "path_type": "hardlink", "sha256": "287175f424b7a974bb1b5f82c1c3220fc679c333ca10a8b81760f3a80b616c11", "sha256_in_prefix": "287175f424b7a974bb1b5f82c1c3220fc679c333ca10a8b81760f3a80b616c11", "size_in_bytes": 3439}, {"_path": "site-packages/huggingface_hub/inference_api.py", "path_type": "hardlink", "sha256": "6f8f8d84f4a7f5be389d8295f2d0ca5e8766138255744ca63162f80951a4ce51", "sha256_in_prefix": "6f8f8d84f4a7f5be389d8295f2d0ca5e8766138255744ca63162f80951a4ce51", "size_in_bytes": 8323}, {"_path": "site-packages/huggingface_hub/keras_mixin.py", "path_type": "hardlink", "sha256": "58635065139dc3aca32750c64cf64fc0a0317f551b933031d5d46276479bd9f9", "sha256_in_prefix": "58635065139dc3aca32750c64cf64fc0a0317f551b933031d5d46276479bd9f9", "size_in_bytes": 19553}, {"_path": "site-packages/huggingface_hub/lfs.py", "path_type": "hardlink", "sha256": "9fe4c88caec9eda5c6df38bfff49e477a68d904e1d8ce7fd083e9d610390e4ff", "sha256_in_prefix": "9fe4c88caec9eda5c6df38bfff49e477a68d904e1d8ce7fd083e9d610390e4ff", "size_in_bytes": 16649}, {"_path": "site-packages/huggingface_hub/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/huggingface_hub/repocard.py", "path_type": "hardlink", "sha256": "d606ac9393b37f2243be75c13119b8f554f4482888b2909852e91238635a7c71", "sha256_in_prefix": "d606ac9393b37f2243be75c13119b8f554f4482888b2909852e91238635a7c71", "size_in_bytes": 34857}, {"_path": "site-packages/huggingface_hub/repocard_data.py", "path_type": "hardlink", "sha256": "86be11785a4440c35d87ff43c7e2fe209a08d44947ca4fa1fbc651ab055860e1", "sha256_in_prefix": "86be11785a4440c35d87ff43c7e2fe209a08d44947ca4fa1fbc651ab055860e1", "size_in_bytes": 34082}, {"_path": "site-packages/huggingface_hub/repository.py", "path_type": "hardlink", "sha256": "2deaeade4afbb42fa851d664e62d42761000f3866f622aa368647bee3da23b29", "sha256_in_prefix": "2deaeade4afbb42fa851d664e62d42761000f3866f622aa368647bee3da23b29", "size_in_bytes": 54536}, {"_path": "site-packages/huggingface_hub/serialization/__init__.py", "path_type": "hardlink", "sha256": "927f856be9b817332737c94db05f92c0571fcee8380ae71ec726c62b2bd9f12d", "sha256_in_prefix": "927f856be9b817332737c94db05f92c0571fcee8380ae71ec726c62b2bd9f12d", "size_in_bytes": 1041}, {"_path": "site-packages/huggingface_hub/serialization/_base.py", "path_type": "hardlink", "sha256": "0dfdc6c0647d37378afd20fbe69ad72ee7090333e234f8076e05d2c3efcb4e4f", "sha256_in_prefix": "0dfdc6c0647d37378afd20fbe69ad72ee7090333e234f8076e05d2c3efcb4e4f", "size_in_bytes": 8126}, {"_path": "site-packages/huggingface_hub/serialization/_dduf.py", "path_type": "hardlink", "sha256": "b38db6dfdacb887c1a244dfa4031264b9187ec34a643ffc17df88724ee518c88", "sha256_in_prefix": "b38db6dfdacb887c1a244dfa4031264b9187ec34a643ffc17df88724ee518c88", "size_in_bytes": 15424}, {"_path": "site-packages/huggingface_hub/serialization/_tensorflow.py", "path_type": "hardlink", "sha256": "cc73af10c83e2470b9e459b8ae80d3dcb5020cee7307daad5d97df1b4eb94403", "sha256_in_prefix": "cc73af10c83e2470b9e459b8ae80d3dcb5020cee7307daad5d97df1b4eb94403", "size_in_bytes": 3625}, {"_path": "site-packages/huggingface_hub/serialization/_torch.py", "path_type": "hardlink", "sha256": "8e9066b92649ca6329bcb70370c68dc43bbf7c4e55918fe90151fc7bcb2d608a", "sha256_in_prefix": "8e9066b92649ca6329bcb70370c68dc43bbf7c4e55918fe90151fc7bcb2d608a", "size_in_bytes": 45201}, {"_path": "site-packages/huggingface_hub/templates/datasetcard_template.md", "path_type": "hardlink", "sha256": "5be10ca91eb09dd6eb9d9a2b915bf9e944563c6e3d97b3004c6788d35e644efb", "sha256_in_prefix": "5be10ca91eb09dd6eb9d9a2b915bf9e944563c6e3d97b3004c6788d35e644efb", "size_in_bytes": 5503}, {"_path": "site-packages/huggingface_hub/templates/modelcard_template.md", "path_type": "hardlink", "sha256": "e00a80ad2ddca9db5b8ade41a3e0e18dc9c3151fa9cdae6112b2cb4cf33862e7", "sha256_in_prefix": "e00a80ad2ddca9db5b8ade41a3e0e18dc9c3151fa9cdae6112b2cb4cf33862e7", "size_in_bytes": 6870}, {"_path": "site-packages/huggingface_hub/utils/__init__.py", "path_type": "hardlink", "sha256": "3917d5927e43d30b8b22ad768e3853ce7e7f73817c7d13f107b4c6fa279d9ee4", "sha256_in_prefix": "3917d5927e43d30b8b22ad768e3853ce7e7f73817c7d13f107b4c6fa279d9ee4", "size_in_bytes": 3722}, {"_path": "site-packages/huggingface_hub/utils/_auth.py", "path_type": "hardlink", "sha256": "231bdedafc5d7ed1d75e4d91daf7f22f3955a034f7f5392af87ae8bbd28252fc", "sha256_in_prefix": "231bdedafc5d7ed1d75e4d91daf7f22f3955a034f7f5392af87ae8bbd28252fc", "size_in_bytes": 8286}, {"_path": "site-packages/huggingface_hub/utils/_cache_assets.py", "path_type": "hardlink", "sha256": "91a8bbec73d031f62944ea2e301402aff81d04269e4e6f7de92aa3d1d1316cd8", "sha256_in_prefix": "91a8bbec73d031f62944ea2e301402aff81d04269e4e6f7de92aa3d1d1316cd8", "size_in_bytes": 5728}, {"_path": "site-packages/huggingface_hub/utils/_cache_manager.py", "path_type": "hardlink", "sha256": "a2ca95e20339331dbddcac353ceb89e6e288728206e7b818d24e8153abdd8cf0", "sha256_in_prefix": "a2ca95e20339331dbddcac353ceb89e6e288728206e7b818d24e8153abdd8cf0", "size_in_bytes": 34518}, {"_path": "site-packages/huggingface_hub/utils/_chunk_utils.py", "path_type": "hardlink", "sha256": "91109a8f9db6f3fbca7322d6b2977c5ead357f5ec9cfa76ce52afd79de5dfd15", "sha256_in_prefix": "91109a8f9db6f3fbca7322d6b2977c5ead357f5ec9cfa76ce52afd79de5dfd15", "size_in_bytes": 2130}, {"_path": "site-packages/huggingface_hub/utils/_datetime.py", "path_type": "hardlink", "sha256": "9024b98da295db990e9dc5f5c6e8db5eccf98832dc8cb70bc3ce6c7a618dcf14", "sha256_in_prefix": "9024b98da295db990e9dc5f5c6e8db5eccf98832dc8cb70bc3ce6c7a618dcf14", "size_in_bytes": 2770}, {"_path": "site-packages/huggingface_hub/utils/_deprecation.py", "path_type": "hardlink", "sha256": "1d9851186517fd030a0410701c795f7cbb660922b4d533a97971de7d96cf7f02", "sha256_in_prefix": "1d9851186517fd030a0410701c795f7cbb660922b4d533a97971de7d96cf7f02", "size_in_bytes": 4872}, {"_path": "site-packages/huggingface_hub/utils/_dotenv.py", "path_type": "hardlink", "sha256": "4731ea0bc1e0cd5c44f8de031417277a6bd7d0d1e65dc574332d8048ad14d4e4", "sha256_in_prefix": "4731ea0bc1e0cd5c44f8de031417277a6bd7d0d1e65dc574332d8048ad14d4e4", "size_in_bytes": 2017}, {"_path": "site-packages/huggingface_hub/utils/_experimental.py", "path_type": "hardlink", "sha256": "dfe73c8ab6e7f6c26bd82c166f3846908add5ca83cff1ec189f847172df67a2f", "sha256_in_prefix": "dfe73c8ab6e7f6c26bd82c166f3846908add5ca83cff1ec189f847172df67a2f", "size_in_bytes": 2470}, {"_path": "site-packages/huggingface_hub/utils/_fixes.py", "path_type": "hardlink", "sha256": "c50575424527d96a4ba8b8ed5cd8b29fd821fb8e782ba005f90de4c24600403f", "sha256_in_prefix": "c50575424527d96a4ba8b8ed5cd8b29fd821fb8e782ba005f90de4c24600403f", "size_in_bytes": 4437}, {"_path": "site-packages/huggingface_hub/utils/_git_credential.py", "path_type": "hardlink", "sha256": "6a8f6babead51e7f258214956448c3017e24224362edb6b2637eb54c34a04a98", "sha256_in_prefix": "6a8f6babead51e7f258214956448c3017e24224362edb6b2637eb54c34a04a98", "size_in_bytes": 4619}, {"_path": "site-packages/huggingface_hub/utils/_headers.py", "path_type": "hardlink", "sha256": "c386b2ab884b19a67707b9f07448b966edb74a6983b8ec2fe7c22defcc246a49", "sha256_in_prefix": "c386b2ab884b19a67707b9f07448b966edb74a6983b8ec2fe7c22defcc246a49", "size_in_bytes": 8868}, {"_path": "site-packages/huggingface_hub/utils/_hf_folder.py", "path_type": "hardlink", "sha256": "58d8d39eed10eeda9c492f44b0fe2cb0226bac931c0af02df0fffe2c4b66394f", "sha256_in_prefix": "58d8d39eed10eeda9c492f44b0fe2cb0226bac931c0af02df0fffe2c4b66394f", "size_in_bytes": 2487}, {"_path": "site-packages/huggingface_hub/utils/_http.py", "path_type": "hardlink", "sha256": "85eafb519d0a468f5660302ba6a545c845d3bac3865040a3e47352f046a1aa6f", "sha256_in_prefix": "85eafb519d0a468f5660302ba6a545c845d3bac3865040a3e47352f046a1aa6f", "size_in_bytes": 25531}, {"_path": "site-packages/huggingface_hub/utils/_lfs.py", "path_type": "hardlink", "sha256": "102d0ecfa5a2c25f1fa1136450eceb1135f301695b8299e9c68e5ae35d28bc56", "sha256_in_prefix": "102d0ecfa5a2c25f1fa1136450eceb1135f301695b8299e9c68e5ae35d28bc56", "size_in_bytes": 3957}, {"_path": "site-packages/huggingface_hub/utils/_pagination.py", "path_type": "hardlink", "sha256": "117e6d45ab12b900da29b5ee1986c89c12b6a1d9d258d1e0cf0dad4a0a9e0512", "sha256_in_prefix": "117e6d45ab12b900da29b5ee1986c89c12b6a1d9d258d1e0cf0dad4a0a9e0512", "size_in_bytes": 1906}, {"_path": "site-packages/huggingface_hub/utils/_paths.py", "path_type": "hardlink", "sha256": "c35661166983e729168e9fe102f863b4ea1ad9951c39726b1786ba3b7429016a", "sha256_in_prefix": "c35661166983e729168e9fe102f863b4ea1ad9951c39726b1786ba3b7429016a", "size_in_bytes": 5042}, {"_path": "site-packages/huggingface_hub/utils/_runtime.py", "path_type": "hardlink", "sha256": "2fb48e61ecddc4a7307780e8bc0634506637aaddbbb685cef908dc7880f01319", "sha256_in_prefix": "2fb48e61ecddc4a7307780e8bc0634506637aaddbbb685cef908dc7880f01319", "size_in_bytes": 11634}, {"_path": "site-packages/huggingface_hub/utils/_safetensors.py", "path_type": "hardlink", "sha256": "196de7cafef141cbb039b298798a13f55854455cc6d436536ca04a868f016e8b", "sha256_in_prefix": "196de7cafef141cbb039b298798a13f55854455cc6d436536ca04a868f016e8b", "size_in_bytes": 4458}, {"_path": "site-packages/huggingface_hub/utils/_subprocess.py", "path_type": "hardlink", "sha256": "bbd14550313b4ebcd04e2b84ce55271f1ed2d8fe7b19b61157cbb5e86270ac5c", "sha256_in_prefix": "bbd14550313b4ebcd04e2b84ce55271f1ed2d8fe7b19b61157cbb5e86270ac5c", "size_in_bytes": 4625}, {"_path": "site-packages/huggingface_hub/utils/_telemetry.py", "path_type": "hardlink", "sha256": "e782d7788254e691068213c0874ea0a8d011f94a313a354bbcaa8042c730a99b", "sha256_in_prefix": "e782d7788254e691068213c0874ea0a8d011f94a313a354bbcaa8042c730a99b", "size_in_bytes": 4890}, {"_path": "site-packages/huggingface_hub/utils/_typing.py", "path_type": "hardlink", "sha256": "cfed77e3e1c6fea25cd1c8dd4979260e6def211c85e5a11f6d9809081fd0a766", "sha256_in_prefix": "cfed77e3e1c6fea25cd1c8dd4979260e6def211c85e5a11f6d9809081fd0a766", "size_in_bytes": 3628}, {"_path": "site-packages/huggingface_hub/utils/_validators.py", "path_type": "hardlink", "sha256": "743b151b7d62a284d8ac8ca2e55c2bd43ba42f47c4989c2eddc79535dba1b2e1", "sha256_in_prefix": "743b151b7d62a284d8ac8ca2e55c2bd43ba42f47c4989c2eddc79535dba1b2e1", "size_in_bytes": 9204}, {"_path": "site-packages/huggingface_hub/utils/_xet.py", "path_type": "hardlink", "sha256": "7fca9f93c60a78f01e1942fa950890d70ff76dcb3bf285b06de00261d51e8399", "sha256_in_prefix": "7fca9f93c60a78f01e1942fa950890d70ff76dcb3bf285b06de00261d51e8399", "size_in_bytes": 7312}, {"_path": "site-packages/huggingface_hub/utils/_xet_progress_reporting.py", "path_type": "hardlink", "sha256": "24aeb886ff28ac005f367935fd67746320ffe457dec95781bde94aa6368d22fb", "sha256_in_prefix": "24aeb886ff28ac005f367935fd67746320ffe457dec95781bde94aa6368d22fb", "size_in_bytes": 6169}, {"_path": "site-packages/huggingface_hub/utils/endpoint_helpers.py", "path_type": "hardlink", "sha256": "f55b48025c50e47ff8cb7d2c8c20206eeed70aa02d34b0bb69163168d9f484b2", "sha256_in_prefix": "f55b48025c50e47ff8cb7d2c8c20206eeed70aa02d34b0bb69163168d9f484b2", "size_in_bytes": 2366}, {"_path": "site-packages/huggingface_hub/utils/insecure_hashlib.py", "path_type": "hardlink", "sha256": "88069ea5abc56790e17dae67f0aa3345f429aca9af7234a7b775f9f0e525f5f4", "sha256_in_prefix": "88069ea5abc56790e17dae67f0aa3345f429aca9af7234a7b775f9f0e525f5f4", "size_in_bytes": 1142}, {"_path": "site-packages/huggingface_hub/utils/logging.py", "path_type": "hardlink", "sha256": "d00f1f175ca1dcbf4a6bf6c20d7da69785391edd2d63c0ebdc971b46f585bb0a", "sha256_in_prefix": "d00f1f175ca1dcbf4a6bf6c20d7da69785391edd2d63c0ebdc971b46f585bb0a", "size_in_bytes": 4909}, {"_path": "site-packages/huggingface_hub/utils/sha.py", "path_type": "hardlink", "sha256": "3859cd1826dad2c35c4f6814c1a54226795dc6596dac71ded034bf3c2a55dc2e", "sha256_in_prefix": "3859cd1826dad2c35c4f6814c1a54226795dc6596dac71ded034bf3c2a55dc2e", "size_in_bytes": 2134}, {"_path": "site-packages/huggingface_hub/utils/tqdm.py", "path_type": "hardlink", "sha256": "c4029cc9f9cd1ec67b2f4f56b84339130e7e3038626a12c009b6cddb332670bb", "sha256_in_prefix": "c4029cc9f9cd1ec67b2f4f56b84339130e7e3038626a12c009b6cddb332670bb", "size_in_bytes": 10671}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/_commit_api.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/_commit_scheduler.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/_inference_endpoints.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/_jobs_api.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/_local_folder.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/_login.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/_oauth.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/_snapshot_download.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/_space_api.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/_tensorboard_logger.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/_upload_large_folder.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/_webhooks_payload.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/_webhooks_server.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/_cli_utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/auth.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/cache.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/download.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/hf.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/jobs.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/lfs.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/repo.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/repo_files.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/system.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/upload.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/cli/__pycache__/upload_large_folder.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/_cli_utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/delete_cache.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/download.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/env.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/huggingface_cli.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/lfs.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/repo.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/repo_files.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/scan_cache.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/tag.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/upload.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/upload_large_folder.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/user.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/commands/__pycache__/version.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/community.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/constants.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/dataclasses.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/errors.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/fastai_utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/file_download.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/hf_api.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/hf_file_system.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/hub_mixin.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/__pycache__/_client.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/__pycache__/_common.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/__pycache__/_async_client.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/audio_classification.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/audio_to_audio.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/automatic_speech_recognition.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/base.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/chat_completion.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/depth_estimation.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/document_question_answering.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/feature_extraction.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/fill_mask.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_classification.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_segmentation.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_to_image.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_to_text.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/image_to_video.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/object_detection.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/question_answering.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/sentence_similarity.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/summarization.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/table_question_answering.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text2text_generation.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_classification.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_generation.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_to_audio.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_to_image.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_to_speech.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/text_to_video.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/token_classification.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/translation.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/video_classification.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/visual_question_answering.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/zero_shot_classification.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/zero_shot_image_classification.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_generated/types/__pycache__/zero_shot_object_detection.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/__pycache__/_cli_hacks.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/__pycache__/agent.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/__pycache__/cli.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/__pycache__/constants.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/__pycache__/mcp_client.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/__pycache__/types.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_mcp/__pycache__/utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/_common.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/black_forest_labs.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/cerebras.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/cohere.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/fal_ai.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/featherless_ai.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/fireworks_ai.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/groq.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/hf_inference.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/hyperbolic.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/nebius.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/novita.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/nscale.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/openai.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/publicai.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/replicate.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/sambanova.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/scaleway.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/inference/_providers/__pycache__/together.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/inference_api.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/keras_mixin.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/lfs.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/repocard.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/repocard_data.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/__pycache__/repository.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/serialization/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/serialization/__pycache__/_base.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/serialization/__pycache__/_dduf.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/serialization/__pycache__/_tensorflow.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/serialization/__pycache__/_torch.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_auth.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_cache_assets.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_cache_manager.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_chunk_utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_datetime.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_deprecation.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_dotenv.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_experimental.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_fixes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_git_credential.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_headers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_hf_folder.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_http.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_lfs.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_pagination.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_paths.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_runtime.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_safetensors.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_subprocess.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_telemetry.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_typing.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_validators.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_xet.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/_xet_progress_reporting.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/endpoint_helpers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/insecure_hashlib.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/logging.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/sha.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/huggingface_hub/utils/__pycache__/tqdm.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "bin/huggingface-cli", "path_type": "unix_python_entry_point"}, {"_path": "bin/hf", "path_type": "unix_python_entry_point"}, {"_path": "bin/tiny-agents", "path_type": "unix_python_entry_point"}], "paths_version": 1}, "requested_spec": "None", "sha256": "4f566f2e62a798f2b0fbe003682e36c626963cb6065dba5ed015fbb0c620d6f0", "size": 335815, "subdir": "noarch", "timestamp": 1758097199000, "url": "https://conda.anaconda.org/conda-forge/noarch/huggingface_hub-0.35.0-pyhd8ed1ab_0.conda", "version": "0.35.0"}