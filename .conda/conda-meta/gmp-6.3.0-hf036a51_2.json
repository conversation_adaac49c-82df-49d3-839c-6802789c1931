{"build": "hf036a51_2", "build_number": 2, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13", "libcxx >=16"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/gmp-6.3.0-hf036a51_2", "files": ["include/gmp.h", "include/gmpxx.h", "lib/libgmp.10.dylib", "lib/libgmp.dylib", "lib/libgmpxx.4.dylib", "lib/libgmpxx.dylib", "lib/pkgconfig/gmp.pc", "lib/pkgconfig/gmpxx.pc", "share/info/gmp.info", "share/info/gmp.info-1", "share/info/gmp.info-2"], "fn": "gmp-6.3.0-hf036a51_2.conda", "license": "GPL-2.0-or-later OR LGPL-3.0-or-later", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/gmp-6.3.0-hf036a51_2", "type": 1}, "md5": "427101d13f19c4974552a4e5b072eef1", "name": "gmp", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/gmp-6.3.0-hf036a51_2.conda", "paths_data": {"paths": [{"_path": "include/gmp.h", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/gmp_1718980472988/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_place", "sha256": "8367553e8818390dea9bbe83ab06b7724eda7eb36f034e3131c09b6c2e5dd67c", "sha256_in_prefix": "fc0e4d67704a0a7bfc7d1357f57a855a7f2569dc461fe6c378eb36dd46fabf3b", "size_in_bytes": 85041}, {"_path": "include/gmpxx.h", "path_type": "hardlink", "sha256": "b287e1197a745582acb9c4aa0d2f2ffa16fabaeeabd0dee836553771556a00ea", "sha256_in_prefix": "b287e1197a745582acb9c4aa0d2f2ffa16fabaeeabd0dee836553771556a00ea", "size_in_bytes": 129502}, {"_path": "lib/libgmp.10.dylib", "path_type": "hardlink", "sha256": "c1107c0c3e0cdfcaace7d4327151184c19be6b3f3730165145c840011bc0a65a", "sha256_in_prefix": "c1107c0c3e0cdfcaace7d4327151184c19be6b3f3730165145c840011bc0a65a", "size_in_bytes": 619624}, {"_path": "lib/libgmp.dylib", "path_type": "softlink", "sha256": "c1107c0c3e0cdfcaace7d4327151184c19be6b3f3730165145c840011bc0a65a", "size_in_bytes": 619624}, {"_path": "lib/libgmpxx.4.dylib", "path_type": "hardlink", "sha256": "1bb98a925f44686e22018f3262f508d2f5b013a2bbedd216e3fe4bad0680c6cb", "sha256_in_prefix": "1bb98a925f44686e22018f3262f508d2f5b013a2bbedd216e3fe4bad0680c6cb", "size_in_bytes": 29552}, {"_path": "lib/libgmpxx.dylib", "path_type": "softlink", "sha256": "1bb98a925f44686e22018f3262f508d2f5b013a2bbedd216e3fe4bad0680c6cb", "size_in_bytes": 29552}, {"_path": "lib/pkgconfig/gmp.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/gmp_1718980472988/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_place", "sha256": "55cb7f309fa3f8e1011436b86c1e4ec040834286d9a86cea843875fe42b23237", "sha256_in_prefix": "1f8516860cdda461549372134113de379bb52cca8afa8a86a8cae68f31f069d8", "size_in_bytes": 496}, {"_path": "lib/pkgconfig/gmpxx.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/gmp_1718980472988/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_place", "sha256": "b10f9af5e37387ac31497f26f5d468015ccb912a90a623c2bb662838e7ec73a6", "sha256_in_prefix": "0d0704d82678878a353c7e43a9b1ea1494829caca453be1190ddde7dbe2c026f", "size_in_bytes": 531}, {"_path": "share/info/gmp.info", "path_type": "hardlink", "sha256": "fa17f953f39ccd59a529dc343ab450825ea858d63116039c60223042072721b8", "sha256_in_prefix": "fa17f953f39ccd59a529dc343ab450825ea858d63116039c60223042072721b8", "size_in_bytes": 5932}, {"_path": "share/info/gmp.info-1", "path_type": "hardlink", "sha256": "ed759b294f9da8a59146b308774484c59203fc6f20f14a4235644b370e434d63", "sha256_in_prefix": "ed759b294f9da8a59146b308774484c59203fc6f20f14a4235644b370e434d63", "size_in_bytes": 300383}, {"_path": "share/info/gmp.info-2", "path_type": "hardlink", "sha256": "f4839a2f350914b08877a2cea3d9a9486a2832ed1ad98bfea08e8d8d2d25944f", "sha256_in_prefix": "f4839a2f350914b08877a2cea3d9a9486a2832ed1ad98bfea08e8d8d2d25944f", "size_in_bytes": 223745}], "paths_version": 1}, "requested_spec": "None", "sha256": "75aa5e7a875afdcf4903b7dc98577672a3dc17b528ac217b915f9528f93c85fc", "size": 428919, "subdir": "osx-64", "timestamp": 1718981041000, "url": "https://conda.anaconda.org/conda-forge/osx-64/gmp-6.3.0-hf036a51_2.conda", "version": "6.3.0"}