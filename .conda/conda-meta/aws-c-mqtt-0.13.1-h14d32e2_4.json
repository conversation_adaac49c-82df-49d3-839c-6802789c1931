{"build": "h14d32e2_4", "build_number": 4, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13", "aws-c-io >=0.21.0,<0.21.1.0a0", "aws-c-http >=0.10.2,<0.10.3.0a0", "aws-c-common >=0.12.4,<0.12.5.0a0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/aws-c-mqtt-0.13.1-h14d32e2_4", "files": ["bin/elastipubsub", "bin/elastipubsub5", "bin/elastishadow", "bin/mqtt5canary", "include/aws/mqtt/client.h", "include/aws/mqtt/exports.h", "include/aws/mqtt/mqtt.h", "include/aws/mqtt/private/mqtt_client_test_helper.h", "include/aws/mqtt/request-response/request_response_client.h", "include/aws/mqtt/v5/mqtt5_client.h", "include/aws/mqtt/v5/mqtt5_listener.h", "include/aws/mqtt/v5/mqtt5_packet_storage.h", "include/aws/mqtt/v5/mqtt5_types.h", "lib/cmake/aws-c-mqtt/aws-c-mqtt-config.cmake", "lib/cmake/aws-c-mqtt/shared/aws-c-mqtt-targets-release.cmake", "lib/cmake/aws-c-mqtt/shared/aws-c-mqtt-targets.cmake", "lib/libaws-c-mqtt.1.0.0.dylib", "lib/libaws-c-mqtt.dylib"], "fn": "aws-c-mqtt-0.13.1-h14d32e2_4.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/aws-c-mqtt-0.13.1-h14d32e2_4", "type": 1}, "md5": "b1ef97bb6349eb3a19c4ee07691bb20a", "name": "aws-c-mqtt", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/aws-c-mqtt-0.13.1-h14d32e2_4.conda", "paths_data": {"paths": [{"_path": "bin/elastipubsub", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/bld/rattler-build_aws-c-mqtt_1752261700/host_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_p", "sha256": "124c2a6cb38af81be57f7c6122bae07027ea6dde9db15a8df85ee2f67a7a7fc7", "sha256_in_prefix": "33c07dd4c7134c0730a315edd990e239204a2b81a40ebe044a7a5e1df4d09fbe", "size_in_bytes": 32352}, {"_path": "bin/elastipubsub5", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/bld/rattler-build_aws-c-mqtt_1752261700/host_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_p", "sha256": "8582cc9aeb31be134cbb56a7db3f967168f1b84d3f46b0a84b69a8f37ca3d424", "sha256_in_prefix": "7a48b7ba368c2943cd28980b4750350f1bdffafb80e9b4b589aa86eb1956c091", "size_in_bytes": 32608}, {"_path": "bin/elastishadow", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/bld/rattler-build_aws-c-mqtt_1752261700/host_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_p", "sha256": "fc37b7dd1299ae025c1d0ee926735c1ce2a9a97b2c9357f6216057e16d53a83b", "sha256_in_prefix": "ab7916072b6bc6af1e5ca781335ad1a2231431869fabb579ed1078709bb6af3e", "size_in_bytes": 42432}, {"_path": "bin/mqtt5canary", "path_type": "hardlink", "sha256": "9cde89fdc19cee85f7316aa668b2e3bcca2d229736a5b1bad6360da610decb3d", "sha256_in_prefix": "9cde89fdc19cee85f7316aa668b2e3bcca2d229736a5b1bad6360da610decb3d", "size_in_bytes": 36256}, {"_path": "include/aws/mqtt/client.h", "path_type": "hardlink", "sha256": "9af742e9208b9216b52db65d71ee2e47d6be5918cad575b5752db6a3cd44d034", "sha256_in_prefix": "9af742e9208b9216b52db65d71ee2e47d6be5918cad575b5752db6a3cd44d034", "size_in_bytes": 28679}, {"_path": "include/aws/mqtt/exports.h", "path_type": "hardlink", "sha256": "5c4f7674789db42728a365bac7f5366de87cf81c4c2e2e2da6492d807530a556", "sha256_in_prefix": "5c4f7674789db42728a365bac7f5366de87cf81c4c2e2e2da6492d807530a556", "size_in_bytes": 923}, {"_path": "include/aws/mqtt/mqtt.h", "path_type": "hardlink", "sha256": "8416ad299404e392f6d04401756ac6ea853437053d229b5ca57fe9557b386a8b", "sha256_in_prefix": "8416ad299404e392f6d04401756ac6ea853437053d229b5ca57fe9557b386a8b", "size_in_bytes": 5090}, {"_path": "include/aws/mqtt/private/mqtt_client_test_helper.h", "path_type": "hardlink", "sha256": "5f446cf0474ff5f290c43185dbf5fa558b0169ffe68af3373b37073710d388c1", "sha256_in_prefix": "5f446cf0474ff5f290c43185dbf5fa558b0169ffe68af3373b37073710d388c1", "size_in_bytes": 1090}, {"_path": "include/aws/mqtt/request-response/request_response_client.h", "path_type": "hardlink", "sha256": "412c5ce0a17910e013281bb89c260f6decca4b42ae0f08126dac5a8302f125ae", "sha256_in_prefix": "412c5ce0a17910e013281bb89c260f6decca4b42ae0f08126dac5a8302f125ae", "size_in_bytes": 9909}, {"_path": "include/aws/mqtt/v5/mqtt5_client.h", "path_type": "hardlink", "sha256": "17b1beeaede0a7396f875845608ef293233c84f279ff776c223de148ea95f8b1", "sha256_in_prefix": "17b1beeaede0a7396f875845608ef293233c84f279ff776c223de148ea95f8b1", "size_in_bytes": 28528}, {"_path": "include/aws/mqtt/v5/mqtt5_listener.h", "path_type": "hardlink", "sha256": "db1ae186806ea891a26b7d96d8a3d48efdeae392e602ed4cbc40f3a6aefc9c62", "sha256_in_prefix": "db1ae186806ea891a26b7d96d8a3d48efdeae392e602ed4cbc40f3a6aefc9c62", "size_in_bytes": 2674}, {"_path": "include/aws/mqtt/v5/mqtt5_packet_storage.h", "path_type": "hardlink", "sha256": "70955ec61379a886ec7cd59d224131aa3525024d59f6a66dda088b19f1ceb4b5", "sha256_in_prefix": "70955ec61379a886ec7cd59d224131aa3525024d59f6a66dda088b19f1ceb4b5", "size_in_bytes": 9797}, {"_path": "include/aws/mqtt/v5/mqtt5_types.h", "path_type": "hardlink", "sha256": "e8e932fb3e61865bd01d128c081aab89113821dd9da0f82cfc9c9a0cde8c97cc", "sha256_in_prefix": "e8e932fb3e61865bd01d128c081aab89113821dd9da0f82cfc9c9a0cde8c97cc", "size_in_bytes": 15717}, {"_path": "lib/cmake/aws-c-mqtt/aws-c-mqtt-config.cmake", "path_type": "hardlink", "sha256": "f23b325f9f814f2cc23fe568f27e0564233f116ca405dde965152f1436812b69", "sha256_in_prefix": "f23b325f9f814f2cc23fe568f27e0564233f116ca405dde965152f1436812b69", "size_in_bytes": 564}, {"_path": "lib/cmake/aws-c-mqtt/shared/aws-c-mqtt-targets-release.cmake", "path_type": "hardlink", "sha256": "7fcc63035290fa1758e7fd505fdbe5443505fbceaaeda73f2e325a6459be9e17", "sha256_in_prefix": "7fcc63035290fa1758e7fd505fdbe5443505fbceaaeda73f2e325a6459be9e17", "size_in_bytes": 887}, {"_path": "lib/cmake/aws-c-mqtt/shared/aws-c-mqtt-targets.cmake", "path_type": "hardlink", "sha256": "592f3acc9bc720926aa77441c2deb8372c36be8d7e3ecb14ed80b78a5a47960d", "sha256_in_prefix": "592f3acc9bc720926aa77441c2deb8372c36be8d7e3ecb14ed80b78a5a47960d", "size_in_bytes": 4299}, {"_path": "lib/libaws-c-mqtt.1.0.0.dylib", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/bld/rattler-build_aws-c-mqtt_1752261700/host_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_p", "sha256": "2b290b88b014c4a20471b5dcc1d957a0393ad12fc608cc93b48e29219486a2ff", "sha256_in_prefix": "2282a78584cbc0480006416b2fddd917456fbe9585e888b130ed68da2831691a", "size_in_bytes": 351712}, {"_path": "lib/libaws-c-mqtt.dylib", "path_type": "softlink", "sha256": "2b290b88b014c4a20471b5dcc1d957a0393ad12fc608cc93b48e29219486a2ff", "size_in_bytes": 25}], "paths_version": 1}, "requested_spec": "None", "sha256": "ddfbf8bf3d0beb2d19e5b86b21a49e8121771455ced91a384c34703f3f01ba67", "size": 187656, "subdir": "osx-64", "timestamp": 1752261700000, "url": "https://conda.anaconda.org/conda-forge/osx-64/aws-c-mqtt-0.13.1-h14d32e2_4.conda", "version": "0.13.1"}