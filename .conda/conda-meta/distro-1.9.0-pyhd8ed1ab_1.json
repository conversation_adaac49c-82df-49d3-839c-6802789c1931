{"build": "pyhd8ed1ab_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.9"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/distro-1.9.0-pyhd8ed1ab_1", "files": ["lib/python3.11/site-packages/distro-1.9.0.dist-info/INSTALLER", "lib/python3.11/site-packages/distro-1.9.0.dist-info/LICENSE", "lib/python3.11/site-packages/distro-1.9.0.dist-info/METADATA", "lib/python3.11/site-packages/distro-1.9.0.dist-info/RECORD", "lib/python3.11/site-packages/distro-1.9.0.dist-info/REQUESTED", "lib/python3.11/site-packages/distro-1.9.0.dist-info/WHEEL", "lib/python3.11/site-packages/distro-1.9.0.dist-info/direct_url.json", "lib/python3.11/site-packages/distro-1.9.0.dist-info/entry_points.txt", "lib/python3.11/site-packages/distro-1.9.0.dist-info/top_level.txt", "lib/python3.11/site-packages/distro/__init__.py", "lib/python3.11/site-packages/distro/__main__.py", "lib/python3.11/site-packages/distro/distro.py", "lib/python3.11/site-packages/distro/py.typed", "lib/python3.11/site-packages/distro/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/distro/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/distro/__pycache__/distro.cpython-311.pyc", "bin/distro"], "fn": "distro-1.9.0-pyhd8ed1ab_1.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/distro-1.9.0-pyhd8ed1ab_1", "type": 1}, "md5": "0a2014fd9860f8b1eaa0b1f3d3771a08", "name": "distro", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/distro-1.9.0-pyhd8ed1ab_1.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/distro-1.9.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/distro-1.9.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "cb5e8e7e5f4a3988e1063c142c60dc2df75605f4c46515e776e3aca6df976e14", "sha256_in_prefix": "cb5e8e7e5f4a3988e1063c142c60dc2df75605f4c46515e776e3aca6df976e14", "size_in_bytes": 11325}, {"_path": "site-packages/distro-1.9.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "31632ab2de5591131091b3397bdcdf797718579d85a75186f06839dd0c09e81d", "sha256_in_prefix": "31632ab2de5591131091b3397bdcdf797718579d85a75186f06839dd0c09e81d", "size_in_bytes": 6791}, {"_path": "site-packages/distro-1.9.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "3f22e5ca63ba68cb10a7bbf783fb1f61002d752148f34b398c32b58bf13ac1e0", "sha256_in_prefix": "3f22e5ca63ba68cb10a7bbf783fb1f61002d752148f34b398c32b58bf13ac1e0", "size_in_bytes": 1256}, {"_path": "site-packages/distro-1.9.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/distro-1.9.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "3d9504c5d7fbd548bfb28ebb417a724ae1ed0a2dfe277c2f1783912ba93f4bc5", "sha256_in_prefix": "3d9504c5d7fbd548bfb28ebb417a724ae1ed0a2dfe277c2f1783912ba93f4bc5", "size_in_bytes": 91}, {"_path": "site-packages/distro-1.9.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "2270cacd972c743fd4c0fdd62ce1d7fe249faa3b98ce2bb38975238d73af392b", "sha256_in_prefix": "2270cacd972c743fd4c0fdd62ce1d7fe249faa3b98ce2bb38975238d73af392b", "size_in_bytes": 102}, {"_path": "site-packages/distro-1.9.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "dce6e3a9031b875c5e410c2c5ad81b7c33433030fe11b8204753a3ff3f2cf617", "sha256_in_prefix": "dce6e3a9031b875c5e410c2c5ad81b7c33433030fe11b8204753a3ff3f2cf617", "size_in_bytes": 46}, {"_path": "site-packages/distro-1.9.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "8a475efd5fd711d481a9a19de6d12b88dff0cd81cb8135ffcd5b65b062c7bf04", "sha256_in_prefix": "8a475efd5fd711d481a9a19de6d12b88dff0cd81cb8135ffcd5b65b062c7bf04", "size_in_bytes": 7}, {"_path": "site-packages/distro/__init__.py", "path_type": "hardlink", "sha256": "d9f1e317e49f80fbe3c8d67588787fc23a96751fd8a393831f0642d232c13e17", "sha256_in_prefix": "d9f1e317e49f80fbe3c8d67588787fc23a96751fd8a393831f0642d232c13e17", "size_in_bytes": 981}, {"_path": "site-packages/distro/__main__.py", "path_type": "hardlink", "sha256": "6eef5ddd389fa0a72264572a441bb2815dc64ae4e19d50ff9b620ae1ccfde95b", "sha256_in_prefix": "6eef5ddd389fa0a72264572a441bb2815dc64ae4e19d50ff9b620ae1ccfde95b", "size_in_bytes": 64}, {"_path": "site-packages/distro/distro.py", "path_type": "hardlink", "sha256": "5ea6de7da7008434f8cebfedae76c0d79798f2f74ae064e08609af506ac433fe", "sha256_in_prefix": "5ea6de7da7008434f8cebfedae76c0d79798f2f74ae064e08609af506ac433fe", "size_in_bytes": 49430}, {"_path": "site-packages/distro/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/distro/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/distro/__pycache__/__main__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/distro/__pycache__/distro.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "bin/distro", "path_type": "unix_python_entry_point"}], "paths_version": 1}, "requested_spec": "None", "sha256": "5603c7d0321963bb9b4030eadabc3fd7ca6103a38475b4e0ed13ed6d97c86f4e", "size": 41773, "subdir": "noarch", "timestamp": 1734729953000, "url": "https://conda.anaconda.org/conda-forge/noarch/distro-1.9.0-pyhd8ed1ab_1.conda", "version": "1.9.0"}