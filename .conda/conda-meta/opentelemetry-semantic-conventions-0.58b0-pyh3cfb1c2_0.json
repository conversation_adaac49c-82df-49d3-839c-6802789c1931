{"build": "pyh3cfb1c2_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["deprecated >=1.2.6", "opentelemetry-api 1.37.0", "python >=3.10", "typing_extensions >=4.5.0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-semantic-conventions-0.58b0-pyh3cfb1c2_0", "files": ["lib/python3.11/site-packages/opentelemetry/semconv/__init__.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/app_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/artifact_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/aws_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/az_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/azure_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/browser_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/cassandra_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/cicd_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/client_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/cloud_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/cloudevents_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/cloudfoundry_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/code_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/container_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/cpu_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/cpython_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/db_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/deployment_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/destination_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/device_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/disk_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/dns_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/elasticsearch_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/enduser_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/error_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/event_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/exception_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/faas_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/feature_flag_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/file_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/gcp_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/gen_ai_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/geo_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/graphql_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/heroku_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/host_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/http_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/hw_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/k8s_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/linux_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/log_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/mainframe_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/message_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/messaging_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/net_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/network_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/oci_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/openai_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/opentracing_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/os_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/otel_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/other_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/peer_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/pool_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/process_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/profile_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/rpc_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/security_rule_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/server_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/service_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/session_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/source_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/system_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/telemetry_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/test_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/thread_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/tls_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/url_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/user_agent_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/user_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/vcs_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/webengine_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/zos_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/azure_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/cicd_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/container_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/cpu_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/cpython_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/db_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/dns_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/faas_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/gen_ai_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/http_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/hw_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/k8s_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/messaging_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/otel_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/process_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/rpc_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/system_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/vcs_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__init__.py", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/client_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/code_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/db_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/error_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/exception_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/http_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/network_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/otel_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/server_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/service_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/telemetry_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/url_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/user_agent_attributes.py", "lib/python3.11/site-packages/opentelemetry/semconv/metrics/__init__.py", "lib/python3.11/site-packages/opentelemetry/semconv/metrics/db_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/metrics/http_metrics.py", "lib/python3.11/site-packages/opentelemetry/semconv/py.typed", "lib/python3.11/site-packages/opentelemetry/semconv/resource/__init__.py", "lib/python3.11/site-packages/opentelemetry/semconv/schemas.py", "lib/python3.11/site-packages/opentelemetry/semconv/trace/__init__.py", "lib/python3.11/site-packages/opentelemetry/semconv/version/__init__.py", "lib/python3.11/site-packages/opentelemetry_semantic_conventions-0.58b0.dist-info/INSTALLER", "lib/python3.11/site-packages/opentelemetry_semantic_conventions-0.58b0.dist-info/METADATA", "lib/python3.11/site-packages/opentelemetry_semantic_conventions-0.58b0.dist-info/RECORD", "lib/python3.11/site-packages/opentelemetry_semantic_conventions-0.58b0.dist-info/REQUESTED", "lib/python3.11/site-packages/opentelemetry_semantic_conventions-0.58b0.dist-info/WHEEL", "lib/python3.11/site-packages/opentelemetry_semantic_conventions-0.58b0.dist-info/direct_url.json", "lib/python3.11/site-packages/opentelemetry_semantic_conventions-0.58b0.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/opentelemetry/semconv/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/app_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/artifact_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/aws_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/az_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/azure_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/browser_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/cassandra_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/cicd_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/client_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/cloud_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/cloudevents_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/cloudfoundry_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/code_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/container_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/cpu_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/cpython_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/db_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/deployment_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/destination_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/device_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/disk_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/dns_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/elasticsearch_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/enduser_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/error_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/event_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/exception_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/faas_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/feature_flag_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/file_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/gcp_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/gen_ai_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/geo_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/graphql_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/heroku_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/host_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/http_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/hw_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/k8s_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/linux_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/log_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/mainframe_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/message_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/messaging_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/net_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/network_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/oci_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/openai_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/opentracing_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/os_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/otel_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/other_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/peer_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/pool_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/process_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/profile_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/rpc_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/security_rule_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/server_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/service_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/session_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/source_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/system_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/telemetry_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/test_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/thread_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/tls_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/url_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/user_agent_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/user_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/vcs_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/webengine_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/zos_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/azure_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/cicd_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/container_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/cpu_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/cpython_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/db_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/dns_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/faas_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/gen_ai_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/http_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/hw_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/k8s_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/messaging_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/otel_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/process_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/rpc_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/system_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/vcs_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/client_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/code_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/db_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/error_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/exception_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/http_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/network_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/otel_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/server_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/service_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/telemetry_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/url_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/user_agent_attributes.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/metrics/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/metrics/__pycache__/db_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/metrics/__pycache__/http_metrics.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/resource/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/__pycache__/schemas.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/trace/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/semconv/version/__pycache__/__init__.cpython-311.pyc"], "fn": "opentelemetry-semantic-conventions-0.58b0-pyh3cfb1c2_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-semantic-conventions-0.58b0-pyh3cfb1c2_0", "type": 1}, "md5": "141e144f61631fffb7528326507fbb71", "name": "opentelemetry-semantic-conventions", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-semantic-conventions-0.58b0-pyh3cfb1c2_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/opentelemetry/semconv/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/app_attributes.py", "path_type": "hardlink", "sha256": "72e7f56221a81a4c593ee905c2db6263f53a9f05eb43fe11f281ca0461268f99", "sha256_in_prefix": "72e7f56221a81a4c593ee905c2db6263f53a9f05eb43fe11f281ca0461268f99", "size_in_bytes": 3471}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/artifact_attributes.py", "path_type": "hardlink", "sha256": "60cb5de63dc16c75c43132c6d5460356b17ea702d55b33a2a8b0d219f92efa94", "sha256_in_prefix": "60cb5de63dc16c75c43132c6d5460356b17ea702d55b33a2a8b0d219f92efa94", "size_in_bytes": 3108}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/aws_attributes.py", "path_type": "hardlink", "sha256": "96d167acae1c0788dc25737bf98902ae7c5793a6d49815eefcbea0ab3def71ba", "sha256_in_prefix": "96d167acae1c0788dc25737bf98902ae7c5793a6d49815eefcbea0ab3def71ba", "size_in_bytes": 14144}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/az_attributes.py", "path_type": "hardlink", "sha256": "cd2141657935952bab85aa85f85024dbc7a55c00e609fe6df9bbcee6c3b505cd", "sha256_in_prefix": "cd2141657935952bab85aa85f85024dbc7a55c00e609fe6df9bbcee6c3b505cd", "size_in_bytes": 833}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/azure_attributes.py", "path_type": "hardlink", "sha256": "385354f75d17005ed7786b29b9621d4dd8869531bedc24ee1ba8f98ae4d55c51", "sha256_in_prefix": "385354f75d17005ed7786b29b9621d4dd8869531bedc24ee1ba8f98ae4d55c51", "size_in_bytes": 2914}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/browser_attributes.py", "path_type": "hardlink", "sha256": "1ce91da459092d1536f74d2de5bed43fbd66b86d291f8247e46f16d2eb701297", "sha256_in_prefix": "1ce91da459092d1536f74d2de5bed43fbd66b86d291f8247e46f16d2eb701297", "size_in_bytes": 2224}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/cassandra_attributes.py", "path_type": "hardlink", "sha256": "980885242c79913f449fdbb27642fd1ddc7e9b5479c6a55108055502297e1c8d", "sha256_in_prefix": "980885242c79913f449fdbb27642fd1ddc7e9b5479c6a55108055502297e1c8d", "size_in_bytes": 2090}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/cicd_attributes.py", "path_type": "hardlink", "sha256": "6022c59d0c688cbb15491f5866dc98f2cbba179545d250ea0fd1a6ccfa0c2d4d", "sha256_in_prefix": "6022c59d0c688cbb15491f5866dc98f2cbba179545d250ea0fd1a6ccfa0c2d4d", "size_in_bytes": 5746}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/client_attributes.py", "path_type": "hardlink", "sha256": "39003cbcd0d385442f6271b3223d6da1fd6c60fe8a34e863653e722d3904d1c1", "sha256_in_prefix": "39003cbcd0d385442f6271b3223d6da1fd6c60fe8a34e863653e722d3904d1c1", "size_in_bytes": 919}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/cloud_attributes.py", "path_type": "hardlink", "sha256": "d7a77f27f4707cd52f30e2ee0ec4f32a3ab9b28b455af9fbc65f67b9d7d019a8", "sha256_in_prefix": "d7a77f27f4707cd52f30e2ee0ec4f32a3ab9b28b455af9fbc65f67b9d7d019a8", "size_in_bytes": 6777}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/cloudevents_attributes.py", "path_type": "hardlink", "sha256": "3a8f9c3044dc513d7c84c7c91c5f72b404f38000dd5870bce11437735e6ff773", "sha256_in_prefix": "3a8f9c3044dc513d7c84c7c91c5f72b404f38000dd5870bce11437735e6ff773", "size_in_bytes": 1713}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/cloudfoundry_attributes.py", "path_type": "hardlink", "sha256": "24d38844338ff0470657ec9221d28369f80cc16069c41f6b5c0474781d9c230b", "sha256_in_prefix": "24d38844338ff0470657ec9221d28369f80cc16069c41f6b5c0474781d9c230b", "size_in_bytes": 4718}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/code_attributes.py", "path_type": "hardlink", "sha256": "1adb0cabfe5e1784dc96aa28164c81e4f508dc07e91d9da7517a86769f623e7b", "sha256_in_prefix": "1adb0cabfe5e1784dc96aa28164c81e4f508dc07e91d9da7517a86769f623e7b", "size_in_bytes": 2012}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/container_attributes.py", "path_type": "hardlink", "sha256": "0999614972845fcdb2dfe8a7521db3551b8fd7a017fc431f9ac4d52344fa4583", "sha256_in_prefix": "0999614972845fcdb2dfe8a7521db3551b8fd7a017fc431f9ac4d52344fa4583", "size_in_bytes": 5463}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/cpu_attributes.py", "path_type": "hardlink", "sha256": "656c5ca0f28dfe35fc86a270cf18d71553fca216e0d2adbf3590362c2928bf05", "sha256_in_prefix": "656c5ca0f28dfe35fc86a270cf18d71553fca216e0d2adbf3590362c2928bf05", "size_in_bytes": 1120}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/cpython_attributes.py", "path_type": "hardlink", "sha256": "5b274b1b285ac9d68ae3c78c0242576b1f84fadf14e0b5415554696346a33d86", "sha256_in_prefix": "5b274b1b285ac9d68ae3c78c0242576b1f84fadf14e0b5415554696346a33d86", "size_in_bytes": 926}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/db_attributes.py", "path_type": "hardlink", "sha256": "e8d83d85c5516250f2696dc1e9fa271aac2f14cc51a4e69e3a7dad5fbdca55f0", "sha256_in_prefix": "e8d83d85c5516250f2696dc1e9fa271aac2f14cc51a4e69e3a7dad5fbdca55f0", "size_in_bytes": 17857}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/deployment_attributes.py", "path_type": "hardlink", "sha256": "4643c844fa426a69612b612f5fea6ae3b6016e3fb5238d2083f6823ebe0d4b21", "sha256_in_prefix": "4643c844fa426a69612b612f5fea6ae3b6016e3fb5238d2083f6823ebe0d4b21", "size_in_bytes": 1753}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/destination_attributes.py", "path_type": "hardlink", "sha256": "87238f3455d0d57be874a9022d34abd0a861033454f2db340d37f86df5fbfd78", "sha256_in_prefix": "87238f3455d0d57be874a9022d34abd0a861033454f2db340d37f86df5fbfd78", "size_in_bytes": 1094}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/device_attributes.py", "path_type": "hardlink", "sha256": "048111a5485bb740f67c03ec6de138e312e3a00344fa92ee356d7e3c971b9bd1", "sha256_in_prefix": "048111a5485bb740f67c03ec6de138e312e3a00344fa92ee356d7e3c971b9bd1", "size_in_bytes": 2704}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/disk_attributes.py", "path_type": "hardlink", "sha256": "7fece1e7b67bcb45c9a95b6ec6c37aaf5b7f1606c877c71ea6cf305a0d255cf8", "sha256_in_prefix": "7fece1e7b67bcb45c9a95b6ec6c37aaf5b7f1606c877c71ea6cf305a0d255cf8", "size_in_bytes": 829}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/dns_attributes.py", "path_type": "hardlink", "sha256": "b1b7bd8f417be3b1854a62c9aaf91e1645208f86aff365f10f1f287c46c51990", "sha256_in_prefix": "b1b7bd8f417be3b1854a62c9aaf91e1645208f86aff365f10f1f287c46c51990", "size_in_bytes": 1093}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/elasticsearch_attributes.py", "path_type": "hardlink", "sha256": "2fa85f9b2847b6a6e1dd790722308b890a85477c7720a85540eb4a73c442a3d0", "sha256_in_prefix": "2fa85f9b2847b6a6e1dd790722308b890a85477c7720a85540eb4a73c442a3d0", "size_in_bytes": 771}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/enduser_attributes.py", "path_type": "hardlink", "sha256": "8c9f471d36c4b70a02aff5a12ed6bf609942fcbc603275fa54528661e7f35158", "sha256_in_prefix": "8c9f471d36c4b70a02aff5a12ed6bf609942fcbc603275fa54528661e7f35158", "size_in_bytes": 1394}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/error_attributes.py", "path_type": "hardlink", "sha256": "e1e9f013f58da6a1c71b83d33e0074f9a0177902dd71165847968b308419c85c", "sha256_in_prefix": "e1e9f013f58da6a1c71b83d33e0074f9a0177902dd71165847968b308419c85c", "size_in_bytes": 1623}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/event_attributes.py", "path_type": "hardlink", "sha256": "899f4ca2de57df9c93a24e19d646f3134f27c32f498fb613394c6098d8badbe9", "sha256_in_prefix": "899f4ca2de57df9c93a24e19d646f3134f27c32f498fb613394c6098d8badbe9", "size_in_bytes": 720}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/exception_attributes.py", "path_type": "hardlink", "sha256": "25f70147087433d9c91ff9f3e36a93b8e8b7dee7067821b0a4b2c523e9a7472a", "sha256_in_prefix": "25f70147087433d9c91ff9f3e36a93b8e8b7dee7067821b0a4b2c523e9a7472a", "size_in_bytes": 1295}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/faas_attributes.py", "path_type": "hardlink", "sha256": "955feb4a953b4f1b395554fce825672adc7d718b5fceca053f8daaf8e8d0be5a", "sha256_in_prefix": "955feb4a953b4f1b395554fce825672adc7d718b5fceca053f8daaf8e8d0be5a", "size_in_bytes": 6202}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/feature_flag_attributes.py", "path_type": "hardlink", "sha256": "50f6103d78bf6af7e25cb24c439435c81fd033210de6a142a3e88f4dca3af4c6", "sha256_in_prefix": "50f6103d78bf6af7e25cb24c439435c81fd033210de6a142a3e88f4dca3af4c6", "size_in_bytes": 5094}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/file_attributes.py", "path_type": "hardlink", "sha256": "e0ab8c353244f0f4179bcf8efa260046fa0533654fd0165058acb8f1ed47c11f", "sha256_in_prefix": "e0ab8c353244f0f4179bcf8efa260046fa0533654fd0165058acb8f1ed47c11f", "size_in_bytes": 4044}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/gcp_attributes.py", "path_type": "hardlink", "sha256": "fd60ca83d54cca23028847fa9f3cf16cd0aa691f80cbcd92eaa713a359844eb7", "sha256_in_prefix": "fd60ca83d54cca23028847fa9f3cf16cd0aa691f80cbcd92eaa713a359844eb7", "size_in_bytes": 5082}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/gen_ai_attributes.py", "path_type": "hardlink", "sha256": "b90313e8cab8b36757fe01ab18534e708f0687a613ab9758a1495bf965caad7a", "sha256_in_prefix": "b90313e8cab8b36757fe01ab18534e708f0687a613ab9758a1495bf965caad7a", "size_in_bytes": 16112}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/geo_attributes.py", "path_type": "hardlink", "sha256": "12d517af6fb273db27fa5912336e7cc6ddf96446fd594e6b2d78de923b2164ee", "sha256_in_prefix": "12d517af6fb273db27fa5912336e7cc6ddf96446fd594e6b2d78de923b2164ee", "size_in_bytes": 1973}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/graphql_attributes.py", "path_type": "hardlink", "sha256": "fcdd81ecb063bfed0db8163de36c7bf68e3e32c084a750021ce4c7d3b7845d64", "sha256_in_prefix": "fcdd81ecb063bfed0db8163de36c7bf68e3e32c084a750021ce4c7d3b7845d64", "size_in_bytes": 1213}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/heroku_attributes.py", "path_type": "hardlink", "sha256": "2e439121f46dfd4137cde7eb960f7c6468e47719a060a06672f9d245f85870f2", "sha256_in_prefix": "2e439121f46dfd4137cde7eb960f7c6468e47719a060a06672f9d245f85870f2", "size_in_bytes": 925}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/host_attributes.py", "path_type": "hardlink", "sha256": "9cfebc97950d6a19b56e37bfadc98f6519011276a03a9b109ca4475e07f9bad9", "sha256_in_prefix": "9cfebc97950d6a19b56e37bfadc98f6519011276a03a9b109ca4475e07f9bad9", "size_in_bytes": 3609}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/http_attributes.py", "path_type": "hardlink", "sha256": "3cb0698eba29d7540cc80062b17dd6a699e44e5b868ad7eb70f5ef6200d0a615", "sha256_in_prefix": "3cb0698eba29d7540cc80062b17dd6a699e44e5b868ad7eb70f5ef6200d0a615", "size_in_bytes": 7164}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/hw_attributes.py", "path_type": "hardlink", "sha256": "73b5e454fb4f07ea54cbb4672a2e19ac1aeea13b7ed16727cb76e6f19c113549", "sha256_in_prefix": "73b5e454fb4f07ea54cbb4672a2e19ac1aeea13b7ed16727cb76e6f19c113549", "size_in_bytes": 5883}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/k8s_attributes.py", "path_type": "hardlink", "sha256": "0c65f107780cca6d624ec8048de738da08243a6e3aa57fadd9b1a36590bd59be", "sha256_in_prefix": "0c65f107780cca6d624ec8048de738da08243a6e3aa57fadd9b1a36590bd59be", "size_in_bytes": 20531}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/linux_attributes.py", "path_type": "hardlink", "sha256": "20007567794819a30bcb331e473b0d7b6cb22e63b38b5727feff26f8c5cccf1a", "sha256_in_prefix": "20007567794819a30bcb331e473b0d7b6cb22e63b38b5727feff26f8c5cccf1a", "size_in_bytes": 887}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/log_attributes.py", "path_type": "hardlink", "sha256": "24481e2176a2cc8622d0acdf374c13210db67f3ee1f0f95258b76c6a0b91e028", "sha256_in_prefix": "24481e2176a2cc8622d0acdf374c13210db67f3ee1f0f95258b76c6a0b91e028", "size_in_bytes": 2089}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/mainframe_attributes.py", "path_type": "hardlink", "sha256": "40e01c708bf5f9f4eda57b2edc50b291bed836184b6dfa316c4e60b5163b6915", "sha256_in_prefix": "40e01c708bf5f9f4eda57b2edc50b291bed836184b6dfa316c4e60b5163b6915", "size_in_bytes": 756}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/message_attributes.py", "path_type": "hardlink", "sha256": "6f1438b12a1030ffa624d4bda20a0eb42fae5e008354d667e5c032cbb6f4d25e", "sha256_in_prefix": "6f1438b12a1030ffa624d4bda20a0eb42fae5e008354d667e5c032cbb6f4d25e", "size_in_bytes": 1308}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/messaging_attributes.py", "path_type": "hardlink", "sha256": "1d5ba8a1d605f4c65057c2f2af2fbe6c1dcaf46ce34b1594ad675f8a21720429", "sha256_in_prefix": "1d5ba8a1d605f4c65057c2f2af2fbe6c1dcaf46ce34b1594ad675f8a21720429", "size_in_bytes": 12694}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/net_attributes.py", "path_type": "hardlink", "sha256": "510c0621e2f39593e4f98851d5979a7b3e4dba155174f424382995dd1c4e67bd", "sha256_in_prefix": "510c0621e2f39593e4f98851d5979a7b3e4dba155174f424382995dd1c4e67bd", "size_in_bytes": 2941}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/network_attributes.py", "path_type": "hardlink", "sha256": "db49546139473e885ab891976d171307186677768b2206e8ef50d507b8142922", "sha256_in_prefix": "db49546139473e885ab891976d171307186677768b2206e8ef50d507b8142922", "size_in_bytes": 6497}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/oci_attributes.py", "path_type": "hardlink", "sha256": "e547194784b6b86b9b9b5eb7a495e87e4e17abea86ebb540ab6cb404503c1b4c", "sha256_in_prefix": "e547194784b6b86b9b9b5eb7a495e87e4e17abea86ebb540ab6cb404503c1b4c", "size_in_bytes": 1175}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/openai_attributes.py", "path_type": "hardlink", "sha256": "c80446be52927b99fba7196078da03853f96f6542f85f4d46773f4c8ee04d31d", "sha256_in_prefix": "c80446be52927b99fba7196078da03853f96f6542f85f4d46773f4c8ee04d31d", "size_in_bytes": 1296}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/opentracing_attributes.py", "path_type": "hardlink", "sha256": "53dd45fc34c617277beefe6f8cd819f79b3ab51341d16849a3ac9c3bd90ac957", "sha256_in_prefix": "53dd45fc34c617277beefe6f8cd819f79b3ab51341d16849a3ac9c3bd90ac957", "size_in_bytes": 1048}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/os_attributes.py", "path_type": "hardlink", "sha256": "ecb3f6dcee5bb939649493996c8e25f079ba46cf41031968f6d0b9968257bc04", "sha256_in_prefix": "ecb3f6dcee5bb939649493996c8e25f079ba46cf41031968f6d0b9968257bc04", "size_in_bytes": 1858}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/otel_attributes.py", "path_type": "hardlink", "sha256": "7daeb477f4ee166d29f7146c30256c2e7d69c771edc704e472f22234c3c272a2", "sha256_in_prefix": "7daeb477f4ee166d29f7146c30256c2e7d69c771edc704e472f22234c3c272a2", "size_in_bytes": 6749}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/other_attributes.py", "path_type": "hardlink", "sha256": "c284a48c69d6dc2810f0a54c01229a04a6ca2754f98d6633037feecd95527761", "sha256_in_prefix": "c284a48c69d6dc2810f0a54c01229a04a6ca2754f98d6633037feecd95527761", "size_in_bytes": 953}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/peer_attributes.py", "path_type": "hardlink", "sha256": "0ea429dee0f3539807eb3cbc034a62b613b84f3b676c9a925b61092b4f27980c", "sha256_in_prefix": "0ea429dee0f3539807eb3cbc034a62b613b84f3b676c9a925b61092b4f27980c", "size_in_bytes": 828}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/pool_attributes.py", "path_type": "hardlink", "sha256": "a68eb7c1b195280a3b265e637b17b7780e5ec772d1611e686340fbfc0e5e2658", "sha256_in_prefix": "a68eb7c1b195280a3b265e637b17b7780e5ec772d1611e686340fbfc0e5e2658", "size_in_bytes": 708}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/process_attributes.py", "path_type": "hardlink", "sha256": "d6094ce17063c20cba917f8b82501c4f1cefb42a68e9684e905fbb129b844dfd", "sha256_in_prefix": "d6094ce17063c20cba917f8b82501c4f1cefb42a68e9684e905fbb129b844dfd", "size_in_bytes": 8083}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/profile_attributes.py", "path_type": "hardlink", "sha256": "bc8d99c4f3c24f7aef7f50260ce8e4f0f62add39b6048394b96d849203706b24", "sha256_in_prefix": "bc8d99c4f3c24f7aef7f50260ce8e4f0f62add39b6048394b96d849203706b24", "size_in_bytes": 2067}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/rpc_attributes.py", "path_type": "hardlink", "sha256": "8c7e9220a099210142ef439e8fa87154c6266594b3cac0c86590691becfe3a39", "sha256_in_prefix": "8c7e9220a099210142ef439e8fa87154c6266594b3cac0c86590691becfe3a39", "size_in_bytes": 8297}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/security_rule_attributes.py", "path_type": "hardlink", "sha256": "80f7e6ec0e769be26881963a4874cbb45a97bbe294153fbf955b9eba5fdbfa58", "sha256_in_prefix": "80f7e6ec0e769be26881963a4874cbb45a97bbe294153fbf955b9eba5fdbfa58", "size_in_bytes": 1999}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/server_attributes.py", "path_type": "hardlink", "sha256": "727f138e20d0f7f7d38307fdba6e2e3ae7c3f9e5d94607b0b1dfb2a771de7c89", "sha256_in_prefix": "727f138e20d0f7f7d38307fdba6e2e3ae7c3f9e5d94607b0b1dfb2a771de7c89", "size_in_bytes": 919}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/service_attributes.py", "path_type": "hardlink", "sha256": "4d3e1305a430e6d40f447a86fd506697fa3f2a4b08a7adfc267e4836785da470", "sha256_in_prefix": "4d3e1305a430e6d40f447a86fd506697fa3f2a4b08a7adfc267e4836785da470", "size_in_bytes": 3609}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/session_attributes.py", "path_type": "hardlink", "sha256": "c199146331023ae367dadf288c6ce6b655adf41f20987ed05a54e7ce770c4cce", "sha256_in_prefix": "c199146331023ae367dadf288c6ce6b655adf41f20987ed05a54e7ce770c4cce", "size_in_bytes": 800}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/source_attributes.py", "path_type": "hardlink", "sha256": "5bed1795f6a3487ab9d6e4c8819af0f6eebc6ffa8e55ea88659c0de4a078f0c7", "sha256_in_prefix": "5bed1795f6a3487ab9d6e4c8819af0f6eebc6ffa8e55ea88659c0de4a078f0c7", "size_in_bytes": 1059}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/system_attributes.py", "path_type": "hardlink", "sha256": "93dcb2a13e36908f004bf2d41132f1c147a448b6965f8e326fe759269d78862c", "sha256_in_prefix": "93dcb2a13e36908f004bf2d41132f1c147a448b6965f8e326fe759269d78862c", "size_in_bytes": 4814}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/telemetry_attributes.py", "path_type": "hardlink", "sha256": "b1726dda9bc05751e72ae741d24e138a24e6a7ba7c7bc0c5991702cb5aa84183", "sha256_in_prefix": "b1726dda9bc05751e72ae741d24e138a24e6a7ba7c7bc0c5991702cb5aa84183", "size_in_bytes": 3785}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/test_attributes.py", "path_type": "hardlink", "sha256": "d81f45304a72efff3f9d7ac6293318f8ec14791a079966fa8afa14040543f76d", "sha256_in_prefix": "d81f45304a72efff3f9d7ac6293318f8ec14791a079966fa8afa14040543f76d", "size_in_bytes": 1569}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/thread_attributes.py", "path_type": "hardlink", "sha256": "478e26a478c29576bfc93312f10983323b8d762ce854fb9e9b0d4a4f56f49489", "sha256_in_prefix": "478e26a478c29576bfc93312f10983323b8d762ce854fb9e9b0d4a4f56f49489", "size_in_bytes": 773}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/tls_attributes.py", "path_type": "hardlink", "sha256": "f3f04c692836a0e0300481cabfde45d7941486123b06f8f5e09c8981ae37495e", "sha256_in_prefix": "f3f04c692836a0e0300481cabfde45d7941486123b06f8f5e09c8981ae37495e", "size_in_bytes": 6656}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/url_attributes.py", "path_type": "hardlink", "sha256": "49d15a08eff956033e53d56babec52eee043f515177667cab30c5bb664b73715", "sha256_in_prefix": "49d15a08eff956033e53d56babec52eee043f515177667cab30c5bb664b73715", "size_in_bytes": 4180}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/user_agent_attributes.py", "path_type": "hardlink", "sha256": "679de5524eaec3a7c2a010f382b39fd6744c28f0094e54116adea8ba2b051ddb", "sha256_in_prefix": "679de5524eaec3a7c2a010f382b39fd6744c28f0094e54116adea8ba2b051ddb", "size_in_bytes": 3101}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/user_attributes.py", "path_type": "hardlink", "sha256": "77727097b68df355032d028765e3aa5575f63870ebb3901a1738d53d45aebb96", "sha256_in_prefix": "77727097b68df355032d028765e3aa5575f63870ebb3901a1738d53d45aebb96", "size_in_bytes": 1184}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/vcs_attributes.py", "path_type": "hardlink", "sha256": "0b5e07bc5d6f5dc2873d894e235646866bc4440c8ed621b61139c674a5344dc0", "sha256_in_prefix": "0b5e07bc5d6f5dc2873d894e235646866bc4440c8ed621b61139c674a5344dc0", "size_in_bytes": 9308}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/webengine_attributes.py", "path_type": "hardlink", "sha256": "b9df12666b6f20014adcf4ce04e88aaff1ec33657092a09c348319051294bedc", "sha256_in_prefix": "b9df12666b6f20014adcf4ce04e88aaff1ec33657092a09c348319051294bedc", "size_in_bytes": 929}, {"_path": "site-packages/opentelemetry/semconv/_incubating/attributes/zos_attributes.py", "path_type": "hardlink", "sha256": "766006f0db22d50c427914d34a89ee1571cf3b0c4ede1273a77553f314703ce7", "sha256_in_prefix": "766006f0db22d50c427914d34a89ee1571cf3b0c4ede1273a77553f314703ce7", "size_in_bytes": 941}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/azure_metrics.py", "path_type": "hardlink", "sha256": "7348dda5af2b97c35c85e93814b06c6682da5d9e43ea938fa7b7bdd768d7bfc5", "sha256_in_prefix": "7348dda5af2b97c35c85e93814b06c6682da5d9e43ea938fa7b7bdd768d7bfc5", "size_in_bytes": 1932}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/cicd_metrics.py", "path_type": "hardlink", "sha256": "cfa987b582edaf6d518fa9a97e551331ea6f6328cc517ea7278e88f51333d861", "sha256_in_prefix": "cfa987b582edaf6d518fa9a97e551331ea6f6328cc517ea7278e88f51333d861", "size_in_bytes": 3656}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/container_metrics.py", "path_type": "hardlink", "sha256": "e954c7db0be44433368b2c4d3c2eabe0c8332e22711ce0f58e1e04135e537de8", "sha256_in_prefix": "e954c7db0be44433368b2c4d3c2eabe0c8332e22711ce0f58e1e04135e537de8", "size_in_bytes": 6392}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/cpu_metrics.py", "path_type": "hardlink", "sha256": "af507faefca2e4289f9327502319b568b01c3ac7eb6bed1d977a8fe84711f278", "sha256_in_prefix": "af507faefca2e4289f9327502319b568b01c3ac7eb6bed1d977a8fe84711f278", "size_in_bytes": 2252}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/cpython_metrics.py", "path_type": "hardlink", "sha256": "bfe081202225eb43728d83307c8cfede9f54936a3fc04185986891d1340201e5", "sha256_in_prefix": "bfe081202225eb43728d83307c8cfede9f54936a3fc04185986891d1340201e5", "size_in_bytes": 2739}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/db_metrics.py", "path_type": "hardlink", "sha256": "e3f5c86c841ebe1c22f9ea77feeb02474d2d74ee8a4083614a0f34283a7559c8", "sha256_in_prefix": "e3f5c86c841ebe1c22f9ea77feeb02474d2d74ee8a4083614a0f34283a7559c8", "size_in_bytes": 12294}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/dns_metrics.py", "path_type": "hardlink", "sha256": "6c63f6b01afcd7b135176e5e2ea67f89c5cc24de76b02ece2d379f0093668391", "sha256_in_prefix": "6c63f6b01afcd7b135176e5e2ea67f89c5cc24de76b02ece2d379f0093668391", "size_in_bytes": 1085}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/faas_metrics.py", "path_type": "hardlink", "sha256": "9f316b63b788de10f4d09626400b6a6ccdfe20f9dee23f58803ccdd11473db6b", "sha256_in_prefix": "9f316b63b788de10f4d09626400b6a6ccdfe20f9dee23f58803ccdd11473db6b", "size_in_bytes": 4249}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/gen_ai_metrics.py", "path_type": "hardlink", "sha256": "ebdf750fe6c5a0bdab4ba39e1685c274583a1dbf0717b1be3cb8caa6ec095358", "sha256_in_prefix": "ebdf750fe6c5a0bdab4ba39e1685c274583a1dbf0717b1be3cb8caa6ec095358", "size_in_bytes": 3163}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/http_metrics.py", "path_type": "hardlink", "sha256": "ed422c6303eac3590fd9a44307f4aaa88964108598745332e2fc2e1bca31f3d5", "sha256_in_prefix": "ed422c6303eac3590fd9a44307f4aaa88964108598745332e2fc2e1bca31f3d5", "size_in_bytes": 6649}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/hw_metrics.py", "path_type": "hardlink", "sha256": "b0c9fd885b2d0a497b14179b4017cb1fc816430b19e866f6f281ad1a521ce032", "sha256_in_prefix": "b0c9fd885b2d0a497b14179b4017cb1fc816430b19e866f6f281ad1a521ce032", "size_in_bytes": 21711}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/k8s_metrics.py", "path_type": "hardlink", "sha256": "1eb6d582f207dac0b28f82d16a12ff7b80e5dfd3bfff342092dec63445bbe3c9", "sha256_in_prefix": "1eb6d582f207dac0b28f82d16a12ff7b80e5dfd3bfff342092dec63445bbe3c9", "size_in_bytes": 70249}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/messaging_metrics.py", "path_type": "hardlink", "sha256": "479b6d572d9d9403cad366f304af09bb33596acf92991242ac36e756f9347937", "sha256_in_prefix": "479b6d572d9d9403cad366f304af09bb33596acf92991242ac36e756f9347937", "size_in_bytes": 6234}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/otel_metrics.py", "path_type": "hardlink", "sha256": "fd1c1d6c6f200bb5355511688a1bc40efac7a2fc7d45b321b34436c6f4c01fb0", "sha256_in_prefix": "fd1c1d6c6f200bb5355511688a1bc40efac7a2fc7d45b321b34436c6f4c01fb0", "size_in_bytes": 17624}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/process_metrics.py", "path_type": "hardlink", "sha256": "104d698e79d6e10a2b4ab989bccd68163ccc4e2e3be3b9579e04484f5decb099", "sha256_in_prefix": "104d698e79d6e10a2b4ab989bccd68163ccc4e2e3be3b9579e04484f5decb099", "size_in_bytes": 6225}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/rpc_metrics.py", "path_type": "hardlink", "sha256": "a0427aa02353b265d7272286f4f8e05921dad5ae1e8286fff7cc0d005ecc1165", "sha256_in_prefix": "a0427aa02353b265d7272286f4f8e05921dad5ae1e8286fff7cc0d005ecc1165", "size_in_bytes": 6238}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/system_metrics.py", "path_type": "hardlink", "sha256": "517ef08db47f2b7a9bc60a25d9770ef9a518866ab34dfdd14288ab98c0cfc482", "sha256_in_prefix": "517ef08db47f2b7a9bc60a25d9770ef9a518866ab34dfdd14288ab98c0cfc482", "size_in_bytes": 18923}, {"_path": "site-packages/opentelemetry/semconv/_incubating/metrics/vcs_metrics.py", "path_type": "hardlink", "sha256": "65a52e4d2ac2ac659c6fec868001f7c521cc253422b1d37a4b21ccc30ecc856b", "sha256_in_prefix": "65a52e4d2ac2ac659c6fec868001f7c521cc253422b1d37a4b21ccc30ecc856b", "size_in_bytes": 8123}, {"_path": "site-packages/opentelemetry/semconv/attributes/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/semconv/attributes/client_attributes.py", "path_type": "hardlink", "sha256": "5dfefc1de1003e74b080de81120b6200fd173c768fea5355732060f865c6906e", "sha256_in_prefix": "5dfefc1de1003e74b080de81120b6200fd173c768fea5355732060f865c6906e", "size_in_bytes": 1260}, {"_path": "site-packages/opentelemetry/semconv/attributes/code_attributes.py", "path_type": "hardlink", "sha256": "1b0b86a833d20ad2fb75f4acb26441caa1e3880232f56b00717444ce65ed61df", "sha256_in_prefix": "1b0b86a833d20ad2fb75f4acb26441caa1e3880232f56b00717444ce65ed61df", "size_in_bytes": 3365}, {"_path": "site-packages/opentelemetry/semconv/attributes/db_attributes.py", "path_type": "hardlink", "sha256": "be63258ac5de709b2dee94214be1804756852ed3676c21cf49eb46b9dfccae43", "sha256_in_prefix": "be63258ac5de709b2dee94214be1804756852ed3676c21cf49eb46b9dfccae43", "size_in_bytes": 5878}, {"_path": "site-packages/opentelemetry/semconv/attributes/error_attributes.py", "path_type": "hardlink", "sha256": "2ac545027c23ecb9d0aa30c36a2c121b017712cf94e5b9824a50c080a0e54221", "sha256_in_prefix": "2ac545027c23ecb9d0aa30c36a2c121b017712cf94e5b9824a50c080a0e54221", "size_in_bytes": 1832}, {"_path": "site-packages/opentelemetry/semconv/attributes/exception_attributes.py", "path_type": "hardlink", "sha256": "84a6b2fa11fa4fdff764a93ed9fbf3f4e2b27a9aea4c1f868603254839fc2112", "sha256_in_prefix": "84a6b2fa11fa4fdff764a93ed9fbf3f4e2b27a9aea4c1f868603254839fc2112", "size_in_bytes": 1310}, {"_path": "site-packages/opentelemetry/semconv/attributes/http_attributes.py", "path_type": "hardlink", "sha256": "96e7757ecbdc78a02d842ff7ca3dfec6539c6d5a18890844aef04b92c7f65254", "sha256_in_prefix": "96e7757ecbdc78a02d842ff7ca3dfec6539c6d5a18890844aef04b92c7f65254", "size_in_bytes": 5958}, {"_path": "site-packages/opentelemetry/semconv/attributes/network_attributes.py", "path_type": "hardlink", "sha256": "2d7a9bd044e949f807a13576d042ed4123678205e98a6b432f9fd2b76ac7c2f9", "sha256_in_prefix": "2d7a9bd044e949f807a13576d042ed4123678205e98a6b432f9fd2b76ac7c2f9", "size_in_bytes": 2735}, {"_path": "site-packages/opentelemetry/semconv/attributes/otel_attributes.py", "path_type": "hardlink", "sha256": "f2fcdae87375adf511101d88c26755b8153f56dd0b16317ae319d14f5221e298", "sha256_in_prefix": "f2fcdae87375adf511101d88c26755b8153f56dd0b16317ae319d14f5221e298", "size_in_bytes": 1407}, {"_path": "site-packages/opentelemetry/semconv/attributes/server_attributes.py", "path_type": "hardlink", "sha256": "fafbc13f5f96b270e7b38d3b83eda7985329fd736e4999ba2315b807ad5adf9d", "sha256_in_prefix": "fafbc13f5f96b270e7b38d3b83eda7985329fd736e4999ba2315b807ad5adf9d", "size_in_bytes": 1248}, {"_path": "site-packages/opentelemetry/semconv/attributes/service_attributes.py", "path_type": "hardlink", "sha256": "60275a948a72f9176a61142362c48957bb31f14f894f5fb99f373c0691658eaa", "sha256_in_prefix": "60275a948a72f9176a61142362c48957bb31f14f894f5fb99f373c0691658eaa", "size_in_bytes": 1168}, {"_path": "site-packages/opentelemetry/semconv/attributes/telemetry_attributes.py", "path_type": "hardlink", "sha256": "6475c0e4e530ee66aad3c6cf304ac4db212b8bf133b423a0c176f4b218105eaa", "sha256_in_prefix": "6475c0e4e530ee66aad3c6cf304ac4db212b8bf133b423a0c176f4b218105eaa", "size_in_bytes": 1930}, {"_path": "site-packages/opentelemetry/semconv/attributes/url_attributes.py", "path_type": "hardlink", "sha256": "15f4dd843c27484811d86d4a343334b0f107cc6045d4f7a1fbb161d2bc71f622", "sha256_in_prefix": "15f4dd843c27484811d86d4a343334b0f107cc6045d4f7a1fbb161d2bc71f622", "size_in_bytes": 3718}, {"_path": "site-packages/opentelemetry/semconv/attributes/user_agent_attributes.py", "path_type": "hardlink", "sha256": "3ac6b997a62e25891bc71acdec5c629fb8578388e46a89122020c9123300b5a3", "sha256_in_prefix": "3ac6b997a62e25891bc71acdec5c629fb8578388e46a89122020c9123300b5a3", "size_in_bytes": 790}, {"_path": "site-packages/opentelemetry/semconv/metrics/__init__.py", "path_type": "hardlink", "sha256": "26b734b95d3c8d170abf873bb46ebf77dcecb22f37fcb85ea678535b8871b99d", "sha256_in_prefix": "26b734b95d3c8d170abf873bb46ebf77dcecb22f37fcb85ea678535b8871b99d", "size_in_bytes": 5808}, {"_path": "site-packages/opentelemetry/semconv/metrics/db_metrics.py", "path_type": "hardlink", "sha256": "96d52312456879873d65ec79c77df8fb151f5997117bc3570fe1ae88d196f8a8", "sha256_in_prefix": "96d52312456879873d65ec79c77df8fb151f5997117bc3570fe1ae88d196f8a8", "size_in_bytes": 823}, {"_path": "site-packages/opentelemetry/semconv/metrics/http_metrics.py", "path_type": "hardlink", "sha256": "83645d414f2aba7bce23445b6ba4abc0c29920dffa93d59309bdf867b791ac40", "sha256_in_prefix": "83645d414f2aba7bce23445b6ba4abc0c29920dffa93d59309bdf867b791ac40", "size_in_bytes": 894}, {"_path": "site-packages/opentelemetry/semconv/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/semconv/resource/__init__.py", "path_type": "hardlink", "sha256": "f305ad8ef142f538ce25e37497d4ca56c6864f67fc3d0d7edd9a0c12bfd0aaec", "sha256_in_prefix": "f305ad8ef142f538ce25e37497d4ca56c6864f67fc3d0d7edd9a0c12bfd0aaec", "size_in_bytes": 32978}, {"_path": "site-packages/opentelemetry/semconv/schemas.py", "path_type": "hardlink", "sha256": "14f7dff1af6d195dfe292bf64137b4df60c94e5e558d9428864395ba2c87dddd", "sha256_in_prefix": "14f7dff1af6d195dfe292bf64137b4df60c94e5e558d9428864395ba2c87dddd", "size_in_bytes": 2533}, {"_path": "site-packages/opentelemetry/semconv/trace/__init__.py", "path_type": "hardlink", "sha256": "a291189c4f0e8eff7e9999a3792e55bbc4cb7ffe8f8d344a966014b4c04c58a0", "sha256_in_prefix": "a291189c4f0e8eff7e9999a3792e55bbc4cb7ffe8f8d344a966014b4c04c58a0", "size_in_bytes": 69390}, {"_path": "site-packages/opentelemetry/semconv/version/__init__.py", "path_type": "hardlink", "sha256": "7df3fea041072f2c50a9ace13e0a7b0ae35b56da7fb236f5328dbfccd65bf905", "sha256_in_prefix": "7df3fea041072f2c50a9ace13e0a7b0ae35b56da7fb236f5328dbfccd65bf905", "size_in_bytes": 608}, {"_path": "site-packages/opentelemetry_semantic_conventions-0.58b0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/opentelemetry_semantic_conventions-0.58b0.dist-info/METADATA", "path_type": "hardlink", "sha256": "2a542b1dbe3764057df136a8358a51a100a7ec2c35f81abde3d7cf235a6a832e", "sha256_in_prefix": "2a542b1dbe3764057df136a8358a51a100a7ec2c35f81abde3d7cf235a6a832e", "size_in_bytes": 2406}, {"_path": "site-packages/opentelemetry_semantic_conventions-0.58b0.dist-info/RECORD", "path_type": "hardlink", "sha256": "7cc1ae30f804b424acdeac07c65964222dd884aba5cac4304a6516b89cc0aa65", "sha256_in_prefix": "7cc1ae30f804b424acdeac07c65964222dd884aba5cac4304a6516b89cc0aa65", "size_in_bytes": 24319}, {"_path": "site-packages/opentelemetry_semantic_conventions-0.58b0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry_semantic_conventions-0.58b0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "sha256_in_prefix": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "size_in_bytes": 87}, {"_path": "site-packages/opentelemetry_semantic_conventions-0.58b0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "83a73382316fc4e29d66c9e1e54530b13689cb78f67ab692ca7f58fed0f18041", "sha256_in_prefix": "83a73382316fc4e29d66c9e1e54530b13689cb78f67ab692ca7f58fed0f18041", "size_in_bytes": 130}, {"_path": "site-packages/opentelemetry_semantic_conventions-0.58b0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "sha256_in_prefix": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "size_in_bytes": 11357}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/app_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/artifact_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/aws_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/az_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/azure_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/browser_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/cassandra_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/cicd_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/client_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/cloud_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/cloudevents_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/cloudfoundry_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/code_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/container_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/cpu_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/cpython_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/db_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/deployment_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/destination_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/device_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/disk_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/dns_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/elasticsearch_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/enduser_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/error_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/event_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/exception_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/faas_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/feature_flag_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/file_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/gcp_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/gen_ai_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/geo_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/graphql_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/heroku_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/host_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/http_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/hw_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/k8s_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/linux_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/log_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/mainframe_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/message_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/messaging_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/net_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/network_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/oci_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/openai_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/opentracing_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/os_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/otel_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/other_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/peer_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/pool_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/process_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/profile_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/rpc_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/security_rule_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/server_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/service_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/session_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/source_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/system_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/telemetry_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/test_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/thread_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/tls_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/url_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/user_agent_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/user_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/vcs_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/webengine_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/attributes/__pycache__/zos_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/azure_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/cicd_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/container_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/cpu_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/cpython_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/db_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/dns_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/faas_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/gen_ai_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/http_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/hw_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/k8s_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/messaging_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/otel_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/process_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/rpc_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/system_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/_incubating/metrics/__pycache__/vcs_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/client_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/code_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/db_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/error_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/exception_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/http_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/network_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/otel_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/server_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/service_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/telemetry_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/url_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/attributes/__pycache__/user_agent_attributes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/metrics/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/metrics/__pycache__/db_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/metrics/__pycache__/http_metrics.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/resource/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/__pycache__/schemas.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/trace/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/semconv/version/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "9d16bccb44aadb1dac337d823501f6ad4b2ffa34b848cc4806db68e4295ef9f9", "size": 111592, "subdir": "noarch", "timestamp": 1757631100000, "url": "https://conda.anaconda.org/conda-forge/noarch/opentelemetry-semantic-conventions-0.58b0-pyh3cfb1c2_0.conda", "version": "0.58b0"}