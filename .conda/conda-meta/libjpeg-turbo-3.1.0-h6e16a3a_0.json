{"build": "h6e16a3a_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": ["jpeg <0.0.0a"], "depends": ["__osx >=10.13"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libjpeg-turbo-3.1.0-h6e16a3a_0", "files": ["bin/cjpeg", "bin/djpeg", "bin/jpegtran", "bin/rdjpgcom", "bin/tjbench", "bin/wrjpgcom", "include/jconfig.h", "include/jerror.h", "include/jmorecfg.h", "include/jpeglib.h", "include/turbojpeg.h", "lib/cmake/libjpeg-turbo/libjpeg-turboConfig.cmake", "lib/cmake/libjpeg-turbo/libjpeg-turboConfigVersion.cmake", "lib/cmake/libjpeg-turbo/libjpeg-turboTargets-release.cmake", "lib/cmake/libjpeg-turbo/libjpeg-turboTargets.cmake", "lib/libjpeg.8.3.2.dylib", "lib/libjpeg.8.dylib", "lib/libjpeg.a", "lib/libjpeg.dylib", "lib/libturbojpeg.0.4.0.dylib", "lib/libturbojpeg.0.dylib", "lib/libturbojpeg.a", "lib/libturbojpeg.dylib", "lib/pkgconfig/libjpeg.pc", "lib/pkgconfig/libturbojpeg.pc", "share/doc/libjpeg-turbo/LICENSE.md", "share/doc/libjpeg-turbo/README.ijg", "share/doc/libjpeg-turbo/README.md", "share/doc/libjpeg-turbo/example.c", "share/doc/libjpeg-turbo/libjpeg.txt", "share/doc/libjpeg-turbo/structure.txt", "share/doc/libjpeg-turbo/tjcomp.c", "share/doc/libjpeg-turbo/tjdecomp.c", "share/doc/libjpeg-turbo/tjtran.c", "share/doc/libjpeg-turbo/usage.txt", "share/doc/libjpeg-turbo/wizard.txt", "share/man/man1/cjpeg.1", "share/man/man1/djpeg.1", "share/man/man1/jpegtran.1", "share/man/man1/rdjpgcom.1", "share/man/man1/wrjpgcom.1"], "fn": "libjpeg-turbo-3.1.0-h6e16a3a_0.conda", "license": "IJG AND BSD-3-Clause AND Zlib", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libjpeg-turbo-3.1.0-h6e16a3a_0", "type": 1}, "md5": "87537967e6de2f885a9fcebd42b7cb10", "name": "libjpeg-turbo", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libjpeg-turbo-3.1.0-h6e16a3a_0.conda", "paths_data": {"paths": [{"_path": "bin/cjpeg", "path_type": "hardlink", "sha256": "cb40d134f7f6d83195ca474740d2414a65319bc8e40490496fff10b2c3ee6bbc", "sha256_in_prefix": "cb40d134f7f6d83195ca474740d2414a65319bc8e40490496fff10b2c3ee6bbc", "size_in_bytes": 84568}, {"_path": "bin/djpeg", "path_type": "hardlink", "sha256": "6e932492a76650b2a844cd22cd1a6f60fd0b6133a6670d59288936bcdc4c9dbc", "sha256_in_prefix": "6e932492a76650b2a844cd22cd1a6f60fd0b6133a6670d59288936bcdc4c9dbc", "size_in_bytes": 59840}, {"_path": "bin/jpegtran", "path_type": "hardlink", "sha256": "66caafa034c7e00e326c1f0f665f4525b8d97cd279cc683646cc54801ad316ae", "sha256_in_prefix": "66caafa034c7e00e326c1f0f665f4525b8d97cd279cc683646cc54801ad316ae", "size_in_bytes": 66464}, {"_path": "bin/rdjpgcom", "path_type": "hardlink", "sha256": "e4d715e883a9efb40e540461d9f668422bfe61f6744a6d2158a4878cb0b48441", "sha256_in_prefix": "e4d715e883a9efb40e540461d9f668422bfe61f6744a6d2158a4878cb0b48441", "size_in_bytes": 13432}, {"_path": "bin/tjbench", "path_type": "hardlink", "sha256": "de0d01f819ef12324bd00a795d6dbad0c5573d7b0ad6228fdb78bae7dad9b48f", "sha256_in_prefix": "de0d01f819ef12324bd00a795d6dbad0c5573d7b0ad6228fdb78bae7dad9b48f", "size_in_bytes": 62672}, {"_path": "bin/wrjpgcom", "path_type": "hardlink", "sha256": "5cffa14d5a5036b6530be39928eaca34d680f5448d5ea7eb0c4764d9693a45ad", "sha256_in_prefix": "5cffa14d5a5036b6530be39928eaca34d680f5448d5ea7eb0c4764d9693a45ad", "size_in_bytes": 17632}, {"_path": "include/jconfig.h", "path_type": "hardlink", "sha256": "0c90798a9323c4ef8803287242e869e13eeaf6337e93113929e4fdc852653341", "sha256_in_prefix": "0c90798a9323c4ef8803287242e869e13eeaf6337e93113929e4fdc852653341", "size_in_bytes": 2065}, {"_path": "include/jerror.h", "path_type": "hardlink", "sha256": "35354d148ea6da234bacd5435155d0ec1de9c05833602232a67f1cf184c04925", "sha256_in_prefix": "35354d148ea6da234bacd5435155d0ec1de9c05833602232a67f1cf184c04925", "size_in_bytes": 16159}, {"_path": "include/jmorecfg.h", "path_type": "hardlink", "sha256": "80a97f45ecb28d83310247eecec2a3865b5fd39ba0ecc490b48a36fac6318e26", "sha256_in_prefix": "80a97f45ecb28d83310247eecec2a3865b5fd39ba0ecc490b48a36fac6318e26", "size_in_bytes": 14386}, {"_path": "include/jpeglib.h", "path_type": "hardlink", "sha256": "dc3e8220f34b7e9f9e48620f5cb8dc070834feb3214bb12bbad78478ebb29e60", "sha256_in_prefix": "dc3e8220f34b7e9f9e48620f5cb8dc070834feb3214bb12bbad78478ebb29e60", "size_in_bytes": 55794}, {"_path": "include/turbojpeg.h", "path_type": "hardlink", "sha256": "3a0cbe2db4d7fc8af1883e5f9868a7e88d87e1e5259b165038439e008e8e505c", "sha256_in_prefix": "3a0cbe2db4d7fc8af1883e5f9868a7e88d87e1e5259b165038439e008e8e505c", "size_in_bytes": 120018}, {"_path": "lib/cmake/libjpeg-turbo/libjpeg-turboConfig.cmake", "path_type": "hardlink", "sha256": "70baa95a13e5238689a76c0adb29d8f6564999b596a4e4512ae37a89311f81a5", "sha256_in_prefix": "70baa95a13e5238689a76c0adb29d8f6564999b596a4e4512ae37a89311f81a5", "size_in_bytes": 981}, {"_path": "lib/cmake/libjpeg-turbo/libjpeg-turboConfigVersion.cmake", "path_type": "hardlink", "sha256": "6404ec044df2ac22b4b57d05843ffde6b990af08eb05848b683a8ad344671d1c", "sha256_in_prefix": "6404ec044df2ac22b4b57d05843ffde6b990af08eb05848b683a8ad344671d1c", "size_in_bytes": 1861}, {"_path": "lib/cmake/libjpeg-turbo/libjpeg-turboTargets-release.cmake", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/libjpeg-turbo_1745268027020/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_place", "sha256": "6bdf48af6b3e6e3913e6048ff701fe5487a2f8763fd24551dec45387a4d76dd3", "sha256_in_prefix": "d59370755bdb6f4d1e4cb43064024390e249427b8dd19aa8f07910820929aa3f", "size_in_bytes": 4427}, {"_path": "lib/cmake/libjpeg-turbo/libjpeg-turboTargets.cmake", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/libjpeg-turbo_1745268027020/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_place", "sha256": "b5eadf21f641117c74697794340d70adf97fcc0feffa983925f4590cf2a0b5c3", "sha256_in_prefix": "0d51150c3a5136107d088657971571e9fbe8c32a5c2b6648478330967a99cdad", "size_in_bytes": 4877}, {"_path": "lib/libjpeg.8.3.2.dylib", "path_type": "hardlink", "sha256": "e25f9d035a711f5970f15b1197167fab7a40c352087562aa732c56aaa787224c", "sha256_in_prefix": "e25f9d035a711f5970f15b1197167fab7a40c352087562aa732c56aaa787224c", "size_in_bytes": 868832}, {"_path": "lib/libjpeg.8.dylib", "path_type": "softlink", "sha256": "e25f9d035a711f5970f15b1197167fab7a40c352087562aa732c56aaa787224c", "size_in_bytes": 868832}, {"_path": "lib/libjpeg.a", "path_type": "hardlink", "sha256": "7d2d90b6d651eb31997e84279945319082d5476a66de7c15fce81a41ddf753db", "sha256_in_prefix": "7d2d90b6d651eb31997e84279945319082d5476a66de7c15fce81a41ddf753db", "size_in_bytes": 1064528}, {"_path": "lib/libjpeg.dylib", "path_type": "softlink", "sha256": "e25f9d035a711f5970f15b1197167fab7a40c352087562aa732c56aaa787224c", "size_in_bytes": 868832}, {"_path": "lib/libturbojpeg.0.4.0.dylib", "path_type": "hardlink", "sha256": "3784ee23f4e64816ff5baaeb89a3565a3131cc7e3147c9633b71fdf3146e0751", "sha256_in_prefix": "3784ee23f4e64816ff5baaeb89a3565a3131cc7e3147c9633b71fdf3146e0751", "size_in_bytes": 1061400}, {"_path": "lib/libturbojpeg.0.dylib", "path_type": "softlink", "sha256": "3784ee23f4e64816ff5baaeb89a3565a3131cc7e3147c9633b71fdf3146e0751", "size_in_bytes": 1061400}, {"_path": "lib/libturbojpeg.a", "path_type": "hardlink", "sha256": "3fd37f32da0fa506b3916b68a9169d7d470e58e27c6333893374d56bcd73fad0", "sha256_in_prefix": "3fd37f32da0fa506b3916b68a9169d7d470e58e27c6333893374d56bcd73fad0", "size_in_bytes": 1315424}, {"_path": "lib/libturbojpeg.dylib", "path_type": "softlink", "sha256": "3784ee23f4e64816ff5baaeb89a3565a3131cc7e3147c9633b71fdf3146e0751", "size_in_bytes": 1061400}, {"_path": "lib/pkgconfig/libjpeg.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/libjpeg-turbo_1745268027020/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_place", "sha256": "630e0a0609bd43ed009efbe1d50a5e275ef7eb09a876ff4be3b1815c1e141772", "sha256_in_prefix": "5f2d79247b7d2ede2744efcedbfc16d1ebae27bcedbf933b6ac6c6a9ead7ffe1", "size_in_bytes": 1225}, {"_path": "lib/pkgconfig/libturbojpeg.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/libjpeg-turbo_1745268027020/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_place", "sha256": "f4432c5c99e873241c76447ebefc7120e71a4bdf24259c27b193cd8626930d5a", "sha256_in_prefix": "ece34383cb4f86b4105d2e1f2534c3e54a021ea92ea354804799849a031a0c09", "size_in_bytes": 1237}, {"_path": "share/doc/libjpeg-turbo/LICENSE.md", "path_type": "hardlink", "sha256": "96f5b328adbb78eeaaec6980d73fd558cb1e4d62560ed615646bc3cf5e532430", "sha256_in_prefix": "96f5b328adbb78eeaaec6980d73fd558cb1e4d62560ed615646bc3cf5e532430", "size_in_bytes": 5620}, {"_path": "share/doc/libjpeg-turbo/README.ijg", "path_type": "hardlink", "sha256": "75815e3bf6484201a3c3d17a1bbf10f2e8e3237f84df10a2357ea896db2a81d6", "sha256_in_prefix": "75815e3bf6484201a3c3d17a1bbf10f2e8e3237f84df10a2357ea896db2a81d6", "size_in_bytes": 12799}, {"_path": "share/doc/libjpeg-turbo/README.md", "path_type": "hardlink", "sha256": "c553459fc4ea1f69526f0edf0c8ff6c4002fda5b76094abefabdc3de1ee62c10", "sha256_in_prefix": "c553459fc4ea1f69526f0edf0c8ff6c4002fda5b76094abefabdc3de1ee62c10", "size_in_bytes": 17784}, {"_path": "share/doc/libjpeg-turbo/example.c", "path_type": "hardlink", "sha256": "1c0aeda4768fb884fc3f052deff778e2a86d13b17803b629e374007460bec328", "sha256_in_prefix": "1c0aeda4768fb884fc3f052deff778e2a86d13b17803b629e374007460bec328", "size_in_bytes": 24191}, {"_path": "share/doc/libjpeg-turbo/libjpeg.txt", "path_type": "hardlink", "sha256": "e22ea2053f7e9b846d174a62e4cce4398bbd49ddd6888463df9884bb0edc7d85", "sha256_in_prefix": "e22ea2053f7e9b846d174a62e4cce4398bbd49ddd6888463df9884bb0edc7d85", "size_in_bytes": 177182}, {"_path": "share/doc/libjpeg-turbo/structure.txt", "path_type": "hardlink", "sha256": "2508e179a6b7f45b91009160247bbb96f14f6f2e32a82b36c27878aae8129884", "sha256_in_prefix": "2508e179a6b7f45b91009160247bbb96f14f6f2e32a82b36c27878aae8129884", "size_in_bytes": 53399}, {"_path": "share/doc/libjpeg-turbo/tjcomp.c", "path_type": "hardlink", "sha256": "6933f4726dd30f06760226ab9616fbce64cd41526174e499efcab521fee615ef", "sha256_in_prefix": "6933f4726dd30f06760226ab9616fbce64cd41526174e499efcab521fee615ef", "size_in_bytes": 13474}, {"_path": "share/doc/libjpeg-turbo/tjdecomp.c", "path_type": "hardlink", "sha256": "ff7b8e02a9eb0d58cd306d0fc38612217b352c04a5e6c203a8eeca36934ae666", "sha256_in_prefix": "ff7b8e02a9eb0d58cd306d0fc38612217b352c04a5e6c203a8eeca36934ae666", "size_in_bytes": 13757}, {"_path": "share/doc/libjpeg-turbo/tjtran.c", "path_type": "hardlink", "sha256": "ab0fbd8c2a586c5149c9de0431a8db99559aaf33086e95929094ad544544d8a4", "sha256_in_prefix": "ab0fbd8c2a586c5149c9de0431a8db99559aaf33086e95929094ad544544d8a4", "size_in_bytes": 14997}, {"_path": "share/doc/libjpeg-turbo/usage.txt", "path_type": "hardlink", "sha256": "d196e734853a0abd0210fb61d5dbf7561c7a7b89722628390771cf411b356150", "sha256_in_prefix": "d196e734853a0abd0210fb61d5dbf7561c7a7b89722628390771cf411b356150", "size_in_bytes": 46748}, {"_path": "share/doc/libjpeg-turbo/wizard.txt", "path_type": "hardlink", "sha256": "3728907472df90ce30b1da41d11e9f23a66d051037c743be74cafd0e36ff176a", "sha256_in_prefix": "3728907472df90ce30b1da41d11e9f23a66d051037c743be74cafd0e36ff176a", "size_in_bytes": 11255}, {"_path": "share/man/man1/cjpeg.1", "path_type": "hardlink", "sha256": "86e42e381b44ac9dd2b48466140eb3a7f1f95961f6f646c144584e173bc6ee6f", "sha256_in_prefix": "86e42e381b44ac9dd2b48466140eb3a7f1f95961f6f646c144584e173bc6ee6f", "size_in_bytes": 16556}, {"_path": "share/man/man1/djpeg.1", "path_type": "hardlink", "sha256": "b559df09a3d4fd28f8ab3b801e22e3aae6195408a62b72a071cfddf053c52b7a", "sha256_in_prefix": "b559df09a3d4fd28f8ab3b801e22e3aae6195408a62b72a071cfddf053c52b7a", "size_in_bytes": 11043}, {"_path": "share/man/man1/jpegtran.1", "path_type": "hardlink", "sha256": "465a48cc70829d305fb2ab22c38611cb98b4635736d1aaead681d5a9b3447b9c", "sha256_in_prefix": "465a48cc70829d305fb2ab22c38611cb98b4635736d1aaead681d5a9b3447b9c", "size_in_bytes": 13300}, {"_path": "share/man/man1/rdjpgcom.1", "path_type": "hardlink", "sha256": "9f6920585ec2f7fe3f72fe2c7d9be1562806e5850b5c9af35b2abc54971c8f79", "sha256_in_prefix": "9f6920585ec2f7fe3f72fe2c7d9be1562806e5850b5c9af35b2abc54971c8f79", "size_in_bytes": 1699}, {"_path": "share/man/man1/wrjpgcom.1", "path_type": "hardlink", "sha256": "d5725d2ae72e8029bccc0cb79f7b02b1d0080bf9ac96ac01bf7160d0fdba9574", "sha256_in_prefix": "d5725d2ae72e8029bccc0cb79f7b02b1d0080bf9ac96ac01bf7160d0fdba9574", "size_in_bytes": 2626}], "paths_version": 1}, "requested_spec": "None", "sha256": "9c0009389c1439ec96a08e3bf7731ac6f0eab794e0a133096556a9ae10be9c27", "size": 586456, "subdir": "osx-64", "timestamp": 1745268522000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libjpeg-turbo-3.1.0-h6e16a3a_0.conda", "version": "3.1.0"}