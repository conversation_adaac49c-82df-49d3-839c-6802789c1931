{"build": "pyhd8ed1ab_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.9"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/nest-asyncio-1.6.0-pyhd8ed1ab_1", "files": ["lib/python3.11/site-packages/nest_asyncio-1.6.0.dist-info/INSTALLER", "lib/python3.11/site-packages/nest_asyncio-1.6.0.dist-info/LICENSE", "lib/python3.11/site-packages/nest_asyncio-1.6.0.dist-info/METADATA", "lib/python3.11/site-packages/nest_asyncio-1.6.0.dist-info/RECORD", "lib/python3.11/site-packages/nest_asyncio-1.6.0.dist-info/REQUESTED", "lib/python3.11/site-packages/nest_asyncio-1.6.0.dist-info/WHEEL", "lib/python3.11/site-packages/nest_asyncio-1.6.0.dist-info/direct_url.json", "lib/python3.11/site-packages/nest_asyncio-1.6.0.dist-info/top_level.txt", "lib/python3.11/site-packages/nest_asyncio.py", "lib/python3.11/site-packages/__pycache__/nest_asyncio.cpython-311.pyc"], "fn": "nest-asyncio-1.6.0-pyhd8ed1ab_1.conda", "license": "BSD-2-<PERSON><PERSON>", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/nest-asyncio-1.6.0-pyhd8ed1ab_1", "type": 1}, "md5": "598fd7d4d0de2455fb74f56063969a97", "name": "nest-asyncio", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/nest-asyncio-1.6.0-pyhd8ed1ab_1.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/nest_asyncio-1.6.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/nest_asyncio-1.6.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "bece9f68655ff23b77413246c0650386b87da7c3682303dd0ee8e3ea7630e85b", "sha256_in_prefix": "bece9f68655ff23b77413246c0650386b87da7c3682303dd0ee8e3ea7630e85b", "size_in_bytes": 1322}, {"_path": "site-packages/nest_asyncio-1.6.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "6e23644710e488e993ac7138b44972a11d76948d62f58d3a9e03ba226bbd02dd", "sha256_in_prefix": "6e23644710e488e993ac7138b44972a11d76948d62f58d3a9e03ba226bbd02dd", "size_in_bytes": 2812}, {"_path": "site-packages/nest_asyncio-1.6.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "19952a2f21308b793b32b494540f5c38099d998b3536681effe5ef6a632ade6a", "sha256_in_prefix": "19952a2f21308b793b32b494540f5c38099d998b3536681effe5ef6a632ade6a", "size_in_bytes": 819}, {"_path": "site-packages/nest_asyncio-1.6.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/nest_asyncio-1.6.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "3d9504c5d7fbd548bfb28ebb417a724ae1ed0a2dfe277c2f1783912ba93f4bc5", "sha256_in_prefix": "3d9504c5d7fbd548bfb28ebb417a724ae1ed0a2dfe277c2f1783912ba93f4bc5", "size_in_bytes": 91}, {"_path": "site-packages/nest_asyncio-1.6.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "6477610c65ed1eb33de6129d91830ca3dded3f3f68a721eac995d243b01bd8b6", "sha256_in_prefix": "6477610c65ed1eb33de6129d91830ca3dded3f3f68a721eac995d243b01bd8b6", "size_in_bytes": 108}, {"_path": "site-packages/nest_asyncio-1.6.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "71014133f7cf6c37615558a15b04bedbd5a25b5ecb677af89525f2bf0173373b", "sha256_in_prefix": "71014133f7cf6c37615558a15b04bedbd5a25b5ecb677af89525f2bf0173373b", "size_in_bytes": 13}, {"_path": "site-packages/nest_asyncio.py", "path_type": "hardlink", "sha256": "2a45b5e1bab4ec3e41a0e4e9923970bfc7455f9b91c3d67ce1347fbb9c11effa", "sha256_in_prefix": "2a45b5e1bab4ec3e41a0e4e9923970bfc7455f9b91c3d67ce1347fbb9c11effa", "size_in_bytes": 7490}, {"_path": "lib/python3.11/site-packages/__pycache__/nest_asyncio.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "bb7b21d7fd0445ddc0631f64e66d91a179de4ba920b8381f29b9d006a42788c0", "size": 11543, "subdir": "noarch", "timestamp": 1733325673000, "url": "https://conda.anaconda.org/conda-forge/noarch/nest-asyncio-1.6.0-pyhd8ed1ab_1.conda", "version": "1.6.0"}