{"build": "pyhd8ed1ab_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.9"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/wcwidth-0.2.13-pyhd8ed1ab_1", "files": ["lib/python3.11/site-packages/wcwidth-0.2.13.dist-info/INSTALLER", "lib/python3.11/site-packages/wcwidth-0.2.13.dist-info/LICENSE", "lib/python3.11/site-packages/wcwidth-0.2.13.dist-info/METADATA", "lib/python3.11/site-packages/wcwidth-0.2.13.dist-info/RECORD", "lib/python3.11/site-packages/wcwidth-0.2.13.dist-info/REQUESTED", "lib/python3.11/site-packages/wcwidth-0.2.13.dist-info/WHEEL", "lib/python3.11/site-packages/wcwidth-0.2.13.dist-info/direct_url.json", "lib/python3.11/site-packages/wcwidth-0.2.13.dist-info/top_level.txt", "lib/python3.11/site-packages/wcwidth-0.2.13.dist-info/zip-safe", "lib/python3.11/site-packages/wcwidth/__init__.py", "lib/python3.11/site-packages/wcwidth/table_vs16.py", "lib/python3.11/site-packages/wcwidth/table_wide.py", "lib/python3.11/site-packages/wcwidth/table_zero.py", "lib/python3.11/site-packages/wcwidth/unicode_versions.py", "lib/python3.11/site-packages/wcwidth/wcwidth.py", "lib/python3.11/site-packages/wcwidth/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/wcwidth/__pycache__/table_vs16.cpython-311.pyc", "lib/python3.11/site-packages/wcwidth/__pycache__/table_wide.cpython-311.pyc", "lib/python3.11/site-packages/wcwidth/__pycache__/table_zero.cpython-311.pyc", "lib/python3.11/site-packages/wcwidth/__pycache__/unicode_versions.cpython-311.pyc", "lib/python3.11/site-packages/wcwidth/__pycache__/wcwidth.cpython-311.pyc"], "fn": "wcwidth-0.2.13-pyhd8ed1ab_1.conda", "license": "MIT", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/wcwidth-0.2.13-pyhd8ed1ab_1", "type": 1}, "md5": "b68980f2495d096e71c7fd9d7ccf63e6", "name": "wcwidth", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/wcwidth-0.2.13-pyhd8ed1ab_1.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/wcwidth-0.2.13.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/wcwidth-0.2.13.dist-info/LICENSE", "path_type": "hardlink", "sha256": "70b98a95a2144eb70af8017fa8c6d95ce247e40867436e8bc649e137fe13d21a", "sha256_in_prefix": "70b98a95a2144eb70af8017fa8c6d95ce247e40867436e8bc649e137fe13d21a", "size_in_bytes": 1322}, {"_path": "site-packages/wcwidth-0.2.13.dist-info/METADATA", "path_type": "hardlink", "sha256": "dfef2844ef0f7190485915d1c0ef0735a3004fce79c60fc909e4ce11e51bb0e4", "sha256_in_prefix": "dfef2844ef0f7190485915d1c0ef0735a3004fce79c60fc909e4ce11e51bb0e4", "size_in_bytes": 14990}, {"_path": "site-packages/wcwidth-0.2.13.dist-info/RECORD", "path_type": "hardlink", "sha256": "452811f4a34c01dd95264dffba7805b63b9d97757240c1850afc76fe54e488b2", "sha256_in_prefix": "452811f4a34c01dd95264dffba7805b63b9d97757240c1850afc76fe54e488b2", "size_in_bytes": 1534}, {"_path": "site-packages/wcwidth-0.2.13.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/wcwidth-0.2.13.dist-info/WHEEL", "path_type": "hardlink", "sha256": "a7178d5f925db427b9f0f51260ff6ea6673b8dd44f82f4f41a6f646f5487955c", "sha256_in_prefix": "a7178d5f925db427b9f0f51260ff6ea6673b8dd44f82f4f41a6f646f5487955c", "size_in_bytes": 109}, {"_path": "site-packages/wcwidth-0.2.13.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "ccef1daf8dd235d56f2d7f7edfc926cdc86137f3f8290962191a96bd6ae17c8c", "sha256_in_prefix": "ccef1daf8dd235d56f2d7f7edfc926cdc86137f3f8290962191a96bd6ae17c8c", "size_in_bytes": 103}, {"_path": "site-packages/wcwidth-0.2.13.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "2cb8d2f121625d7b8b11c0f604d7457468692967b9a291edbe7eca363f402112", "sha256_in_prefix": "2cb8d2f121625d7b8b11c0f604d7457468692967b9a291edbe7eca363f402112", "size_in_bytes": 8}, {"_path": "site-packages/wcwidth-0.2.13.dist-info/zip-safe", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "site-packages/wcwidth/__init__.py", "path_type": "hardlink", "sha256": "79c671dd456892d66e02f6ad1ba35eb41c9516010d90a97d86d9a4d196ac4e60", "sha256_in_prefix": "79c671dd456892d66e02f6ad1ba35eb41c9516010d90a97d86d9a4d196ac4e60", "size_in_bytes": 1076}, {"_path": "site-packages/wcwidth/table_vs16.py", "path_type": "hardlink", "sha256": "84f6eea05c66c6b19fb8169ea2178c4c01a6801d9ae04b9d87162c62890b7faa", "sha256_in_prefix": "84f6eea05c66c6b19fb8169ea2178c4c01a6801d9ae04b9d87162c62890b7faa", "size_in_bytes": 6857}, {"_path": "site-packages/wcwidth/table_wide.py", "path_type": "hardlink", "sha256": "bd41e310eb91c35586c94708c362fd1b2999b18bc2d88ddd73ce7c9a56324d83", "sha256_in_prefix": "bd41e310eb91c35586c94708c362fd1b2999b18bc2d88ddd73ce7c9a56324d83", "size_in_bytes": 100896}, {"_path": "site-packages/wcwidth/table_zero.py", "path_type": "hardlink", "sha256": "e197a284b6431fca1b82bc00e9cb7ebeeda5c5ce2dfc3b1fca207da7d3afc5ba", "sha256_in_prefix": "e197a284b6431fca1b82bc00e9cb7ebeeda5c5ce2dfc3b1fca207da7d3afc5ba", "size_in_bytes": 359450}, {"_path": "site-packages/wcwidth/unicode_versions.py", "path_type": "hardlink", "sha256": "ee74a181e458aef645906a5111dafe3e451e5e7b8cf96c5e3a61988fa40d69a1", "sha256_in_prefix": "ee74a181e458aef645906a5111dafe3e451e5e7b8cf96c5e3a61988fa40d69a1", "size_in_bytes": 851}, {"_path": "site-packages/wcwidth/wcwidth.py", "path_type": "hardlink", "sha256": "4cbcf21f56a17440c33ce20c72a54ee14d20c8ac12c9977302c4a010fb951466", "sha256_in_prefix": "4cbcf21f56a17440c33ce20c72a54ee14d20c8ac12c9977302c4a010fb951466", "size_in_bytes": 14512}, {"_path": "lib/python3.11/site-packages/wcwidth/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/wcwidth/__pycache__/table_vs16.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/wcwidth/__pycache__/table_wide.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/wcwidth/__pycache__/table_zero.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/wcwidth/__pycache__/unicode_versions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/wcwidth/__pycache__/wcwidth.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "f21e63e8f7346f9074fd00ca3b079bd3d2fa4d71f1f89d5b6934bf31446dc2a5", "size": 32581, "subdir": "noarch", "timestamp": 1733231433000, "url": "https://conda.anaconda.org/conda-forge/noarch/wcwidth-0.2.13-pyhd8ed1ab_1.conda", "version": "0.2.13"}