{"build": "pyh707e725_8", "build_number": 8, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["__unix", "python >=3.9"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/humanfriendly-10.0-pyh707e725_8", "files": ["lib/python3.11/site-packages/humanfriendly-10.0.dist-info/INSTALLER", "lib/python3.11/site-packages/humanfriendly-10.0.dist-info/LICENSE.txt", "lib/python3.11/site-packages/humanfriendly-10.0.dist-info/METADATA", "lib/python3.11/site-packages/humanfriendly-10.0.dist-info/RECORD", "lib/python3.11/site-packages/humanfriendly-10.0.dist-info/REQUESTED", "lib/python3.11/site-packages/humanfriendly-10.0.dist-info/WHEEL", "lib/python3.11/site-packages/humanfriendly-10.0.dist-info/direct_url.json", "lib/python3.11/site-packages/humanfriendly-10.0.dist-info/entry_points.txt", "lib/python3.11/site-packages/humanfriendly-10.0.dist-info/top_level.txt", "lib/python3.11/site-packages/humanfriendly/__init__.py", "lib/python3.11/site-packages/humanfriendly/case.py", "lib/python3.11/site-packages/humanfriendly/cli.py", "lib/python3.11/site-packages/humanfriendly/compat.py", "lib/python3.11/site-packages/humanfriendly/decorators.py", "lib/python3.11/site-packages/humanfriendly/deprecation.py", "lib/python3.11/site-packages/humanfriendly/prompts.py", "lib/python3.11/site-packages/humanfriendly/sphinx.py", "lib/python3.11/site-packages/humanfriendly/tables.py", "lib/python3.11/site-packages/humanfriendly/terminal/__init__.py", "lib/python3.11/site-packages/humanfriendly/terminal/html.py", "lib/python3.11/site-packages/humanfriendly/terminal/spinners.py", "lib/python3.11/site-packages/humanfriendly/testing.py", "lib/python3.11/site-packages/humanfriendly/tests.py", "lib/python3.11/site-packages/humanfriendly/text.py", "lib/python3.11/site-packages/humanfriendly/usage.py", "lib/python3.11/site-packages/humanfriendly/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/humanfriendly/__pycache__/case.cpython-311.pyc", "lib/python3.11/site-packages/humanfriendly/__pycache__/cli.cpython-311.pyc", "lib/python3.11/site-packages/humanfriendly/__pycache__/compat.cpython-311.pyc", "lib/python3.11/site-packages/humanfriendly/__pycache__/decorators.cpython-311.pyc", "lib/python3.11/site-packages/humanfriendly/__pycache__/deprecation.cpython-311.pyc", "lib/python3.11/site-packages/humanfriendly/__pycache__/prompts.cpython-311.pyc", "lib/python3.11/site-packages/humanfriendly/__pycache__/sphinx.cpython-311.pyc", "lib/python3.11/site-packages/humanfriendly/__pycache__/tables.cpython-311.pyc", "lib/python3.11/site-packages/humanfriendly/terminal/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/humanfriendly/terminal/__pycache__/html.cpython-311.pyc", "lib/python3.11/site-packages/humanfriendly/terminal/__pycache__/spinners.cpython-311.pyc", "lib/python3.11/site-packages/humanfriendly/__pycache__/testing.cpython-311.pyc", "lib/python3.11/site-packages/humanfriendly/__pycache__/tests.cpython-311.pyc", "lib/python3.11/site-packages/humanfriendly/__pycache__/text.cpython-311.pyc", "lib/python3.11/site-packages/humanfriendly/__pycache__/usage.cpython-311.pyc", "bin/humanfriendly"], "fn": "humanfriendly-10.0-pyh707e725_8.conda", "license": "MIT", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/humanfriendly-10.0-pyh707e725_8", "type": 1}, "md5": "7fe569c10905402ed47024fc481bb371", "name": "humanfriendly", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/humanfriendly-10.0-pyh707e725_8.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/humanfriendly-10.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/humanfriendly-10.0.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "4ac48f4117809f2734066150450f120b3bb110ac1d3b32170795e0560dbbc1f5", "sha256_in_prefix": "4ac48f4117809f2734066150450f120b3bb110ac1d3b32170795e0560dbbc1f5", "size_in_bytes": 1056}, {"_path": "site-packages/humanfriendly-10.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "ba2cb27ceb38e8ad4e5a5e26391755e5040ac63e4cb36f9b038cce61c2746aa0", "sha256_in_prefix": "ba2cb27ceb38e8ad4e5a5e26391755e5040ac63e4cb36f9b038cce61c2746aa0", "size_in_bytes": 9208}, {"_path": "site-packages/humanfriendly-10.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "c85bef6da250864310c82598785d7018ffe0784c3ddfe97d6658ff7bdf2d6302", "sha256_in_prefix": "c85bef6da250864310c82598785d7018ffe0784c3ddfe97d6658ff7bdf2d6302", "size_in_bytes": 3081}, {"_path": "site-packages/humanfriendly-10.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/humanfriendly-10.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "a7178d5f925db427b9f0f51260ff6ea6673b8dd44f82f4f41a6f646f5487955c", "sha256_in_prefix": "a7178d5f925db427b9f0f51260ff6ea6673b8dd44f82f4f41a6f646f5487955c", "size_in_bytes": 109}, {"_path": "site-packages/humanfriendly-10.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "b991340c09e7ffe48bd24ef4578cb3a856d3cc31731d63099029d70d07411d55", "sha256_in_prefix": "b991340c09e7ffe48bd24ef4578cb3a856d3cc31731d63099029d70d07411d55", "size_in_bytes": 109}, {"_path": "site-packages/humanfriendly-10.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "813895d24b0a2286939e0e20b792d660fbe034b2f92fcba1b6464e8659617e6c", "sha256_in_prefix": "813895d24b0a2286939e0e20b792d660fbe034b2f92fcba1b6464e8659617e6c", "size_in_bytes": 57}, {"_path": "site-packages/humanfriendly-10.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "ede2802a17249a50f86685899169a19d3b35a673ff6f317ee7a553ab40fb588a", "sha256_in_prefix": "ede2802a17249a50f86685899169a19d3b35a673ff6f317ee7a553ab40fb588a", "size_in_bytes": 14}, {"_path": "site-packages/humanfriendly/__init__.py", "path_type": "hardlink", "sha256": "b0f08c42fd7a9b7a7cc58c1ddcdde452cd6b7b05490776460c1905d3ff2feafa", "sha256_in_prefix": "b0f08c42fd7a9b7a7cc58c1ddcdde452cd6b7b05490776460c1905d3ff2feafa", "size_in_bytes": 31725}, {"_path": "site-packages/humanfriendly/case.py", "path_type": "hardlink", "sha256": "7e42229e3e15d52f179773824984a0a8aa06cc209b5493a32396739d58824f54", "sha256_in_prefix": "7e42229e3e15d52f179773824984a0a8aa06cc209b5493a32396739d58824f54", "size_in_bytes": 6008}, {"_path": "site-packages/humanfriendly/cli.py", "path_type": "hardlink", "sha256": "6691aa4c72f07cb8c5002b1069c62437d0e205990679c3f3207cac96258dd62f", "sha256_in_prefix": "6691aa4c72f07cb8c5002b1069c62437d0e205990679c3f3207cac96258dd62f", "size_in_bytes": 9822}, {"_path": "site-packages/humanfriendly/compat.py", "path_type": "hardlink", "sha256": "eeaa0518c3458b31a1b39048ca230a4c352adbd0f1cee5c65f8311a192e8e26b", "sha256_in_prefix": "eeaa0518c3458b31a1b39048ca230a4c352adbd0f1cee5c65f8311a192e8e26b", "size_in_bytes": 3984}, {"_path": "site-packages/humanfriendly/decorators.py", "path_type": "hardlink", "sha256": "8afc41f94f5d7d75230a5e0667c83f80a142e1adeecb10d58bedeb9713e3bc9a", "sha256_in_prefix": "8afc41f94f5d7d75230a5e0667c83f80a142e1adeecb10d58bedeb9713e3bc9a", "size_in_bytes": 1501}, {"_path": "site-packages/humanfriendly/deprecation.py", "path_type": "hardlink", "sha256": "6ddc75fd3f0bd6017adf9138d44e9b613aa27bd3f7a3bad8ea7e30a218e42cb9", "sha256_in_prefix": "6ddc75fd3f0bd6017adf9138d44e9b613aa27bd3f7a3bad8ea7e30a218e42cb9", "size_in_bytes": 9499}, {"_path": "site-packages/humanfriendly/prompts.py", "path_type": "hardlink", "sha256": "f0f489d47a6bde577a62409a5d0b131c07a9f41b5329894a9908b54626872047", "sha256_in_prefix": "f0f489d47a6bde577a62409a5d0b131c07a9f41b5329894a9908b54626872047", "size_in_bytes": 16335}, {"_path": "site-packages/humanfriendly/sphinx.py", "path_type": "hardlink", "sha256": "06b1712bead7dcb37888caa0345b8e4f07987e9a58a7ef8edc7a96065bb4e4cd", "sha256_in_prefix": "06b1712bead7dcb37888caa0345b8e4f07987e9a58a7ef8edc7a96065bb4e4cd", "size_in_bytes": 11452}, {"_path": "site-packages/humanfriendly/tables.py", "path_type": "hardlink", "sha256": "9420e710acb26519ab207ac3954cf356983dcfa94a38ef2574fe1f8758333012", "sha256_in_prefix": "9420e710acb26519ab207ac3954cf356983dcfa94a38ef2574fe1f8758333012", "size_in_bytes": 13968}, {"_path": "site-packages/humanfriendly/terminal/__init__.py", "path_type": "hardlink", "sha256": "e41cf15472ab8b39dc9518eb5b060493697572dd2ad1676ea4922b92e23d8257", "sha256_in_prefix": "e41cf15472ab8b39dc9518eb5b060493697572dd2ad1676ea4922b92e23d8257", "size_in_bytes": 30759}, {"_path": "site-packages/humanfriendly/terminal/html.py", "path_type": "hardlink", "sha256": "fdcb146788480f4013c13bcf5d4f0aac07ba6730ff8144bbfed3771660870384", "sha256_in_prefix": "fdcb146788480f4013c13bcf5d4f0aac07ba6730ff8144bbfed3771660870384", "size_in_bytes": 16747}, {"_path": "site-packages/humanfriendly/terminal/spinners.py", "path_type": "hardlink", "sha256": "a3b9e49fcac1753aabf971c84fdef6b08808bb1883ca643990f56407347bbad1", "sha256_in_prefix": "a3b9e49fcac1753aabf971c84fdef6b08808bb1883ca643990f56407347bbad1", "size_in_bytes": 11323}, {"_path": "site-packages/humanfriendly/testing.py", "path_type": "hardlink", "sha256": "844aec9813790ab7a3e64d4ffc8d1d8502bc200c05af520e67d9103c8ad9532b", "sha256_in_prefix": "844aec9813790ab7a3e64d4ffc8d1d8502bc200c05af520e67d9103c8ad9532b", "size_in_bytes": 24359}, {"_path": "site-packages/humanfriendly/tests.py", "path_type": "hardlink", "sha256": "ddb91ab2b460c347007591478264e8e03507817127947b51a2afedd8c11a7db7", "sha256_in_prefix": "ddb91ab2b460c347007591478264e8e03507817127947b51a2afedd8c11a7db7", "size_in_bytes": 68919}, {"_path": "site-packages/humanfriendly/text.py", "path_type": "hardlink", "sha256": "fd6046e126786d3e521fde332d475f981e8d5dbbdce200581021264185c9d6c1", "sha256_in_prefix": "fd6046e126786d3e521fde332d475f981e8d5dbbdce200581021264185c9d6c1", "size_in_bytes": 16212}, {"_path": "site-packages/humanfriendly/usage.py", "path_type": "hardlink", "sha256": "0213e8e8372f68144814658d72d536d8c70cccddd1c9ce99082f3792dd996e4e", "sha256_in_prefix": "0213e8e8372f68144814658d72d536d8c70cccddd1c9ce99082f3792dd996e4e", "size_in_bytes": 13768}, {"_path": "lib/python3.11/site-packages/humanfriendly/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/humanfriendly/__pycache__/case.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/humanfriendly/__pycache__/cli.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/humanfriendly/__pycache__/compat.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/humanfriendly/__pycache__/decorators.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/humanfriendly/__pycache__/deprecation.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/humanfriendly/__pycache__/prompts.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/humanfriendly/__pycache__/sphinx.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/humanfriendly/__pycache__/tables.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/humanfriendly/terminal/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/humanfriendly/terminal/__pycache__/html.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/humanfriendly/terminal/__pycache__/spinners.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/humanfriendly/__pycache__/testing.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/humanfriendly/__pycache__/tests.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/humanfriendly/__pycache__/text.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/humanfriendly/__pycache__/usage.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "bin/humanfriendly", "path_type": "unix_python_entry_point"}], "paths_version": 1}, "requested_spec": "None", "sha256": "fa2071da7fab758c669e78227e6094f6b3608228740808a6de5d6bce83d9e52d", "size": 73563, "subdir": "noarch", "timestamp": 1733928021000, "url": "https://conda.anaconda.org/conda-forge/noarch/humanfriendly-10.0-pyh707e725_8.conda", "version": "10.0"}