{"build": "py311he02522f_0_cpu", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": ["apache-arrow-proc * cpu", "numpy >=1.21,<3"], "depends": ["__osx >=10.13", "libarrow 20.0.0.* *cpu", "libcxx >=18", "libzlib >=1.3.1,<2.0a0", "python >=3.11,<3.12.0a0", "python_abi 3.11.* *_cp311"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/pyarrow-core-20.0.0-py311he02522f_0_cpu", "files": ["lib/python3.11/site-packages/pyarrow-20.0.0-py3.11.egg-info/PKG-INFO", "lib/python3.11/site-packages/pyarrow-20.0.0-py3.11.egg-info/SOURCES.txt", "lib/python3.11/site-packages/pyarrow-20.0.0-py3.11.egg-info/dependency_links.txt", "lib/python3.11/site-packages/pyarrow-20.0.0-py3.11.egg-info/not-zip-safe", "lib/python3.11/site-packages/pyarrow-20.0.0-py3.11.egg-info/requires.txt", "lib/python3.11/site-packages/pyarrow-20.0.0-py3.11.egg-info/top_level.txt", "lib/python3.11/site-packages/pyarrow/__init__.pxd", "lib/python3.11/site-packages/pyarrow/__init__.py", "lib/python3.11/site-packages/pyarrow/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/_compute_docstrings.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/_generated_version.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/acero.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/benchmark.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/cffi.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/compute.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/conftest.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/csv.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/cuda.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/dataset.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/feather.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/flight.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/fs.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/ipc.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/json.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/jvm.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/orc.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/pandas_compat.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/substrait.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/types.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/__pycache__/util.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/_acero.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_acero.pxd", "lib/python3.11/site-packages/pyarrow/_acero.pyx", "lib/python3.11/site-packages/pyarrow/_azurefs.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_azurefs.pyx", "lib/python3.11/site-packages/pyarrow/_compute.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_compute.pxd", "lib/python3.11/site-packages/pyarrow/_compute.pyx", "lib/python3.11/site-packages/pyarrow/_compute_docstrings.py", "lib/python3.11/site-packages/pyarrow/_csv.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_csv.pxd", "lib/python3.11/site-packages/pyarrow/_csv.pyx", "lib/python3.11/site-packages/pyarrow/_cuda.pxd", "lib/python3.11/site-packages/pyarrow/_cuda.pyx", "lib/python3.11/site-packages/pyarrow/_dataset.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_dataset.pxd", "lib/python3.11/site-packages/pyarrow/_dataset.pyx", "lib/python3.11/site-packages/pyarrow/_dataset_orc.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_dataset_orc.pyx", "lib/python3.11/site-packages/pyarrow/_dataset_parquet.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_dataset_parquet.pxd", "lib/python3.11/site-packages/pyarrow/_dataset_parquet.pyx", "lib/python3.11/site-packages/pyarrow/_dataset_parquet_encryption.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_dataset_parquet_encryption.pyx", "lib/python3.11/site-packages/pyarrow/_dlpack.pxi", "lib/python3.11/site-packages/pyarrow/_feather.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_feather.pyx", "lib/python3.11/site-packages/pyarrow/_flight.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_flight.pyx", "lib/python3.11/site-packages/pyarrow/_fs.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_fs.pxd", "lib/python3.11/site-packages/pyarrow/_fs.pyx", "lib/python3.11/site-packages/pyarrow/_gcsfs.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_gcsfs.pyx", "lib/python3.11/site-packages/pyarrow/_generated_version.py", "lib/python3.11/site-packages/pyarrow/_hdfs.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_hdfs.pyx", "lib/python3.11/site-packages/pyarrow/_json.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_json.pxd", "lib/python3.11/site-packages/pyarrow/_json.pyx", "lib/python3.11/site-packages/pyarrow/_orc.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_orc.pxd", "lib/python3.11/site-packages/pyarrow/_orc.pyx", "lib/python3.11/site-packages/pyarrow/_parquet.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_parquet.pxd", "lib/python3.11/site-packages/pyarrow/_parquet.pyx", "lib/python3.11/site-packages/pyarrow/_parquet_encryption.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_parquet_encryption.pxd", "lib/python3.11/site-packages/pyarrow/_parquet_encryption.pyx", "lib/python3.11/site-packages/pyarrow/_pyarrow_cpp_tests.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_pyarrow_cpp_tests.pxd", "lib/python3.11/site-packages/pyarrow/_pyarrow_cpp_tests.pyx", "lib/python3.11/site-packages/pyarrow/_s3fs.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_s3fs.pyx", "lib/python3.11/site-packages/pyarrow/_substrait.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/_substrait.pyx", "lib/python3.11/site-packages/pyarrow/acero.py", "lib/python3.11/site-packages/pyarrow/array.pxi", "lib/python3.11/site-packages/pyarrow/benchmark.pxi", "lib/python3.11/site-packages/pyarrow/benchmark.py", "lib/python3.11/site-packages/pyarrow/builder.pxi", "lib/python3.11/site-packages/pyarrow/cffi.py", "lib/python3.11/site-packages/pyarrow/compat.pxi", "lib/python3.11/site-packages/pyarrow/compute.py", "lib/python3.11/site-packages/pyarrow/config.pxi", "lib/python3.11/site-packages/pyarrow/conftest.py", "lib/python3.11/site-packages/pyarrow/csv.py", "lib/python3.11/site-packages/pyarrow/cuda.py", "lib/python3.11/site-packages/pyarrow/dataset.py", "lib/python3.11/site-packages/pyarrow/device.pxi", "lib/python3.11/site-packages/pyarrow/error.pxi", "lib/python3.11/site-packages/pyarrow/feather.py", "lib/python3.11/site-packages/pyarrow/flight.py", "lib/python3.11/site-packages/pyarrow/fs.py", "lib/python3.11/site-packages/pyarrow/gandiva.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/gandiva.pyx", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/accumulation_queue.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/aggregate_node.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/api.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/asof_join_node.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/backpressure_handler.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/benchmark_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/bloom_filter.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/exec_plan.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/hash_join.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/hash_join_dict.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/hash_join_node.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/map_node.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/options.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/order_by_impl.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/partition_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/pch.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/query_context.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/schema_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/task_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/test_nodes.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/time_series_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/tpch_node.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/type_fwd.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/acero/visibility.h", "lib/python3.11/site-packages/pyarrow/include/arrow/adapters/orc/adapter.h", "lib/python3.11/site-packages/pyarrow/include/arrow/adapters/orc/options.h", "lib/python3.11/site-packages/pyarrow/include/arrow/api.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/array_base.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/array_binary.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/array_decimal.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/array_dict.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/array_nested.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/array_primitive.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/array_run_end.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_adaptive.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_base.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_binary.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_decimal.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_dict.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_nested.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_primitive.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_run_end.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_time.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_union.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/concatenate.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/data.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/diff.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/statistics.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/array/validate.h", "lib/python3.11/site-packages/pyarrow/include/arrow/buffer.h", "lib/python3.11/site-packages/pyarrow/include/arrow/buffer_builder.h", "lib/python3.11/site-packages/pyarrow/include/arrow/builder.h", "lib/python3.11/site-packages/pyarrow/include/arrow/c/abi.h", "lib/python3.11/site-packages/pyarrow/include/arrow/c/bridge.h", "lib/python3.11/site-packages/pyarrow/include/arrow/c/dlpack.h", "lib/python3.11/site-packages/pyarrow/include/arrow/c/dlpack_abi.h", "lib/python3.11/site-packages/pyarrow/include/arrow/c/helpers.h", "lib/python3.11/site-packages/pyarrow/include/arrow/chunk_resolver.h", "lib/python3.11/site-packages/pyarrow/include/arrow/chunked_array.h", "lib/python3.11/site-packages/pyarrow/include/arrow/compare.h", "lib/python3.11/site-packages/pyarrow/include/arrow/compute/api.h", "lib/python3.11/site-packages/pyarrow/include/arrow/compute/api_aggregate.h", "lib/python3.11/site-packages/pyarrow/include/arrow/compute/api_scalar.h", "lib/python3.11/site-packages/pyarrow/include/arrow/compute/api_vector.h", "lib/python3.11/site-packages/pyarrow/include/arrow/compute/cast.h", "lib/python3.11/site-packages/pyarrow/include/arrow/compute/exec.h", "lib/python3.11/site-packages/pyarrow/include/arrow/compute/expression.h", "lib/python3.11/site-packages/pyarrow/include/arrow/compute/function.h", "lib/python3.11/site-packages/pyarrow/include/arrow/compute/function_options.h", "lib/python3.11/site-packages/pyarrow/include/arrow/compute/kernel.h", "lib/python3.11/site-packages/pyarrow/include/arrow/compute/ordering.h", "lib/python3.11/site-packages/pyarrow/include/arrow/compute/registry.h", "lib/python3.11/site-packages/pyarrow/include/arrow/compute/row/grouper.h", "lib/python3.11/site-packages/pyarrow/include/arrow/compute/type_fwd.h", "lib/python3.11/site-packages/pyarrow/include/arrow/compute/util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/config.h", "lib/python3.11/site-packages/pyarrow/include/arrow/csv/api.h", "lib/python3.11/site-packages/pyarrow/include/arrow/csv/chunker.h", "lib/python3.11/site-packages/pyarrow/include/arrow/csv/column_builder.h", "lib/python3.11/site-packages/pyarrow/include/arrow/csv/column_decoder.h", "lib/python3.11/site-packages/pyarrow/include/arrow/csv/converter.h", "lib/python3.11/site-packages/pyarrow/include/arrow/csv/invalid_row.h", "lib/python3.11/site-packages/pyarrow/include/arrow/csv/options.h", "lib/python3.11/site-packages/pyarrow/include/arrow/csv/parser.h", "lib/python3.11/site-packages/pyarrow/include/arrow/csv/reader.h", "lib/python3.11/site-packages/pyarrow/include/arrow/csv/test_common.h", "lib/python3.11/site-packages/pyarrow/include/arrow/csv/type_fwd.h", "lib/python3.11/site-packages/pyarrow/include/arrow/csv/writer.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/api.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/dataset.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/dataset_writer.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/discovery.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/file_base.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/file_csv.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/file_ipc.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/file_json.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/file_orc.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/file_parquet.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/parquet_encryption_config.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/partition.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/pch.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/plan.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/projector.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/scanner.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/type_fwd.h", "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/visibility.h", "lib/python3.11/site-packages/pyarrow/include/arrow/datum.h", "lib/python3.11/site-packages/pyarrow/include/arrow/device.h", "lib/python3.11/site-packages/pyarrow/include/arrow/device_allocation_type_set.h", "lib/python3.11/site-packages/pyarrow/include/arrow/engine/api.h", "lib/python3.11/site-packages/pyarrow/include/arrow/engine/pch.h", "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/api.h", "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/extension_set.h", "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/extension_types.h", "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/options.h", "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/relation.h", "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/serde.h", "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/test_plan_builder.h", "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/test_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/type_fwd.h", "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/visibility.h", "lib/python3.11/site-packages/pyarrow/include/arrow/extension/bool8.h", "lib/python3.11/site-packages/pyarrow/include/arrow/extension/fixed_shape_tensor.h", "lib/python3.11/site-packages/pyarrow/include/arrow/extension/json.h", "lib/python3.11/site-packages/pyarrow/include/arrow/extension/opaque.h", "lib/python3.11/site-packages/pyarrow/include/arrow/extension/uuid.h", "lib/python3.11/site-packages/pyarrow/include/arrow/extension_type.h", "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/api.h", "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/azurefs.h", "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/filesystem.h", "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/filesystem_library.h", "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/gcsfs.h", "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/hdfs.h", "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/localfs.h", "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/mockfs.h", "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/path_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/s3_test_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/s3fs.h", "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/test_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/type_fwd.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/api.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/client.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/client_auth.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/client_cookie_middleware.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/client_middleware.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/client_tracing_middleware.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/middleware.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/otel_logging.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/pch.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/platform.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/server.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/server_auth.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/server_middleware.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/server_tracing_middleware.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/sql/api.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/sql/client.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/sql/column_metadata.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/sql/server.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/sql/server_session_middleware.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/sql/server_session_middleware_factory.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/sql/types.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/sql/visibility.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/test_auth_handlers.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/test_definitions.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/test_flight_server.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/test_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/transport.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/transport_server.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/type_fwd.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/types.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/types_async.h", "lib/python3.11/site-packages/pyarrow/include/arrow/flight/visibility.h", "lib/python3.11/site-packages/pyarrow/include/arrow/integration/json_integration.h", "lib/python3.11/site-packages/pyarrow/include/arrow/io/api.h", "lib/python3.11/site-packages/pyarrow/include/arrow/io/buffered.h", "lib/python3.11/site-packages/pyarrow/include/arrow/io/caching.h", "lib/python3.11/site-packages/pyarrow/include/arrow/io/compressed.h", "lib/python3.11/site-packages/pyarrow/include/arrow/io/concurrency.h", "lib/python3.11/site-packages/pyarrow/include/arrow/io/file.h", "lib/python3.11/site-packages/pyarrow/include/arrow/io/hdfs.h", "lib/python3.11/site-packages/pyarrow/include/arrow/io/interfaces.h", "lib/python3.11/site-packages/pyarrow/include/arrow/io/memory.h", "lib/python3.11/site-packages/pyarrow/include/arrow/io/mman.h", "lib/python3.11/site-packages/pyarrow/include/arrow/io/slow.h", "lib/python3.11/site-packages/pyarrow/include/arrow/io/stdio.h", "lib/python3.11/site-packages/pyarrow/include/arrow/io/test_common.h", "lib/python3.11/site-packages/pyarrow/include/arrow/io/transform.h", "lib/python3.11/site-packages/pyarrow/include/arrow/io/type_fwd.h", "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/api.h", "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/dictionary.h", "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/feather.h", "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/json_simple.h", "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/message.h", "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/options.h", "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/reader.h", "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/test_common.h", "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/type_fwd.h", "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/writer.h", "lib/python3.11/site-packages/pyarrow/include/arrow/json/api.h", "lib/python3.11/site-packages/pyarrow/include/arrow/json/chunked_builder.h", "lib/python3.11/site-packages/pyarrow/include/arrow/json/chunker.h", "lib/python3.11/site-packages/pyarrow/include/arrow/json/converter.h", "lib/python3.11/site-packages/pyarrow/include/arrow/json/object_parser.h", "lib/python3.11/site-packages/pyarrow/include/arrow/json/object_writer.h", "lib/python3.11/site-packages/pyarrow/include/arrow/json/options.h", "lib/python3.11/site-packages/pyarrow/include/arrow/json/parser.h", "lib/python3.11/site-packages/pyarrow/include/arrow/json/rapidjson_defs.h", "lib/python3.11/site-packages/pyarrow/include/arrow/json/reader.h", "lib/python3.11/site-packages/pyarrow/include/arrow/json/test_common.h", "lib/python3.11/site-packages/pyarrow/include/arrow/json/type_fwd.h", "lib/python3.11/site-packages/pyarrow/include/arrow/memory_pool.h", "lib/python3.11/site-packages/pyarrow/include/arrow/memory_pool_test.h", "lib/python3.11/site-packages/pyarrow/include/arrow/pch.h", "lib/python3.11/site-packages/pyarrow/include/arrow/pretty_print.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/api.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/arrow_to_pandas.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/async.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/benchmark.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/common.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/csv.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/datetime.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/decimal.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/extension_type.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/filesystem.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/flight.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/gdb.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/helpers.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/inference.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/io.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/ipc.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/iterators.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/lib.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/lib_api.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/numpy_convert.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/numpy_init.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/numpy_interop.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/numpy_to_arrow.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/parquet_encryption.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/pch.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/platform.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/pyarrow.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/pyarrow_api.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/pyarrow_lib.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/python_test.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/python_to_arrow.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/type_traits.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/udf.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/vendored/pythoncapi_compat.h", "lib/python3.11/site-packages/pyarrow/include/arrow/python/visibility.h", "lib/python3.11/site-packages/pyarrow/include/arrow/record_batch.h", "lib/python3.11/site-packages/pyarrow/include/arrow/result.h", "lib/python3.11/site-packages/pyarrow/include/arrow/scalar.h", "lib/python3.11/site-packages/pyarrow/include/arrow/sparse_tensor.h", "lib/python3.11/site-packages/pyarrow/include/arrow/status.h", "lib/python3.11/site-packages/pyarrow/include/arrow/stl.h", "lib/python3.11/site-packages/pyarrow/include/arrow/stl_allocator.h", "lib/python3.11/site-packages/pyarrow/include/arrow/stl_iterator.h", "lib/python3.11/site-packages/pyarrow/include/arrow/table.h", "lib/python3.11/site-packages/pyarrow/include/arrow/table_builder.h", "lib/python3.11/site-packages/pyarrow/include/arrow/telemetry/logging.h", "lib/python3.11/site-packages/pyarrow/include/arrow/tensor.h", "lib/python3.11/site-packages/pyarrow/include/arrow/tensor/converter.h", "lib/python3.11/site-packages/pyarrow/include/arrow/testing/async_test_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/testing/builder.h", "lib/python3.11/site-packages/pyarrow/include/arrow/testing/executor_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/testing/extension_type.h", "lib/python3.11/site-packages/pyarrow/include/arrow/testing/fixed_width_test_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/testing/future_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/testing/generator.h", "lib/python3.11/site-packages/pyarrow/include/arrow/testing/gtest_compat.h", "lib/python3.11/site-packages/pyarrow/include/arrow/testing/gtest_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/testing/matchers.h", "lib/python3.11/site-packages/pyarrow/include/arrow/testing/math.h", "lib/python3.11/site-packages/pyarrow/include/arrow/testing/pch.h", "lib/python3.11/site-packages/pyarrow/include/arrow/testing/process.h", "lib/python3.11/site-packages/pyarrow/include/arrow/testing/random.h", "lib/python3.11/site-packages/pyarrow/include/arrow/testing/uniform_real.h", "lib/python3.11/site-packages/pyarrow/include/arrow/testing/util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/testing/visibility.h", "lib/python3.11/site-packages/pyarrow/include/arrow/type.h", "lib/python3.11/site-packages/pyarrow/include/arrow/type_fwd.h", "lib/python3.11/site-packages/pyarrow/include/arrow/type_traits.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/algorithm.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/align_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/aligned_storage.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/async_generator.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/async_generator_fwd.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/async_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/base64.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/basic_decimal.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/benchmark_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/binary_view_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/bit_block_counter.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/bit_run_reader.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/bit_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/bitmap.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/bitmap_builders.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/bitmap_generate.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/bitmap_ops.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/bitmap_reader.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/bitmap_visit.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/bitmap_writer.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/bitset_stack.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/bpacking.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/bpacking64_default.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/bpacking_avx2.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/bpacking_avx512.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/bpacking_default.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/bpacking_neon.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/byte_size.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/cancel.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/checked_cast.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/compare.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/compression.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/concurrent_map.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/config.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/converter.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/counting_semaphore.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/cpu_info.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/crc32.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/debug.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/decimal.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/delimiting.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/dict_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/dispatch.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/double_conversion.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/endian.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/float16.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/formatting.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/functional.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/future.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/hash_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/hashing.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/int_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/int_util_overflow.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/io_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/iterator.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/key_value_metadata.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/launder.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/list_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/logger.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/logging.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/macros.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/map.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/math_constants.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/memory.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/mutex.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/parallel.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/pcg_random.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/prefetch.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/print.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/queue.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/range.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/ree_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/regex.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/rows_to_batches.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/simd.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/small_vector.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/sort.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/spaced.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/span.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/stopwatch.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/string.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/string_builder.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/task_group.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/tdigest.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/test_common.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/thread_pool.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/time.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/tracing.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/trie.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/type_fwd.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/type_traits.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/ubsan.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/union_util.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/unreachable.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/uri.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/utf8.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/value_parsing.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/vector.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/visibility.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/windows_compatibility.h", "lib/python3.11/site-packages/pyarrow/include/arrow/util/windows_fixup.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/ProducerConsumerQueue.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/datetime.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/datetime/date.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/datetime/ios.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/datetime/tz.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/datetime/tz_private.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/datetime/visibility.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/bignum-dtoa.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/bignum.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/cached-powers.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/diy-fp.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/double-conversion.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/double-to-string.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/fast-dtoa.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/fixed-dtoa.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/ieee.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/string-to-double.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/strtod.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/utils.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_extras.hpp", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_random.hpp", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_uint128.hpp", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/portable-snippets/debug-trap.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/portable-snippets/safe-math.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/strptime.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/xxhash.h", "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/xxhash/xxhash.h", "lib/python3.11/site-packages/pyarrow/include/arrow/visit_array_inline.h", "lib/python3.11/site-packages/pyarrow/include/arrow/visit_data_inline.h", "lib/python3.11/site-packages/pyarrow/include/arrow/visit_scalar_inline.h", "lib/python3.11/site-packages/pyarrow/include/arrow/visit_type_inline.h", "lib/python3.11/site-packages/pyarrow/include/arrow/visitor.h", "lib/python3.11/site-packages/pyarrow/include/arrow/visitor_generate.h", "lib/python3.11/site-packages/pyarrow/includes/__init__.pxd", "lib/python3.11/site-packages/pyarrow/includes/common.pxd", "lib/python3.11/site-packages/pyarrow/includes/libarrow.pxd", "lib/python3.11/site-packages/pyarrow/includes/libarrow_acero.pxd", "lib/python3.11/site-packages/pyarrow/includes/libarrow_cuda.pxd", "lib/python3.11/site-packages/pyarrow/includes/libarrow_dataset.pxd", "lib/python3.11/site-packages/pyarrow/includes/libarrow_dataset_parquet.pxd", "lib/python3.11/site-packages/pyarrow/includes/libarrow_feather.pxd", "lib/python3.11/site-packages/pyarrow/includes/libarrow_flight.pxd", "lib/python3.11/site-packages/pyarrow/includes/libarrow_fs.pxd", "lib/python3.11/site-packages/pyarrow/includes/libarrow_python.pxd", "lib/python3.11/site-packages/pyarrow/includes/libarrow_substrait.pxd", "lib/python3.11/site-packages/pyarrow/includes/libgandiva.pxd", "lib/python3.11/site-packages/pyarrow/includes/libparquet_encryption.pxd", "lib/python3.11/site-packages/pyarrow/interchange/__init__.py", "lib/python3.11/site-packages/pyarrow/interchange/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/interchange/__pycache__/buffer.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/interchange/__pycache__/column.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/interchange/__pycache__/dataframe.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/interchange/__pycache__/from_dataframe.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/interchange/buffer.py", "lib/python3.11/site-packages/pyarrow/interchange/column.py", "lib/python3.11/site-packages/pyarrow/interchange/dataframe.py", "lib/python3.11/site-packages/pyarrow/interchange/from_dataframe.py", "lib/python3.11/site-packages/pyarrow/io.pxi", "lib/python3.11/site-packages/pyarrow/ipc.pxi", "lib/python3.11/site-packages/pyarrow/ipc.py", "lib/python3.11/site-packages/pyarrow/json.py", "lib/python3.11/site-packages/pyarrow/jvm.py", "lib/python3.11/site-packages/pyarrow/lib.cpython-311-darwin.so", "lib/python3.11/site-packages/pyarrow/lib.h", "lib/python3.11/site-packages/pyarrow/lib.pxd", "lib/python3.11/site-packages/pyarrow/lib.pyx", "lib/python3.11/site-packages/pyarrow/lib_api.h", "lib/python3.11/site-packages/pyarrow/libarrow_python.2000.0.0.dylib", "lib/python3.11/site-packages/pyarrow/libarrow_python.2000.dylib", "lib/python3.11/site-packages/pyarrow/libarrow_python.dylib", "lib/python3.11/site-packages/pyarrow/libarrow_python_flight.2000.0.0.dylib", "lib/python3.11/site-packages/pyarrow/libarrow_python_flight.2000.dylib", "lib/python3.11/site-packages/pyarrow/libarrow_python_flight.dylib", "lib/python3.11/site-packages/pyarrow/libarrow_python_parquet_encryption.2000.0.0.dylib", "lib/python3.11/site-packages/pyarrow/libarrow_python_parquet_encryption.2000.dylib", "lib/python3.11/site-packages/pyarrow/libarrow_python_parquet_encryption.dylib", "lib/python3.11/site-packages/pyarrow/memory.pxi", "lib/python3.11/site-packages/pyarrow/orc.py", "lib/python3.11/site-packages/pyarrow/pandas-shim.pxi", "lib/python3.11/site-packages/pyarrow/pandas_compat.py", "lib/python3.11/site-packages/pyarrow/parquet/__init__.py", "lib/python3.11/site-packages/pyarrow/parquet/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/parquet/__pycache__/core.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/parquet/__pycache__/encryption.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/parquet/core.py", "lib/python3.11/site-packages/pyarrow/parquet/encryption.py", "lib/python3.11/site-packages/pyarrow/public-api.pxi", "lib/python3.11/site-packages/pyarrow/scalar.pxi", "lib/python3.11/site-packages/pyarrow/src/arrow/python/CMakeLists.txt", "lib/python3.11/site-packages/pyarrow/src/arrow/python/api.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/arrow_to_pandas.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/arrow_to_pandas.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/arrow_to_python_internal.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/async.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/benchmark.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/benchmark.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/common.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/common.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/csv.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/csv.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/datetime.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/datetime.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/decimal.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/decimal.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/extension_type.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/extension_type.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/filesystem.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/filesystem.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/flight.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/flight.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/gdb.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/gdb.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/helpers.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/helpers.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/inference.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/inference.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/io.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/io.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/ipc.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/ipc.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/iterators.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/numpy_convert.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/numpy_convert.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/numpy_init.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/numpy_init.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/numpy_internal.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/numpy_interop.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/numpy_to_arrow.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/numpy_to_arrow.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/parquet_encryption.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/parquet_encryption.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/pch.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/platform.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/pyarrow.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/pyarrow.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/pyarrow_api.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/pyarrow_lib.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/python_test.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/python_test.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/python_to_arrow.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/python_to_arrow.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/type_traits.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/udf.cc", "lib/python3.11/site-packages/pyarrow/src/arrow/python/udf.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/vendored/CMakeLists.txt", "lib/python3.11/site-packages/pyarrow/src/arrow/python/vendored/pythoncapi_compat.h", "lib/python3.11/site-packages/pyarrow/src/arrow/python/visibility.h", "lib/python3.11/site-packages/pyarrow/substrait.py", "lib/python3.11/site-packages/pyarrow/table.pxi", "lib/python3.11/site-packages/pyarrow/tensor.pxi", "lib/python3.11/site-packages/pyarrow/types.pxi", "lib/python3.11/site-packages/pyarrow/types.py", "lib/python3.11/site-packages/pyarrow/util.py", "lib/python3.11/site-packages/pyarrow/vendored/__init__.py", "lib/python3.11/site-packages/pyarrow/vendored/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/vendored/__pycache__/docscrape.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/vendored/__pycache__/version.cpython-311.pyc", "lib/python3.11/site-packages/pyarrow/vendored/docscrape.py", "lib/python3.11/site-packages/pyarrow/vendored/version.py"], "fn": "pyarrow-core-20.0.0-py311he02522f_0_cpu.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/pyarrow-core-20.0.0-py311he02522f_0_cpu", "type": 1}, "md5": "1f3da24b20299735c80fd3ec083e27dc", "name": "pyarrow-core", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/pyarrow-core-20.0.0-py311he02522f_0_cpu.conda", "paths_data": {"paths": [{"_path": "lib/python3.11/site-packages/pyarrow-20.0.0-py3.11.egg-info/PKG-INFO", "path_type": "hardlink", "sha256": "efc7951a4d6c7f08e6b898fb5661668ca7fcc47046d5c9ae033b4699271b3ddf", "sha256_in_prefix": "efc7951a4d6c7f08e6b898fb5661668ca7fcc47046d5c9ae033b4699271b3ddf", "size_in_bytes": 3303}, {"_path": "lib/python3.11/site-packages/pyarrow-20.0.0-py3.11.egg-info/SOURCES.txt", "path_type": "hardlink", "sha256": "58c466095d9478051d618d6f50dd9b4a42bf70ded91294982545b0885b4925e1", "sha256_in_prefix": "58c466095d9478051d618d6f50dd9b4a42bf70ded91294982545b0885b4925e1", "size_in_bytes": 9344}, {"_path": "lib/python3.11/site-packages/pyarrow-20.0.0-py3.11.egg-info/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "lib/python3.11/site-packages/pyarrow-20.0.0-py3.11.egg-info/not-zip-safe", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "lib/python3.11/site-packages/pyarrow-20.0.0-py3.11.egg-info/requires.txt", "path_type": "hardlink", "sha256": "ea632c4fff37476cb896f9628ee121a8bc1e0a10c35ccd16c04717b57b783cea", "sha256_in_prefix": "ea632c4fff37476cb896f9628ee121a8bc1e0a10c35ccd16c04717b57b783cea", "size_in_bytes": 43}, {"_path": "lib/python3.11/site-packages/pyarrow-20.0.0-py3.11.egg-info/top_level.txt", "path_type": "hardlink", "sha256": "66e93f73559e8a75dd333db47d7944b460baef37ca396bb053c69da44114fe72", "sha256_in_prefix": "66e93f73559e8a75dd333db47d7944b460baef37ca396bb053c69da44114fe72", "size_in_bytes": 18}, {"_path": "lib/python3.11/site-packages/pyarrow/__init__.pxd", "path_type": "hardlink", "sha256": "5a76abd69845a8cfd91e7666b5bbaa9bac091ec5e506860a855ed09a8da35070", "sha256_in_prefix": "5a76abd69845a8cfd91e7666b5bbaa9bac091ec5e506860a855ed09a8da35070", "size_in_bytes": 2195}, {"_path": "lib/python3.11/site-packages/pyarrow/__init__.py", "path_type": "hardlink", "sha256": "d0f7566577662eb70b14ddaa6f2be67223e776bc0cc1793b6534a019d3876310", "sha256_in_prefix": "d0f7566577662eb70b14ddaa6f2be67223e776bc0cc1793b6534a019d3876310", "size_in_bytes": 18448}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "1be2fb0b25fcc2f687d0ab671f8e7ef01d87a17d86567c21f554599c965809f5", "sha256_in_prefix": "1be2fb0b25fcc2f687d0ab671f8e7ef01d87a17d86567c21f554599c965809f5", "size_in_bytes": 26006}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/_compute_docstrings.cpython-311.pyc", "path_type": "hardlink", "sha256": "c716222ee14a961381aaa4421a236bea1db157c4ed6adafbecb189d1df3cba90", "sha256_in_prefix": "c716222ee14a961381aaa4421a236bea1db157c4ed6adafbecb189d1df3cba90", "size_in_bytes": 1093}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/_generated_version.cpython-311.pyc", "path_type": "hardlink", "sha256": "8beb0fd6adf9ef7163d353e415681752b42c7cde990c42c942bcbfbeab76118e", "sha256_in_prefix": "8beb0fd6adf9ef7163d353e415681752b42c7cde990c42c942bcbfbeab76118e", "size_in_bytes": 700}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/acero.cpython-311.pyc", "path_type": "hardlink", "sha256": "c136f5d71fb72fd22bd50f14aabc63f815a6239df908764e5e54d91502be3b2b", "sha256_in_prefix": "c136f5d71fb72fd22bd50f14aabc63f815a6239df908764e5e54d91502be3b2b", "size_in_bytes": 15592}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/benchmark.cpython-311.pyc", "path_type": "hardlink", "sha256": "d2b1522e1c041348ebf2a965e526b31697e745682bfd290cb6c004d95350e714", "sha256_in_prefix": "d2b1522e1c041348ebf2a965e526b31697e745682bfd290cb6c004d95350e714", "size_in_bytes": 238}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/cffi.cpython-311.pyc", "path_type": "hardlink", "sha256": "6664b15dcc61cc42cefd2d635d9770fe2e1544bf617943ff86f01af9c69301ae", "sha256_in_prefix": "6664b15dcc61cc42cefd2d635d9770fe2e1544bf617943ff86f01af9c69301ae", "size_in_bytes": 1826}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/compute.cpython-311.pyc", "path_type": "hardlink", "sha256": "fbaf0f49eb038d289b9ba043f7ef3af0060f0a3d7458d2e57dc24ee795b34f4e", "sha256_in_prefix": "fbaf0f49eb038d289b9ba043f7ef3af0060f0a3d7458d2e57dc24ee795b34f4e", "size_in_bytes": 29477}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/conftest.cpython-311.pyc", "path_type": "hardlink", "sha256": "37e2e84b0aea2bb20c7ce5ad004c35a0e0d3bdaa37cac987c7f685b9c60ad9f9", "sha256_in_prefix": "37e2e84b0aea2bb20c7ce5ad004c35a0e0d3bdaa37cac987c7f685b9c60ad9f9", "size_in_bytes": 11054}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/csv.cpython-311.pyc", "path_type": "hardlink", "sha256": "a162056cd3e108b56ea95d342583ef4835a2a296487be1511cce6a374d2096da", "sha256_in_prefix": "a162056cd3e108b56ea95d342583ef4835a2a296487be1511cce6a374d2096da", "size_in_bytes": 547}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/cuda.cpython-311.pyc", "path_type": "hardlink", "sha256": "5b05281e02e773d0655b3bd1703d269738efe266d58db3714d11bcbdcd791577", "sha256_in_prefix": "5b05281e02e773d0655b3bd1703d269738efe266d58db3714d11bcbdcd791577", "size_in_bytes": 539}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/dataset.cpython-311.pyc", "path_type": "hardlink", "sha256": "4dd38b47852b7b2a10421c2dfd8c0d62bc5fc04ffd4b697e9898b234a3c465ba", "sha256_in_prefix": "4dd38b47852b7b2a10421c2dfd8c0d62bc5fc04ffd4b697e9898b234a3c465ba", "size_in_bytes": 42866}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/feather.cpython-311.pyc", "path_type": "hardlink", "sha256": "013fa02a767480fb337e95a5fcc6534af1e7167d949e2bad83e1921f79452b3b", "sha256_in_prefix": "013fa02a767480fb337e95a5fcc6534af1e7167d949e2bad83e1921f79452b3b", "size_in_bytes": 11740}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/flight.cpython-311.pyc", "path_type": "hardlink", "sha256": "45c94135d24c71577d65876b4d04e7b9bf5dfa83cd84964fa01c95fed055af64", "sha256_in_prefix": "45c94135d24c71577d65876b4d04e7b9bf5dfa83cd84964fa01c95fed055af64", "size_in_bytes": 2158}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/fs.cpython-311.pyc", "path_type": "hardlink", "sha256": "0adb2c50dd5c1a704a900502a345c0eec0e16e3ebe35c8df371f3815d56de8ea", "sha256_in_prefix": "0adb2c50dd5c1a704a900502a345c0eec0e16e3ebe35c8df371f3815d56de8ea", "size_in_bytes": 18381}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/ipc.cpython-311.pyc", "path_type": "hardlink", "sha256": "7fc19f1e9f0bdd269c7ed833aded11bd5d5833971e68171b40dbb6cf5a0016b5", "sha256_in_prefix": "7fc19f1e9f0bdd269c7ed833aded11bd5d5833971e68171b40dbb6cf5a0016b5", "size_in_bytes": 12334}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/json.cpython-311.pyc", "path_type": "hardlink", "sha256": "875fb2c4d7b5ebeb3fa4b1e1c7d68cf050ceaae64388184656c8932003a9cc6d", "sha256_in_prefix": "875fb2c4d7b5ebeb3fa4b1e1c7d68cf050ceaae64388184656c8932003a9cc6d", "size_in_bytes": 300}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/jvm.cpython-311.pyc", "path_type": "hardlink", "sha256": "175c79ec1842e95ba2ad1c56b58f076eb0278d3b8bd60edc8e3651cd19e7d2b6", "sha256_in_prefix": "175c79ec1842e95ba2ad1c56b58f076eb0278d3b8bd60edc8e3651cd19e7d2b6", "size_in_bytes": 13964}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/orc.cpython-311.pyc", "path_type": "hardlink", "sha256": "bdf4c3febd84431592f734365057b932e9f3d542c00ba454970eccdfbbe29937", "sha256_in_prefix": "bdf4c3febd84431592f734365057b932e9f3d542c00ba454970eccdfbbe29937", "size_in_bytes": 15693}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/pandas_compat.cpython-311.pyc", "path_type": "hardlink", "sha256": "fffe9c864835591d643e4e7aa2b42f5cde0de7362a7ac7b6741cbd556c8b363c", "sha256_in_prefix": "fffe9c864835591d643e4e7aa2b42f5cde0de7362a7ac7b6741cbd556c8b363c", "size_in_bytes": 51420}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/substrait.cpython-311.pyc", "path_type": "hardlink", "sha256": "0dc76e4eb1f96ea4f7370d35ec30dfeda3501e6cf54f25e41c7ecd9120c75f04", "sha256_in_prefix": "0dc76e4eb1f96ea4f7370d35ec30dfeda3501e6cf54f25e41c7ecd9120c75f04", "size_in_bytes": 803}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/types.cpython-311.pyc", "path_type": "hardlink", "sha256": "68a335e25b5648f63f90aa4d2eac6b228deaebea88a8be2160c797c1ddb6beab", "sha256_in_prefix": "68a335e25b5648f63f90aa4d2eac6b228deaebea88a8be2160c797c1ddb6beab", "size_in_bytes": 15354}, {"_path": "lib/python3.11/site-packages/pyarrow/__pycache__/util.cpython-311.pyc", "path_type": "hardlink", "sha256": "bc5b51fd36a4d448539d314cd7c723a4adfb7469562f0330f91278030b8e68c9", "sha256_in_prefix": "bc5b51fd36a4d448539d314cd7c723a4adfb7469562f0330f91278030b8e68c9", "size_in_bytes": 12679}, {"_path": "lib/python3.11/site-packages/pyarrow/_acero.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "46d81ede985329c1863ecadff199a554653b972e787670540ef18208c65041f5", "sha256_in_prefix": "46d81ede985329c1863ecadff199a554653b972e787670540ef18208c65041f5", "size_in_bytes": 312392}, {"_path": "lib/python3.11/site-packages/pyarrow/_acero.pxd", "path_type": "hardlink", "sha256": "e62b21fc68065af8ade1e6e1ce866297b6fe9b4af646e1b9270628c6c1f7e052", "sha256_in_prefix": "e62b21fc68065af8ade1e6e1ce866297b6fe9b4af646e1b9270628c6c1f7e052", "size_in_bytes": 1440}, {"_path": "lib/python3.11/site-packages/pyarrow/_acero.pyx", "path_type": "hardlink", "sha256": "559d4da9184c7b17c5c83ae0b0ad37555601d533e9cd0734d874fe8aa94b8b0b", "sha256_in_prefix": "559d4da9184c7b17c5c83ae0b0ad37555601d533e9cd0734d874fe8aa94b8b0b", "size_in_bytes": 21139}, {"_path": "lib/python3.11/site-packages/pyarrow/_azurefs.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "070c5b79bc56b2b1c48be1047eb18e540e5a36c8002c849d98fbe5f2f10d662a", "sha256_in_prefix": "070c5b79bc56b2b1c48be1047eb18e540e5a36c8002c849d98fbe5f2f10d662a", "size_in_bytes": 100048}, {"_path": "lib/python3.11/site-packages/pyarrow/_azurefs.pyx", "path_type": "hardlink", "sha256": "68b1e792e8cd3802be923f690e44855c4a45ef06418949bcedb16df8173c3f4b", "sha256_in_prefix": "68b1e792e8cd3802be923f690e44855c4a45ef06418949bcedb16df8173c3f4b", "size_in_bytes": 6575}, {"_path": "lib/python3.11/site-packages/pyarrow/_compute.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "0864f5a597e939afdf9095cad6fe2ad27d90103f75c460ccd2ae77cb034feb4d", "sha256_in_prefix": "0864f5a597e939afdf9095cad6fe2ad27d90103f75c460ccd2ae77cb034feb4d", "size_in_bytes": 1254296}, {"_path": "lib/python3.11/site-packages/pyarrow/_compute.pxd", "path_type": "hardlink", "sha256": "7e18bcee390deb1e2a1bc25b02d464b31fb86139f7b05230db3671b8ad5f9693", "sha256_in_prefix": "7e18bcee390deb1e2a1bc25b02d464b31fb86139f7b05230db3671b8ad5f9693", "size_in_bytes": 2022}, {"_path": "lib/python3.11/site-packages/pyarrow/_compute.pyx", "path_type": "hardlink", "sha256": "8fc8a48a869b7e6568f2abbb4496cb9e8057cec1fb1f70d406a860bb6992e524", "sha256_in_prefix": "8fc8a48a869b7e6568f2abbb4496cb9e8057cec1fb1f70d406a860bb6992e524", "size_in_bytes": 111609}, {"_path": "lib/python3.11/site-packages/pyarrow/_compute_docstrings.py", "path_type": "hardlink", "sha256": "ed583c8edd5a0ac5aba53c6c76a47b818e8cd1f6b15cd5f7d5cd5165dabd085c", "sha256_in_prefix": "ed583c8edd5a0ac5aba53c6c76a47b818e8cd1f6b15cd5f7d5cd5165dabd085c", "size_in_bytes": 1707}, {"_path": "lib/python3.11/site-packages/pyarrow/_csv.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "bf292ffd57b88984b7916ec32a09a2eeea6e4d4a053eda8ca03e61689b535751", "sha256_in_prefix": "bf292ffd57b88984b7916ec32a09a2eeea6e4d4a053eda8ca03e61689b535751", "size_in_bytes": 332600}, {"_path": "lib/python3.11/site-packages/pyarrow/_csv.pxd", "path_type": "hardlink", "sha256": "d59937669bef872f936fb73bf40a9de1eedb04cdb591cd49c56264974d98e031", "sha256_in_prefix": "d59937669bef872f936fb73bf40a9de1eedb04cdb591cd49c56264974d98e031", "size_in_bytes": 1638}, {"_path": "lib/python3.11/site-packages/pyarrow/_csv.pyx", "path_type": "hardlink", "sha256": "6ebd3296185e6292bd529c5ac71ff2d34a06d3c1ec0034390d83374a7b4f337c", "sha256_in_prefix": "6ebd3296185e6292bd529c5ac71ff2d34a06d3c1ec0034390d83374a7b4f337c", "size_in_bytes": 54711}, {"_path": "lib/python3.11/site-packages/pyarrow/_cuda.pxd", "path_type": "hardlink", "sha256": "57384cea3f5da4d82b00196f1492a8329442d00b39e629be337b603d33aec049", "sha256_in_prefix": "57384cea3f5da4d82b00196f1492a8329442d00b39e629be337b603d33aec049", "size_in_bytes": 1922}, {"_path": "lib/python3.11/site-packages/pyarrow/_cuda.pyx", "path_type": "hardlink", "sha256": "61296cc27e138f51f6e122ff88f206a93defdc99a87c4ecdc02ba7b8e2cc3706", "sha256_in_prefix": "61296cc27e138f51f6e122ff88f206a93defdc99a87c4ecdc02ba7b8e2cc3706", "size_in_bytes": 35136}, {"_path": "lib/python3.11/site-packages/pyarrow/_dataset.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "8e9ea99ca6a67236f21095fbed422fd6b369a34f933e3b8b78eabecee0b0ea19", "sha256_in_prefix": "8e9ea99ca6a67236f21095fbed422fd6b369a34f933e3b8b78eabecee0b0ea19", "size_in_bytes": 1036896}, {"_path": "lib/python3.11/site-packages/pyarrow/_dataset.pxd", "path_type": "hardlink", "sha256": "020f6b521a01c9253a6dadf014b7ae67258c267cfd56401ff53404cd61b88545", "sha256_in_prefix": "020f6b521a01c9253a6dadf014b7ae67258c267cfd56401ff53404cd61b88545", "size_in_bytes": 4944}, {"_path": "lib/python3.11/site-packages/pyarrow/_dataset.pyx", "path_type": "hardlink", "sha256": "724ad83225eaac00241e7b4cd8c73c9b058eb2ccf9a759fce23466fdf908f38f", "sha256_in_prefix": "724ad83225eaac00241e7b4cd8c73c9b058eb2ccf9a759fce23466fdf908f38f", "size_in_bytes": 162731}, {"_path": "lib/python3.11/site-packages/pyarrow/_dataset_orc.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "540ea19227b29e500495be2ced91df4582c6600f277d0a05f21ed606ec5b732f", "sha256_in_prefix": "540ea19227b29e500495be2ced91df4582c6600f277d0a05f21ed606ec5b732f", "size_in_bytes": 72624}, {"_path": "lib/python3.11/site-packages/pyarrow/_dataset_orc.pyx", "path_type": "hardlink", "sha256": "252168448d297c7b4bda3788b8f8394c91e875c7ee08d6268ff884678c58f3bc", "sha256_in_prefix": "252168448d297c7b4bda3788b8f8394c91e875c7ee08d6268ff884678c58f3bc", "size_in_bytes": 1499}, {"_path": "lib/python3.11/site-packages/pyarrow/_dataset_parquet.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "5414add61868c5b8832c023e077929d2b172a6ff373b746ed6d5b3402661fd0b", "sha256_in_prefix": "5414add61868c5b8832c023e077929d2b172a6ff373b746ed6d5b3402661fd0b", "size_in_bytes": 333200}, {"_path": "lib/python3.11/site-packages/pyarrow/_dataset_parquet.pxd", "path_type": "hardlink", "sha256": "cbede229e87207f781fe87aa8edb7869045b52954654dd6850c146218d776eb1", "sha256_in_prefix": "cbede229e87207f781fe87aa8edb7869045b52954654dd6850c146218d776eb1", "size_in_bytes": 1572}, {"_path": "lib/python3.11/site-packages/pyarrow/_dataset_parquet.pyx", "path_type": "hardlink", "sha256": "f7f67ddd41abbb0636c2e2478169ca4dee691d389ade81a574473b0ff22da8d0", "sha256_in_prefix": "f7f67ddd41abbb0636c2e2478169ca4dee691d389ade81a574473b0ff22da8d0", "size_in_bytes": 38882}, {"_path": "lib/python3.11/site-packages/pyarrow/_dataset_parquet_encryption.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "b1b751b52cb73f61fb5ebc108875deece2d4aa5a8430d5b479c5e75a2842e51f", "sha256_in_prefix": "b1b751b52cb73f61fb5ebc108875deece2d4aa5a8430d5b479c5e75a2842e51f", "size_in_bytes": 103296}, {"_path": "lib/python3.11/site-packages/pyarrow/_dataset_parquet_encryption.pyx", "path_type": "hardlink", "sha256": "a7b2c3354b29de33157160dc6ce169f1adc26098c04953c8fed7c04e963e78f8", "sha256_in_prefix": "a7b2c3354b29de33157160dc6ce169f1adc26098c04953c8fed7c04e963e78f8", "size_in_bytes": 7229}, {"_path": "lib/python3.11/site-packages/pyarrow/_dlpack.pxi", "path_type": "hardlink", "sha256": "725c341641a8c99304b5453cccfa4efc332d9765fedbb91bd94b72850b88735b", "sha256_in_prefix": "725c341641a8c99304b5453cccfa4efc332d9765fedbb91bd94b72850b88735b", "size_in_bytes": 1832}, {"_path": "lib/python3.11/site-packages/pyarrow/_feather.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "3ca44f4deb007b35da97af5d489f66716db680d459ed4346e1dd076bb13e0e50", "sha256_in_prefix": "3ca44f4deb007b35da97af5d489f66716db680d459ed4346e1dd076bb13e0e50", "size_in_bytes": 103096}, {"_path": "lib/python3.11/site-packages/pyarrow/_feather.pyx", "path_type": "hardlink", "sha256": "0d6408e14d2e0161356585303eb7813c98354c62c41a6177c0f10844bf8f84fc", "sha256_in_prefix": "0d6408e14d2e0161356585303eb7813c98354c62c41a6177c0f10844bf8f84fc", "size_in_bytes": 3773}, {"_path": "lib/python3.11/site-packages/pyarrow/_flight.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "bb60784ef25a063abb11d08f585733ff729e3aa9b3945aa7d4a99e5e96bfd15e", "sha256_in_prefix": "bb60784ef25a063abb11d08f585733ff729e3aa9b3945aa7d4a99e5e96bfd15e", "size_in_bytes": 1230480}, {"_path": "lib/python3.11/site-packages/pyarrow/_flight.pyx", "path_type": "hardlink", "sha256": "d097fc295f2e74ffe7a35244801090e319487b5be9b7580beece95b874c1686d", "sha256_in_prefix": "d097fc295f2e74ffe7a35244801090e319487b5be9b7580beece95b874c1686d", "size_in_bytes": 114138}, {"_path": "lib/python3.11/site-packages/pyarrow/_fs.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "6223377d192b73d19a5628712ee22407630877c7d64ed247428ea7bf1bb9d98f", "sha256_in_prefix": "6223377d192b73d19a5628712ee22407630877c7d64ed247428ea7bf1bb9d98f", "size_in_bytes": 488112}, {"_path": "lib/python3.11/site-packages/pyarrow/_fs.pxd", "path_type": "hardlink", "sha256": "4a61d2df57b2614ed5519955b8feb5dc72a0a5dedb10d9d0180a6f5ff8362dfc", "sha256_in_prefix": "4a61d2df57b2614ed5519955b8feb5dc72a0a5dedb10d9d0180a6f5ff8362dfc", "size_in_bytes": 2439}, {"_path": "lib/python3.11/site-packages/pyarrow/_fs.pyx", "path_type": "hardlink", "sha256": "cd61bc289c5a35da4c4282fc87aa54d5cdc89301cfe38c43b9c7b402896d6f62", "sha256_in_prefix": "cd61bc289c5a35da4c4282fc87aa54d5cdc89301cfe38c43b9c7b402896d6f62", "size_in_bytes": 52405}, {"_path": "lib/python3.11/site-packages/pyarrow/_gcsfs.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "bbc70a1220ef40ab3bf575de398f5a94072467d18e883b571f13242c0bd84987", "sha256_in_prefix": "bbc70a1220ef40ab3bf575de398f5a94072467d18e883b571f13242c0bd84987", "size_in_bytes": 111976}, {"_path": "lib/python3.11/site-packages/pyarrow/_gcsfs.pyx", "path_type": "hardlink", "sha256": "b28a08df3b731105d554a71a8608a7d141a6ffa4a0cbebb07dc32b0023faf74c", "sha256_in_prefix": "b28a08df3b731105d554a71a8608a7d141a6ffa4a0cbebb07dc32b0023faf74c", "size_in_bytes": 9046}, {"_path": "lib/python3.11/site-packages/pyarrow/_generated_version.py", "path_type": "hardlink", "sha256": "f41098d59a2062e3c941f40941301c83b1ee173bb5d0560543bf95f53ec0c281", "sha256_in_prefix": "f41098d59a2062e3c941f40941301c83b1ee173bb5d0560543bf95f53ec0c281", "size_in_bytes": 513}, {"_path": "lib/python3.11/site-packages/pyarrow/_hdfs.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "0c2a2d89645c0c8b29e4dae492bd886b5c0101ef0e23adc5216a5bf3ada011f1", "sha256_in_prefix": "0c2a2d89645c0c8b29e4dae492bd886b5c0101ef0e23adc5216a5bf3ada011f1", "size_in_bytes": 122056}, {"_path": "lib/python3.11/site-packages/pyarrow/_hdfs.pyx", "path_type": "hardlink", "sha256": "81f4b0810c317035838fb9a03b0cee1c28c43980b0ccab803e9f01fb60bc70b1", "sha256_in_prefix": "81f4b0810c317035838fb9a03b0cee1c28c43980b0ccab803e9f01fb60bc70b1", "size_in_bytes": 5810}, {"_path": "lib/python3.11/site-packages/pyarrow/_json.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "310d7d12e47e47ffa5c8ecdc4b4033655146c845ecb1a59f1af874e5b59e8eb8", "sha256_in_prefix": "310d7d12e47e47ffa5c8ecdc4b4033655146c845ecb1a59f1af874e5b59e8eb8", "size_in_bytes": 120816}, {"_path": "lib/python3.11/site-packages/pyarrow/_json.pxd", "path_type": "hardlink", "sha256": "b440933f5e0cd76f9bfe36b9408dec9d06ddd2e58f5a6982f45c245aadb7560d", "sha256_in_prefix": "b440933f5e0cd76f9bfe36b9408dec9d06ddd2e58f5a6982f45c245aadb7560d", "size_in_bytes": 1206}, {"_path": "lib/python3.11/site-packages/pyarrow/_json.pyx", "path_type": "hardlink", "sha256": "3a2876bb995000648d7397ce5f140ab4aeba4925e0bf6b9cb213145a978c3e3a", "sha256_in_prefix": "3a2876bb995000648d7397ce5f140ab4aeba4925e0bf6b9cb213145a978c3e3a", "size_in_bytes": 12539}, {"_path": "lib/python3.11/site-packages/pyarrow/_orc.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "f81f830390c96f4a486c859649cea0447221a74d399110280087e1549d6d0741", "sha256_in_prefix": "f81f830390c96f4a486c859649cea0447221a74d399110280087e1549d6d0741", "size_in_bytes": 185552}, {"_path": "lib/python3.11/site-packages/pyarrow/_orc.pxd", "path_type": "hardlink", "sha256": "ea12f472ad51b9fa900fe07f6d5de77b5ae1bb683e878ac3385350b126faaa9b", "sha256_in_prefix": "ea12f472ad51b9fa900fe07f6d5de77b5ae1bb683e878ac3385350b126faaa9b", "size_in_bytes": 5689}, {"_path": "lib/python3.11/site-packages/pyarrow/_orc.pyx", "path_type": "hardlink", "sha256": "3e7eebe1dcda8166aa31ff2bca66d70485a2b31a2705a4ad6605c28bba5fad92", "sha256_in_prefix": "3e7eebe1dcda8166aa31ff2bca66d70485a2b31a2705a4ad6605c28bba5fad92", "size_in_bytes": 15556}, {"_path": "lib/python3.11/site-packages/pyarrow/_parquet.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "c9d3fb6b0690e3a6de5481fbfba933d18adec3e9423c568285ea8c9210edf779", "sha256_in_prefix": "c9d3fb6b0690e3a6de5481fbfba933d18adec3e9423c568285ea8c9210edf779", "size_in_bytes": 514880}, {"_path": "lib/python3.11/site-packages/pyarrow/_parquet.pxd", "path_type": "hardlink", "sha256": "8417dbda5b47bb6756f90a9cbdb35adb535f83800bcd71490f16717ecfba6f05", "sha256_in_prefix": "8417dbda5b47bb6756f90a9cbdb35adb535f83800bcd71490f16717ecfba6f05", "size_in_bytes": 27013}, {"_path": "lib/python3.11/site-packages/pyarrow/_parquet.pyx", "path_type": "hardlink", "sha256": "1de466d48fa219512d201a5b73952248ba08ecbd8c4e85b775883e6203565300", "sha256_in_prefix": "1de466d48fa219512d201a5b73952248ba08ecbd8c4e85b775883e6203565300", "size_in_bytes": 73843}, {"_path": "lib/python3.11/site-packages/pyarrow/_parquet_encryption.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "ea08fa20b22e94b53d9821c363a344ce393dc18111e9f24646a395ec81244a10", "sha256_in_prefix": "ea08fa20b22e94b53d9821c363a344ce393dc18111e9f24646a395ec81244a10", "size_in_bytes": 259632}, {"_path": "lib/python3.11/site-packages/pyarrow/_parquet_encryption.pxd", "path_type": "hardlink", "sha256": "d6f4279324b5acbad234c966b96eb63f190e982b18a730bad0bf66a83d3f2d87", "sha256_in_prefix": "d6f4279324b5acbad234c966b96eb63f190e982b18a730bad0bf66a83d3f2d87", "size_in_bytes": 2586}, {"_path": "lib/python3.11/site-packages/pyarrow/_parquet_encryption.pyx", "path_type": "hardlink", "sha256": "6b0cc0a63f237996fd6b8e5c78d5e1f999206bae22ca31dd16096d480cddd2d8", "sha256_in_prefix": "6b0cc0a63f237996fd6b8e5c78d5e1f999206bae22ca31dd16096d480cddd2d8", "size_in_bytes": 18588}, {"_path": "lib/python3.11/site-packages/pyarrow/_pyarrow_cpp_tests.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "1ea65b955dc2585690594915e3c0572614b015172b6f57f0138fcb862cd83f03", "sha256_in_prefix": "1ea65b955dc2585690594915e3c0572614b015172b6f57f0138fcb862cd83f03", "size_in_bytes": 81608}, {"_path": "lib/python3.11/site-packages/pyarrow/_pyarrow_cpp_tests.pxd", "path_type": "hardlink", "sha256": "9cfc9198db456ce52f4970b07a0000a5015f87c508fcaf47ab974de283c0c5da", "sha256_in_prefix": "9cfc9198db456ce52f4970b07a0000a5015f87c508fcaf47ab974de283c0c5da", "size_in_bytes": 1199}, {"_path": "lib/python3.11/site-packages/pyarrow/_pyarrow_cpp_tests.pyx", "path_type": "hardlink", "sha256": "80b78ccc1f515a87598171298a95fae7fd1aa96bb5d928cc95dd0966665544fd", "sha256_in_prefix": "80b78ccc1f515a87598171298a95fae7fd1aa96bb5d928cc95dd0966665544fd", "size_in_bytes": 1753}, {"_path": "lib/python3.11/site-packages/pyarrow/_s3fs.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "ce184e11a64dddfd3f626025a65609ffe67091f4bb187d3ab00b976a5f4d2f68", "sha256_in_prefix": "ce184e11a64dddfd3f626025a65609ffe67091f4bb187d3ab00b976a5f4d2f68", "size_in_bytes": 208128}, {"_path": "lib/python3.11/site-packages/pyarrow/_s3fs.pyx", "path_type": "hardlink", "sha256": "cceb488ce45ce6837c4f37d65dbce5b6182f5332ede022ae952feb46168be283", "sha256_in_prefix": "cceb488ce45ce6837c4f37d65dbce5b6182f5332ede022ae952feb46168be283", "size_in_bytes": 19631}, {"_path": "lib/python3.11/site-packages/pyarrow/_substrait.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "de98742bd8125ec036f1a7b9f67e7ffdf3a92e923138d26a338c5bf1c07643d6", "sha256_in_prefix": "de98742bd8125ec036f1a7b9f67e7ffdf3a92e923138d26a338c5bf1c07643d6", "size_in_bytes": 202616}, {"_path": "lib/python3.11/site-packages/pyarrow/_substrait.pyx", "path_type": "hardlink", "sha256": "7d94f8f7a9b3a75b2c48a233aad98f78604a798ab97f4be6f1c5a55aa682e34f", "sha256_in_prefix": "7d94f8f7a9b3a75b2c48a233aad98f78604a798ab97f4be6f1c5a55aa682e34f", "size_in_bytes": 15666}, {"_path": "lib/python3.11/site-packages/pyarrow/acero.py", "path_type": "hardlink", "sha256": "3ef054552bb16dc49628c2c97fe37fbc351db6bd3e93f776704c2b513ca93dbb", "sha256_in_prefix": "3ef054552bb16dc49628c2c97fe37fbc351db6bd3e93f776704c2b513ca93dbb", "size_in_bytes": 15297}, {"_path": "lib/python3.11/site-packages/pyarrow/array.pxi", "path_type": "hardlink", "sha256": "cdc9bb45266f594d465d8b580e8bddd99bbef5c3047dee0dd55400ee505698e2", "sha256_in_prefix": "cdc9bb45266f594d465d8b580e8bddd99bbef5c3047dee0dd55400ee505698e2", "size_in_bytes": 156807}, {"_path": "lib/python3.11/site-packages/pyarrow/benchmark.pxi", "path_type": "hardlink", "sha256": "0d85ddbbe8cc487ed7713a216dcf31f0d88a44bb695fd2142df636d288647df0", "sha256_in_prefix": "0d85ddbbe8cc487ed7713a216dcf31f0d88a44bb695fd2142df636d288647df0", "size_in_bytes": 869}, {"_path": "lib/python3.11/site-packages/pyarrow/benchmark.py", "path_type": "hardlink", "sha256": "93d677c90ca8a23a58cf895303a0e409fa93e9f3c6dcdd9f26db072a3a5b605a", "sha256_in_prefix": "93d677c90ca8a23a58cf895303a0e409fa93e9f3c6dcdd9f26db072a3a5b605a", "size_in_bytes": 856}, {"_path": "lib/python3.11/site-packages/pyarrow/builder.pxi", "path_type": "hardlink", "sha256": "f50138280880e09a40efed892e05f7c68df68d1b6e599dd8a82f93f46cd45437", "sha256_in_prefix": "f50138280880e09a40efed892e05f7c68df68d1b6e599dd8a82f93f46cd45437", "size_in_bytes": 4634}, {"_path": "lib/python3.11/site-packages/pyarrow/cffi.py", "path_type": "hardlink", "sha256": "84472b3c7f4a786e8d112dd90a949b3956213a00ceb8107fd8b80c32e720c3ef", "sha256_in_prefix": "84472b3c7f4a786e8d112dd90a949b3956213a00ceb8107fd8b80c32e720c3ef", "size_in_bytes": 2396}, {"_path": "lib/python3.11/site-packages/pyarrow/compat.pxi", "path_type": "hardlink", "sha256": "4aae5cdc22aad2e8f9683c8ea071e43c43bf56c4a967dd0945a2f6ac02879392", "sha256_in_prefix": "4aae5cdc22aad2e8f9683c8ea071e43c43bf56c4a967dd0945a2f6ac02879392", "size_in_bytes": 1920}, {"_path": "lib/python3.11/site-packages/pyarrow/compute.py", "path_type": "hardlink", "sha256": "9dd1aac8f8c9f8b159304687b2f95ab7fe8e23a0dcec5f7d92a907209747409e", "sha256_in_prefix": "9dd1aac8f8c9f8b159304687b2f95ab7fe8e23a0dcec5f7d92a907209747409e", "size_in_bytes": 24237}, {"_path": "lib/python3.11/site-packages/pyarrow/config.pxi", "path_type": "hardlink", "sha256": "13a40e163765c371f56b904e01ebd83492449a69ba15b95f69a7b2b299d6056c", "sha256_in_prefix": "13a40e163765c371f56b904e01ebd83492449a69ba15b95f69a7b2b299d6056c", "size_in_bytes": 3092}, {"_path": "lib/python3.11/site-packages/pyarrow/conftest.py", "path_type": "hardlink", "sha256": "e81e32deccec5ebd965d07c7aebb750080b1dd4fdf1e509525e7645d52e30617", "sha256_in_prefix": "e81e32deccec5ebd965d07c7aebb750080b1dd4fdf1e509525e7645d52e30617", "size_in_bytes": 9899}, {"_path": "lib/python3.11/site-packages/pyarrow/csv.py", "path_type": "hardlink", "sha256": "4bab66df506b6bd1cf7fd22c63004b96d6412ccbcdcf2a567de08bc92b2399db", "sha256_in_prefix": "4bab66df506b6bd1cf7fd22c63004b96d6412ccbcdcf2a567de08bc92b2399db", "size_in_bytes": 974}, {"_path": "lib/python3.11/site-packages/pyarrow/cuda.py", "path_type": "hardlink", "sha256": "8fefbc1dc0409b921bfa46e12b877633a4954260d692beccb797e7c14d8bcdd4", "sha256_in_prefix": "8fefbc1dc0409b921bfa46e12b877633a4954260d692beccb797e7c14d8bcdd4", "size_in_bytes": 1087}, {"_path": "lib/python3.11/site-packages/pyarrow/dataset.py", "path_type": "hardlink", "sha256": "ab9c31d9cf02258680bdd2727d23b68764b37c02ea3785cccfee2e1a8577d6a9", "sha256_in_prefix": "ab9c31d9cf02258680bdd2727d23b68764b37c02ea3785cccfee2e1a8577d6a9", "size_in_bytes": 40303}, {"_path": "lib/python3.11/site-packages/pyarrow/device.pxi", "path_type": "hardlink", "sha256": "0ad5415e9ebcccd5ebacfc1e861e7a8a07cbb0c4a5611a39ad615c2a410ffe06", "sha256_in_prefix": "0ad5415e9ebcccd5ebacfc1e861e7a8a07cbb0c4a5611a39ad615c2a410ffe06", "size_in_bytes": 5569}, {"_path": "lib/python3.11/site-packages/pyarrow/error.pxi", "path_type": "hardlink", "sha256": "5a3efe34651f76f944c00c1df136bf26a442f0850e5299bf3e6a6f8b3085bdf6", "sha256_in_prefix": "5a3efe34651f76f944c00c1df136bf26a442f0850e5299bf3e6a6f8b3085bdf6", "size_in_bytes": 8909}, {"_path": "lib/python3.11/site-packages/pyarrow/feather.py", "path_type": "hardlink", "sha256": "f6b58bf9360afea734156def23263277a5edf3a0292562eaa3ed9c2b7179bc64", "sha256_in_prefix": "f6b58bf9360afea734156def23263277a5edf3a0292562eaa3ed9c2b7179bc64", "size_in_bytes": 9959}, {"_path": "lib/python3.11/site-packages/pyarrow/flight.py", "path_type": "hardlink", "sha256": "1cb074e00d12677e4c649ba63c8b81bb92367697ad8c473e08630476341e4114", "sha256_in_prefix": "1cb074e00d12677e4c649ba63c8b81bb92367697ad8c473e08630476341e4114", "size_in_bytes": 2177}, {"_path": "lib/python3.11/site-packages/pyarrow/fs.py", "path_type": "hardlink", "sha256": "33e7126d2d9b051e0cc1b26aa73f50ed5c4763c7daf3d4ad130d89d1730c17b1", "sha256_in_prefix": "33e7126d2d9b051e0cc1b26aa73f50ed5c4763c7daf3d4ad130d89d1730c17b1", "size_in_bytes": 14899}, {"_path": "lib/python3.11/site-packages/pyarrow/gandiva.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "5c251c48c4db150aa473291be4c97f36ba1dd4ed2e68548f2ef3199f65b04382", "sha256_in_prefix": "5c251c48c4db150aa473291be4c97f36ba1dd4ed2e68548f2ef3199f65b04382", "size_in_bytes": 271160}, {"_path": "lib/python3.11/site-packages/pyarrow/gandiva.pyx", "path_type": "hardlink", "sha256": "6c5db7ae4aba7b8e62f8278f0d9793cbd885c287a0f0496b363cfd54af7b411b", "sha256_in_prefix": "6c5db7ae4aba7b8e62f8278f0d9793cbd885c287a0f0496b363cfd54af7b411b", "size_in_bytes": 24503}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/accumulation_queue.h", "path_type": "hardlink", "sha256": "7f3d28907eac66a712a41976c37bce53dc126bd425388017ce29266f798834f1", "sha256_in_prefix": "7f3d28907eac66a712a41976c37bce53dc126bd425388017ce29266f798834f1", "size_in_bytes": 5985}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/aggregate_node.h", "path_type": "hardlink", "sha256": "f5f866050194a61b2a2ddedb36688e25a120e4d271572580e511acf54693c51d", "sha256_in_prefix": "f5f866050194a61b2a2ddedb36688e25a120e4d271572580e511acf54693c51d", "size_in_bytes": 2201}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/api.h", "path_type": "hardlink", "sha256": "7d1b8a1076ca0d85910b04872dceef483fba9906afc8eb33b65b8247b7af1429", "sha256_in_prefix": "7d1b8a1076ca0d85910b04872dceef483fba9906afc8eb33b65b8247b7af1429", "size_in_bytes": 1151}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/asof_join_node.h", "path_type": "hardlink", "sha256": "2a8eabd700e3c60d35144f7ec4a92db71ed6cc20337f8dc6c5ba6f18780a669f", "sha256_in_prefix": "2a8eabd700e3c60d35144f7ec4a92db71ed6cc20337f8dc6c5ba6f18780a669f", "size_in_bytes": 1490}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/backpressure_handler.h", "path_type": "hardlink", "sha256": "0ac49645e9ebb5b65888d9dff9c7580a030b9aee4a5003d428d28c0d6b6da03e", "sha256_in_prefix": "0ac49645e9ebb5b65888d9dff9c7580a030b9aee4a5003d428d28c0d6b6da03e", "size_in_bytes": 2810}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/benchmark_util.h", "path_type": "hardlink", "sha256": "4f96cd69b1754c3029dbc4bb57fbedfd52039fa97905ed3339508785c4dc15ff", "sha256_in_prefix": "4f96cd69b1754c3029dbc4bb57fbedfd52039fa97905ed3339508785c4dc15ff", "size_in_bytes": 1943}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/bloom_filter.h", "path_type": "hardlink", "sha256": "6c5cf303342bb3d78fa76b423d022e93539af601bf372a7bd8cfc73347616a43", "sha256_in_prefix": "6c5cf303342bb3d78fa76b423d022e93539af601bf372a7bd8cfc73347616a43", "size_in_bytes": 11978}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/exec_plan.h", "path_type": "hardlink", "sha256": "534280ded9cdbd56fbe46d174052d56c6cd708675d746c91856df331af285897", "sha256_in_prefix": "534280ded9cdbd56fbe46d174052d56c6cd708675d746c91856df331af285897", "size_in_bytes": 35909}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/hash_join.h", "path_type": "hardlink", "sha256": "ce39635231f6010948e78573dbfee1bc92008d381a4f404f0bf2d50fbc4d7769", "sha256_in_prefix": "ce39635231f6010948e78573dbfee1bc92008d381a4f404f0bf2d50fbc4d7769", "size_in_bytes": 3022}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/hash_join_dict.h", "path_type": "hardlink", "sha256": "fc128998add9fca749b981e1e0a402b93ff5ad7954a21aed12018bb442787e04", "sha256_in_prefix": "fc128998add9fca749b981e1e0a402b93ff5ad7954a21aed12018bb442787e04", "size_in_bytes": 15360}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/hash_join_node.h", "path_type": "hardlink", "sha256": "1574fe69e5cbee7353b95ef97fda1781d1b2a8c2bfef61a7a86526f5c9819e4a", "sha256_in_prefix": "1574fe69e5cbee7353b95ef97fda1781d1b2a8c2bfef61a7a86526f5c9819e4a", "size_in_bytes": 4378}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/map_node.h", "path_type": "hardlink", "sha256": "05dd47716d0de5ace8215b61d804de1f181329df579a6124cf8d9804dc3978ad", "sha256_in_prefix": "05dd47716d0de5ace8215b61d804de1f181329df579a6124cf8d9804dc3978ad", "size_in_bytes": 2628}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/options.h", "path_type": "hardlink", "sha256": "e8e5408b98b7877a43eb86362943ba938bc49e4c43bce4a1a0334458d428e1ef", "sha256_in_prefix": "e8e5408b98b7877a43eb86362943ba938bc49e4c43bce4a1a0334458d428e1ef", "size_in_bytes": 37394}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/order_by_impl.h", "path_type": "hardlink", "sha256": "750aa994ff806563d944a8a8f0b9938d85a5098cfd5665bebac52bb5a2e977fc", "sha256_in_prefix": "750aa994ff806563d944a8a8f0b9938d85a5098cfd5665bebac52bb5a2e977fc", "size_in_bytes": 1691}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/partition_util.h", "path_type": "hardlink", "sha256": "0d2fc99575139fe3acf899152c826fcbde05b2a3c7bb714f78fe346c6d912e81", "sha256_in_prefix": "0d2fc99575139fe3acf899152c826fcbde05b2a3c7bb714f78fe346c6d912e81", "size_in_bytes": 7436}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/pch.h", "path_type": "hardlink", "sha256": "f155d7235d2b507ce542202d871fb28c73100a918bddd800895686cd3b9b3cf1", "sha256_in_prefix": "f155d7235d2b507ce542202d871fb28c73100a918bddd800895686cd3b9b3cf1", "size_in_bytes": 1094}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/query_context.h", "path_type": "hardlink", "sha256": "0f7eb8686452dee59ef25818a823518d5bece6c29eb4b38e5f3002769e4665e8", "sha256_in_prefix": "0f7eb8686452dee59ef25818a823518d5bece6c29eb4b38e5f3002769e4665e8", "size_in_bytes": 6212}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/schema_util.h", "path_type": "hardlink", "sha256": "280fe1576c72d9345c70cca4b12cd0add1fdfeb746a37b50c87388beb5966014", "sha256_in_prefix": "280fe1576c72d9345c70cca4b12cd0add1fdfeb746a37b50c87388beb5966014", "size_in_bytes": 7961}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/task_util.h", "path_type": "hardlink", "sha256": "ea9a882ee61f715c2df47a958517d7155268382f90fddb64f264394b18e0c1b6", "sha256_in_prefix": "ea9a882ee61f715c2df47a958517d7155268382f90fddb64f264394b18e0c1b6", "size_in_bytes": 3706}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/test_nodes.h", "path_type": "hardlink", "sha256": "c4a78b599642f22a24bde5573e3b1e3b530ebd631c6f2fb4c623084b2d2ac3c1", "sha256_in_prefix": "c4a78b599642f22a24bde5573e3b1e3b530ebd631c6f2fb4c623084b2d2ac3c1", "size_in_bytes": 2877}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/time_series_util.h", "path_type": "hardlink", "sha256": "5bdcb3a1a4c6901da3b589bcc360989274b0d448cc6ec7537e612eb8bdb332d9", "sha256_in_prefix": "5bdcb3a1a4c6901da3b589bcc360989274b0d448cc6ec7537e612eb8bdb332d9", "size_in_bytes": 1210}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/tpch_node.h", "path_type": "hardlink", "sha256": "977ce87311d37c69974e3cb0271c285c2224f6d8f351181675829281293c0c04", "sha256_in_prefix": "977ce87311d37c69974e3cb0271c285c2224f6d8f351181675829281293c0c04", "size_in_bytes": 2671}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/type_fwd.h", "path_type": "hardlink", "sha256": "e332e1b4b25fffb3125e0ae140865519e2f18d3ec9ac40c09fdc96ef90d31657", "sha256_in_prefix": "e332e1b4b25fffb3125e0ae140865519e2f18d3ec9ac40c09fdc96ef90d31657", "size_in_bytes": 1103}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/util.h", "path_type": "hardlink", "sha256": "6f284c123e57a00532fbddc08cbaf1fe9f7f89465d627e6e27f703902250b794", "sha256_in_prefix": "6f284c123e57a00532fbddc08cbaf1fe9f7f89465d627e6e27f703902250b794", "size_in_bytes": 6121}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/acero/visibility.h", "path_type": "hardlink", "sha256": "13ee06d8ee05d9869b5e714d2589ec23655b568281b4eec05ea87f48fb898ba9", "sha256_in_prefix": "13ee06d8ee05d9869b5e714d2589ec23655b568281b4eec05ea87f48fb898ba9", "size_in_bytes": 1616}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/adapters/orc/adapter.h", "path_type": "hardlink", "sha256": "1b949218660c4911082c2e3792a2f97eaa3de1ce2d2a0ba48ad3b5e66db5eed6", "sha256_in_prefix": "1b949218660c4911082c2e3792a2f97eaa3de1ce2d2a0ba48ad3b5e66db5eed6", "size_in_bytes": 11031}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/adapters/orc/options.h", "path_type": "hardlink", "sha256": "14cc5d6b9612b2446b07a87d16f700b8cc65e6a75abd6acd6073e56a78ed938f", "sha256_in_prefix": "14cc5d6b9612b2446b07a87d16f700b8cc65e6a75abd6acd6073e56a78ed938f", "size_in_bytes": 3696}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/api.h", "path_type": "hardlink", "sha256": "1ace8789105853937bf9aefd8634e5f5631275ae79d577542a95ad845636bf5b", "sha256_in_prefix": "1ace8789105853937bf9aefd8634e5f5631275ae79d577542a95ad845636bf5b", "size_in_bytes": 2491}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array.h", "path_type": "hardlink", "sha256": "3f9a16ea1bc3da3f7b6cb6924c4e3f507b95e98dfc0d3c09570c3711bdf17534", "sha256_in_prefix": "3f9a16ea1bc3da3f7b6cb6924c4e3f507b95e98dfc0d3c09570c3711bdf17534", "size_in_bytes": 1981}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/array_base.h", "path_type": "hardlink", "sha256": "2b05650ea3e22beda42d8dc2f3fedcf9948684a233051e235906a4ae7f959acf", "sha256_in_prefix": "2b05650ea3e22beda42d8dc2f3fedcf9948684a233051e235906a4ae7f959acf", "size_in_bytes": 12147}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/array_binary.h", "path_type": "hardlink", "sha256": "26fb41f03a11d3fb6a7d2152ffd9cc46b277f65b757139b9c9787e0cb921aa35", "sha256_in_prefix": "26fb41f03a11d3fb6a7d2152ffd9cc46b277f65b757139b9c9787e0cb921aa35", "size_in_bytes": 11247}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/array_decimal.h", "path_type": "hardlink", "sha256": "c517eb6752053b4f449a41c4a250b0ac9e25b178cba390d775f1f9fefda04b2c", "sha256_in_prefix": "c517eb6752053b4f449a41c4a250b0ac9e25b178cba390d775f1f9fefda04b2c", "size_in_bytes": 3105}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/array_dict.h", "path_type": "hardlink", "sha256": "e8031b4a7668323fa7850859846e11367c72f5554f9360ef66355b9702148606", "sha256_in_prefix": "e8031b4a7668323fa7850859846e11367c72f5554f9360ef66355b9702148606", "size_in_bytes": 7611}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/array_nested.h", "path_type": "hardlink", "sha256": "c724a21796f569bf7b1a27ca331e85b986566f6ffa7b762f48c7ce39119edc9e", "sha256_in_prefix": "c724a21796f569bf7b1a27ca331e85b986566f6ffa7b762f48c7ce39119edc9e", "size_in_bytes": 37605}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/array_primitive.h", "path_type": "hardlink", "sha256": "fd4f3fa531e0f9a1d8e636921631d9e21a913d77588b2293a6bdb2dce70971d8", "sha256_in_prefix": "fd4f3fa531e0f9a1d8e636921631d9e21a913d77588b2293a6bdb2dce70971d8", "size_in_bytes": 8184}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/array_run_end.h", "path_type": "hardlink", "sha256": "e33b37b5c52b2200ce85210ecb0275bc663696c1fee50b84067f3b9b19c36e2f", "sha256_in_prefix": "e33b37b5c52b2200ce85210ecb0275bc663696c1fee50b84067f3b9b19c36e2f", "size_in_bytes": 5101}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_adaptive.h", "path_type": "hardlink", "sha256": "f760e98886435d223fc8eacc7ed8fb3fad339422e3366c1f18ce6e6dd6d758ce", "sha256_in_prefix": "f760e98886435d223fc8eacc7ed8fb3fad339422e3366c1f18ce6e6dd6d758ce", "size_in_bytes": 6861}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_base.h", "path_type": "hardlink", "sha256": "08ff644bca4315de17c8941d808941a77a53217f66343d4bbe1f39adee080bcc", "sha256_in_prefix": "08ff644bca4315de17c8941d808941a77a53217f66343d4bbe1f39adee080bcc", "size_in_bytes": 13723}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_binary.h", "path_type": "hardlink", "sha256": "d3506b4b090540d004cb8155622f04b1b77609a7b2c4dd381835285ec4141615", "sha256_in_prefix": "d3506b4b090540d004cb8155622f04b1b77609a7b2c4dd381835285ec4141615", "size_in_bytes": 32718}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_decimal.h", "path_type": "hardlink", "sha256": "0c5c72165a73591652f7374186c8c82397c550e318f5b5c79f7108ac8be638ca", "sha256_in_prefix": "0c5c72165a73591652f7374186c8c82397c550e318f5b5c79f7108ac8be638ca", "size_in_bytes": 5051}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_dict.h", "path_type": "hardlink", "sha256": "1598ef091203995bb09b3c7f1c27032ba66336864a084b1257e7c62342bdef86", "sha256_in_prefix": "1598ef091203995bb09b3c7f1c27032ba66336864a084b1257e7c62342bdef86", "size_in_bytes": 27899}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_nested.h", "path_type": "hardlink", "sha256": "d489ff33ca6391aa93baf35995b197586203df7096d9e061cb88a7d2866c6af0", "sha256_in_prefix": "d489ff33ca6391aa93baf35995b197586203df7096d9e061cb88a7d2866c6af0", "size_in_bytes": 31231}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_primitive.h", "path_type": "hardlink", "sha256": "d9c5de452e32634e02cc03c23686c5608ae4404ae52b2eafd35bfa55ea4736c9", "sha256_in_prefix": "d9c5de452e32634e02cc03c23686c5608ae4404ae52b2eafd35bfa55ea4736c9", "size_in_bytes": 20926}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_run_end.h", "path_type": "hardlink", "sha256": "49921db1428ad6a01cf697689cf19fd00fda8a4647731c7389c7b344647984bb", "sha256_in_prefix": "49921db1428ad6a01cf697689cf19fd00fda8a4647731c7389c7b344647984bb", "size_in_bytes": 11416}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_time.h", "path_type": "hardlink", "sha256": "f0cda27d99c382e8d222d5cab1ebf205db4ce88932dc89b2788740a995777de7", "sha256_in_prefix": "f0cda27d99c382e8d222d5cab1ebf205db4ce88932dc89b2788740a995777de7", "size_in_bytes": 2548}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/builder_union.h", "path_type": "hardlink", "sha256": "f01179df6b0031cec9c5621b48dfb25fa67d7ea63d8e69ac21ad39e033ef6d61", "sha256_in_prefix": "f01179df6b0031cec9c5621b48dfb25fa67d7ea63d8e69ac21ad39e033ef6d61", "size_in_bytes": 10144}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/concatenate.h", "path_type": "hardlink", "sha256": "c01cbe0814f3f4c7910a67277d71af9179ef491029bcf39c7c27fae00e36cacf", "sha256_in_prefix": "c01cbe0814f3f4c7910a67277d71af9179ef491029bcf39c7c27fae00e36cacf", "size_in_bytes": 2059}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/data.h", "path_type": "hardlink", "sha256": "940919c9c877b9ed71cb9e366564863fa652fe207acfc5b8a81c3e233acf27a1", "sha256_in_prefix": "940919c9c877b9ed71cb9e366564863fa652fe207acfc5b8a81c3e233acf27a1", "size_in_bytes": 25160}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/diff.h", "path_type": "hardlink", "sha256": "6d834acb6a0b031b6de956035af09fab66e725354d8c6e4a313b0697e813fe43", "sha256_in_prefix": "6d834acb6a0b031b6de956035af09fab66e725354d8c6e4a313b0697e813fe43", "size_in_bytes": 3344}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/statistics.h", "path_type": "hardlink", "sha256": "ee2354766f6f39da0dda00e8afe014740c18700cd3ba1663b3e7cea419da4c84", "sha256_in_prefix": "ee2354766f6f39da0dda00e8afe014740c18700cd3ba1663b3e7cea419da4c84", "size_in_bytes": 5325}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/util.h", "path_type": "hardlink", "sha256": "a951ef09a56500bcfc589c008f2330b019b927688df3d092823ecda669aa9642", "sha256_in_prefix": "a951ef09a56500bcfc589c008f2330b019b927688df3d092823ecda669aa9642", "size_in_bytes": 3652}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/array/validate.h", "path_type": "hardlink", "sha256": "25d0dbdd7260e139807e9bffce0bb62137cbd87f67a35d13422b7e1cf8fd332c", "sha256_in_prefix": "25d0dbdd7260e139807e9bffce0bb62137cbd87f67a35d13422b7e1cf8fd332c", "size_in_bytes": 1710}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/buffer.h", "path_type": "hardlink", "sha256": "fdb2ad5a4f128f439d74652311c66f6edf40249be0518a85f11ac8a7f3ed3016", "sha256_in_prefix": "fdb2ad5a4f128f439d74652311c66f6edf40249be0518a85f11ac8a7f3ed3016", "size_in_bytes": 23221}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/buffer_builder.h", "path_type": "hardlink", "sha256": "b575882f01d6d0c2a9bdeecd214d8494494f634cb4a282126bcd83aba51d8555", "sha256_in_prefix": "b575882f01d6d0c2a9bdeecd214d8494494f634cb4a282126bcd83aba51d8555", "size_in_bytes": 17371}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/builder.h", "path_type": "hardlink", "sha256": "981c4c928dbbd6527b5db92ed210a2c63f78dd8c7e7768b84391e6d967f08863", "sha256_in_prefix": "981c4c928dbbd6527b5db92ed210a2c63f78dd8c7e7768b84391e6d967f08863", "size_in_bytes": 1546}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/c/abi.h", "path_type": "hardlink", "sha256": "1828f1ce60368cd590d19595910fa19db09b30728f8a43b5f89933623f7a7276", "sha256_in_prefix": "1828f1ce60368cd590d19595910fa19db09b30728f8a43b5f89933623f7a7276", "size_in_bytes": 20318}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/c/bridge.h", "path_type": "hardlink", "sha256": "e9ee99a08b06fa913c150e5a5389d127bd6f54bdd4f64ebdee0ecf60a0ec573e", "sha256_in_prefix": "e9ee99a08b06fa913c150e5a5389d127bd6f54bdd4f64ebdee0ecf60a0ec573e", "size_in_bytes": 21789}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/c/dlpack.h", "path_type": "hardlink", "sha256": "fc721af40291da6c1b85fd5a0a1229305fd70e916b3da7f9f0bb777d5c564567", "sha256_in_prefix": "fc721af40291da6c1b85fd5a0a1229305fd70e916b3da7f9f0bb777d5c564567", "size_in_bytes": 1817}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/c/dlpack_abi.h", "path_type": "hardlink", "sha256": "9a3a7d596abcaafea09068ab4f8cb4a3704bfd923d5721d0a49e5a1293c57ad2", "sha256_in_prefix": "9a3a7d596abcaafea09068ab4f8cb4a3704bfd923d5721d0a49e5a1293c57ad2", "size_in_bytes": 9920}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/c/helpers.h", "path_type": "hardlink", "sha256": "7f4439d7d3f0a258851e9c6c1e9f90bdb3fa7e954c3761dadf94e4f9104ae96b", "sha256_in_prefix": "7f4439d7d3f0a258851e9c6c1e9f90bdb3fa7e954c3761dadf94e4f9104ae96b", "size_in_bytes": 6279}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/chunk_resolver.h", "path_type": "hardlink", "sha256": "a159bb0e96d64668c985ec63fe1e81b62b29081c4564f2072b5925f60a01186f", "sha256_in_prefix": "a159bb0e96d64668c985ec63fe1e81b62b29081c4564f2072b5925f60a01186f", "size_in_bytes": 12841}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/chunked_array.h", "path_type": "hardlink", "sha256": "cfa2c0f4e077ba1b669fb6597b9c1f8b7026de271543e2ff7bcb3728432eab5f", "sha256_in_prefix": "cfa2c0f4e077ba1b669fb6597b9c1f8b7026de271543e2ff7bcb3728432eab5f", "size_in_bytes": 10647}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/compare.h", "path_type": "hardlink", "sha256": "53972b6979d7002094cd0f079866328537a136b39ecdc5543f5001025c48eb61", "sha256_in_prefix": "53972b6979d7002094cd0f079866328537a136b39ecdc5543f5001025c48eb61", "size_in_bytes": 5555}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/compute/api.h", "path_type": "hardlink", "sha256": "210297cffe980417c728e92eaa45c887d653658c9582aeda1014c8ccc9191222", "sha256_in_prefix": "210297cffe980417c728e92eaa45c887d653658c9582aeda1014c8ccc9191222", "size_in_bytes": 2071}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/compute/api_aggregate.h", "path_type": "hardlink", "sha256": "0ddeb9f4edd352db54ab05742c7422be7e16b74e32e73227fb533c259082d2a4", "sha256_in_prefix": "0ddeb9f4edd352db54ab05742c7422be7e16b74e32e73227fb533c259082d2a4", "size_in_bytes": 21945}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/compute/api_scalar.h", "path_type": "hardlink", "sha256": "fd2ea2e516cca86473e8c5ac6c1ae69f605405127ec2d09dcabf88de958f1567", "sha256_in_prefix": "fd2ea2e516cca86473e8c5ac6c1ae69f605405127ec2d09dcabf88de958f1567", "size_in_bytes": 69726}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/compute/api_vector.h", "path_type": "hardlink", "sha256": "c67e51dbc1c730c4ffbd3e4b68398a63cb9e5150f531128fd0f07b1d2532f177", "sha256_in_prefix": "c67e51dbc1c730c4ffbd3e4b68398a63cb9e5150f531128fd0f07b1d2532f177", "size_in_bytes": 34508}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/compute/cast.h", "path_type": "hardlink", "sha256": "5f0f63d3700800c53f8598aa93d776643e314e611291f5da0ecb999224f2a4bb", "sha256_in_prefix": "5f0f63d3700800c53f8598aa93d776643e314e611291f5da0ecb999224f2a4bb", "size_in_bytes": 4245}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/compute/exec.h", "path_type": "hardlink", "sha256": "d9b697bead455b0c9f41f4d5e2beaa845f35f0b1183b1ca7fc3893a84e403029", "sha256_in_prefix": "d9b697bead455b0c9f41f4d5e2beaa845f35f0b1183b1ca7fc3893a84e403029", "size_in_bytes": 17975}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/compute/expression.h", "path_type": "hardlink", "sha256": "9655fff35b9423227cbcf98ff3eda6022a69cb0e1c54d84219fa87458dfb14e3", "sha256_in_prefix": "9655fff35b9423227cbcf98ff3eda6022a69cb0e1c54d84219fa87458dfb14e3", "size_in_bytes": 11184}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/compute/function.h", "path_type": "hardlink", "sha256": "92b4d768ba30bd3d5c2a179cb3bd2eacf41cc7be2f4022788ecc2d044e17b390", "sha256_in_prefix": "92b4d768ba30bd3d5c2a179cb3bd2eacf41cc7be2f4022788ecc2d044e17b390", "size_in_bytes": 16345}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/compute/function_options.h", "path_type": "hardlink", "sha256": "43dae39173eb53df978bae3f7cc2cf6c16d6fdbca18c916cbc7a693f50ae99d0", "sha256_in_prefix": "43dae39173eb53df978bae3f7cc2cf6c16d6fdbca18c916cbc7a693f50ae99d0", "size_in_bytes": 3088}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/compute/kernel.h", "path_type": "hardlink", "sha256": "cb0b3117cef0d9e238962f1b7bb5a2b9ae5b5e9d0d62131becb4bc2333c53b75", "sha256_in_prefix": "cb0b3117cef0d9e238962f1b7bb5a2b9ae5b5e9d0d62131becb4bc2333c53b75", "size_in_bytes": 31406}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/compute/ordering.h", "path_type": "hardlink", "sha256": "f15c375730e2d661a05702864196a4cfd4d58f4038d30c5c2f5dc4beea8d5635", "sha256_in_prefix": "f15c375730e2d661a05702864196a4cfd4d58f4038d30c5c2f5dc4beea8d5635", "size_in_bytes": 4129}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/compute/registry.h", "path_type": "hardlink", "sha256": "c7b2c789a34456f674554b2c645b1ab01e7667503145f964748e6d475861cea7", "sha256_in_prefix": "c7b2c789a34456f674554b2c645b1ab01e7667503145f964748e6d475861cea7", "size_in_bytes": 4837}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/compute/row/grouper.h", "path_type": "hardlink", "sha256": "9ca58be1b61416106220afdfc248cb4019082739206c0f80eb9305c44b742180", "sha256_in_prefix": "9ca58be1b61416106220afdfc248cb4019082739206c0f80eb9305c44b742180", "size_in_bytes": 7458}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/compute/type_fwd.h", "path_type": "hardlink", "sha256": "b621bb32b2c47d700f179dc92a59c5749ee4ce384d3fd6bfc426f6129b77a337", "sha256_in_prefix": "b621bb32b2c47d700f179dc92a59c5749ee4ce384d3fd6bfc426f6129b77a337", "size_in_bytes": 1555}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/compute/util.h", "path_type": "hardlink", "sha256": "785fc15f669fb536b7a942706990374064da8eb0efe277fa1ca5ece9d3a68ee8", "sha256_in_prefix": "785fc15f669fb536b7a942706990374064da8eb0efe277fa1ca5ece9d3a68ee8", "size_in_bytes": 8863}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/config.h", "path_type": "hardlink", "sha256": "f258b2288d0224ed06f85cf923ef908c80308749b8868b1fc803b0bd556cd2c5", "sha256_in_prefix": "f258b2288d0224ed06f85cf923ef908c80308749b8868b1fc803b0bd556cd2c5", "size_in_bytes": 3044}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/csv/api.h", "path_type": "hardlink", "sha256": "2dbc1684fc88b22ffbde1bec4abefb44d47db94c6b5725ccfff869ed0712a26d", "sha256_in_prefix": "2dbc1684fc88b22ffbde1bec4abefb44d47db94c6b5725ccfff869ed0712a26d", "size_in_bytes": 907}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/csv/chunker.h", "path_type": "hardlink", "sha256": "9d3b3c85dcb80f7373de86569b624cb80d369e863fd29591616abf469b6acc76", "sha256_in_prefix": "9d3b3c85dcb80f7373de86569b624cb80d369e863fd29591616abf469b6acc76", "size_in_bytes": 1171}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/csv/column_builder.h", "path_type": "hardlink", "sha256": "ee86bd60283651cda607b1311c8c9822f6ddb79e79a8b5e2534cb815ea642125", "sha256_in_prefix": "ee86bd60283651cda607b1311c8c9822f6ddb79e79a8b5e2534cb815ea642125", "size_in_bytes": 2890}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/csv/column_decoder.h", "path_type": "hardlink", "sha256": "d7489d70f244d95fd36ef823ccfaa6172d5d77fa92196beef5e0e47a74ee0b3d", "sha256_in_prefix": "d7489d70f244d95fd36ef823ccfaa6172d5d77fa92196beef5e0e47a74ee0b3d", "size_in_bytes": 2358}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/csv/converter.h", "path_type": "hardlink", "sha256": "723b67cff8591719bf7568c0323af58aaaa4d5e81723661bf01774c42f267791", "sha256_in_prefix": "723b67cff8591719bf7568c0323af58aaaa4d5e81723661bf01774c42f267791", "size_in_bytes": 2789}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/csv/invalid_row.h", "path_type": "hardlink", "sha256": "8131e311b8e4a5e7bab322c603c84563bb29c7544e3099ad31cc215efdb5c794", "sha256_in_prefix": "8131e311b8e4a5e7bab322c603c84563bb29c7544e3099ad31cc215efdb5c794", "size_in_bytes": 1889}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/csv/options.h", "path_type": "hardlink", "sha256": "fc79234a88803d6efbcf90075559d36b8e76cb529f2609d7597cf63683cf018c", "sha256_in_prefix": "fc79234a88803d6efbcf90075559d36b8e76cb529f2609d7597cf63683cf018c", "size_in_bytes": 7980}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/csv/parser.h", "path_type": "hardlink", "sha256": "f0fa65461dd0c5c924f153f233bd0ffdfd4c05be163069cd569a1e27d90e7475", "sha256_in_prefix": "f0fa65461dd0c5c924f153f233bd0ffdfd4c05be163069cd569a1e27d90e7475", "size_in_bytes": 8616}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/csv/reader.h", "path_type": "hardlink", "sha256": "e35ea9b77c8d42c827e11848c9132c992266befd6cc37a2e428b6e6d71bdd604", "sha256_in_prefix": "e35ea9b77c8d42c827e11848c9132c992266befd6cc37a2e428b6e6d71bdd604", "size_in_bytes": 4606}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/csv/test_common.h", "path_type": "hardlink", "sha256": "b84633c3c1113af77540c050f7c77831a67b06a325c367b47e5032cfe76ed19e", "sha256_in_prefix": "b84633c3c1113af77540c050f7c77831a67b06a325c367b47e5032cfe76ed19e", "size_in_bytes": 1972}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/csv/type_fwd.h", "path_type": "hardlink", "sha256": "a6d55b7a782663f6bb633d70d122a628bd7acb2c3dc847b29b4434727442495e", "sha256_in_prefix": "a6d55b7a782663f6bb633d70d122a628bd7acb2c3dc847b29b4434727442495e", "size_in_bytes": 984}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/csv/writer.h", "path_type": "hardlink", "sha256": "635cc4ad9e47d6bd90ce302d6b74d7a45add9766eda1aadd085f14482006b468", "sha256_in_prefix": "635cc4ad9e47d6bd90ce302d6b74d7a45add9766eda1aadd085f14482006b468", "size_in_bytes": 3549}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/api.h", "path_type": "hardlink", "sha256": "a7b8be6e77092e199f0647e32564bbebce2f0fd2e47b59bab5bec742aed3a67e", "sha256_in_prefix": "a7b8be6e77092e199f0647e32564bbebce2f0fd2e47b59bab5bec742aed3a67e", "size_in_bytes": 1322}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/dataset.h", "path_type": "hardlink", "sha256": "36c5dd48562b3b8069252115d9ee32251acdfa174bc4c25ea70032caa156b7ed", "sha256_in_prefix": "36c5dd48562b3b8069252115d9ee32251acdfa174bc4c25ea70032caa156b7ed", "size_in_bytes": 20327}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/dataset_writer.h", "path_type": "hardlink", "sha256": "4d057be5bfd48a07c68c8a419cf93cb5e3a770ce56ae829f295d79a227014516", "sha256_in_prefix": "4d057be5bfd48a07c68c8a419cf93cb5e3a770ce56ae829f295d79a227014516", "size_in_bytes": 4589}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/discovery.h", "path_type": "hardlink", "sha256": "c7bfb934103211e4161a559a9c90cb640a1692c2a233033dead943c7f33a9f97", "sha256_in_prefix": "c7bfb934103211e4161a559a9c90cb640a1692c2a233033dead943c7f33a9f97", "size_in_bytes": 11236}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/file_base.h", "path_type": "hardlink", "sha256": "da87b9bfc432eaffd4b6125abe0f6b8d4fd6b90bf05dc25e9e05b0737b162ea9", "sha256_in_prefix": "da87b9bfc432eaffd4b6125abe0f6b8d4fd6b90bf05dc25e9e05b0737b162ea9", "size_in_bytes": 20203}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/file_csv.h", "path_type": "hardlink", "sha256": "ecf96f416ff6149e5144df951f8f8e070e5c67a9e4774284d2c8f54d0bc265ea", "sha256_in_prefix": "ecf96f416ff6149e5144df951f8f8e070e5c67a9e4774284d2c8f54d0bc265ea", "size_in_bytes": 5016}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/file_ipc.h", "path_type": "hardlink", "sha256": "ebe6edbd785f959b001fdd13df0324c33664b5ee93e22c737821149ffe6e616f", "sha256_in_prefix": "ebe6edbd785f959b001fdd13df0324c33664b5ee93e22c737821149ffe6e616f", "size_in_bytes": 4083}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/file_json.h", "path_type": "hardlink", "sha256": "b0f8ce78c3ad6d965bbce8af9ce75be0cbd828796da539d8f1f38d92107d3d9b", "sha256_in_prefix": "b0f8ce78c3ad6d965bbce8af9ce75be0cbd828796da539d8f1f38d92107d3d9b", "size_in_bytes": 3523}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/file_orc.h", "path_type": "hardlink", "sha256": "3fb9c00fd9da7159e00c48c7f0285046dd004260e0e19d70071dfad0b0cea128", "sha256_in_prefix": "3fb9c00fd9da7159e00c48c7f0285046dd004260e0e19d70071dfad0b0cea128", "size_in_bytes": 2452}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/file_parquet.h", "path_type": "hardlink", "sha256": "7db8a18b7851daedcadaa64783a1daae3e1aa6c5b05d75ff08ed1b91563df018", "sha256_in_prefix": "7db8a18b7851daedcadaa64783a1daae3e1aa6c5b05d75ff08ed1b91563df018", "size_in_bytes": 16878}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/parquet_encryption_config.h", "path_type": "hardlink", "sha256": "529a349393228d968c6916633e9e5783c4d1b75a7c670876736b5d8a68d57b50", "sha256_in_prefix": "529a349393228d968c6916633e9e5783c4d1b75a7c670876736b5d8a68d57b50", "size_in_bytes": 3425}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/partition.h", "path_type": "hardlink", "sha256": "df0acd7a40fff9f3ced585bdd596be4f89ae09f41e017ed265121cb0237f7332", "sha256_in_prefix": "df0acd7a40fff9f3ced585bdd596be4f89ae09f41e017ed265121cb0237f7332", "size_in_bytes": 16815}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/pch.h", "path_type": "hardlink", "sha256": "88013f3db56d2877e1ca0cfb3b1f59da7961b08adf6a07868b118a8e5ccd46f8", "sha256_in_prefix": "88013f3db56d2877e1ca0cfb3b1f59da7961b08adf6a07868b118a8e5ccd46f8", "size_in_bytes": 1194}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/plan.h", "path_type": "hardlink", "sha256": "223b91f4adac583f39ff61e9555a09fb76140aafbe50f6e51d4e3a7b15f9a918", "sha256_in_prefix": "223b91f4adac583f39ff61e9555a09fb76140aafbe50f6e51d4e3a7b15f9a918", "size_in_bytes": 1181}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/projector.h", "path_type": "hardlink", "sha256": "29f6628ead3d1edd19d9c247b2b8e0fac1374a26784ca6a29df95178afb7f5c8", "sha256_in_prefix": "29f6628ead3d1edd19d9c247b2b8e0fac1374a26784ca6a29df95178afb7f5c8", "size_in_bytes": 1135}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/scanner.h", "path_type": "hardlink", "sha256": "aa8a89491d9eafbd3b02528b8a3fcf4ba1832a987cd987478b48aee549840354", "sha256_in_prefix": "aa8a89491d9eafbd3b02528b8a3fcf4ba1832a987cd987478b48aee549840354", "size_in_bytes": 25917}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/type_fwd.h", "path_type": "hardlink", "sha256": "60e51247074d0255c9ee67852e896900543f99294e6ecd85f35cdc3b2d03a08e", "sha256_in_prefix": "60e51247074d0255c9ee67852e896900543f99294e6ecd85f35cdc3b2d03a08e", "size_in_bytes": 3170}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/dataset/visibility.h", "path_type": "hardlink", "sha256": "72499ffec108d16068e16ec32201f542b3aaf36b24387b6892c97d077c98bf35", "sha256_in_prefix": "72499ffec108d16068e16ec32201f542b3aaf36b24387b6892c97d077c98bf35", "size_in_bytes": 1586}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/datum.h", "path_type": "hardlink", "sha256": "5d8699fd446b02d56a1ccabeff662d5e4fc44de438c9958b5409ec8be93631a7", "sha256_in_prefix": "5d8699fd446b02d56a1ccabeff662d5e4fc44de438c9958b5409ec8be93631a7", "size_in_bytes": 11511}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/device.h", "path_type": "hardlink", "sha256": "98bcfdf6d6fbe15763c57b4ab7a45909828943c4d8cfddee682149d59888b4cc", "sha256_in_prefix": "98bcfdf6d6fbe15763c57b4ab7a45909828943c4d8cfddee682149d59888b4cc", "size_in_bytes": 15344}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/device_allocation_type_set.h", "path_type": "hardlink", "sha256": "ca7a19f97c8594e023874d4f53e475d6613f10ec6ec37c7373de2fe4e5dad2ee", "sha256_in_prefix": "ca7a19f97c8594e023874d4f53e475d6613f10ec6ec37c7373de2fe4e5dad2ee", "size_in_bytes": 3306}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/engine/api.h", "path_type": "hardlink", "sha256": "3913343392907aeae3106f04a1ae48795fd980a0513e55b289ccafdce4569006", "sha256_in_prefix": "3913343392907aeae3106f04a1ae48795fd980a0513e55b289ccafdce4569006", "size_in_bytes": 886}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/engine/pch.h", "path_type": "hardlink", "sha256": "f155d7235d2b507ce542202d871fb28c73100a918bddd800895686cd3b9b3cf1", "sha256_in_prefix": "f155d7235d2b507ce542202d871fb28c73100a918bddd800895686cd3b9b3cf1", "size_in_bytes": 1094}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/api.h", "path_type": "hardlink", "sha256": "5bd341d51026d19571ced457600f860fb1fc5cb40d5c5a184fb4dd1851e83531", "sha256_in_prefix": "5bd341d51026d19571ced457600f860fb1fc5cb40d5c5a184fb4dd1851e83531", "size_in_bytes": 1079}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/extension_set.h", "path_type": "hardlink", "sha256": "144e9c71ec9cb90bfb0827bf165e2dead20c4727e825f582954852bc781c9bdd", "sha256_in_prefix": "144e9c71ec9cb90bfb0827bf165e2dead20c4727e825f582954852bc781c9bdd", "size_in_bytes": 21552}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/extension_types.h", "path_type": "hardlink", "sha256": "c79648bb29cd87a585b77c118d6fbecd4b2e0b749e0cb935a9183dff186cc163", "sha256_in_prefix": "c79648bb29cd87a585b77c118d6fbecd4b2e0b749e0cb935a9183dff186cc163", "size_in_bytes": 3075}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/options.h", "path_type": "hardlink", "sha256": "76dbd4b72ff3a0399c17055f969a620f37a562478e8423bbe2e445ea2cd04b39", "sha256_in_prefix": "76dbd4b72ff3a0399c17055f969a620f37a562478e8423bbe2e445ea2cd04b39", "size_in_bytes": 5820}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/relation.h", "path_type": "hardlink", "sha256": "57754a1650dd13ad5ed4e4bc2db262c2f9b9c34baae5bcc12e12aa9a098a6b0b", "sha256_in_prefix": "57754a1650dd13ad5ed4e4bc2db262c2f9b9c34baae5bcc12e12aa9a098a6b0b", "size_in_bytes": 2385}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/serde.h", "path_type": "hardlink", "sha256": "9a3c5fb85a3868f8428b079fa4a0093089649c5e143874abea0594fffd52c027", "sha256_in_prefix": "9a3c5fb85a3868f8428b079fa4a0093089649c5e143874abea0594fffd52c027", "size_in_bytes": 16528}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/test_plan_builder.h", "path_type": "hardlink", "sha256": "44415aefd0f500e2088e9d887b3ef78b0e60127cc6f5169cf6df16c2218bb2e2", "sha256_in_prefix": "44415aefd0f500e2088e9d887b3ef78b0e60127cc6f5169cf6df16c2218bb2e2", "size_in_bytes": 3003}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/test_util.h", "path_type": "hardlink", "sha256": "20765e62b939d12c7d6a725f0b6e4358fe977ac22d284cb00d652abd425c8c44", "sha256_in_prefix": "20765e62b939d12c7d6a725f0b6e4358fe977ac22d284cb00d652abd425c8c44", "size_in_bytes": 1517}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/type_fwd.h", "path_type": "hardlink", "sha256": "3fd6118c04294a0a088c30b4b22632c6843370f568debf72f39aa388cb6e741b", "sha256_in_prefix": "3fd6118c04294a0a088c30b4b22632c6843370f568debf72f39aa388cb6e741b", "size_in_bytes": 1028}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/util.h", "path_type": "hardlink", "sha256": "fdd46240168831635bb181bb92e5e1b3774c938748eb7fa9334b92c583cebe01", "sha256_in_prefix": "fdd46240168831635bb181bb92e5e1b3774c938748eb7fa9334b92c583cebe01", "size_in_bytes": 3570}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/engine/substrait/visibility.h", "path_type": "hardlink", "sha256": "191cc7e94f9408f4fc77ad1ccb03a415f71a9cf4a08992820cfe97dab2296ccb", "sha256_in_prefix": "191cc7e94f9408f4fc77ad1ccb03a415f71a9cf4a08992820cfe97dab2296ccb", "size_in_bytes": 1740}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/extension/bool8.h", "path_type": "hardlink", "sha256": "56c1d3b55cabaa4e942a0be27da77b2e8bae89ea0066e66cfee56f7a0746ab84", "sha256_in_prefix": "56c1d3b55cabaa4e942a0be27da77b2e8bae89ea0066e66cfee56f7a0746ab84", "size_in_bytes": 2145}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/extension/fixed_shape_tensor.h", "path_type": "hardlink", "sha256": "54eaaf4d29f00c8be785bb2d617e679ea59f86d67b31a0fe95217cf4112a9611", "sha256_in_prefix": "54eaaf4d29f00c8be785bb2d617e679ea59f86d67b31a0fe95217cf4112a9611", "size_in_bytes": 5610}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/extension/json.h", "path_type": "hardlink", "sha256": "827252cc25678bfa092b128ca12341c2cb81835049ce4fe0a06204fee4d23096", "sha256_in_prefix": "827252cc25678bfa092b128ca12341c2cb81835049ce4fe0a06204fee4d23096", "size_in_bytes": 2109}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/extension/opaque.h", "path_type": "hardlink", "sha256": "b8c56a49271ecbfd771e8e95f3abdf92ea01c999e2f6e7e1e4118a817e1b4d99", "sha256_in_prefix": "b8c56a49271ecbfd771e8e95f3abdf92ea01c999e2f6e7e1e4118a817e1b4d99", "size_in_bytes": 2920}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/extension/uuid.h", "path_type": "hardlink", "sha256": "13f067a7928d292c55baf7614078d84fed0729af66cd53db480423b99f4ddcf7", "sha256_in_prefix": "13f067a7928d292c55baf7614078d84fed0729af66cd53db480423b99f4ddcf7", "size_in_bytes": 2278}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/extension_type.h", "path_type": "hardlink", "sha256": "614bd4ccfcfed10886fa33a3b3ffd37f23c4a6c1d61a418ee4f3151b5bcc8e46", "sha256_in_prefix": "614bd4ccfcfed10886fa33a3b3ffd37f23c4a6c1d61a418ee4f3151b5bcc8e46", "size_in_bytes": 6639}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/api.h", "path_type": "hardlink", "sha256": "5e0cb618e66d055c038d36973c3c8f3e54bd070b7d8235979b923f41bc916c5a", "sha256_in_prefix": "5e0cb618e66d055c038d36973c3c8f3e54bd070b7d8235979b923f41bc916c5a", "size_in_bytes": 1383}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/azurefs.h", "path_type": "hardlink", "sha256": "33eee347c02ac8fbbcb935dbc3820d2dbf51b192ab3e66653ceed3eccb2443ff", "sha256_in_prefix": "33eee347c02ac8fbbcb935dbc3820d2dbf51b192ab3e66653ceed3eccb2443ff", "size_in_bytes": 15299}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/filesystem.h", "path_type": "hardlink", "sha256": "1fb3045f5db9f5a56b58cb20b165f6eadb8210f48917e888e75277b0ab1879cd", "sha256_in_prefix": "1fb3045f5db9f5a56b58cb20b165f6eadb8210f48917e888e75277b0ab1879cd", "size_in_bytes": 29585}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/filesystem_library.h", "path_type": "hardlink", "sha256": "6b16a87fe1bd1b1063cd785122de1acc1ec707c549e3d32d198b0bee948ed00d", "sha256_in_prefix": "6b16a87fe1bd1b1063cd785122de1acc1ec707c549e3d32d198b0bee948ed00d", "size_in_bytes": 1725}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/gcsfs.h", "path_type": "hardlink", "sha256": "e47487a80f5ad53db356764ed6b3592a75e32d5caad5a026e8346642683fbf9c", "sha256_in_prefix": "e47487a80f5ad53db356764ed6b3592a75e32d5caad5a026e8346642683fbf9c", "size_in_bytes": 10372}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/hdfs.h", "path_type": "hardlink", "sha256": "267f75a637e4e91331f8cb805ac10028b4f2290edb0cf340e6330457369f4a07", "sha256_in_prefix": "267f75a637e4e91331f8cb805ac10028b4f2290edb0cf340e6330457369f4a07", "size_in_bytes": 4133}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/localfs.h", "path_type": "hardlink", "sha256": "78884fae900185e433db5584f38e542e579393cddee04b499c44b88c02d6ea63", "sha256_in_prefix": "78884fae900185e433db5584f38e542e579393cddee04b499c44b88c02d6ea63", "size_in_bytes": 4972}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/mockfs.h", "path_type": "hardlink", "sha256": "92886eeecf6cf71b5def9b064c4d8afebb075bcf6cc033ad492485c418b131c7", "sha256_in_prefix": "92886eeecf6cf71b5def9b064c4d8afebb075bcf6cc033ad492485c418b131c7", "size_in_bytes": 4768}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/path_util.h", "path_type": "hardlink", "sha256": "86b0d51e4e05f4cee8180041e31db029f40c8d29520084b421a2d5bf68c7ae5e", "sha256_in_prefix": "86b0d51e4e05f4cee8180041e31db029f40c8d29520084b421a2d5bf68c7ae5e", "size_in_bytes": 5698}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/s3_test_util.h", "path_type": "hardlink", "sha256": "db607ad347ebf381aae2b1802dd89d20fd08cc3b3d1b768eaa42cde32fbac6b2", "sha256_in_prefix": "db607ad347ebf381aae2b1802dd89d20fd08cc3b3d1b768eaa42cde32fbac6b2", "size_in_bytes": 2962}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/s3fs.h", "path_type": "hardlink", "sha256": "57e4b1edd5b64c7b6d8ce7314cf0220fee42f95912020a75f069ce707bf5f3dd", "sha256_in_prefix": "57e4b1edd5b64c7b6d8ce7314cf0220fee42f95912020a75f069ce707bf5f3dd", "size_in_bytes": 17923}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/test_util.h", "path_type": "hardlink", "sha256": "9204f75a02af1b669c369897590a952f573a5217e6ce0d66f61ea8d6787774a8", "sha256_in_prefix": "9204f75a02af1b669c369897590a952f573a5217e6ce0d66f61ea8d6787774a8", "size_in_bytes": 11729}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/filesystem/type_fwd.h", "path_type": "hardlink", "sha256": "cf3b43111e7959bb78ad59e477e45e7833be627ae97a67ed15e16d67b64689d6", "sha256_in_prefix": "cf3b43111e7959bb78ad59e477e45e7833be627ae97a67ed15e16d67b64689d6", "size_in_bytes": 1462}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/api.h", "path_type": "hardlink", "sha256": "628b4b4d09fe28297acb904883c72810567d9fb3ccb49d36972ecf7398265fb5", "sha256_in_prefix": "628b4b4d09fe28297acb904883c72810567d9fb3ccb49d36972ecf7398265fb5", "size_in_bytes": 1257}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/client.h", "path_type": "hardlink", "sha256": "36d16ab9639a69f05c99d201e1e9fdb9ae7148425a081902d591e1014fc683ad", "sha256_in_prefix": "36d16ab9639a69f05c99d201e1e9fdb9ae7148425a081902d591e1014fc683ad", "size_in_bytes": 17798}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/client_auth.h", "path_type": "hardlink", "sha256": "6b70e49bf8cf3aeab336c0c0e1e7a3b8c53008404c6af33cb92ef0f358a16d16", "sha256_in_prefix": "6b70e49bf8cf3aeab336c0c0e1e7a3b8c53008404c6af33cb92ef0f358a16d16", "size_in_bytes": 2216}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/client_cookie_middleware.h", "path_type": "hardlink", "sha256": "e739023f64b130542e4d7f0df4d1f13af7b927f79fdab14eebec58e137e7ca09", "sha256_in_prefix": "e739023f64b130542e4d7f0df4d1f13af7b927f79fdab14eebec58e137e7ca09", "size_in_bytes": 1204}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/client_middleware.h", "path_type": "hardlink", "sha256": "68067009a86e88184ff3988c3deef1356be274147d29e1c6b68d9802a262a08e", "sha256_in_prefix": "68067009a86e88184ff3988c3deef1356be274147d29e1c6b68d9802a262a08e", "size_in_bytes": 2948}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/client_tracing_middleware.h", "path_type": "hardlink", "sha256": "774b1399439fab933d14c96220a2be7e526447afbbafaf4d8d4d93a7185fa96a", "sha256_in_prefix": "774b1399439fab933d14c96220a2be7e526447afbbafaf4d8d4d93a7185fa96a", "size_in_bytes": 1217}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/middleware.h", "path_type": "hardlink", "sha256": "24f41df099c855cc234c7eb2381724e01591f9657de5fa6701d847c8462f7ca1", "sha256_in_prefix": "24f41df099c855cc234c7eb2381724e01591f9657de5fa6701d847c8462f7ca1", "size_in_bytes": 2254}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/otel_logging.h", "path_type": "hardlink", "sha256": "ae24bdb193360b7987e9531b1128b327a9469ae76a74985f74263dfdc249a8c0", "sha256_in_prefix": "ae24bdb193360b7987e9531b1128b327a9469ae76a74985f74263dfdc249a8c0", "size_in_bytes": 1139}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/pch.h", "path_type": "hardlink", "sha256": "0e9da7ad9dedfca3e39b4708332bbdd7705b0a8ac91b9ae66eda5fc83374f5ba", "sha256_in_prefix": "0e9da7ad9dedfca3e39b4708332bbdd7705b0a8ac91b9ae66eda5fc83374f5ba", "size_in_bytes": 1192}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/platform.h", "path_type": "hardlink", "sha256": "d597f355aa25940668b06c87ff526fcc403c88d474862f5c506cf97b22d3d737", "sha256_in_prefix": "d597f355aa25940668b06c87ff526fcc403c88d474862f5c506cf97b22d3d737", "size_in_bytes": 1209}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/server.h", "path_type": "hardlink", "sha256": "180715d3e4c7b81ba3f9b5dfc2a62b6753f66f06602904892dbbbc4e896d46f5", "sha256_in_prefix": "180715d3e4c7b81ba3f9b5dfc2a62b6753f66f06602904892dbbbc4e896d46f5", "size_in_bytes": 13185}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/server_auth.h", "path_type": "hardlink", "sha256": "c57928b7f7e69c4674c971c406689ecc0402c4ceef41e9b5568d55d2f130716e", "sha256_in_prefix": "c57928b7f7e69c4674c971c406689ecc0402c4ceef41e9b5568d55d2f130716e", "size_in_bytes": 4457}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/server_middleware.h", "path_type": "hardlink", "sha256": "9115e6d797359a33ddba84bb37ad66d131bc9c622137f89e96c8caf72165b20b", "sha256_in_prefix": "9115e6d797359a33ddba84bb37ad66d131bc9c622137f89e96c8caf72165b20b", "size_in_bytes": 3155}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/server_tracing_middleware.h", "path_type": "hardlink", "sha256": "cd1d05159606c0002a8735613d50e3c977d975af7398bb5ea9abb003976047fc", "sha256_in_prefix": "cd1d05159606c0002a8735613d50e3c977d975af7398bb5ea9abb003976047fc", "size_in_bytes": 2186}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/sql/api.h", "path_type": "hardlink", "sha256": "398bea6af5150f2c22b1ccc8da345fb2e152912d00b8033f675948694fe9e44b", "sha256_in_prefix": "398bea6af5150f2c22b1ccc8da345fb2e152912d00b8033f675948694fe9e44b", "size_in_bytes": 853}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/sql/client.h", "path_type": "hardlink", "sha256": "02adf80e0ff6cd67600d30cc249a74ff736bc142db721a2312eb4872dcb30067", "sha256_in_prefix": "02adf80e0ff6cd67600d30cc249a74ff736bc142db721a2312eb4872dcb30067", "size_in_bytes": 24476}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/sql/column_metadata.h", "path_type": "hardlink", "sha256": "7e7147b9329d2827596c83aad4b4e22113a0db5b0c7d9d2a83e4fe44d82bd13c", "sha256_in_prefix": "7e7147b9329d2827596c83aad4b4e22113a0db5b0c7d9d2a83e4fe44d82bd13c", "size_in_bytes": 7366}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/sql/server.h", "path_type": "hardlink", "sha256": "f7354d0a6ca7d10b111add196fad1de76659a65bd5a868dff4c0f9e9052c9343", "sha256_in_prefix": "f7354d0a6ca7d10b111add196fad1de76659a65bd5a868dff4c0f9e9052c9343", "size_in_bytes": 36835}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/sql/server_session_middleware.h", "path_type": "hardlink", "sha256": "0f552e03692aa12481d44cb05d72a1f8b7ac3fb96f222d5fed4bbb943c7b4ee8", "sha256_in_prefix": "0f552e03692aa12481d44cb05d72a1f8b7ac3fb96f222d5fed4bbb943c7b4ee8", "size_in_bytes": 3400}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/sql/server_session_middleware_factory.h", "path_type": "hardlink", "sha256": "ae402ab80d8bcc202c528dd4cfc3636b4a740fc6a5e69d110eeb96dd25d64ee7", "sha256_in_prefix": "ae402ab80d8bcc202c528dd4cfc3636b4a740fc6a5e69d110eeb96dd25d64ee7", "size_in_bytes": 2215}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/sql/types.h", "path_type": "hardlink", "sha256": "6eb90d748b500eaed626943b434224b83db2a7c9ecf22aa4d1a13a4951ca672b", "sha256_in_prefix": "6eb90d748b500eaed626943b434224b83db2a7c9ecf22aa4d1a13a4951ca672b", "size_in_bytes": 40848}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/sql/visibility.h", "path_type": "hardlink", "sha256": "a0e5c087670607bd2b0e4118328dad452d855ecd7d3f2c5f1a28a98ea228c017", "sha256_in_prefix": "a0e5c087670607bd2b0e4118328dad452d855ecd7d3f2c5f1a28a98ea228c017", "size_in_bytes": 1636}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/test_auth_handlers.h", "path_type": "hardlink", "sha256": "5e4bcc5ae72ff4642396ddadb6f631b21ca6e2452e6d474c87eb629a5408b752", "sha256_in_prefix": "5e4bcc5ae72ff4642396ddadb6f631b21ca6e2452e6d474c87eb629a5408b752", "size_in_bytes": 3315}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/test_definitions.h", "path_type": "hardlink", "sha256": "53f1e106735d3599a88eb698cc8081981bee79fd1b4d3e5e633157a7c8861737", "sha256_in_prefix": "53f1e106735d3599a88eb698cc8081981bee79fd1b4d3e5e633157a7c8861737", "size_in_bytes": 13110}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/test_flight_server.h", "path_type": "hardlink", "sha256": "49b46164fd14e082e76e0ee5610bc67979af3ccfc1e9b6a2d7615333f1c3e114", "sha256_in_prefix": "49b46164fd14e082e76e0ee5610bc67979af3ccfc1e9b6a2d7615333f1c3e114", "size_in_bytes": 3930}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/test_util.h", "path_type": "hardlink", "sha256": "1343a50cbc1c9277af29fe0bcf3a9d5378dfc54315fe6708271cb853fba9efb4", "sha256_in_prefix": "1343a50cbc1c9277af29fe0bcf3a9d5378dfc54315fe6708271cb853fba9efb4", "size_in_bytes": 6860}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/transport.h", "path_type": "hardlink", "sha256": "6435dcf9ff28d344c55844b0b06535332eeb47d39f3375fb3998c37065d3c080", "sha256_in_prefix": "6435dcf9ff28d344c55844b0b06535332eeb47d39f3375fb3998c37065d3c080", "size_in_bytes": 12181}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/transport_server.h", "path_type": "hardlink", "sha256": "8957579ab6f6a5e9a1e28e81c70bc1ece64057851ea16adb85ee1e3d9e4f8b8b", "sha256_in_prefix": "8957579ab6f6a5e9a1e28e81c70bc1ece64057851ea16adb85ee1e3d9e4f8b8b", "size_in_bytes": 5268}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/type_fwd.h", "path_type": "hardlink", "sha256": "b5014033740d28f77307856a5067445058cd3d85d564b029c069d2bacd86431f", "sha256_in_prefix": "b5014033740d28f77307856a5067445058cd3d85d564b029c069d2bacd86431f", "size_in_bytes": 1797}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/types.h", "path_type": "hardlink", "sha256": "1341f1a6d895af55016755dc2895598adccc4c20b2bccdac0863eba6aeb14012", "sha256_in_prefix": "1341f1a6d895af55016755dc2895598adccc4c20b2bccdac0863eba6aeb14012", "size_in_bytes": 46681}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/types_async.h", "path_type": "hardlink", "sha256": "de7210ab00983b822bdccb766c6ec1367b57c6e34761034da73f9897711a1534", "sha256_in_prefix": "de7210ab00983b822bdccb766c6ec1367b57c6e34761034da73f9897711a1534", "size_in_bytes": 2599}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/flight/visibility.h", "path_type": "hardlink", "sha256": "37593be1cc3246f39a6056bfb428dd8148e249d3c1866cb6d14b951aed304e0d", "sha256_in_prefix": "37593be1cc3246f39a6056bfb428dd8148e249d3c1866cb6d14b951aed304e0d", "size_in_bytes": 1596}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/integration/json_integration.h", "path_type": "hardlink", "sha256": "a3168f5268d41b4008eabdbf50cbc85f9bfbce5f6aaa2955700661c956b2db87", "sha256_in_prefix": "a3168f5268d41b4008eabdbf50cbc85f9bfbce5f6aaa2955700661c956b2db87", "size_in_bytes": 3828}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/io/api.h", "path_type": "hardlink", "sha256": "3e7e236524ec2d6f0c0253325d4a2498976ea57e78bb5e7819823902f0f90720", "sha256_in_prefix": "3e7e236524ec2d6f0c0253325d4a2498976ea57e78bb5e7819823902f0f90720", "size_in_bytes": 996}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/io/buffered.h", "path_type": "hardlink", "sha256": "3c768cc0231a5eedef023843a59302db1b7984aa2825755e31f7e1a03cd3c4d7", "sha256_in_prefix": "3c768cc0231a5eedef023843a59302db1b7984aa2825755e31f7e1a03cd3c4d7", "size_in_bytes": 5912}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/io/caching.h", "path_type": "hardlink", "sha256": "0008e8c8ac10d3a9b65e2825152e8287f71d836a78fb0900ec66a4188fde5f51", "sha256_in_prefix": "0008e8c8ac10d3a9b65e2825152e8287f71d836a78fb0900ec66a4188fde5f51", "size_in_bytes": 6708}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/io/compressed.h", "path_type": "hardlink", "sha256": "dc9c483a8d6af15863204adfc1533964b564c3040a5ddf854f9e75ee3e7c7ad0", "sha256_in_prefix": "dc9c483a8d6af15863204adfc1533964b564c3040a5ddf854f9e75ee3e7c7ad0", "size_in_bytes": 3774}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/io/concurrency.h", "path_type": "hardlink", "sha256": "4a622bd0e58280c511de3f678156e3309856394ad4d798d07ff8f3dab530eebe", "sha256_in_prefix": "4a622bd0e58280c511de3f678156e3309856394ad4d798d07ff8f3dab530eebe", "size_in_bytes": 7934}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/io/file.h", "path_type": "hardlink", "sha256": "f99124956d50d2c8f7a580902d0d5e6e2aca777b36188def50422ccc5afc9955", "sha256_in_prefix": "f99124956d50d2c8f7a580902d0d5e6e2aca777b36188def50422ccc5afc9955", "size_in_bytes": 7625}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/io/hdfs.h", "path_type": "hardlink", "sha256": "dacddfe3d8200188120ac5f94a8aa7a26c2c5dddb8fc86615be552049725a938", "sha256_in_prefix": "dacddfe3d8200188120ac5f94a8aa7a26c2c5dddb8fc86615be552049725a938", "size_in_bytes": 8559}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/io/interfaces.h", "path_type": "hardlink", "sha256": "4080474c95286c493072a9ca313fc610abb902bce978698afbcbfbcf8a861c84", "sha256_in_prefix": "4080474c95286c493072a9ca313fc610abb902bce978698afbcbfbcf8a861c84", "size_in_bytes": 13428}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/io/memory.h", "path_type": "hardlink", "sha256": "498e77e4310510e217a23b6daec1a632d2b93c29701b1e6c1caf04fd39421824", "sha256_in_prefix": "498e77e4310510e217a23b6daec1a632d2b93c29701b1e6c1caf04fd39421824", "size_in_bytes": 6321}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/io/mman.h", "path_type": "hardlink", "sha256": "aa82c100615cbe9613cbde866bb14d58324a4f7ba1c6914017785b5c86834a26", "sha256_in_prefix": "aa82c100615cbe9613cbde866bb14d58324a4f7ba1c6914017785b5c86834a26", "size_in_bytes": 4111}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/io/slow.h", "path_type": "hardlink", "sha256": "f3e663409ab8f44409e1eb10eaa1c78e5282799360e0148d0fb8ab7be66d2d84", "sha256_in_prefix": "f3e663409ab8f44409e1eb10eaa1c78e5282799360e0148d0fb8ab7be66d2d84", "size_in_bytes": 3942}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/io/stdio.h", "path_type": "hardlink", "sha256": "76a3131e825b9a25dcc8d6b67cdeb4b52590b31d063e98595422dd1a264db7c2", "sha256_in_prefix": "76a3131e825b9a25dcc8d6b67cdeb4b52590b31d063e98595422dd1a264db7c2", "size_in_bytes": 2095}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/io/test_common.h", "path_type": "hardlink", "sha256": "485e7da1d6f4137aed1a3bb98c006c43365c522f609794f01ee2a0e1fd044396", "sha256_in_prefix": "485e7da1d6f4137aed1a3bb98c006c43365c522f609794f01ee2a0e1fd044396", "size_in_bytes": 2146}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/io/transform.h", "path_type": "hardlink", "sha256": "5bd5d6a27c3af55ca6400690a6d7d6ee30feeabcbb542a5f3d796407b699cce1", "sha256_in_prefix": "5bd5d6a27c3af55ca6400690a6d7d6ee30feeabcbb542a5f3d796407b699cce1", "size_in_bytes": 1890}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/io/type_fwd.h", "path_type": "hardlink", "sha256": "3e2ec416916f057b05375c4a3b26634d23fde71343b3a5b98716f91ae7285551", "sha256_in_prefix": "3e2ec416916f057b05375c4a3b26634d23fde71343b3a5b98716f91ae7285551", "size_in_bytes": 2315}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/api.h", "path_type": "hardlink", "sha256": "a2591dbbcda64d2f219b00f9dc304924be90434601a6586cfacfa6e2e3889d24", "sha256_in_prefix": "a2591dbbcda64d2f219b00f9dc304924be90434601a6586cfacfa6e2e3889d24", "size_in_bytes": 1007}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/dictionary.h", "path_type": "hardlink", "sha256": "5138d93c81bc98b64e93d216d909d1f51646af59e97b164ea75d377eff8eef41", "sha256_in_prefix": "5138d93c81bc98b64e93d216d909d1f51646af59e97b164ea75d377eff8eef41", "size_in_bytes": 6104}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/feather.h", "path_type": "hardlink", "sha256": "b829f1c0eede507d7c909fa55b3f485b04a3e808df7a3aaa2dda227c9f94043a", "sha256_in_prefix": "b829f1c0eede507d7c909fa55b3f485b04a3e808df7a3aaa2dda227c9f94043a", "size_in_bytes": 4918}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/json_simple.h", "path_type": "hardlink", "sha256": "223163c7a67b87f58b5edd696952096e850e4d1e4614185652109b9bf9bd94d9", "sha256_in_prefix": "223163c7a67b87f58b5edd696952096e850e4d1e4614185652109b9bf9bd94d9", "size_in_bytes": 2455}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/message.h", "path_type": "hardlink", "sha256": "2ad3026c80b6278fb98b23c6e528a3aaefcc00bc62b8a5816198469f0d23c4e4", "sha256_in_prefix": "2ad3026c80b6278fb98b23c6e528a3aaefcc00bc62b8a5816198469f0d23c4e4", "size_in_bytes": 20011}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/options.h", "path_type": "hardlink", "sha256": "5f605b09a434dd2d6ea9e7602d16ef2f27dbd4f1d9ed61910630cb2c26d03061", "sha256_in_prefix": "5f605b09a434dd2d6ea9e7602d16ef2f27dbd4f1d9ed61910630cb2c26d03061", "size_in_bytes": 6888}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/reader.h", "path_type": "hardlink", "sha256": "36a76baaa00422d3b579c61420d44eefeaa12a5632f821d224a188da17579514", "sha256_in_prefix": "36a76baaa00422d3b579c61420d44eefeaa12a5632f821d224a188da17579514", "size_in_bytes": 24106}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/test_common.h", "path_type": "hardlink", "sha256": "fe458e47ff982ad8a57022162ba238598a3c7dc46de9e04c7f110ce240ed636d", "sha256_in_prefix": "fe458e47ff982ad8a57022162ba238598a3c7dc46de9e04c7f110ce240ed636d", "size_in_bytes": 6351}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/type_fwd.h", "path_type": "hardlink", "sha256": "4f2f044fb9cb238249793a833323f4a441fd4158fdc6cec1a49919ae7ae968f6", "sha256_in_prefix": "4f2f044fb9cb238249793a833323f4a441fd4158fdc6cec1a49919ae7ae968f6", "size_in_bytes": 1440}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/util.h", "path_type": "hardlink", "sha256": "c1391f0bd605299940023cb396641565c5bdd283a3fc96ad8c3378ab3d08c478", "sha256_in_prefix": "c1391f0bd605299940023cb396641565c5bdd283a3fc96ad8c3378ab3d08c478", "size_in_bytes": 1414}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/ipc/writer.h", "path_type": "hardlink", "sha256": "86e9bc13fa2b906fd7dfcbe0c9fc8a8466f2bdc2c9dc09171329320630172188", "sha256_in_prefix": "86e9bc13fa2b906fd7dfcbe0c9fc8a8466f2bdc2c9dc09171329320630172188", "size_in_bytes": 18870}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/json/api.h", "path_type": "hardlink", "sha256": "5d15b57cfe37cd5ab0cb5c9a6da29cb4d2bd30366a9f19281c31f57f1e41dd8e", "sha256_in_prefix": "5d15b57cfe37cd5ab0cb5c9a6da29cb4d2bd30366a9f19281c31f57f1e41dd8e", "size_in_bytes": 879}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/json/chunked_builder.h", "path_type": "hardlink", "sha256": "0c3b8cc2b2263040b0e8c85f9dc9f6c4c3a390570a515d4ed79f7bfdf485480b", "sha256_in_prefix": "0c3b8cc2b2263040b0e8c85f9dc9f6c4c3a390570a515d4ed79f7bfdf485480b", "size_in_bytes": 2365}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/json/chunker.h", "path_type": "hardlink", "sha256": "76464e731b05d50dde939f0fec8a00f1fde5432050a45bc64857b29cd5763a57", "sha256_in_prefix": "76464e731b05d50dde939f0fec8a00f1fde5432050a45bc64857b29cd5763a57", "size_in_bytes": 1119}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/json/converter.h", "path_type": "hardlink", "sha256": "de55ec3f70527692cf224140267616f6f3fc05b5f79de55847f5b4cc5282950d", "sha256_in_prefix": "de55ec3f70527692cf224140267616f6f3fc05b5f79de55847f5b4cc5282950d", "size_in_bytes": 3134}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/json/object_parser.h", "path_type": "hardlink", "sha256": "63fe8e71ec9ad3a694c9ea3ed64d38edd9b6f8950c25adbfc3d8b267e8282114", "sha256_in_prefix": "63fe8e71ec9ad3a694c9ea3ed64d38edd9b6f8950c25adbfc3d8b267e8282114", "size_in_bytes": 1627}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/json/object_writer.h", "path_type": "hardlink", "sha256": "52b22b8c2908cfb4399ff16957934d3c3f7a8071dd4e4bd325a7a4b86047c13a", "sha256_in_prefix": "52b22b8c2908cfb4399ff16957934d3c3f7a8071dd4e4bd325a7a4b86047c13a", "size_in_bytes": 1428}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/json/options.h", "path_type": "hardlink", "sha256": "132a50803c0b6506eb3e7021e399d23dfa46198af1bcb81fa757801bf974a774", "sha256_in_prefix": "132a50803c0b6506eb3e7021e399d23dfa46198af1bcb81fa757801bf974a774", "size_in_bytes": 2227}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/json/parser.h", "path_type": "hardlink", "sha256": "de82333b9914b364da91ceedfdde661fb6e9d6e21c73533ea9bb8798fa12237e", "sha256_in_prefix": "de82333b9914b364da91ceedfdde661fb6e9d6e21c73533ea9bb8798fa12237e", "size_in_bytes": 3383}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/json/rapidjson_defs.h", "path_type": "hardlink", "sha256": "9412657ee61621e410f1ac0f7776e4e2325cf3579faff2b32b01bc2a5c3bb75b", "sha256_in_prefix": "9412657ee61621e410f1ac0f7776e4e2325cf3579faff2b32b01bc2a5c3bb75b", "size_in_bytes": 1474}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/json/reader.h", "path_type": "hardlink", "sha256": "28d3bd742c9cd9166ced6c544845bb6e9098061fe1d42dd4e766076310673f43", "sha256_in_prefix": "28d3bd742c9cd9166ced6c544845bb6e9098061fe1d42dd4e766076310673f43", "size_in_bytes": 5212}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/json/test_common.h", "path_type": "hardlink", "sha256": "622898fe3b30a69ecdbba216d58da5061a9648546835a344cb58c7779aa46074", "sha256_in_prefix": "622898fe3b30a69ecdbba216d58da5061a9648546835a344cb58c7779aa46074", "size_in_bytes": 10874}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/json/type_fwd.h", "path_type": "hardlink", "sha256": "a3d6a2801e65a2c92724515e8b5936e690d56198240b67a5991317d42e9a4e3a", "sha256_in_prefix": "a3d6a2801e65a2c92724515e8b5936e690d56198240b67a5991317d42e9a4e3a", "size_in_bytes": 942}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/memory_pool.h", "path_type": "hardlink", "sha256": "779b8c966469e02dd3aef2e3e3bfbde4baaaf3455fd45d38142e7341c463b7dd", "sha256_in_prefix": "779b8c966469e02dd3aef2e3e3bfbde4baaaf3455fd45d38142e7341c463b7dd", "size_in_bytes": 11392}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/memory_pool_test.h", "path_type": "hardlink", "sha256": "aafedcb24ea16623b610b145fb5cae929a698c44c30d7d27b811416cf147b4c5", "sha256_in_prefix": "aafedcb24ea16623b610b145fb5cae929a698c44c30d7d27b811416cf147b4c5", "size_in_bytes": 3350}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/pch.h", "path_type": "hardlink", "sha256": "31a47d6eacb67056436e3abc01e92af46875bf32d395664e487bbe1a158fd60f", "sha256_in_prefix": "31a47d6eacb67056436e3abc01e92af46875bf32d395664e487bbe1a158fd60f", "size_in_bytes": 1286}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/pretty_print.h", "path_type": "hardlink", "sha256": "64396ba0f46bf7faf20a4ee1feb8c0f292fb04d81a250f479d16f63d953ade58", "sha256_in_prefix": "64396ba0f46bf7faf20a4ee1feb8c0f292fb04d81a250f479d16f63d953ade58", "size_in_bytes": 5529}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/api.h", "path_type": "hardlink", "sha256": "5ab5d6ca18b9e9efa5009d900d9224af14825a88986cc8433fede7f0439dd242", "sha256_in_prefix": "5ab5d6ca18b9e9efa5009d900d9224af14825a88986cc8433fede7f0439dd242", "size_in_bytes": 1148}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/arrow_to_pandas.h", "path_type": "hardlink", "sha256": "8d404450c297c3bd2825d32582449fe878ad68dc1e41c73b87123be7f0bd5922", "sha256_in_prefix": "8d404450c297c3bd2825d32582449fe878ad68dc1e41c73b87123be7f0bd5922", "size_in_bytes": 5561}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/async.h", "path_type": "hardlink", "sha256": "0b47fc6189a0c011a00dabb8c4416c763ba489907862fa729444c7119af21cea", "sha256_in_prefix": "0b47fc6189a0c011a00dabb8c4416c763ba489907862fa729444c7119af21cea", "size_in_bytes": 2352}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/benchmark.h", "path_type": "hardlink", "sha256": "7fe933c8c3a53ca0ec7b66dc2d687232b0c4319ac6fc91c03e90c98065b46d75", "sha256_in_prefix": "7fe933c8c3a53ca0ec7b66dc2d687232b0c4319ac6fc91c03e90c98065b46d75", "size_in_bytes": 1192}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/common.h", "path_type": "hardlink", "sha256": "ca39637c92b57fbb2567b0c14382d3a3fa686fbd338a8b3024d59acf2d29fae3", "sha256_in_prefix": "ca39637c92b57fbb2567b0c14382d3a3fa686fbd338a8b3024d59acf2d29fae3", "size_in_bytes": 14412}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/csv.h", "path_type": "hardlink", "sha256": "43153707e1effd1b2811c3064bdfb5e37e2e828b8bdb2802eb82eae8582f88d3", "sha256_in_prefix": "43153707e1effd1b2811c3064bdfb5e37e2e828b8bdb2802eb82eae8582f88d3", "size_in_bytes": 1397}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/datetime.h", "path_type": "hardlink", "sha256": "067cbf4c71a2dadc94787c5c3aec34d4eee1344f01fe06af7b900101942dc134", "sha256_in_prefix": "067cbf4c71a2dadc94787c5c3aec34d4eee1344f01fe06af7b900101942dc134", "size_in_bytes": 7931}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/decimal.h", "path_type": "hardlink", "sha256": "2bd49c9b349aed70aebb0a572a7ab418f6d10d3b91539c977d6afdcc6b4e7217", "sha256_in_prefix": "2bd49c9b349aed70aebb0a572a7ab418f6d10d3b91539c977d6afdcc6b4e7217", "size_in_bytes": 6362}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/extension_type.h", "path_type": "hardlink", "sha256": "d20cdbe36cbf99bc387ec62cdeef1cc0f14b051946fa490740b81bbc6b6b6345", "sha256_in_prefix": "d20cdbe36cbf99bc387ec62cdeef1cc0f14b051946fa490740b81bbc6b6b6345", "size_in_bytes": 3181}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/filesystem.h", "path_type": "hardlink", "sha256": "146d0070b7a4a9a0dff4840fa8a8b101f21c63f64b80828fe4dbef5ddb415543", "sha256_in_prefix": "146d0070b7a4a9a0dff4840fa8a8b101f21c63f64b80828fe4dbef5ddb415543", "size_in_bytes": 5126}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/flight.h", "path_type": "hardlink", "sha256": "b78643f2059ab9b6cefe6043bf45d9e7c4c2a355509748731e7d7fef0e14eb82", "sha256_in_prefix": "b78643f2059ab9b6cefe6043bf45d9e7c4c2a355509748731e7d7fef0e14eb82", "size_in_bytes": 14450}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/gdb.h", "path_type": "hardlink", "sha256": "1feaaf33e9d4f1aff7679b64f0fbe9a53c10b4131266140a408560440b117c58", "sha256_in_prefix": "1feaaf33e9d4f1aff7679b64f0fbe9a53c10b4131266140a408560440b117c58", "size_in_bytes": 972}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/helpers.h", "path_type": "hardlink", "sha256": "8d534511bbc95e609c789b62dc9dfe327664365268ca740dab7df8b6ddbd6dbb", "sha256_in_prefix": "8d534511bbc95e609c789b62dc9dfe327664365268ca740dab7df8b6ddbd6dbb", "size_in_bytes": 5489}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/inference.h", "path_type": "hardlink", "sha256": "15416f078672ed5fadb9e5dd99b0dca9378b2b8c63e466447915b9ca188996c5", "sha256_in_prefix": "15416f078672ed5fadb9e5dd99b0dca9378b2b8c63e466447915b9ca188996c5", "size_in_bytes": 2038}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/io.h", "path_type": "hardlink", "sha256": "e231a7a1da525259d5a80561f5f58877b1f85a57652cf917caba0006975a5bac", "sha256_in_prefix": "e231a7a1da525259d5a80561f5f58877b1f85a57652cf917caba0006975a5bac", "size_in_bytes": 3858}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/ipc.h", "path_type": "hardlink", "sha256": "4996f0ea3082a8b88b34263793adf61a6c07783febff1ac34b4761a95e3d5616", "sha256_in_prefix": "4996f0ea3082a8b88b34263793adf61a6c07783febff1ac34b4761a95e3d5616", "size_in_bytes": 2259}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/iterators.h", "path_type": "hardlink", "sha256": "5207e6dc9bdeb401f497ea008e3a597e1ad406a46299531ac38fb1bacbea2d28", "sha256_in_prefix": "5207e6dc9bdeb401f497ea008e3a597e1ad406a46299531ac38fb1bacbea2d28", "size_in_bytes": 7327}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/lib.h", "path_type": "hardlink", "sha256": "69600e4a549271dbaf7378063ba69201670d065d901025847500196fa98897f5", "sha256_in_prefix": "69600e4a549271dbaf7378063ba69201670d065d901025847500196fa98897f5", "size_in_bytes": 4631}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/lib_api.h", "path_type": "hardlink", "sha256": "e2888a5a6761ca39f93b10d205a9abba41e95b041b522a0347b5fcd9d860757b", "sha256_in_prefix": "e2888a5a6761ca39f93b10d205a9abba41e95b041b522a0347b5fcd9d860757b", "size_in_bytes": 19487}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/numpy_convert.h", "path_type": "hardlink", "sha256": "cb5dde1f07ded6524acda7684ebdbe1b25fac4fb04e99ec5377d6cecf37ed919", "sha256_in_prefix": "cb5dde1f07ded6524acda7684ebdbe1b25fac4fb04e99ec5377d6cecf37ed919", "size_in_bytes": 4870}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/numpy_init.h", "path_type": "hardlink", "sha256": "1678951cfed6d980657a7a0c62142b383be8aaabc331296cd8900d1ad34f42db", "sha256_in_prefix": "1678951cfed6d980657a7a0c62142b383be8aaabc331296cd8900d1ad34f42db", "size_in_bytes": 999}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/numpy_interop.h", "path_type": "hardlink", "sha256": "ac8e9e93c253398b63a3b804003483052e90b803876b60346103d9d867b2a45c", "sha256_in_prefix": "ac8e9e93c253398b63a3b804003483052e90b803876b60346103d9d867b2a45c", "size_in_bytes": 3418}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/numpy_to_arrow.h", "path_type": "hardlink", "sha256": "cfd29aa6cba8392a5620b3edf5b79aec647804be80436f13e8350ed264a48779", "sha256_in_prefix": "cfd29aa6cba8392a5620b3edf5b79aec647804be80436f13e8350ed264a48779", "size_in_bytes": 2760}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/parquet_encryption.h", "path_type": "hardlink", "sha256": "31cf2d67c8087e41f401c90d8883adea17ac3ff31529e2a1732b53db864e2dd4", "sha256_in_prefix": "31cf2d67c8087e41f401c90d8883adea17ac3ff31529e2a1732b53db864e2dd4", "size_in_bytes": 4861}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/pch.h", "path_type": "hardlink", "sha256": "be46e04ad423abcdb461e1e55c13ddcd0f96f4bcb326b4c67cc0699cc32a6a19", "sha256_in_prefix": "be46e04ad423abcdb461e1e55c13ddcd0f96f4bcb326b4c67cc0699cc32a6a19", "size_in_bytes": 1129}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/platform.h", "path_type": "hardlink", "sha256": "5d84b922a88c51e8f137608eceeeb466cf1bff001a193070e0cfb3295aaab395", "sha256_in_prefix": "5d84b922a88c51e8f137608eceeeb466cf1bff001a193070e0cfb3295aaab395", "size_in_bytes": 1422}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/pyarrow.h", "path_type": "hardlink", "sha256": "4cadc1b43f67dd028e43d757dcb5db41cd21bbd6a559cb9f5743bdde2416ec1d", "sha256_in_prefix": "4cadc1b43f67dd028e43d757dcb5db41cd21bbd6a559cb9f5743bdde2416ec1d", "size_in_bytes": 2761}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/pyarrow_api.h", "path_type": "hardlink", "sha256": "ee5d06e3efe6f7200b62889fb18f19eaa8771c70f43e09295528029ff25a194e", "sha256_in_prefix": "ee5d06e3efe6f7200b62889fb18f19eaa8771c70f43e09295528029ff25a194e", "size_in_bytes": 867}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/pyarrow_lib.h", "path_type": "hardlink", "sha256": "fbbd3f0a48f7ff4226973697489384fdddf0f6918ceba9578863f2967f5cf7a6", "sha256_in_prefix": "fbbd3f0a48f7ff4226973697489384fdddf0f6918ceba9578863f2967f5cf7a6", "size_in_bytes": 863}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/python_test.h", "path_type": "hardlink", "sha256": "79adf698cdb4b87c92972822f4cb55c6bdba3bec9d4d91c2510225c5a20c8d3e", "sha256_in_prefix": "79adf698cdb4b87c92972822f4cb55c6bdba3bec9d4d91c2510225c5a20c8d3e", "size_in_bytes": 1195}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/python_to_arrow.h", "path_type": "hardlink", "sha256": "068572b5fe8fecf0585f2ce7721125299485bc4b05ca2981fad2c5770d0050da", "sha256_in_prefix": "068572b5fe8fecf0585f2ce7721125299485bc4b05ca2981fad2c5770d0050da", "size_in_bytes": 2521}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/type_traits.h", "path_type": "hardlink", "sha256": "07f36c453fe1646f03f75b13722849c8a1794abb253dc1668f5d907dba47b8b2", "sha256_in_prefix": "07f36c453fe1646f03f75b13722849c8a1794abb253dc1668f5d907dba47b8b2", "size_in_bytes": 10093}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/udf.h", "path_type": "hardlink", "sha256": "75edd1f0f84d24ee654fda02a91c5ef1edbf484de30691ce9306cda82ae58234", "sha256_in_prefix": "75edd1f0f84d24ee654fda02a91c5ef1edbf484de30691ce9306cda82ae58234", "size_in_bytes": 3104}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/vendored/pythoncapi_compat.h", "path_type": "hardlink", "sha256": "6f33279474c27e39390d0448c70cadba7621e5a43153789212569a0f2367018f", "sha256_in_prefix": "6f33279474c27e39390d0448c70cadba7621e5a43153789212569a0f2367018f", "size_in_bytes": 40900}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/python/visibility.h", "path_type": "hardlink", "sha256": "870270e6c1ab58972442435a02e2dee137fe5438d06d7927cb334e560648dc52", "sha256_in_prefix": "870270e6c1ab58972442435a02e2dee137fe5438d06d7927cb334e560648dc52", "size_in_bytes": 1381}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/record_batch.h", "path_type": "hardlink", "sha256": "37bd727360761d1fae8b9bab1438eafd3f262dcf699ca93e3b2cc8a4c2925df0", "sha256_in_prefix": "37bd727360761d1fae8b9bab1438eafd3f262dcf699ca93e3b2cc8a4c2925df0", "size_in_bytes": 18533}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/result.h", "path_type": "hardlink", "sha256": "f15dbf3bc8aa641c62d1be01f5f73da431f8f98a97beb7508b5d2bc88b18df23", "sha256_in_prefix": "f15dbf3bc8aa641c62d1be01f5f73da431f8f98a97beb7508b5d2bc88b18df23", "size_in_bytes": 18185}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/scalar.h", "path_type": "hardlink", "sha256": "ed282e4af278c28863a95f8548a6e80b5a60685541d3d74d7e4333dfa26370cf", "sha256_in_prefix": "ed282e4af278c28863a95f8548a6e80b5a60685541d3d74d7e4333dfa26370cf", "size_in_bytes": 36543}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/sparse_tensor.h", "path_type": "hardlink", "sha256": "75de9e4260a37c29a623bea182c0b7ed1faa3c9d7520c85a7d56b14a8d97245b", "sha256_in_prefix": "75de9e4260a37c29a623bea182c0b7ed1faa3c9d7520c85a7d56b14a8d97245b", "size_in_bytes": 25205}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/status.h", "path_type": "hardlink", "sha256": "058d01fb316ef37733bdcd1bbc116427de9b5a36750f12452b59d8ae0060da44", "sha256_in_prefix": "058d01fb316ef37733bdcd1bbc116427de9b5a36750f12452b59d8ae0060da44", "size_in_bytes": 16401}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/stl.h", "path_type": "hardlink", "sha256": "32d1165c6df1d5860d8bbc4f743386551aa99cb56f35016213f64902c3c52758", "sha256_in_prefix": "32d1165c6df1d5860d8bbc4f743386551aa99cb56f35016213f64902c3c52758", "size_in_bytes": 19343}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/stl_allocator.h", "path_type": "hardlink", "sha256": "4c16ef8dbb90207f72f3c148d926aa00bee9388b77c19d7130ac17a9e28d8891", "sha256_in_prefix": "4c16ef8dbb90207f72f3c148d926aa00bee9388b77c19d7130ac17a9e28d8891", "size_in_bytes": 4956}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/stl_iterator.h", "path_type": "hardlink", "sha256": "45e94d42b0031eea4a593b850420a4a95972b861d7537c81ea072c0e9429adaf", "sha256_in_prefix": "45e94d42b0031eea4a593b850420a4a95972b861d7537c81ea072c0e9429adaf", "size_in_bytes": 9953}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/table.h", "path_type": "hardlink", "sha256": "5288b15c69394b5724577e6eb578db03e294050ad27aabdfae14a6936da4efad", "sha256_in_prefix": "5288b15c69394b5724577e6eb578db03e294050ad27aabdfae14a6936da4efad", "size_in_bytes": 14647}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/table_builder.h", "path_type": "hardlink", "sha256": "2d170b08bda252b8fabc5e1ff408cfb30563aadaa5330ef3ffc54101f509782a", "sha256_in_prefix": "2d170b08bda252b8fabc5e1ff408cfb30563aadaa5330ef3ffc54101f509782a", "size_in_bytes": 3763}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/telemetry/logging.h", "path_type": "hardlink", "sha256": "e1791aadcacf2e922a8ff11cbd8898c55fc060ce5d5b8f1397127055f918aa3e", "sha256_in_prefix": "e1791aadcacf2e922a8ff11cbd8898c55fc060ce5d5b8f1397127055f918aa3e", "size_in_bytes": 3400}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/tensor.h", "path_type": "hardlink", "sha256": "9a03e42797f99e0974a8391e61ffae93e06d5fb1b2afed0352e5f5a81e9869d1", "sha256_in_prefix": "9a03e42797f99e0974a8391e61ffae93e06d5fb1b2afed0352e5f5a81e9869d1", "size_in_bytes": 9093}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/tensor/converter.h", "path_type": "hardlink", "sha256": "459ab44ebcbf922674f39fddfc2be17b032c779ec89c66f64c27a2bde69ff8ea", "sha256_in_prefix": "459ab44ebcbf922674f39fddfc2be17b032c779ec89c66f64c27a2bde69ff8ea", "size_in_bytes": 2891}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/testing/async_test_util.h", "path_type": "hardlink", "sha256": "22b1d67cf788ca1ae079318750f2edf762ddb287e6157ea48639e05ac66fddd6", "sha256_in_prefix": "22b1d67cf788ca1ae079318750f2edf762ddb287e6157ea48639e05ac66fddd6", "size_in_bytes": 2262}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/testing/builder.h", "path_type": "hardlink", "sha256": "e31d1b58e79d695a26594d26edd17df62acebf77e547efe9f883287d30d9b70a", "sha256_in_prefix": "e31d1b58e79d695a26594d26edd17df62acebf77e547efe9f883287d30d9b70a", "size_in_bytes": 8556}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/testing/executor_util.h", "path_type": "hardlink", "sha256": "dfcfeb17e57ff73175b6d24cb2990f888fb7e155354438e0d400c14bc9541479", "sha256_in_prefix": "dfcfeb17e57ff73175b6d24cb2990f888fb7e155354438e0d400c14bc9541479", "size_in_bytes": 1885}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/testing/extension_type.h", "path_type": "hardlink", "sha256": "e65ff6f3e49da0ed2beabfa756a917b1f4911564cb4cf80e144a57cd98aa87a5", "sha256_in_prefix": "e65ff6f3e49da0ed2beabfa756a917b1f4911564cb4cf80e144a57cd98aa87a5", "size_in_bytes": 7430}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/testing/fixed_width_test_util.h", "path_type": "hardlink", "sha256": "83ac81ed1933894ec71213499f13a1927da71391de5da157ded6c15f7abdfec1", "sha256_in_prefix": "83ac81ed1933894ec71213499f13a1927da71391de5da157ded6c15f7abdfec1", "size_in_bytes": 3091}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/testing/future_util.h", "path_type": "hardlink", "sha256": "a88862e35ece18c5923144831e39064d877e8a165bab0f19488470274d6f6ca8", "sha256_in_prefix": "a88862e35ece18c5923144831e39064d877e8a165bab0f19488470274d6f6ca8", "size_in_bytes": 6246}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/testing/generator.h", "path_type": "hardlink", "sha256": "5f7306754db9c08d668c9339b36b6518396f0c080b2fc7e89b4bf916ef1a2238", "sha256_in_prefix": "5f7306754db9c08d668c9339b36b6518396f0c080b2fc7e89b4bf916ef1a2238", "size_in_bytes": 13180}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/testing/gtest_compat.h", "path_type": "hardlink", "sha256": "d0da87dfd9b2ee6d45329b2b4189f1431e1b744135d1209765acac37aca34050", "sha256_in_prefix": "d0da87dfd9b2ee6d45329b2b4189f1431e1b744135d1209765acac37aca34050", "size_in_bytes": 1311}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/testing/gtest_util.h", "path_type": "hardlink", "sha256": "8e75466cce779e75cee37dda50d9991e5332890d5fb443484cb6eda9117a474f", "sha256_in_prefix": "8e75466cce779e75cee37dda50d9991e5332890d5fb443484cb6eda9117a474f", "size_in_bytes": 24496}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/testing/matchers.h", "path_type": "hardlink", "sha256": "df2b3b508e98a4578cbc50a08e617f5569f5c3b1f386a6ebd9cfbf12e241a675", "sha256_in_prefix": "df2b3b508e98a4578cbc50a08e617f5569f5c3b1f386a6ebd9cfbf12e241a675", "size_in_bytes": 16852}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/testing/math.h", "path_type": "hardlink", "sha256": "611a0d55956b785d63f06d46f2869a17378aed5198036436e63d7480537f8530", "sha256_in_prefix": "611a0d55956b785d63f06d46f2869a17378aed5198036436e63d7480537f8530", "size_in_bytes": 1210}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/testing/pch.h", "path_type": "hardlink", "sha256": "c0a3cde2b66755c41b9a99f4d92c79b526bbf8c121a54475c3e609e9daedc913", "sha256_in_prefix": "c0a3cde2b66755c41b9a99f4d92c79b526bbf8c121a54475c3e609e9daedc913", "size_in_bytes": 1164}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/testing/process.h", "path_type": "hardlink", "sha256": "0333d6dcb876478b139be454522e0e7776926daf73a0b712ff31c1c73b6fe332", "sha256_in_prefix": "0333d6dcb876478b139be454522e0e7776926daf73a0b712ff31c1c73b6fe332", "size_in_bytes": 1372}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/testing/random.h", "path_type": "hardlink", "sha256": "50cc62a10391be864eb2875933a4feba3cdae56b982b002775bbe7388983b2a4", "sha256_in_prefix": "50cc62a10391be864eb2875933a4feba3cdae56b982b002775bbe7388983b2a4", "size_in_bytes": 37046}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/testing/uniform_real.h", "path_type": "hardlink", "sha256": "f86ff627d72f7afa02b41e79bec0ac5ad2643141cb20cc8ec1d4fa1bc656e396", "sha256_in_prefix": "f86ff627d72f7afa02b41e79bec0ac5ad2643141cb20cc8ec1d4fa1bc656e396", "size_in_bytes": 2970}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/testing/util.h", "path_type": "hardlink", "sha256": "c4408f49717c2db4bd1560cb222beb24c5e98cc5344dffaf67a60ca6da379984", "sha256_in_prefix": "c4408f49717c2db4bd1560cb222beb24c5e98cc5344dffaf67a60ca6da379984", "size_in_bytes": 5582}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/testing/visibility.h", "path_type": "hardlink", "sha256": "fb08dcd3440887281725aeed9276c82faf39010d709f208fafe12d5739339aad", "sha256_in_prefix": "fb08dcd3440887281725aeed9276c82faf39010d709f208fafe12d5739339aad", "size_in_bytes": 1606}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/type.h", "path_type": "hardlink", "sha256": "5ef38df1021a233b8c1a0ddce83399edc56c8c6110ffa913472fc5f3a1008106", "sha256_in_prefix": "5ef38df1021a233b8c1a0ddce83399edc56c8c6110ffa913472fc5f3a1008106", "size_in_bytes": 96939}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/type_fwd.h", "path_type": "hardlink", "sha256": "5fe76b130302657879c6df378a57cdbd12bb7f99f2117667776493e0d5007dfb", "sha256_in_prefix": "5fe76b130302657879c6df378a57cdbd12bb7f99f2117667776493e0d5007dfb", "size_in_bytes": 23504}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/type_traits.h", "path_type": "hardlink", "sha256": "46693c89479ee52bfd692805bc7e95ae57c3acac10d5bf43532da5c4a1129bb9", "sha256_in_prefix": "46693c89479ee52bfd692805bc7e95ae57c3acac10d5bf43532da5c4a1129bb9", "size_in_bytes": 55275}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/algorithm.h", "path_type": "hardlink", "sha256": "d38e44573b02f6b4e1951545682a019a6b56666172e72dbc3d1f726a99fdb176", "sha256_in_prefix": "d38e44573b02f6b4e1951545682a019a6b56666172e72dbc3d1f726a99fdb176", "size_in_bytes": 1229}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/align_util.h", "path_type": "hardlink", "sha256": "0c6d8bdb8291793894f271695cb8a06df9642a8b8a5933d47faa2c42cea6c626", "sha256_in_prefix": "0c6d8bdb8291793894f271695cb8a06df9642a8b8a5933d47faa2c42cea6c626", "size_in_bytes": 10669}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/aligned_storage.h", "path_type": "hardlink", "sha256": "7b4a424c167d867f550154e710db9b6c48a2ff08ad17bdf151e05934841def72", "sha256_in_prefix": "7b4a424c167d867f550154e710db9b6c48a2ff08ad17bdf151e05934841def72", "size_in_bytes": 4254}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/async_generator.h", "path_type": "hardlink", "sha256": "6af547dc93da0e4fefac487cba13cba2954ddcb4a7fb6f72b2ddb71a8a6d651f", "sha256_in_prefix": "6af547dc93da0e4fefac487cba13cba2954ddcb4a7fb6f72b2ddb71a8a6d651f", "size_in_bytes": 78200}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/async_generator_fwd.h", "path_type": "hardlink", "sha256": "43cb2e798656c1dc646950da6ee8251ccdd7b8ffbe35ead882af14c6c5478056", "sha256_in_prefix": "43cb2e798656c1dc646950da6ee8251ccdd7b8ffbe35ead882af14c6c5478056", "size_in_bytes": 1728}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/async_util.h", "path_type": "hardlink", "sha256": "d679c0259db688aef04b39af643a373cc86e5aa248b76a8a7655f34f286808ae", "sha256_in_prefix": "d679c0259db688aef04b39af643a373cc86e5aa248b76a8a7655f34f286808ae", "size_in_bytes": 19759}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/base64.h", "path_type": "hardlink", "sha256": "ab370113df1c83c4f1e623c902f4383dd7f6c9ce91dabfb8c86252d7f0c421e6", "sha256_in_prefix": "ab370113df1c83c4f1e623c902f4383dd7f6c9ce91dabfb8c86252d7f0c421e6", "size_in_bytes": 1095}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/basic_decimal.h", "path_type": "hardlink", "sha256": "436cdf92c51d61cf764cbf29a3cd48fda40e9b01d3c1918f8ea5ef24cedc81e9", "sha256_in_prefix": "436cdf92c51d61cf764cbf29a3cd48fda40e9b01d3c1918f8ea5ef24cedc81e9", "size_in_bytes": 33569}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/benchmark_util.h", "path_type": "hardlink", "sha256": "486de07f013ec06359030a4bdd3bdf7e74a2646336733b55e714419dbab2d8cc", "sha256_in_prefix": "486de07f013ec06359030a4bdd3bdf7e74a2646336733b55e714419dbab2d8cc", "size_in_bytes": 7641}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/binary_view_util.h", "path_type": "hardlink", "sha256": "fac140417f5c9df5a6999268f2cb455f9be42648bb4f6525a00bc3cd83b430df", "sha256_in_prefix": "fac140417f5c9df5a6999268f2cb455f9be42648bb4f6525a00bc3cd83b430df", "size_in_bytes": 4625}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/bit_block_counter.h", "path_type": "hardlink", "sha256": "89221e9b38b3c55a24c02d197ba4a34a2f9a97f9eb3f65625c5e893e82155167", "sha256_in_prefix": "89221e9b38b3c55a24c02d197ba4a34a2f9a97f9eb3f65625c5e893e82155167", "size_in_bytes": 20162}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/bit_run_reader.h", "path_type": "hardlink", "sha256": "2160f0c3a0e6f0e16c091949d2112924a88730ade750cde9a9b774f666617084", "sha256_in_prefix": "2160f0c3a0e6f0e16c091949d2112924a88730ade750cde9a9b774f666617084", "size_in_bytes": 16616}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/bit_util.h", "path_type": "hardlink", "sha256": "4b44db45e65eb7c3293c5664f708df6337caa6406abad86490593642dc7307b5", "sha256_in_prefix": "4b44db45e65eb7c3293c5664f708df6337caa6406abad86490593642dc7307b5", "size_in_bytes": 12108}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/bitmap.h", "path_type": "hardlink", "sha256": "a83a0d97e4bc405a19db6d07b00b4037eb3e5e6e497278ce5cd38675a22cb0bd", "sha256_in_prefix": "a83a0d97e4bc405a19db6d07b00b4037eb3e5e6e497278ce5cd38675a22cb0bd", "size_in_bytes": 17462}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/bitmap_builders.h", "path_type": "hardlink", "sha256": "dbcae307d006c06f95333b9e9fd14046cb1d32e974449c766663c540bf525751", "sha256_in_prefix": "dbcae307d006c06f95333b9e9fd14046cb1d32e974449c766663c540bf525751", "size_in_bytes": 1596}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/bitmap_generate.h", "path_type": "hardlink", "sha256": "9ba66c370c751a1b0492d42beb73715c74245f607b36d8b4d755d8b0f836c5fa", "sha256_in_prefix": "9ba66c370c751a1b0492d42beb73715c74245f607b36d8b4d755d8b0f836c5fa", "size_in_bytes": 3661}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/bitmap_ops.h", "path_type": "hardlink", "sha256": "7cf3c3fdcbd779b60bac0790382098c9d5350301747da3790f040a57ca29f84b", "sha256_in_prefix": "7cf3c3fdcbd779b60bac0790382098c9d5350301747da3790f040a57ca29f84b", "size_in_bytes": 10877}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/bitmap_reader.h", "path_type": "hardlink", "sha256": "a4bacc0d6855a3e41bdd5d662c0480cff688e906710c746bdfb12dab1a8677d1", "sha256_in_prefix": "a4bacc0d6855a3e41bdd5d662c0480cff688e906710c746bdfb12dab1a8677d1", "size_in_bytes": 8353}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/bitmap_visit.h", "path_type": "hardlink", "sha256": "9b29fc93ae95aef6672fa47a556e880cf4df3baf15c636c9f14a7922e4a314be", "sha256_in_prefix": "9b29fc93ae95aef6672fa47a556e880cf4df3baf15c636c9f14a7922e4a314be", "size_in_bytes": 3470}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/bitmap_writer.h", "path_type": "hardlink", "sha256": "6b88285e12e5634a9c7ef6316df6c60ff1d9f00bb5c0571b575b5517704f697b", "sha256_in_prefix": "6b88285e12e5634a9c7ef6316df6c60ff1d9f00bb5c0571b575b5517704f697b", "size_in_bytes": 9383}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/bitset_stack.h", "path_type": "hardlink", "sha256": "0f8f486594b364e33686a87a6fe7d3d2f8112127f59909e5e281b99e72c16780", "sha256_in_prefix": "0f8f486594b364e33686a87a6fe7d3d2f8112127f59909e5e281b99e72c16780", "size_in_bytes": 2776}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/bpacking.h", "path_type": "hardlink", "sha256": "aa28985e064b5997185fab26ef9fef050eaaa47b52d40c1ab0be7d6102f63ed9", "sha256_in_prefix": "aa28985e064b5997185fab26ef9fef050eaaa47b52d40c1ab0be7d6102f63ed9", "size_in_bytes": 1175}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/bpacking64_default.h", "path_type": "hardlink", "sha256": "abb91ffc15bada4e39bf5a8cb6726d2c83e4f15b492002dce675e4626212cb7c", "sha256_in_prefix": "abb91ffc15bada4e39bf5a8cb6726d2c83e4f15b492002dce675e4626212cb7c", "size_in_bytes": 196990}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/bpacking_avx2.h", "path_type": "hardlink", "sha256": "ca6409190739e16df36eb4a892d8db01c0675b06eafd2a618972cb23e1ba7c78", "sha256_in_prefix": "ca6409190739e16df36eb4a892d8db01c0675b06eafd2a618972cb23e1ba7c78", "size_in_bytes": 1009}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/bpacking_avx512.h", "path_type": "hardlink", "sha256": "67fac042988a2441fef50487517a5b0e6662020226ec23c21df3e7c252190c01", "sha256_in_prefix": "67fac042988a2441fef50487517a5b0e6662020226ec23c21df3e7c252190c01", "size_in_bytes": 1011}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/bpacking_default.h", "path_type": "hardlink", "sha256": "9c38b883925dc96c176bf27712a136d9b1bd4781bb0b377a5bbe45f6ca515395", "sha256_in_prefix": "9c38b883925dc96c176bf27712a136d9b1bd4781bb0b377a5bbe45f6ca515395", "size_in_bytes": 103760}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/bpacking_neon.h", "path_type": "hardlink", "sha256": "bc4f95e04f1da6a4a393b76af246a00f4efe9e1450286bdc60c85cfdd1389ea8", "sha256_in_prefix": "bc4f95e04f1da6a4a393b76af246a00f4efe9e1450286bdc60c85cfdd1389ea8", "size_in_bytes": 1009}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/byte_size.h", "path_type": "hardlink", "sha256": "3ddd9cff76b421e48e51ebe13c89573640e6828074ea0e1cf580acb91c3048a3", "sha256_in_prefix": "3ddd9cff76b421e48e51ebe13c89573640e6828074ea0e1cf580acb91c3048a3", "size_in_bytes": 3997}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/cancel.h", "path_type": "hardlink", "sha256": "4a0032e52d130484a70b2a69e11fce23e8cf510f7deeab6c2ce49d5e8f25e5b3", "sha256_in_prefix": "4a0032e52d130484a70b2a69e11fce23e8cf510f7deeab6c2ce49d5e8f25e5b3", "size_in_bytes": 3659}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/checked_cast.h", "path_type": "hardlink", "sha256": "491f5083c36e2d2049c36c3551f81e1af09f4fc93bc2b6cdec1cc00d03997c05", "sha256_in_prefix": "491f5083c36e2d2049c36c3551f81e1af09f4fc93bc2b6cdec1cc00d03997c05", "size_in_bytes": 2076}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/compare.h", "path_type": "hardlink", "sha256": "38bad24b2965918e12bf4d082bedfb03677af20af83b09d626cc67d5a17dc535", "sha256_in_prefix": "38bad24b2965918e12bf4d082bedfb03677af20af83b09d626cc67d5a17dc535", "size_in_bytes": 1982}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/compression.h", "path_type": "hardlink", "sha256": "7ef954468589b203bc1ebe97b3f54d6aa88e6ad9881a7f64b5552462fee922ee", "sha256_in_prefix": "7ef954468589b203bc1ebe97b3f54d6aa88e6ad9881a7f64b5552462fee922ee", "size_in_bytes": 8427}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/concurrent_map.h", "path_type": "hardlink", "sha256": "c0c8bd5831df46e27f6921607093e9b15c0627dbc82796a0699deb55497019ee", "sha256_in_prefix": "c0c8bd5831df46e27f6921607093e9b15c0627dbc82796a0699deb55497019ee", "size_in_bytes": 1775}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/config.h", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/apache-arrow_1745999995354/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "a15f2d609e8f5551119fd1b866c2324f1f4a66b1440eba035085a146da58b48a", "sha256_in_prefix": "d9a84704a56c7aef5f35fff6a98b92847c4ffde253a82dffddf08a11b7cb96c0", "size_in_bytes": 3976}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/converter.h", "path_type": "hardlink", "sha256": "3c82dfa2ce959672cae9f38531f2c852188a977a35749a3d4f81c95de47b5791", "sha256_in_prefix": "3c82dfa2ce959672cae9f38531f2c852188a977a35749a3d4f81c95de47b5791", "size_in_bytes": 14637}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/counting_semaphore.h", "path_type": "hardlink", "sha256": "8971d86a0aa2ffe6b2ef74f5b8f9afee91b7df8498df80d440b49db43ff8fed0", "sha256_in_prefix": "8971d86a0aa2ffe6b2ef74f5b8f9afee91b7df8498df80d440b49db43ff8fed0", "size_in_bytes": 2251}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/cpu_info.h", "path_type": "hardlink", "sha256": "32a2dd25a6c1664cc38e249c690ee27fd766a001af5d3d906af1a81a4868de55", "sha256_in_prefix": "32a2dd25a6c1664cc38e249c690ee27fd766a001af5d3d906af1a81a4868de55", "size_in_bytes": 3964}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/crc32.h", "path_type": "hardlink", "sha256": "e2037433e4919f168629c8b60133db316646d931b76142d78da4e969d57451d9", "sha256_in_prefix": "e2037433e4919f168629c8b60133db316646d931b76142d78da4e969d57451d9", "size_in_bytes": 1337}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/debug.h", "path_type": "hardlink", "sha256": "08f07fa033ae67fbbcf5ef7033c6c69fcf261af0a581f6bb503c43a61eaff6c6", "sha256_in_prefix": "08f07fa033ae67fbbcf5ef7033c6c69fcf261af0a581f6bb503c43a61eaff6c6", "size_in_bytes": 971}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/decimal.h", "path_type": "hardlink", "sha256": "a3363fa51b0181fb46ef7ab3d0a2843dc8454391d14dbe68c4271321d5842fb8", "sha256_in_prefix": "a3363fa51b0181fb46ef7ab3d0a2843dc8454391d14dbe68c4271321d5842fb8", "size_in_bytes": 20831}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/delimiting.h", "path_type": "hardlink", "sha256": "2587bd61c58c7854ff212ba88f1fd5815a8e60bbd9d938a2476b0d9fe59d7814", "sha256_in_prefix": "2587bd61c58c7854ff212ba88f1fd5815a8e60bbd9d938a2476b0d9fe59d7814", "size_in_bytes": 7317}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/dict_util.h", "path_type": "hardlink", "sha256": "1e2a6f015950d50eb33677aef6d60ec1552fe8d2e4941bb621f6757a8792a558", "sha256_in_prefix": "1e2a6f015950d50eb33677aef6d60ec1552fe8d2e4941bb621f6757a8792a558", "size_in_bytes": 986}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/dispatch.h", "path_type": "hardlink", "sha256": "83a47dc3c6ac0934720d3168c548a9bdd39e87a61efc5bd90463fa670836b773", "sha256_in_prefix": "83a47dc3c6ac0934720d3168c548a9bdd39e87a61efc5bd90463fa670836b773", "size_in_bytes": 3235}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/double_conversion.h", "path_type": "hardlink", "sha256": "db7414d93157e21a41667a2a303c932b16681fb994f6a918daf905d4a2fc6d6e", "sha256_in_prefix": "db7414d93157e21a41667a2a303c932b16681fb994f6a918daf905d4a2fc6d6e", "size_in_bytes": 1243}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/endian.h", "path_type": "hardlink", "sha256": "8e9e10a10f6bdaf6fea2282b95bf408505bb2f18318e3edd7ac423ce412b7bb8", "sha256_in_prefix": "8e9e10a10f6bdaf6fea2282b95bf408505bb2f18318e3edd7ac423ce412b7bb8", "size_in_bytes": 8176}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/float16.h", "path_type": "hardlink", "sha256": "57a398caf313c9b45625684d7e33fe36caebc4130f883900fbb45691f0556520", "sha256_in_prefix": "57a398caf313c9b45625684d7e33fe36caebc4130f883900fbb45691f0556520", "size_in_bytes": 7230}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/formatting.h", "path_type": "hardlink", "sha256": "efcdb028de992a51ceedc40b0bc08a085f524e2c6f2c68d7ae9fc2c11a97c95b", "sha256_in_prefix": "efcdb028de992a51ceedc40b0bc08a085f524e2c6f2c68d7ae9fc2c11a97c95b", "size_in_bytes": 22554}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/functional.h", "path_type": "hardlink", "sha256": "e258ca5d2597dc6fe5053d817cb5ee1b8e29cd9c1529e6a88e92d60a78aa2b27", "sha256_in_prefix": "e258ca5d2597dc6fe5053d817cb5ee1b8e29cd9c1529e6a88e92d60a78aa2b27", "size_in_bytes": 5612}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/future.h", "path_type": "hardlink", "sha256": "b6c4950c41f67615cacaf20a97a47d0550689693dd657a1145fdbe6116ed7718", "sha256_in_prefix": "b6c4950c41f67615cacaf20a97a47d0550689693dd657a1145fdbe6116ed7718", "size_in_bytes": 32296}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/hash_util.h", "path_type": "hardlink", "sha256": "0a388d54f5093f15ef549cbbcacefd6886fb601e819bee674c9011e031ecc5cb", "sha256_in_prefix": "0a388d54f5093f15ef549cbbcacefd6886fb601e819bee674c9011e031ecc5cb", "size_in_bytes": 1918}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/hashing.h", "path_type": "hardlink", "sha256": "4bcf234d3439936dcbecd392a844a9fbf285bc74bf7e0d06c9f885f4d030bd5f", "sha256_in_prefix": "4bcf234d3439936dcbecd392a844a9fbf285bc74bf7e0d06c9f885f4d030bd5f", "size_in_bytes": 33214}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/int_util.h", "path_type": "hardlink", "sha256": "cd3380ab9ecce2951ee3af56a675ba23984db717b7bc64479595a19e00350f33", "sha256_in_prefix": "cd3380ab9ecce2951ee3af56a675ba23984db717b7bc64479595a19e00350f33", "size_in_bytes": 4859}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/int_util_overflow.h", "path_type": "hardlink", "sha256": "02dbe41bbbf7fb5815cd6e52ac5add56462e5c5b53efafe7c6b2adcc86f3ffd5", "sha256_in_prefix": "02dbe41bbbf7fb5815cd6e52ac5add56462e5c5b53efafe7c6b2adcc86f3ffd5", "size_in_bytes": 4895}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/io_util.h", "path_type": "hardlink", "sha256": "53a5530a1d322949983dac36a06f8296525de09d3619c7ba6f4a937ea162f44e", "sha256_in_prefix": "53a5530a1d322949983dac36a06f8296525de09d3619c7ba6f4a937ea162f44e", "size_in_bytes": 13709}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/iterator.h", "path_type": "hardlink", "sha256": "d3199fa3aaeff03c0220ddc89af2a716405786a4169156eaa7ee146dd781dcad", "sha256_in_prefix": "d3199fa3aaeff03c0220ddc89af2a716405786a4169156eaa7ee146dd781dcad", "size_in_bytes": 18354}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/key_value_metadata.h", "path_type": "hardlink", "sha256": "c2353ab9019c4a6cbe605a8cb3aaf02cfec4e17fb42050633eb599b2d8acb734", "sha256_in_prefix": "c2353ab9019c4a6cbe605a8cb3aaf02cfec4e17fb42050633eb599b2d8acb734", "size_in_bytes": 3590}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/launder.h", "path_type": "hardlink", "sha256": "0b7acd051878adeb94a7c62e45d19053de563dcf2f9786c063ecf92d78038ae0", "sha256_in_prefix": "0b7acd051878adeb94a7c62e45d19053de563dcf2f9786c063ecf92d78038ae0", "size_in_bytes": 1046}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/list_util.h", "path_type": "hardlink", "sha256": "fce9adb03a9efa69d9ffbb555b1076c8776008986988c13f44fde75c7cca6dd2", "sha256_in_prefix": "fce9adb03a9efa69d9ffbb555b1076c8776008986988c13f44fde75c7cca6dd2", "size_in_bytes": 2028}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/logger.h", "path_type": "hardlink", "sha256": "a7d8b874d8277b7e8b5a91663526016204d0e241526a8db4749e342e045164a4", "sha256_in_prefix": "a7d8b874d8277b7e8b5a91663526016204d0e241526a8db4749e342e045164a4", "size_in_bytes": 6693}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/logging.h", "path_type": "hardlink", "sha256": "798d6c67540272fcb99692707ce08bdabb5180b8dcf15f320dff6eb126bdf9de", "sha256_in_prefix": "798d6c67540272fcb99692707ce08bdabb5180b8dcf15f320dff6eb126bdf9de", "size_in_bytes": 9694}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/macros.h", "path_type": "hardlink", "sha256": "76a9c588352b154caa1f234fe3112be785a06801175fc804e191bbfa2de77d94", "sha256_in_prefix": "76a9c588352b154caa1f234fe3112be785a06801175fc804e191bbfa2de77d94", "size_in_bytes": 9336}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/map.h", "path_type": "hardlink", "sha256": "29b281dd035cdda591ff4614d52eda17d7dd2345420011b1105d55112da83aa5", "sha256_in_prefix": "29b281dd035cdda591ff4614d52eda17d7dd2345420011b1105d55112da83aa5", "size_in_bytes": 2476}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/math_constants.h", "path_type": "hardlink", "sha256": "dac7d6a1573cb321f3f17dba5e00667a3cd74ad97b866bca88e87deb6da85190", "sha256_in_prefix": "dac7d6a1573cb321f3f17dba5e00667a3cd74ad97b866bca88e87deb6da85190", "size_in_bytes": 1112}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/memory.h", "path_type": "hardlink", "sha256": "aacc4582f8ffc28ccee4ec48b3a7c775c6a6eda89fa68cea735684f353fdd58a", "sha256_in_prefix": "aacc4582f8ffc28ccee4ec48b3a7c775c6a6eda89fa68cea735684f353fdd58a", "size_in_bytes": 1566}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/mutex.h", "path_type": "hardlink", "sha256": "9f86ecac72b643ccdb62c404c8d685a8dbbffefbea830a3501facb2c2c5f9295", "sha256_in_prefix": "9f86ecac72b643ccdb62c404c8d685a8dbbffefbea830a3501facb2c2c5f9295", "size_in_bytes": 2554}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/parallel.h", "path_type": "hardlink", "sha256": "ff8df04d54abb49c0e911b772897091750f2607c21a166d63ecac0b307ed3fe4", "sha256_in_prefix": "ff8df04d54abb49c0e911b772897091750f2607c21a166d63ecac0b307ed3fe4", "size_in_bytes": 3817}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/pcg_random.h", "path_type": "hardlink", "sha256": "9db5e8c1f089162cb81a355f17d23c56f07a7f1932479ccd07514a76716c6134", "sha256_in_prefix": "9db5e8c1f089162cb81a355f17d23c56f07a7f1932479ccd07514a76716c6134", "size_in_bytes": 1252}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/prefetch.h", "path_type": "hardlink", "sha256": "bda13814f76c71bb4ed1c3dbce5f05d4fcc1d4d0ced7ccad6251266421c38c7b", "sha256_in_prefix": "bda13814f76c71bb4ed1c3dbce5f05d4fcc1d4d0ced7ccad6251266421c38c7b", "size_in_bytes": 1251}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/print.h", "path_type": "hardlink", "sha256": "5f409fb96cc392af02347684507dc8dbb622e2fff37683a8eec76b4da77c5abd", "sha256_in_prefix": "5f409fb96cc392af02347684507dc8dbb622e2fff37683a8eec76b4da77c5abd", "size_in_bytes": 2444}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/queue.h", "path_type": "hardlink", "sha256": "5fdbd16505f760bfdad8bcf07becdc34d1e0b91ec5a066260fe4344c7aac0813", "sha256_in_prefix": "5fdbd16505f760bfdad8bcf07becdc34d1e0b91ec5a066260fe4344c7aac0813", "size_in_bytes": 1017}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/range.h", "path_type": "hardlink", "sha256": "ca17b9a498992222d43bcb58af8d3c63dc84b05ac577b16b04c7932f684038a6", "sha256_in_prefix": "ca17b9a498992222d43bcb58af8d3c63dc84b05ac577b16b04c7932f684038a6", "size_in_bytes": 8526}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/ree_util.h", "path_type": "hardlink", "sha256": "c1a4c13907f05861e1a006074f2ca1527336052c0eaaca1ffc7fda907bd4827a", "sha256_in_prefix": "c1a4c13907f05861e1a006074f2ca1527336052c0eaaca1ffc7fda907bd4827a", "size_in_bytes": 22395}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/regex.h", "path_type": "hardlink", "sha256": "4e3f760adb4e8761f14b410a43ff7eb3130102c42b0ce50a34fd2780824574ff", "sha256_in_prefix": "4e3f760adb4e8761f14b410a43ff7eb3130102c42b0ce50a34fd2780824574ff", "size_in_bytes": 1742}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/rows_to_batches.h", "path_type": "hardlink", "sha256": "3d93682de3027c925d78756f527cb450773902d4b485cb540a2ef3533b49a5e1", "sha256_in_prefix": "3d93682de3027c925d78756f527cb450773902d4b485cb540a2ef3533b49a5e1", "size_in_bytes": 7120}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/simd.h", "path_type": "hardlink", "sha256": "3e92a6f9a5a96582183f4367c86ad071e38ef66dffec96cde2eae8d088484fdc", "sha256_in_prefix": "3e92a6f9a5a96582183f4367c86ad071e38ef66dffec96cde2eae8d088484fdc", "size_in_bytes": 1679}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/small_vector.h", "path_type": "hardlink", "sha256": "74334d305a4d76d21bc4b3f72fe87f6efdc0f2b698bf8215bb22c4cd454c81c9", "sha256_in_prefix": "74334d305a4d76d21bc4b3f72fe87f6efdc0f2b698bf8215bb22c4cd454c81c9", "size_in_bytes": 14421}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/sort.h", "path_type": "hardlink", "sha256": "71766f04dfc471790de63d31857da8362b1b0a14f640a5cff4acc38235d6dbf3", "sha256_in_prefix": "71766f04dfc471790de63d31857da8362b1b0a14f640a5cff4acc38235d6dbf3", "size_in_bytes": 2466}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/spaced.h", "path_type": "hardlink", "sha256": "efdd051424dd640fb3eaa2ae109339ff01b6e12a934d5b728fb3ca9cb05eeff4", "sha256_in_prefix": "efdd051424dd640fb3eaa2ae109339ff01b6e12a934d5b728fb3ca9cb05eeff4", "size_in_bytes": 3567}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/span.h", "path_type": "hardlink", "sha256": "d5e76f7a1279ebbb107d99f8806be7e24715a4b00953dada5c76ea13f743a594", "sha256_in_prefix": "d5e76f7a1279ebbb107d99f8806be7e24715a4b00953dada5c76ea13f743a594", "size_in_bytes": 4298}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/stopwatch.h", "path_type": "hardlink", "sha256": "00319b104535c7e7efa7f36c21d4c71f9056d1bf630c1f2b4c08f558e8240a57", "sha256_in_prefix": "00319b104535c7e7efa7f36c21d4c71f9056d1bf630c1f2b4c08f558e8240a57", "size_in_bytes": 1401}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/string.h", "path_type": "hardlink", "sha256": "858b60e1dde419004775dd2f1ae2894e555eb9e08281fc83de2abe7de300de9f", "sha256_in_prefix": "858b60e1dde419004775dd2f1ae2894e555eb9e08281fc83de2abe7de300de9f", "size_in_bytes": 5756}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/string_builder.h", "path_type": "hardlink", "sha256": "1064f46b3445e1d075710a309b67ebbb04918cf6ee1ede61d239daf095b24f09", "sha256_in_prefix": "1064f46b3445e1d075710a309b67ebbb04918cf6ee1ede61d239daf095b24f09", "size_in_bytes": 2653}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/task_group.h", "path_type": "hardlink", "sha256": "7c8df7d0da094fcbbce0011403aa52c56ac4ed404a9f62da3380df3c5a1a96a0", "sha256_in_prefix": "7c8df7d0da094fcbbce0011403aa52c56ac4ed404a9f62da3380df3c5a1a96a0", "size_in_bytes": 4362}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/tdigest.h", "path_type": "hardlink", "sha256": "2fa9d28fe1559582edc0a27de165fdaa9b3d614e9883e7b7c703fa0b4a84ee9c", "sha256_in_prefix": "2fa9d28fe1559582edc0a27de165fdaa9b3d614e9883e7b7c703fa0b4a84ee9c", "size_in_bytes": 3058}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/test_common.h", "path_type": "hardlink", "sha256": "66788b4fc4ef0147421364f662bb650ca7700cd3c1a53e5ef55d4488f1c7f4b5", "sha256_in_prefix": "66788b4fc4ef0147421364f662bb650ca7700cd3c1a53e5ef55d4488f1c7f4b5", "size_in_bytes": 2837}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/thread_pool.h", "path_type": "hardlink", "sha256": "e33b4bc242474094264e6ab0cbc2060e6028f17e0dfa8dea8ba7fdd5a8339244", "sha256_in_prefix": "e33b4bc242474094264e6ab0cbc2060e6028f17e0dfa8dea8ba7fdd5a8339244", "size_in_bytes": 24426}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/time.h", "path_type": "hardlink", "sha256": "e178bc27369895617155a7a798227ba2b320bb872e2842ef6ed322b33b891d40", "sha256_in_prefix": "e178bc27369895617155a7a798227ba2b320bb872e2842ef6ed322b33b891d40", "size_in_bytes": 2988}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/tracing.h", "path_type": "hardlink", "sha256": "b157c2fd18f683091629548a4f497414e3b9736106b1f630964657e1df677310", "sha256_in_prefix": "b157c2fd18f683091629548a4f497414e3b9736106b1f630964657e1df677310", "size_in_bytes": 1286}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/trie.h", "path_type": "hardlink", "sha256": "581bebc983b6b0d7683dcf9407e5e64375b349e77bf6a22c4a0ed8582aefc0d6", "sha256_in_prefix": "581bebc983b6b0d7683dcf9407e5e64375b349e77bf6a22c4a0ed8582aefc0d6", "size_in_bytes": 7121}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/type_fwd.h", "path_type": "hardlink", "sha256": "25e6759ae22254ac477005f306290c0a693fed2c45cff22a9b3b93eb263517e0", "sha256_in_prefix": "25e6759ae22254ac477005f306290c0a693fed2c45cff22a9b3b93eb263517e0", "size_in_bytes": 1803}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/type_traits.h", "path_type": "hardlink", "sha256": "17419d83fddf68cd0c99904e5ceb29473530bb19e329b15a5692624c468e5c65", "sha256_in_prefix": "17419d83fddf68cd0c99904e5ceb29473530bb19e329b15a5692624c468e5c65", "size_in_bytes": 1731}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/ubsan.h", "path_type": "hardlink", "sha256": "9b5f0fc6763e074c78b707b08f236dd94532e6a1e7b62a25327582d099244e63", "sha256_in_prefix": "9b5f0fc6763e074c78b707b08f236dd94532e6a1e7b62a25327582d099244e63", "size_in_bytes": 2817}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/union_util.h", "path_type": "hardlink", "sha256": "3d2b2c062c3ebfe3c37a7feaef973a3a434ee4fc3220f8466dff4f3098fb3f63", "sha256_in_prefix": "3d2b2c062c3ebfe3c37a7feaef973a3a434ee4fc3220f8466dff4f3098fb3f63", "size_in_bytes": 1211}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/unreachable.h", "path_type": "hardlink", "sha256": "3b54c6e28cc2613dffc6f0e9268b8a5ab945003204a487a6436f32e03a88c2ee", "sha256_in_prefix": "3b54c6e28cc2613dffc6f0e9268b8a5ab945003204a487a6436f32e03a88c2ee", "size_in_bytes": 1070}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/uri.h", "path_type": "hardlink", "sha256": "0f6e3379b6b315caca1adee21a991c190f3b0ee17ed806e33ca5640e5abd36e9", "sha256_in_prefix": "0f6e3379b6b315caca1adee21a991c190f3b0ee17ed806e33ca5640e5abd36e9", "size_in_bytes": 3886}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/utf8.h", "path_type": "hardlink", "sha256": "7e5199efcea41e8df75e0ff3c34cd503d1804fc8d874f5074d58483c71a33a3f", "sha256_in_prefix": "7e5199efcea41e8df75e0ff3c34cd503d1804fc8d874f5074d58483c71a33a3f", "size_in_bytes": 2031}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/value_parsing.h", "path_type": "hardlink", "sha256": "ca96e7208c5f1437c39842e29c4892d9161291e69b60301fb8a3d6e58b267d1c", "sha256_in_prefix": "ca96e7208c5f1437c39842e29c4892d9161291e69b60301fb8a3d6e58b267d1c", "size_in_bytes": 29995}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/vector.h", "path_type": "hardlink", "sha256": "c35971646dc253482ad99ac1c9e53c417d80d09793b68a0675a64e354b1595fb", "sha256_in_prefix": "c35971646dc253482ad99ac1c9e53c417d80d09793b68a0675a64e354b1595fb", "size_in_bytes": 5697}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/visibility.h", "path_type": "hardlink", "sha256": "0c511d97c4c2af7d2bddbeef969833248880e4db0aede5bd52678be3b3e070b9", "sha256_in_prefix": "0c511d97c4c2af7d2bddbeef969833248880e4db0aede5bd52678be3b3e070b9", "size_in_bytes": 2835}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/windows_compatibility.h", "path_type": "hardlink", "sha256": "0a199ef5f591a984737c86cbc3b57fc9e88859ddc5e1d15e1ba2261c7af85eac", "sha256_in_prefix": "0a199ef5f591a984737c86cbc3b57fc9e88859ddc5e1d15e1ba2261c7af85eac", "size_in_bytes": 1255}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/util/windows_fixup.h", "path_type": "hardlink", "sha256": "863a21eb3bc1f2ef0e55442a2ed75cae6a21333a00a0bcba5c914bc5c7c584ad", "sha256_in_prefix": "863a21eb3bc1f2ef0e55442a2ed75cae6a21333a00a0bcba5c914bc5c7c584ad", "size_in_bytes": 1435}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/ProducerConsumerQueue.h", "path_type": "hardlink", "sha256": "073d64b373438175cb7d3f13314904dfc46930e4b029146dc14d5edfb635094c", "sha256_in_prefix": "073d64b373438175cb7d3f13314904dfc46930e4b029146dc14d5edfb635094c", "size_in_bytes": 6101}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/datetime.h", "path_type": "hardlink", "sha256": "b6c15bcfc2ca045cd1cd310e00a6725916dd14b7e70a765108af53ca2d4f00db", "sha256_in_prefix": "b6c15bcfc2ca045cd1cd310e00a6725916dd14b7e70a765108af53ca2d4f00db", "size_in_bytes": 1103}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/datetime/date.h", "path_type": "hardlink", "sha256": "7dafed9a4307c26c7dbd91e31f995956164509811c1e2e719ae796dc5d6d0c4e", "sha256_in_prefix": "7dafed9a4307c26c7dbd91e31f995956164509811c1e2e719ae796dc5d6d0c4e", "size_in_bytes": 237808}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/datetime/ios.h", "path_type": "hardlink", "sha256": "427bb48aecb6f9e8a7f4a915a122f5b7bd7f5bf5456647630d5b0e9d84e73f7f", "sha256_in_prefix": "427bb48aecb6f9e8a7f4a915a122f5b7bd7f5bf5456647630d5b0e9d84e73f7f", "size_in_bytes": 1641}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/datetime/tz.h", "path_type": "hardlink", "sha256": "9b9249bfb2c4ed5ba4a7c879d2bf74b027db392003d9b3152154145313597834", "sha256_in_prefix": "9b9249bfb2c4ed5ba4a7c879d2bf74b027db392003d9b3152154145313597834", "size_in_bytes": 85347}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/datetime/tz_private.h", "path_type": "hardlink", "sha256": "a4390a5d87737f3439ba1fa37148544412ea1e8d34b74527962994762339dc2b", "sha256_in_prefix": "a4390a5d87737f3439ba1fa37148544412ea1e8d34b74527962994762339dc2b", "size_in_bytes": 10706}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/datetime/visibility.h", "path_type": "hardlink", "sha256": "54218ace140e80bd73c065ca97fee52d42df4b2d0eb0fb7c14b587c00e2c3ad5", "sha256_in_prefix": "54218ace140e80bd73c065ca97fee52d42df4b2d0eb0fb7c14b587c00e2c3ad5", "size_in_bytes": 1002}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/bignum-dtoa.h", "path_type": "hardlink", "sha256": "8a61a1720d11cb032c14d3184eaa7aae55f0d8764220095af120bf9fdda00aa1", "sha256_in_prefix": "8a61a1720d11cb032c14d3184eaa7aae55f0d8764220095af120bf9fdda00aa1", "size_in_bytes": 4358}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/bignum.h", "path_type": "hardlink", "sha256": "46743608f2fc3ede9f5421a1ffc543175d5efc6cabaf03b4207d2e3274dcb04b", "sha256_in_prefix": "46743608f2fc3ede9f5421a1ffc543175d5efc6cabaf03b4207d2e3274dcb04b", "size_in_bytes": 5949}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/cached-powers.h", "path_type": "hardlink", "sha256": "8e3c1f4776ee7bb98d944e656d3ac54762a582345ec363a49a305aee840ed108", "sha256_in_prefix": "8e3c1f4776ee7bb98d944e656d3ac54762a582345ec363a49a305aee840ed108", "size_in_bytes": 3079}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/diy-fp.h", "path_type": "hardlink", "sha256": "27e460a87dbb8eca53e546ed87da5303d340647e9eee7d4e561c4ca1d822f127", "sha256_in_prefix": "27e460a87dbb8eca53e546ed87da5303d340647e9eee7d4e561c4ca1d822f127", "size_in_bytes": 5088}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/double-conversion.h", "path_type": "hardlink", "sha256": "2754e5e7ef1a158d00f529da03dcf943dd239ccc70e798a594f22e13e8dd0f94", "sha256_in_prefix": "2754e5e7ef1a158d00f529da03dcf943dd239ccc70e798a594f22e13e8dd0f94", "size_in_bytes": 1804}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/double-to-string.h", "path_type": "hardlink", "sha256": "0beb4a462d08b8bc9c5e04ba082d68885902ae83a8ffe00ed153a399f7b4b651", "sha256_in_prefix": "0beb4a462d08b8bc9c5e04ba082d68885902ae83a8ffe00ed153a399f7b4b651", "size_in_bytes": 23925}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/fast-dtoa.h", "path_type": "hardlink", "sha256": "640868db97ea78fdedd913345e0a9f85305740822270008ba5dc871cc457dc95", "sha256_in_prefix": "640868db97ea78fdedd913345e0a9f85305740822270008ba5dc871cc457dc95", "size_in_bytes": 4122}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/fixed-dtoa.h", "path_type": "hardlink", "sha256": "1cb9e9c641e32a5766f850620d16c00d89632414986d018fe06cfeb156e24895", "sha256_in_prefix": "1cb9e9c641e32a5766f850620d16c00d89632414986d018fe06cfeb156e24895", "size_in_bytes": 2828}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/ieee.h", "path_type": "hardlink", "sha256": "095280f515d28efe19ca0a831cc885f87da1521dd01d0be9d466580b73008641", "sha256_in_prefix": "095280f515d28efe19ca0a831cc885f87da1521dd01d0be9d466580b73008641", "size_in_bytes": 15281}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/string-to-double.h", "path_type": "hardlink", "sha256": "525e9bfb6474a6351a01634cdca8b8907f77dcbaab5bafd77cfcf8052884daff", "sha256_in_prefix": "525e9bfb6474a6351a01634cdca8b8907f77dcbaab5bafd77cfcf8052884daff", "size_in_bytes": 10906}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/strtod.h", "path_type": "hardlink", "sha256": "eb10919b8eef99c821609ba0e668614d56ec6779b763ab507cc7a1132559371d", "sha256_in_prefix": "eb10919b8eef99c821609ba0e668614d56ec6779b763ab507cc7a1132559371d", "size_in_bytes": 3096}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/double-conversion/utils.h", "path_type": "hardlink", "sha256": "c0545be5c1800623685120a7bca99dbff28831c06d5f53d7f3db4f15fbe06d02", "sha256_in_prefix": "c0545be5c1800623685120a7bca99dbff28831c06d5f53d7f3db4f15fbe06d02", "size_in_bytes": 15614}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_extras.hpp", "path_type": "hardlink", "sha256": "144633abc345c4f7dd2722ece2456d4c12e4683ea23bbd48373f442676b14dd7", "sha256_in_prefix": "144633abc345c4f7dd2722ece2456d4c12e4683ea23bbd48373f442676b14dd7", "size_in_bytes": 19784}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_random.hpp", "path_type": "hardlink", "sha256": "ed3695de7661730a5fe97c6567a72877519a5b9826fa2527ebbb767e230f35b0", "sha256_in_prefix": "ed3695de7661730a5fe97c6567a72877519a5b9826fa2527ebbb767e230f35b0", "size_in_bytes": 73501}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/pcg/pcg_uint128.hpp", "path_type": "hardlink", "sha256": "afc7b132d1f6d52f298e2cf2659bcff10f2501dc64285db66448aead24c5b733", "sha256_in_prefix": "afc7b132d1f6d52f298e2cf2659bcff10f2501dc64285db66448aead24c5b733", "size_in_bytes": 28411}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/portable-snippets/debug-trap.h", "path_type": "hardlink", "sha256": "f4aa6127d811b434fd0d747d899eda4b6df16b64fcb4b98bb05109320d292c34", "sha256_in_prefix": "f4aa6127d811b434fd0d747d899eda4b6df16b64fcb4b98bb05109320d292c34", "size_in_bytes": 3081}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/portable-snippets/safe-math.h", "path_type": "hardlink", "sha256": "abdc96877e1bb05bb5bd2a8b4ee2379ff7085384e563df0b9357a5c4a1ef64fd", "sha256_in_prefix": "abdc96877e1bb05bb5bd2a8b4ee2379ff7085384e563df0b9357a5c4a1ef64fd", "size_in_bytes": 48167}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/strptime.h", "path_type": "hardlink", "sha256": "ab52198b90afc94a7f3cdcdb438fd72eba00576e15128bc1133d93929c1427d7", "sha256_in_prefix": "ab52198b90afc94a7f3cdcdb438fd72eba00576e15128bc1133d93929c1427d7", "size_in_bytes": 1212}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/xxhash.h", "path_type": "hardlink", "sha256": "314c2dcb3bbbc63931f6605c4bae526837022bbb607aa4208fe29810cc5c1d67", "sha256_in_prefix": "314c2dcb3bbbc63931f6605c4bae526837022bbb607aa4208fe29810cc5c1d67", "size_in_bytes": 844}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/vendored/xxhash/xxhash.h", "path_type": "hardlink", "sha256": "be275e9db21a503c37f24683cdb4908f2370a3e35ab96e02c4ea73dc8e399c43", "sha256_in_prefix": "be275e9db21a503c37f24683cdb4908f2370a3e35ab96e02c4ea73dc8e399c43", "size_in_bytes": 253096}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/visit_array_inline.h", "path_type": "hardlink", "sha256": "5ee423b8c13c5d9789a7b5bce98b82b2ea15560986d4dba55c0034289926981d", "sha256_in_prefix": "5ee423b8c13c5d9789a7b5bce98b82b2ea15560986d4dba55c0034289926981d", "size_in_bytes": 2446}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/visit_data_inline.h", "path_type": "hardlink", "sha256": "e0c91d155b2b8e13324c336b49c42d39857f9f0cea47675d492db26276f22edd", "sha256_in_prefix": "e0c91d155b2b8e13324c336b49c42d39857f9f0cea47675d492db26276f22edd", "size_in_bytes": 12460}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/visit_scalar_inline.h", "path_type": "hardlink", "sha256": "2af358d23f2713d82cffcd392d7315dc04e0bf1bd4a96e1478aa57531477acc0", "sha256_in_prefix": "2af358d23f2713d82cffcd392d7315dc04e0bf1bd4a96e1478aa57531477acc0", "size_in_bytes": 2419}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/visit_type_inline.h", "path_type": "hardlink", "sha256": "3b4054471d93a30a9d3bad56f43244af970a3f906c56452df01a5cf15ba25de7", "sha256_in_prefix": "3b4054471d93a30a9d3bad56f43244af970a3f906c56452df01a5cf15ba25de7", "size_in_bytes": 4387}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/visitor.h", "path_type": "hardlink", "sha256": "34aa2cf7c8f9e2e63db5d5f372d23f9ff9f0151ad734ec1a9f12c3a8364e370e", "sha256_in_prefix": "34aa2cf7c8f9e2e63db5d5f372d23f9ff9f0151ad734ec1a9f12c3a8364e370e", "size_in_bytes": 8690}, {"_path": "lib/python3.11/site-packages/pyarrow/include/arrow/visitor_generate.h", "path_type": "hardlink", "sha256": "9f660a656fb9858ec80904b01140592188767a0f59a137c3e785d177d3a5343a", "sha256_in_prefix": "9f660a656fb9858ec80904b01140592188767a0f59a137c3e785d177d3a5343a", "size_in_bytes": 3324}, {"_path": "lib/python3.11/site-packages/pyarrow/includes/__init__.pxd", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/pyarrow/includes/common.pxd", "path_type": "hardlink", "sha256": "ccc307e9aaa55b5810425ec4e676df5738d7915063934201f42e1aedae189e0e", "sha256_in_prefix": "ccc307e9aaa55b5810425ec4e676df5738d7915063934201f42e1aedae189e0e", "size_in_bytes": 5020}, {"_path": "lib/python3.11/site-packages/pyarrow/includes/libarrow.pxd", "path_type": "hardlink", "sha256": "f64299a5918c23abdfdc759cba9dd426fd27c60f4158464181f63d0c61817fda", "sha256_in_prefix": "f64299a5918c23abdfdc759cba9dd426fd27c60f4158464181f63d0c61817fda", "size_in_bytes": 118916}, {"_path": "lib/python3.11/site-packages/pyarrow/includes/libarrow_acero.pxd", "path_type": "hardlink", "sha256": "73ce117587c8b85596ff7ebed5110b26ca307d0c178605316dd0bfc4a4321422", "sha256_in_prefix": "73ce117587c8b85596ff7ebed5110b26ca307d0c178605316dd0bfc4a4321422", "size_in_bytes": 5298}, {"_path": "lib/python3.11/site-packages/pyarrow/includes/libarrow_cuda.pxd", "path_type": "hardlink", "sha256": "d1f45c1db09963f805770217204967a46bd3c5e039c55c591f5fafc19875e923", "sha256_in_prefix": "d1f45c1db09963f805770217204967a46bd3c5e039c55c591f5fafc19875e923", "size_in_bytes": 4942}, {"_path": "lib/python3.11/site-packages/pyarrow/includes/libarrow_dataset.pxd", "path_type": "hardlink", "sha256": "b11c3105cd692b2c6ed642806948eb5a99ed8285f1a75efd94dd0197fa61a2c8", "sha256_in_prefix": "b11c3105cd692b2c6ed642806948eb5a99ed8285f1a75efd94dd0197fa61a2c8", "size_in_bytes": 17131}, {"_path": "lib/python3.11/site-packages/pyarrow/includes/libarrow_dataset_parquet.pxd", "path_type": "hardlink", "sha256": "e267bfbbcd898889c7351be86b32d751338ee6cc559f20a4f8175fb18419c964", "sha256_in_prefix": "e267bfbbcd898889c7351be86b32d751338ee6cc559f20a4f8175fb18419c964", "size_in_bytes": 4536}, {"_path": "lib/python3.11/site-packages/pyarrow/includes/libarrow_feather.pxd", "path_type": "hardlink", "sha256": "3132540d06df28ff08afbd341686e5ef171b8d7ed672bb145789b115795fc27d", "sha256_in_prefix": "3132540d06df28ff08afbd341686e5ef171b8d7ed672bb145789b115795fc27d", "size_in_bytes": 2140}, {"_path": "lib/python3.11/site-packages/pyarrow/includes/libarrow_flight.pxd", "path_type": "hardlink", "sha256": "1cc2ac5a1c863ed76545df15a63fb4f4f676c963d1c03f378c6a8ff819e7996f", "sha256_in_prefix": "1cc2ac5a1c863ed76545df15a63fb4f4f676c963d1c03f378c6a8ff819e7996f", "size_in_bytes": 24665}, {"_path": "lib/python3.11/site-packages/pyarrow/includes/libarrow_fs.pxd", "path_type": "hardlink", "sha256": "bf847b1239612c234b57cf5d631b47e4ae433b69183ddb988767f13e2f37ed61", "sha256_in_prefix": "bf847b1239612c234b57cf5d631b47e4ae433b69183ddb988767f13e2f37ed61", "size_in_bytes": 15032}, {"_path": "lib/python3.11/site-packages/pyarrow/includes/libarrow_python.pxd", "path_type": "hardlink", "sha256": "590eaa3a7c4a09f26b066015b37ef06cbeb6f1269562e16bbbe8f2e1dda010ad", "sha256_in_prefix": "590eaa3a7c4a09f26b066015b37ef06cbeb6f1269562e16bbbe8f2e1dda010ad", "size_in_bytes": 11259}, {"_path": "lib/python3.11/site-packages/pyarrow/includes/libarrow_substrait.pxd", "path_type": "hardlink", "sha256": "3ed830545aac090073087ccaf95aa140e753c94f49e12ce243f967c51c0285fb", "sha256_in_prefix": "3ed830545aac090073087ccaf95aa140e753c94f49e12ce243f967c51c0285fb", "size_in_bytes": 4061}, {"_path": "lib/python3.11/site-packages/pyarrow/includes/libgandiva.pxd", "path_type": "hardlink", "sha256": "14b05df7d21e53aec36fd4a71d2ee87ba160059d5a207b91734a4e883bfb8507", "sha256_in_prefix": "14b05df7d21e53aec36fd4a71d2ee87ba160059d5a207b91734a4e883bfb8507", "size_in_bytes": 11538}, {"_path": "lib/python3.11/site-packages/pyarrow/includes/libparquet_encryption.pxd", "path_type": "hardlink", "sha256": "7e2dd0acba47375fc8698457bd5309748829ec5ffa69a2eed68c0fd08dc10f98", "sha256_in_prefix": "7e2dd0acba47375fc8698457bd5309748829ec5ffa69a2eed68c0fd08dc10f98", "size_in_bytes": 5898}, {"_path": "lib/python3.11/site-packages/pyarrow/interchange/__init__.py", "path_type": "hardlink", "sha256": "0c7d1bc1b2a97630f55825b55629e75c4b952d8d3df2e1ca922aefec315cf493", "sha256_in_prefix": "0c7d1bc1b2a97630f55825b55629e75c4b952d8d3df2e1ca922aefec315cf493", "size_in_bytes": 845}, {"_path": "lib/python3.11/site-packages/pyarrow/interchange/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "e2461db88726ee9573827e4727f3670b095f5968df7ec0876a90902a89dc8c58", "sha256_in_prefix": "e2461db88726ee9573827e4727f3670b095f5968df7ec0876a90902a89dc8c58", "size_in_bytes": 220}, {"_path": "lib/python3.11/site-packages/pyarrow/interchange/__pycache__/buffer.cpython-311.pyc", "path_type": "hardlink", "sha256": "b710ce160147eae3a84e2c0d5b47d5773c05509372acbcb7b1a55614cbd1729f", "sha256_in_prefix": "b710ce160147eae3a84e2c0d5b47d5773c05509372acbcb7b1a55614cbd1729f", "size_in_bytes": 4056}, {"_path": "lib/python3.11/site-packages/pyarrow/interchange/__pycache__/column.cpython-311.pyc", "path_type": "hardlink", "sha256": "7f4e4ab18df950659e8892bf47074f7a34806aac9d2a1fa997e7bb6b42623ed2", "sha256_in_prefix": "7f4e4ab18df950659e8892bf47074f7a34806aac9d2a1fa997e7bb6b42623ed2", "size_in_bytes": 22327}, {"_path": "lib/python3.11/site-packages/pyarrow/interchange/__pycache__/dataframe.cpython-311.pyc", "path_type": "hardlink", "sha256": "1a4cd32f8fc9eba19a914457fe1b33d889601ba07e831b7d69ea19a73d611652", "sha256_in_prefix": "1a4cd32f8fc9eba19a914457fe1b33d889601ba07e831b7d69ea19a73d611652", "size_in_bytes": 10265}, {"_path": "lib/python3.11/site-packages/pyarrow/interchange/__pycache__/from_dataframe.cpython-311.pyc", "path_type": "hardlink", "sha256": "e2cbe5bd5f055c62b24ef950dbc0f0871edd1c92398d89febc2b0b61d6feded6", "sha256_in_prefix": "e2cbe5bd5f055c62b24ef950dbc0f0871edd1c92398d89febc2b0b61d6feded6", "size_in_bytes": 19809}, {"_path": "lib/python3.11/site-packages/pyarrow/interchange/buffer.py", "path_type": "hardlink", "sha256": "345fc6535b90e8836a1ea0b0cd8e97410a91c4a0e1eb39c47831e246a684210d", "sha256_in_prefix": "345fc6535b90e8836a1ea0b0cd8e97410a91c4a0e1eb39c47831e246a684210d", "size_in_bytes": 3359}, {"_path": "lib/python3.11/site-packages/pyarrow/interchange/column.py", "path_type": "hardlink", "sha256": "69f53bf789f71fbc9fe200d00ee14b6edc839605672e4f6267ab2e81bd21f3fe", "sha256_in_prefix": "69f53bf789f71fbc9fe200d00ee14b6edc839605672e4f6267ab2e81bd21f3fe", "size_in_bytes": 19370}, {"_path": "lib/python3.11/site-packages/pyarrow/interchange/dataframe.py", "path_type": "hardlink", "sha256": "b6648c981bc101cf99494cc4f2d04d6ef40b1eebb12ee0643242ba298c2d4bc3", "sha256_in_prefix": "b6648c981bc101cf99494cc4f2d04d6ef40b1eebb12ee0643242ba298c2d4bc3", "size_in_bytes": 8405}, {"_path": "lib/python3.11/site-packages/pyarrow/interchange/from_dataframe.py", "path_type": "hardlink", "sha256": "25f90fe30b98ffdc7be87e910ed9ac3b3b3a07aa9efb5592ef3c6929e0f8f35b", "sha256_in_prefix": "25f90fe30b98ffdc7be87e910ed9ac3b3b3a07aa9efb5592ef3c6929e0f8f35b", "size_in_bytes": 19709}, {"_path": "lib/python3.11/site-packages/pyarrow/io.pxi", "path_type": "hardlink", "sha256": "8c580cebc344a8bee8eb65bb783cc853b3d56c54593f75d6b8e692f541030b58", "sha256_in_prefix": "8c580cebc344a8bee8eb65bb783cc853b3d56c54593f75d6b8e692f541030b58", "size_in_bytes": 86496}, {"_path": "lib/python3.11/site-packages/pyarrow/ipc.pxi", "path_type": "hardlink", "sha256": "45e6a46ffac76c18a93abf503c40b40f68c1bd0f3b391a556f991ab0375e63fe", "sha256_in_prefix": "45e6a46ffac76c18a93abf503c40b40f68c1bd0f3b391a556f991ab0375e63fe", "size_in_bytes": 41081}, {"_path": "lib/python3.11/site-packages/pyarrow/ipc.py", "path_type": "hardlink", "sha256": "1dbdea08f291affc2d879bb85ab9191c9c8066620ae52a1549ecdf04e23ded6c", "sha256_in_prefix": "1dbdea08f291affc2d879bb85ab9191c9c8066620ae52a1549ecdf04e23ded6c", "size_in_bytes": 10107}, {"_path": "lib/python3.11/site-packages/pyarrow/json.py", "path_type": "hardlink", "sha256": "24c1170798cd669f8339898606bcecd91b703822dd28245adc4e6451f1f409e8", "sha256_in_prefix": "24c1170798cd669f8339898606bcecd91b703822dd28245adc4e6451f1f409e8", "size_in_bytes": 869}, {"_path": "lib/python3.11/site-packages/pyarrow/jvm.py", "path_type": "hardlink", "sha256": "b7302c22b31208878d02d482f256568d04b4aeaee48da4033e578f0e6be9a83c", "sha256_in_prefix": "b7302c22b31208878d02d482f256568d04b4aeaee48da4033e578f0e6be9a83c", "size_in_bytes": 9593}, {"_path": "lib/python3.11/site-packages/pyarrow/lib.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "03b8053e9073be69e8e3292518f7c2ef1802a02f5895451d2b9c4ff70ad9f920", "sha256_in_prefix": "03b8053e9073be69e8e3292518f7c2ef1802a02f5895451d2b9c4ff70ad9f920", "size_in_bytes": 4474368}, {"_path": "lib/python3.11/site-packages/pyarrow/lib.h", "path_type": "hardlink", "sha256": "69600e4a549271dbaf7378063ba69201670d065d901025847500196fa98897f5", "sha256_in_prefix": "69600e4a549271dbaf7378063ba69201670d065d901025847500196fa98897f5", "size_in_bytes": 4631}, {"_path": "lib/python3.11/site-packages/pyarrow/lib.pxd", "path_type": "hardlink", "sha256": "a860adbe045805b9cd4c4258e2add6e13f55112acde1ac3be07f63b3235ed662", "sha256_in_prefix": "a860adbe045805b9cd4c4258e2add6e13f55112acde1ac3be07f63b3235ed662", "size_in_bytes": 17597}, {"_path": "lib/python3.11/site-packages/pyarrow/lib.pyx", "path_type": "hardlink", "sha256": "b0e6cbb7dcc42d2790664c8bdfbdcbf721e8daaba1972fee0213485f3692f5f0", "sha256_in_prefix": "b0e6cbb7dcc42d2790664c8bdfbdcbf721e8daaba1972fee0213485f3692f5f0", "size_in_bytes": 6080}, {"_path": "lib/python3.11/site-packages/pyarrow/lib_api.h", "path_type": "hardlink", "sha256": "e2888a5a6761ca39f93b10d205a9abba41e95b041b522a0347b5fcd9d860757b", "sha256_in_prefix": "e2888a5a6761ca39f93b10d205a9abba41e95b041b522a0347b5fcd9d860757b", "size_in_bytes": 19487}, {"_path": "lib/python3.11/site-packages/pyarrow/libarrow_python.2000.0.0.dylib", "path_type": "hardlink", "sha256": "0a9604a7d02bfddc18f7362a5b91c268465a19051e219ee295b289cf2dceca0b", "sha256_in_prefix": "0a9604a7d02bfddc18f7362a5b91c268465a19051e219ee295b289cf2dceca0b", "size_in_bytes": 2013432}, {"_path": "lib/python3.11/site-packages/pyarrow/libarrow_python.2000.dylib", "path_type": "hardlink", "sha256": "0a9604a7d02bfddc18f7362a5b91c268465a19051e219ee295b289cf2dceca0b", "sha256_in_prefix": "0a9604a7d02bfddc18f7362a5b91c268465a19051e219ee295b289cf2dceca0b", "size_in_bytes": 2013432}, {"_path": "lib/python3.11/site-packages/pyarrow/libarrow_python.dylib", "path_type": "hardlink", "sha256": "0a9604a7d02bfddc18f7362a5b91c268465a19051e219ee295b289cf2dceca0b", "sha256_in_prefix": "0a9604a7d02bfddc18f7362a5b91c268465a19051e219ee295b289cf2dceca0b", "size_in_bytes": 2013432}, {"_path": "lib/python3.11/site-packages/pyarrow/libarrow_python_flight.2000.0.0.dylib", "path_type": "hardlink", "sha256": "9956427b3c45b03f00d5584ed9c3b2d9e34c70fa6e9be66fd23935952ea25468", "sha256_in_prefix": "9956427b3c45b03f00d5584ed9c3b2d9e34c70fa6e9be66fd23935952ea25468", "size_in_bytes": 75896}, {"_path": "lib/python3.11/site-packages/pyarrow/libarrow_python_flight.2000.dylib", "path_type": "hardlink", "sha256": "9956427b3c45b03f00d5584ed9c3b2d9e34c70fa6e9be66fd23935952ea25468", "sha256_in_prefix": "9956427b3c45b03f00d5584ed9c3b2d9e34c70fa6e9be66fd23935952ea25468", "size_in_bytes": 75896}, {"_path": "lib/python3.11/site-packages/pyarrow/libarrow_python_flight.dylib", "path_type": "hardlink", "sha256": "9956427b3c45b03f00d5584ed9c3b2d9e34c70fa6e9be66fd23935952ea25468", "sha256_in_prefix": "9956427b3c45b03f00d5584ed9c3b2d9e34c70fa6e9be66fd23935952ea25468", "size_in_bytes": 75896}, {"_path": "lib/python3.11/site-packages/pyarrow/libarrow_python_parquet_encryption.2000.0.0.dylib", "path_type": "hardlink", "sha256": "9f787f9dae971f1481168d0c63fc510aad7194b99e3f77e77774bb4b04322750", "sha256_in_prefix": "9f787f9dae971f1481168d0c63fc510aad7194b99e3f77e77774bb4b04322750", "size_in_bytes": 37584}, {"_path": "lib/python3.11/site-packages/pyarrow/libarrow_python_parquet_encryption.2000.dylib", "path_type": "hardlink", "sha256": "9f787f9dae971f1481168d0c63fc510aad7194b99e3f77e77774bb4b04322750", "sha256_in_prefix": "9f787f9dae971f1481168d0c63fc510aad7194b99e3f77e77774bb4b04322750", "size_in_bytes": 37584}, {"_path": "lib/python3.11/site-packages/pyarrow/libarrow_python_parquet_encryption.dylib", "path_type": "hardlink", "sha256": "9f787f9dae971f1481168d0c63fc510aad7194b99e3f77e77774bb4b04322750", "sha256_in_prefix": "9f787f9dae971f1481168d0c63fc510aad7194b99e3f77e77774bb4b04322750", "size_in_bytes": 37584}, {"_path": "lib/python3.11/site-packages/pyarrow/memory.pxi", "path_type": "hardlink", "sha256": "350ef3950a35127d51da8e10a462c81469fcb391cb771d8cd1a21bb92a1423f1", "sha256_in_prefix": "350ef3950a35127d51da8e10a462c81469fcb391cb771d8cd1a21bb92a1423f1", "size_in_bytes": 8905}, {"_path": "lib/python3.11/site-packages/pyarrow/orc.py", "path_type": "hardlink", "sha256": "2238de1801199742a11efc32dd8b1219f4d6971ec895be783f4b4528fbf071f9", "sha256_in_prefix": "2238de1801199742a11efc32dd8b1219f4d6971ec895be783f4b4528fbf071f9", "size_in_bytes": 12618}, {"_path": "lib/python3.11/site-packages/pyarrow/pandas-shim.pxi", "path_type": "hardlink", "sha256": "2ed573b5fe7a161cbb5735b006e10141b594a617b395bcbb0284a73d4f50497f", "sha256_in_prefix": "2ed573b5fe7a161cbb5735b006e10141b594a617b395bcbb0284a73d4f50497f", "size_in_bytes": 8796}, {"_path": "lib/python3.11/site-packages/pyarrow/pandas_compat.py", "path_type": "hardlink", "sha256": "b30bc4d0d1bfac44950de06f929cb0ed192cf2fbd9862a4cce9ef7099fcfff26", "sha256_in_prefix": "b30bc4d0d1bfac44950de06f929cb0ed192cf2fbd9862a4cce9ef7099fcfff26", "size_in_bytes": 45728}, {"_path": "lib/python3.11/site-packages/pyarrow/parquet/__init__.py", "path_type": "hardlink", "sha256": "e16eb809bbf0bceeb4b46e7c9df36dc82c0c5427ee3ee9adbbcda9fa48863da1", "sha256_in_prefix": "e16eb809bbf0bceeb4b46e7c9df36dc82c0c5427ee3ee9adbbcda9fa48863da1", "size_in_bytes": 822}, {"_path": "lib/python3.11/site-packages/pyarrow/parquet/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "695a34ce69838f8adad400d1869d38b7dc390510736d5aa3ee645c8d77a3aeef", "sha256_in_prefix": "695a34ce69838f8adad400d1869d38b7dc390510736d5aa3ee645c8d77a3aeef", "size_in_bytes": 196}, {"_path": "lib/python3.11/site-packages/pyarrow/parquet/__pycache__/core.cpython-311.pyc", "path_type": "hardlink", "sha256": "e0950713e150ab70ec77531ad8c832f10ed061824eb27a780a598a3dcdba1fe1", "sha256_in_prefix": "e0950713e150ab70ec77531ad8c832f10ed061824eb27a780a598a3dcdba1fe1", "size_in_bytes": 90413}, {"_path": "lib/python3.11/site-packages/pyarrow/parquet/__pycache__/encryption.cpython-311.pyc", "path_type": "hardlink", "sha256": "cffe73e3ded02e428d7a85efb59fc86dabea9edc79380e519ac14d10afb87192", "sha256_in_prefix": "cffe73e3ded02e428d7a85efb59fc86dabea9edc79380e519ac14d10afb87192", "size_in_bytes": 420}, {"_path": "lib/python3.11/site-packages/pyarrow/parquet/core.py", "path_type": "hardlink", "sha256": "2b841eb6a63acc7bf0d48c9e7c4e24dab0b9f85c7f46e451a14230dd2d433968", "sha256_in_prefix": "2b841eb6a63acc7bf0d48c9e7c4e24dab0b9f85c7f46e451a14230dd2d433968", "size_in_bytes": 89257}, {"_path": "lib/python3.11/site-packages/pyarrow/parquet/encryption.py", "path_type": "hardlink", "sha256": "f975bb41c6e5fa3421a59b11eb5d2e43cfb3f59544fcd2f4e3925d9e9d5367d3", "sha256_in_prefix": "f975bb41c6e5fa3421a59b11eb5d2e43cfb3f59544fcd2f4e3925d9e9d5367d3", "size_in_bytes": 1153}, {"_path": "lib/python3.11/site-packages/pyarrow/public-api.pxi", "path_type": "hardlink", "sha256": "1bab64d20396301a106901a8e25ca9d0e2d292a119a8c841d33dbb328411deb5", "sha256_in_prefix": "1bab64d20396301a106901a8e25ca9d0e2d292a119a8c841d33dbb328411deb5", "size_in_bytes": 14064}, {"_path": "lib/python3.11/site-packages/pyarrow/scalar.pxi", "path_type": "hardlink", "sha256": "bce962a83c6a68d6448539914490ced8f3df3739792189e6345edc38854f6014", "sha256_in_prefix": "bce962a83c6a68d6448539914490ced8f3df3739792189e6345edc38854f6014", "size_in_bytes": 50564}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/CMakeLists.txt", "path_type": "hardlink", "sha256": "0f8629ae8affe74f1a01dfe3b9892b4bd42eda669e8ab2b8417cf01846448f43", "sha256_in_prefix": "0f8629ae8affe74f1a01dfe3b9892b4bd42eda669e8ab2b8417cf01846448f43", "size_in_bytes": 855}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/api.h", "path_type": "hardlink", "sha256": "5ab5d6ca18b9e9efa5009d900d9224af14825a88986cc8433fede7f0439dd242", "sha256_in_prefix": "5ab5d6ca18b9e9efa5009d900d9224af14825a88986cc8433fede7f0439dd242", "size_in_bytes": 1148}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/arrow_to_pandas.cc", "path_type": "hardlink", "sha256": "a76e16bd8f82a1c0cfabb7dfcff299fbdd8179a89f0fa5fcc19bf4e271969dbd", "sha256_in_prefix": "a76e16bd8f82a1c0cfabb7dfcff299fbdd8179a89f0fa5fcc19bf4e271969dbd", "size_in_bytes": 95468}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/arrow_to_pandas.h", "path_type": "hardlink", "sha256": "8d404450c297c3bd2825d32582449fe878ad68dc1e41c73b87123be7f0bd5922", "sha256_in_prefix": "8d404450c297c3bd2825d32582449fe878ad68dc1e41c73b87123be7f0bd5922", "size_in_bytes": 5561}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/arrow_to_python_internal.h", "path_type": "hardlink", "sha256": "9d05cf6532f7c5ae129be6be1affbc6e916cfaa00e64792a5a603f9be7522d5c", "sha256_in_prefix": "9d05cf6532f7c5ae129be6be1affbc6e916cfaa00e64792a5a603f9be7522d5c", "size_in_bytes": 1740}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/async.h", "path_type": "hardlink", "sha256": "0b47fc6189a0c011a00dabb8c4416c763ba489907862fa729444c7119af21cea", "sha256_in_prefix": "0b47fc6189a0c011a00dabb8c4416c763ba489907862fa729444c7119af21cea", "size_in_bytes": 2352}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/benchmark.cc", "path_type": "hardlink", "sha256": "cfaa98471e2a32e3573da0bc7ee3c995077dd9aa2c30df39bb5683e74475f945", "sha256_in_prefix": "cfaa98471e2a32e3573da0bc7ee3c995077dd9aa2c30df39bb5683e74475f945", "size_in_bytes": 1293}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/benchmark.h", "path_type": "hardlink", "sha256": "7fe933c8c3a53ca0ec7b66dc2d687232b0c4319ac6fc91c03e90c98065b46d75", "sha256_in_prefix": "7fe933c8c3a53ca0ec7b66dc2d687232b0c4319ac6fc91c03e90c98065b46d75", "size_in_bytes": 1192}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/common.cc", "path_type": "hardlink", "sha256": "ffda33211a3f5930d6a2f04aa8e5725f6f1dd08b6d1efc16f4c1be3e44f398a7", "sha256_in_prefix": "ffda33211a3f5930d6a2f04aa8e5725f6f1dd08b6d1efc16f4c1be3e44f398a7", "size_in_bytes": 7591}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/common.h", "path_type": "hardlink", "sha256": "ca39637c92b57fbb2567b0c14382d3a3fa686fbd338a8b3024d59acf2d29fae3", "sha256_in_prefix": "ca39637c92b57fbb2567b0c14382d3a3fa686fbd338a8b3024d59acf2d29fae3", "size_in_bytes": 14412}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/csv.cc", "path_type": "hardlink", "sha256": "aa5e4063be80aa2164b16b2b9b34a5e79d64e6cf6f4bc61c9b2a4cd80f6b870f", "sha256_in_prefix": "aa5e4063be80aa2164b16b2b9b34a5e79d64e6cf6f4bc61c9b2a4cd80f6b870f", "size_in_bytes": 1803}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/csv.h", "path_type": "hardlink", "sha256": "43153707e1effd1b2811c3064bdfb5e37e2e828b8bdb2802eb82eae8582f88d3", "sha256_in_prefix": "43153707e1effd1b2811c3064bdfb5e37e2e828b8bdb2802eb82eae8582f88d3", "size_in_bytes": 1397}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/datetime.cc", "path_type": "hardlink", "sha256": "fd529129ec85a91ed7cdacb6c1acdcbde6fb9a03aff0adfb79b32898d63ffe54", "sha256_in_prefix": "fd529129ec85a91ed7cdacb6c1acdcbde6fb9a03aff0adfb79b32898d63ffe54", "size_in_bytes": 23001}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/datetime.h", "path_type": "hardlink", "sha256": "067cbf4c71a2dadc94787c5c3aec34d4eee1344f01fe06af7b900101942dc134", "sha256_in_prefix": "067cbf4c71a2dadc94787c5c3aec34d4eee1344f01fe06af7b900101942dc134", "size_in_bytes": 7931}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/decimal.cc", "path_type": "hardlink", "sha256": "45acfb90c6bd379cb68264883345c3908525e934ef5af299aae92c6b0bf42cdd", "sha256_in_prefix": "45acfb90c6bd379cb68264883345c3908525e934ef5af299aae92c6b0bf42cdd", "size_in_bytes": 9592}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/decimal.h", "path_type": "hardlink", "sha256": "2bd49c9b349aed70aebb0a572a7ab418f6d10d3b91539c977d6afdcc6b4e7217", "sha256_in_prefix": "2bd49c9b349aed70aebb0a572a7ab418f6d10d3b91539c977d6afdcc6b4e7217", "size_in_bytes": 6362}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/extension_type.cc", "path_type": "hardlink", "sha256": "794e4fee9b9f5a370472655e3b2bb58ed119d3c01677db644d231fc7ca61d2b4", "sha256_in_prefix": "794e4fee9b9f5a370472655e3b2bb58ed119d3c01677db644d231fc7ca61d2b4", "size_in_bytes": 6860}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/extension_type.h", "path_type": "hardlink", "sha256": "d20cdbe36cbf99bc387ec62cdeef1cc0f14b051946fa490740b81bbc6b6b6345", "sha256_in_prefix": "d20cdbe36cbf99bc387ec62cdeef1cc0f14b051946fa490740b81bbc6b6b6345", "size_in_bytes": 3181}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/filesystem.cc", "path_type": "hardlink", "sha256": "d2dc1abc8f754c4db43adab9924554c27379b229dd53f9815a8540bf3d593202", "sha256_in_prefix": "d2dc1abc8f754c4db43adab9924554c27379b229dd53f9815a8540bf3d593202", "size_in_bytes": 6152}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/filesystem.h", "path_type": "hardlink", "sha256": "146d0070b7a4a9a0dff4840fa8a8b101f21c63f64b80828fe4dbef5ddb415543", "sha256_in_prefix": "146d0070b7a4a9a0dff4840fa8a8b101f21c63f64b80828fe4dbef5ddb415543", "size_in_bytes": 5126}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/flight.cc", "path_type": "hardlink", "sha256": "c0b1730919001a72b21d987bffa639f959e70b62cac2c4749a8f75b71588228d", "sha256_in_prefix": "c0b1730919001a72b21d987bffa639f959e70b62cac2c4749a8f75b71588228d", "size_in_bytes": 14193}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/flight.h", "path_type": "hardlink", "sha256": "b78643f2059ab9b6cefe6043bf45d9e7c4c2a355509748731e7d7fef0e14eb82", "sha256_in_prefix": "b78643f2059ab9b6cefe6043bf45d9e7c4c2a355509748731e7d7fef0e14eb82", "size_in_bytes": 14450}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/gdb.cc", "path_type": "hardlink", "sha256": "67458b0581d6073738b84c4d1bb9d625e4675010154aaa3f0c5a4a62bcb46801", "sha256_in_prefix": "67458b0581d6073738b84c4d1bb9d625e4675010154aaa3f0c5a4a62bcb46801", "size_in_bytes": 22667}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/gdb.h", "path_type": "hardlink", "sha256": "1feaaf33e9d4f1aff7679b64f0fbe9a53c10b4131266140a408560440b117c58", "sha256_in_prefix": "1feaaf33e9d4f1aff7679b64f0fbe9a53c10b4131266140a408560440b117c58", "size_in_bytes": 972}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/helpers.cc", "path_type": "hardlink", "sha256": "cebad4239e911ab67c541cd1d9d24526846aecbe9c8655fbdbcf472bbb63a0e0", "sha256_in_prefix": "cebad4239e911ab67c541cd1d9d24526846aecbe9c8655fbdbcf472bbb63a0e0", "size_in_bytes": 16627}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/helpers.h", "path_type": "hardlink", "sha256": "8d534511bbc95e609c789b62dc9dfe327664365268ca740dab7df8b6ddbd6dbb", "sha256_in_prefix": "8d534511bbc95e609c789b62dc9dfe327664365268ca740dab7df8b6ddbd6dbb", "size_in_bytes": 5489}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/inference.cc", "path_type": "hardlink", "sha256": "1a6fa53970f3a9c6de7fa81d80241ae5e5cfba1f2ebd8ab3f62523292dbfc8ee", "sha256_in_prefix": "1a6fa53970f3a9c6de7fa81d80241ae5e5cfba1f2ebd8ab3f62523292dbfc8ee", "size_in_bytes": 24350}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/inference.h", "path_type": "hardlink", "sha256": "15416f078672ed5fadb9e5dd99b0dca9378b2b8c63e466447915b9ca188996c5", "sha256_in_prefix": "15416f078672ed5fadb9e5dd99b0dca9378b2b8c63e466447915b9ca188996c5", "size_in_bytes": 2038}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/io.cc", "path_type": "hardlink", "sha256": "6404500afe164261c342b035765359b7a989b8f8722bcc0db869bbce82fa57bf", "sha256_in_prefix": "6404500afe164261c342b035765359b7a989b8f8722bcc0db869bbce82fa57bf", "size_in_bytes": 11936}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/io.h", "path_type": "hardlink", "sha256": "e231a7a1da525259d5a80561f5f58877b1f85a57652cf917caba0006975a5bac", "sha256_in_prefix": "e231a7a1da525259d5a80561f5f58877b1f85a57652cf917caba0006975a5bac", "size_in_bytes": 3858}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/ipc.cc", "path_type": "hardlink", "sha256": "dc3f6231b3851e584d5d7e38df6c2c7db7e35af0ebc995a0740d00935f55ffc4", "sha256_in_prefix": "dc3f6231b3851e584d5d7e38df6c2c7db7e35af0ebc995a0740d00935f55ffc4", "size_in_bytes": 4472}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/ipc.h", "path_type": "hardlink", "sha256": "4996f0ea3082a8b88b34263793adf61a6c07783febff1ac34b4761a95e3d5616", "sha256_in_prefix": "4996f0ea3082a8b88b34263793adf61a6c07783febff1ac34b4761a95e3d5616", "size_in_bytes": 2259}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/iterators.h", "path_type": "hardlink", "sha256": "5207e6dc9bdeb401f497ea008e3a597e1ad406a46299531ac38fb1bacbea2d28", "sha256_in_prefix": "5207e6dc9bdeb401f497ea008e3a597e1ad406a46299531ac38fb1bacbea2d28", "size_in_bytes": 7327}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/numpy_convert.cc", "path_type": "hardlink", "sha256": "d7ae81216ef355344c2a88312d4ba15787b98ce7af9a89ef46d5c3c9d36e8e07", "sha256_in_prefix": "d7ae81216ef355344c2a88312d4ba15787b98ce7af9a89ef46d5c3c9d36e8e07", "size_in_bytes": 21194}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/numpy_convert.h", "path_type": "hardlink", "sha256": "cb5dde1f07ded6524acda7684ebdbe1b25fac4fb04e99ec5377d6cecf37ed919", "sha256_in_prefix": "cb5dde1f07ded6524acda7684ebdbe1b25fac4fb04e99ec5377d6cecf37ed919", "size_in_bytes": 4870}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/numpy_init.cc", "path_type": "hardlink", "sha256": "70928e1fde3a4fb542701fa0548a0681f6d64eb6e3dc53e42384e09ec2d37fbb", "sha256_in_prefix": "70928e1fde3a4fb542701fa0548a0681f6d64eb6e3dc53e42384e09ec2d37fbb", "size_in_bytes": 1178}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/numpy_init.h", "path_type": "hardlink", "sha256": "1678951cfed6d980657a7a0c62142b383be8aaabc331296cd8900d1ad34f42db", "sha256_in_prefix": "1678951cfed6d980657a7a0c62142b383be8aaabc331296cd8900d1ad34f42db", "size_in_bytes": 999}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/numpy_internal.h", "path_type": "hardlink", "sha256": "17da7e8734ca088851aa0b596eca323e8c7b451f3961c130e85624145f0aa9f3", "sha256_in_prefix": "17da7e8734ca088851aa0b596eca323e8c7b451f3961c130e85624145f0aa9f3", "size_in_bytes": 5314}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/numpy_interop.h", "path_type": "hardlink", "sha256": "ac8e9e93c253398b63a3b804003483052e90b803876b60346103d9d867b2a45c", "sha256_in_prefix": "ac8e9e93c253398b63a3b804003483052e90b803876b60346103d9d867b2a45c", "size_in_bytes": 3418}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/numpy_to_arrow.cc", "path_type": "hardlink", "sha256": "97ef5ec7b490f147b5364d00dae6021b4626995a1bab8b74e0dd1da525896577", "sha256_in_prefix": "97ef5ec7b490f147b5364d00dae6021b4626995a1bab8b74e0dd1da525896577", "size_in_bytes": 31937}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/numpy_to_arrow.h", "path_type": "hardlink", "sha256": "cfd29aa6cba8392a5620b3edf5b79aec647804be80436f13e8350ed264a48779", "sha256_in_prefix": "cfd29aa6cba8392a5620b3edf5b79aec647804be80436f13e8350ed264a48779", "size_in_bytes": 2760}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/parquet_encryption.cc", "path_type": "hardlink", "sha256": "44dba9c1ac926951ca5ff88260e3cad32256913529a9bae96c25b676e589de45", "sha256_in_prefix": "44dba9c1ac926951ca5ff88260e3cad32256913529a9bae96c25b676e589de45", "size_in_bytes": 3567}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/parquet_encryption.h", "path_type": "hardlink", "sha256": "31cf2d67c8087e41f401c90d8883adea17ac3ff31529e2a1732b53db864e2dd4", "sha256_in_prefix": "31cf2d67c8087e41f401c90d8883adea17ac3ff31529e2a1732b53db864e2dd4", "size_in_bytes": 4861}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/pch.h", "path_type": "hardlink", "sha256": "be46e04ad423abcdb461e1e55c13ddcd0f96f4bcb326b4c67cc0699cc32a6a19", "sha256_in_prefix": "be46e04ad423abcdb461e1e55c13ddcd0f96f4bcb326b4c67cc0699cc32a6a19", "size_in_bytes": 1129}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/platform.h", "path_type": "hardlink", "sha256": "5d84b922a88c51e8f137608eceeeb466cf1bff001a193070e0cfb3295aaab395", "sha256_in_prefix": "5d84b922a88c51e8f137608eceeeb466cf1bff001a193070e0cfb3295aaab395", "size_in_bytes": 1422}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/pyarrow.cc", "path_type": "hardlink", "sha256": "3ee97896617b9f943d712ce0052bcf02b59f658fea0f202ad5afedc8c2101910", "sha256_in_prefix": "3ee97896617b9f943d712ce0052bcf02b59f658fea0f202ad5afedc8c2101910", "size_in_bytes": 3677}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/pyarrow.h", "path_type": "hardlink", "sha256": "4cadc1b43f67dd028e43d757dcb5db41cd21bbd6a559cb9f5743bdde2416ec1d", "sha256_in_prefix": "4cadc1b43f67dd028e43d757dcb5db41cd21bbd6a559cb9f5743bdde2416ec1d", "size_in_bytes": 2761}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/pyarrow_api.h", "path_type": "hardlink", "sha256": "ee5d06e3efe6f7200b62889fb18f19eaa8771c70f43e09295528029ff25a194e", "sha256_in_prefix": "ee5d06e3efe6f7200b62889fb18f19eaa8771c70f43e09295528029ff25a194e", "size_in_bytes": 867}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/pyarrow_lib.h", "path_type": "hardlink", "sha256": "fbbd3f0a48f7ff4226973697489384fdddf0f6918ceba9578863f2967f5cf7a6", "sha256_in_prefix": "fbbd3f0a48f7ff4226973697489384fdddf0f6918ceba9578863f2967f5cf7a6", "size_in_bytes": 863}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/python_test.cc", "path_type": "hardlink", "sha256": "d59f65bdefc533502747e8938ad8745acf9cdcb6bbcbba60fb4c560cf2699e56", "sha256_in_prefix": "d59f65bdefc533502747e8938ad8745acf9cdcb6bbcbba60fb4c560cf2699e56", "size_in_bytes": 32415}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/python_test.h", "path_type": "hardlink", "sha256": "79adf698cdb4b87c92972822f4cb55c6bdba3bec9d4d91c2510225c5a20c8d3e", "sha256_in_prefix": "79adf698cdb4b87c92972822f4cb55c6bdba3bec9d4d91c2510225c5a20c8d3e", "size_in_bytes": 1195}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/python_to_arrow.cc", "path_type": "hardlink", "sha256": "21ae2f35d6e267bbf9a24bc128798a9ab5db6bb9b98d6aa4a1a90d54b4a325e0", "sha256_in_prefix": "21ae2f35d6e267bbf9a24bc128798a9ab5db6bb9b98d6aa4a1a90d54b4a325e0", "size_in_bytes": 47535}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/python_to_arrow.h", "path_type": "hardlink", "sha256": "068572b5fe8fecf0585f2ce7721125299485bc4b05ca2981fad2c5770d0050da", "sha256_in_prefix": "068572b5fe8fecf0585f2ce7721125299485bc4b05ca2981fad2c5770d0050da", "size_in_bytes": 2521}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/type_traits.h", "path_type": "hardlink", "sha256": "07f36c453fe1646f03f75b13722849c8a1794abb253dc1668f5d907dba47b8b2", "sha256_in_prefix": "07f36c453fe1646f03f75b13722849c8a1794abb253dc1668f5d907dba47b8b2", "size_in_bytes": 10093}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/udf.cc", "path_type": "hardlink", "sha256": "ebd0ee1d18d5eab5006d9aa4584284506dce0ee1e0f729b9da59c7ec0fe53396", "sha256_in_prefix": "ebd0ee1d18d5eab5006d9aa4584284506dce0ee1e0f729b9da59c7ec0fe53396", "size_in_bytes": 29814}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/udf.h", "path_type": "hardlink", "sha256": "75edd1f0f84d24ee654fda02a91c5ef1edbf484de30691ce9306cda82ae58234", "sha256_in_prefix": "75edd1f0f84d24ee654fda02a91c5ef1edbf484de30691ce9306cda82ae58234", "size_in_bytes": 3104}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/vendored/CMakeLists.txt", "path_type": "hardlink", "sha256": "d365ef0c901d2a26a308404e9a730a0699b36d153b14f90d765357b70d3e036e", "sha256_in_prefix": "d365ef0c901d2a26a308404e9a730a0699b36d153b14f90d765357b70d3e036e", "size_in_bytes": 837}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/vendored/pythoncapi_compat.h", "path_type": "hardlink", "sha256": "6f33279474c27e39390d0448c70cadba7621e5a43153789212569a0f2367018f", "sha256_in_prefix": "6f33279474c27e39390d0448c70cadba7621e5a43153789212569a0f2367018f", "size_in_bytes": 40900}, {"_path": "lib/python3.11/site-packages/pyarrow/src/arrow/python/visibility.h", "path_type": "hardlink", "sha256": "870270e6c1ab58972442435a02e2dee137fe5438d06d7927cb334e560648dc52", "sha256_in_prefix": "870270e6c1ab58972442435a02e2dee137fe5438d06d7927cb334e560648dc52", "size_in_bytes": 1381}, {"_path": "lib/python3.11/site-packages/pyarrow/substrait.py", "path_type": "hardlink", "sha256": "c8e32009bf80e575bdb760ca82eb51c9d0c7a45bd092234daba648bb4848501f", "sha256_in_prefix": "c8e32009bf80e575bdb760ca82eb51c9d0c7a45bd092234daba648bb4848501f", "size_in_bytes": 1230}, {"_path": "lib/python3.11/site-packages/pyarrow/table.pxi", "path_type": "hardlink", "sha256": "416a6060fc6cf11bded332218f2ac08fce933b8e40b6156501f00cebd5795249", "sha256_in_prefix": "416a6060fc6cf11bded332218f2ac08fce933b8e40b6156501f00cebd5795249", "size_in_bytes": 207873}, {"_path": "lib/python3.11/site-packages/pyarrow/tensor.pxi", "path_type": "hardlink", "sha256": "09794c71345687f9ff153cc8231f12a470a662557420103af6da10fb712fb39a", "sha256_in_prefix": "09794c71345687f9ff153cc8231f12a470a662557420103af6da10fb712fb39a", "size_in_bytes": 42071}, {"_path": "lib/python3.11/site-packages/pyarrow/types.pxi", "path_type": "hardlink", "sha256": "3735d61ba805264dc26cfe4f4f78423a0db327752a2a39f2715045959076440c", "sha256_in_prefix": "3735d61ba805264dc26cfe4f4f78423a0db327752a2a39f2715045959076440c", "size_in_bytes": 166663}, {"_path": "lib/python3.11/site-packages/pyarrow/types.py", "path_type": "hardlink", "sha256": "33e7c6a4715cf8ec930d3fc8886ffd9793ced87ec0da2ca76e24b193a66ff1b3", "sha256_in_prefix": "33e7c6a4715cf8ec930d3fc8886ffd9793ced87ec0da2ca76e24b193a66ff1b3", "size_in_bytes": 7492}, {"_path": "lib/python3.11/site-packages/pyarrow/util.py", "path_type": "hardlink", "sha256": "0119707d914aeac30a18d1b657d1dc1b4102d0b1871c8981f40321a16508e272", "sha256_in_prefix": "0119707d914aeac30a18d1b657d1dc1b4102d0b1871c8981f40321a16508e272", "size_in_bytes": 8640}, {"_path": "lib/python3.11/site-packages/pyarrow/vendored/__init__.py", "path_type": "hardlink", "sha256": "f617571c006b56991ba6366051fb77f643852f6c5219e1b8184b9ad0799e96eb", "sha256_in_prefix": "f617571c006b56991ba6366051fb77f643852f6c5219e1b8184b9ad0799e96eb", "size_in_bytes": 785}, {"_path": "lib/python3.11/site-packages/pyarrow/vendored/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "4851b0506efd423988208dfebe891157b1ba01c2f4a3edfe847359f5376469c0", "sha256_in_prefix": "4851b0506efd423988208dfebe891157b1ba01c2f4a3edfe847359f5376469c0", "size_in_bytes": 166}, {"_path": "lib/python3.11/site-packages/pyarrow/vendored/__pycache__/docscrape.cpython-311.pyc", "path_type": "hardlink", "sha256": "74ca15b658763479e13df3fcea9d92f60ab16966b20b9cf6f43db556a1eac44b", "sha256_in_prefix": "74ca15b658763479e13df3fcea9d92f60ab16966b20b9cf6f43db556a1eac44b", "size_in_bytes": 36543}, {"_path": "lib/python3.11/site-packages/pyarrow/vendored/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "4c7a8d4f478969e5ff09573fa5a2353d74cdc443b83c8b15a5f7a6bef497100b", "sha256_in_prefix": "4c7a8d4f478969e5ff09573fa5a2353d74cdc443b83c8b15a5f7a6bef497100b", "size_in_bytes": 22424}, {"_path": "lib/python3.11/site-packages/pyarrow/vendored/docscrape.py", "path_type": "hardlink", "sha256": "a614e3c2ece83b9841f3c41ead9937b86bbd7393ab6708c5cc8eef10821b0944", "sha256_in_prefix": "a614e3c2ece83b9841f3c41ead9937b86bbd7393ab6708c5cc8eef10821b0944", "size_in_bytes": 22975}, {"_path": "lib/python3.11/site-packages/pyarrow/vendored/version.py", "path_type": "hardlink", "sha256": "e7e568e10de43c9ae6d43486bac9cc953c6e03cca7238840af202905de8c9e94", "sha256_in_prefix": "e7e568e10de43c9ae6d43486bac9cc953c6e03cca7238840af202905de8c9e94", "size_in_bytes": 14345}], "paths_version": 1}, "requested_spec": "None", "sha256": "94e7c13f91ab71351aae344b0d42283be3549d66767a3bc97bf238d91c2bbb7d", "size": 4148827, "subdir": "osx-64", "timestamp": 1746000633000, "url": "https://conda.anaconda.org/conda-forge/osx-64/pyarrow-core-20.0.0-py311he02522f_0_cpu.conda", "version": "20.0.0"}