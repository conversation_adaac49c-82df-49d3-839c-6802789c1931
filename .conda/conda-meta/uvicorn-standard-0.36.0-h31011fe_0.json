{"build": "h31011fe_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["__unix", "httptools >=0.6.3", "python-dotenv >=0.13", "pyyaml >=5.1", "uvicorn 0.36.0 pyh31011fe_0", "uvloop >=0.14.0,!=0.15.0,!=0.15.1", "watchfiles >=0.13", "websockets >=10.4"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/uvicorn-standard-0.36.0-h31011fe_0", "files": [], "fn": "uvicorn-standard-0.36.0-h31011fe_0.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/uvicorn-standard-0.36.0-h31011fe_0", "type": 1}, "md5": "f7d2a92dea4c271c3c4d1f0867d58e12", "name": "uvicorn-standard", "noarch": "generic", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/uvicorn-standard-0.36.0-h31011fe_0.conda", "package_type": "noarch_generic", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "fcf515e3c3646db35c29392631dc28da8b1968bf2691d57fb64c740b2daa28f0", "size": 7667, "subdir": "noarch", "timestamp": 1758365927000, "url": "https://conda.anaconda.org/conda-forge/noarch/uvicorn-standard-0.36.0-h31011fe_0.conda", "version": "0.36.0"}