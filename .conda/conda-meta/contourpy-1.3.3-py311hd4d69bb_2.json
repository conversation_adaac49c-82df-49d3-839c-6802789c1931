{"build": "py311hd4d69bb_2", "build_number": 2, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13", "libcxx >=19", "numpy >=1.25", "python >=3.11,<3.12.0a0", "python_abi 3.11.* *_cp311"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/contourpy-1.3.3-py311hd4d69bb_2", "files": ["lib/python3.11/site-packages/contourpy-1.3.3.dist-info/INSTALLER", "lib/python3.11/site-packages/contourpy-1.3.3.dist-info/LICENSE", "lib/python3.11/site-packages/contourpy-1.3.3.dist-info/METADATA", "lib/python3.11/site-packages/contourpy-1.3.3.dist-info/RECORD", "lib/python3.11/site-packages/contourpy-1.3.3.dist-info/REQUESTED", "lib/python3.11/site-packages/contourpy-1.3.3.dist-info/WHEEL", "lib/python3.11/site-packages/contourpy-1.3.3.dist-info/direct_url.json", "lib/python3.11/site-packages/contourpy/__init__.py", "lib/python3.11/site-packages/contourpy/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/contourpy/__pycache__/_version.cpython-311.pyc", "lib/python3.11/site-packages/contourpy/__pycache__/array.cpython-311.pyc", "lib/python3.11/site-packages/contourpy/__pycache__/chunk.cpython-311.pyc", "lib/python3.11/site-packages/contourpy/__pycache__/convert.cpython-311.pyc", "lib/python3.11/site-packages/contourpy/__pycache__/dechunk.cpython-311.pyc", "lib/python3.11/site-packages/contourpy/__pycache__/enum_util.cpython-311.pyc", "lib/python3.11/site-packages/contourpy/__pycache__/typecheck.cpython-311.pyc", "lib/python3.11/site-packages/contourpy/__pycache__/types.cpython-311.pyc", "lib/python3.11/site-packages/contourpy/_contourpy.cpython-311-darwin.so", "lib/python3.11/site-packages/contourpy/_contourpy.pyi", "lib/python3.11/site-packages/contourpy/_version.py", "lib/python3.11/site-packages/contourpy/array.py", "lib/python3.11/site-packages/contourpy/chunk.py", "lib/python3.11/site-packages/contourpy/convert.py", "lib/python3.11/site-packages/contourpy/dechunk.py", "lib/python3.11/site-packages/contourpy/enum_util.py", "lib/python3.11/site-packages/contourpy/py.typed", "lib/python3.11/site-packages/contourpy/typecheck.py", "lib/python3.11/site-packages/contourpy/types.py", "lib/python3.11/site-packages/contourpy/util/__init__.py", "lib/python3.11/site-packages/contourpy/util/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/contourpy/util/__pycache__/_build_config.cpython-311.pyc", "lib/python3.11/site-packages/contourpy/util/__pycache__/bokeh_renderer.cpython-311.pyc", "lib/python3.11/site-packages/contourpy/util/__pycache__/bokeh_util.cpython-311.pyc", "lib/python3.11/site-packages/contourpy/util/__pycache__/data.cpython-311.pyc", "lib/python3.11/site-packages/contourpy/util/__pycache__/mpl_renderer.cpython-311.pyc", "lib/python3.11/site-packages/contourpy/util/__pycache__/mpl_util.cpython-311.pyc", "lib/python3.11/site-packages/contourpy/util/__pycache__/renderer.cpython-311.pyc", "lib/python3.11/site-packages/contourpy/util/_build_config.py", "lib/python3.11/site-packages/contourpy/util/bokeh_renderer.py", "lib/python3.11/site-packages/contourpy/util/bokeh_util.py", "lib/python3.11/site-packages/contourpy/util/data.py", "lib/python3.11/site-packages/contourpy/util/mpl_renderer.py", "lib/python3.11/site-packages/contourpy/util/mpl_util.py", "lib/python3.11/site-packages/contourpy/util/renderer.py"], "fn": "contourpy-1.3.3-py311hd4d69bb_2.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/contourpy-1.3.3-py311hd4d69bb_2", "type": 1}, "md5": "7d3b5e08077a62b8f24737e54aa3fe81", "name": "contourpy", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/contourpy-1.3.3-py311hd4d69bb_2.conda", "paths_data": {"paths": [{"_path": "lib/python3.11/site-packages/contourpy-1.3.3.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.11/site-packages/contourpy-1.3.3.dist-info/LICENSE", "path_type": "hardlink", "sha256": "34170979fc64f4f5e6dfa66ef27dec314ffffc5852000c60f4836ec1dfbf156e", "sha256_in_prefix": "34170979fc64f4f5e6dfa66ef27dec314ffffc5852000c60f4836ec1dfbf156e", "size_in_bytes": 1534}, {"_path": "lib/python3.11/site-packages/contourpy-1.3.3.dist-info/METADATA", "path_type": "hardlink", "sha256": "c43cef86baef09cff9c77975e578fa8cca0c9454df40dc176938b499a63de54a", "sha256_in_prefix": "c43cef86baef09cff9c77975e578fa8cca0c9454df40dc176938b499a63de54a", "size_in_bytes": 5461}, {"_path": "lib/python3.11/site-packages/contourpy-1.3.3.dist-info/RECORD", "path_type": "hardlink", "sha256": "7380e8cf0e5fcfc80f0b15db9f2b3eab42640d5ca1be34270ef5f8edb07e4574", "sha256_in_prefix": "7380e8cf0e5fcfc80f0b15db9f2b3eab42640d5ca1be34270ef5f8edb07e4574", "size_in_bytes": 3122}, {"_path": "lib/python3.11/site-packages/contourpy-1.3.3.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/contourpy-1.3.3.dist-info/WHEEL", "path_type": "hardlink", "sha256": "a927b1c2c86b34e4038ec286c76dcdd3ae2f4ff273fbdf3fdd04eae6ce84fb42", "sha256_in_prefix": "a927b1c2c86b34e4038ec286c76dcdd3ae2f4ff273fbdf3fdd04eae6ce84fb42", "size_in_bytes": 95}, {"_path": "lib/python3.11/site-packages/contourpy-1.3.3.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "1fda101a6a058e7c6c3cf13131cd3a84e009ee8cf819ba6c8da8190307600968", "sha256_in_prefix": "1fda101a6a058e7c6c3cf13131cd3a84e009ee8cf819ba6c8da8190307600968", "size_in_bytes": 97}, {"_path": "lib/python3.11/site-packages/contourpy/__init__.py", "path_type": "hardlink", "sha256": "eac7fe2b40f8c5a1080e1937f1448e8eccd188bcf9dab338dfebed71f56b32f6", "sha256_in_prefix": "eac7fe2b40f8c5a1080e1937f1448e8eccd188bcf9dab338dfebed71f56b32f6", "size_in_bytes": 11765}, {"_path": "lib/python3.11/site-packages/contourpy/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "7b78ccc8651fd0d87228357e75265d17c22c5c1774f65a967fc6ec8a520669a7", "sha256_in_prefix": "7b78ccc8651fd0d87228357e75265d17c22c5c1774f65a967fc6ec8a520669a7", "size_in_bytes": 13365}, {"_path": "lib/python3.11/site-packages/contourpy/__pycache__/_version.cpython-311.pyc", "path_type": "hardlink", "sha256": "4f72d3818f1e6f342d44d35861dab32c400b7964e50fba0a4e889304a6016dc8", "sha256_in_prefix": "4f72d3818f1e6f342d44d35861dab32c400b7964e50fba0a4e889304a6016dc8", "size_in_bytes": 179}, {"_path": "lib/python3.11/site-packages/contourpy/__pycache__/array.cpython-311.pyc", "path_type": "hardlink", "sha256": "8f630674576d8b1b2c4c74e770a6cf5f03bc09c7aa37105c29cdb4147e6677bf", "sha256_in_prefix": "8f630674576d8b1b2c4c74e770a6cf5f03bc09c7aa37105c29cdb4147e6677bf", "size_in_bytes": 15254}, {"_path": "lib/python3.11/site-packages/contourpy/__pycache__/chunk.cpython-311.pyc", "path_type": "hardlink", "sha256": "42fa1b1c091458a9f59af0366195bbcd86cebf24c30db3c3283e3e87acc755fb", "sha256_in_prefix": "42fa1b1c091458a9f59af0366195bbcd86cebf24c30db3c3283e3e87acc755fb", "size_in_bytes": 3938}, {"_path": "lib/python3.11/site-packages/contourpy/__pycache__/convert.cpython-311.pyc", "path_type": "hardlink", "sha256": "f0d48f67e4d337fac44cd128a2fc7186f5e3371554eaca01ad959d75831a2691", "sha256_in_prefix": "f0d48f67e4d337fac44cd128a2fc7186f5e3371554eaca01ad959d75831a2691", "size_in_bytes": 29536}, {"_path": "lib/python3.11/site-packages/contourpy/__pycache__/dechunk.cpython-311.pyc", "path_type": "hardlink", "sha256": "4907cf5d64f7fc65105f4772f4f3fd4e583690491a97403c44bcfd45354c0141", "sha256_in_prefix": "4907cf5d64f7fc65105f4772f4f3fd4e583690491a97403c44bcfd45354c0141", "size_in_bytes": 9013}, {"_path": "lib/python3.11/site-packages/contourpy/__pycache__/enum_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "c0728868859a1fb90394af2b76f56f3ee1bf0d2927571423c8673a4b0b84dec4", "sha256_in_prefix": "c0728868859a1fb90394af2b76f56f3ee1bf0d2927571423c8673a4b0b84dec4", "size_in_bytes": 2313}, {"_path": "lib/python3.11/site-packages/contourpy/__pycache__/typecheck.cpython-311.pyc", "path_type": "hardlink", "sha256": "c08f4e54fd1a37ccb0efbad4b78a3a227e42d8c4cd54b6e441d99efd3859fcdf", "sha256_in_prefix": "c08f4e54fd1a37ccb0efbad4b78a3a227e42d8c4cd54b6e441d99efd3859fcdf", "size_in_bytes": 13754}, {"_path": "lib/python3.11/site-packages/contourpy/__pycache__/types.cpython-311.pyc", "path_type": "hardlink", "sha256": "157a6e311bfbf57b07c9e40655c544c41373eadcd467a85b5b370172fa3220e7", "sha256_in_prefix": "157a6e311bfbf57b07c9e40655c544c41373eadcd467a85b5b370172fa3220e7", "size_in_bytes": 444}, {"_path": "lib/python3.11/site-packages/contourpy/_contourpy.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "f399b388ff3db32477183d4684b07ecd86a421586e435eb59fc44a0886675933", "sha256_in_prefix": "f399b388ff3db32477183d4684b07ecd86a421586e435eb59fc44a0886675933", "size_in_bytes": 751568}, {"_path": "lib/python3.11/site-packages/contourpy/_contourpy.pyi", "path_type": "hardlink", "sha256": "7efb5c73192267019beaa61a83b169e04f1bb05980223026a1b7fc2cdc6a7e07", "sha256_in_prefix": "7efb5c73192267019beaa61a83b169e04f1bb05980223026a1b7fc2cdc6a7e07", "size_in_bytes": 7122}, {"_path": "lib/python3.11/site-packages/contourpy/_version.py", "path_type": "hardlink", "sha256": "562ea89b72889a52ac4bf5a0e428d48187dfa22db3c7b4fe4913e79c62f41bb3", "sha256_in_prefix": "562ea89b72889a52ac4bf5a0e428d48187dfa22db3c7b4fe4913e79c62f41bb3", "size_in_bytes": 22}, {"_path": "lib/python3.11/site-packages/contourpy/array.py", "path_type": "hardlink", "sha256": "e16c0bba265edf4ae2ce7feb6b29a6635dcecc4ea1942b0338ef24b9514e3f5f", "sha256_in_prefix": "e16c0bba265edf4ae2ce7feb6b29a6635dcecc4ea1942b0338ef24b9514e3f5f", "size_in_bytes": 8979}, {"_path": "lib/python3.11/site-packages/contourpy/chunk.py", "path_type": "hardlink", "sha256": "f278c342a969b83db646369a0b203be455d0b124036398591894abc45a6f1865", "sha256_in_prefix": "f278c342a969b83db646369a0b203be455d0b124036398591894abc45a6f1865", "size_in_bytes": 3279}, {"_path": "lib/python3.11/site-packages/contourpy/convert.py", "path_type": "hardlink", "sha256": "9a1ca7ee9ac4a160a77f48a0687f95a83c2593e09e805b19e2a3b2d8b2fe8695", "sha256_in_prefix": "9a1ca7ee9ac4a160a77f48a0687f95a83c2593e09e805b19e2a3b2d8b2fe8695", "size_in_bytes": 26154}, {"_path": "lib/python3.11/site-packages/contourpy/dechunk.py", "path_type": "hardlink", "sha256": "12014bea1c391f9e1c72ea1fe2d2767a176792d4fbb6b8198e266aa69b07f102", "sha256_in_prefix": "12014bea1c391f9e1c72ea1fe2d2767a176792d4fbb6b8198e266aa69b07f102", "size_in_bytes": 7756}, {"_path": "lib/python3.11/site-packages/contourpy/enum_util.py", "path_type": "hardlink", "sha256": "a3c308b4946cd3ca2acf03cfdc8c02ef9041018f50abde6c688ce3917057c2a0", "sha256_in_prefix": "a3c308b4946cd3ca2acf03cfdc8c02ef9041018f50abde6c688ce3917057c2a0", "size_in_bytes": 1519}, {"_path": "lib/python3.11/site-packages/contourpy/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/contourpy/typecheck.py", "path_type": "hardlink", "sha256": "b759efbc2b8a318bdad59c787dcdf412974a15c3b4127cf79ff5057d706cabda", "sha256_in_prefix": "b759efbc2b8a318bdad59c787dcdf412974a15c3b4127cf79ff5057d706cabda", "size_in_bytes": 10747}, {"_path": "lib/python3.11/site-packages/contourpy/types.py", "path_type": "hardlink", "sha256": "d8ae13e6d2693088d8ae4920d4baa1dc2d992a59ce86732d626b70cfdda5ff2f", "sha256_in_prefix": "d8ae13e6d2693088d8ae4920d4baa1dc2d992a59ce86732d626b70cfdda5ff2f", "size_in_bytes": 247}, {"_path": "lib/python3.11/site-packages/contourpy/util/__init__.py", "path_type": "hardlink", "sha256": "795849fdcace1cbee7906e0a6f474ea3b34be161cccbf3f1ebae5a00dff777ef", "sha256_in_prefix": "795849fdcace1cbee7906e0a6f474ea3b34be161cccbf3f1ebae5a00dff777ef", "size_in_bytes": 118}, {"_path": "lib/python3.11/site-packages/contourpy/util/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "45dbae20916862477c9687b4612b8d192b89ba74ff8f1a681130e7c52613e93e", "sha256_in_prefix": "45dbae20916862477c9687b4612b8d192b89ba74ff8f1a681130e7c52613e93e", "size_in_bytes": 331}, {"_path": "lib/python3.11/site-packages/contourpy/util/__pycache__/_build_config.cpython-311.pyc", "path_type": "hardlink", "sha256": "8351be9c8b57ca531333f842ffdac1a64bbdef48defe56041db6441068152922", "sha256_in_prefix": "8351be9c8b57ca531333f842ffdac1a64bbdef48defe56041db6441068152922", "size_in_bytes": 2555}, {"_path": "lib/python3.11/site-packages/contourpy/util/__pycache__/bokeh_renderer.cpython-311.pyc", "path_type": "hardlink", "sha256": "81f28030a923fd2b00ef1e0ea17b9f34a43e309ef1048bf84ee1fb91b48e5c1a", "sha256_in_prefix": "81f28030a923fd2b00ef1e0ea17b9f34a43e309ef1048bf84ee1fb91b48e5c1a", "size_in_bytes": 19592}, {"_path": "lib/python3.11/site-packages/contourpy/util/__pycache__/bokeh_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "bf54c0f7272ca82f3c33daa6bac31a15db76385d133a92d9b6cce7ae89087a70", "sha256_in_prefix": "bf54c0f7272ca82f3c33daa6bac31a15db76385d133a92d9b6cce7ae89087a70", "size_in_bytes": 4143}, {"_path": "lib/python3.11/site-packages/contourpy/util/__pycache__/data.cpython-311.pyc", "path_type": "hardlink", "sha256": "44cb1fa10b1eeff257e024c953c1bae93926fc179503076fa1c4b2f9542dd786", "sha256_in_prefix": "44cb1fa10b1eeff257e024c953c1bae93926fc179503076fa1c4b2f9542dd786", "size_in_bytes": 4311}, {"_path": "lib/python3.11/site-packages/contourpy/util/__pycache__/mpl_renderer.cpython-311.pyc", "path_type": "hardlink", "sha256": "c6eb7ecd4553ad2ccf24e825fb836345d381163dc8c1a5fe72bea2b84e4c1f76", "sha256_in_prefix": "c6eb7ecd4553ad2ccf24e825fb836345d381163dc8c1a5fe72bea2b84e4c1f76", "size_in_bytes": 28505}, {"_path": "lib/python3.11/site-packages/contourpy/util/__pycache__/mpl_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "cb3033424575d26905fec04db35cee6ee0a68dc0b4fcf84c6bd8ba8be69cdb4f", "sha256_in_prefix": "cb3033424575d26905fec04db35cee6ee0a68dc0b4fcf84c6bd8ba8be69cdb4f", "size_in_bytes": 5989}, {"_path": "lib/python3.11/site-packages/contourpy/util/__pycache__/renderer.cpython-311.pyc", "path_type": "hardlink", "sha256": "3961575aff7cfcee193ca1a0a1444c012489c10e9160da86f46682e4f224c730", "sha256_in_prefix": "3961575aff7cfcee193ca1a0a1444c012489c10e9160da86f46682e4f224c730", "size_in_bytes": 7316}, {"_path": "lib/python3.11/site-packages/contourpy/util/_build_config.py", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/contourpy_1756544721130/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold", "sha256": "c032ccb2a5361bcf1fdc5b063bdaa8b9824546d1b3ac9e46ac853adea98faf8b", "sha256_in_prefix": "53f54c228f5d0974351f16e7ce2d47adf9acb717a200829f9bfe04144a84ac5e", "size_in_bytes": 2276}, {"_path": "lib/python3.11/site-packages/contourpy/util/bokeh_renderer.py", "path_type": "hardlink", "sha256": "bd241cabfdf32105a3d44f2aa0cda3dec23b5e504525c6a6f5ac6e85f6657b82", "sha256_in_prefix": "bd241cabfdf32105a3d44f2aa0cda3dec23b5e504525c6a6f5ac6e85f6657b82", "size_in_bytes": 13836}, {"_path": "lib/python3.11/site-packages/contourpy/util/bokeh_util.py", "path_type": "hardlink", "sha256": "c1cf92ddec015185b222412ff639211724887ab8232d09786745045671341e10", "sha256_in_prefix": "c1cf92ddec015185b222412ff639211724887ab8232d09786745045671341e10", "size_in_bytes": 2804}, {"_path": "lib/python3.11/site-packages/contourpy/util/data.py", "path_type": "hardlink", "sha256": "fbb49218c2d7fe037ed47d89ce9352101fc470417fb8cb5874ea3f78f448720f", "sha256_in_prefix": "fbb49218c2d7fe037ed47d89ce9352101fc470417fb8cb5874ea3f78f448720f", "size_in_bytes": 2586}, {"_path": "lib/python3.11/site-packages/contourpy/util/mpl_renderer.py", "path_type": "hardlink", "sha256": "7613e148c2b056e16778d199b812205c2a7d6237dfa87a54c8f1b90be4fee6d5", "sha256_in_prefix": "7613e148c2b056e16778d199b812205c2a7d6237dfa87a54c8f1b90be4fee6d5", "size_in_bytes": 20092}, {"_path": "lib/python3.11/site-packages/contourpy/util/mpl_util.py", "path_type": "hardlink", "sha256": "d09cf97fe680f573169693b6a439c76e4560c488b0e1263fcacc5ffe0002584a", "sha256_in_prefix": "d09cf97fe680f573169693b6a439c76e4560c488b0e1263fcacc5ffe0002584a", "size_in_bytes": 3452}, {"_path": "lib/python3.11/site-packages/contourpy/util/renderer.py", "path_type": "hardlink", "sha256": "f02047ccf995b053dfaac5b1aabc46061a85a498557851c5783cd55c0813f057", "sha256_in_prefix": "f02047ccf995b053dfaac5b1aabc46061a85a498557851c5783cd55c0813f057", "size_in_bytes": 5118}], "paths_version": 1}, "requested_spec": "None", "sha256": "69740b02124fd104b0c14494bc333cfc1bd64508d9dd0075a1493935866fbd64", "size": 269105, "subdir": "osx-64", "timestamp": 1756545009000, "url": "https://conda.anaconda.org/conda-forge/osx-64/contourpy-1.3.3-py311hd4d69bb_2.conda", "version": "1.3.3"}