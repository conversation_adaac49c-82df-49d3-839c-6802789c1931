{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["opentelemetry-api 1.37.0", "opentelemetry-semantic-conventions 0.58b0", "python >=3.10", "typing-extensions >=3.7.4", "typing_extensions >=4.5.0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-sdk-1.37.0-pyhd8ed1ab_0", "files": ["lib/python3.11/site-packages/opentelemetry/sdk/__init__.pyi", "lib/python3.11/site-packages/opentelemetry/sdk/_configuration/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/_events/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/_logs/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/_logs/_internal/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/_logs/_internal/export/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/_logs/_internal/export/in_memory_log_exporter.py", "lib/python3.11/site-packages/opentelemetry/sdk/_logs/export/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/_shared_internal/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/environment_variables/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/error_handler/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/_view_instrument_match.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/aggregation.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exceptions.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exemplar/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exemplar/exemplar.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exemplar/exemplar_filter.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exemplar/exemplar_reservoir.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/buckets.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/errors.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/exponent_mapping.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/ieee_754.md", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/ieee_754.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/logarithm_mapping.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/export/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/instrument.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/measurement.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/measurement_consumer.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/metric_reader_storage.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/point.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/sdk_configuration.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/view.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/export/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/view/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/py.typed", "lib/python3.11/site-packages/opentelemetry/sdk/resources/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/trace/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/_always_off.py", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/_always_on.py", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/_composable.py", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/_parent_threshold.py", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/_sampler.py", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/_trace_state.py", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/_traceid_ratio.py", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/_util.py", "lib/python3.11/site-packages/opentelemetry/sdk/trace/export/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/trace/export/in_memory_span_exporter.py", "lib/python3.11/site-packages/opentelemetry/sdk/trace/id_generator.py", "lib/python3.11/site-packages/opentelemetry/sdk/trace/sampling.py", "lib/python3.11/site-packages/opentelemetry/sdk/util/__init__.py", "lib/python3.11/site-packages/opentelemetry/sdk/util/__init__.pyi", "lib/python3.11/site-packages/opentelemetry/sdk/util/instrumentation.py", "lib/python3.11/site-packages/opentelemetry/sdk/version/__init__.py", "lib/python3.11/site-packages/opentelemetry_sdk-1.37.0.dist-info/INSTALLER", "lib/python3.11/site-packages/opentelemetry_sdk-1.37.0.dist-info/METADATA", "lib/python3.11/site-packages/opentelemetry_sdk-1.37.0.dist-info/RECORD", "lib/python3.11/site-packages/opentelemetry_sdk-1.37.0.dist-info/REQUESTED", "lib/python3.11/site-packages/opentelemetry_sdk-1.37.0.dist-info/WHEEL", "lib/python3.11/site-packages/opentelemetry_sdk-1.37.0.dist-info/direct_url.json", "lib/python3.11/site-packages/opentelemetry_sdk-1.37.0.dist-info/entry_points.txt", "lib/python3.11/site-packages/opentelemetry_sdk-1.37.0.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/opentelemetry/sdk/_configuration/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/_events/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/_logs/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/_logs/_internal/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/_logs/_internal/export/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/_logs/_internal/export/__pycache__/in_memory_log_exporter.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/_logs/export/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/_shared_internal/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/environment_variables/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/error_handler/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/_view_instrument_match.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/aggregation.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/exceptions.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exemplar/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exemplar/__pycache__/exemplar.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exemplar/__pycache__/exemplar_filter.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exemplar/__pycache__/exemplar_reservoir.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/__pycache__/buckets.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/__pycache__/errors.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/__pycache__/exponent_mapping.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/__pycache__/ieee_754.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/__pycache__/logarithm_mapping.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/export/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/instrument.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/measurement.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/measurement_consumer.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/metric_reader_storage.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/point.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/sdk_configuration.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/view.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/export/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/metrics/view/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/resources/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/trace/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/_always_off.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/_always_on.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/_composable.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/_parent_threshold.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/_sampler.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/_trace_state.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/_traceid_ratio.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/_util.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/trace/export/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/trace/export/__pycache__/in_memory_span_exporter.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/trace/__pycache__/id_generator.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/trace/__pycache__/sampling.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/util/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/util/__pycache__/instrumentation.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/sdk/version/__pycache__/__init__.cpython-311.pyc"], "fn": "opentelemetry-sdk-1.37.0-pyhd8ed1ab_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-sdk-1.37.0-pyhd8ed1ab_0", "type": 1}, "md5": "a447020a0099d43f970bdd862c492708", "name": "opentelemetry-sdk", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-sdk-1.37.0-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/opentelemetry/sdk/__init__.pyi", "path_type": "hardlink", "sha256": "91031b330f302d0b562756d5066ed7a08d3a07fe05bf4ba7e61bf714ac2b8114", "sha256_in_prefix": "91031b330f302d0b562756d5066ed7a08d3a07fe05bf4ba7e61bf714ac2b8114", "size_in_bytes": 669}, {"_path": "site-packages/opentelemetry/sdk/_configuration/__init__.py", "path_type": "hardlink", "sha256": "560ca113da3170847bada5b6fa88887d66a607aa36e41abc9a7118ca1769496c", "sha256_in_prefix": "560ca113da3170847bada5b6fa88887d66a607aa36e41abc9a7118ca1769496c", "size_in_bytes": 17034}, {"_path": "site-packages/opentelemetry/sdk/_events/__init__.py", "path_type": "hardlink", "sha256": "7b3f81554a4d86617b2d8721f854cc770f26ac9a76b0911d7c2042bdd4b8268e", "sha256_in_prefix": "7b3f81554a4d86617b2d8721f854cc770f26ac9a76b0911d7c2042bdd4b8268e", "size_in_bytes": 3309}, {"_path": "site-packages/opentelemetry/sdk/_logs/__init__.py", "path_type": "hardlink", "sha256": "4c4076fb777535c586c9b3cad4f7eb54ab303b5b0ed21f8c1244cf3b4fe640a6", "sha256_in_prefix": "4c4076fb777535c586c9b3cad4f7eb54ab303b5b0ed21f8c1244cf3b4fe640a6", "size_in_bytes": 1033}, {"_path": "site-packages/opentelemetry/sdk/_logs/_internal/__init__.py", "path_type": "hardlink", "sha256": "319e2817b669e0d8e37884d0a871f31898df570570fc08d6a2b311220b7ef721", "sha256_in_prefix": "319e2817b669e0d8e37884d0a871f31898df570570fc08d6a2b311220b7ef721", "size_in_bytes": 30573}, {"_path": "site-packages/opentelemetry/sdk/_logs/_internal/export/__init__.py", "path_type": "hardlink", "sha256": "ff951d38e3d1d95c5e798b37702d6467212ccdddf2c68efe733eeb9592bae670", "sha256_in_prefix": "ff951d38e3d1d95c5e798b37702d6467212ccdddf2c68efe733eeb9592bae670", "size_in_bytes": 9017}, {"_path": "site-packages/opentelemetry/sdk/_logs/_internal/export/in_memory_log_exporter.py", "path_type": "hardlink", "sha256": "6e45509869e49315f7c050cd33fe80ba68e3a70ec98e7bdfcde97fe7d6f22005", "sha256_in_prefix": "6e45509869e49315f7c050cd33fe80ba68e3a70ec98e7bdfcde97fe7d6f22005", "size_in_bytes": 1667}, {"_path": "site-packages/opentelemetry/sdk/_logs/export/__init__.py", "path_type": "hardlink", "sha256": "9d41dd5cd830a9f0ded0aa0690d057ed797f9a8e3bee2c8adcdd03e411fd8368", "sha256_in_prefix": "9d41dd5cd830a9f0ded0aa0690d057ed797f9a8e3bee2c8adcdd03e411fd8368", "size_in_bytes": 1120}, {"_path": "site-packages/opentelemetry/sdk/_shared_internal/__init__.py", "path_type": "hardlink", "sha256": "4458b342b79a4d22ad86f9ae08191f797fc8bb27dec8f35d9e0d3dadfbcae767", "sha256_in_prefix": "4458b342b79a4d22ad86f9ae08191f797fc8bb27dec8f35d9e0d3dadfbcae767", "size_in_bytes": 9826}, {"_path": "site-packages/opentelemetry/sdk/environment_variables/__init__.py", "path_type": "hardlink", "sha256": "f8d86e21524ae7634653148d49fb7b3dc318854698ca188f192338864c8a208d", "sha256_in_prefix": "f8d86e21524ae7634653148d49fb7b3dc318854698ca188f192338864c8a208d", "size_in_bytes": 30429}, {"_path": "site-packages/opentelemetry/sdk/error_handler/__init__.py", "path_type": "hardlink", "sha256": "eb60cde85fe17dacd6aaf05d9705dc9b08242151e3ad915a3b112929e8eab99e", "sha256_in_prefix": "eb60cde85fe17dacd6aaf05d9705dc9b08242151e3ad915a3b112929e8eab99e", "size_in_bytes": 4614}, {"_path": "site-packages/opentelemetry/sdk/metrics/__init__.py", "path_type": "hardlink", "sha256": "910e3aed6b7fdee40c6d5b2dbe0ba09ba2c306eb4578310defaa04949879cf7e", "sha256_in_prefix": "910e3aed6b7fdee40c6d5b2dbe0ba09ba2c306eb4578310defaa04949879cf7e", "size_in_bytes": 1745}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/__init__.py", "path_type": "hardlink", "sha256": "51d0c1fbfb05b0ad7e981058b0f5c4b509fa34d7663618e81bdee46580743f56", "sha256_in_prefix": "51d0c1fbfb05b0ad7e981058b0f5c4b509fa34d7663618e81bdee46580743f56", "size_in_bytes": 20789}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/_view_instrument_match.py", "path_type": "hardlink", "sha256": "f1c4cb9c343a16546d4e374cabc578567fcd0fe29aea230f045f1e31e7313c31", "sha256_in_prefix": "f1c4cb9c343a16546d4e374cabc578567fcd0fe29aea230f045f1e31e7313c31", "size_in_bytes": 5933}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/aggregation.py", "path_type": "hardlink", "sha256": "da529f9af5a48f9748ff594d5ed1d87320448f866d48a54997d7812c1b678114", "sha256_in_prefix": "da529f9af5a48f9748ff594d5ed1d87320448f866d48a54997d7812c1b678114", "size_in_bytes": 51481}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/exceptions.py", "path_type": "hardlink", "sha256": "ff46cf837b2e628217289ede0aa1b74bf814295714007a75d6fc13870a7fc808", "sha256_in_prefix": "ff46cf837b2e628217289ede0aa1b74bf814295714007a75d6fc13870a7fc808", "size_in_bytes": 675}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/exemplar/__init__.py", "path_type": "hardlink", "sha256": "ccfc75caa6da365e8f9d092d2114f358aa9426dba7c2106b07dbba059f12c0b6", "sha256_in_prefix": "ccfc75caa6da365e8f9d092d2114f358aa9426dba7c2106b07dbba059f12c0b6", "size_in_bytes": 1218}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/exemplar/exemplar.py", "path_type": "hardlink", "sha256": "3e70ff6682c7e5e2e899679f67276f3db027c391537b004bc92fe06ff95cd97d", "sha256_in_prefix": "3e70ff6682c7e5e2e899679f67276f3db027c391537b004bc92fe06ff95cd97d", "size_in_bytes": 2112}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/exemplar/exemplar_filter.py", "path_type": "hardlink", "sha256": "413c8c9f87f1ea9c8fdd59a59fa74942dab8729a6586afc3c38a51aa21843f74", "sha256_in_prefix": "413c8c9f87f1ea9c8fdd59a59fa74942dab8729a6586afc3c38a51aa21843f74", "size_in_bytes": 4673}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/exemplar/exemplar_reservoir.py", "path_type": "hardlink", "sha256": "8505d0ec536898429642361fb4d7e8c82726a5b83f7ad0988e18f34cec92e16e", "sha256_in_prefix": "8505d0ec536898429642361fb4d7e8c82726a5b83f7ad0988e18f34cec92e16e", "size_in_bytes": 10656}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/buckets.py", "path_type": "hardlink", "sha256": "c179461c702780c50d198e69909f9802878a63ddff7ca0c7af2a8bc6b8438ad5", "sha256_in_prefix": "c179461c702780c50d198e69909f9802878a63ddff7ca0c7af2a8bc6b8438ad5", "size_in_bytes": 5943}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/__init__.py", "path_type": "hardlink", "sha256": "16a5ebad3914e67818341ff16fa72baba800a6692077c3f4a7f6df0974829ca8", "sha256_in_prefix": "16a5ebad3914e67818341ff16fa72baba800a6692077c3f4a7f6df0974829ca8", "size_in_bytes": 3859}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/errors.py", "path_type": "hardlink", "sha256": "e90ea37ec565b842a9e51ffd1022d6f26ab7668a325f4c3d595cf97be60086e6", "sha256_in_prefix": "e90ea37ec565b842a9e51ffd1022d6f26ab7668a325f4c3d595cf97be60086e6", "size_in_bytes": 886}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/exponent_mapping.py", "path_type": "hardlink", "sha256": "93bd28e8577acde768e15708d533932a1d91bab75a01431153cdfbb1de643b9e", "sha256_in_prefix": "93bd28e8577acde768e15708d533932a1d91bab75a01431153cdfbb1de643b9e", "size_in_bytes": 6130}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/ieee_754.md", "path_type": "hardlink", "sha256": "f0d7fc1466d98b6e9ce8a724c48b091c7dac6b4849676e1009e3a613d86e24b8", "sha256_in_prefix": "f0d7fc1466d98b6e9ce8a724c48b091c7dac6b4849676e1009e3a613d86e24b8", "size_in_bytes": 4980}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/ieee_754.py", "path_type": "hardlink", "sha256": "6873f8f73c7a1088e96027c365cc2bce8612931de8935119ac678b1bc4faaab0", "sha256_in_prefix": "6873f8f73c7a1088e96027c365cc2bce8612931de8935119ac678b1bc4faaab0", "size_in_bytes": 5482}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/logarithm_mapping.py", "path_type": "hardlink", "sha256": "ea5f705df0fd4a6b4e1947927c36fca8859dc45f5a4a50e292eab4ff7885f47f", "sha256_in_prefix": "ea5f705df0fd4a6b4e1947927c36fca8859dc45f5a4a50e292eab4ff7885f47f", "size_in_bytes": 5832}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/export/__init__.py", "path_type": "hardlink", "sha256": "82255da115475d7d6ed77b2a7d6a0a5cacc2320e7a62ac4207295de182146643", "sha256_in_prefix": "82255da115475d7d6ed77b2a7d6a0a5cacc2320e7a62ac4207295de182146643", "size_in_bytes": 21378}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/instrument.py", "path_type": "hardlink", "sha256": "9dff564d64f5584ea2c8a4536a96ef334bb05893afd55f669146fe954f95e65e", "sha256_in_prefix": "9dff564d64f5584ea2c8a4536a96ef334bb05893afd55f669146fe954f95e65e", "size_in_bytes": 10507}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/measurement.py", "path_type": "hardlink", "sha256": "53d495d52203d2d08ebe251b4f89363749ccb03f1c76c9228483ce490490aca0", "sha256_in_prefix": "53d495d52203d2d08ebe251b4f89363749ccb03f1c76c9228483ce490490aca0", "size_in_bytes": 1663}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/measurement_consumer.py", "path_type": "hardlink", "sha256": "7d7e3008c0d4901bbcc3766399719806cee35a92f9ef244c6d8808aa6e055adf", "sha256_in_prefix": "7d7e3008c0d4901bbcc3766399719806cee35a92f9ef244c6d8808aa6e055adf", "size_in_bytes": 5164}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/metric_reader_storage.py", "path_type": "hardlink", "sha256": "382c2f0d4086accc9d93f3a58b8fd436bc0de204f831dc9dc59a4a88046ec7d6", "sha256_in_prefix": "382c2f0d4086accc9d93f3a58b8fd436bc0de204f831dc9dc59a4a88046ec7d6", "size_in_bytes": 12050}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/point.py", "path_type": "hardlink", "sha256": "4b2584dc6416cde4a5964f8f5c77fe32fc20abde76464e8671a97376eb41f849", "sha256_in_prefix": "4b2584dc6416cde4a5964f8f5c77fe32fc20abde76464e8671a97376eb41f849", "size_in_bytes": 8100}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/sdk_configuration.py", "path_type": "hardlink", "sha256": "dd375f2f40d691c78c3703d608cab9b3a8c786e881ef556365457e44381da5c2", "sha256_in_prefix": "dd375f2f40d691c78c3703d608cab9b3a8c786e881ef556365457e44381da5c2", "size_in_bytes": 1084}, {"_path": "site-packages/opentelemetry/sdk/metrics/_internal/view.py", "path_type": "hardlink", "sha256": "4b0578a4520632883d123648403880206eb756a94818b95cd9abaf0658909a08", "sha256_in_prefix": "4b0578a4520632883d123648403880206eb756a94818b95cd9abaf0658909a08", "size_in_bytes": 7526}, {"_path": "site-packages/opentelemetry/sdk/metrics/export/__init__.py", "path_type": "hardlink", "sha256": "1a0e97da23151eb509ed49222993d4ccd851cbae47ab53c6f93940cfe47914a8", "sha256_in_prefix": "1a0e97da23151eb509ed49222993d4ccd851cbae47ab53c6f93940cfe47914a8", "size_in_bytes": 1707}, {"_path": "site-packages/opentelemetry/sdk/metrics/view/__init__.py", "path_type": "hardlink", "sha256": "90fa9de9841d20aa7502c3bc962e1389888062f6d374a099565fdf3874403a49", "sha256_in_prefix": "90fa9de9841d20aa7502c3bc962e1389888062f6d374a099565fdf3874403a49", "size_in_bytes": 1130}, {"_path": "site-packages/opentelemetry/sdk/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/sdk/resources/__init__.py", "path_type": "hardlink", "sha256": "8a0c61d2fdad040e9128020d1f2ae4c3b34929a4611162e08ffe837117356a1a", "sha256_in_prefix": "8a0c61d2fdad040e9128020d1f2ae4c3b34929a4611162e08ffe837117356a1a", "size_in_bytes": 19896}, {"_path": "site-packages/opentelemetry/sdk/trace/__init__.py", "path_type": "hardlink", "sha256": "89210c878ec958c694c9301f890ed06473ebf5d8ae181be82e668830f27daa07", "sha256_in_prefix": "89210c878ec958c694c9301f890ed06473ebf5d8ae181be82e668830f27daa07", "size_in_bytes": 45188}, {"_path": "site-packages/opentelemetry/sdk/trace/_sampling_experimental/__init__.py", "path_type": "hardlink", "sha256": "8adbddc264a65a4321c6d4dce9eb75e756d2e095d8462c6b569b0cb2a45fb115", "sha256_in_prefix": "8adbddc264a65a4321c6d4dce9eb75e756d2e095d8462c6b569b0cb2a45fb115", "size_in_bytes": 1112}, {"_path": "site-packages/opentelemetry/sdk/trace/_sampling_experimental/_always_off.py", "path_type": "hardlink", "sha256": "8f56083dc07a5fa76973760a9207c0e4eec0654954dc950831ce966e389349ea", "sha256_in_prefix": "8f56083dc07a5fa76973760a9207c0e4eec0654954dc950831ce966e389349ea", "size_in_bytes": 1770}, {"_path": "site-packages/opentelemetry/sdk/trace/_sampling_experimental/_always_on.py", "path_type": "hardlink", "sha256": "1886ba728aecc0cfe13743c0a20322bb0d1b421b1a45c8dc4e08050891ae449d", "sha256_in_prefix": "1886ba728aecc0cfe13743c0a20322bb0d1b421b1a45c8dc4e08050891ae449d", "size_in_bytes": 1719}, {"_path": "site-packages/opentelemetry/sdk/trace/_sampling_experimental/_composable.py", "path_type": "hardlink", "sha256": "2b8d787f55c57a5642f326a6680a0094d1c9ff4adeb22d913c04c13b94e38ea8", "sha256_in_prefix": "2b8d787f55c57a5642f326a6680a0094d1c9ff4adeb22d913c04c13b94e38ea8", "size_in_bytes": 2121}, {"_path": "site-packages/opentelemetry/sdk/trace/_sampling_experimental/_parent_threshold.py", "path_type": "hardlink", "sha256": "b45e6ad7625d479a08c0311399c7f65b5671a8f661a24756b699dfbb26ebbb12", "sha256_in_prefix": "b45e6ad7625d479a08c0311399c7f65b5671a8f661a24756b699dfbb26ebbb12", "size_in_bytes": 3338}, {"_path": "site-packages/opentelemetry/sdk/trace/_sampling_experimental/_sampler.py", "path_type": "hardlink", "sha256": "c74e802d5fa71fa53c324df8ca2e2f9ce80232eb5e46dc30106d143ed8ac16e1", "sha256_in_prefix": "c74e802d5fa71fa53c324df8ca2e2f9ce80232eb5e46dc30106d143ed8ac16e1", "size_in_bytes": 3531}, {"_path": "site-packages/opentelemetry/sdk/trace/_sampling_experimental/_trace_state.py", "path_type": "hardlink", "sha256": "18cc8c4d4170726286e289eb9e23522bf2916de12134079f1d785430defaa4dc", "sha256_in_prefix": "18cc8c4d4170726286e289eb9e23522bf2916de12134079f1d785430defaa4dc", "size_in_bytes": 4121}, {"_path": "site-packages/opentelemetry/sdk/trace/_sampling_experimental/_traceid_ratio.py", "path_type": "hardlink", "sha256": "be3c345d482c60064822b4f2860c1b134aec7ecef5f0f1759f294b12d04790a2", "sha256_in_prefix": "be3c345d482c60064822b4f2860c1b134aec7ecef5f0f1759f294b12d04790a2", "size_in_bytes": 2675}, {"_path": "site-packages/opentelemetry/sdk/trace/_sampling_experimental/_util.py", "path_type": "hardlink", "sha256": "63b6cbad0782c08dfe46d88b4d04cf55c342dbf374c54496fbc67cf6bd2a347d", "sha256_in_prefix": "63b6cbad0782c08dfe46d88b4d04cf55c342dbf374c54496fbc67cf6bd2a347d", "size_in_bytes": 1212}, {"_path": "site-packages/opentelemetry/sdk/trace/export/__init__.py", "path_type": "hardlink", "sha256": "13141a7524fc12d82959eefaac55361ce0e000f25f702a25309d1a268b0baefd", "sha256_in_prefix": "13141a7524fc12d82959eefaac55361ce0e000f25f702a25309d1a268b0baefd", "size_in_bytes": 9783}, {"_path": "site-packages/opentelemetry/sdk/trace/export/in_memory_span_exporter.py", "path_type": "hardlink", "sha256": "1ffe1345a4e130ed47eaf510d0ea50bf3264fdf647d0e3ac440335899417b11f", "sha256_in_prefix": "1ffe1345a4e130ed47eaf510d0ea50bf3264fdf647d0e3ac440335899417b11f", "size_in_bytes": 2112}, {"_path": "site-packages/opentelemetry/sdk/trace/id_generator.py", "path_type": "hardlink", "sha256": "61d311101e1470f6dd9e13000c5486d678ebbb83e3c8d1784430a9b63704e8b7", "sha256_in_prefix": "61d311101e1470f6dd9e13000c5486d678ebbb83e3c8d1784430a9b63704e8b7", "size_in_bytes": 1959}, {"_path": "site-packages/opentelemetry/sdk/trace/sampling.py", "path_type": "hardlink", "sha256": "9f9900322cb7184d365036c6b8a175900b292d10a7a8e16d4a9274d0a66f1f0f", "sha256_in_prefix": "9f9900322cb7184d365036c6b8a175900b292d10a7a8e16d4a9274d0a66f1f0f", "size_in_bytes": 16876}, {"_path": "site-packages/opentelemetry/sdk/util/__init__.py", "path_type": "hardlink", "sha256": "679eef83485882aaa9b51c2ce81af5ee9fa32715efa96d8255d96cb2cb970a14", "sha256_in_prefix": "679eef83485882aaa9b51c2ce81af5ee9fa32715efa96d8255d96cb2cb970a14", "size_in_bytes": 4402}, {"_path": "site-packages/opentelemetry/sdk/util/__init__.pyi", "path_type": "hardlink", "sha256": "95876bf46840b684c5fba9ca7352c7963752181454c94804b61f081230a83448", "sha256_in_prefix": "95876bf46840b684c5fba9ca7352c7963752181454c94804b61f081230a83448", "size_in_bytes": 2350}, {"_path": "site-packages/opentelemetry/sdk/util/instrumentation.py", "path_type": "hardlink", "sha256": "a339628754ec3ab95ad70426105bc1598487405ca6889e0e1b209122249c6343", "sha256_in_prefix": "a339628754ec3ab95ad70426105bc1598487405ca6889e0e1b209122249c6343", "size_in_bytes": 4880}, {"_path": "site-packages/opentelemetry/sdk/version/__init__.py", "path_type": "hardlink", "sha256": "420f83b16cfb888dea77dc3e0d220cfd48ae5a7b8b63a49afd9ea2c88e9d3518", "sha256_in_prefix": "420f83b16cfb888dea77dc3e0d220cfd48ae5a7b8b63a49afd9ea2c88e9d3518", "size_in_bytes": 608}, {"_path": "site-packages/opentelemetry_sdk-1.37.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/opentelemetry_sdk-1.37.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "381d6335eec6f6481b0da3bc4ee9c64bd71843b7d1d887a60b8c0af64a43307c", "sha256_in_prefix": "381d6335eec6f6481b0da3bc4ee9c64bd71843b7d1d887a60b8c0af64a43307c", "size_in_bytes": 1508}, {"_path": "site-packages/opentelemetry_sdk-1.37.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "1cc0825619fd03901e35227264655f438d5c857adbd3f09cf4a6d623532caf28", "sha256_in_prefix": "1cc0825619fd03901e35227264655f438d5c857adbd3f09cf4a6d623532caf28", "size_in_bytes": 11644}, {"_path": "site-packages/opentelemetry_sdk-1.37.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry_sdk-1.37.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "sha256_in_prefix": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "size_in_bytes": 87}, {"_path": "site-packages/opentelemetry_sdk-1.37.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "b8429a4acfd56ab9acf597c8430196d6d22c2b5b680736004952ae0829082f88", "sha256_in_prefix": "b8429a4acfd56ab9acf597c8430196d6d22c2b5b680736004952ae0829082f88", "size_in_bytes": 113}, {"_path": "site-packages/opentelemetry_sdk-1.37.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "f8ea276464b8c47758850b48fd7caa8f5136ec4a27a5b49fdfbd2d3be81960d9", "sha256_in_prefix": "f8ea276464b8c47758850b48fd7caa8f5136ec4a27a5b49fdfbd2d3be81960d9", "size_in_bytes": 1457}, {"_path": "site-packages/opentelemetry_sdk-1.37.0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "sha256_in_prefix": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "size_in_bytes": 11357}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/_configuration/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/_events/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/_logs/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/_logs/_internal/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/_logs/_internal/export/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/_logs/_internal/export/__pycache__/in_memory_log_exporter.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/_logs/export/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/_shared_internal/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/environment_variables/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/error_handler/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/_view_instrument_match.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/aggregation.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/exceptions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exemplar/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exemplar/__pycache__/exemplar.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exemplar/__pycache__/exemplar_filter.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exemplar/__pycache__/exemplar_reservoir.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/__pycache__/buckets.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/__pycache__/errors.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/__pycache__/exponent_mapping.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/__pycache__/ieee_754.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/exponential_histogram/mapping/__pycache__/logarithm_mapping.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/export/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/instrument.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/measurement.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/measurement_consumer.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/metric_reader_storage.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/point.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/sdk_configuration.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/_internal/__pycache__/view.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/export/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/metrics/view/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/resources/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/trace/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/_always_off.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/_always_on.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/_composable.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/_parent_threshold.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/_sampler.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/_trace_state.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/_traceid_ratio.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/trace/_sampling_experimental/__pycache__/_util.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/trace/export/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/trace/export/__pycache__/in_memory_span_exporter.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/trace/__pycache__/id_generator.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/trace/__pycache__/sampling.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/util/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/util/__pycache__/instrumentation.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/sdk/version/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "514a6743d1a0569b1c6666e4f5f1a3d05fc51836975970c6f632e97821e4835d", "size": 84325, "subdir": "noarch", "timestamp": 1757743400000, "url": "https://conda.anaconda.org/conda-forge/noarch/opentelemetry-sdk-1.37.0-pyhd8ed1ab_0.conda", "version": "1.37.0"}