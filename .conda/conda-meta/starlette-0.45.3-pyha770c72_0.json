{"build": "pyha770c72_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["anyio >=3.6.2,<5", "python >=3.9", "typing_extensions >=3.10.0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/starlette-0.45.3-pyha770c72_0", "files": ["lib/python3.11/site-packages/starlette-0.45.3.dist-info/INSTALLER", "lib/python3.11/site-packages/starlette-0.45.3.dist-info/METADATA", "lib/python3.11/site-packages/starlette-0.45.3.dist-info/RECORD", "lib/python3.11/site-packages/starlette-0.45.3.dist-info/REQUESTED", "lib/python3.11/site-packages/starlette-0.45.3.dist-info/WHEEL", "lib/python3.11/site-packages/starlette-0.45.3.dist-info/direct_url.json", "lib/python3.11/site-packages/starlette-0.45.3.dist-info/licenses/LICENSE.md", "lib/python3.11/site-packages/starlette/__init__.py", "lib/python3.11/site-packages/starlette/_exception_handler.py", "lib/python3.11/site-packages/starlette/_utils.py", "lib/python3.11/site-packages/starlette/applications.py", "lib/python3.11/site-packages/starlette/authentication.py", "lib/python3.11/site-packages/starlette/background.py", "lib/python3.11/site-packages/starlette/concurrency.py", "lib/python3.11/site-packages/starlette/config.py", "lib/python3.11/site-packages/starlette/convertors.py", "lib/python3.11/site-packages/starlette/datastructures.py", "lib/python3.11/site-packages/starlette/endpoints.py", "lib/python3.11/site-packages/starlette/exceptions.py", "lib/python3.11/site-packages/starlette/formparsers.py", "lib/python3.11/site-packages/starlette/middleware/__init__.py", "lib/python3.11/site-packages/starlette/middleware/authentication.py", "lib/python3.11/site-packages/starlette/middleware/base.py", "lib/python3.11/site-packages/starlette/middleware/cors.py", "lib/python3.11/site-packages/starlette/middleware/errors.py", "lib/python3.11/site-packages/starlette/middleware/exceptions.py", "lib/python3.11/site-packages/starlette/middleware/gzip.py", "lib/python3.11/site-packages/starlette/middleware/httpsredirect.py", "lib/python3.11/site-packages/starlette/middleware/sessions.py", "lib/python3.11/site-packages/starlette/middleware/trustedhost.py", "lib/python3.11/site-packages/starlette/middleware/wsgi.py", "lib/python3.11/site-packages/starlette/py.typed", "lib/python3.11/site-packages/starlette/requests.py", "lib/python3.11/site-packages/starlette/responses.py", "lib/python3.11/site-packages/starlette/routing.py", "lib/python3.11/site-packages/starlette/schemas.py", "lib/python3.11/site-packages/starlette/staticfiles.py", "lib/python3.11/site-packages/starlette/status.py", "lib/python3.11/site-packages/starlette/templating.py", "lib/python3.11/site-packages/starlette/testclient.py", "lib/python3.11/site-packages/starlette/types.py", "lib/python3.11/site-packages/starlette/websockets.py", "lib/python3.11/site-packages/starlette/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/_exception_handler.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/_utils.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/applications.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/authentication.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/background.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/concurrency.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/config.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/convertors.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/datastructures.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/endpoints.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/exceptions.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/formparsers.cpython-311.pyc", "lib/python3.11/site-packages/starlette/middleware/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/starlette/middleware/__pycache__/authentication.cpython-311.pyc", "lib/python3.11/site-packages/starlette/middleware/__pycache__/base.cpython-311.pyc", "lib/python3.11/site-packages/starlette/middleware/__pycache__/cors.cpython-311.pyc", "lib/python3.11/site-packages/starlette/middleware/__pycache__/errors.cpython-311.pyc", "lib/python3.11/site-packages/starlette/middleware/__pycache__/exceptions.cpython-311.pyc", "lib/python3.11/site-packages/starlette/middleware/__pycache__/gzip.cpython-311.pyc", "lib/python3.11/site-packages/starlette/middleware/__pycache__/httpsredirect.cpython-311.pyc", "lib/python3.11/site-packages/starlette/middleware/__pycache__/sessions.cpython-311.pyc", "lib/python3.11/site-packages/starlette/middleware/__pycache__/trustedhost.cpython-311.pyc", "lib/python3.11/site-packages/starlette/middleware/__pycache__/wsgi.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/requests.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/responses.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/routing.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/schemas.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/staticfiles.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/status.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/templating.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/testclient.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/types.cpython-311.pyc", "lib/python3.11/site-packages/starlette/__pycache__/websockets.cpython-311.pyc"], "fn": "starlette-0.45.3-pyha770c72_0.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/starlette-0.45.3-pyha770c72_0", "type": 1}, "md5": "9b3a68bc7aed7949ef86f950993261f4", "name": "starlette", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/starlette-0.45.3-pyha770c72_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/starlette-0.45.3.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/starlette-0.45.3.dist-info/METADATA", "path_type": "hardlink", "sha256": "16ec171407eaee262c5c04408b3b2ad281c86a3fbc6a76f1d70fb443c19b484d", "sha256_in_prefix": "16ec171407eaee262c5c04408b3b2ad281c86a3fbc6a76f1d70fb443c19b484d", "size_in_bytes": 6280}, {"_path": "site-packages/starlette-0.45.3.dist-info/RECORD", "path_type": "hardlink", "sha256": "d1cc495164404275f833e4a7ca1300d2da2e33dfb0beb11ada176c910b7a391c", "sha256_in_prefix": "d1cc495164404275f833e4a7ca1300d2da2e33dfb0beb11ada176c910b7a391c", "size_in_bytes": 5346}, {"_path": "site-packages/starlette-0.45.3.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/starlette-0.45.3.dist-info/WHEEL", "path_type": "hardlink", "sha256": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "sha256_in_prefix": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "size_in_bytes": 87}, {"_path": "site-packages/starlette-0.45.3.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "ecf215335959f8258f28507d26c8bc3c0e4ec09955a1f8f40dfaa580ad2a996d", "sha256_in_prefix": "ecf215335959f8258f28507d26c8bc3c0e4ec09955a1f8f40dfaa580ad2a996d", "size_in_bytes": 105}, {"_path": "site-packages/starlette-0.45.3.dist-info/licenses/LICENSE.md", "path_type": "hardlink", "sha256": "dcb95677a02240243187e964f941847d19b17821cf99e5afae684fab328c19bf", "sha256_in_prefix": "dcb95677a02240243187e964f941847d19b17821cf99e5afae684fab328c19bf", "size_in_bytes": 1518}, {"_path": "site-packages/starlette/__init__.py", "path_type": "hardlink", "sha256": "c613dfb0c2a79b7bc5d6b406a9c7206fbf934d9240fc695104c90e1ad436c1e2", "sha256_in_prefix": "c613dfb0c2a79b7bc5d6b406a9c7206fbf934d9240fc695104c90e1ad436c1e2", "size_in_bytes": 23}, {"_path": "site-packages/starlette/_exception_handler.py", "path_type": "hardlink", "sha256": "38ce07e3c7798b2bfcfe87f1043632d4f7e5b091a5af8d231593c8dc36194d08", "sha256_in_prefix": "38ce07e3c7798b2bfcfe87f1043632d4f7e5b091a5af8d231593c8dc36194d08", "size_in_bytes": 2219}, {"_path": "site-packages/starlette/_utils.py", "path_type": "hardlink", "sha256": "bdca2bb6035c8fe01ab5a00655f37bac8715c51ca1e963325197c451527830e6", "sha256_in_prefix": "bdca2bb6035c8fe01ab5a00655f37bac8715c51ca1e963325197c451527830e6", "size_in_bytes": 2764}, {"_path": "site-packages/starlette/applications.py", "path_type": "hardlink", "sha256": "f4aaf99f0763d5e94a31ff6b7a348b95852fac093ffd4d04815b2a705685bd67", "sha256_in_prefix": "f4aaf99f0763d5e94a31ff6b7a348b95852fac093ffd4d04815b2a705685bd67", "size_in_bytes": 10676}, {"_path": "site-packages/starlette/authentication.py", "path_type": "hardlink", "sha256": "f5d9727b2903321a3182623d845559dfcbaef0d97a93635989aa1aeb4ed9f217", "sha256_in_prefix": "f5d9727b2903321a3182623d845559dfcbaef0d97a93635989aa1aeb4ed9f217", "size_in_bytes": 4948}, {"_path": "site-packages/starlette/background.py", "path_type": "hardlink", "sha256": "e74cc143fe74955f5819119ebc15054380cec22b14e3ba5457f3df8811bd0a0e", "sha256_in_prefix": "e74cc143fe74955f5819119ebc15054380cec22b14e3ba5457f3df8811bd0a0e", "size_in_bytes": 1257}, {"_path": "site-packages/starlette/concurrency.py", "path_type": "hardlink", "sha256": "ce3058cfd50f697a542b1d6a573a1c5501e5c430e2f74c0e3942ae6d78d75932", "sha256_in_prefix": "ce3058cfd50f697a542b1d6a573a1c5501e5c430e2f74c0e3942ae6d78d75932", "size_in_bytes": 1746}, {"_path": "site-packages/starlette/config.py", "path_type": "hardlink", "sha256": "c425e041380c69ebb25da1d1168afb10988054c51d8e7bba1e0c33e2bafab145", "sha256_in_prefix": "c425e041380c69ebb25da1d1168afb10988054c51d8e7bba1e0c33e2bafab145", "size_in_bytes": 4445}, {"_path": "site-packages/starlette/convertors.py", "path_type": "hardlink", "sha256": "44177baba5329e5265e313ff3170eb466b47f0de79adef1dfd241aced9b03d0c", "sha256_in_prefix": "44177baba5329e5265e313ff3170eb466b47f0de79adef1dfd241aced9b03d0c", "size_in_bytes": 2302}, {"_path": "site-packages/starlette/datastructures.py", "path_type": "hardlink", "sha256": "8a7347a833948318e88d255ce45379a0bd378b3b903369c8803ce7eddce109b8", "sha256_in_prefix": "8a7347a833948318e88d255ce45379a0bd378b3b903369c8803ce7eddce109b8", "size_in_bytes": 22334}, {"_path": "site-packages/starlette/endpoints.py", "path_type": "hardlink", "sha256": "4601571c0c3cfcab60a9e6b94f97b88c4598ef72db07cdb6a1dc7a2d723effa2", "sha256_in_prefix": "4601571c0c3cfcab60a9e6b94f97b88c4598ef72db07cdb6a1dc7a2d723effa2", "size_in_bytes": 5098}, {"_path": "site-packages/starlette/exceptions.py", "path_type": "hardlink", "sha256": "b48a619596bc12c41f2b0dfec70e49dd93751a3691e147317c92baf40776846f", "sha256_in_prefix": "b48a619596bc12c41f2b0dfec70e49dd93751a3691e147317c92baf40776846f", "size_in_bytes": 1066}, {"_path": "site-packages/starlette/formparsers.py", "path_type": "hardlink", "sha256": "616f51627abfec27308bcbb6a91e38e73dec57ff0feabf853bdc68d29eb9ea5e", "sha256_in_prefix": "616f51627abfec27308bcbb6a91e38e73dec57ff0feabf853bdc68d29eb9ea5e", "size_in_bytes": 10859}, {"_path": "site-packages/starlette/middleware/__init__.py", "path_type": "hardlink", "sha256": "dd696371f0039d296d26b554b85829bc989929c8ec8c2d4887d6aa614bd29279", "sha256_in_prefix": "dd696371f0039d296d26b554b85829bc989929c8ec8c2d4887d6aa614bd29279", "size_in_bytes": 1224}, {"_path": "site-packages/starlette/middleware/authentication.py", "path_type": "hardlink", "sha256": "7ed59555b113c6baf480ec2dc7050bc30dfad9692dd6cf62395bb8b352c9e772", "sha256_in_prefix": "7ed59555b113c6baf480ec2dc7050bc30dfad9692dd6cf62395bb8b352c9e772", "size_in_bytes": 1791}, {"_path": "site-packages/starlette/middleware/base.py", "path_type": "hardlink", "sha256": "7c4c6d0e8cb58d440f230a862bac1e291957daac403e2ceb519ec6335bae1558", "sha256_in_prefix": "7c4c6d0e8cb58d440f230a862bac1e291957daac403e2ceb519ec6335bae1558", "size_in_bytes": 8813}, {"_path": "site-packages/starlette/middleware/cors.py", "path_type": "hardlink", "sha256": "fa287827199e45a2a19afd3be8370913ec1dd0ad8525ba36de0cd974bac96f4d", "sha256_in_prefix": "fa287827199e45a2a19afd3be8370913ec1dd0ad8525ba36de0cd974bac96f4d", "size_in_bytes": 7051}, {"_path": "site-packages/starlette/middleware/errors.py", "path_type": "hardlink", "sha256": "6e899351f408a3b088c27650f1f173e0108363989d427aecae1192a593621feb", "sha256_in_prefix": "6e899351f408a3b088c27650f1f173e0108363989d427aecae1192a593621feb", "size_in_bytes": 8066}, {"_path": "site-packages/starlette/middleware/exceptions.py", "path_type": "hardlink", "sha256": "43de9a717cfd6d121464e4402a396c6dc685b88d3e9bad5c49799f16afcd0fad", "sha256_in_prefix": "43de9a717cfd6d121464e4402a396c6dc685b88d3e9bad5c49799f16afcd0fad", "size_in_bytes": 2791}, {"_path": "site-packages/starlette/middleware/gzip.py", "path_type": "hardlink", "sha256": "1a2d42aedda5f4616663ee7f68cea9475a67b3edd2e03f1f40531439f76d7de9", "sha256_in_prefix": "1a2d42aedda5f4616663ee7f68cea9475a67b3edd2e03f1f40531439f76d7de9", "size_in_bytes": 4526}, {"_path": "site-packages/starlette/middleware/httpsredirect.py", "path_type": "hardlink", "sha256": "48d4e579a6002c6a084d5ef1c1b89ce2007a55874cf1895e6bfca4722533df58", "sha256_in_prefix": "48d4e579a6002c6a084d5ef1c1b89ce2007a55874cf1895e6bfca4722533df58", "size_in_bytes": 848}, {"_path": "site-packages/starlette/middleware/sessions.py", "path_type": "hardlink", "sha256": "0a96ea97f0536a756a0db84f19da14acdbc720a7fd0f762e738f1c2597ab1535", "sha256_in_prefix": "0a96ea97f0536a756a0db84f19da14acdbc720a7fd0f762e738f1c2597ab1535", "size_in_bytes": 3566}, {"_path": "site-packages/starlette/middleware/trustedhost.py", "path_type": "hardlink", "sha256": "7576d6db60b7458369dd3b1d7c2b84d0c352f9564570df01fd2df062bcc44e6f", "sha256_in_prefix": "7576d6db60b7458369dd3b1d7c2b84d0c352f9564570df01fd2df062bcc44e6f", "size_in_bytes": 2203}, {"_path": "site-packages/starlette/middleware/wsgi.py", "path_type": "hardlink", "sha256": "d2f2ecb5c6a557e92ad98caa411f4d08b88f360a245bb9c3bf246a4298ffe624", "sha256_in_prefix": "d2f2ecb5c6a557e92ad98caa411f4d08b88f360a245bb9c3bf246a4298ffe624", "size_in_bytes": 5386}, {"_path": "site-packages/starlette/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/starlette/requests.py", "path_type": "hardlink", "sha256": "42f82cd915ca36d1385ff71450d5acba296c15f448529dcd1e69657690b31ef1", "sha256_in_prefix": "42f82cd915ca36d1385ff71450d5acba296c15f448529dcd1e69657690b31ef1", "size_in_bytes": 11693}, {"_path": "site-packages/starlette/responses.py", "path_type": "hardlink", "sha256": "8385788cb9ebf216113c54109458484761354c35b062eda1257fa5f994d1929f", "sha256_in_prefix": "8385788cb9ebf216113c54109458484761354c35b062eda1257fa5f994d1929f", "size_in_bytes": 20172}, {"_path": "site-packages/starlette/routing.py", "path_type": "hardlink", "sha256": "4ee51c69b075c2027ebd89a1073795f9b17331ff3bafba27cdbd18d71e17b1fc", "sha256_in_prefix": "4ee51c69b075c2027ebd89a1073795f9b17331ff3bafba27cdbd18d71e17b1fc", "size_in_bytes": 34569}, {"_path": "site-packages/starlette/schemas.py", "path_type": "hardlink", "sha256": "90a551146fe489c0fc81e12795f1e812c04213ef0cb1d5f23c4d153387f6436e", "sha256_in_prefix": "90a551146fe489c0fc81e12795f1e812c04213ef0cb1d5f23c4d153387f6436e", "size_in_bytes": 5181}, {"_path": "site-packages/starlette/staticfiles.py", "path_type": "hardlink", "sha256": "6bb7c71a63452c617884112f7cb3580e2f996d2eb66d557044f47082f94ad18a", "sha256_in_prefix": "6bb7c71a63452c617884112f7cb3580e2f996d2eb66d557044f47082f94ad18a", "size_in_bytes": 8417}, {"_path": "site-packages/starlette/status.py", "path_type": "hardlink", "sha256": "7bbd3157ac18151e5b7669189206024b0664d4bd8574a0f003abb8cc28e6ca94", "sha256_in_prefix": "7bbd3157ac18151e5b7669189206024b0664d4bd8574a0f003abb8cc28e6ca94", "size_in_bytes": 2820}, {"_path": "site-packages/starlette/templating.py", "path_type": "hardlink", "sha256": "73cf06a6150359c2e250a49524dbe71c8e57a7a7197bd1d85fb55bfebfa95449", "sha256_in_prefix": "73cf06a6150359c2e250a49524dbe71c8e57a7a7197bd1d85fb55bfebfa95449", "size_in_bytes": 8408}, {"_path": "site-packages/starlette/testclient.py", "path_type": "hardlink", "sha256": "9f61ab5c5693f840d196e8140b81eac52bcce336fc629d01d1f50099fb9959d4", "sha256_in_prefix": "9f61ab5c5693f840d196e8140b81eac52bcce336fc629d01d1f50099fb9959d4", "size_in_bytes": 27667}, {"_path": "site-packages/starlette/types.py", "path_type": "hardlink", "sha256": "236c883f9475ab2543d4606225943f23d769adc61439554af1f1dae94415b2a8", "sha256_in_prefix": "236c883f9475ab2543d4606225943f23d769adc61439554af1f1dae94415b2a8", "size_in_bytes": 1048}, {"_path": "site-packages/starlette/websockets.py", "path_type": "hardlink", "sha256": "33ff209909ad8411efa9d55f2c8cece32b90a6ea7b07ce77b4f49a35007f50ca", "sha256_in_prefix": "33ff209909ad8411efa9d55f2c8cece32b90a6ea7b07ce77b4f49a35007f50ca", "size_in_bytes": 8332}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/_exception_handler.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/_utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/applications.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/authentication.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/background.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/concurrency.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/config.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/convertors.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/datastructures.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/endpoints.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/exceptions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/formparsers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/middleware/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/middleware/__pycache__/authentication.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/middleware/__pycache__/base.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/middleware/__pycache__/cors.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/middleware/__pycache__/errors.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/middleware/__pycache__/exceptions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/middleware/__pycache__/gzip.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/middleware/__pycache__/httpsredirect.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/middleware/__pycache__/sessions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/middleware/__pycache__/trustedhost.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/middleware/__pycache__/wsgi.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/requests.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/responses.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/routing.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/schemas.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/staticfiles.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/status.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/templating.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/testclient.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/types.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/starlette/__pycache__/websockets.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "be48c99e6fb8e12ebee09e6fbb4d78a170b614cdaa19ab791a8f5b6caf09919a", "size": 57934, "subdir": "noarch", "timestamp": 1737824077000, "url": "https://conda.anaconda.org/conda-forge/noarch/starlette-0.45.3-pyha770c72_0.conda", "version": "0.45.3"}