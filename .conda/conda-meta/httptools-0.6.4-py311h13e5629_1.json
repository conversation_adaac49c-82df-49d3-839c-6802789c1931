{"build": "py311h13e5629_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13", "python >=3.11,<3.12.0a0", "python_abi 3.11.* *_cp311"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/httptools-0.6.4-py311h13e5629_1", "files": ["lib/python3.11/site-packages/httptools-0.6.4.dist-info/INSTALLER", "lib/python3.11/site-packages/httptools-0.6.4.dist-info/METADATA", "lib/python3.11/site-packages/httptools-0.6.4.dist-info/RECORD", "lib/python3.11/site-packages/httptools-0.6.4.dist-info/REQUESTED", "lib/python3.11/site-packages/httptools-0.6.4.dist-info/WHEEL", "lib/python3.11/site-packages/httptools-0.6.4.dist-info/direct_url.json", "lib/python3.11/site-packages/httptools-0.6.4.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/httptools-0.6.4.dist-info/top_level.txt", "lib/python3.11/site-packages/httptools/__init__.py", "lib/python3.11/site-packages/httptools/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/httptools/__pycache__/_version.cpython-311.pyc", "lib/python3.11/site-packages/httptools/_version.py", "lib/python3.11/site-packages/httptools/parser/__init__.py", "lib/python3.11/site-packages/httptools/parser/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/httptools/parser/__pycache__/errors.cpython-311.pyc", "lib/python3.11/site-packages/httptools/parser/cparser.pxd", "lib/python3.11/site-packages/httptools/parser/errors.py", "lib/python3.11/site-packages/httptools/parser/parser.cpython-311-darwin.so", "lib/python3.11/site-packages/httptools/parser/parser.pyx", "lib/python3.11/site-packages/httptools/parser/python.pxd", "lib/python3.11/site-packages/httptools/parser/url_cparser.pxd", "lib/python3.11/site-packages/httptools/parser/url_parser.cpython-311-darwin.so", "lib/python3.11/site-packages/httptools/parser/url_parser.pyx"], "fn": "httptools-0.6.4-py311h13e5629_1.conda", "license": "MIT", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/httptools-0.6.4-py311h13e5629_1", "type": 1}, "md5": "3dfd489d2462a2617665f89045b34381", "name": "httptools", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/httptools-0.6.4-py311h13e5629_1.conda", "paths_data": {"paths": [{"_path": "lib/python3.11/site-packages/httptools-0.6.4.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.11/site-packages/httptools-0.6.4.dist-info/METADATA", "path_type": "hardlink", "sha256": "0238d349fd3931fd8cf619bb2349bd35c8486abbc55543004a1fe4801282adfe", "sha256_in_prefix": "0238d349fd3931fd8cf619bb2349bd35c8486abbc55543004a1fe4801282adfe", "size_in_bytes": 3838}, {"_path": "lib/python3.11/site-packages/httptools-0.6.4.dist-info/RECORD", "path_type": "hardlink", "sha256": "8f41a7538a68b13d5aff5211a885bf0b7f0ca88d7d3a6b238b708282a441a609", "sha256_in_prefix": "8f41a7538a68b13d5aff5211a885bf0b7f0ca88d7d3a6b238b708282a441a609", "size_in_bytes": 1871}, {"_path": "lib/python3.11/site-packages/httptools-0.6.4.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/httptools-0.6.4.dist-info/WHEEL", "path_type": "hardlink", "sha256": "9a99a83f4370ced80864e3ca8f3c6d5e12203e9375332d371b67792389f5359e", "sha256_in_prefix": "9a99a83f4370ced80864e3ca8f3c6d5e12203e9375332d371b67792389f5359e", "size_in_bytes": 111}, {"_path": "lib/python3.11/site-packages/httptools-0.6.4.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "c929aea32e11d94a131ba7d37c81733db9f3db23a460c16782e7a590d8dbdbc9", "sha256_in_prefix": "c929aea32e11d94a131ba7d37c81733db9f3db23a460c16782e7a590d8dbdbc9", "size_in_bytes": 97}, {"_path": "lib/python3.11/site-packages/httptools-0.6.4.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "f4573e7cb7676745fb5b8d36fa548aa93e391cfb6872ddacd65130c45ea6a92d", "sha256_in_prefix": "f4573e7cb7676745fb5b8d36fa548aa93e391cfb6872ddacd65130c45ea6a92d", "size_in_bytes": 1093}, {"_path": "lib/python3.11/site-packages/httptools-0.6.4.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "00f8c92936d9723d0e4387dd81fd9e4c293cda72b59f60455ce0fb932e3530f6", "sha256_in_prefix": "00f8c92936d9723d0e4387dd81fd9e4c293cda72b59f60455ce0fb932e3530f6", "size_in_bytes": 10}, {"_path": "lib/python3.11/site-packages/httptools/__init__.py", "path_type": "hardlink", "sha256": "a65b773086ee78975ca3d0f2ef3a07de4b0b35eca2aea59a81ab79af0466023a", "sha256_in_prefix": "a65b773086ee78975ca3d0f2ef3a07de4b0b35eca2aea59a81ab79af0466023a", "size_in_bytes": 147}, {"_path": "lib/python3.11/site-packages/httptools/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "d924d541d7b3f5d7b0ae85722c7f78208ebf4a23fb46228e2ea36a27030578a0", "sha256_in_prefix": "d924d541d7b3f5d7b0ae85722c7f78208ebf4a23fb46228e2ea36a27030578a0", "size_in_bytes": 329}, {"_path": "lib/python3.11/site-packages/httptools/__pycache__/_version.cpython-311.pyc", "path_type": "hardlink", "sha256": "338149dd1de89ad9f1fce1075aeab6ba3c4a0546ada9699e2dbe3493debc105c", "sha256_in_prefix": "338149dd1de89ad9f1fce1075aeab6ba3c4a0546ada9699e2dbe3493debc105c", "size_in_bytes": 181}, {"_path": "lib/python3.11/site-packages/httptools/_version.py", "path_type": "hardlink", "sha256": "012a8e07c7cb4bb8f066c339e752dce3d5b160fca3b5eaa7cf58835a691af8a0", "sha256_in_prefix": "012a8e07c7cb4bb8f066c339e752dce3d5b160fca3b5eaa7cf58835a691af8a0", "size_in_bytes": 575}, {"_path": "lib/python3.11/site-packages/httptools/parser/__init__.py", "path_type": "hardlink", "sha256": "7d6c9ca273c41d9949a23cd1c2604a4a7e02f393865e6284c2211c7631ea5cef", "sha256_in_prefix": "7d6c9ca273c41d9949a23cd1c2604a4a7e02f393865e6284c2211c7631ea5cef", "size_in_bytes": 166}, {"_path": "lib/python3.11/site-packages/httptools/parser/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "2d78140c66633d3d8e4f6b20daabf8bdddbdcd20516d7fc0b8511259a5599de5", "sha256_in_prefix": "2d78140c66633d3d8e4f6b20daabf8bdddbdcd20516d7fc0b8511259a5599de5", "size_in_bytes": 332}, {"_path": "lib/python3.11/site-packages/httptools/parser/__pycache__/errors.cpython-311.pyc", "path_type": "hardlink", "sha256": "eb434ba1247a96f34f6787c6a2dc872cd1bb1930a37c76db9135d225aaef9fe0", "sha256_in_prefix": "eb434ba1247a96f34f6787c6a2dc872cd1bb1930a37c76db9135d225aaef9fe0", "size_in_bytes": 1422}, {"_path": "lib/python3.11/site-packages/httptools/parser/cparser.pxd", "path_type": "hardlink", "sha256": "e2a0719e66bcdd5cfce99f6c3994718ea623db403e68b4965465d981354b26a1", "sha256_in_prefix": "e2a0719e66bcdd5cfce99f6c3994718ea623db403e68b4965465d981354b26a1", "size_in_bytes": 4977}, {"_path": "lib/python3.11/site-packages/httptools/parser/errors.py", "path_type": "hardlink", "sha256": "655aed375b263c86ffa2943651ddee09b1a534b32510260cdbee92eebe4b9c7b", "sha256_in_prefix": "655aed375b263c86ffa2943651ddee09b1a534b32510260cdbee92eebe4b9c7b", "size_in_bytes": 566}, {"_path": "lib/python3.11/site-packages/httptools/parser/parser.cpython-311-darwin.so", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/httptools_1756754444466/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold", "sha256": "647e914d1a535dd75645ab57ede92f8db4d1aae58b112bd13aad3d15140401b0", "sha256_in_prefix": "25b74e008ee0f5e68a35eb126e3816d440de1119f2df2d1fe0df45636a6d2ce2", "size_in_bytes": 153128}, {"_path": "lib/python3.11/site-packages/httptools/parser/parser.pyx", "path_type": "hardlink", "sha256": "c7405463d1331cd2820dac3e53c6e467531a286b6b390f225429b522e3ad1102", "sha256_in_prefix": "c7405463d1331cd2820dac3e53c6e467531a286b6b390f225429b522e3ad1102", "size_in_bytes": 15140}, {"_path": "lib/python3.11/site-packages/httptools/parser/python.pxd", "path_type": "hardlink", "sha256": "cd609d199877e1fc9036ddc15072148cfa98f1ae6ca1d45491f740071a981e04", "sha256_in_prefix": "cd609d199877e1fc9036ddc15072148cfa98f1ae6ca1d45491f740071a981e04", "size_in_bytes": 138}, {"_path": "lib/python3.11/site-packages/httptools/parser/url_cparser.pxd", "path_type": "hardlink", "sha256": "5f974323c03b4f49791cbfc2cedd264ecd25fddda55e75031f1d5337c2de8823", "sha256_in_prefix": "5f974323c03b4f49791cbfc2cedd264ecd25fddda55e75031f1d5337c2de8823", "size_in_bytes": 779}, {"_path": "lib/python3.11/site-packages/httptools/parser/url_parser.cpython-311-darwin.so", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/httptools_1756754444466/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold", "sha256": "452e808172c6e3417cd55eb1c59f39fb99c22671ac003011ba9cfe09db5953a8", "sha256_in_prefix": "fbe653bdd3de38ee8fbf01dec41fa290c069d1c215c60eced419d39d0da00c5e", "size_in_bytes": 85984}, {"_path": "lib/python3.11/site-packages/httptools/parser/url_parser.pyx", "path_type": "hardlink", "sha256": "64955466aac80dd873568740eed4eda056f9ef46aff92733232876a006572b33", "sha256_in_prefix": "64955466aac80dd873568740eed4eda056f9ef46aff92733232876a006572b33", "size_in_bytes": 3758}], "paths_version": 1}, "requested_spec": "None", "sha256": "df06d65646c3c04b7c6930b7789de2f7cd29d7ce5ceb3d267e8b36555b229353", "size": 86606, "subdir": "osx-64", "timestamp": 1756754683000, "url": "https://conda.anaconda.org/conda-forge/osx-64/httptools-0.6.4-py311h13e5629_1.conda", "version": "0.6.4"}