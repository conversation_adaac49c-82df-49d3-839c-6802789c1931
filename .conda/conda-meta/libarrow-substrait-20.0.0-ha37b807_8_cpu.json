{"build": "ha37b807_8_cpu", "build_number": 8, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.14", "libabseil * cxx17*", "libabseil >=20250127.1,<20250128.0a0", "libarrow 20.0.0 h7601d43_8_cpu", "libarrow-acero 20.0.0 hdc53af8_8_cpu", "libarrow-dataset 20.0.0 hdc53af8_8_cpu", "libcxx >=18", "libprotobuf >=5.29.3,<5.29.4.0a0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libarrow-substrait-20.0.0-ha37b807_8_cpu", "files": ["lib/cmake/ArrowSubstrait/ArrowSubstraitConfig.cmake", "lib/cmake/ArrowSubstrait/ArrowSubstraitConfigVersion.cmake", "lib/cmake/ArrowSubstrait/ArrowSubstraitTargets-release.cmake", "lib/cmake/ArrowSubstrait/ArrowSubstraitTargets.cmake", "lib/libarrow_substrait.2000.0.0.dylib", "lib/libarrow_substrait.2000.dylib", "lib/libarrow_substrait.dylib", "lib/pkgconfig/arrow-substrait.pc"], "fn": "libarrow-substrait-20.0.0-ha37b807_8_cpu.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libarrow-substrait-20.0.0-ha37b807_8_cpu", "type": 1}, "md5": "7e3b840ff61f7a451deff498f7344ddb", "name": "libarrow-substrait", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libarrow-substrait-20.0.0-ha37b807_8_cpu.conda", "paths_data": {"paths": [{"_path": "lib/cmake/ArrowSubstrait/ArrowSubstraitConfig.cmake", "path_type": "hardlink", "sha256": "5d2faccc8080c15f44ef953965e7158e5e06a7a882caf87be3fc591ef5836c71", "sha256_in_prefix": "5d2faccc8080c15f44ef953965e7158e5e06a7a882caf87be3fc591ef5836c71", "size_in_bytes": 2443}, {"_path": "lib/cmake/ArrowSubstrait/ArrowSubstraitConfigVersion.cmake", "path_type": "hardlink", "sha256": "4eb556774449e5062cea02eaaab6b824572fe8dadcdfb258fc6e518fac98006b", "sha256_in_prefix": "4eb556774449e5062cea02eaaab6b824572fe8dadcdfb258fc6e518fac98006b", "size_in_bytes": 2765}, {"_path": "lib/cmake/ArrowSubstrait/ArrowSubstraitTargets-release.cmake", "path_type": "hardlink", "sha256": "7cdc08a03077e54e74b66f0ba6c79a0c2fc723616e0066af6a9d503091850b38", "sha256_in_prefix": "7cdc08a03077e54e74b66f0ba6c79a0c2fc723616e0066af6a9d503091850b38", "size_in_bytes": 1022}, {"_path": "lib/cmake/ArrowSubstrait/ArrowSubstraitTargets.cmake", "path_type": "hardlink", "sha256": "c9b856c18035fc11b64d181510e6d7110f3ede2f9f21a28c6afd1162e2c62117", "sha256_in_prefix": "c9b856c18035fc11b64d181510e6d7110f3ede2f9f21a28c6afd1162e2c62117", "size_in_bytes": 4288}, {"_path": "lib/libarrow_substrait.2000.0.0.dylib", "path_type": "hardlink", "sha256": "ce9c14ec15ddbff35a2fb3d4f42dd1e070dd50f7c9b95f157e60d3fd9794fbbd", "sha256_in_prefix": "ce9c14ec15ddbff35a2fb3d4f42dd1e070dd50f7c9b95f157e60d3fd9794fbbd", "size_in_bytes": 2168224}, {"_path": "lib/libarrow_substrait.2000.dylib", "path_type": "softlink", "sha256": "ce9c14ec15ddbff35a2fb3d4f42dd1e070dd50f7c9b95f157e60d3fd9794fbbd", "size_in_bytes": 2168224}, {"_path": "lib/libarrow_substrait.dylib", "path_type": "softlink", "sha256": "ce9c14ec15ddbff35a2fb3d4f42dd1e070dd50f7c9b95f157e60d3fd9794fbbd", "size_in_bytes": 2168224}, {"_path": "lib/pkgconfig/arrow-substrait.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/apache-arrow_1751095169775/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "ca39679289b92f95313dd06a450e73e060421fb9c389122385b6758a90964ad1", "sha256_in_prefix": "baae7df82bc685b357fb82393390efd07f3a61d37d0867a31d02e162f9ffc1f5", "size_in_bytes": 1286}], "paths_version": 1}, "requested_spec": "None", "sha256": "5e82f0e338b238b59a8f78581c74a3821606820bc32b5f43fd5cba871fc0ea3b", "size": 465030, "subdir": "osx-64", "timestamp": 1751098242000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libarrow-substrait-20.0.0-ha37b807_8_cpu.conda", "version": "20.0.0"}