{"build": "h7a4e982_6", "build_number": 6, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13", "aws-c-common >=0.12.4,<0.12.5.0a0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/aws-c-compression-0.3.1-h7a4e982_6", "files": ["include/aws/compression/compression.h", "include/aws/compression/exports.h", "include/aws/compression/huffman.h", "lib/cmake/aws-c-compression/aws-c-compression-config.cmake", "lib/cmake/aws-c-compression/shared/aws-c-compression-targets-release.cmake", "lib/cmake/aws-c-compression/shared/aws-c-compression-targets.cmake", "lib/libaws-c-compression.1.0.0.dylib", "lib/libaws-c-compression.dylib"], "fn": "aws-c-compression-0.3.1-h7a4e982_6.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/aws-c-compression-0.3.1-h7a4e982_6", "type": 1}, "md5": "6a4b25acf73532bbec863c2c2ae45842", "name": "aws-c-compression", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/aws-c-compression-0.3.1-h7a4e982_6.conda", "paths_data": {"paths": [{"_path": "include/aws/compression/compression.h", "path_type": "hardlink", "sha256": "4a7e0eba0e35676fbacfb6c1d08ada087a21ec4b3a4245a75c95b72ad23e8aa3", "sha256_in_prefix": "4a7e0eba0e35676fbacfb6c1d08ada087a21ec4b3a4245a75c95b72ad23e8aa3", "size_in_bytes": 1128}, {"_path": "include/aws/compression/exports.h", "path_type": "hardlink", "sha256": "8851c4f63053a1d90b17eb29ea4bb477cdfcb315090d06933b18e9710b85dba8", "sha256_in_prefix": "8851c4f63053a1d90b17eb29ea4bb477cdfcb315090d06933b18e9710b85dba8", "size_in_bytes": 1120}, {"_path": "include/aws/compression/huffman.h", "path_type": "hardlink", "sha256": "a0ae03aa45aa8049d704105d6ccaf52342e94f279c04b5616a9416e5a408c43e", "sha256_in_prefix": "a0ae03aa45aa8049d704105d6ccaf52342e94f279c04b5616a9416e5a408c43e", "size_in_bytes": 4752}, {"_path": "lib/cmake/aws-c-compression/aws-c-compression-config.cmake", "path_type": "hardlink", "sha256": "0b355e8406ff62a1379aa02bce03381d4489a1df03ceea0ef4ff5d6aa9e54e59", "sha256_in_prefix": "0b355e8406ff62a1379aa02bce03381d4489a1df03ceea0ef4ff5d6aa9e54e59", "size_in_bytes": 576}, {"_path": "lib/cmake/aws-c-compression/shared/aws-c-compression-targets-release.cmake", "path_type": "hardlink", "sha256": "7048f23514265178f848a1828a5aadccf359ee53dfe306a7144f9519d28f9aec", "sha256_in_prefix": "7048f23514265178f848a1828a5aadccf359ee53dfe306a7144f9519d28f9aec", "size_in_bytes": 943}, {"_path": "lib/cmake/aws-c-compression/shared/aws-c-compression-targets.cmake", "path_type": "hardlink", "sha256": "6255354084b075257224fa1deb2c6da7286272edd51a795f28dfc2bf560baf9f", "sha256_in_prefix": "6255354084b075257224fa1deb2c6da7286272edd51a795f28dfc2bf560baf9f", "size_in_bytes": 4343}, {"_path": "lib/libaws-c-compression.1.0.0.dylib", "path_type": "hardlink", "sha256": "42dcb8c49f00f16402e7926f5a7d3bae9f836b951d5d84f7302d94cfb9e29c7b", "sha256_in_prefix": "42dcb8c49f00f16402e7926f5a7d3bae9f836b951d5d84f7302d94cfb9e29c7b", "size_in_bytes": 20032}, {"_path": "lib/libaws-c-compression.dylib", "path_type": "softlink", "sha256": "42dcb8c49f00f16402e7926f5a7d3bae9f836b951d5d84f7302d94cfb9e29c7b", "size_in_bytes": 32}], "paths_version": 1}, "requested_spec": "None", "sha256": "2029ee55f83e1952ea0c220b0dd30f1b6f9e9411146c659489fcfd6a29af2ddf", "size": 21116, "subdir": "osx-64", "timestamp": 1752240021000, "url": "https://conda.anaconda.org/conda-forge/osx-64/aws-c-compression-0.3.1-h7a4e982_6.conda", "version": "0.3.1"}