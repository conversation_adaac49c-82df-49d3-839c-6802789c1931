{"build": "py311h62e9434_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["python", "cffi >=1.11", "zstd >=1.5.7,<1.5.8.0a0", "__osx >=10.13", "python_abi 3.11.* *_cp311", "zstd >=1.5.7,<1.6.0a0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/zstandard-0.25.0-py311h62e9434_0", "files": ["lib/python3.11/site-packages/zstandard/__init__.py", "lib/python3.11/site-packages/zstandard/__init__.pyi", "lib/python3.11/site-packages/zstandard/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/zstandard/__pycache__/backend_cffi.cpython-311.pyc", "lib/python3.11/site-packages/zstandard/_cffi.cpython-311-darwin.so", "lib/python3.11/site-packages/zstandard/backend_c.cpython-311-darwin.so", "lib/python3.11/site-packages/zstandard/backend_cffi.py", "lib/python3.11/site-packages/zstandard/py.typed", "lib/python3.11/site-packages/zstandard-0.25.0-py3.11.egg-info/PKG-INFO", "lib/python3.11/site-packages/zstandard-0.25.0-py3.11.egg-info/SOURCES.txt", "lib/python3.11/site-packages/zstandard-0.25.0-py3.11.egg-info/dependency_links.txt", "lib/python3.11/site-packages/zstandard-0.25.0-py3.11.egg-info/requires.txt", "lib/python3.11/site-packages/zstandard-0.25.0-py3.11.egg-info/top_level.txt"], "fn": "zstandard-0.25.0-py311h62e9434_0.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/zstandard-0.25.0-py311h62e9434_0", "type": 1}, "md5": "5425495af6b0b010230320d618022f20", "name": "zstandard", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/zstandard-0.25.0-py311h62e9434_0.conda", "paths_data": {"paths": [{"_path": "lib/python3.11/site-packages/zstandard/__init__.py", "path_type": "hardlink", "sha256": "5dd909e0b60a946a0b08deaf57545d17dc044f5df4040beec55f7ef6c6fecd11", "sha256_in_prefix": "5dd909e0b60a946a0b08deaf57545d17dc044f5df4040beec55f7ef6c6fecd11", "size_in_bytes": 7235}, {"_path": "lib/python3.11/site-packages/zstandard/__init__.pyi", "path_type": "hardlink", "sha256": "8f9f3d95bde368d7483c73a28c24fadc14df69673f145703346d3d327ac8a030", "sha256_in_prefix": "8f9f3d95bde368d7483c73a28c24fadc14df69673f145703346d3d327ac8a030", "size_in_bytes": 13973}, {"_path": "lib/python3.11/site-packages/zstandard/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "edeb043460a85df46b9a4bfa1eafbd9998b311d6f4d4faa151d480fdc4491957", "sha256_in_prefix": "edeb043460a85df46b9a4bfa1eafbd9998b311d6f4d4faa151d480fdc4491957", "size_in_bytes": 7286}, {"_path": "lib/python3.11/site-packages/zstandard/__pycache__/backend_cffi.cpython-311.pyc", "path_type": "hardlink", "sha256": "078f6bd0bcfc3ea3d5aca9091af4258944196a8b08872e02c67a553b4dd5c142", "sha256_in_prefix": "078f6bd0bcfc3ea3d5aca9091af4258944196a8b08872e02c67a553b4dd5c142", "size_in_bytes": 179579}, {"_path": "lib/python3.11/site-packages/zstandard/_cffi.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "125704f5d4b2e9e727fb33cbcab5cea9244a891bcb18fc0e2fa2dac2424e2537", "sha256_in_prefix": "125704f5d4b2e9e727fb33cbcab5cea9244a891bcb18fc0e2fa2dac2424e2537", "size_in_bytes": 912672}, {"_path": "lib/python3.11/site-packages/zstandard/backend_c.cpython-311-darwin.so", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/bld/rattler-build_zstandard_1757930157/host_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_pl", "sha256": "c9ebe05b217d04a8c3f8ebabd18210edeacb19c0a1f71bf5639509b984111e55", "sha256_in_prefix": "98b1d4753d5bb23eec55f1d0f5f92c79245a4293e1d33a537d89fddd5982c5f8", "size_in_bytes": 107456}, {"_path": "lib/python3.11/site-packages/zstandard/backend_cffi.py", "path_type": "hardlink", "sha256": "0b268ae515d511531f1c6e76068123efb8f725b4f62484aa11e887fadc1975ce", "sha256_in_prefix": "0b268ae515d511531f1c6e76068123efb8f725b4f62484aa11e887fadc1975ce", "size_in_bytes": 152627}, {"_path": "lib/python3.11/site-packages/zstandard/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/zstandard-0.25.0-py3.11.egg-info/PKG-INFO", "path_type": "hardlink", "sha256": "03d37736b26b9ae48451dd17026d93dffbd252fb708c1a2a9ae1be0c0966de92", "sha256_in_prefix": "03d37736b26b9ae48451dd17026d93dffbd252fb708c1a2a9ae1be0c0966de92", "size_in_bytes": 3275}, {"_path": "lib/python3.11/site-packages/zstandard-0.25.0-py3.11.egg-info/SOURCES.txt", "path_type": "hardlink", "sha256": "2902c4078db8dc1081804d32ed07d8472efcde5971844f7a10624f60a8224299", "sha256_in_prefix": "2902c4078db8dc1081804d32ed07d8472efcde5971844f7a10624f60a8224299", "size_in_bytes": 2739}, {"_path": "lib/python3.11/site-packages/zstandard-0.25.0-py3.11.egg-info/dependency_links.txt", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "lib/python3.11/site-packages/zstandard-0.25.0-py3.11.egg-info/requires.txt", "path_type": "hardlink", "sha256": "fe92236aa62893134f8bc2970b6fbb1bd3cf028a6975c87909d831b7a8a7ae06", "sha256_in_prefix": "fe92236aa62893134f8bc2970b6fbb1bd3cf028a6975c87909d831b7a8a7ae06", "size_in_bytes": 187}, {"_path": "lib/python3.11/site-packages/zstandard-0.25.0-py3.11.egg-info/top_level.txt", "path_type": "hardlink", "sha256": "27ec23f78a4f69d6388a915a6a7ad8065acc6e53927a01184bca3f2dda20ae95", "sha256_in_prefix": "27ec23f78a4f69d6388a915a6a7ad8065acc6e53927a01184bca3f2dda20ae95", "size_in_bytes": 10}], "paths_version": 1}, "requested_spec": "None", "sha256": "be241ea3ca603d68654beeab4c991c225c9361378a107f72c2433ddfdff88132", "size": 462903, "subdir": "osx-64", "timestamp": 1757930157000, "url": "https://conda.anaconda.org/conda-forge/osx-64/zstandard-0.25.0-py311h62e9434_0.conda", "version": "0.25.0"}