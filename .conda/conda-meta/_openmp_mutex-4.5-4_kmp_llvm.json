{"build": "4_kmp_llvm", "build_number": 4, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["llvm-openmp >=9.0.1"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/_openmp_mutex-4.5-4_kmp_llvm", "files": ["lib/libgomp.dylib.1"], "fn": "_openmp_mutex-4.5-4_kmp_llvm.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/_openmp_mutex-4.5-4_kmp_llvm", "type": 1}, "md5": "f817d8c3ef180901aedbb9fe68c81252", "name": "_openmp_mutex", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/_openmp_mutex-4.5-4_kmp_llvm.conda", "paths_data": {"paths": [{"_path": "lib/libgomp.dylib.1", "path_type": "softlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}], "paths_version": 1}, "requested_spec": "None", "sha256": "eb6dae227f5d7e870d142782296b67f143b4e33019cff00274a18d38bd6e79db", "size": 8193, "subdir": "osx-64", "timestamp": 1756424769000, "url": "https://conda.anaconda.org/conda-forge/osx-64/_openmp_mutex-4.5-4_kmp_llvm.conda", "version": "4.5"}