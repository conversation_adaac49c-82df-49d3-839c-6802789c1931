{"build": "py311h179db11_2", "build_number": 2, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["python", "__osx >=10.13", "python_abi 3.11.* *_cp311"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/websockets-15.0.1-py311h179db11_2", "files": ["bin/websockets", "etc/conda/test-files/websockets/1/run_test.py", "etc/conda/test-files/websockets/1/src/tests/__init__.py", "etc/conda/test-files/websockets/1/src/tests/asyncio/__init__.py", "etc/conda/test-files/websockets/1/src/tests/asyncio/connection.py", "etc/conda/test-files/websockets/1/src/tests/asyncio/server.py", "etc/conda/test-files/websockets/1/src/tests/asyncio/test_client.py", "etc/conda/test-files/websockets/1/src/tests/asyncio/test_connection.py", "etc/conda/test-files/websockets/1/src/tests/asyncio/test_messages.py", "etc/conda/test-files/websockets/1/src/tests/asyncio/test_router.py", "etc/conda/test-files/websockets/1/src/tests/asyncio/test_server.py", "etc/conda/test-files/websockets/1/src/tests/asyncio/utils.py", "etc/conda/test-files/websockets/1/src/tests/extensions/__init__.py", "etc/conda/test-files/websockets/1/src/tests/extensions/test_base.py", "etc/conda/test-files/websockets/1/src/tests/extensions/test_permessage_deflate.py", "etc/conda/test-files/websockets/1/src/tests/extensions/utils.py", "etc/conda/test-files/websockets/1/src/tests/legacy/__init__.py", "etc/conda/test-files/websockets/1/src/tests/legacy/test_auth.py", "etc/conda/test-files/websockets/1/src/tests/legacy/test_client_server.py", "etc/conda/test-files/websockets/1/src/tests/legacy/test_exceptions.py", "etc/conda/test-files/websockets/1/src/tests/legacy/test_framing.py", "etc/conda/test-files/websockets/1/src/tests/legacy/test_handshake.py", "etc/conda/test-files/websockets/1/src/tests/legacy/test_http.py", "etc/conda/test-files/websockets/1/src/tests/legacy/test_protocol.py", "etc/conda/test-files/websockets/1/src/tests/legacy/utils.py", "etc/conda/test-files/websockets/1/src/tests/maxi_cov.py", "etc/conda/test-files/websockets/1/src/tests/protocol.py", "etc/conda/test-files/websockets/1/src/tests/proxy.py", "etc/conda/test-files/websockets/1/src/tests/requirements.txt", "etc/conda/test-files/websockets/1/src/tests/sync/__init__.py", "etc/conda/test-files/websockets/1/src/tests/sync/connection.py", "etc/conda/test-files/websockets/1/src/tests/sync/server.py", "etc/conda/test-files/websockets/1/src/tests/sync/test_client.py", "etc/conda/test-files/websockets/1/src/tests/sync/test_connection.py", "etc/conda/test-files/websockets/1/src/tests/sync/test_messages.py", "etc/conda/test-files/websockets/1/src/tests/sync/test_router.py", "etc/conda/test-files/websockets/1/src/tests/sync/test_server.py", "etc/conda/test-files/websockets/1/src/tests/sync/test_utils.py", "etc/conda/test-files/websockets/1/src/tests/sync/utils.py", "etc/conda/test-files/websockets/1/src/tests/test_auth.py", "etc/conda/test-files/websockets/1/src/tests/test_cli.py", "etc/conda/test-files/websockets/1/src/tests/test_client.py", "etc/conda/test-files/websockets/1/src/tests/test_connection.py", "etc/conda/test-files/websockets/1/src/tests/test_datastructures.py", "etc/conda/test-files/websockets/1/src/tests/test_exceptions.py", "etc/conda/test-files/websockets/1/src/tests/test_exports.py", "etc/conda/test-files/websockets/1/src/tests/test_frames.py", "etc/conda/test-files/websockets/1/src/tests/test_headers.py", "etc/conda/test-files/websockets/1/src/tests/test_http.py", "etc/conda/test-files/websockets/1/src/tests/test_http11.py", "etc/conda/test-files/websockets/1/src/tests/test_imports.py", "etc/conda/test-files/websockets/1/src/tests/test_localhost.cnf", "etc/conda/test-files/websockets/1/src/tests/test_localhost.pem", "etc/conda/test-files/websockets/1/src/tests/test_protocol.py", "etc/conda/test-files/websockets/1/src/tests/test_server.py", "etc/conda/test-files/websockets/1/src/tests/test_streams.py", "etc/conda/test-files/websockets/1/src/tests/test_uri.py", "etc/conda/test-files/websockets/1/src/tests/test_utils.py", "etc/conda/test-files/websockets/1/src/tests/utils.py", "lib/python3.11/site-packages/websockets/__init__.py", "lib/python3.11/site-packages/websockets/__main__.py", "lib/python3.11/site-packages/websockets/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/auth.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/cli.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/client.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/connection.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/datastructures.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/exceptions.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/frames.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/headers.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/http.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/http11.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/imports.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/protocol.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/server.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/streams.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/typing.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/uri.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/websockets/__pycache__/version.cpython-311.pyc", "lib/python3.11/site-packages/websockets/asyncio/__init__.py", "lib/python3.11/site-packages/websockets/asyncio/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/websockets/asyncio/__pycache__/async_timeout.cpython-311.pyc", "lib/python3.11/site-packages/websockets/asyncio/__pycache__/client.cpython-311.pyc", "lib/python3.11/site-packages/websockets/asyncio/__pycache__/compatibility.cpython-311.pyc", "lib/python3.11/site-packages/websockets/asyncio/__pycache__/connection.cpython-311.pyc", "lib/python3.11/site-packages/websockets/asyncio/__pycache__/messages.cpython-311.pyc", "lib/python3.11/site-packages/websockets/asyncio/__pycache__/router.cpython-311.pyc", "lib/python3.11/site-packages/websockets/asyncio/__pycache__/server.cpython-311.pyc", "lib/python3.11/site-packages/websockets/asyncio/async_timeout.py", "lib/python3.11/site-packages/websockets/asyncio/client.py", "lib/python3.11/site-packages/websockets/asyncio/compatibility.py", "lib/python3.11/site-packages/websockets/asyncio/connection.py", "lib/python3.11/site-packages/websockets/asyncio/messages.py", "lib/python3.11/site-packages/websockets/asyncio/router.py", "lib/python3.11/site-packages/websockets/asyncio/server.py", "lib/python3.11/site-packages/websockets/auth.py", "lib/python3.11/site-packages/websockets/cli.py", "lib/python3.11/site-packages/websockets/client.py", "lib/python3.11/site-packages/websockets/connection.py", "lib/python3.11/site-packages/websockets/datastructures.py", "lib/python3.11/site-packages/websockets/exceptions.py", "lib/python3.11/site-packages/websockets/extensions/__init__.py", "lib/python3.11/site-packages/websockets/extensions/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/websockets/extensions/__pycache__/base.cpython-311.pyc", "lib/python3.11/site-packages/websockets/extensions/__pycache__/permessage_deflate.cpython-311.pyc", "lib/python3.11/site-packages/websockets/extensions/base.py", "lib/python3.11/site-packages/websockets/extensions/permessage_deflate.py", "lib/python3.11/site-packages/websockets/frames.py", "lib/python3.11/site-packages/websockets/headers.py", "lib/python3.11/site-packages/websockets/http.py", "lib/python3.11/site-packages/websockets/http11.py", "lib/python3.11/site-packages/websockets/imports.py", "lib/python3.11/site-packages/websockets/legacy/__init__.py", "lib/python3.11/site-packages/websockets/legacy/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/websockets/legacy/__pycache__/auth.cpython-311.pyc", "lib/python3.11/site-packages/websockets/legacy/__pycache__/client.cpython-311.pyc", "lib/python3.11/site-packages/websockets/legacy/__pycache__/exceptions.cpython-311.pyc", "lib/python3.11/site-packages/websockets/legacy/__pycache__/framing.cpython-311.pyc", "lib/python3.11/site-packages/websockets/legacy/__pycache__/handshake.cpython-311.pyc", "lib/python3.11/site-packages/websockets/legacy/__pycache__/http.cpython-311.pyc", "lib/python3.11/site-packages/websockets/legacy/__pycache__/protocol.cpython-311.pyc", "lib/python3.11/site-packages/websockets/legacy/__pycache__/server.cpython-311.pyc", "lib/python3.11/site-packages/websockets/legacy/auth.py", "lib/python3.11/site-packages/websockets/legacy/client.py", "lib/python3.11/site-packages/websockets/legacy/exceptions.py", "lib/python3.11/site-packages/websockets/legacy/framing.py", "lib/python3.11/site-packages/websockets/legacy/handshake.py", "lib/python3.11/site-packages/websockets/legacy/http.py", "lib/python3.11/site-packages/websockets/legacy/protocol.py", "lib/python3.11/site-packages/websockets/legacy/server.py", "lib/python3.11/site-packages/websockets/protocol.py", "lib/python3.11/site-packages/websockets/py.typed", "lib/python3.11/site-packages/websockets/server.py", "lib/python3.11/site-packages/websockets/speedups.c", "lib/python3.11/site-packages/websockets/speedups.cpython-311-darwin.so", "lib/python3.11/site-packages/websockets/speedups.pyi", "lib/python3.11/site-packages/websockets/streams.py", "lib/python3.11/site-packages/websockets/sync/__init__.py", "lib/python3.11/site-packages/websockets/sync/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/websockets/sync/__pycache__/client.cpython-311.pyc", "lib/python3.11/site-packages/websockets/sync/__pycache__/connection.cpython-311.pyc", "lib/python3.11/site-packages/websockets/sync/__pycache__/messages.cpython-311.pyc", "lib/python3.11/site-packages/websockets/sync/__pycache__/router.cpython-311.pyc", "lib/python3.11/site-packages/websockets/sync/__pycache__/server.cpython-311.pyc", "lib/python3.11/site-packages/websockets/sync/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/websockets/sync/client.py", "lib/python3.11/site-packages/websockets/sync/connection.py", "lib/python3.11/site-packages/websockets/sync/messages.py", "lib/python3.11/site-packages/websockets/sync/router.py", "lib/python3.11/site-packages/websockets/sync/server.py", "lib/python3.11/site-packages/websockets/sync/utils.py", "lib/python3.11/site-packages/websockets/typing.py", "lib/python3.11/site-packages/websockets/uri.py", "lib/python3.11/site-packages/websockets/utils.py", "lib/python3.11/site-packages/websockets/version.py", "lib/python3.11/site-packages/websockets-15.0.1.dist-info/INSTALLER", "lib/python3.11/site-packages/websockets-15.0.1.dist-info/METADATA", "lib/python3.11/site-packages/websockets-15.0.1.dist-info/RECORD", "lib/python3.11/site-packages/websockets-15.0.1.dist-info/REQUESTED", "lib/python3.11/site-packages/websockets-15.0.1.dist-info/WHEEL", "lib/python3.11/site-packages/websockets-15.0.1.dist-info/direct_url.json", "lib/python3.11/site-packages/websockets-15.0.1.dist-info/entry_points.txt", "lib/python3.11/site-packages/websockets-15.0.1.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/websockets-15.0.1.dist-info/top_level.txt"], "fn": "websockets-15.0.1-py311h179db11_2.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/websockets-15.0.1-py311h179db11_2", "type": 1}, "md5": "24bb97dc90bf7dce2a368caf823eed95", "name": "websockets", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/websockets-15.0.1-py311h179db11_2.conda", "paths_data": {"paths": [{"_path": "bin/websockets", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/bld/rattler-build_websockets_1756476420/host_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_p", "sha256": "5dcf67b0a8743080f060d9a897d6b5c30287222a59c2c0fca161567b9a344c32", "sha256_in_prefix": "0214692f52c0da4ad5faf2ec5345e94fcf6af8d83a6c5bf53ac3b0326872ad5f", "size_in_bytes": 436}, {"_path": "etc/conda/test-files/websockets/1/run_test.py", "path_type": "hardlink", "sha256": "6712671abc1cb4016fbdec2c08eb36df6de0a0718fd3934bce057ce1b782faf9", "sha256_in_prefix": "6712671abc1cb4016fbdec2c08eb36df6de0a0718fd3934bce057ce1b782faf9", "size_in_bytes": 845}, {"_path": "etc/conda/test-files/websockets/1/src/tests/__init__.py", "path_type": "hardlink", "sha256": "969b2c7a50363425dbdcbbff5864e1a29549de860226210ed480cca1944ed3d2", "sha256_in_prefix": "969b2c7a50363425dbdcbbff5864e1a29549de860226210ed480cca1944ed3d2", "size_in_bytes": 358}, {"_path": "etc/conda/test-files/websockets/1/src/tests/asyncio/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "etc/conda/test-files/websockets/1/src/tests/asyncio/connection.py", "path_type": "hardlink", "sha256": "2352d161e249d278f6437cc09e2e94d47c2e51994d430dde3b8e4d692adb196f", "sha256_in_prefix": "2352d161e249d278f6437cc09e2e94d47c2e51994d430dde3b8e4d692adb196f", "size_in_bytes": 3323}, {"_path": "etc/conda/test-files/websockets/1/src/tests/asyncio/server.py", "path_type": "hardlink", "sha256": "01ad0997447743e556c67c8dc867235449332b987e6e34ed3c47a702edc14dcf", "sha256_in_prefix": "01ad0997447743e556c67c8dc867235449332b987e6e34ed3c47a702edc14dcf", "size_in_bytes": 1323}, {"_path": "etc/conda/test-files/websockets/1/src/tests/asyncio/test_client.py", "path_type": "hardlink", "sha256": "702f416c4984ee4b95811cb1edb0eafe3459c802cd0fd37725fbc40ed20451c8", "sha256_in_prefix": "702f416c4984ee4b95811cb1edb0eafe3459c802cd0fd37725fbc40ed20451c8", "size_in_bytes": 44125}, {"_path": "etc/conda/test-files/websockets/1/src/tests/asyncio/test_connection.py", "path_type": "hardlink", "sha256": "10e77210078180d8ae549547d5c58078c3aeda00040b5376d7c4a62695a4b515", "sha256_in_prefix": "10e77210078180d8ae549547d5c58078c3aeda00040b5376d7c4a62695a4b515", "size_in_bytes": 57271}, {"_path": "etc/conda/test-files/websockets/1/src/tests/asyncio/test_messages.py", "path_type": "hardlink", "sha256": "1068a0ab479462ad4fee477d4fb919c03b2297bc0c279841d9971d84b576adb5", "sha256_in_prefix": "1068a0ab479462ad4fee477d4fb919c03b2297bc0c279841d9971d84b576adb5", "size_in_bytes": 24786}, {"_path": "etc/conda/test-files/websockets/1/src/tests/asyncio/test_router.py", "path_type": "hardlink", "sha256": "64e2d0140d84c26b8f4e1e934a37d7fa39f1bbeeef1ae66abb136e006ae85648", "sha256_in_prefix": "64e2d0140d84c26b8f4e1e934a37d7fa39f1bbeeef1ae66abb136e006ae85648", "size_in_bytes": 8243}, {"_path": "etc/conda/test-files/websockets/1/src/tests/asyncio/test_server.py", "path_type": "hardlink", "sha256": "8de5955b72697a0e591c812837a2fffece56ae6865c1f5b401f6ce2245bc775b", "sha256_in_prefix": "8de5955b72697a0e591c812837a2fffece56ae6865c1f5b401f6ce2245bc775b", "size_in_bytes": 33938}, {"_path": "etc/conda/test-files/websockets/1/src/tests/asyncio/utils.py", "path_type": "hardlink", "sha256": "9a74ebba0a6b148ae9a1291ff9c7d13be7e9ed86064b061dddea34e03a5d8c20", "sha256_in_prefix": "9a74ebba0a6b148ae9a1291ff9c7d13be7e9ed86064b061dddea34e03a5d8c20", "size_in_bytes": 130}, {"_path": "etc/conda/test-files/websockets/1/src/tests/extensions/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "etc/conda/test-files/websockets/1/src/tests/extensions/test_base.py", "path_type": "hardlink", "sha256": "640d90613c919187370b42ef7b625f97aa0d07283c39236335a55eb1cb43a989", "sha256_in_prefix": "640d90613c919187370b42ef7b625f97aa0d07283c39236335a55eb1cb43a989", "size_in_bytes": 1011}, {"_path": "etc/conda/test-files/websockets/1/src/tests/extensions/test_permessage_deflate.py", "path_type": "hardlink", "sha256": "076b5dcdcccbe391d9af028011d498e9de8e9d812756bb4f47f3a0127566f496", "sha256_in_prefix": "076b5dcdcccbe391d9af028011d498e9de8e9d812756bb4f47f3a0127566f496", "size_in_bytes": 34198}, {"_path": "etc/conda/test-files/websockets/1/src/tests/extensions/utils.py", "path_type": "hardlink", "sha256": "20fd5617615cb4dc2512eb0773acf765bcdfcd462f7dde6cf58bb98c53a6f816", "sha256_in_prefix": "20fd5617615cb4dc2512eb0773acf765bcdfcd462f7dde6cf58bb98c53a6f816", "size_in_bytes": 2549}, {"_path": "etc/conda/test-files/websockets/1/src/tests/legacy/__init__.py", "path_type": "hardlink", "sha256": "008e1331c1a7bf8be3f1559e72f776c91312de8db820d7928d355d79fb901950", "sha256_in_prefix": "008e1331c1a7bf8be3f1559e72f776c91312de8db820d7928d355d79fb901950", "size_in_bytes": 259}, {"_path": "etc/conda/test-files/websockets/1/src/tests/legacy/test_auth.py", "path_type": "hardlink", "sha256": "4f3b7a139a5e9b166184eef94600922aed964ee9b1a0b6a042ef5cb0b9ee3560", "sha256_in_prefix": "4f3b7a139a5e9b166184eef94600922aed964ee9b1a0b6a042ef5cb0b9ee3560", "size_in_bytes": 7950}, {"_path": "etc/conda/test-files/websockets/1/src/tests/legacy/test_client_server.py", "path_type": "hardlink", "sha256": "0706a1b0d0c4804b79e47af9f2d73d143b2358e0b67bc709fd71ee037683bf3d", "sha256_in_prefix": "0706a1b0d0c4804b79e47af9f2d73d143b2358e0b67bc709fd71ee037683bf3d", "size_in_bytes": 63594}, {"_path": "etc/conda/test-files/websockets/1/src/tests/legacy/test_exceptions.py", "path_type": "hardlink", "sha256": "9f0b0c7c23431c8939bd83e10e07fe7666d0d9a2efd793eb046780a00aaa025f", "sha256_in_prefix": "9f0b0c7c23431c8939bd83e10e07fe7666d0d9a2efd793eb046780a00aaa025f", "size_in_bytes": 757}, {"_path": "etc/conda/test-files/websockets/1/src/tests/legacy/test_framing.py", "path_type": "hardlink", "sha256": "d6ce93679f5ce942169dab6881516d5d3461658b9363349026e6f150ec08ed2d", "sha256_in_prefix": "d6ce93679f5ce942169dab6881516d5d3461658b9363349026e6f150ec08ed2d", "size_in_bytes": 9025}, {"_path": "etc/conda/test-files/websockets/1/src/tests/legacy/test_handshake.py", "path_type": "hardlink", "sha256": "7d1ea0897011e6e8a9c87adc6489b9854004e55a922648aa7ceb58151bda5cba", "sha256_in_prefix": "7d1ea0897011e6e8a9c87adc6489b9854004e55a922648aa7ceb58151bda5cba", "size_in_bytes": 6887}, {"_path": "etc/conda/test-files/websockets/1/src/tests/legacy/test_http.py", "path_type": "hardlink", "sha256": "5126519016fc0b1e6bacdefdeb44539dc9a92fe20e0fb03963f95aec46ba65f8", "sha256_in_prefix": "5126519016fc0b1e6bacdefdeb44539dc9a92fe20e0fb03963f95aec46ba65f8", "size_in_bytes": 6640}, {"_path": "etc/conda/test-files/websockets/1/src/tests/legacy/test_protocol.py", "path_type": "hardlink", "sha256": "f1fd1d9d76d315460c6162d6276f07f0703f7c905fbe1e8671cc3e5e9312ac0b", "sha256_in_prefix": "f1fd1d9d76d315460c6162d6276f07f0703f7c905fbe1e8671cc3e5e9312ac0b", "size_in_bytes": 66005}, {"_path": "etc/conda/test-files/websockets/1/src/tests/legacy/utils.py", "path_type": "hardlink", "sha256": "7b854f66180a45a32d1a9b877a4c8661c8b5d7550b8be555e05374f70e2e9791", "sha256_in_prefix": "7b854f66180a45a32d1a9b877a4c8661c8b5d7550b8be555e05374f70e2e9791", "size_in_bytes": 2484}, {"_path": "etc/conda/test-files/websockets/1/src/tests/maxi_cov.py", "path_type": "hardlink", "sha256": "ffb8c819129b04112e25d7e976827655257fc6284fbc2ea08ce4bb38101959c0", "sha256_in_prefix": "ffb8c819129b04112e25d7e976827655257fc6284fbc2ea08ce4bb38101959c0", "size_in_bytes": 5123}, {"_path": "etc/conda/test-files/websockets/1/src/tests/protocol.py", "path_type": "hardlink", "sha256": "f8e48cfa7f4109d2ea8bce7952eb3a14a3ace4e15fd16eb2830b68f454acbcbc", "sha256_in_prefix": "f8e48cfa7f4109d2ea8bce7952eb3a14a3ace4e15fd16eb2830b68f454acbcbc", "size_in_bytes": 767}, {"_path": "etc/conda/test-files/websockets/1/src/tests/proxy.py", "path_type": "hardlink", "sha256": "8fbad24b243cc577392e8534d376b2fc32544768966fb72562afa5c373425ad3", "sha256_in_prefix": "8fbad24b243cc577392e8534d376b2fc32544768966fb72562afa5c373425ad3", "size_in_bytes": 4903}, {"_path": "etc/conda/test-files/websockets/1/src/tests/requirements.txt", "path_type": "hardlink", "sha256": "611facc7ed2e568e7502a5284da81ac043f943861227910dd8bcadad6b82fd9c", "sha256_in_prefix": "611facc7ed2e568e7502a5284da81ac043f943861227910dd8bcadad6b82fd9c", "size_in_bytes": 32}, {"_path": "etc/conda/test-files/websockets/1/src/tests/sync/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "etc/conda/test-files/websockets/1/src/tests/sync/connection.py", "path_type": "hardlink", "sha256": "695c5483054d934952991e048c7d20f8ca7e9aa7b68ff32e82408f06eef90177", "sha256_in_prefix": "695c5483054d934952991e048c7d20f8ca7e9aa7b68ff32e82408f06eef90177", "size_in_bytes": 2923}, {"_path": "etc/conda/test-files/websockets/1/src/tests/sync/server.py", "path_type": "hardlink", "sha256": "9dbcb0daf6b8e3edb4c14f9a2fc4e994f506f7a45c3cb22648be9a3764943aea", "sha256_in_prefix": "9dbcb0daf6b8e3edb4c14f9a2fc4e994f506f7a45c3cb22648be9a3764943aea", "size_in_bytes": 2877}, {"_path": "etc/conda/test-files/websockets/1/src/tests/sync/test_client.py", "path_type": "hardlink", "sha256": "36dd34e8954895680701b57dbab38593bbb3f379b1b21fd8ab40d48fef452dca", "sha256_in_prefix": "36dd34e8954895680701b57dbab38593bbb3f379b1b21fd8ab40d48fef452dca", "size_in_bytes": 31142}, {"_path": "etc/conda/test-files/websockets/1/src/tests/sync/test_connection.py", "path_type": "hardlink", "sha256": "12e0f232e52c51defa254f775b3c7abb8f9b764f84946c5975bef5d0a3dce7f8", "sha256_in_prefix": "12e0f232e52c51defa254f775b3c7abb8f9b764f84946c5975bef5d0a3dce7f8", "size_in_bytes": 39657}, {"_path": "etc/conda/test-files/websockets/1/src/tests/sync/test_messages.py", "path_type": "hardlink", "sha256": "ce076bd1a3c4c31694533178cd6c349d5b9c1f771e45805957c425987a68104c", "sha256_in_prefix": "ce076bd1a3c4c31694533178cd6c349d5b9c1f771e45805957c425987a68104c", "size_in_bytes": 23217}, {"_path": "etc/conda/test-files/websockets/1/src/tests/sync/test_router.py", "path_type": "hardlink", "sha256": "089ed15fe449c872bd7509a2cbdca15b83c8ee9c7f2d65c96ae0dd5b2f213705", "sha256_in_prefix": "089ed15fe449c872bd7509a2cbdca15b83c8ee9c7f2d65c96ae0dd5b2f213705", "size_in_bytes": 6956}, {"_path": "etc/conda/test-files/websockets/1/src/tests/sync/test_server.py", "path_type": "hardlink", "sha256": "ed03866b459fc932812d7216b881d6c4dacfb674c73bac96c91726b4a7a4f465", "sha256_in_prefix": "ed03866b459fc932812d7216b881d6c4dacfb674c73bac96c91726b4a7a4f465", "size_in_bytes": 23543}, {"_path": "etc/conda/test-files/websockets/1/src/tests/sync/test_utils.py", "path_type": "hardlink", "sha256": "6a5330222268a54b68fff9fa04c4edec9f88970a8fb42c3c7d0837fafb5b6087", "sha256_in_prefix": "6a5330222268a54b68fff9fa04c4edec9f88970a8fb42c3c7d0837fafb5b6087", "size_in_bytes": 1115}, {"_path": "etc/conda/test-files/websockets/1/src/tests/sync/utils.py", "path_type": "hardlink", "sha256": "42183fbfdba8d20f03f4b1d787dbf5a68ae22a75c5d1eb40c3f4ec5b4bb20706", "sha256_in_prefix": "42183fbfdba8d20f03f4b1d787dbf5a68ae22a75c5d1eb40c3f4ec5b4bb20706", "size_in_bytes": 658}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_auth.py", "path_type": "hardlink", "sha256": "ca0b39375fafe14874a8482732ce64c3e02a30a972ac6a18b3c0bb5ed74a1ce7", "sha256_in_prefix": "ca0b39375fafe14874a8482732ce64c3e02a30a972ac6a18b3c0bb5ed74a1ce7", "size_in_bytes": 571}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_cli.py", "path_type": "hardlink", "sha256": "95ef06758b75408e66fa39860043c7298e639bce4aea2bbec4d977820c5932f5", "sha256_in_prefix": "95ef06758b75408e66fa39860043c7298e639bce4aea2bbec4d977820c5932f5", "size_in_bytes": 4031}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_client.py", "path_type": "hardlink", "sha256": "f0fc96a48eed5a38ca4336b9e5eacf3d7d98322edcbde1e074dc09f7fc5a03e0", "sha256_in_prefix": "f0fc96a48eed5a38ca4336b9e5eacf3d7d98322edcbde1e074dc09f7fc5a03e0", "size_in_bytes": 25392}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_connection.py", "path_type": "hardlink", "sha256": "3dd1a996adf728841521907cf0735d8660100deeb72553748cd479fe35336cd0", "sha256_in_prefix": "3dd1a996adf728841521907cf0735d8660100deeb72553748cd479fe35336cd0", "size_in_bytes": 519}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_datastructures.py", "path_type": "hardlink", "sha256": "dd173b1f4843d86e4718acb9735d7ee5e262bbf3b3082b67250b9b13b99e1196", "sha256_in_prefix": "dd173b1f4843d86e4718acb9735d7ee5e262bbf3b3082b67250b9b13b99e1196", "size_in_bytes": 7218}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_exceptions.py", "path_type": "hardlink", "sha256": "9274460a7b6bb6feb3e7ac4f51a02ffc7ffb538b06a0ae4e69c30cf87b62a1d1", "sha256_in_prefix": "9274460a7b6bb6feb3e7ac4f51a02ffc7ffb538b06a0ae4e69c30cf87b62a1d1", "size_in_bytes": 8516}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_exports.py", "path_type": "hardlink", "sha256": "99e5ae34ef621dc5ee0e33ec171a97c8b05fa456f8d61b0daf20f55c1feedfc7", "sha256_in_prefix": "99e5ae34ef621dc5ee0e33ec171a97c8b05fa456f8d61b0daf20f55c1feedfc7", "size_in_bytes": 1230}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_frames.py", "path_type": "hardlink", "sha256": "79f99714486d7f92de62da92bfaac429581a9ffd4c1715f36dfcc8e733e05b01", "sha256_in_prefix": "79f99714486d7f92de62da92bfaac429581a9ffd4c1715f36dfcc8e733e05b01", "size_in_bytes": 13653}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_headers.py", "path_type": "hardlink", "sha256": "62ab476c8ca2ef71cecfec222f67f3999adc40e8aa58828bc67ab84b81211a0b", "sha256_in_prefix": "62ab476c8ca2ef71cecfec222f67f3999adc40e8aa58828bc67ab84b81211a0b", "size_in_bytes": 9452}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_http.py", "path_type": "hardlink", "sha256": "1205cd8c16ee1c42d5228b793521449956f2519443ca41ce2f50bfa2ee780c41", "sha256_in_prefix": "1205cd8c16ee1c42d5228b793521449956f2519443ca41ce2f50bfa2ee780c41", "size_in_bytes": 584}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_http11.py", "path_type": "hardlink", "sha256": "03874ed17e2d6515f40e4c115560dbd7affd9a7ed44ac2a23fca0023256b0f15", "sha256_in_prefix": "03874ed17e2d6515f40e4c115560dbd7affd9a7ed44ac2a23fca0023256b0f15", "size_in_bytes": 14606}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_imports.py", "path_type": "hardlink", "sha256": "2b539bcc62aed20d0638bcef8d67f4f75d159544da27c93ad6be2eac5ecce2cb", "sha256_in_prefix": "2b539bcc62aed20d0638bcef8d67f4f75d159544da27c93ad6be2eac5ecce2cb", "size_in_bytes": 1717}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_localhost.cnf", "path_type": "hardlink", "sha256": "feba3f35c49c4854bb720ca8d315654a87bc3d74c300e34ee55384fd5ba12765", "sha256_in_prefix": "feba3f35c49c4854bb720ca8d315654a87bc3d74c300e34ee55384fd5ba12765", "size_in_bytes": 283}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_localhost.pem", "path_type": "hardlink", "sha256": "5eeb9e18b0f4c008dfdee42b65b37858df55125fd98bf90a1cc12abb544b6726", "sha256_in_prefix": "5eeb9e18b0f4c008dfdee42b65b37858df55125fd98bf90a1cc12abb544b6726", "size_in_bytes": 2924}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_protocol.py", "path_type": "hardlink", "sha256": "fd54e8bbbbc46d548f26bc6ce38c20bbd569f44f08cb8a1820b3735263c9e658", "sha256_in_prefix": "fd54e8bbbbc46d548f26bc6ce38c20bbd569f44f08cb8a1820b3735263c9e658", "size_in_bytes": 74138}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_server.py", "path_type": "hardlink", "sha256": "83360d0d482b705950e2dc815f78f135c6f91e44dcb6a7f22c25d0310cb1b399", "sha256_in_prefix": "83360d0d482b705950e2dc815f78f135c6f91e44dcb6a7f22c25d0310cb1b399", "size_in_bytes": 35950}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_streams.py", "path_type": "hardlink", "sha256": "3542b198b5777e7df6373477e8be7d5164d034a4308a25bb0a3111c7f48ad52f", "sha256_in_prefix": "3542b198b5777e7df6373477e8be7d5164d034a4308a25bb0a3111c7f48ad52f", "size_in_bytes": 6055}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_uri.py", "path_type": "hardlink", "sha256": "d70ddbf9ad6019f0eec1be82217cad033a4473ed90c5650be33acf68db090f92", "sha256_in_prefix": "d70ddbf9ad6019f0eec1be82217cad033a4473ed90c5650be33acf68db090f92", "size_in_bytes": 7034}, {"_path": "etc/conda/test-files/websockets/1/src/tests/test_utils.py", "path_type": "hardlink", "sha256": "8d17469e01d1c00bfda0f03d34ed13bd93aa1df251b15675c3230b366633d4fe", "sha256_in_prefix": "8d17469e01d1c00bfda0f03d34ed13bd93aa1df251b15675c3230b366633d4fe", "size_in_bytes": 3676}, {"_path": "etc/conda/test-files/websockets/1/src/tests/utils.py", "path_type": "hardlink", "sha256": "d0b65072ab08a32faf306aa6c1e7fb8d4ed7da63d15f3e6aa42e17a98ee5be84", "sha256_in_prefix": "d0b65072ab08a32faf306aa6c1e7fb8d4ed7da63d15f3e6aa42e17a98ee5be84", "size_in_bytes": 4364}, {"_path": "lib/python3.11/site-packages/websockets/__init__.py", "path_type": "hardlink", "sha256": "002d87abddae49cfd63a8f7fc6f213a46b2127b0f2d0c7799b6ff2c2c752b7f6", "sha256_in_prefix": "002d87abddae49cfd63a8f7fc6f213a46b2127b0f2d0c7799b6ff2c2c752b7f6", "size_in_bytes": 7058}, {"_path": "lib/python3.11/site-packages/websockets/__main__.py", "path_type": "hardlink", "sha256": "c2ee4ddb093c9af060cafaf68219907f8a6b7b301ed3f8bea75db7555adec987", "sha256_in_prefix": "c2ee4ddb093c9af060cafaf68219907f8a6b7b301ed3f8bea75db7555adec987", "size_in_bytes": 62}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "c5e24d541f62bf18eb50dc265e019ec1e7257268b1a7b92f218608fbbbcd7a1b", "sha256_in_prefix": "c5e24d541f62bf18eb50dc265e019ec1e7257268b1a7b92f218608fbbbcd7a1b", "size_in_bytes": 5456}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "bcceca2bea90b6b53926428c9d46b4898a136e9e67548cee5de087e877e3f8b0", "sha256_in_prefix": "bcceca2bea90b6b53926428c9d46b4898a136e9e67548cee5de087e877e3f8b0", "size_in_bytes": 551}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/auth.cpython-311.pyc", "path_type": "hardlink", "sha256": "a22f3ebb8aa1f483076047c0a3357d122db9e3151faae0b2cdb3946c9d70d8b5", "sha256_in_prefix": "a22f3ebb8aa1f483076047c0a3357d122db9e3151faae0b2cdb3946c9d70d8b5", "size_in_bytes": 1163}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/cli.cpython-311.pyc", "path_type": "hardlink", "sha256": "88286a219b2a8c4a759d0211b15ba9eb1ad31ae1929a299f2e2f5139a662cfe3", "sha256_in_prefix": "88286a219b2a8c4a759d0211b15ba9eb1ad31ae1929a299f2e2f5139a662cfe3", "size_in_bytes": 9393}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/client.cpython-311.pyc", "path_type": "hardlink", "sha256": "8087e1e55925a937a639f81e9579cda370e4fb7cebe1f137f536a5f642ea63a9", "sha256_in_prefix": "8087e1e55925a937a639f81e9579cda370e4fb7cebe1f137f536a5f642ea63a9", "size_in_bytes": 18341}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/connection.cpython-311.pyc", "path_type": "hardlink", "sha256": "7dba40381b7ddf896d50d4448f8fbe0976f287924b81d5a0a63ac486ec3aef2f", "sha256_in_prefix": "7dba40381b7ddf896d50d4448f8fbe0976f287924b81d5a0a63ac486ec3aef2f", "size_in_bytes": 843}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/datastructures.cpython-311.pyc", "path_type": "hardlink", "sha256": "e1b837932e1fbe1ae73f0de462265cdf84f2e0425f638bbfb453b07ae42a7522", "sha256_in_prefix": "e1b837932e1fbe1ae73f0de462265cdf84f2e0425f638bbfb453b07ae42a7522", "size_in_bytes": 10302}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/exceptions.cpython-311.pyc", "path_type": "hardlink", "sha256": "492860ea984817307f6f8b35e220c59c13c94d1195fb71267a4fe352c98c8e00", "sha256_in_prefix": "492860ea984817307f6f8b35e220c59c13c94d1195fb71267a4fe352c98c8e00", "size_in_bytes": 20031}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/frames.cpython-311.pyc", "path_type": "hardlink", "sha256": "8a1c7fb6ee638cbfcba1d9cbfa99722749dc8143fe852af6ceaed4eba33d198e", "sha256_in_prefix": "8a1c7fb6ee638cbfcba1d9cbfa99722749dc8143fe852af6ceaed4eba33d198e", "size_in_bytes": 16831}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/headers.cpython-311.pyc", "path_type": "hardlink", "sha256": "cdba8ab1ec6dd6f8f131bb4a58c576713702d17d9f24239526384a487798459e", "sha256_in_prefix": "cdba8ab1ec6dd6f8f131bb4a58c576713702d17d9f24239526384a487798459e", "size_in_bytes": 20223}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/http.cpython-311.pyc", "path_type": "hardlink", "sha256": "8c8a765ef027612857162b24df9b5af6c8405bdb9cdb2bf31ced7d34150ae92b", "sha256_in_prefix": "8c8a765ef027612857162b24df9b5af6c8405bdb9cdb2bf31ced7d34150ae92b", "size_in_bytes": 1295}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/http11.cpython-311.pyc", "path_type": "hardlink", "sha256": "7c664c5d9a708fae11ae549363e3ad6177301ab92a0e0816b3b1c8d31209dbd9", "sha256_in_prefix": "7c664c5d9a708fae11ae549363e3ad6177301ab92a0e0816b3b1c8d31209dbd9", "size_in_bytes": 16971}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/imports.cpython-311.pyc", "path_type": "hardlink", "sha256": "c3d40202bf29510491cfb46b2820b1659622d72c9cf26f69bd66842cec503278", "sha256_in_prefix": "c3d40202bf29510491cfb46b2820b1659622d72c9cf26f69bd66842cec503278", "size_in_bytes": 4118}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/protocol.cpython-311.pyc", "path_type": "hardlink", "sha256": "e732f8a3d8f8917dc516ad3a245aeb846dccb37294344c01741e7bcd22c063dc", "sha256_in_prefix": "e732f8a3d8f8917dc516ad3a245aeb846dccb37294344c01741e7bcd22c063dc", "size_in_bytes": 26409}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/server.cpython-311.pyc", "path_type": "hardlink", "sha256": "730247582f259e9d039eb76781132ef5343ca17ba9981b60971b9f714da5e971", "sha256_in_prefix": "730247582f259e9d039eb76781132ef5343ca17ba9981b60971b9f714da5e971", "size_in_bytes": 25533}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/streams.cpython-311.pyc", "path_type": "hardlink", "sha256": "0d31923117b5b1fc53e8efcd5701c3d9a976968cd435f65350d3f54cd796d6b4", "sha256_in_prefix": "0d31923117b5b1fc53e8efcd5701c3d9a976968cd435f65350d3f54cd796d6b4", "size_in_bytes": 5978}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/typing.cpython-311.pyc", "path_type": "hardlink", "sha256": "6e2ca14375d68ca706a8161731bd51304909dcaa250cd98896292438dd83f2b9", "sha256_in_prefix": "6e2ca14375d68ca706a8161731bd51304909dcaa250cd98896292438dd83f2b9", "size_in_bytes": 1614}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/uri.cpython-311.pyc", "path_type": "hardlink", "sha256": "2d8b900c0597402914781850fd30b6249759d415ec7ce3c8c64def73f2f550cd", "sha256_in_prefix": "2d8b900c0597402914781850fd30b6249759d415ec7ce3c8c64def73f2f550cd", "size_in_bytes": 8702}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "2cac8b7c3d23776ae3fe82678757f9f3332722545bb97f82e50599e7985b1964", "sha256_in_prefix": "2cac8b7c3d23776ae3fe82678757f9f3332722545bb97f82e50599e7985b1964", "size_in_bytes": 2697}, {"_path": "lib/python3.11/site-packages/websockets/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "d38225b2a85debf487ab11e47dced6287d9342c8eab6f444933540c21c126474", "sha256_in_prefix": "d38225b2a85debf487ab11e47dced6287d9342c8eab6f444933540c21c126474", "size_in_bytes": 3576}, {"_path": "lib/python3.11/site-packages/websockets/asyncio/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/websockets/asyncio/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "62b3ca05da8ed11970058063e1c3131a434f3877e0759f11edf3982529149ab3", "sha256_in_prefix": "62b3ca05da8ed11970058063e1c3131a434f3877e0759f11edf3982529149ab3", "size_in_bytes": 427}, {"_path": "lib/python3.11/site-packages/websockets/asyncio/__pycache__/async_timeout.cpython-311.pyc", "path_type": "hardlink", "sha256": "a4a1de8bbde3c30209f48ee18bd373815d029bde4b2e41c93cd28bdbdd447bea", "sha256_in_prefix": "a4a1de8bbde3c30209f48ee18bd373815d029bde4b2e41c93cd28bdbdd447bea", "size_in_bytes": 11418}, {"_path": "lib/python3.11/site-packages/websockets/asyncio/__pycache__/client.cpython-311.pyc", "path_type": "hardlink", "sha256": "050f8b8460d9bd899eb9ed3e95563ee8ead89b5952d5107c112afffa1e74f746", "sha256_in_prefix": "050f8b8460d9bd899eb9ed3e95563ee8ead89b5952d5107c112afffa1e74f746", "size_in_bytes": 35653}, {"_path": "lib/python3.11/site-packages/websockets/asyncio/__pycache__/compatibility.cpython-311.pyc", "path_type": "hardlink", "sha256": "f00c13791f957d2c334983d21cea7c5218c06156c165aeb9e168a60a70309543", "sha256_in_prefix": "f00c13791f957d2c334983d21cea7c5218c06156c165aeb9e168a60a70309543", "size_in_bytes": 1475}, {"_path": "lib/python3.11/site-packages/websockets/asyncio/__pycache__/connection.cpython-311.pyc", "path_type": "hardlink", "sha256": "4e046648ae35502e0b526b737f18bba16a4cb617fb65ac7110029604c6c54bb9", "sha256_in_prefix": "4e046648ae35502e0b526b737f18bba16a4cb617fb65ac7110029604c6c54bb9", "size_in_bytes": 56694}, {"_path": "lib/python3.11/site-packages/websockets/asyncio/__pycache__/messages.cpython-311.pyc", "path_type": "hardlink", "sha256": "e69c7a7a9241d0b1b7d4e79ea21274581cdedc7c932f2238a82c686b23c0c415", "sha256_in_prefix": "e69c7a7a9241d0b1b7d4e79ea21274581cdedc7c932f2238a82c686b23c0c415", "size_in_bytes": 14548}, {"_path": "lib/python3.11/site-packages/websockets/asyncio/__pycache__/router.cpython-311.pyc", "path_type": "hardlink", "sha256": "64c0f483d3b5ed76c060f7aed8d98943ab0750b4aa1e9960e9b5c83d5cf7edf1", "sha256_in_prefix": "64c0f483d3b5ed76c060f7aed8d98943ab0750b4aa1e9960e9b5c83d5cf7edf1", "size_in_bytes": 8731}, {"_path": "lib/python3.11/site-packages/websockets/asyncio/__pycache__/server.cpython-311.pyc", "path_type": "hardlink", "sha256": "c055f80b65e750cd845d8171615afd7d569f16c8eacba7e2ab6369377fe9ea87", "sha256_in_prefix": "c055f80b65e750cd845d8171615afd7d569f16c8eacba7e2ab6369377fe9ea87", "size_in_bytes": 41385}, {"_path": "lib/python3.11/site-packages/websockets/asyncio/async_timeout.py", "path_type": "hardlink", "sha256": "37ee8cb9bca26a887ae8f0171af0b3860c4233eed5d978919c7df65e2e89e931", "sha256_in_prefix": "37ee8cb9bca26a887ae8f0171af0b3860c4233eed5d978919c7df65e2e89e931", "size_in_bytes": 8971}, {"_path": "lib/python3.11/site-packages/websockets/asyncio/client.py", "path_type": "hardlink", "sha256": "947d3cdc61ae9c57c41733a92f59cf729ee89787e9f67de59e1c27044eca14bd", "sha256_in_prefix": "947d3cdc61ae9c57c41733a92f59cf729ee89787e9f67de59e1c27044eca14bd", "size_in_bytes": 31490}, {"_path": "lib/python3.11/site-packages/websockets/asyncio/compatibility.py", "path_type": "hardlink", "sha256": "8247a70c387335b9bafe25d5e4476f6efa7ab8765876b4ef18d8edf0ffc9b724", "sha256_in_prefix": "8247a70c387335b9bafe25d5e4476f6efa7ab8765876b4ef18d8edf0ffc9b724", "size_in_bytes": 786}, {"_path": "lib/python3.11/site-packages/websockets/asyncio/connection.py", "path_type": "hardlink", "sha256": "ea1a0fa5a9e9f93ba2ac42223a2e8ed8fcaae70250984da8b02a8829bdb65e9a", "sha256_in_prefix": "ea1a0fa5a9e9f93ba2ac42223a2e8ed8fcaae70250984da8b02a8829bdb65e9a", "size_in_bytes": 48722}, {"_path": "lib/python3.11/site-packages/websockets/asyncio/messages.py", "path_type": "hardlink", "sha256": "11f63a6ba596ea025d9c4345aaf55bcf09bc9e8149a2f280419c281467f4a78b", "sha256_in_prefix": "11f63a6ba596ea025d9c4345aaf55bcf09bc9e8149a2f280419c281467f4a78b", "size_in_bytes": 10993}, {"_path": "lib/python3.11/site-packages/websockets/asyncio/router.py", "path_type": "hardlink", "sha256": "4d6d8f462106108ba7c840dfdd7d7ec9a6c4c2b9e2f1af3e30fff00fc5a8d08a", "sha256_in_prefix": "4d6d8f462106108ba7c840dfdd7d7ec9a6c4c2b9e2f1af3e30fff00fc5a8d08a", "size_in_bytes": 6601}, {"_path": "lib/python3.11/site-packages/websockets/asyncio/server.py", "path_type": "hardlink", "sha256": "a16ae1f59679cb0deff7d8e967c72cd9a8c2c3dca07428ef1fb10b87f57172be", "sha256_in_prefix": "a16ae1f59679cb0deff7d8e967c72cd9a8c2c3dca07428ef1fb10b87f57172be", "size_in_bytes": 37385}, {"_path": "lib/python3.11/site-packages/websockets/auth.py", "path_type": "hardlink", "sha256": "53f2709a7e7d65143a11e7293af322cd0086fd96c0be051fd6293b85a6510b77", "sha256_in_prefix": "53f2709a7e7d65143a11e7293af322cd0086fd96c0be051fd6293b85a6510b77", "size_in_bytes": 568}, {"_path": "lib/python3.11/site-packages/websockets/cli.py", "path_type": "hardlink", "sha256": "6277a01f9f73f77271495206897896851de4b6023a935fe97ff0512da9f12ab4", "sha256_in_prefix": "6277a01f9f73f77271495206897896851de4b6023a935fe97ff0512da9f12ab4", "size_in_bytes": 5336}, {"_path": "lib/python3.11/site-packages/websockets/client.py", "path_type": "hardlink", "sha256": "8706a8387b9986e971c94a84894dbb0b1e68512bc9b8725137ae5a8a9edb4ef5", "sha256_in_prefix": "8706a8387b9986e971c94a84894dbb0b1e68512bc9b8725137ae5a8a9edb4ef5", "size_in_bytes": 13564}, {"_path": "lib/python3.11/site-packages/websockets/connection.py", "path_type": "hardlink", "sha256": "38b88c56435ddb9ffceac07c43b0ab0b00685f2f6703438281d80b440f16511f", "sha256_in_prefix": "38b88c56435ddb9ffceac07c43b0ab0b00685f2f6703438281d80b440f16511f", "size_in_bytes": 323}, {"_path": "lib/python3.11/site-packages/websockets/datastructures.py", "path_type": "hardlink", "sha256": "ce3d1e98c37ca4bb56caa83697d6989e52616adffc2333e122e2c2be11a4123d", "sha256_in_prefix": "ce3d1e98c37ca4bb56caa83697d6989e52616adffc2333e122e2c2be11a4123d", "size_in_bytes": 5615}, {"_path": "lib/python3.11/site-packages/websockets/exceptions.py", "path_type": "hardlink", "sha256": "05ccf02a5a3ea318228ca08bb2e3e9f8f1bbeed0c358fcbcbc216301ae56ca51", "sha256_in_prefix": "05ccf02a5a3ea318228ca08bb2e3e9f8f1bbeed0c358fcbcbc216301ae56ca51", "size_in_bytes": 12811}, {"_path": "lib/python3.11/site-packages/websockets/extensions/__init__.py", "path_type": "hardlink", "sha256": "42466cc5a255965552a75ba1743e6e3c689b75bc7fd3dd46ad555f4b92d7729c", "sha256_in_prefix": "42466cc5a255965552a75ba1743e6e3c689b75bc7fd3dd46ad555f4b92d7729c", "size_in_bytes": 98}, {"_path": "lib/python3.11/site-packages/websockets/extensions/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "ef89972242aa7802df5115c1a9733381ffbc04faf9aaf619e0bd152c78ba3602", "sha256_in_prefix": "ef89972242aa7802df5115c1a9733381ffbc04faf9aaf619e0bd152c78ba3602", "size_in_bytes": 549}, {"_path": "lib/python3.11/site-packages/websockets/extensions/__pycache__/base.cpython-311.pyc", "path_type": "hardlink", "sha256": "bee8fc0f37e2691ae08599ee59007c43601313e3c8685dd70904e929ab83b1a3", "sha256_in_prefix": "bee8fc0f37e2691ae08599ee59007c43601313e3c8685dd70904e929ab83b1a3", "size_in_bytes": 4535}, {"_path": "lib/python3.11/site-packages/websockets/extensions/__pycache__/permessage_deflate.cpython-311.pyc", "path_type": "hardlink", "sha256": "56021eb2be0c8e956355cd6dfe28636e24c810a5f06deb69c21c6672dc78198c", "sha256_in_prefix": "56021eb2be0c8e956355cd6dfe28636e24c810a5f06deb69c21c6672dc78198c", "size_in_bytes": 20352}, {"_path": "lib/python3.11/site-packages/websockets/extensions/base.py", "path_type": "hardlink", "sha256": "24d7f2939e370bb56e3c7d103a86e2a8aa06af38c920b8deeaccf920bbce3e5e", "sha256_in_prefix": "24d7f2939e370bb56e3c7d103a86e2a8aa06af38c920b8deeaccf920bbce3e5e", "size_in_bytes": 2903}, {"_path": "lib/python3.11/site-packages/websockets/extensions/permessage_deflate.py", "path_type": "hardlink", "sha256": "fedbb8f8de15ebe518f668418414c4d34f53483aaf635cba3e461a9c862984e3", "sha256_in_prefix": "fedbb8f8de15ebe518f668418414c4d34f53483aaf635cba3e461a9c862984e3", "size_in_bytes": 25711}, {"_path": "lib/python3.11/site-packages/websockets/frames.py", "path_type": "hardlink", "sha256": "a7a7b7474e7e48ab4c0dc1e1f121d8b2914e1c6d5e8b9c89bd35abe4b79bc71b", "sha256_in_prefix": "a7a7b7474e7e48ab4c0dc1e1f121d8b2914e1c6d5e8b9c89bd35abe4b79bc71b", "size_in_bytes": 12759}, {"_path": "lib/python3.11/site-packages/websockets/headers.py", "path_type": "hardlink", "sha256": "c909cf963559c15d7f57ea4e49128d2c6feef36ef0142d61ef671c8a88dc43c3", "sha256_in_prefix": "c909cf963559c15d7f57ea4e49128d2c6feef36ef0142d61ef671c8a88dc43c3", "size_in_bytes": 16046}, {"_path": "lib/python3.11/site-packages/websockets/http.py", "path_type": "hardlink", "sha256": "4f5b4d2e66e41429de5d0eaa7a9066b155435f23fd8b9d34215cd3253781311e", "sha256_in_prefix": "4f5b4d2e66e41429de5d0eaa7a9066b155435f23fd8b9d34215cd3253781311e", "size_in_bytes": 659}, {"_path": "lib/python3.11/site-packages/websockets/http11.py", "path_type": "hardlink", "sha256": "6b6204bd353a13952da33a9db56a1a9b7d6b2a7d4f395285c2095a7930528ee6", "sha256_in_prefix": "6b6204bd353a13952da33a9db56a1a9b7d6b2a7d4f395285c2095a7930528ee6", "size_in_bytes": 14925}, {"_path": "lib/python3.11/site-packages/websockets/imports.py", "path_type": "hardlink", "sha256": "4ff07d4d4987a1c78a310f8f36985d410007d97771031c1240d790120a882e41", "sha256_in_prefix": "4ff07d4d4987a1c78a310f8f36985d410007d97771031c1240d790120a882e41", "size_in_bytes": 2795}, {"_path": "lib/python3.11/site-packages/websockets/legacy/__init__.py", "path_type": "hardlink", "sha256": "c10e7344810d1944bfe5e28d017f42444ef1d53c0a6a92a29ab40515637d4b1b", "sha256_in_prefix": "c10e7344810d1944bfe5e28d017f42444ef1d53c0a6a92a29ab40515637d4b1b", "size_in_bytes": 276}, {"_path": "lib/python3.11/site-packages/websockets/legacy/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "d44391dc406d97531778f507f4ee6aca67736441b69e584ecb4b0ea55482524a", "sha256_in_prefix": "d44391dc406d97531778f507f4ee6aca67736441b69e584ecb4b0ea55482524a", "size_in_bytes": 737}, {"_path": "lib/python3.11/site-packages/websockets/legacy/__pycache__/auth.cpython-311.pyc", "path_type": "hardlink", "sha256": "fe5ddf85e3c5a6ffbd60af53775d1f46318372ba1b1511a7fdc5f964c157c9cf", "sha256_in_prefix": "fe5ddf85e3c5a6ffbd60af53775d1f46318372ba1b1511a7fdc5f964c157c9cf", "size_in_bytes": 8595}, {"_path": "lib/python3.11/site-packages/websockets/legacy/__pycache__/client.cpython-311.pyc", "path_type": "hardlink", "sha256": "17103c12c65ca01f0ee369e44863c1be4858c967f0181a8939e34e2fe4a4e605", "sha256_in_prefix": "17103c12c65ca01f0ee369e44863c1be4858c967f0181a8939e34e2fe4a4e605", "size_in_bytes": 29762}, {"_path": "lib/python3.11/site-packages/websockets/legacy/__pycache__/exceptions.cpython-311.pyc", "path_type": "hardlink", "sha256": "86728290bec66ef36ca45d92d369e946e97a34e9aed274dfbcea10cfe03ec79b", "sha256_in_prefix": "86728290bec66ef36ca45d92d369e946e97a34e9aed274dfbcea10cfe03ec79b", "size_in_bytes": 3884}, {"_path": "lib/python3.11/site-packages/websockets/legacy/__pycache__/framing.cpython-311.pyc", "path_type": "hardlink", "sha256": "8d24483af1c10b1a43c31d21aa1f19e1cb2a7d27faeec74402bb4e1ee9d7c85f", "sha256_in_prefix": "8d24483af1c10b1a43c31d21aa1f19e1cb2a7d27faeec74402bb4e1ee9d7c85f", "size_in_bytes": 8936}, {"_path": "lib/python3.11/site-packages/websockets/legacy/__pycache__/handshake.cpython-311.pyc", "path_type": "hardlink", "sha256": "c8c57dc5cb4ad4f4d450d29cd0a0780f2f95676b2ec31ede5fea4accfca3bda7", "sha256_in_prefix": "c8c57dc5cb4ad4f4d450d29cd0a0780f2f95676b2ec31ede5fea4accfca3bda7", "size_in_bytes": 7902}, {"_path": "lib/python3.11/site-packages/websockets/legacy/__pycache__/http.cpython-311.pyc", "path_type": "hardlink", "sha256": "14426a3b7c50269d5175449c52427da3ff23bd13b6230ce8aab4c35d30dceb9b", "sha256_in_prefix": "14426a3b7c50269d5175449c52427da3ff23bd13b6230ce8aab4c35d30dceb9b", "size_in_bytes": 8733}, {"_path": "lib/python3.11/site-packages/websockets/legacy/__pycache__/protocol.cpython-311.pyc", "path_type": "hardlink", "sha256": "7e5bb733062d1ea7b9ced4cf4bc8fa50687ea651320566d8b2aa6c06f774da65", "sha256_in_prefix": "7e5bb733062d1ea7b9ced4cf4bc8fa50687ea651320566d8b2aa6c06f774da65", "size_in_bytes": 66012}, {"_path": "lib/python3.11/site-packages/websockets/legacy/__pycache__/server.cpython-311.pyc", "path_type": "hardlink", "sha256": "641e351c48d98f532322da5001438628090b6e573e49fac8848e758e435d8bb9", "sha256_in_prefix": "641e351c48d98f532322da5001438628090b6e573e49fac8848e758e435d8bb9", "size_in_bytes": 49693}, {"_path": "lib/python3.11/site-packages/websockets/legacy/auth.py", "path_type": "hardlink", "sha256": "0dc41c09279578ff7725c1fcbc5584d072092fe5fe7f6dcb6740ec2696afd52a", "sha256_in_prefix": "0dc41c09279578ff7725c1fcbc5584d072092fe5fe7f6dcb6740ec2696afd52a", "size_in_bytes": 6531}, {"_path": "lib/python3.11/site-packages/websockets/legacy/client.py", "path_type": "hardlink", "sha256": "d8926ab15087cf87174937fefa34ae50abc2d3819c3985c4832ff5190cf118c2", "sha256_in_prefix": "d8926ab15087cf87174937fefa34ae50abc2d3819c3985c4832ff5190cf118c2", "size_in_bytes": 26985}, {"_path": "lib/python3.11/site-packages/websockets/legacy/exceptions.py", "path_type": "hardlink", "sha256": "562123a684f4f5fcf1fd9a9fd1a3460d5b510cd8d768ec3481d0ad6b72e48c57", "sha256_in_prefix": "562123a684f4f5fcf1fd9a9fd1a3460d5b510cd8d768ec3481d0ad6b72e48c57", "size_in_bytes": 1924}, {"_path": "lib/python3.11/site-packages/websockets/legacy/framing.py", "path_type": "hardlink", "sha256": "45b2c6e53f58d332684b44726d327ece9a371951a373022deda264093576f5d8", "sha256_in_prefix": "45b2c6e53f58d332684b44726d327ece9a371951a373022deda264093576f5d8", "size_in_bytes": 6366}, {"_path": "lib/python3.11/site-packages/websockets/legacy/handshake.py", "path_type": "hardlink", "sha256": "d8dcebe40376c6f0c2e4474d3fe901de539cac069436562e8fffa1aff8efee93", "sha256_in_prefix": "d8dcebe40376c6f0c2e4474d3fe901de539cac069436562e8fffa1aff8efee93", "size_in_bytes": 5285}, {"_path": "lib/python3.11/site-packages/websockets/legacy/http.py", "path_type": "hardlink", "sha256": "70e0909835a120a4269bc5161973d6ec20d9834df08e8802b1bd0b3fda1eb4d4", "sha256_in_prefix": "70e0909835a120a4269bc5161973d6ec20d9834df08e8802b1bd0b3fda1eb4d4", "size_in_bytes": 7061}, {"_path": "lib/python3.11/site-packages/websockets/legacy/protocol.py", "path_type": "hardlink", "sha256": "1aa3d1d8422b61ed2172e3b3a926b4ea3cd7ee608d8d40920ba4e93f34b359a5", "sha256_in_prefix": "1aa3d1d8422b61ed2172e3b3a926b4ea3cd7ee608d8d40920ba4e93f34b359a5", "size_in_bytes": 63902}, {"_path": "lib/python3.11/site-packages/websockets/legacy/server.py", "path_type": "hardlink", "sha256": "04d868a3c43a8c3ae6778d87ad99416062fbc5f892a15bd407ec91068ce1f83d", "sha256_in_prefix": "04d868a3c43a8c3ae6778d87ad99416062fbc5f892a15bd407ec91068ce1f83d", "size_in_bytes": 45250}, {"_path": "lib/python3.11/site-packages/websockets/protocol.py", "path_type": "hardlink", "sha256": "172a20d44b15f31b619c9757dcc1fdf9b1db10680f442d2dab019608f2b826b8", "sha256_in_prefix": "172a20d44b15f31b619c9757dcc1fdf9b1db10680f442d2dab019608f2b826b8", "size_in_bytes": 26537}, {"_path": "lib/python3.11/site-packages/websockets/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/websockets/server.py", "path_type": "hardlink", "sha256": "a3694f42063bb201daaacb7ec0c703b6c8b1684b2c6a95bf03bc991c60732e21", "sha256_in_prefix": "a3694f42063bb201daaacb7ec0c703b6c8b1684b2c6a95bf03bc991c60732e21", "size_in_bytes": 21565}, {"_path": "lib/python3.11/site-packages/websockets/speedups.c", "path_type": "hardlink", "sha256": "8fe75a9a74f4d8c291a18c3c32d4d3e39a8b197eb3e939eb8aa8539327dc3591", "sha256_in_prefix": "8fe75a9a74f4d8c291a18c3c32d4d3e39a8b197eb3e939eb8aa8539327dc3591", "size_in_bytes": 5767}, {"_path": "lib/python3.11/site-packages/websockets/speedups.cpython-311-darwin.so", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/bld/rattler-build_websockets_1756476420/host_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_p", "sha256": "5b7c3c663da5a65b9e6ba4d51919a9d637a2f833532497a2ab6a78c99c43a874", "sha256_in_prefix": "3c69135f059d4affdc0c227fff6ccccf9c0dfffad6e813536164d6b9b04d715f", "size_in_bytes": 15104}, {"_path": "lib/python3.11/site-packages/websockets/speedups.pyi", "path_type": "hardlink", "sha256": "362919dec031b3d676b961ff66f72dbcc25405bb07782d83d4bf79e04552c097", "sha256_in_prefix": "362919dec031b3d676b961ff66f72dbcc25405bb07782d83d4bf79e04552c097", "size_in_bytes": 55}, {"_path": "lib/python3.11/site-packages/websockets/streams.py", "path_type": "hardlink", "sha256": "91c234257351a953e854484bebbfaeac80b08b4b202cdc8fc9675c705cc1b955", "sha256_in_prefix": "91c234257351a953e854484bebbfaeac80b08b4b202cdc8fc9675c705cc1b955", "size_in_bytes": 4047}, {"_path": "lib/python3.11/site-packages/websockets/sync/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/websockets/sync/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "78c38930d063adeace0279b9d4462cd55efc35dfdfaab95f6df6a838b0cc54ab", "sha256_in_prefix": "78c38930d063adeace0279b9d4462cd55efc35dfdfaab95f6df6a838b0cc54ab", "size_in_bytes": 424}, {"_path": "lib/python3.11/site-packages/websockets/sync/__pycache__/client.cpython-311.pyc", "path_type": "hardlink", "sha256": "f577691a4ca82253c8e740a2374d642552097adf9c1945614c1871f0bb71d7dd", "sha256_in_prefix": "f577691a4ca82253c8e740a2374d642552097adf9c1945614c1871f0bb71d7dd", "size_in_bytes": 26420}, {"_path": "lib/python3.11/site-packages/websockets/sync/__pycache__/connection.cpython-311.pyc", "path_type": "hardlink", "sha256": "37dfaedd1468e751e0f8f1cb012aa37dfffe9495ea2d5de80896d714ac8ec70b", "sha256_in_prefix": "37dfaedd1468e751e0f8f1cb012aa37dfffe9495ea2d5de80896d714ac8ec70b", "size_in_bytes": 43903}, {"_path": "lib/python3.11/site-packages/websockets/sync/__pycache__/messages.cpython-311.pyc", "path_type": "hardlink", "sha256": "9c449a45005e321e8f9611c3e4b7f04b4b1e0c0ceb8ebbdd14a13641d51b70ba", "sha256_in_prefix": "9c449a45005e321e8f9611c3e4b7f04b4b1e0c0ceb8ebbdd14a13641d51b70ba", "size_in_bytes": 15308}, {"_path": "lib/python3.11/site-packages/websockets/sync/__pycache__/router.cpython-311.pyc", "path_type": "hardlink", "sha256": "ddf5b824a785d29bd540058b8f3c76dad96bd08648a386ae2f94738083b7348b", "sha256_in_prefix": "ddf5b824a785d29bd540058b8f3c76dad96bd08648a386ae2f94738083b7348b", "size_in_bytes": 8420}, {"_path": "lib/python3.11/site-packages/websockets/sync/__pycache__/server.cpython-311.pyc", "path_type": "hardlink", "sha256": "f45b97918bc4ec33c08d288a557a626e49a96a22f7b2e77e56861020e1faa4e0", "sha256_in_prefix": "f45b97918bc4ec33c08d288a557a626e49a96a22f7b2e77e56861020e1faa4e0", "size_in_bytes": 30499}, {"_path": "lib/python3.11/site-packages/websockets/sync/__pycache__/utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "8a82afea23bb48d5073453667558d1f21647af61f2226e89ddff5f3b91e28a2d", "sha256_in_prefix": "8a82afea23bb48d5073453667558d1f21647af61f2226e89ddff5f3b91e28a2d", "size_in_bytes": 2058}, {"_path": "lib/python3.11/site-packages/websockets/sync/client.py", "path_type": "hardlink", "sha256": "eb75f4c8736a64630129a55561ee6cade8b2b1e5eedc2c2b892bcfa64e0985fa", "sha256_in_prefix": "eb75f4c8736a64630129a55561ee6cade8b2b1e5eedc2c2b892bcfa64e0985fa", "size_in_bytes": 22648}, {"_path": "lib/python3.11/site-packages/websockets/sync/connection.py", "path_type": "hardlink", "sha256": "02f667220b611cf92312cc43464ba9b0714432a4659c0480d5793271301b3a06", "sha256_in_prefix": "02f667220b611cf92312cc43464ba9b0714432a4659c0480d5793271301b3a06", "size_in_bytes": 41574}, {"_path": "lib/python3.11/site-packages/websockets/sync/messages.py", "path_type": "hardlink", "sha256": "9ad855b650e45c322a554d1a1b18dbe84a210de6bafde6554d6c83c39799ce45", "sha256_in_prefix": "9ad855b650e45c322a554d1a1b18dbe84a210de6bafde6554d6c83c39799ce45", "size_in_bytes": 12607}, {"_path": "lib/python3.11/site-packages/websockets/sync/router.py", "path_type": "hardlink", "sha256": "85fe9e66f194d16fc1aa3882f3c0c022928407bb30276c33f1b273c1b884279e", "sha256_in_prefix": "85fe9e66f194d16fc1aa3882f3c0c022928407bb30276c33f1b273c1b884279e", "size_in_bytes": 6291}, {"_path": "lib/python3.11/site-packages/websockets/sync/server.py", "path_type": "hardlink", "sha256": "8477241374efd5cf301bd09bc6a0774b2311822b2948b71e7f8a4cd9eade167d", "sha256_in_prefix": "8477241374efd5cf301bd09bc6a0774b2311822b2948b71e7f8a4cd9eade167d", "size_in_bytes": 27436}, {"_path": "lib/python3.11/site-packages/websockets/sync/utils.py", "path_type": "hardlink", "sha256": "4ed5be9dc605bc99a2496da03bcea7804d8156c9e705d2fee07f3c91634361b8", "sha256_in_prefix": "4ed5be9dc605bc99a2496da03bcea7804d8156c9e705d2fee07f3c91634361b8", "size_in_bytes": 1107}, {"_path": "lib/python3.11/site-packages/websockets/typing.py", "path_type": "hardlink", "sha256": "d59b763fe9574c2dca2613a1cd6d882f2128d48e5947d8c754fedbfeb9c9c90b", "sha256_in_prefix": "d59b763fe9574c2dca2613a1cd6d882f2128d48e5947d8c754fedbfeb9c9c90b", "size_in_bytes": 2025}, {"_path": "lib/python3.11/site-packages/websockets/uri.py", "path_type": "hardlink", "sha256": "3297488a064823b469c3bffeeb50e2b19c03b55fbaf2fe187b1ccf9913fc2e19", "sha256_in_prefix": "3297488a064823b469c3bffeeb50e2b19c03b55fbaf2fe187b1ccf9913fc2e19", "size_in_bytes": 6986}, {"_path": "lib/python3.11/site-packages/websockets/utils.py", "path_type": "hardlink", "sha256": "6691f75892ec412dbd25fe51ea54da7317ff84f77c138cd2d89986c8da60e1b0", "sha256_in_prefix": "6691f75892ec412dbd25fe51ea54da7317ff84f77c138cd2d89986c8da60e1b0", "size_in_bytes": 1150}, {"_path": "lib/python3.11/site-packages/websockets/version.py", "path_type": "hardlink", "sha256": "3ce864661d56d11715a2a5d9996507051fbee21afcf81c7fdfe5c39f25e8b6e8", "sha256_in_prefix": "3ce864661d56d11715a2a5d9996507051fbee21afcf81c7fdfe5c39f25e8b6e8", "size_in_bytes": 3204}, {"_path": "lib/python3.11/site-packages/websockets-15.0.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "bc33022edcb7639ff53355b4e91dade50a0bbf0299efeb6171d1ec0ba5029cfc", "sha256_in_prefix": "bc33022edcb7639ff53355b4e91dade50a0bbf0299efeb6171d1ec0ba5029cfc", "size_in_bytes": 6}, {"_path": "lib/python3.11/site-packages/websockets-15.0.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "69317591563eaf2a922f1655b96ae8a1c3e4dfa82718bb3246cff63eb964cf72", "sha256_in_prefix": "69317591563eaf2a922f1655b96ae8a1c3e4dfa82718bb3246cff63eb964cf72", "size_in_bytes": 6839}, {"_path": "lib/python3.11/site-packages/websockets-15.0.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "b26d9547d9f7672b07abcb08227b50eca790002ae1b9f0bc71c617d4f937f401", "sha256_in_prefix": "b26d9547d9f7672b07abcb08227b50eca790002ae1b9f0bc71c617d4f937f401", "size_in_bytes": 7708}, {"_path": "lib/python3.11/site-packages/websockets-15.0.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/websockets-15.0.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "9a99a83f4370ced80864e3ca8f3c6d5e12203e9375332d371b67792389f5359e", "sha256_in_prefix": "9a99a83f4370ced80864e3ca8f3c6d5e12203e9375332d371b67792389f5359e", "size_in_bytes": 111}, {"_path": "lib/python3.11/site-packages/websockets-15.0.1.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "acd88430f2e22b9ce9690e666bc2f118b552864cbeeefd7795f8527ab2b9b9ad", "sha256_in_prefix": "acd88430f2e22b9ce9690e666bc2f118b552864cbeeefd7795f8527ab2b9b9ad", "size_in_bytes": 118}, {"_path": "lib/python3.11/site-packages/websockets-15.0.1.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "0e7867e1d9b912c23864c02c1e574617a0b005766b1979d1edc9cadbe5d1ef36", "sha256_in_prefix": "0e7867e1d9b912c23864c02c1e574617a0b005766b1979d1edc9cadbe5d1ef36", "size_in_bytes": 51}, {"_path": "lib/python3.11/site-packages/websockets-15.0.1.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "3d6a0c050d8bec52fabad502e45fb25bd02bcadbd70dea34d447b6a0ff4e6da8", "sha256_in_prefix": "3d6a0c050d8bec52fabad502e45fb25bd02bcadbd70dea34d447b6a0ff4e6da8", "size_in_bytes": 1514}, {"_path": "lib/python3.11/site-packages/websockets-15.0.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "08ca5d2a49712acbd980283296dc5458e1e26d95d9d6e60856971af71b10f079", "sha256_in_prefix": "08ca5d2a49712acbd980283296dc5458e1e26d95d9d6e60856971af71b10f079", "size_in_bytes": 11}], "paths_version": 1}, "requested_spec": "None", "sha256": "d1b7a1ee0f2bb9a41b489dc431d189015b72fc8e33123fa694227f6538039d61", "size": 354032, "subdir": "osx-64", "timestamp": 1756476420000, "url": "https://conda.anaconda.org/conda-forge/osx-64/websockets-15.0.1-py311h179db11_2.conda", "version": "15.0.1"}