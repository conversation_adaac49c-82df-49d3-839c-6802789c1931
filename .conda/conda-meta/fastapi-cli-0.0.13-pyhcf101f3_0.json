{"build": "pyhcf101f3_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.10", "rich-toolkit >=0.14.8", "typer >=0.15.1", "uvicorn-standard >=0.15.0", "python"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/fastapi-cli-0.0.13-pyhcf101f3_0", "files": ["lib/python3.11/site-packages/fastapi_cli/__init__.py", "lib/python3.11/site-packages/fastapi_cli/__main__.py", "lib/python3.11/site-packages/fastapi_cli/cli.py", "lib/python3.11/site-packages/fastapi_cli/discover.py", "lib/python3.11/site-packages/fastapi_cli/exceptions.py", "lib/python3.11/site-packages/fastapi_cli/logging.py", "lib/python3.11/site-packages/fastapi_cli/py.typed", "lib/python3.11/site-packages/fastapi_cli/utils/__init__.py", "lib/python3.11/site-packages/fastapi_cli/utils/cli.py", "lib/python3.11/site-packages/fastapi_cli-0.0.13.dist-info/INSTALLER", "lib/python3.11/site-packages/fastapi_cli-0.0.13.dist-info/METADATA", "lib/python3.11/site-packages/fastapi_cli-0.0.13.dist-info/RECORD", "lib/python3.11/site-packages/fastapi_cli-0.0.13.dist-info/REQUESTED", "lib/python3.11/site-packages/fastapi_cli-0.0.13.dist-info/WHEEL", "lib/python3.11/site-packages/fastapi_cli-0.0.13.dist-info/direct_url.json", "lib/python3.11/site-packages/fastapi_cli-0.0.13.dist-info/entry_points.txt", "lib/python3.11/site-packages/fastapi_cli-0.0.13.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/fastapi_cli/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fastapi_cli/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/fastapi_cli/__pycache__/cli.cpython-311.pyc", "lib/python3.11/site-packages/fastapi_cli/__pycache__/discover.cpython-311.pyc", "lib/python3.11/site-packages/fastapi_cli/__pycache__/exceptions.cpython-311.pyc", "lib/python3.11/site-packages/fastapi_cli/__pycache__/logging.cpython-311.pyc", "lib/python3.11/site-packages/fastapi_cli/utils/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fastapi_cli/utils/__pycache__/cli.cpython-311.pyc", "bin/fastapi"], "fn": "fastapi-cli-0.0.13-pyhcf101f3_0.conda", "license": "MIT", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/fastapi-cli-0.0.13-pyhcf101f3_0", "type": 1}, "md5": "ac56247bdee6912941229d8e897672af", "name": "fastapi-cli", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/fastapi-cli-0.0.13-pyhcf101f3_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/fastapi_cli/__init__.py", "path_type": "hardlink", "sha256": "4b1bd17acffcd1cfb8db3c2f9f90573efa4656cfc467af45504b54045d64f53b", "sha256_in_prefix": "4b1bd17acffcd1cfb8db3c2f9f90573efa4656cfc467af45504b54045d64f53b", "size_in_bytes": 23}, {"_path": "site-packages/fastapi_cli/__main__.py", "path_type": "hardlink", "sha256": "6d8b7d7846a845059d7a3107143f11131f63c5511d669b44085b15ec5e3d2279", "sha256_in_prefix": "6d8b7d7846a845059d7a3107143f11131f63c5511d669b44085b15ec5e3d2279", "size_in_bytes": 30}, {"_path": "site-packages/fastapi_cli/cli.py", "path_type": "hardlink", "sha256": "04482420574d8f18325f3974dccef8f9fb53401130850e9c5a4c3534969751e4", "sha256_in_prefix": "04482420574d8f18325f3974dccef8f9fb53401130850e9c5a4c3534969751e4", "size_in_bytes": 13158}, {"_path": "site-packages/fastapi_cli/discover.py", "path_type": "hardlink", "sha256": "0d20a08c2659519bf31380101f52b72e0bf64fbce153c186c4c3c7dece8e4a3a", "sha256_in_prefix": "0d20a08c2659519bf31380101f52b72e0bf64fbce153c186c4c3c7dece8e4a3a", "size_in_bytes": 4575}, {"_path": "site-packages/fastapi_cli/exceptions.py", "path_type": "hardlink", "sha256": "007452a9de377dba8de48a57f85ab7f3d93d32810afeadbcc1514bee8a8f35c7", "sha256_in_prefix": "007452a9de377dba8de48a57f85ab7f3d93d32810afeadbcc1514bee8a8f35c7", "size_in_bytes": 47}, {"_path": "site-packages/fastapi_cli/logging.py", "path_type": "hardlink", "sha256": "621d8dc79782f1713f6b7a6c4cc3b4900e4133c95feb76c10aa48cc83c5437bb", "sha256_in_prefix": "621d8dc79782f1713f6b7a6c4cc3b4900e4133c95feb76c10aa48cc83c5437bb", "size_in_bytes": 690}, {"_path": "site-packages/fastapi_cli/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/fastapi_cli/utils/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/fastapi_cli/utils/cli.py", "path_type": "hardlink", "sha256": "b401511e74aeacf8065fe267790846509af360bbae10a9f54b11caf198f79e78", "sha256_in_prefix": "b401511e74aeacf8065fe267790846509af360bbae10a9f54b11caf198f79e78", "size_in_bytes": 2268}, {"_path": "site-packages/fastapi_cli-0.0.13.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "bc33022edcb7639ff53355b4e91dade50a0bbf0299efeb6171d1ec0ba5029cfc", "sha256_in_prefix": "bc33022edcb7639ff53355b4e91dade50a0bbf0299efeb6171d1ec0ba5029cfc", "size_in_bytes": 6}, {"_path": "site-packages/fastapi_cli-0.0.13.dist-info/METADATA", "path_type": "hardlink", "sha256": "653ac352a41b2dde2403b65d49abfa99e4b931b1aca54353477cf6def6cbb91e", "sha256_in_prefix": "653ac352a41b2dde2403b65d49abfa99e4b931b1aca54353477cf6def6cbb91e", "size_in_bytes": 6343}, {"_path": "site-packages/fastapi_cli-0.0.13.dist-info/RECORD", "path_type": "hardlink", "sha256": "93cf21bef16ce61533484e32fb85991212656231a3ec0c01ba255ca7786bf31e", "sha256_in_prefix": "93cf21bef16ce61533484e32fb85991212656231a3ec0c01ba255ca7786bf31e", "size_in_bytes": 1851}, {"_path": "site-packages/fastapi_cli-0.0.13.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/fastapi_cli-0.0.13.dist-info/WHEEL", "path_type": "hardlink", "sha256": "f4fdb2811c43ad3273de0b1a81cd19f7aba4af18ebf8b14118e82fdc0b8a9420", "sha256_in_prefix": "f4fdb2811c43ad3273de0b1a81cd19f7aba4af18ebf8b14118e82fdc0b8a9420", "size_in_bytes": 90}, {"_path": "site-packages/fastapi_cli-0.0.13.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "16ef2274a23fc987ba0243f43e3cc183209815431044e850a786f737f7972056", "sha256_in_prefix": "16ef2274a23fc987ba0243f43e3cc183209815431044e850a786f737f7972056", "size_in_bytes": 122}, {"_path": "site-packages/fastapi_cli-0.0.13.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "e8e62005c2f21425207aa2e09ef332389c4f096ce0cbbb1ee2b2cf2ad3689ccb", "sha256_in_prefix": "e8e62005c2f21425207aa2e09ef332389c4f096ce0cbbb1ee2b2cf2ad3689ccb", "size_in_bytes": 34}, {"_path": "site-packages/fastapi_cli-0.0.13.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "16a0f907855b5c99de7e9ad0b1e13453c9652fa171a230be8bc9ae672ed89925", "sha256_in_prefix": "16a0f907855b5c99de7e9ad0b1e13453c9652fa171a230be8bc9ae672ed89925", "size_in_bytes": 1086}, {"_path": "lib/python3.11/site-packages/fastapi_cli/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/fastapi_cli/__pycache__/__main__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/fastapi_cli/__pycache__/cli.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/fastapi_cli/__pycache__/discover.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/fastapi_cli/__pycache__/exceptions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/fastapi_cli/__pycache__/logging.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/fastapi_cli/utils/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/fastapi_cli/utils/__pycache__/cli.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "bin/fastapi", "path_type": "unix_python_entry_point"}], "paths_version": 1}, "requested_spec": "None", "sha256": "178a205d4b6636cb9f40f999bf2e6f559e9cbc53f1dcd8cd3f7e5fd78f193a54", "size": 17616, "subdir": "noarch", "timestamp": 1758405759000, "url": "https://conda.anaconda.org/conda-forge/noarch/fastapi-cli-0.0.13-pyhcf101f3_0.conda", "version": "0.0.13"}