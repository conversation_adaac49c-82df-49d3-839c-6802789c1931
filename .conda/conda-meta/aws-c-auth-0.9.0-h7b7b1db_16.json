{"build": "h7b7b1db_16", "build_number": 16, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13", "aws-c-cal >=0.9.2,<0.9.3.0a0", "aws-c-sdkutils >=0.2.4,<0.2.5.0a0", "aws-c-io >=0.21.0,<0.21.1.0a0", "aws-c-http >=0.10.2,<0.10.3.0a0", "aws-c-common >=0.12.4,<0.12.5.0a0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/aws-c-auth-0.9.0-h7b7b1db_16", "files": ["include/aws/auth/auth.h", "include/aws/auth/aws_imds_client.h", "include/aws/auth/credentials.h", "include/aws/auth/exports.h", "include/aws/auth/signable.h", "include/aws/auth/signing.h", "include/aws/auth/signing_config.h", "include/aws/auth/signing_result.h", "lib/cmake/aws-c-auth/aws-c-auth-config.cmake", "lib/cmake/aws-c-auth/shared/aws-c-auth-targets-release.cmake", "lib/cmake/aws-c-auth/shared/aws-c-auth-targets.cmake", "lib/libaws-c-auth.1.0.0.dylib", "lib/libaws-c-auth.dylib"], "fn": "aws-c-auth-0.9.0-h7b7b1db_16.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/aws-c-auth-0.9.0-h7b7b1db_16", "type": 1}, "md5": "3eff2d17cf73a181982ddf6fcc26e7d1", "name": "aws-c-auth", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/aws-c-auth-0.9.0-h7b7b1db_16.conda", "paths_data": {"paths": [{"_path": "include/aws/auth/auth.h", "path_type": "hardlink", "sha256": "c1c4bdb46b666663d123a74c25d25da64a56f57a5314160afb38f8b532ca520e", "sha256_in_prefix": "c1c4bdb46b666663d123a74c25d25da64a56f57a5314160afb38f8b532ca520e", "size_in_bytes": 3205}, {"_path": "include/aws/auth/aws_imds_client.h", "path_type": "hardlink", "sha256": "1e477ce6d2fcb161ed29f8b6eb7dedc3e0a223a3a6f9fd9e7ab7de88721abd62", "sha256_in_prefix": "1e477ce6d2fcb161ed29f8b6eb7dedc3e0a223a3a6f9fd9e7ab7de88721abd62", "size_in_bytes": 17479}, {"_path": "include/aws/auth/credentials.h", "path_type": "hardlink", "sha256": "4b2aeb37d0aeaeed168dc5a81a85dce7fe405a87155aadb27309c569dcea96bf", "sha256_in_prefix": "4b2aeb37d0aeaeed168dc5a81a85dce7fe405a87155aadb27309c569dcea96bf", "size_in_bytes": 46620}, {"_path": "include/aws/auth/exports.h", "path_type": "hardlink", "sha256": "283982d87ad7d36eb6538cb9ef282f0d95a9fa5af73287f48b673d1da90ae625", "sha256_in_prefix": "283982d87ad7d36eb6538cb9ef282f0d95a9fa5af73287f48b673d1da90ae625", "size_in_bytes": 919}, {"_path": "include/aws/auth/signable.h", "path_type": "hardlink", "sha256": "3ee67dbb4ac736584d92775d72ca5f768277fcc80d5f31bbd93ffd6aaa3b8c7b", "sha256_in_prefix": "3ee67dbb4ac736584d92775d72ca5f768277fcc80d5f31bbd93ffd6aaa3b8c7b", "size_in_bytes": 8518}, {"_path": "include/aws/auth/signing.h", "path_type": "hardlink", "sha256": "833ae094420c3dd66c012790bea45938a37f49240ca3673265ec8bf861404815", "sha256_in_prefix": "833ae094420c3dd66c012790bea45938a37f49240ca3673265ec8bf861404815", "size_in_bytes": 5383}, {"_path": "include/aws/auth/signing_config.h", "path_type": "hardlink", "sha256": "0377237be7b9a0440b53dcd3b89b471f017af9d7706401df3d14dbd0c81342ca", "sha256_in_prefix": "0377237be7b9a0440b53dcd3b89b471f017af9d7706401df3d14dbd0c81342ca", "size_in_bytes": 10863}, {"_path": "include/aws/auth/signing_result.h", "path_type": "hardlink", "sha256": "7fea744cad6d07f0e976d366836486455b70074afe5e6197b4f3403778277bff", "sha256_in_prefix": "7fea744cad6d07f0e976d366836486455b70074afe5e6197b4f3403778277bff", "size_in_bytes": 5830}, {"_path": "lib/cmake/aws-c-auth/aws-c-auth-config.cmake", "path_type": "hardlink", "sha256": "4137ae92d4ff115e1d563a8ab38cd2c67a2791f9404b684f8106d1c7555cd0d9", "sha256_in_prefix": "4137ae92d4ff115e1d563a8ab38cd2c67a2791f9404b684f8106d1c7555cd0d9", "size_in_bytes": 682}, {"_path": "lib/cmake/aws-c-auth/shared/aws-c-auth-targets-release.cmake", "path_type": "hardlink", "sha256": "573766be114096042dcabd525a54684a90ab0be99c587489e631a54a3ca3dd26", "sha256_in_prefix": "573766be114096042dcabd525a54684a90ab0be99c587489e631a54a3ca3dd26", "size_in_bytes": 887}, {"_path": "lib/cmake/aws-c-auth/shared/aws-c-auth-targets.cmake", "path_type": "hardlink", "sha256": "195819718abcab49bc6038f6d8bcac16e9a3e9396a510c0bd9c6332af84c7fac", "sha256_in_prefix": "195819718abcab49bc6038f6d8bcac16e9a3e9396a510c0bd9c6332af84c7fac", "size_in_bytes": 4334}, {"_path": "lib/libaws-c-auth.1.0.0.dylib", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/bld/rattler-build_aws-c-auth_1752261077/host_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_p", "sha256": "ea32b8e770d1189d4a01351974905c81e3ce538c4d73130dbbe5509fa19fb10e", "sha256_in_prefix": "d2df161be9b216904d1056f00489539b6d820bba6c1074fd9f03b65fa151fb53", "size_in_bytes": 229376}, {"_path": "lib/libaws-c-auth.dylib", "path_type": "softlink", "sha256": "ea32b8e770d1189d4a01351974905c81e3ce538c4d73130dbbe5509fa19fb10e", "size_in_bytes": 25}], "paths_version": 1}, "requested_spec": "None", "sha256": "ef8d36d2cfa0e12a794cf58c8e21378e06f20cb93492a108d65d88b8fceb48df", "size": 110746, "subdir": "osx-64", "timestamp": 1752261077000, "url": "https://conda.anaconda.org/conda-forge/osx-64/aws-c-auth-0.9.0-h7b7b1db_16.conda", "version": "0.9.0"}