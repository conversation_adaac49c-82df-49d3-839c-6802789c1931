{"build": "h3397294_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.14", "libabseil", "libcrc32c >=1.1.2,<1.2.0a0", "libcurl", "libcxx >=18", "libgoogle-cloud 2.36.0 h777fda5_1", "libzlib >=1.3.1,<2.0a0", "openssl"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libgoogle-cloud-storage-2.36.0-h3397294_1", "files": ["lib/libgoogle_cloud_cpp_storage.2.36.0.dylib", "lib/libgoogle_cloud_cpp_storage.2.dylib"], "fn": "libgoogle-cloud-storage-2.36.0-h3397294_1.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libgoogle-cloud-storage-2.36.0-h3397294_1", "type": 1}, "md5": "f360c132b279b8a3c3af5c57390524be", "name": "libgoogle-cloud-storage", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libgoogle-cloud-storage-2.36.0-h3397294_1.conda", "paths_data": {"paths": [{"_path": "lib/libgoogle_cloud_cpp_storage.2.36.0.dylib", "path_type": "hardlink", "sha256": "6b2b450e1b4544dbfc4b0564ce70452f3536c6d02005a276ebb9e19ba14f2522", "sha256_in_prefix": "6b2b450e1b4544dbfc4b0564ce70452f3536c6d02005a276ebb9e19ba14f2522", "size_in_bytes": 3080544}, {"_path": "lib/libgoogle_cloud_cpp_storage.2.dylib", "path_type": "softlink", "sha256": "6b2b450e1b4544dbfc4b0564ce70452f3536c6d02005a276ebb9e19ba14f2522", "size_in_bytes": 3080544}], "paths_version": 1}, "requested_spec": "None", "sha256": "2b294f87a6fe2463db6a0af9ca7a721324aab3711e475c0e28e35f233f624245", "size": 544276, "subdir": "osx-64", "timestamp": 1741880880000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libgoogle-cloud-storage-2.36.0-h3397294_1.conda", "version": "2.36.0"}