{"build": "pl5321ha958ccf_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["ncurses", "__osx >=10.13", "ncurses >=6.5,<7.0a0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libedit-3.1.20250104-pl5321ha958ccf_0", "files": ["include/editline/readline.h", "include/histedit.h", "lib/libedit.0.dylib", "lib/libedit.dylib", "lib/pkgconfig/libedit.pc", "share/man/man3/editline.3", "share/man/man3/el_deletestr.3", "share/man/man3/el_end.3", "share/man/man3/el_get.3", "share/man/man3/el_getc.3", "share/man/man3/el_gets.3", "share/man/man3/el_history.3", "share/man/man3/el_history_end.3", "share/man/man3/el_history_init.3", "share/man/man3/el_history_w.3", "share/man/man3/el_history_wend.3", "share/man/man3/el_history_winit.3", "share/man/man3/el_init.3", "share/man/man3/el_init_fd.3", "share/man/man3/el_insertstr.3", "share/man/man3/el_line.3", "share/man/man3/el_parse.3", "share/man/man3/el_push.3", "share/man/man3/el_reset.3", "share/man/man3/el_resize.3", "share/man/man3/el_set.3", "share/man/man3/el_source.3", "share/man/man3/el_tok_end.3", "share/man/man3/el_tok_init.3", "share/man/man3/el_tok_line.3", "share/man/man3/el_tok_reset.3", "share/man/man3/el_tok_str.3", "share/man/man3/el_tok_wend.3", "share/man/man3/el_tok_winit.3", "share/man/man3/el_tok_wline.3", "share/man/man3/el_tok_wreset.3", "share/man/man3/el_tok_wstr.3", "share/man/man3/el_wdeletestr.3", "share/man/man3/el_wget.3", "share/man/man3/el_wgetc.3", "share/man/man3/el_wgets.3", "share/man/man3/el_winsertstr.3", "share/man/man3/el_wline.3", "share/man/man3/el_wparse.3", "share/man/man3/el_wpush.3", "share/man/man3/el_wset.3", "share/man/man5/editrc.5", "share/man/man7/editline.7"], "fn": "libedit-3.1.20250104-pl5321ha958ccf_0.conda", "license": "BSD-2-<PERSON><PERSON>", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libedit-3.1.20250104-pl5321ha958ccf_0", "type": 1}, "md5": "1f4ed31220402fcddc083b4bff406868", "name": "libedit", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libedit-3.1.20250104-pl5321ha958ccf_0.conda", "paths_data": {"paths": [{"_path": "include/editline/readline.h", "path_type": "hardlink", "sha256": "c67b10c8cc3b478a64b7345dbf3261b8ba604d64dcbb619db685b77805535c45", "sha256_in_prefix": "c67b10c8cc3b478a64b7345dbf3261b8ba604d64dcbb619db685b77805535c45", "size_in_bytes": 9592}, {"_path": "include/histedit.h", "path_type": "hardlink", "sha256": "352b99708fb81b1fb98f173d0e30a7286c4ee937e488bc75ad9e8e319e24f923", "sha256_in_prefix": "352b99708fb81b1fb98f173d0e30a7286c4ee937e488bc75ad9e8e319e24f923", "size_in_bytes": 9640}, {"_path": "lib/libedit.0.dylib", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/bld/rattler-build_libedit_1738479554/host_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_plac", "sha256": "a029eb43c339018dc157c96a74e86ee3cc7624ac8d52ed65d12f4e0a40129554", "sha256_in_prefix": "d61dc9466a39656483e06e4943a0284d8d3182d8afa9614945125b0703d791be", "size_in_bytes": 200672}, {"_path": "lib/libedit.dylib", "path_type": "softlink", "sha256": "a029eb43c339018dc157c96a74e86ee3cc7624ac8d52ed65d12f4e0a40129554", "size_in_bytes": 15}, {"_path": "lib/pkgconfig/libedit.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/bld/rattler-build_libedit_1738479554/host_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_plac", "sha256": "1939284f147772e63ec097de72bc907dc8406cbb1e4907280b44aab65be4a570", "sha256_in_prefix": "2e293b8ca140c446648d752481f10d50a158a511abd387f56133e649c0a7bda0", "size_in_bytes": 587}, {"_path": "share/man/man3/editline.3", "path_type": "hardlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "sha256_in_prefix": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 23827}, {"_path": "share/man/man3/el_deletestr.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_end.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_get.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_getc.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_gets.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_history.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_history_end.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_history_init.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_history_w.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_history_wend.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_history_winit.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_init.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_init_fd.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_insertstr.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_line.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_parse.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_push.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_reset.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_resize.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_set.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_source.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_tok_end.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_tok_init.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_tok_line.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_tok_reset.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_tok_str.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_tok_wend.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_tok_winit.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_tok_wline.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_tok_wreset.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_tok_wstr.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_wdeletestr.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_wget.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_wgetc.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_wgets.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_winsertstr.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_wline.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_wparse.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_wpush.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man3/el_wset.3", "path_type": "softlink", "sha256": "03c074075497b2aa3a20c229e76a6ba8f00dc70b38f1b323e7ab2cfbc8fa45d1", "size_in_bytes": 10}, {"_path": "share/man/man5/editrc.5", "path_type": "hardlink", "sha256": "6ccf6a545e09863fbe659ee742bcafabc212359295b9f3f6abc45ea5eea2da86", "sha256_in_prefix": "6ccf6a545e09863fbe659ee742bcafabc212359295b9f3f6abc45ea5eea2da86", "size_in_bytes": 3598}, {"_path": "share/man/man7/editline.7", "path_type": "hardlink", "sha256": "0bcb8718502ff3eea01f458a0b819de4ca3bb1ef015d417f48d3961e15cd06f1", "sha256_in_prefix": "0bcb8718502ff3eea01f458a0b819de4ca3bb1ef015d417f48d3961e15cd06f1", "size_in_bytes": 33136}], "paths_version": 1}, "requested_spec": "None", "sha256": "6cc49785940a99e6a6b8c6edbb15f44c2dd6c789d9c283e5ee7bdfedd50b4cd6", "size": 115563, "subdir": "osx-64", "timestamp": 1738479554000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libedit-3.1.20250104-pl5321ha958ccf_0.conda", "version": "3.1.20250104"}