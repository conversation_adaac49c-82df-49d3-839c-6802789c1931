{"build": "pyh31011fe_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["__unix", "click >=7.0", "h11 >=0.8", "python >=3.10", "typing_extensions >=4.0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/uvicorn-0.36.0-pyh31011fe_0", "files": ["lib/python3.11/site-packages/uvicorn-0.36.0.dist-info/INSTALLER", "lib/python3.11/site-packages/uvicorn-0.36.0.dist-info/METADATA", "lib/python3.11/site-packages/uvicorn-0.36.0.dist-info/RECORD", "lib/python3.11/site-packages/uvicorn-0.36.0.dist-info/REQUESTED", "lib/python3.11/site-packages/uvicorn-0.36.0.dist-info/WHEEL", "lib/python3.11/site-packages/uvicorn-0.36.0.dist-info/direct_url.json", "lib/python3.11/site-packages/uvicorn-0.36.0.dist-info/entry_points.txt", "lib/python3.11/site-packages/uvicorn-0.36.0.dist-info/licenses/LICENSE.md", "lib/python3.11/site-packages/uvicorn/__init__.py", "lib/python3.11/site-packages/uvicorn/__main__.py", "lib/python3.11/site-packages/uvicorn/_compat.py", "lib/python3.11/site-packages/uvicorn/_subprocess.py", "lib/python3.11/site-packages/uvicorn/_types.py", "lib/python3.11/site-packages/uvicorn/config.py", "lib/python3.11/site-packages/uvicorn/importer.py", "lib/python3.11/site-packages/uvicorn/lifespan/__init__.py", "lib/python3.11/site-packages/uvicorn/lifespan/off.py", "lib/python3.11/site-packages/uvicorn/lifespan/on.py", "lib/python3.11/site-packages/uvicorn/logging.py", "lib/python3.11/site-packages/uvicorn/loops/__init__.py", "lib/python3.11/site-packages/uvicorn/loops/asyncio.py", "lib/python3.11/site-packages/uvicorn/loops/auto.py", "lib/python3.11/site-packages/uvicorn/loops/uvloop.py", "lib/python3.11/site-packages/uvicorn/main.py", "lib/python3.11/site-packages/uvicorn/middleware/__init__.py", "lib/python3.11/site-packages/uvicorn/middleware/asgi2.py", "lib/python3.11/site-packages/uvicorn/middleware/message_logger.py", "lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", "lib/python3.11/site-packages/uvicorn/middleware/wsgi.py", "lib/python3.11/site-packages/uvicorn/protocols/__init__.py", "lib/python3.11/site-packages/uvicorn/protocols/http/__init__.py", "lib/python3.11/site-packages/uvicorn/protocols/http/auto.py", "lib/python3.11/site-packages/uvicorn/protocols/http/flow_control.py", "lib/python3.11/site-packages/uvicorn/protocols/http/h11_impl.py", "lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", "lib/python3.11/site-packages/uvicorn/protocols/utils.py", "lib/python3.11/site-packages/uvicorn/protocols/websockets/__init__.py", "lib/python3.11/site-packages/uvicorn/protocols/websockets/auto.py", "lib/python3.11/site-packages/uvicorn/protocols/websockets/websockets_impl.py", "lib/python3.11/site-packages/uvicorn/protocols/websockets/websockets_sansio_impl.py", "lib/python3.11/site-packages/uvicorn/protocols/websockets/wsproto_impl.py", "lib/python3.11/site-packages/uvicorn/py.typed", "lib/python3.11/site-packages/uvicorn/server.py", "lib/python3.11/site-packages/uvicorn/supervisors/__init__.py", "lib/python3.11/site-packages/uvicorn/supervisors/basereload.py", "lib/python3.11/site-packages/uvicorn/supervisors/multiprocess.py", "lib/python3.11/site-packages/uvicorn/supervisors/statreload.py", "lib/python3.11/site-packages/uvicorn/supervisors/watchfilesreload.py", "lib/python3.11/site-packages/uvicorn/workers.py", "lib/python3.11/site-packages/uvicorn/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/__pycache__/_compat.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/__pycache__/_subprocess.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/__pycache__/_types.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/__pycache__/config.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/__pycache__/importer.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/lifespan/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/lifespan/__pycache__/off.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/lifespan/__pycache__/on.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/__pycache__/logging.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/loops/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/loops/__pycache__/asyncio.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/loops/__pycache__/auto.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/loops/__pycache__/uvloop.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/__pycache__/main.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/middleware/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/middleware/__pycache__/asgi2.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/middleware/__pycache__/message_logger.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/middleware/__pycache__/proxy_headers.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/middleware/__pycache__/wsgi.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/protocols/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/protocols/http/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/protocols/http/__pycache__/auto.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/protocols/http/__pycache__/flow_control.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/protocols/http/__pycache__/h11_impl.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/protocols/http/__pycache__/httptools_impl.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/protocols/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/protocols/websockets/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/protocols/websockets/__pycache__/auto.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/protocols/websockets/__pycache__/websockets_impl.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/protocols/websockets/__pycache__/websockets_sansio_impl.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/protocols/websockets/__pycache__/wsproto_impl.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/__pycache__/server.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/supervisors/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/supervisors/__pycache__/basereload.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/supervisors/__pycache__/multiprocess.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/supervisors/__pycache__/statreload.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/supervisors/__pycache__/watchfilesreload.cpython-311.pyc", "lib/python3.11/site-packages/uvicorn/__pycache__/workers.cpython-311.pyc", "bin/uvicorn"], "fn": "uvicorn-0.36.0-pyh31011fe_0.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/uvicorn-0.36.0-pyh31011fe_0", "type": 1}, "md5": "343e886a163cc2c3a7420c675055525d", "name": "u<PERSON><PERSON>", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/uvicorn-0.36.0-pyh31011fe_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/uvicorn-0.36.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/uvicorn-0.36.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "c9a6d141af67fc67d56d23ab756ecb0c76dc1f90fb0b5287839924408ce30eb3", "sha256_in_prefix": "c9a6d141af67fc67d56d23ab756ecb0c76dc1f90fb0b5287839924408ce30eb3", "size_in_bytes": 6607}, {"_path": "site-packages/uvicorn-0.36.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "aefe42155477284ab8dcb915884d181edf3daee4b030e051b1a3714e554721df", "sha256_in_prefix": "aefe42155477284ab8dcb915884d181edf3daee4b030e051b1a3714e554721df", "size_in_bytes": 6577}, {"_path": "site-packages/uvicorn-0.36.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/uvicorn-0.36.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "sha256_in_prefix": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "size_in_bytes": 87}, {"_path": "site-packages/uvicorn-0.36.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "3f36c417fbbeed693c15f08f7f3f59ce037e3fcc70f7013788370d3fdd2e0ce6", "sha256_in_prefix": "3f36c417fbbeed693c15f08f7f3f59ce037e3fcc70f7013788370d3fdd2e0ce6", "size_in_bytes": 103}, {"_path": "site-packages/uvicorn-0.36.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "156d70fa191cf50830686a28bccbe6d1963bdf0fcd7327167460147c3b0d1b1c", "sha256_in_prefix": "156d70fa191cf50830686a28bccbe6d1963bdf0fcd7327167460147c3b0d1b1c", "size_in_bytes": 46}, {"_path": "site-packages/uvicorn-0.36.0.dist-info/licenses/LICENSE.md", "path_type": "hardlink", "sha256": "6e40f231fbf861db5bb27f728c47340c69eea8bb864f624f49df892fa02adfaf", "sha256_in_prefix": "6e40f231fbf861db5bb27f728c47340c69eea8bb864f624f49df892fa02adfaf", "size_in_bytes": 1557}, {"_path": "site-packages/uvicorn/__init__.py", "path_type": "hardlink", "sha256": "24e389450c3c6cd81d8aaf415d84a2f97a8e5ea3ad2ea18923f6b30db380e6c8", "sha256_in_prefix": "24e389450c3c6cd81d8aaf415d84a2f97a8e5ea3ad2ea18923f6b30db380e6c8", "size_in_bytes": 147}, {"_path": "site-packages/uvicorn/__main__.py", "path_type": "hardlink", "sha256": "0d08b3cba9ca3f4cb084fa6708782644360831f72a64a544ccd2164198eab554", "sha256_in_prefix": "0d08b3cba9ca3f4cb084fa6708782644360831f72a64a544ccd2164198eab554", "size_in_bytes": 62}, {"_path": "site-packages/uvicorn/_compat.py", "path_type": "hardlink", "sha256": "802bdc7ad80655506aaae767c66daba32850b82cad6dc4ea815c7642934e7186", "sha256_in_prefix": "802bdc7ad80655506aaae767c66daba32850b82cad6dc4ea815c7642934e7186", "size_in_bytes": 2739}, {"_path": "site-packages/uvicorn/_subprocess.py", "path_type": "hardlink", "sha256": "1db7d19ec0a45f283bc425950165b35d04de5a5bcb29f4e5205e707af141911e", "sha256_in_prefix": "1db7d19ec0a45f283bc425950165b35d04de5a5bcb29f4e5205e707af141911e", "size_in_bytes": 2766}, {"_path": "site-packages/uvicorn/_types.py", "path_type": "hardlink", "sha256": "e4570fbef21f78ab090e31a14eb71e0eff139b0cd823cc8f17b997b174d63943", "sha256_in_prefix": "e4570fbef21f78ab090e31a14eb71e0eff139b0cd823cc8f17b997b174d63943", "size_in_bytes": 7775}, {"_path": "site-packages/uvicorn/config.py", "path_type": "hardlink", "sha256": "ea5612d2e6f596d96020bce3d0059ba576dd3a546b2459972b5cb90f3827a067", "sha256_in_prefix": "ea5612d2e6f596d96020bce3d0059ba576dd3a546b2459972b5cb90f3827a067", "size_in_bytes": 21401}, {"_path": "site-packages/uvicorn/importer.py", "path_type": "hardlink", "sha256": "9d1b74410deaa62dbae3e9ff991d25e790b675d33c9e8c133734f58ec59a6a6f", "sha256_in_prefix": "9d1b74410deaa62dbae3e9ff991d25e790b675d33c9e8c133734f58ec59a6a6f", "size_in_bytes": 1128}, {"_path": "site-packages/uvicorn/lifespan/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/uvicorn/lifespan/off.py", "path_type": "hardlink", "sha256": "9df23aa87014a3ff3e0445cc04aa0743dc146ec5eb3da5cb09b0d24b4bca4abf", "sha256_in_prefix": "9df23aa87014a3ff3e0445cc04aa0743dc146ec5eb3da5cb09b0d24b4bca4abf", "size_in_bytes": 332}, {"_path": "site-packages/uvicorn/lifespan/on.py", "path_type": "hardlink", "sha256": "59e493a86b0aae75842606cefa5f80f3deba39ba16711f77bf376bf3e0972afe", "sha256_in_prefix": "59e493a86b0aae75842606cefa5f80f3deba39ba16711f77bf376bf3e0972afe", "size_in_bytes": 5184}, {"_path": "site-packages/uvicorn/logging.py", "path_type": "hardlink", "sha256": "f9e084e273899856ed07da9f349b8454d174635dcb1941eabc5cde9984f43da4", "sha256_in_prefix": "f9e084e273899856ed07da9f349b8454d174635dcb1941eabc5cde9984f43da4", "size_in_bytes": 4235}, {"_path": "site-packages/uvicorn/loops/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/uvicorn/loops/asyncio.py", "path_type": "hardlink", "sha256": "6bcc0ee1f078a4293e44b202abb0a71abfdcc5023b3012c98fe1a8e14425743f", "sha256_in_prefix": "6bcc0ee1f078a4293e44b202abb0a71abfdcc5023b3012c98fe1a8e14425743f", "size_in_bytes": 333}, {"_path": "site-packages/uvicorn/loops/auto.py", "path_type": "hardlink", "sha256": "a5d4ec31caa46c4f1fbdd3602ecd72e1cea1b2e379f7c9993923a50b46b6dd2e", "sha256_in_prefix": "a5d4ec31caa46c4f1fbdd3602ecd72e1cea1b2e379f7c9993923a50b46b6dd2e", "size_in_bytes": 566}, {"_path": "site-packages/uvicorn/loops/uvloop.py", "path_type": "hardlink", "sha256": "09af932fe5bcb5b7445b9baf2ff1ab32287b1bc7bfb7fbe135ebe2d8d2988c45", "sha256_in_prefix": "09af932fe5bcb5b7445b9baf2ff1ab32287b1bc7bfb7fbe135ebe2d8d2988c45", "size_in_bytes": 236}, {"_path": "site-packages/uvicorn/main.py", "path_type": "hardlink", "sha256": "3565dd133403d348ae9d06e947cb61d930373fb13326920cca7fdf142fa86eb8", "sha256_in_prefix": "3565dd133403d348ae9d06e947cb61d930373fb13326920cca7fdf142fa86eb8", "size_in_bytes": 17288}, {"_path": "site-packages/uvicorn/middleware/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/uvicorn/middleware/asgi2.py", "path_type": "hardlink", "sha256": "610ad0366dd17a116db379b3937938cb0f1a0fc120b63d2d452dcde3962441ad", "sha256_in_prefix": "610ad0366dd17a116db379b3937938cb0f1a0fc120b63d2d452dcde3962441ad", "size_in_bytes": 394}, {"_path": "site-packages/uvicorn/middleware/message_logger.py", "path_type": "hardlink", "sha256": "2071195129c535a305505770b593b702e1404e761c4a8afe815b4e8db0b3b7c3", "sha256_in_prefix": "2071195129c535a305505770b593b702e1404e761c4a8afe815b4e8db0b3b7c3", "size_in_bytes": 2859}, {"_path": "site-packages/uvicorn/middleware/proxy_headers.py", "path_type": "hardlink", "sha256": "c901b74e199985187d8e9f8021dec34216f959597ab68d210f51b40cfaed2e8c", "sha256_in_prefix": "c901b74e199985187d8e9f8021dec34216f959597ab68d210f51b40cfaed2e8c", "size_in_bytes": 5790}, {"_path": "site-packages/uvicorn/middleware/wsgi.py", "path_type": "hardlink", "sha256": "37a7d6c8e9c7a1e1db51ebd7d260d814d988e25b0cbfb634aa777b6377c954be", "sha256_in_prefix": "37a7d6c8e9c7a1e1db51ebd7d260d814d988e25b0cbfb634aa777b6377c954be", "size_in_bytes": 7105}, {"_path": "site-packages/uvicorn/protocols/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/uvicorn/protocols/http/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/uvicorn/protocols/http/auto.py", "path_type": "hardlink", "sha256": "61f5c6cb359369a136a7f8e44cf5ab2425ecc446909c2dcd2b4f86ed68269e5b", "sha256_in_prefix": "61f5c6cb359369a136a7f8e44cf5ab2425ecc446909c2dcd2b4f86ed68269e5b", "size_in_bytes": 403}, {"_path": "site-packages/uvicorn/protocols/http/flow_control.py", "path_type": "hardlink", "sha256": "d39d16560df512f3ce907c329c2a0c3f5cd7165fef3b753876eae5739bf2a785", "sha256_in_prefix": "d39d16560df512f3ce907c329c2a0c3f5cd7165fef3b753876eae5739bf2a785", "size_in_bytes": 1701}, {"_path": "site-packages/uvicorn/protocols/http/h11_impl.py", "path_type": "hardlink", "sha256": "e1bf8ab302b9ec505a44f7875e52bfa32f2928ceaf1b585a00b0887911fed5b0", "sha256_in_prefix": "e1bf8ab302b9ec505a44f7875e52bfa32f2928ceaf1b585a00b0887911fed5b0", "size_in_bytes": 20694}, {"_path": "site-packages/uvicorn/protocols/http/httptools_impl.py", "path_type": "hardlink", "sha256": "b6e4010a20faadfe4341ec90c0755ce39af11fda2ea233299178bd286e8d4419", "sha256_in_prefix": "b6e4010a20faadfe4341ec90c0755ce39af11fda2ea233299178bd286e8d4419", "size_in_bytes": 21805}, {"_path": "site-packages/uvicorn/protocols/utils.py", "path_type": "hardlink", "sha256": "ac28d82dde3fbb03de6646d15d0e9b7827f1c88fe8629bc90b0cf78c418d3a21", "sha256_in_prefix": "ac28d82dde3fbb03de6646d15d0e9b7827f1c88fe8629bc90b0cf78c418d3a21", "size_in_bytes": 1849}, {"_path": "site-packages/uvicorn/protocols/websockets/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/uvicorn/protocols/websockets/auto.py", "path_type": "hardlink", "sha256": "487fca57fdefc11f3fa0675ad86ac716ddfc546f88c18d2e7e3bc73aee15034a", "sha256_in_prefix": "487fca57fdefc11f3fa0675ad86ac716ddfc546f88c18d2e7e3bc73aee15034a", "size_in_bytes": 581}, {"_path": "site-packages/uvicorn/protocols/websockets/websockets_impl.py", "path_type": "hardlink", "sha256": "029977b2bd016295a06d5893019885200d86d15602c5835ff02924f7606a5ea8", "sha256_in_prefix": "029977b2bd016295a06d5893019885200d86d15602c5835ff02924f7606a5ea8", "size_in_bytes": 15546}, {"_path": "site-packages/uvicorn/protocols/websockets/websockets_sansio_impl.py", "path_type": "hardlink", "sha256": "4f1a6391bba169377451761cd5531ee14801afa927fc9c0957e86a7a01c79b1b", "sha256_in_prefix": "4f1a6391bba169377451761cd5531ee14801afa927fc9c0957e86a7a01c79b1b", "size_in_bytes": 17145}, {"_path": "site-packages/uvicorn/protocols/websockets/wsproto_impl.py", "path_type": "hardlink", "sha256": "bb64cacb34540a64294b57b813f5fa528bbd408ba829934d3661d4d48ce458f5", "sha256_in_prefix": "bb64cacb34540a64294b57b813f5fa528bbd408ba829934d3661d4d48ce458f5", "size_in_bytes": 15366}, {"_path": "site-packages/uvicorn/py.typed", "path_type": "hardlink", "sha256": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "sha256_in_prefix": "01ba4719c80b6fe911b091a7c05124b64eeece964e09c058ef8f9805daca546b", "size_in_bytes": 1}, {"_path": "site-packages/uvicorn/server.py", "path_type": "hardlink", "sha256": "a3bdfc6d064d11cb9a1ac7bd10e4781183b1ccf9b590e6802aea512e3b1fb88d", "sha256_in_prefix": "a3bdfc6d064d11cb9a1ac7bd10e4781183b1ccf9b590e6802aea512e3b1fb88d", "size_in_bytes": 13044}, {"_path": "site-packages/uvicorn/supervisors/__init__.py", "path_type": "hardlink", "sha256": "c13f1e38422a4f5c96420cad66dbf9b5a58cba5ef1a13218d319b59b8a323d3c", "sha256_in_prefix": "c13f1e38422a4f5c96420cad66dbf9b5a58cba5ef1a13218d319b59b8a323d3c", "size_in_bytes": 507}, {"_path": "site-packages/uvicorn/supervisors/basereload.py", "path_type": "hardlink", "sha256": "3005d243772464fc2acc9f149fdc83864dd6d32c8002b42d64bb8e1343889d27", "sha256_in_prefix": "3005d243772464fc2acc9f149fdc83864dd6d32c8002b42d64bb8e1343889d27", "size_in_bytes": 4036}, {"_path": "site-packages/uvicorn/supervisors/multiprocess.py", "path_type": "hardlink", "sha256": "3a9b745ef3948f50c83176306f83a59096717a1fd18f07859d39833d822d38dc", "sha256_in_prefix": "3a9b745ef3948f50c83176306f83a59096717a1fd18f07859d39833d822d38dc", "size_in_bytes": 7507}, {"_path": "site-packages/uvicorn/supervisors/statreload.py", "path_type": "hardlink", "sha256": "b986e59a8c4cdc86cf6ef303cebe406f0dbe5b291097c3714d3cde2df572be75", "sha256_in_prefix": "b986e59a8c4cdc86cf6ef303cebe406f0dbe5b291097c3714d3cde2df572be75", "size_in_bytes": 1566}, {"_path": "site-packages/uvicorn/supervisors/watchfilesreload.py", "path_type": "hardlink", "sha256": "5bce986dbd04e5274c618b961c9ddba40157770e59babbcb45d15cbcb9d81080", "sha256_in_prefix": "5bce986dbd04e5274c618b961c9ddba40157770e59babbcb45d15cbcb9d81080", "size_in_bytes": 2859}, {"_path": "site-packages/uvicorn/workers.py", "path_type": "hardlink", "sha256": "94c0ae6e7d568bee21da3c6b5cd32684e259cbb64ff10c5b65b3f7ff63b58871", "sha256_in_prefix": "94c0ae6e7d568bee21da3c6b5cd32684e259cbb64ff10c5b65b3f7ff63b58871", "size_in_bytes": 3873}, {"_path": "lib/python3.11/site-packages/uvicorn/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/__pycache__/__main__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/__pycache__/_compat.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/__pycache__/_subprocess.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/__pycache__/_types.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/__pycache__/config.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/__pycache__/importer.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/lifespan/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/lifespan/__pycache__/off.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/lifespan/__pycache__/on.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/__pycache__/logging.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/loops/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/loops/__pycache__/asyncio.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/loops/__pycache__/auto.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/loops/__pycache__/uvloop.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/__pycache__/main.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/middleware/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/middleware/__pycache__/asgi2.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/middleware/__pycache__/message_logger.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/middleware/__pycache__/proxy_headers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/middleware/__pycache__/wsgi.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/protocols/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/protocols/http/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/protocols/http/__pycache__/auto.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/protocols/http/__pycache__/flow_control.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/protocols/http/__pycache__/h11_impl.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/protocols/http/__pycache__/httptools_impl.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/protocols/__pycache__/utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/protocols/websockets/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/protocols/websockets/__pycache__/auto.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/protocols/websockets/__pycache__/websockets_impl.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/protocols/websockets/__pycache__/websockets_sansio_impl.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/protocols/websockets/__pycache__/wsproto_impl.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/__pycache__/server.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/supervisors/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/supervisors/__pycache__/basereload.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/supervisors/__pycache__/multiprocess.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/supervisors/__pycache__/statreload.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/supervisors/__pycache__/watchfilesreload.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/uvicorn/__pycache__/workers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "bin/uvicorn", "path_type": "unix_python_entry_point"}], "paths_version": 1}, "requested_spec": "None", "sha256": "a17cf73fdc630ca4fe9524c656bd4db79adb52473b17709add2494a71c252246", "size": 51161, "subdir": "noarch", "timestamp": 1758365926000, "url": "https://conda.anaconda.org/conda-forge/noarch/uvicorn-0.36.0-pyh31011fe_0.conda", "version": "0.36.0"}