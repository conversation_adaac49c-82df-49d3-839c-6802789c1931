{"build": "hfa3c126_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": ["libgfortran 15.1.0"], "depends": ["llvm-openmp >=8.0.0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libgfortran5-15.1.0-hfa3c126_1", "files": ["lib/libgcc_s.1.1.dylib", "lib/libgcc_s.1.dylib", "lib/libgfortran.5.dylib", "lib/libgfortran.dylib", "lib/libgomp.1.dylib", "lib/libgomp.dylib", "lib/libquadmath.0.dylib", "lib/libquadmath.dylib"], "fn": "libgfortran5-15.1.0-hfa3c126_1.conda", "license": "GPL-3.0-only WITH GCC-exception-3.1", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libgfortran5-15.1.0-hfa3c126_1", "type": 1}, "md5": "696e408f36a5a25afdb23e862053ca82", "name": "libgfortran5", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libgfortran5-15.1.0-hfa3c126_1.conda", "paths_data": {"paths": [{"_path": "lib/libgcc_s.1.1.dylib", "path_type": "hardlink", "sha256": "83df02698ac616c9bf4140c89887e01c7dd1f68324029b4cc00df56bf642d488", "sha256_in_prefix": "83df02698ac616c9bf4140c89887e01c7dd1f68324029b4cc00df56bf642d488", "size_in_bytes": 188672}, {"_path": "lib/libgcc_s.1.dylib", "path_type": "hardlink", "sha256": "be180638ed7017ec4eebb051168a113a4e232c6dedee4f882b1e2b376dff7a15", "sha256_in_prefix": "be180638ed7017ec4eebb051168a113a4e232c6dedee4f882b1e2b376dff7a15", "size_in_bytes": 10840}, {"_path": "lib/libgfortran.5.dylib", "path_type": "hardlink", "sha256": "d36b215bb661be5bb6a08156c0f5bc32e5f7cb554b7853fe7f651818a288c59d", "sha256_in_prefix": "d36b215bb661be5bb6a08156c0f5bc32e5f7cb554b7853fe7f651818a288c59d", "size_in_bytes": 3542752}, {"_path": "lib/libgfortran.dylib", "path_type": "softlink", "sha256": "d36b215bb661be5bb6a08156c0f5bc32e5f7cb554b7853fe7f651818a288c59d", "size_in_bytes": 3542752}, {"_path": "lib/libgomp.1.dylib", "path_type": "softlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/libgomp.dylib", "path_type": "softlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/libquadmath.0.dylib", "path_type": "hardlink", "sha256": "76b601e8fc9082a5f41d0f26292092c66f6ea57287d2b02a065a6d01dd6ab9ad", "sha256_in_prefix": "76b601e8fc9082a5f41d0f26292092c66f6ea57287d2b02a065a6d01dd6ab9ad", "size_in_bytes": 293200}, {"_path": "lib/libquadmath.dylib", "path_type": "softlink", "sha256": "76b601e8fc9082a5f41d0f26292092c66f6ea57287d2b02a065a6d01dd6ab9ad", "size_in_bytes": 293200}], "paths_version": 1}, "requested_spec": "None", "sha256": "c4bb79d9e9be3e3a335282b50d18a7965e2a972b95508ea47e4086f1fd699342", "size": 1225193, "subdir": "osx-64", "timestamp": 1756238834000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libgfortran5-15.1.0-hfa3c126_1.conda", "version": "15.1.0"}