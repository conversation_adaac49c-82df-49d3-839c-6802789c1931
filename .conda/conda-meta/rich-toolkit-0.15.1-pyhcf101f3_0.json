{"build": "pyhcf101f3_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.10", "rich >=13.7.1", "click >=8.1.7", "typing_extensions >=4.12.2", "python"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/rich-toolkit-0.15.1-pyhcf101f3_0", "files": ["lib/python3.11/site-packages/rich_toolkit/__init__.py", "lib/python3.11/site-packages/rich_toolkit/_getchar.py", "lib/python3.11/site-packages/rich_toolkit/_input_handler.py", "lib/python3.11/site-packages/rich_toolkit/_rich_components.py", "lib/python3.11/site-packages/rich_toolkit/button.py", "lib/python3.11/site-packages/rich_toolkit/container.py", "lib/python3.11/site-packages/rich_toolkit/element.py", "lib/python3.11/site-packages/rich_toolkit/form.py", "lib/python3.11/site-packages/rich_toolkit/input.py", "lib/python3.11/site-packages/rich_toolkit/menu.py", "lib/python3.11/site-packages/rich_toolkit/progress.py", "lib/python3.11/site-packages/rich_toolkit/py.typed", "lib/python3.11/site-packages/rich_toolkit/spacer.py", "lib/python3.11/site-packages/rich_toolkit/styles/__init__.py", "lib/python3.11/site-packages/rich_toolkit/styles/base.py", "lib/python3.11/site-packages/rich_toolkit/styles/border.py", "lib/python3.11/site-packages/rich_toolkit/styles/fancy.py", "lib/python3.11/site-packages/rich_toolkit/styles/minimal.py", "lib/python3.11/site-packages/rich_toolkit/styles/tagged.py", "lib/python3.11/site-packages/rich_toolkit/toolkit.py", "lib/python3.11/site-packages/rich_toolkit/utils/__init__.py", "lib/python3.11/site-packages/rich_toolkit/utils/colors.py", "lib/python3.11/site-packages/rich_toolkit/utils/map_range.py", "lib/python3.11/site-packages/rich_toolkit-0.15.1.dist-info/INSTALLER", "lib/python3.11/site-packages/rich_toolkit-0.15.1.dist-info/METADATA", "lib/python3.11/site-packages/rich_toolkit-0.15.1.dist-info/RECORD", "lib/python3.11/site-packages/rich_toolkit-0.15.1.dist-info/REQUESTED", "lib/python3.11/site-packages/rich_toolkit-0.15.1.dist-info/WHEEL", "lib/python3.11/site-packages/rich_toolkit-0.15.1.dist-info/direct_url.json", "lib/python3.11/site-packages/rich_toolkit-0.15.1.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/rich_toolkit-0.15.1.dist-info/licenses/LICENSE-THIRD-PARTY", "lib/python3.11/site-packages/rich_toolkit/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/__pycache__/_getchar.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/__pycache__/_input_handler.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/__pycache__/_rich_components.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/__pycache__/button.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/__pycache__/container.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/__pycache__/element.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/__pycache__/form.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/__pycache__/input.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/__pycache__/menu.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/__pycache__/progress.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/__pycache__/spacer.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/styles/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/styles/__pycache__/base.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/styles/__pycache__/border.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/styles/__pycache__/fancy.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/styles/__pycache__/minimal.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/styles/__pycache__/tagged.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/__pycache__/toolkit.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/utils/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/utils/__pycache__/colors.cpython-311.pyc", "lib/python3.11/site-packages/rich_toolkit/utils/__pycache__/map_range.cpython-311.pyc"], "fn": "rich-toolkit-0.15.1-pyhcf101f3_0.conda", "license": "MIT", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/rich-toolkit-0.15.1-pyhcf101f3_0", "type": 1}, "md5": "12f69ed6e4115871451a3c7809b4651e", "name": "rich-toolkit", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/rich-toolkit-0.15.1-pyhcf101f3_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/rich_toolkit/__init__.py", "path_type": "hardlink", "sha256": "0ec7dd7fadd6fa3b4a4c4f6f9ccfd6dec64a37064ca131d92cfd8b3b8162d9bc", "sha256_in_prefix": "0ec7dd7fadd6fa3b4a4c4f6f9ccfd6dec64a37064ca131d92cfd8b3b8162d9bc", "size_in_bytes": 98}, {"_path": "site-packages/rich_toolkit/_getchar.py", "path_type": "hardlink", "sha256": "c6d75524fb877a1163f64ca0e846c3f0066f0f794dd3dfc0072bb807c3891fbc", "sha256_in_prefix": "c6d75524fb877a1163f64ca0e846c3f0066f0f794dd3dfc0072bb807c3891fbc", "size_in_bytes": 4841}, {"_path": "site-packages/rich_toolkit/_input_handler.py", "path_type": "hardlink", "sha256": "e0915a33cc81f234a01aa74ad3dfb1a345e8bc369a9fb4da1b16dc3c39341e31", "sha256_in_prefix": "e0915a33cc81f234a01aa74ad3dfb1a345e8bc369a9fb4da1b16dc3c39341e31", "size_in_bytes": 4422}, {"_path": "site-packages/rich_toolkit/_rich_components.py", "path_type": "hardlink", "sha256": "e4f0f603d84c8f57d416410fcaecf5436dd501252291a4917da0de611b717b24", "sha256_in_prefix": "e4f0f603d84c8f57d416410fcaecf5436dd501252291a4917da0de611b717b24", "size_in_bytes": 5423}, {"_path": "site-packages/rich_toolkit/button.py", "path_type": "hardlink", "sha256": "aad22559e12db6cce9147536e21798b78c16b8f30e38daf009cef4b297a937d4", "sha256_in_prefix": "aad22559e12db6cce9147536e21798b78c16b8f30e38daf009cef4b297a937d4", "size_in_bytes": 654}, {"_path": "site-packages/rich_toolkit/container.py", "path_type": "hardlink", "sha256": "88fb74514464a9213c1e97ec94a3936e14e4232b5f019f329dd0c47f504f4d41", "sha256_in_prefix": "88fb74514464a9213c1e97ec94a3936e14e4232b5f019f329dd0c47f504f4d41", "size_in_bytes": 5983}, {"_path": "site-packages/rich_toolkit/element.py", "path_type": "hardlink", "sha256": "37cc2c47f4108f7ba2a99353d4148b9ce438d4ce2f54a3ebd8edcf624c11e78e", "sha256_in_prefix": "37cc2c47f4108f7ba2a99353d4148b9ce438d4ce2f54a3ebd8edcf624c11e78e", "size_in_bytes": 940}, {"_path": "site-packages/rich_toolkit/form.py", "path_type": "hardlink", "sha256": "e357681796214d1b5833a0f503e86ab2eab72592e0a60d0cbda18aae8372c672", "sha256_in_prefix": "e357681796214d1b5833a0f503e86ab2eab72592e0a60d0cbda18aae8372c672", "size_in_bytes": 1887}, {"_path": "site-packages/rich_toolkit/input.py", "path_type": "hardlink", "sha256": "a1e2731d052b104adeaf7ce73f3a3d6d781bc8f494f9b7eb6b2c12cda463ff44", "sha256_in_prefix": "a1e2731d052b104adeaf7ce73f3a3d6d781bc8f494f9b7eb6b2c12cda463ff44", "size_in_bytes": 3144}, {"_path": "site-packages/rich_toolkit/menu.py", "path_type": "hardlink", "sha256": "0749ad0a9afd2a994f3c6e62e9df3894507b30c754e2c19e9b7024e533f72050", "sha256_in_prefix": "0749ad0a9afd2a994f3c6e62e9df3894507b30c754e2c19e9b7024e533f72050", "size_in_bytes": 5421}, {"_path": "site-packages/rich_toolkit/progress.py", "path_type": "hardlink", "sha256": "78ed8e2520800dea9f18ef6d1b292c61311c6099ac5bcc04deb36323e9c4a144", "sha256_in_prefix": "78ed8e2520800dea9f18ef6d1b292c61311c6099ac5bcc04deb36323e9c4a144", "size_in_bytes": 1931}, {"_path": "site-packages/rich_toolkit/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/rich_toolkit/spacer.py", "path_type": "hardlink", "sha256": "cbd8564ee7730730cf9e15f54b683c336bec8ad44b2ba7944e52cd4c546977cd", "sha256_in_prefix": "cbd8564ee7730730cf9e15f54b683c336bec8ad44b2ba7944e52cd4c546977cd", "size_in_bytes": 112}, {"_path": "site-packages/rich_toolkit/styles/__init__.py", "path_type": "hardlink", "sha256": "528e32f7485867b625d1a3637c070b05ce8246d93d779aa04bd99192e716df39", "sha256_in_prefix": "528e32f7485867b625d1a3637c070b05ce8246d93d779aa04bd99192e716df39", "size_in_bytes": 245}, {"_path": "site-packages/rich_toolkit/styles/base.py", "path_type": "hardlink", "sha256": "5d9e319bc366765ebd1695dc7012489cf6fe83fd0a049d4caeb5bf448c3ecae7", "sha256_in_prefix": "5d9e319bc366765ebd1695dc7012489cf6fe83fd0a049d4caeb5bf448c3ecae7", "size_in_bytes": 13419}, {"_path": "site-packages/rich_toolkit/styles/border.py", "path_type": "hardlink", "sha256": "8d30d30a855b7a5ded846702b5d6accc3d5a69a8056383a0b8c12a574f3ddac8", "sha256_in_prefix": "8d30d30a855b7a5ded846702b5d6accc3d5a69a8056383a0b8c12a574f3ddac8", "size_in_bytes": 7228}, {"_path": "site-packages/rich_toolkit/styles/fancy.py", "path_type": "hardlink", "sha256": "b6e351743b10fa89285aac38114ef56d2ac6e80d98551705c3f6375f0fba41cb", "sha256_in_prefix": "b6e351743b10fa89285aac38114ef56d2ac6e80d98551705c3f6375f0fba41cb", "size_in_bytes": 5079}, {"_path": "site-packages/rich_toolkit/styles/minimal.py", "path_type": "hardlink", "sha256": "f71d114f2fb24582c12d1ba70a316ac8e9f0d409fe5e4630d01ea2ce913d66c5", "sha256_in_prefix": "f71d114f2fb24582c12d1ba70a316ac8e9f0d409fe5e4630d01ea2ce913d66c5", "size_in_bytes": 70}, {"_path": "site-packages/rich_toolkit/styles/tagged.py", "path_type": "hardlink", "sha256": "302fe402ddff32efbc4d974a15edb96b735471f1670d200b26ab307230672896", "sha256_in_prefix": "302fe402ddff32efbc4d974a15edb96b735471f1670d200b26ab307230672896", "size_in_bytes": 4227}, {"_path": "site-packages/rich_toolkit/toolkit.py", "path_type": "hardlink", "sha256": "409b51dd08cce1ab2a39d7996f019549506cd199ae2a1747ab3306470ad399b4", "sha256_in_prefix": "409b51dd08cce1ab2a39d7996f019549506cd199ae2a1747ab3306470ad399b4", "size_in_bytes": 4198}, {"_path": "site-packages/rich_toolkit/utils/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/rich_toolkit/utils/colors.py", "path_type": "hardlink", "sha256": "709f286216bdb290c1815726afac0312ff6869c4c1cf5355fdc53084f1e09997", "sha256_in_prefix": "709f286216bdb290c1815726afac0312ff6869c4c1cf5355fdc53084f1e09997", "size_in_bytes": 6744}, {"_path": "site-packages/rich_toolkit/utils/map_range.py", "path_type": "hardlink", "sha256": "d053af1fa7d5a78902a696c9e126e8e6deb34320c41672b96edbf171c1abf5e2", "sha256_in_prefix": "d053af1fa7d5a78902a696c9e126e8e6deb34320c41672b96edbf171c1abf5e2", "size_in_bytes": 336}, {"_path": "site-packages/rich_toolkit-0.15.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "bc33022edcb7639ff53355b4e91dade50a0bbf0299efeb6171d1ec0ba5029cfc", "sha256_in_prefix": "bc33022edcb7639ff53355b4e91dade50a0bbf0299efeb6171d1ec0ba5029cfc", "size_in_bytes": 6}, {"_path": "site-packages/rich_toolkit-0.15.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "24a3b246ec98c36d45a18d71070578504f8c451f7d3b42415ae537ed0113c98a", "sha256_in_prefix": "24a3b246ec98c36d45a18d71070578504f8c451f7d3b42415ae537ed0113c98a", "size_in_bytes": 1033}, {"_path": "site-packages/rich_toolkit-0.15.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "c946dfaf558e921fdf633a06e4da4bb1192b4af9f15f9b53e5fd0d68e9f89615", "sha256_in_prefix": "c946dfaf558e921fdf633a06e4da4bb1192b4af9f15f9b53e5fd0d68e9f89615", "size_in_bytes": 3862}, {"_path": "site-packages/rich_toolkit-0.15.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/rich_toolkit-0.15.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "sha256_in_prefix": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "size_in_bytes": 87}, {"_path": "site-packages/rich_toolkit-0.15.1.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "1ef580129e3d6c00f500fe04a2a6f17290fa611cf01d330c45b2509b91733463", "sha256_in_prefix": "1ef580129e3d6c00f500fe04a2a6f17290fa611cf01d330c45b2509b91733463", "size_in_bytes": 123}, {"_path": "site-packages/rich_toolkit-0.15.1.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "71579251fc27ee0d14b1686288608807e3ca46bf85badf4648de4d283cd8a532", "sha256_in_prefix": "71579251fc27ee0d14b1686288608807e3ca46bf85badf4648de4d283cd8a532", "size_in_bytes": 1072}, {"_path": "site-packages/rich_toolkit-0.15.1.dist-info/licenses/LICENSE-THIRD-PARTY", "path_type": "hardlink", "sha256": "512631a0a7f2fad4e4d5f33e09851ea2afc0ce09d40e37c318df7f3187ccbe16", "sha256_in_prefix": "512631a0a7f2fad4e4d5f33e09851ea2afc0ce09d40e37c318df7f3187ccbe16", "size_in_bytes": 2864}, {"_path": "lib/python3.11/site-packages/rich_toolkit/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/__pycache__/_getchar.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/__pycache__/_input_handler.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/__pycache__/_rich_components.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/__pycache__/button.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/__pycache__/container.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/__pycache__/element.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/__pycache__/form.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/__pycache__/input.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/__pycache__/menu.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/__pycache__/progress.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/__pycache__/spacer.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/styles/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/styles/__pycache__/base.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/styles/__pycache__/border.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/styles/__pycache__/fancy.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/styles/__pycache__/minimal.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/styles/__pycache__/tagged.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/__pycache__/toolkit.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/utils/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/utils/__pycache__/colors.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/rich_toolkit/utils/__pycache__/map_range.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "7c8ffaa40bf4ba5fc6bb8f0e4b9da77678fe74cdb50ab82041d6a5e4a25f530b", "size": 29432, "subdir": "noarch", "timestamp": 1756998936000, "url": "https://conda.anaconda.org/conda-forge/noarch/rich-toolkit-0.15.1-pyhcf101f3_0.conda", "version": "0.15.1"}