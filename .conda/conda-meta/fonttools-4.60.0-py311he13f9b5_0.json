{"build": "py311he13f9b5_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13", "brotli", "munkres", "python >=3.11,<3.12.0a0", "python_abi 3.11.* *_cp311", "unicodedata2 >=15.1.0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/fonttools-4.60.0-py311he13f9b5_0", "files": ["bin/fonttools", "bin/pyftmerge", "bin/pyftsubset", "bin/ttx", "lib/python3.11/site-packages/fontTools/__init__.py", "lib/python3.11/site-packages/fontTools/__main__.py", "lib/python3.11/site-packages/fontTools/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/__pycache__/afmLib.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/__pycache__/agl.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/__pycache__/annotations.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/__pycache__/fontBuilder.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/__pycache__/help.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/__pycache__/tfmLib.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/__pycache__/ttx.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/__pycache__/unicode.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/afmLib.py", "lib/python3.11/site-packages/fontTools/agl.py", "lib/python3.11/site-packages/fontTools/annotations.py", "lib/python3.11/site-packages/fontTools/cffLib/CFF2ToCFF.py", "lib/python3.11/site-packages/fontTools/cffLib/CFFToCFF2.py", "lib/python3.11/site-packages/fontTools/cffLib/__init__.py", "lib/python3.11/site-packages/fontTools/cffLib/__pycache__/CFF2ToCFF.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/cffLib/__pycache__/CFFToCFF2.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/cffLib/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/cffLib/__pycache__/specializer.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/cffLib/__pycache__/transforms.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/cffLib/__pycache__/width.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/cffLib/specializer.py", "lib/python3.11/site-packages/fontTools/cffLib/transforms.py", "lib/python3.11/site-packages/fontTools/cffLib/width.py", "lib/python3.11/site-packages/fontTools/colorLib/__init__.py", "lib/python3.11/site-packages/fontTools/colorLib/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/colorLib/__pycache__/builder.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/colorLib/__pycache__/errors.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/colorLib/__pycache__/geometry.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/colorLib/__pycache__/table_builder.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/colorLib/__pycache__/unbuilder.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/colorLib/builder.py", "lib/python3.11/site-packages/fontTools/colorLib/errors.py", "lib/python3.11/site-packages/fontTools/colorLib/geometry.py", "lib/python3.11/site-packages/fontTools/colorLib/table_builder.py", "lib/python3.11/site-packages/fontTools/colorLib/unbuilder.py", "lib/python3.11/site-packages/fontTools/config/__init__.py", "lib/python3.11/site-packages/fontTools/config/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/cu2qu/__init__.py", "lib/python3.11/site-packages/fontTools/cu2qu/__main__.py", "lib/python3.11/site-packages/fontTools/cu2qu/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/cu2qu/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/cu2qu/__pycache__/benchmark.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/cu2qu/__pycache__/cli.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/cu2qu/__pycache__/cu2qu.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/cu2qu/__pycache__/errors.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/cu2qu/__pycache__/ufo.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/cu2qu/benchmark.py", "lib/python3.11/site-packages/fontTools/cu2qu/cli.py", "lib/python3.11/site-packages/fontTools/cu2qu/cu2qu.c", "lib/python3.11/site-packages/fontTools/cu2qu/cu2qu.cpython-311-darwin.so", "lib/python3.11/site-packages/fontTools/cu2qu/cu2qu.py", "lib/python3.11/site-packages/fontTools/cu2qu/errors.py", "lib/python3.11/site-packages/fontTools/cu2qu/ufo.py", "lib/python3.11/site-packages/fontTools/designspaceLib/__init__.py", "lib/python3.11/site-packages/fontTools/designspaceLib/__main__.py", "lib/python3.11/site-packages/fontTools/designspaceLib/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/designspaceLib/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/designspaceLib/__pycache__/split.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/designspaceLib/__pycache__/statNames.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/designspaceLib/__pycache__/types.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/designspaceLib/split.py", "lib/python3.11/site-packages/fontTools/designspaceLib/statNames.py", "lib/python3.11/site-packages/fontTools/designspaceLib/types.py", "lib/python3.11/site-packages/fontTools/encodings/MacRoman.py", "lib/python3.11/site-packages/fontTools/encodings/StandardEncoding.py", "lib/python3.11/site-packages/fontTools/encodings/__init__.py", "lib/python3.11/site-packages/fontTools/encodings/__pycache__/MacRoman.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/encodings/__pycache__/StandardEncoding.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/encodings/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/encodings/__pycache__/codecs.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/encodings/codecs.py", "lib/python3.11/site-packages/fontTools/feaLib/__init__.py", "lib/python3.11/site-packages/fontTools/feaLib/__main__.py", "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/ast.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/builder.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/error.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/lexer.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/location.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/lookupDebugInfo.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/parser.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/variableScalar.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/feaLib/ast.py", "lib/python3.11/site-packages/fontTools/feaLib/builder.py", "lib/python3.11/site-packages/fontTools/feaLib/error.py", "lib/python3.11/site-packages/fontTools/feaLib/lexer.c", "lib/python3.11/site-packages/fontTools/feaLib/lexer.cpython-311-darwin.so", "lib/python3.11/site-packages/fontTools/feaLib/lexer.py", "lib/python3.11/site-packages/fontTools/feaLib/location.py", "lib/python3.11/site-packages/fontTools/feaLib/lookupDebugInfo.py", "lib/python3.11/site-packages/fontTools/feaLib/parser.py", "lib/python3.11/site-packages/fontTools/feaLib/variableScalar.py", "lib/python3.11/site-packages/fontTools/fontBuilder.py", "lib/python3.11/site-packages/fontTools/help.py", "lib/python3.11/site-packages/fontTools/merge/__init__.py", "lib/python3.11/site-packages/fontTools/merge/__main__.py", "lib/python3.11/site-packages/fontTools/merge/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/merge/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/merge/__pycache__/base.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/merge/__pycache__/cmap.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/merge/__pycache__/layout.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/merge/__pycache__/options.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/merge/__pycache__/tables.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/merge/__pycache__/unicode.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/merge/__pycache__/util.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/merge/base.py", "lib/python3.11/site-packages/fontTools/merge/cmap.py", "lib/python3.11/site-packages/fontTools/merge/layout.py", "lib/python3.11/site-packages/fontTools/merge/options.py", "lib/python3.11/site-packages/fontTools/merge/tables.py", "lib/python3.11/site-packages/fontTools/merge/unicode.py", "lib/python3.11/site-packages/fontTools/merge/util.py", "lib/python3.11/site-packages/fontTools/misc/__init__.py", "lib/python3.11/site-packages/fontTools/misc/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/arrayTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/bezierTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/classifyTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/cliTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/configTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/cython.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/dictTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/eexec.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/encodingTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/enumTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/etree.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/filenames.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/fixedTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/intTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/iterTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/lazyTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/loggingTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/macCreatorType.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/macRes.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/psCharStrings.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/psLib.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/psOperators.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/py23.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/roundTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/sstruct.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/symfont.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/testTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/textTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/timeTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/transform.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/treeTools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/vector.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/visitor.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/xmlReader.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/__pycache__/xmlWriter.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/arrayTools.py", "lib/python3.11/site-packages/fontTools/misc/bezierTools.c", "lib/python3.11/site-packages/fontTools/misc/bezierTools.cpython-311-darwin.so", "lib/python3.11/site-packages/fontTools/misc/bezierTools.py", "lib/python3.11/site-packages/fontTools/misc/classifyTools.py", "lib/python3.11/site-packages/fontTools/misc/cliTools.py", "lib/python3.11/site-packages/fontTools/misc/configTools.py", "lib/python3.11/site-packages/fontTools/misc/cython.py", "lib/python3.11/site-packages/fontTools/misc/dictTools.py", "lib/python3.11/site-packages/fontTools/misc/eexec.py", "lib/python3.11/site-packages/fontTools/misc/encodingTools.py", "lib/python3.11/site-packages/fontTools/misc/enumTools.py", "lib/python3.11/site-packages/fontTools/misc/etree.py", "lib/python3.11/site-packages/fontTools/misc/filenames.py", "lib/python3.11/site-packages/fontTools/misc/filesystem/__init__.py", "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_base.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_copy.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_errors.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_info.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_osfs.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_path.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_subfs.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_tempfs.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_tools.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_walk.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_zipfs.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/filesystem/_base.py", "lib/python3.11/site-packages/fontTools/misc/filesystem/_copy.py", "lib/python3.11/site-packages/fontTools/misc/filesystem/_errors.py", "lib/python3.11/site-packages/fontTools/misc/filesystem/_info.py", "lib/python3.11/site-packages/fontTools/misc/filesystem/_osfs.py", "lib/python3.11/site-packages/fontTools/misc/filesystem/_path.py", "lib/python3.11/site-packages/fontTools/misc/filesystem/_subfs.py", "lib/python3.11/site-packages/fontTools/misc/filesystem/_tempfs.py", "lib/python3.11/site-packages/fontTools/misc/filesystem/_tools.py", "lib/python3.11/site-packages/fontTools/misc/filesystem/_walk.py", "lib/python3.11/site-packages/fontTools/misc/filesystem/_zipfs.py", "lib/python3.11/site-packages/fontTools/misc/fixedTools.py", "lib/python3.11/site-packages/fontTools/misc/intTools.py", "lib/python3.11/site-packages/fontTools/misc/iterTools.py", "lib/python3.11/site-packages/fontTools/misc/lazyTools.py", "lib/python3.11/site-packages/fontTools/misc/loggingTools.py", "lib/python3.11/site-packages/fontTools/misc/macCreatorType.py", "lib/python3.11/site-packages/fontTools/misc/macRes.py", "lib/python3.11/site-packages/fontTools/misc/plistlib/__init__.py", "lib/python3.11/site-packages/fontTools/misc/plistlib/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/misc/plistlib/py.typed", "lib/python3.11/site-packages/fontTools/misc/psCharStrings.py", "lib/python3.11/site-packages/fontTools/misc/psLib.py", "lib/python3.11/site-packages/fontTools/misc/psOperators.py", "lib/python3.11/site-packages/fontTools/misc/py23.py", "lib/python3.11/site-packages/fontTools/misc/roundTools.py", "lib/python3.11/site-packages/fontTools/misc/sstruct.py", "lib/python3.11/site-packages/fontTools/misc/symfont.py", "lib/python3.11/site-packages/fontTools/misc/testTools.py", "lib/python3.11/site-packages/fontTools/misc/textTools.py", "lib/python3.11/site-packages/fontTools/misc/timeTools.py", "lib/python3.11/site-packages/fontTools/misc/transform.py", "lib/python3.11/site-packages/fontTools/misc/treeTools.py", "lib/python3.11/site-packages/fontTools/misc/vector.py", "lib/python3.11/site-packages/fontTools/misc/visitor.py", "lib/python3.11/site-packages/fontTools/misc/xmlReader.py", "lib/python3.11/site-packages/fontTools/misc/xmlWriter.py", "lib/python3.11/site-packages/fontTools/mtiLib/__init__.py", "lib/python3.11/site-packages/fontTools/mtiLib/__main__.py", "lib/python3.11/site-packages/fontTools/mtiLib/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/mtiLib/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/otlLib/__init__.py", "lib/python3.11/site-packages/fontTools/otlLib/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/otlLib/__pycache__/builder.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/otlLib/__pycache__/error.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/otlLib/__pycache__/maxContextCalc.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/otlLib/builder.py", "lib/python3.11/site-packages/fontTools/otlLib/error.py", "lib/python3.11/site-packages/fontTools/otlLib/maxContextCalc.py", "lib/python3.11/site-packages/fontTools/otlLib/optimize/__init__.py", "lib/python3.11/site-packages/fontTools/otlLib/optimize/__main__.py", "lib/python3.11/site-packages/fontTools/otlLib/optimize/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/otlLib/optimize/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/otlLib/optimize/__pycache__/gpos.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/otlLib/optimize/gpos.py", "lib/python3.11/site-packages/fontTools/pens/__init__.py", "lib/python3.11/site-packages/fontTools/pens/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/areaPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/basePen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/boundsPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/cairoPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/cocoaPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/cu2quPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/explicitClosingLinePen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/filterPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/freetypePen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/hashPointPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/momentsPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/perimeterPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/pointInsidePen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/pointPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/qtPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/qu2cuPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/quartzPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/recordingPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/reportLabPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/reverseContourPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/roundingPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/statisticsPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/svgPathPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/t2CharStringPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/teePen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/transformPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/ttGlyphPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/__pycache__/wxPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/pens/areaPen.py", "lib/python3.11/site-packages/fontTools/pens/basePen.py", "lib/python3.11/site-packages/fontTools/pens/boundsPen.py", "lib/python3.11/site-packages/fontTools/pens/cairoPen.py", "lib/python3.11/site-packages/fontTools/pens/cocoaPen.py", "lib/python3.11/site-packages/fontTools/pens/cu2quPen.py", "lib/python3.11/site-packages/fontTools/pens/explicitClosingLinePen.py", "lib/python3.11/site-packages/fontTools/pens/filterPen.py", "lib/python3.11/site-packages/fontTools/pens/freetypePen.py", "lib/python3.11/site-packages/fontTools/pens/hashPointPen.py", "lib/python3.11/site-packages/fontTools/pens/momentsPen.c", "lib/python3.11/site-packages/fontTools/pens/momentsPen.cpython-311-darwin.so", "lib/python3.11/site-packages/fontTools/pens/momentsPen.py", "lib/python3.11/site-packages/fontTools/pens/perimeterPen.py", "lib/python3.11/site-packages/fontTools/pens/pointInsidePen.py", "lib/python3.11/site-packages/fontTools/pens/pointPen.py", "lib/python3.11/site-packages/fontTools/pens/qtPen.py", "lib/python3.11/site-packages/fontTools/pens/qu2cuPen.py", "lib/python3.11/site-packages/fontTools/pens/quartzPen.py", "lib/python3.11/site-packages/fontTools/pens/recordingPen.py", "lib/python3.11/site-packages/fontTools/pens/reportLabPen.py", "lib/python3.11/site-packages/fontTools/pens/reverseContourPen.py", "lib/python3.11/site-packages/fontTools/pens/roundingPen.py", "lib/python3.11/site-packages/fontTools/pens/statisticsPen.py", "lib/python3.11/site-packages/fontTools/pens/svgPathPen.py", "lib/python3.11/site-packages/fontTools/pens/t2CharStringPen.py", "lib/python3.11/site-packages/fontTools/pens/teePen.py", "lib/python3.11/site-packages/fontTools/pens/transformPen.py", "lib/python3.11/site-packages/fontTools/pens/ttGlyphPen.py", "lib/python3.11/site-packages/fontTools/pens/wxPen.py", "lib/python3.11/site-packages/fontTools/qu2cu/__init__.py", "lib/python3.11/site-packages/fontTools/qu2cu/__main__.py", "lib/python3.11/site-packages/fontTools/qu2cu/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/qu2cu/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/qu2cu/__pycache__/benchmark.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/qu2cu/__pycache__/cli.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/qu2cu/__pycache__/qu2cu.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/qu2cu/benchmark.py", "lib/python3.11/site-packages/fontTools/qu2cu/cli.py", "lib/python3.11/site-packages/fontTools/qu2cu/qu2cu.c", "lib/python3.11/site-packages/fontTools/qu2cu/qu2cu.cpython-311-darwin.so", "lib/python3.11/site-packages/fontTools/qu2cu/qu2cu.py", "lib/python3.11/site-packages/fontTools/subset/__init__.py", "lib/python3.11/site-packages/fontTools/subset/__main__.py", "lib/python3.11/site-packages/fontTools/subset/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/subset/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/subset/__pycache__/cff.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/subset/__pycache__/svg.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/subset/__pycache__/util.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/subset/cff.py", "lib/python3.11/site-packages/fontTools/subset/svg.py", "lib/python3.11/site-packages/fontTools/subset/util.py", "lib/python3.11/site-packages/fontTools/svgLib/__init__.py", "lib/python3.11/site-packages/fontTools/svgLib/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/svgLib/path/__init__.py", "lib/python3.11/site-packages/fontTools/svgLib/path/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/svgLib/path/__pycache__/arc.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/svgLib/path/__pycache__/parser.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/svgLib/path/__pycache__/shapes.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/svgLib/path/arc.py", "lib/python3.11/site-packages/fontTools/svgLib/path/parser.py", "lib/python3.11/site-packages/fontTools/svgLib/path/shapes.py", "lib/python3.11/site-packages/fontTools/t1Lib/__init__.py", "lib/python3.11/site-packages/fontTools/t1Lib/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/tfmLib.py", "lib/python3.11/site-packages/fontTools/ttLib/__init__.py", "lib/python3.11/site-packages/fontTools/ttLib/__main__.py", "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/macUtils.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/removeOverlaps.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/reorderGlyphs.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/scaleUpem.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/sfnt.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/standardGlyphOrder.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/ttCollection.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/ttFont.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/ttGlyphSet.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/ttVisitor.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/woff2.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/macUtils.py", "lib/python3.11/site-packages/fontTools/ttLib/removeOverlaps.py", "lib/python3.11/site-packages/fontTools/ttLib/reorderGlyphs.py", "lib/python3.11/site-packages/fontTools/ttLib/scaleUpem.py", "lib/python3.11/site-packages/fontTools/ttLib/sfnt.py", "lib/python3.11/site-packages/fontTools/ttLib/standardGlyphOrder.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/B_A_S_E_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/BitmapGlyphMetrics.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/C_B_D_T_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/C_B_L_C_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/C_F_F_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/C_F_F__2.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/C_O_L_R_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/C_P_A_L_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/D_S_I_G_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/D__e_b_g.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/DefaultTable.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/E_B_D_T_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/E_B_L_C_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/F_F_T_M_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/F__e_a_t.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/G_D_E_F_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/G_M_A_P_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/G_P_K_G_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/G_P_O_S_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/G_S_U_B_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/G_V_A_R_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/G__l_a_t.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/G__l_o_c.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/H_V_A_R_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/J_S_T_F_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/L_T_S_H_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/M_A_T_H_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/M_E_T_A_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/M_V_A_R_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/O_S_2f_2.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/S_I_N_G_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/S_T_A_T_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/S_V_G_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/S__i_l_f.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/S__i_l_l.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I_B_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I_C_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I_D_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I_J_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I_P_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I_S_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I_V_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I__0.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I__1.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I__2.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I__3.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I__5.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/T_T_F_A_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/TupleVariation.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/V_A_R_C_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/V_D_M_X_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/V_O_R_G_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/V_V_A_R_.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/__init__.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/B_A_S_E_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/BitmapGlyphMetrics.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/C_B_D_T_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/C_B_L_C_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/C_F_F_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/C_F_F__2.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/C_O_L_R_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/C_P_A_L_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/D_S_I_G_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/D__e_b_g.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/DefaultTable.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/E_B_D_T_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/E_B_L_C_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/F_F_T_M_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/F__e_a_t.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/G_D_E_F_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/G_M_A_P_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/G_P_K_G_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/G_P_O_S_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/G_S_U_B_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/G_V_A_R_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/G__l_a_t.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/G__l_o_c.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/H_V_A_R_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/J_S_T_F_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/L_T_S_H_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/M_A_T_H_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/M_E_T_A_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/M_V_A_R_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/O_S_2f_2.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/S_I_N_G_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/S_T_A_T_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/S_V_G_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/S__i_l_f.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/S__i_l_l.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I_B_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I_C_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I_D_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I_J_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I_P_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I_S_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I_V_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I__0.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I__1.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I__2.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I__3.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I__5.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_T_F_A_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/TupleVariation.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/V_A_R_C_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/V_D_M_X_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/V_O_R_G_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/V_V_A_R_.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_a_n_k_r.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_a_v_a_r.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_b_s_l_n.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_c_i_d_g.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_c_m_a_p.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_c_v_a_r.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_c_v_t.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_f_e_a_t.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_f_p_g_m.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_f_v_a_r.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_g_a_s_p.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_g_c_i_d.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_g_l_y_f.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_g_v_a_r.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_h_d_m_x.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_h_e_a_d.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_h_h_e_a.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_h_m_t_x.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_k_e_r_n.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_l_c_a_r.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_l_o_c_a.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_l_t_a_g.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_m_a_x_p.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_m_e_t_a.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_m_o_r_t.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_m_o_r_x.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_n_a_m_e.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_o_p_b_d.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_p_o_s_t.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_p_r_e_p.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_p_r_o_p.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_s_b_i_x.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_t_r_a_k.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_v_h_e_a.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_v_m_t_x.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/asciiTable.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/grUtils.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/otBase.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/otConverters.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/otData.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/otTables.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/otTraverse.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/sbixGlyph.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/sbixStrike.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/ttProgram.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ttLib/tables/_a_n_k_r.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_a_v_a_r.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_b_s_l_n.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_c_i_d_g.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_c_m_a_p.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_c_v_a_r.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_c_v_t.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_f_e_a_t.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_f_p_g_m.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_f_v_a_r.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_g_a_s_p.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_g_c_i_d.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_g_l_y_f.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_g_v_a_r.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_h_d_m_x.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_h_e_a_d.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_h_h_e_a.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_h_m_t_x.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_k_e_r_n.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_l_c_a_r.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_l_o_c_a.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_l_t_a_g.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_m_a_x_p.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_m_e_t_a.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_m_o_r_t.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_m_o_r_x.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_n_a_m_e.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_o_p_b_d.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_p_o_s_t.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_p_r_e_p.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_p_r_o_p.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_s_b_i_x.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_t_r_a_k.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_v_h_e_a.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/_v_m_t_x.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/asciiTable.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/grUtils.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/otBase.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/otConverters.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/otData.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/otTables.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/otTraverse.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/sbixGlyph.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/sbixStrike.py", "lib/python3.11/site-packages/fontTools/ttLib/tables/table_API_readme.txt", "lib/python3.11/site-packages/fontTools/ttLib/tables/ttProgram.py", "lib/python3.11/site-packages/fontTools/ttLib/ttCollection.py", "lib/python3.11/site-packages/fontTools/ttLib/ttFont.py", "lib/python3.11/site-packages/fontTools/ttLib/ttGlyphSet.py", "lib/python3.11/site-packages/fontTools/ttLib/ttVisitor.py", "lib/python3.11/site-packages/fontTools/ttLib/woff2.py", "lib/python3.11/site-packages/fontTools/ttx.py", "lib/python3.11/site-packages/fontTools/ufoLib/__init__.py", "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/converters.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/errors.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/etree.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/filenames.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/glifLib.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/kerning.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/plistlib.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/pointPen.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/validators.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/ufoLib/converters.py", "lib/python3.11/site-packages/fontTools/ufoLib/errors.py", "lib/python3.11/site-packages/fontTools/ufoLib/etree.py", "lib/python3.11/site-packages/fontTools/ufoLib/filenames.py", "lib/python3.11/site-packages/fontTools/ufoLib/glifLib.py", "lib/python3.11/site-packages/fontTools/ufoLib/kerning.py", "lib/python3.11/site-packages/fontTools/ufoLib/plistlib.py", "lib/python3.11/site-packages/fontTools/ufoLib/pointPen.py", "lib/python3.11/site-packages/fontTools/ufoLib/utils.py", "lib/python3.11/site-packages/fontTools/ufoLib/validators.py", "lib/python3.11/site-packages/fontTools/unicode.py", "lib/python3.11/site-packages/fontTools/unicodedata/Blocks.py", "lib/python3.11/site-packages/fontTools/unicodedata/Mirrored.py", "lib/python3.11/site-packages/fontTools/unicodedata/OTTags.py", "lib/python3.11/site-packages/fontTools/unicodedata/ScriptExtensions.py", "lib/python3.11/site-packages/fontTools/unicodedata/Scripts.py", "lib/python3.11/site-packages/fontTools/unicodedata/__init__.py", "lib/python3.11/site-packages/fontTools/unicodedata/__pycache__/Blocks.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/unicodedata/__pycache__/Mirrored.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/unicodedata/__pycache__/OTTags.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/unicodedata/__pycache__/ScriptExtensions.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/unicodedata/__pycache__/Scripts.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/unicodedata/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__init__.py", "lib/python3.11/site-packages/fontTools/varLib/__main__.py", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/avarPlanner.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/builder.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/cff.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/errors.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/featureVars.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/hvar.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/interpolatable.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/interpolatableHelpers.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/interpolatablePlot.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/interpolatableTestContourOrder.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/interpolatableTestStartingPoint.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/interpolate_layout.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/iup.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/merger.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/models.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/multiVarStore.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/mutator.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/mvar.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/plot.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/stat.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/__pycache__/varStore.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/avar/__init__.py", "lib/python3.11/site-packages/fontTools/varLib/avar/__main__.py", "lib/python3.11/site-packages/fontTools/varLib/avar/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/avar/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/avar/__pycache__/build.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/avar/__pycache__/map.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/avar/__pycache__/plan.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/avar/__pycache__/unbuild.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/avar/build.py", "lib/python3.11/site-packages/fontTools/varLib/avar/map.py", "lib/python3.11/site-packages/fontTools/varLib/avar/plan.py", "lib/python3.11/site-packages/fontTools/varLib/avar/unbuild.py", "lib/python3.11/site-packages/fontTools/varLib/avarPlanner.py", "lib/python3.11/site-packages/fontTools/varLib/builder.py", "lib/python3.11/site-packages/fontTools/varLib/cff.py", "lib/python3.11/site-packages/fontTools/varLib/errors.py", "lib/python3.11/site-packages/fontTools/varLib/featureVars.py", "lib/python3.11/site-packages/fontTools/varLib/hvar.py", "lib/python3.11/site-packages/fontTools/varLib/instancer/__init__.py", "lib/python3.11/site-packages/fontTools/varLib/instancer/__main__.py", "lib/python3.11/site-packages/fontTools/varLib/instancer/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/instancer/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/instancer/__pycache__/featureVars.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/instancer/__pycache__/names.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/instancer/__pycache__/solver.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/varLib/instancer/featureVars.py", "lib/python3.11/site-packages/fontTools/varLib/instancer/names.py", "lib/python3.11/site-packages/fontTools/varLib/instancer/solver.py", "lib/python3.11/site-packages/fontTools/varLib/interpolatable.py", "lib/python3.11/site-packages/fontTools/varLib/interpolatableHelpers.py", "lib/python3.11/site-packages/fontTools/varLib/interpolatablePlot.py", "lib/python3.11/site-packages/fontTools/varLib/interpolatableTestContourOrder.py", "lib/python3.11/site-packages/fontTools/varLib/interpolatableTestStartingPoint.py", "lib/python3.11/site-packages/fontTools/varLib/interpolate_layout.py", "lib/python3.11/site-packages/fontTools/varLib/iup.c", "lib/python3.11/site-packages/fontTools/varLib/iup.cpython-311-darwin.so", "lib/python3.11/site-packages/fontTools/varLib/iup.py", "lib/python3.11/site-packages/fontTools/varLib/merger.py", "lib/python3.11/site-packages/fontTools/varLib/models.py", "lib/python3.11/site-packages/fontTools/varLib/multiVarStore.py", "lib/python3.11/site-packages/fontTools/varLib/mutator.py", "lib/python3.11/site-packages/fontTools/varLib/mvar.py", "lib/python3.11/site-packages/fontTools/varLib/plot.py", "lib/python3.11/site-packages/fontTools/varLib/stat.py", "lib/python3.11/site-packages/fontTools/varLib/varStore.py", "lib/python3.11/site-packages/fontTools/voltLib/__init__.py", "lib/python3.11/site-packages/fontTools/voltLib/__main__.py", "lib/python3.11/site-packages/fontTools/voltLib/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/voltLib/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/voltLib/__pycache__/ast.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/voltLib/__pycache__/error.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/voltLib/__pycache__/lexer.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/voltLib/__pycache__/parser.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/voltLib/__pycache__/voltToFea.cpython-311.pyc", "lib/python3.11/site-packages/fontTools/voltLib/ast.py", "lib/python3.11/site-packages/fontTools/voltLib/error.py", "lib/python3.11/site-packages/fontTools/voltLib/lexer.py", "lib/python3.11/site-packages/fontTools/voltLib/parser.py", "lib/python3.11/site-packages/fontTools/voltLib/voltToFea.py", "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/INSTALLER", "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/METADATA", "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/RECORD", "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/REQUESTED", "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/WHEEL", "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/direct_url.json", "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/entry_points.txt", "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/licenses/LICENSE.external", "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/top_level.txt", "share/man/man1/ttx.1"], "fn": "fonttools-4.60.0-py311he13f9b5_0.conda", "license": "MIT", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/fonttools-4.60.0-py311he13f9b5_0", "type": 1}, "md5": "d3c136bd22e73f51f446fa42cba15bc1", "name": "fonttools", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/fonttools-4.60.0-py311he13f9b5_0.conda", "paths_data": {"paths": [{"_path": "bin/fonttools", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/fonttools_1758132623042/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold", "sha256": "dac8f8fa9c54d2195e1a4f6c6299da9580dcbb387ddd69b8786fb43d94450190", "sha256_in_prefix": "78f0c8613294e8ba41844b4594d77163f4d90c7d2d5e16d6dde431c81acc8a8b", "size_in_bytes": 471}, {"_path": "bin/pyftmerge", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/fonttools_1758132623042/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold", "sha256": "24ed3a0d6e87faa0b74351c0ff194fbbb58a283ce3b079d2965d60fe09c07822", "sha256_in_prefix": "e5c42ec05aa73b4aa25f941e6780b6342237c2befa3fbb746273ae51d7db877b", "size_in_bytes": 468}, {"_path": "bin/pyftsubset", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/fonttools_1758132623042/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold", "sha256": "b6c517c2766baf0424d4a2ee4c0a7810ac4f2b563e81a78216a77cbad06cf277", "sha256_in_prefix": "762a7ad1ebe9fab75c878eba84578927a0e49d77f630b453dcdb373321a23fc6", "size_in_bytes": 469}, {"_path": "bin/ttx", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/fonttools_1758132623042/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold", "sha256": "7625911cc77a6284ee3721a56f7b2283201f66f3d2c7e75b6fe633117d5e7199", "sha256_in_prefix": "8d93cd3bd12589aad6313b56664312ee0dccbe9bbe5981f0f328f1687eb75839", "size_in_bytes": 466}, {"_path": "lib/python3.11/site-packages/fontTools/__init__.py", "path_type": "hardlink", "sha256": "404ac35b02f2b5eaa1a43dd315ccb27f91e2826c2ff8d82841c5df20e17ceb63", "sha256_in_prefix": "404ac35b02f2b5eaa1a43dd315ccb27f91e2826c2ff8d82841c5df20e17ceb63", "size_in_bytes": 183}, {"_path": "lib/python3.11/site-packages/fontTools/__main__.py", "path_type": "hardlink", "sha256": "563906875503fa2d734c3035757a35b9e712b3a3f11dd190e6f94293f982098b", "sha256_in_prefix": "563906875503fa2d734c3035757a35b9e712b3a3f11dd190e6f94293f982098b", "size_in_bytes": 925}, {"_path": "lib/python3.11/site-packages/fontTools/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "d6247dcde28ce292c8547db1d726a4ec499955e9c82f260473956946db2a03f7", "sha256_in_prefix": "d6247dcde28ce292c8547db1d726a4ec499955e9c82f260473956946db2a03f7", "size_in_bytes": 430}, {"_path": "lib/python3.11/site-packages/fontTools/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "700c05644fa030ca4be4b7bf23fa4ff7972e0e5626d6b6a2035274c729cdada3", "sha256_in_prefix": "700c05644fa030ca4be4b7bf23fa4ff7972e0e5626d6b6a2035274c729cdada3", "size_in_bytes": 1266}, {"_path": "lib/python3.11/site-packages/fontTools/__pycache__/afmLib.cpython-311.pyc", "path_type": "hardlink", "sha256": "bf02eb7fc7f63e72d53a83e7b870c0f79b7948338a894ae51e48333c31eac1d2", "sha256_in_prefix": "bf02eb7fc7f63e72d53a83e7b870c0f79b7948338a894ae51e48333c31eac1d2", "size_in_bytes": 18820}, {"_path": "lib/python3.11/site-packages/fontTools/__pycache__/agl.cpython-311.pyc", "path_type": "hardlink", "sha256": "a35e77c343b58549170df84bbc067cd0b1c160b2509172c4e2e1beae808ecc0c", "sha256_in_prefix": "a35e77c343b58549170df84bbc067cd0b1c160b2509172c4e2e1beae808ecc0c", "size_in_bytes": 114192}, {"_path": "lib/python3.11/site-packages/fontTools/__pycache__/annotations.cpython-311.pyc", "path_type": "hardlink", "sha256": "90e9fb3583edcb6b4c379fe9151b86e9d1e8bbed595f8149175ebe2e8b142260", "sha256_in_prefix": "90e9fb3583edcb6b4c379fe9151b86e9d1e8bbed595f8149175ebe2e8b142260", "size_in_bytes": 1977}, {"_path": "lib/python3.11/site-packages/fontTools/__pycache__/fontBuilder.cpython-311.pyc", "path_type": "hardlink", "sha256": "206defc7c10f4f237a3ae45d30dcab71f97fac72bb142579ccfea4f7c171401c", "sha256_in_prefix": "206defc7c10f4f237a3ae45d30dcab71f97fac72bb142579ccfea4f7c171401c", "size_in_bytes": 39550}, {"_path": "lib/python3.11/site-packages/fontTools/__pycache__/help.cpython-311.pyc", "path_type": "hardlink", "sha256": "0fc45678d3197d692d210c6f7be31847b5f8bfe8f554319540f8819126433d66", "sha256_in_prefix": "0fc45678d3197d692d210c6f7be31847b5f8bfe8f554319540f8819126433d66", "size_in_bytes": 2248}, {"_path": "lib/python3.11/site-packages/fontTools/__pycache__/tfmLib.cpython-311.pyc", "path_type": "hardlink", "sha256": "42bc19b246497472a78af7d881bba1e39f00579f6e9c5ffc72a0842d539e95e2", "sha256_in_prefix": "42bc19b246497472a78af7d881bba1e39f00579f6e9c5ffc72a0842d539e95e2", "size_in_bytes": 19595}, {"_path": "lib/python3.11/site-packages/fontTools/__pycache__/ttx.cpython-311.pyc", "path_type": "hardlink", "sha256": "2542c3a68a67ece648cb42696721ada7b9d2f07ac3306a1a3b13d0784f0dce68", "sha256_in_prefix": "2542c3a68a67ece648cb42696721ada7b9d2f07ac3306a1a3b13d0784f0dce68", "size_in_bytes": 20733}, {"_path": "lib/python3.11/site-packages/fontTools/__pycache__/unicode.cpython-311.pyc", "path_type": "hardlink", "sha256": "fe593f1e8d005810e146f232328536818aca5447ae3b8dc0dd18617098d86b07", "sha256_in_prefix": "fe593f1e8d005810e146f232328536818aca5447ae3b8dc0dd18617098d86b07", "size_in_bytes": 2751}, {"_path": "lib/python3.11/site-packages/fontTools/afmLib.py", "path_type": "hardlink", "sha256": "d4c6a0208b4ecd1578bd5e6428fc5e0d96cf26c7f12c1df07472c5910be5d2e9", "sha256_in_prefix": "d4c6a0208b4ecd1578bd5e6428fc5e0d96cf26c7f12c1df07472c5910be5d2e9", "size_in_bytes": 13164}, {"_path": "lib/python3.11/site-packages/fontTools/agl.py", "path_type": "hardlink", "sha256": "d396e6f14ab8e6e5565bc9cf6cfeb16cd826172c50afcb168580223f45528e72", "sha256_in_prefix": "d396e6f14ab8e6e5565bc9cf6cfeb16cd826172c50afcb168580223f45528e72", "size_in_bytes": 112975}, {"_path": "lib/python3.11/site-packages/fontTools/annotations.py", "path_type": "hardlink", "sha256": "05d208ae23580f305f8278b058583fbbbd6a2d99d5d2c09c9f8f950245e460d3", "sha256_in_prefix": "05d208ae23580f305f8278b058583fbbbd6a2d99d5d2c09c9f8f950245e460d3", "size_in_bytes": 1225}, {"_path": "lib/python3.11/site-packages/fontTools/cffLib/CFF2ToCFF.py", "path_type": "hardlink", "sha256": "9dcede4d6f0d0cc2c7090ce914c24a5c99c3083e4d03011eced1d21edff1112d", "sha256_in_prefix": "9dcede4d6f0d0cc2c7090ce914c24a5c99c3083e4d03011eced1d21edff1112d", "size_in_bytes": 7424}, {"_path": "lib/python3.11/site-packages/fontTools/cffLib/CFFToCFF2.py", "path_type": "hardlink", "sha256": "42793b95896c4d11e795943a35735daff7f8309c19436d64712d3c2856ecc98f", "sha256_in_prefix": "42793b95896c4d11e795943a35735daff7f8309c19436d64712d3c2856ecc98f", "size_in_bytes": 10119}, {"_path": "lib/python3.11/site-packages/fontTools/cffLib/__init__.py", "path_type": "hardlink", "sha256": "eb6be9711eeef1c138d3b91776e0309c5b6d1e7b280a90d0ec804afaa398150f", "sha256_in_prefix": "eb6be9711eeef1c138d3b91776e0309c5b6d1e7b280a90d0ec804afaa398150f", "size_in_bytes": 107886}, {"_path": "lib/python3.11/site-packages/fontTools/cffLib/__pycache__/CFF2ToCFF.cpython-311.pyc", "path_type": "hardlink", "sha256": "85b037a47a1886e440f67c9b373dc70c6aec078a007de8c15b13424e2fe678f1", "sha256_in_prefix": "85b037a47a1886e440f67c9b373dc70c6aec078a007de8c15b13424e2fe678f1", "size_in_bytes": 10906}, {"_path": "lib/python3.11/site-packages/fontTools/cffLib/__pycache__/CFFToCFF2.cpython-311.pyc", "path_type": "hardlink", "sha256": "4ce34c31a5e6d0d5738441f9b747f2eed2bc7a0efe4fa84508d83201a3b888b8", "sha256_in_prefix": "4ce34c31a5e6d0d5738441f9b747f2eed2bc7a0efe4fa84508d83201a3b888b8", "size_in_bytes": 11358}, {"_path": "lib/python3.11/site-packages/fontTools/cffLib/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "b7ba6ce934483c6a2ab4d23c6906e20fa40d63847b4f9273b9dc99d183aeeb54", "sha256_in_prefix": "b7ba6ce934483c6a2ab4d23c6906e20fa40d63847b4f9273b9dc99d183aeeb54", "size_in_bytes": 139029}, {"_path": "lib/python3.11/site-packages/fontTools/cffLib/__pycache__/specializer.cpython-311.pyc", "path_type": "hardlink", "sha256": "850914a174ac9a5ed713047a44426e3e473a0bc1f0e052cad50cd0bb5beacca7", "sha256_in_prefix": "850914a174ac9a5ed713047a44426e3e473a0bc1f0e052cad50cd0bb5beacca7", "size_in_bytes": 34847}, {"_path": "lib/python3.11/site-packages/fontTools/cffLib/__pycache__/transforms.cpython-311.pyc", "path_type": "hardlink", "sha256": "304f2dd201c2247868e1dca3b63cd5ed5b67cfa403254052f874142a23e60006", "sha256_in_prefix": "304f2dd201c2247868e1dca3b63cd5ed5b67cfa403254052f874142a23e60006", "size_in_bytes": 22774}, {"_path": "lib/python3.11/site-packages/fontTools/cffLib/__pycache__/width.cpython-311.pyc", "path_type": "hardlink", "sha256": "0bf46aeb3b15c379ce55957c8ddae89811d423a1af5fcfc84ec4d689e33f4724", "sha256_in_prefix": "0bf46aeb3b15c379ce55957c8ddae89811d423a1af5fcfc84ec4d689e33f4724", "size_in_bytes": 11444}, {"_path": "lib/python3.11/site-packages/fontTools/cffLib/specializer.py", "path_type": "hardlink", "sha256": "bec38f911fe31cd7bab444901239a6d22efacbbb1623930aa376ec4e6237b0d3", "sha256_in_prefix": "bec38f911fe31cd7bab444901239a6d22efacbbb1623930aa376ec4e6237b0d3", "size_in_bytes": 32609}, {"_path": "lib/python3.11/site-packages/fontTools/cffLib/transforms.py", "path_type": "hardlink", "sha256": "4842197355f15988955d506ca0d9ba2d3bccf5250ded9efa40e692025475642a", "sha256_in_prefix": "4842197355f15988955d506ca0d9ba2d3bccf5250ded9efa40e692025475642a", "size_in_bytes": 17455}, {"_path": "lib/python3.11/site-packages/fontTools/cffLib/width.py", "path_type": "hardlink", "sha256": "22a18bd022f2099aa2fe1bec1f2486d3caa96314b791aa96fadb004fe6e31d5e", "sha256_in_prefix": "22a18bd022f2099aa2fe1bec1f2486d3caa96314b791aa96fadb004fe6e31d5e", "size_in_bytes": 6074}, {"_path": "lib/python3.11/site-packages/fontTools/colorLib/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/fontTools/colorLib/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "cef9a7fa7146fe0f7ec7d64ce6dec33a8434c0663029d67c6a8aaca80ee11daa", "sha256_in_prefix": "cef9a7fa7146fe0f7ec7d64ce6dec33a8434c0663029d67c6a8aaca80ee11daa", "size_in_bytes": 168}, {"_path": "lib/python3.11/site-packages/fontTools/colorLib/__pycache__/builder.cpython-311.pyc", "path_type": "hardlink", "sha256": "82d4e31427655e9b7c8cc3f6a91b9400059b02adc85634a09acf4ad218ae0c04", "sha256_in_prefix": "82d4e31427655e9b7c8cc3f6a91b9400059b02adc85634a09acf4ad218ae0c04", "size_in_bytes": 33595}, {"_path": "lib/python3.11/site-packages/fontTools/colorLib/__pycache__/errors.cpython-311.pyc", "path_type": "hardlink", "sha256": "c796b1011b654964ca63cda215ac2509e7fd288b07ea1242377348927a8434f9", "sha256_in_prefix": "c796b1011b654964ca63cda215ac2509e7fd288b07ea1242377348927a8434f9", "size_in_bytes": 404}, {"_path": "lib/python3.11/site-packages/fontTools/colorLib/__pycache__/geometry.cpython-311.pyc", "path_type": "hardlink", "sha256": "19935fbc655181a416fd3077becf10dc9988470b2d91fc3acfe0c5a9a44ae967", "sha256_in_prefix": "19935fbc655181a416fd3077becf10dc9988470b2d91fc3acfe0c5a9a44ae967", "size_in_bytes": 5859}, {"_path": "lib/python3.11/site-packages/fontTools/colorLib/__pycache__/table_builder.cpython-311.pyc", "path_type": "hardlink", "sha256": "af1dba6386465ac3975dd2b7c93143517844ae4d5ee4214a868e9236204b73c6", "sha256_in_prefix": "af1dba6386465ac3975dd2b7c93143517844ae4d5ee4214a868e9236204b73c6", "size_in_bytes": 11729}, {"_path": "lib/python3.11/site-packages/fontTools/colorLib/__pycache__/unbuilder.cpython-311.pyc", "path_type": "hardlink", "sha256": "6ca158946650234c6ec0df56f5d5bd0d043f8c065a0ae82c20601c3cd2defd7b", "sha256_in_prefix": "6ca158946650234c6ec0df56f5d5bd0d043f8c065a0ae82c20601c3cd2defd7b", "size_in_bytes": 4217}, {"_path": "lib/python3.11/site-packages/fontTools/colorLib/builder.py", "path_type": "hardlink", "sha256": "9263bb3aeb9d4106f77c4392eda2f38130d58ea4bd8b6c4842693da75b817bc0", "sha256_in_prefix": "9263bb3aeb9d4106f77c4392eda2f38130d58ea4bd8b6c4842693da75b817bc0", "size_in_bytes": 23008}, {"_path": "lib/python3.11/site-packages/fontTools/colorLib/errors.py", "path_type": "hardlink", "sha256": "0ac6af8a2471c6ba60557e1b966eca0b22bce79de58f02f8e3190939e0b953b5", "sha256_in_prefix": "0ac6af8a2471c6ba60557e1b966eca0b22bce79de58f02f8e3190939e0b953b5", "size_in_bytes": 41}, {"_path": "lib/python3.11/site-packages/fontTools/colorLib/geometry.py", "path_type": "hardlink", "sha256": "dd27324ab47660325aedde4ae7fc4ce58b75fb734257eaf2f2291803957055b2", "sha256_in_prefix": "dd27324ab47660325aedde4ae7fc4ce58b75fb734257eaf2f2291803957055b2", "size_in_bytes": 5518}, {"_path": "lib/python3.11/site-packages/fontTools/colorLib/table_builder.py", "path_type": "hardlink", "sha256": "65e96d598ea7f983e226ffe14358815e8105006ef410ac59c92720b0c2941465", "sha256_in_prefix": "65e96d598ea7f983e226ffe14358815e8105006ef410ac59c92720b0c2941465", "size_in_bytes": 7469}, {"_path": "lib/python3.11/site-packages/fontTools/colorLib/unbuilder.py", "path_type": "hardlink", "sha256": "896f84e48dfd5ac57cd8adcd8023b80a3cf09b55aacc6aedca91edf1ea706c73", "sha256_in_prefix": "896f84e48dfd5ac57cd8adcd8023b80a3cf09b55aacc6aedca91edf1ea706c73", "size_in_bytes": 2142}, {"_path": "lib/python3.11/site-packages/fontTools/config/__init__.py", "path_type": "hardlink", "sha256": "24808e1c8cf4e8ab870a2066af18fe81ad7d3fa653b8b5e1d251e63e1f916b5c", "sha256_in_prefix": "24808e1c8cf4e8ab870a2066af18fe81ad7d3fa653b8b5e1d251e63e1f916b5c", "size_in_bytes": 3154}, {"_path": "lib/python3.11/site-packages/fontTools/config/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "c6c590b0a748d4e3c1b5b96cac78b1eb9138658868abd38a900b313f51f8faca", "sha256_in_prefix": "c6c590b0a748d4e3c1b5b96cac78b1eb9138658868abd38a900b313f51f8faca", "size_in_bytes": 3834}, {"_path": "lib/python3.11/site-packages/fontTools/cu2qu/__init__.py", "path_type": "hardlink", "sha256": "0ae73b52095bd274a06ab693c5763927c6d17b39c7e700295b4b9a90deccc9c6", "sha256_in_prefix": "0ae73b52095bd274a06ab693c5763927c6d17b39c7e700295b4b9a90deccc9c6", "size_in_bytes": 618}, {"_path": "lib/python3.11/site-packages/fontTools/cu2qu/__main__.py", "path_type": "hardlink", "sha256": "913508fa3733b0779e9542cb968af2ef84047858d95a9f738a009cecfadd5506", "sha256_in_prefix": "913508fa3733b0779e9542cb968af2ef84047858d95a9f738a009cecfadd5506", "size_in_bytes": 92}, {"_path": "lib/python3.11/site-packages/fontTools/cu2qu/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "1215aeccb619798be484c0cd354086484ed906ed7f713c9cf12e6f8ced082c41", "sha256_in_prefix": "1215aeccb619798be484c0cd354086484ed906ed7f713c9cf12e6f8ced082c41", "size_in_bytes": 197}, {"_path": "lib/python3.11/site-packages/fontTools/cu2qu/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "5a4e599039917413964108909542c32ef777c515eabf57ad04ce2c98333b5d6d", "sha256_in_prefix": "5a4e599039917413964108909542c32ef777c515eabf57ad04ce2c98333b5d6d", "size_in_bytes": 380}, {"_path": "lib/python3.11/site-packages/fontTools/cu2qu/__pycache__/benchmark.cpython-311.pyc", "path_type": "hardlink", "sha256": "69b703c7041ffc3aa7332cec674e26c6f03c12231d6fe161d5b39fed16f0150a", "sha256_in_prefix": "69b703c7041ffc3aa7332cec674e26c6f03c12231d6fe161d5b39fed16f0150a", "size_in_bytes": 3460}, {"_path": "lib/python3.11/site-packages/fontTools/cu2qu/__pycache__/cli.cpython-311.pyc", "path_type": "hardlink", "sha256": "7801c0bb99e463a6684fc47ffb0aa8205be424cfeaf50ce1b01cf1602e7345a2", "sha256_in_prefix": "7801c0bb99e463a6684fc47ffb0aa8205be424cfeaf50ce1b01cf1602e7345a2", "size_in_bytes": 9763}, {"_path": "lib/python3.11/site-packages/fontTools/cu2qu/__pycache__/cu2qu.cpython-311.pyc", "path_type": "hardlink", "sha256": "3fc8a5245f30fcbb7810c2dd50209c62e9726483a0a7a30cfef2bbed152d21ef", "sha256_in_prefix": "3fc8a5245f30fcbb7810c2dd50209c62e9726483a0a7a30cfef2bbed152d21ef", "size_in_bytes": 23840}, {"_path": "lib/python3.11/site-packages/fontTools/cu2qu/__pycache__/errors.cpython-311.pyc", "path_type": "hardlink", "sha256": "6e9b98922910ed52569421c2eb85563fae6d124ba0a8d562a02661a60ae61a65", "sha256_in_prefix": "6e9b98922910ed52569421c2eb85563fae6d124ba0a8d562a02661a60ae61a65", "size_in_bytes": 5813}, {"_path": "lib/python3.11/site-packages/fontTools/cu2qu/__pycache__/ufo.cpython-311.pyc", "path_type": "hardlink", "sha256": "4895ba3fe03f181ad29591c44fb91c63c7d4023e5b5ae7bcf6b533b73cadc314", "sha256_in_prefix": "4895ba3fe03f181ad29591c44fb91c63c7d4023e5b5ae7bcf6b533b73cadc314", "size_in_bytes": 17071}, {"_path": "lib/python3.11/site-packages/fontTools/cu2qu/benchmark.py", "path_type": "hardlink", "sha256": "c1ab0f2667fcabd93d5078e91c2842dd640019b040c8737d3ef2735ef582d05c", "sha256_in_prefix": "c1ab0f2667fcabd93d5078e91c2842dd640019b040c8737d3ef2735ef582d05c", "size_in_bytes": 1296}, {"_path": "lib/python3.11/site-packages/fontTools/cu2qu/cli.py", "path_type": "hardlink", "sha256": "31b0109cea59c2b5057bfb6300fdd381feae2dd3a01e538d51c3cd7935f01f46", "sha256_in_prefix": "31b0109cea59c2b5057bfb6300fdd381feae2dd3a01e538d51c3cd7935f01f46", "size_in_bytes": 6076}, {"_path": "lib/python3.11/site-packages/fontTools/cu2qu/cu2qu.c", "path_type": "hardlink", "sha256": "fbb27058149e277238bc8b3a65a4951f37c17f8e8c4b1db9946d39351a80e998", "sha256_in_prefix": "fbb27058149e277238bc8b3a65a4951f37c17f8e8c4b1db9946d39351a80e998", "size_in_bytes": 637967}, {"_path": "lib/python3.11/site-packages/fontTools/cu2qu/cu2qu.cpython-311-darwin.so", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/fonttools_1758132623042/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold", "sha256": "e51756f174e0e52fcca7e6dc2605b7fab94cd8a5a3f549bc8263b9167627d2c2", "sha256_in_prefix": "3c6558791844240f258bf1892a2e88e78dad0dc237861631b202c07d9f018a68", "size_in_bytes": 128672}, {"_path": "lib/python3.11/site-packages/fontTools/cu2qu/cu2qu.py", "path_type": "hardlink", "sha256": "e8b4ded5923e8f15bccbbf6cfd41636e4785a0595e21e9132e6da3004faea864", "sha256_in_prefix": "e8b4ded5923e8f15bccbbf6cfd41636e4785a0595e21e9132e6da3004faea864", "size_in_bytes": 17986}, {"_path": "lib/python3.11/site-packages/fontTools/cu2qu/errors.py", "path_type": "hardlink", "sha256": "3f224d332f251c3b4aa5f1647349e433c1788cd2d9002e253d008dd4a9b86e98", "sha256_in_prefix": "3f224d332f251c3b4aa5f1647349e433c1788cd2d9002e253d008dd4a9b86e98", "size_in_bytes": 2441}, {"_path": "lib/python3.11/site-packages/fontTools/cu2qu/ufo.py", "path_type": "hardlink", "sha256": "a9947bd2e59d0a26b5f457fc18b9f935e22db1cbef9faf437a08c365517878d2", "sha256_in_prefix": "a9947bd2e59d0a26b5f457fc18b9f935e22db1cbef9faf437a08c365517878d2", "size_in_bytes": 11794}, {"_path": "lib/python3.11/site-packages/fontTools/designspaceLib/__init__.py", "path_type": "hardlink", "sha256": "346202e736aad0d0dd4a43b297afa2df6edc0338024b78de6b2103bcc1174706", "sha256_in_prefix": "346202e736aad0d0dd4a43b297afa2df6edc0338024b78de6b2103bcc1174706", "size_in_bytes": 129263}, {"_path": "lib/python3.11/site-packages/fontTools/designspaceLib/__main__.py", "path_type": "hardlink", "sha256": "c61b585e8d53d6db329214030f4b5c7289d23588162f98684c1391a550d462b7", "sha256_in_prefix": "c61b585e8d53d6db329214030f4b5c7289d23588162f98684c1391a550d462b7", "size_in_bytes": 103}, {"_path": "lib/python3.11/site-packages/fontTools/designspaceLib/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "a7131ecbb0e1897ba36e996871617980835911a6ff5d7033229371aea817b5b1", "sha256_in_prefix": "a7131ecbb0e1897ba36e996871617980835911a6ff5d7033229371aea817b5b1", "size_in_bytes": 138864}, {"_path": "lib/python3.11/site-packages/fontTools/designspaceLib/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "51a575a1620612c439c8d5efaeadda777cd15f9a0e2af6ad17b19e79226ae8c3", "sha256_in_prefix": "51a575a1620612c439c8d5efaeadda777cd15f9a0e2af6ad17b19e79226ae8c3", "size_in_bytes": 398}, {"_path": "lib/python3.11/site-packages/fontTools/designspaceLib/__pycache__/split.cpython-311.pyc", "path_type": "hardlink", "sha256": "ac6694bce3fd451bcf46bdee4149fbd0c02c87130b99a2d72d81cdb417a88c15", "sha256_in_prefix": "ac6694bce3fd451bcf46bdee4149fbd0c02c87130b99a2d72d81cdb417a88c15", "size_in_bytes": 18773}, {"_path": "lib/python3.11/site-packages/fontTools/designspaceLib/__pycache__/statNames.cpython-311.pyc", "path_type": "hardlink", "sha256": "e7fc1f9bf327505e3ceb054928b5f5669c1920fb658e9b71403019d8f52a22a5", "sha256_in_prefix": "e7fc1f9bf327505e3ceb054928b5f5669c1920fb658e9b71403019d8f52a22a5", "size_in_bytes": 10193}, {"_path": "lib/python3.11/site-packages/fontTools/designspaceLib/__pycache__/types.cpython-311.pyc", "path_type": "hardlink", "sha256": "4cf0f7ade457fda2df5d86770fc4367f66060af12e9b85f020f0ef739647432c", "sha256_in_prefix": "4cf0f7ade457fda2df5d86770fc4367f66060af12e9b85f020f0ef739647432c", "size_in_bytes": 6817}, {"_path": "lib/python3.11/site-packages/fontTools/designspaceLib/split.py", "path_type": "hardlink", "sha256": "141d4dbaf8543b8e77517bde4198bda32ad6fdc6a808f3374400e9d6b616903b", "sha256_in_prefix": "141d4dbaf8543b8e77517bde4198bda32ad6fdc6a808f3374400e9d6b616903b", "size_in_bytes": 19239}, {"_path": "lib/python3.11/site-packages/fontTools/designspaceLib/statNames.py", "path_type": "hardlink", "sha256": "81718a595af58eedbfa0bf91fc3932a19dc6948ee67cb391a2fa64d446e09af7", "sha256_in_prefix": "81718a595af58eedbfa0bf91fc3932a19dc6948ee67cb391a2fa64d446e09af7", "size_in_bytes": 9237}, {"_path": "lib/python3.11/site-packages/fontTools/designspaceLib/types.py", "path_type": "hardlink", "sha256": "a1f2bae6a5cd003a9ca65ef323bd8f6b9b34efe726ec6e3588498b555e38f84b", "sha256_in_prefix": "a1f2bae6a5cd003a9ca65ef323bd8f6b9b34efe726ec6e3588498b555e38f84b", "size_in_bytes": 5320}, {"_path": "lib/python3.11/site-packages/fontTools/encodings/MacRoman.py", "path_type": "hardlink", "sha256": "e2f128a140e6da02c21bc288203851c66e7e03ae30ed7ae13fd7230d1af6128d", "sha256_in_prefix": "e2f128a140e6da02c21bc288203851c66e7e03ae30ed7ae13fd7230d1af6128d", "size_in_bytes": 3576}, {"_path": "lib/python3.11/site-packages/fontTools/encodings/StandardEncoding.py", "path_type": "hardlink", "sha256": "128dc0184f0513fa7e215618b95d3decaa2e4ac17752b5e84513745f2bd86ebb", "sha256_in_prefix": "128dc0184f0513fa7e215618b95d3decaa2e4ac17752b5e84513745f2bd86ebb", "size_in_bytes": 3581}, {"_path": "lib/python3.11/site-packages/fontTools/encodings/__init__.py", "path_type": "hardlink", "sha256": "0c90569a85ff1da6aeeea9609af5b27db852ceb5f6a8beb7e919ecec21b4d699", "sha256_in_prefix": "0c90569a85ff1da6aeeea9609af5b27db852ceb5f6a8beb7e919ecec21b4d699", "size_in_bytes": 75}, {"_path": "lib/python3.11/site-packages/fontTools/encodings/__pycache__/MacRoman.cpython-311.pyc", "path_type": "hardlink", "sha256": "debf187f0889de77b73ba64cf621cba68499cee0435d28bae90c8f440e081f51", "sha256_in_prefix": "debf187f0889de77b73ba64cf621cba68499cee0435d28bae90c8f440e081f51", "size_in_bytes": 2228}, {"_path": "lib/python3.11/site-packages/fontTools/encodings/__pycache__/StandardEncoding.cpython-311.pyc", "path_type": "hardlink", "sha256": "27ea1daffe155c5df525fd606e5b2bf92369f23aaebe8f12b0602165bdf16e2f", "sha256_in_prefix": "27ea1daffe155c5df525fd606e5b2bf92369f23aaebe8f12b0602165bdf16e2f", "size_in_bytes": 1820}, {"_path": "lib/python3.11/site-packages/fontTools/encodings/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "f91806ce215a3e8c96f869fee81245c32de5039e070b7c2dc9d1d48ae7f8f755", "sha256_in_prefix": "f91806ce215a3e8c96f869fee81245c32de5039e070b7c2dc9d1d48ae7f8f755", "size_in_bytes": 251}, {"_path": "lib/python3.11/site-packages/fontTools/encodings/__pycache__/codecs.cpython-311.pyc", "path_type": "hardlink", "sha256": "5c106f30f6e19a368fe48e4e2696256ddecd45db69f8d93dc297b1e6c86995b9", "sha256_in_prefix": "5c106f30f6e19a368fe48e4e2696256ddecd45db69f8d93dc297b1e6c86995b9", "size_in_bytes": 6336}, {"_path": "lib/python3.11/site-packages/fontTools/encodings/codecs.py", "path_type": "hardlink", "sha256": "bb9d2bbb0cfd7dc46cad4ad11a9475ec2af9e4ebe7d5fbc21c22ab12556e9831", "sha256_in_prefix": "bb9d2bbb0cfd7dc46cad4ad11a9475ec2af9e4ebe7d5fbc21c22ab12556e9831", "size_in_bytes": 4721}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/__init__.py", "path_type": "hardlink", "sha256": "8e522bbb6821c6f6f51e10b925ed810978c52661509582a9aeea2b3e8cf706f4", "sha256_in_prefix": "8e522bbb6821c6f6f51e10b925ed810978c52661509582a9aeea2b3e8cf706f4", "size_in_bytes": 213}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/__main__.py", "path_type": "hardlink", "sha256": "0dfd8f03a2d7c276bdf2549788bed1e1ab3f6447560889377a048ce70ec6a6f3", "sha256_in_prefix": "0dfd8f03a2d7c276bdf2549788bed1e1ab3f6447560889377a048ce70ec6a6f3", "size_in_bytes": 2240}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "ebea1bd2bde2d072910ae2e128009c130137b0729385acfd3accf447e8ab9878", "sha256_in_prefix": "ebea1bd2bde2d072910ae2e128009c130137b0729385acfd3accf447e8ab9878", "size_in_bytes": 250}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "a7690dd051e19e23c235ccaeb9d4c57f09e7980f9c078ee8bfce3ee8a74ddf8b", "sha256_in_prefix": "a7690dd051e19e23c235ccaeb9d4c57f09e7980f9c078ee8bfce3ee8a74ddf8b", "size_in_bytes": 3614}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/ast.cpython-311.pyc", "path_type": "hardlink", "sha256": "fdf2330cb350b539f33a311b9c6b1b9befa4b248b0f6bb62bb2042dede0e641b", "sha256_in_prefix": "fdf2330cb350b539f33a311b9c6b1b9befa4b248b0f6bb62bb2042dede0e641b", "size_in_bytes": 134228}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/builder.cpython-311.pyc", "path_type": "hardlink", "sha256": "74b487bd03d38d40631b3016ba0305f5f65b953dd89adbecbace1a5faad17aac", "sha256_in_prefix": "74b487bd03d38d40631b3016ba0305f5f65b953dd89adbecbace1a5faad17aac", "size_in_bytes": 93515}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/error.cpython-311.pyc", "path_type": "hardlink", "sha256": "67164cae77528e68e3fa3f9c340e8d783f1c168619c9fa59648c6ab1f36b3bfc", "sha256_in_prefix": "67164cae77528e68e3fa3f9c340e8d783f1c168619c9fa59648c6ab1f36b3bfc", "size_in_bytes": 1603}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/lexer.cpython-311.pyc", "path_type": "hardlink", "sha256": "3c9458fdf6ef85f91194c492e42ca4c73cae6021037b59f51846794d21ff3bda", "sha256_in_prefix": "3c9458fdf6ef85f91194c492e42ca4c73cae6021037b59f51846794d21ff3bda", "size_in_bytes": 15697}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/location.cpython-311.pyc", "path_type": "hardlink", "sha256": "340b483c31a51656cc511a7724dd0cecd5b05be6c2725f84ea6af7c55678312a", "sha256_in_prefix": "340b483c31a51656cc511a7724dd0cecd5b05be6c2725f84ea6af7c55678312a", "size_in_bytes": 855}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/lookupDebugInfo.cpython-311.pyc", "path_type": "hardlink", "sha256": "1bd6988ca85b3f2e575f7af53217d3a0e0fe07f63ea23dbbbdf6fca0909f443a", "sha256_in_prefix": "1bd6988ca85b3f2e575f7af53217d3a0e0fe07f63ea23dbbbdf6fca0909f443a", "size_in_bytes": 788}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/parser.cpython-311.pyc", "path_type": "hardlink", "sha256": "978a43da29afd8fe485320037f6ffa4ed152e3b4986bfcfa017decde37c19caf", "sha256_in_prefix": "978a43da29afd8fe485320037f6ffa4ed152e3b4986bfcfa017decde37c19caf", "size_in_bytes": 122083}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/__pycache__/variableScalar.cpython-311.pyc", "path_type": "hardlink", "sha256": "d96d87249c8998f1b9e2003982ca860ddb483d2e05acc697b43361da601709d0", "sha256_in_prefix": "d96d87249c8998f1b9e2003982ca860ddb483d2e05acc697b43361da601709d0", "size_in_bytes": 9431}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/ast.py", "path_type": "hardlink", "sha256": "e3c63abe9483ff061fb9c5b287f6d0b737f6010157fa93b006fb3175ca550f3d", "sha256_in_prefix": "e3c63abe9483ff061fb9c5b287f6d0b737f6010157fa93b006fb3175ca550f3d", "size_in_bytes": 74158}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/builder.py", "path_type": "hardlink", "sha256": "8c825599a2f0acd18504eee6403369721886062bfbd273696782d545569a2143", "sha256_in_prefix": "8c825599a2f0acd18504eee6403369721886062bfbd273696782d545569a2143", "size_in_bytes": 73757}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/error.py", "path_type": "hardlink", "sha256": "073ff9b4d70d55c63bfe7ac098595036142576da9959df165064361373d6859a", "sha256_in_prefix": "073ff9b4d70d55c63bfe7ac098595036142576da9959df165064361373d6859a", "size_in_bytes": 648}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/lexer.c", "path_type": "hardlink", "sha256": "51a6391f2d1662c185f543099c0dad331d24e43c1803bd8dd882b1a51b007027", "sha256_in_prefix": "51a6391f2d1662c185f543099c0dad331d24e43c1803bd8dd882b1a51b007027", "size_in_bytes": 754285}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/lexer.cpython-311-darwin.so", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/fonttools_1758132623042/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold", "sha256": "09dabd3e3dd6bfd0ca41cdecbb85eb26c7ae8f8f12fde4cbcd78cb0df8bdbc9d", "sha256_in_prefix": "2903aab5341a32c2344a54ec3a7a9554eecdedc4be838f0720d009f96206c364", "size_in_bytes": 161504}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/lexer.py", "path_type": "hardlink", "sha256": "7a6c8c3e6468a8d664cf19c9c88e894420ad5eb6c2385a1fc1af4ee800462e2c", "sha256_in_prefix": "7a6c8c3e6468a8d664cf19c9c88e894420ad5eb6c2385a1fc1af4ee800462e2c", "size_in_bytes": 11121}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/location.py", "path_type": "hardlink", "sha256": "257cc7a86579e841dd72af36dc0c00e6868ad3985fff5c925a949c6e8df3182d", "sha256_in_prefix": "257cc7a86579e841dd72af36dc0c00e6868ad3985fff5c925a949c6e8df3182d", "size_in_bytes": 234}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/lookupDebugInfo.py", "path_type": "hardlink", "sha256": "81546be7e00f59f4ff6b9fb6e6146e6b04955fc7c4bd756c3922d6907f754f6c", "sha256_in_prefix": "81546be7e00f59f4ff6b9fb6e6146e6b04955fc7c4bd756c3922d6907f754f6c", "size_in_bytes": 304}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/parser.py", "path_type": "hardlink", "sha256": "13c132fc5b764d46db95e44990243d3668e7d55678482b7752d2c009c66aaf6b", "sha256_in_prefix": "13c132fc5b764d46db95e44990243d3668e7d55678482b7752d2c009c66aaf6b", "size_in_bytes": 99716}, {"_path": "lib/python3.11/site-packages/fontTools/feaLib/variableScalar.py", "path_type": "hardlink", "sha256": "7fab0e83d71f149448ddfc34e2e468843785bb1d319d96a768f4ff95cc4054ec", "sha256_in_prefix": "7fab0e83d71f149448ddfc34e2e468843785bb1d319d96a768f4ff95cc4054ec", "size_in_bytes": 4200}, {"_path": "lib/python3.11/site-packages/fontTools/fontBuilder.py", "path_type": "hardlink", "sha256": "c85dbe21897f85aa3e672fc559223847e1e7945a7316bcf46011903bccdf68eb", "sha256_in_prefix": "c85dbe21897f85aa3e672fc559223847e1e7945a7316bcf46011903bccdf68eb", "size_in_bytes": 34130}, {"_path": "lib/python3.11/site-packages/fontTools/help.py", "path_type": "hardlink", "sha256": "6c08dab6f22157b4c9c9723b5a1b317583b861596149c657bbf911b4700d10fa", "sha256_in_prefix": "6c08dab6f22157b4c9c9723b5a1b317583b861596149c657bbf911b4700d10fa", "size_in_bytes": 1125}, {"_path": "lib/python3.11/site-packages/fontTools/merge/__init__.py", "path_type": "hardlink", "sha256": "f22ea8c27c904c03812969d3107bef098170eb832fed9d473c180923e662b8aa", "sha256_in_prefix": "f22ea8c27c904c03812969d3107bef098170eb832fed9d473c180923e662b8aa", "size_in_bytes": 8256}, {"_path": "lib/python3.11/site-packages/fontTools/merge/__main__.py", "path_type": "hardlink", "sha256": "843c7781f6d404ef370092ae992121895fb1a8d4c934d80adae1636b33864e6c", "sha256_in_prefix": "843c7781f6d404ef370092ae992121895fb1a8d4c934d80adae1636b33864e6c", "size_in_bytes": 94}, {"_path": "lib/python3.11/site-packages/fontTools/merge/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "728182bd7a1ec40e2060edb73a9c8aa7103145032b9ffe307e5a6c20adcbc64f", "sha256_in_prefix": "728182bd7a1ec40e2060edb73a9c8aa7103145032b9ffe307e5a6c20adcbc64f", "size_in_bytes": 13608}, {"_path": "lib/python3.11/site-packages/fontTools/merge/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "6763d3cdd9e726bab85e461a2bb516641e429c4fffaf9246abe0b8fd4c114c05", "sha256_in_prefix": "6763d3cdd9e726bab85e461a2bb516641e429c4fffaf9246abe0b8fd4c114c05", "size_in_bytes": 380}, {"_path": "lib/python3.11/site-packages/fontTools/merge/__pycache__/base.cpython-311.pyc", "path_type": "hardlink", "sha256": "cd46636e16b09fa2241685c661ec582e51fd6d5feaa241135e9542df18ec6915", "sha256_in_prefix": "cd46636e16b09fa2241685c661ec582e51fd6d5feaa241135e9542df18ec6915", "size_in_bytes": 4490}, {"_path": "lib/python3.11/site-packages/fontTools/merge/__pycache__/cmap.cpython-311.pyc", "path_type": "hardlink", "sha256": "ada8b62a9ea9a76bafee98b13074891180636545680b01d321e35c76bd7b0f22", "sha256_in_prefix": "ada8b62a9ea9a76bafee98b13074891180636545680b01d321e35c76bd7b0f22", "size_in_bytes": 6934}, {"_path": "lib/python3.11/site-packages/fontTools/merge/__pycache__/layout.cpython-311.pyc", "path_type": "hardlink", "sha256": "b794652242213b3934db28e2093cecdf07e5aec522cb1d371d9ef012235a65b9", "sha256_in_prefix": "b794652242213b3934db28e2093cecdf07e5aec522cb1d371d9ef012235a65b9", "size_in_bytes": 24465}, {"_path": "lib/python3.11/site-packages/fontTools/merge/__pycache__/options.cpython-311.pyc", "path_type": "hardlink", "sha256": "1d26998f66c4fd50b877cd1817b606e9b3abe7e29ac69bc42afb8582f1121b31", "sha256_in_prefix": "1d26998f66c4fd50b877cd1817b606e9b3abe7e29ac69bc42afb8582f1121b31", "size_in_bytes": 3946}, {"_path": "lib/python3.11/site-packages/fontTools/merge/__pycache__/tables.cpython-311.pyc", "path_type": "hardlink", "sha256": "6f6d1ab5dcfd9470c787a3e899790ea6147f09fbc4e34f72bc46360aabd5bdb2", "sha256_in_prefix": "6f6d1ab5dcfd9470c787a3e899790ea6147f09fbc4e34f72bc46360aabd5bdb2", "size_in_bytes": 13833}, {"_path": "lib/python3.11/site-packages/fontTools/merge/__pycache__/unicode.cpython-311.pyc", "path_type": "hardlink", "sha256": "95f754f23d7d36941554f46f0f3a44231e0daf68cf9e532dd4231db4eb7f2c39", "sha256_in_prefix": "95f754f23d7d36941554f46f0f3a44231e0daf68cf9e532dd4231db4eb7f2c39", "size_in_bytes": 2036}, {"_path": "lib/python3.11/site-packages/fontTools/merge/__pycache__/util.cpython-311.pyc", "path_type": "hardlink", "sha256": "b195dcbc6e5dcefc088d58fee93d5b68de9c74a54b4bcc2b552334ecaad2234a", "sha256_in_prefix": "b195dcbc6e5dcefc088d58fee93d5b68de9c74a54b4bcc2b552334ecaad2234a", "size_in_bytes": 9005}, {"_path": "lib/python3.11/site-packages/fontTools/merge/base.py", "path_type": "hardlink", "sha256": "9741b53f1f7c13d65d56e14b31404a59db6bec96fc257f2fc5c8de6835149f36", "sha256_in_prefix": "9741b53f1f7c13d65d56e14b31404a59db6bec96fc257f2fc5c8de6835149f36", "size_in_bytes": 2389}, {"_path": "lib/python3.11/site-packages/fontTools/merge/cmap.py", "path_type": "hardlink", "sha256": "1e9b61c551f9940ed57a027cc87a018ddf6fac5055ed40793a49ca198a71598f", "sha256_in_prefix": "1e9b61c551f9940ed57a027cc87a018ddf6fac5055ed40793a49ca198a71598f", "size_in_bytes": 6728}, {"_path": "lib/python3.11/site-packages/fontTools/merge/layout.py", "path_type": "hardlink", "sha256": "7e430f18f2f111dc68852dec7153385bb2e636d852cfe50fca872c7df7b62aa1", "sha256_in_prefix": "7e430f18f2f111dc68852dec7153385bb2e636d852cfe50fca872c7df7b62aa1", "size_in_bytes": 16075}, {"_path": "lib/python3.11/site-packages/fontTools/merge/options.py", "path_type": "hardlink", "sha256": "c64a3fd7e584adc350922ac40b320e398c52251fdb46d75061060eb6681c7182", "sha256_in_prefix": "c64a3fd7e584adc350922ac40b320e398c52251fdb46d75061060eb6681c7182", "size_in_bytes": 2501}, {"_path": "lib/python3.11/site-packages/fontTools/merge/tables.py", "path_type": "hardlink", "sha256": "ed2cd760bd386b00c40e1bd4dbef53ffc0368008ef806c9800750809424ea598", "sha256_in_prefix": "ed2cd760bd386b00c40e1bd4dbef53ffc0368008ef806c9800750809424ea598", "size_in_bytes": 10958}, {"_path": "lib/python3.11/site-packages/fontTools/merge/unicode.py", "path_type": "hardlink", "sha256": "91bd49adfba8ab529471586148a9df940103ff0319c570e357007e088f64d396", "sha256_in_prefix": "91bd49adfba8ab529471586148a9df940103ff0319c570e357007e088f64d396", "size_in_bytes": 4273}, {"_path": "lib/python3.11/site-packages/fontTools/merge/util.py", "path_type": "hardlink", "sha256": "047ddb656345cbe4ec8f572d87b692a46549d7c617297a836a43e7e96ce4bba5", "sha256_in_prefix": "047ddb656345cbe4ec8f572d87b692a46549d7c617297a836a43e7e96ce4bba5", "size_in_bytes": 3378}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__init__.py", "path_type": "hardlink", "sha256": "0c90569a85ff1da6aeeea9609af5b27db852ceb5f6a8beb7e919ecec21b4d699", "sha256_in_prefix": "0c90569a85ff1da6aeeea9609af5b27db852ceb5f6a8beb7e919ecec21b4d699", "size_in_bytes": 75}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "4187f615f2648f2ea816f0c7809c6ea252677668ec698d3eee8ee9509d62c200", "sha256_in_prefix": "4187f615f2648f2ea816f0c7809c6ea252677668ec698d3eee8ee9509d62c200", "size_in_bytes": 246}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/arrayTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "948d57f5b8cfe7db3bd04dac3f84e6ebc45a154094f462cb775c4eb1ec83e9ec", "sha256_in_prefix": "948d57f5b8cfe7db3bd04dac3f84e6ebc45a154094f462cb775c4eb1ec83e9ec", "size_in_bytes": 16405}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/bezierTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "04094da461323e935a48759bd79c2b7979b0bb6c5257abe4dc6c9e0125bba898", "sha256_in_prefix": "04094da461323e935a48759bd79c2b7979b0bb6c5257abe4dc6c9e0125bba898", "size_in_bytes": 60718}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/classifyTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "9a42fd8f7908f3d8d4dc8c73c2cb3acda1bb930a3bbc680f6e543ca6f089d2b2", "sha256_in_prefix": "9a42fd8f7908f3d8d4dc8c73c2cb3acda1bb930a3bbc680f6e543ca6f089d2b2", "size_in_bytes": 7282}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/cliTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "ba296082fcfa30a9b3a5aeac635d0f6bc9005642abd7ed83936988dc976b1112", "sha256_in_prefix": "ba296082fcfa30a9b3a5aeac635d0f6bc9005642abd7ed83936988dc976b1112", "size_in_bytes": 2651}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/configTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "23829159ceb3fab4ec0cfe4fab7cdd41e64611d769d371b8c0bceba9b0a11643", "sha256_in_prefix": "23829159ceb3fab4ec0cfe4fab7cdd41e64611d769d371b8c0bceba9b0a11643", "size_in_bytes": 17546}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/cython.cpython-311.pyc", "path_type": "hardlink", "sha256": "5b788ec8e4f3b5bbb140608ebcb07b4b7432aaf30bfc62feae5f7a0b5ba17af7", "sha256_in_prefix": "5b788ec8e4f3b5bbb140608ebcb07b4b7432aaf30bfc62feae5f7a0b5ba17af7", "size_in_bytes": 1201}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/dictTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "3dd9165a1bae9cff8acfd3f1137ab5f71572a281984b23e319936d504b625d11", "sha256_in_prefix": "3dd9165a1bae9cff8acfd3f1137ab5f71572a281984b23e319936d504b625d11", "size_in_bytes": 4213}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/eexec.cpython-311.pyc", "path_type": "hardlink", "sha256": "ac058aa78b602d065a7f0829c168c71bd372055a5398287ba058a5e22176b694", "sha256_in_prefix": "ac058aa78b602d065a7f0829c168c71bd372055a5398287ba058a5e22176b694", "size_in_bytes": 4729}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/encodingTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "230015b9d522736888ef1c507b5078065b31ba12208a53035a103f21e067aa80", "sha256_in_prefix": "230015b9d522736888ef1c507b5078065b31ba12208a53035a103f21e067aa80", "size_in_bytes": 1934}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/enumTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "464d6e911fb37ef78de1078786ccd38affd8ac996c7564a27bd95dc37f1894de", "sha256_in_prefix": "464d6e911fb37ef78de1078786ccd38affd8ac996c7564a27bd95dc37f1894de", "size_in_bytes": 1096}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/etree.cpython-311.pyc", "path_type": "hardlink", "sha256": "ddf15302466fc73c08d75b6061659522a46863f643148442c1ce0e5d0274f2cf", "sha256_in_prefix": "ddf15302466fc73c08d75b6061659522a46863f643148442c1ce0e5d0274f2cf", "size_in_bytes": 17808}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/filenames.cpython-311.pyc", "path_type": "hardlink", "sha256": "352a1742fed18df683db84664a51da1af1a31e0e8da1f51d241151b355f9e7e4", "sha256_in_prefix": "352a1742fed18df683db84664a51da1af1a31e0e8da1f51d241151b355f9e7e4", "size_in_bytes": 9203}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/fixedTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "a1da8a975d804910c179afadacdb003d545e6c11871a98922139fe4da436a1e4", "sha256_in_prefix": "a1da8a975d804910c179afadacdb003d545e6c11871a98922139fe4da436a1e4", "size_in_bytes": 8741}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/intTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "543a57db7ab4845a21025f0f931914ac0e528cc1dafca7e987f5ac3acd8dfd6b", "sha256_in_prefix": "543a57db7ab4845a21025f0f931914ac0e528cc1dafca7e987f5ac3acd8dfd6b", "size_in_bytes": 1169}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/iterTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "7b2472023af5cf128036552d05d77844b878d07a1f54e156ccfe93f9b999210a", "sha256_in_prefix": "7b2472023af5cf128036552d05d77844b878d07a1f54e156ccfe93f9b999210a", "size_in_bytes": 807}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/lazyTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "9d63913cd70eb6932680148bed1cbb5160463f2aa8a740b1ce1daec17a3b9d64", "sha256_in_prefix": "9d63913cd70eb6932680148bed1cbb5160463f2aa8a740b1ce1daec17a3b9d64", "size_in_bytes": 2791}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/loggingTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "0183828fe3aabdda789c183d54b546665a300be3ea2fbb38e06ec7917e039a69", "sha256_in_prefix": "0183828fe3aabdda789c183d54b546665a300be3ea2fbb38e06ec7917e039a69", "size_in_bytes": 26363}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/macCreatorType.cpython-311.pyc", "path_type": "hardlink", "sha256": "47d2f84e81b7ff38c03004ee8b5912ebcd805ae21686b7f32de40b2a948d4e4c", "sha256_in_prefix": "47d2f84e81b7ff38c03004ee8b5912ebcd805ae21686b7f32de40b2a948d4e4c", "size_in_bytes": 2739}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/macRes.cpython-311.pyc", "path_type": "hardlink", "sha256": "de632c46d160b826f3522d94c2f84714d61ba2e819aac0c58ac2b30c88409070", "sha256_in_prefix": "de632c46d160b826f3522d94c2f84714d61ba2e819aac0c58ac2b30c88409070", "size_in_bytes": 14257}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/psCharStrings.cpython-311.pyc", "path_type": "hardlink", "sha256": "b6e270bb86cf458c90dfc2a815a93395dea40c3b8ac57dbe9e718896863a5bd7", "sha256_in_prefix": "b6e270bb86cf458c90dfc2a815a93395dea40c3b8ac57dbe9e718896863a5bd7", "size_in_bytes": 70762}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/psLib.cpython-311.pyc", "path_type": "hardlink", "sha256": "afc66fdf91eb9ae69783acada72416d1ed9f3cb6b68c57f256236df1936bb9ad", "sha256_in_prefix": "afc66fdf91eb9ae69783acada72416d1ed9f3cb6b68c57f256236df1936bb9ad", "size_in_bytes": 18874}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/psOperators.cpython-311.pyc", "path_type": "hardlink", "sha256": "5e7028acf198b5ae07511b97cbe807b741b8c5a3201a928db2ce242d9e679516", "sha256_in_prefix": "5e7028acf198b5ae07511b97cbe807b741b8c5a3201a928db2ce242d9e679516", "size_in_bytes": 34798}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/py23.cpython-311.pyc", "path_type": "hardlink", "sha256": "70f1e9c1b0dd55c53b04a3e4be25f749db1a7dbe02f55fb9046ae41a3563634b", "sha256_in_prefix": "70f1e9c1b0dd55c53b04a3e4be25f749db1a7dbe02f55fb9046ae41a3563634b", "size_in_bytes": 3134}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/roundTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "8305a796e07ea1aa92b95f9519dbadb96aec145b4814b3ced67fbb6dc2101622", "sha256_in_prefix": "8305a796e07ea1aa92b95f9519dbadb96aec145b4814b3ced67fbb6dc2101622", "size_in_bytes": 4232}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/sstruct.cpython-311.pyc", "path_type": "hardlink", "sha256": "8c02dd211594c3e1a4386aae977b31802e60a1454bbc2367179e09d73745f68e", "sha256_in_prefix": "8c02dd211594c3e1a4386aae977b31802e60a1454bbc2367179e09d73745f68e", "size_in_bytes": 9512}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/symfont.cpython-311.pyc", "path_type": "hardlink", "sha256": "8e7679da4183a40b25bf4ff2a6212336627fed25ebdeeb274093a16bb101ce82", "sha256_in_prefix": "8e7679da4183a40b25bf4ff2a6212336627fed25ebdeeb274093a16bb101ce82", "size_in_bytes": 15642}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/testTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "bd32e88a9c770cf5841b08b679e1afce9b6d0cf09be317365d6b67a5362abe80", "sha256_in_prefix": "bd32e88a9c770cf5841b08b679e1afce9b6d0cf09be317365d6b67a5362abe80", "size_in_bytes": 14669}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/textTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "72af3de72155e268e23fcce0e7815b424a780c8a5b1e7cd89059239e18b372d0", "sha256_in_prefix": "72af3de72155e268e23fcce0e7815b424a780c8a5b1e7cd89059239e18b372d0", "size_in_bytes": 8028}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/timeTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "9f220aba2919a554c553d6c087d349c5b00367edba8b2057c9c7adfcb9fa5ac8", "sha256_in_prefix": "9f220aba2919a554c553d6c087d349c5b00367edba8b2057c9c7adfcb9fa5ac8", "size_in_bytes": 3929}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/transform.cpython-311.pyc", "path_type": "hardlink", "sha256": "4ebaabd9e0d2f8d7c8ed5a83fd93211313995639708e625d8ae2955bb6b6fd7a", "sha256_in_prefix": "4ebaabd9e0d2f8d7c8ed5a83fd93211313995639708e625d8ae2955bb6b6fd7a", "size_in_bytes": 21405}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/treeTools.cpython-311.pyc", "path_type": "hardlink", "sha256": "af5f7fde72de5bf29517f0976d1a55f91a4f410de18c5444774739b2abe52d42", "sha256_in_prefix": "af5f7fde72de5bf29517f0976d1a55f91a4f410de18c5444774739b2abe52d42", "size_in_bytes": 2045}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/vector.cpython-311.pyc", "path_type": "hardlink", "sha256": "224e8b82e5324c3b3ccac0dc32365c4dd8ecd5b53d4dc8c8ad45375e34a93db9", "sha256_in_prefix": "224e8b82e5324c3b3ccac0dc32365c4dd8ecd5b53d4dc8c8ad45375e34a93db9", "size_in_bytes": 10185}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/visitor.cpython-311.pyc", "path_type": "hardlink", "sha256": "62a635b1c7c0eb97f1d046bb1307d5c7da2cad8ae489788502db5e1f7cf27a8e", "sha256_in_prefix": "62a635b1c7c0eb97f1d046bb1307d5c7da2cad8ae489788502db5e1f7cf27a8e", "size_in_bytes": 7989}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/xmlReader.cpython-311.pyc", "path_type": "hardlink", "sha256": "bc116ba73ff7ada147dbd702019563529feae8774cd872e672ba25daba3bb66a", "sha256_in_prefix": "bc116ba73ff7ada147dbd702019563529feae8774cd872e672ba25daba3bb66a", "size_in_bytes": 9414}, {"_path": "lib/python3.11/site-packages/fontTools/misc/__pycache__/xmlWriter.cpython-311.pyc", "path_type": "hardlink", "sha256": "42c6d4a95eabc28dd58d2e7bae9869c885e214d1a9b1e10d46ab7efd6fcbf43e", "sha256_in_prefix": "42c6d4a95eabc28dd58d2e7bae9869c885e214d1a9b1e10d46ab7efd6fcbf43e", "size_in_bytes": 12209}, {"_path": "lib/python3.11/site-packages/fontTools/misc/arrayTools.py", "path_type": "hardlink", "sha256": "8d993ffc613e2bd55589913f1fe2cf3e3d2c9966ca9e0fb27cf13c05f1a9f072", "sha256_in_prefix": "8d993ffc613e2bd55589913f1fe2cf3e3d2c9966ca9e0fb27cf13c05f1a9f072", "size_in_bytes": 11483}, {"_path": "lib/python3.11/site-packages/fontTools/misc/bezierTools.c", "path_type": "hardlink", "sha256": "985a369eb9ab22ad75589f138ff73147833a537b71ef94553dc53e383907adb8", "sha256_in_prefix": "985a369eb9ab22ad75589f138ff73147833a537b71ef94553dc53e383907adb8", "size_in_bytes": 1821732}, {"_path": "lib/python3.11/site-packages/fontTools/misc/bezierTools.cpython-311-darwin.so", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/fonttools_1758132623042/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold", "sha256": "6215bb728848b6e0e78f32264948b21ee3a234514307414c4d6964263ffac55e", "sha256_in_prefix": "41e99c62daeda8982e492acee137c0d2fb9f0a1b01698ad47e19b57040f8130a", "size_in_bytes": 503048}, {"_path": "lib/python3.11/site-packages/fontTools/misc/bezierTools.py", "path_type": "hardlink", "sha256": "3a6477a7308613136f6724cfac1c87ee0407a4026c60e975726bdf6102157d00", "sha256_in_prefix": "3a6477a7308613136f6724cfac1c87ee0407a4026c60e975726bdf6102157d00", "size_in_bytes": 45038}, {"_path": "lib/python3.11/site-packages/fontTools/misc/classifyTools.py", "path_type": "hardlink", "sha256": "cdc83710ce0639eac15bd734eb796368b96581e799efbd84a45663a7d09d80b2", "sha256_in_prefix": "cdc83710ce0639eac15bd734eb796368b96581e799efbd84a45663a7d09d80b2", "size_in_bytes": 5613}, {"_path": "lib/python3.11/site-packages/fontTools/misc/cliTools.py", "path_type": "hardlink", "sha256": "a82ce724c2c242edd91d00ffe1cb549ebdd391f0147641a5f94bb1654ae9a72d", "sha256_in_prefix": "a82ce724c2c242edd91d00ffe1cb549ebdd391f0147641a5f94bb1654ae9a72d", "size_in_bytes": 1862}, {"_path": "lib/python3.11/site-packages/fontTools/misc/configTools.py", "path_type": "hardlink", "sha256": "617044fef2f674c5829cae28637bed535e41efdabcd83b4aa7b87b5d1a897354", "sha256_in_prefix": "617044fef2f674c5829cae28637bed535e41efdabcd83b4aa7b87b5d1a897354", "size_in_bytes": 11188}, {"_path": "lib/python3.11/site-packages/fontTools/misc/cython.py", "path_type": "hardlink", "sha256": "7b22dc2f6070f92493a18ae8f1fe3876491846541f8856e17336b469f4bea871", "sha256_in_prefix": "7b22dc2f6070f92493a18ae8f1fe3876491846541f8856e17336b469f4bea871", "size_in_bytes": 682}, {"_path": "lib/python3.11/site-packages/fontTools/misc/dictTools.py", "path_type": "hardlink", "sha256": "5718daaec189ba4ff06b7cf6f45482b4a64d0857d7b4c062344bb444f025a439", "sha256_in_prefix": "5718daaec189ba4ff06b7cf6f45482b4a64d0857d7b4c062344bb444f025a439", "size_in_bytes": 2417}, {"_path": "lib/python3.11/site-packages/fontTools/misc/eexec.py", "path_type": "hardlink", "sha256": "18d9f638246f3b51db6c87833f193d8b48253bb72ec7f0106a854c5e1051f32f", "sha256_in_prefix": "18d9f638246f3b51db6c87833f193d8b48253bb72ec7f0106a854c5e1051f32f", "size_in_bytes": 3331}, {"_path": "lib/python3.11/site-packages/fontTools/misc/encodingTools.py", "path_type": "hardlink", "sha256": "842bf93c57e75d025509903c5b29f5bebdefccb05b52e10fb469390b358cf516", "sha256_in_prefix": "842bf93c57e75d025509903c5b29f5bebdefccb05b52e10fb469390b358cf516", "size_in_bytes": 2073}, {"_path": "lib/python3.11/site-packages/fontTools/misc/enumTools.py", "path_type": "hardlink", "sha256": "610656f9dd844bd28516401739430805b45493abffdc14fa43b957135b9f8710", "sha256_in_prefix": "610656f9dd844bd28516401739430805b45493abffdc14fa43b957135b9f8710", "size_in_bytes": 502}, {"_path": "lib/python3.11/site-packages/fontTools/misc/etree.py", "path_type": "hardlink", "sha256": "67325ce93bc04b9efd75e02064b543bd363f1de4e6f99b0a279b372d88594926", "sha256_in_prefix": "67325ce93bc04b9efd75e02064b543bd363f1de4e6f99b0a279b372d88594926", "size_in_bytes": 16304}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filenames.py", "path_type": "hardlink", "sha256": "30c08edf18e4d6970373e6daa1b70a77c21da053edf9b706aaef2df075060242", "sha256_in_prefix": "30c08edf18e4d6970373e6daa1b70a77c21da053edf9b706aaef2df075060242", "size_in_bytes": 8223}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/__init__.py", "path_type": "hardlink", "sha256": "8b0a0e8fa0e95ca93cabe351447a8e7d1c4517a95c5c886c200e3a8fe719b305", "sha256_in_prefix": "8b0a0e8fa0e95ca93cabe351447a8e7d1c4517a95c5c886c200e3a8fe719b305", "size_in_bytes": 2011}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "19e3d9d6e593064a279763fa38a571e67aebdb0a8c032a206b2ffc270fb3f679", "sha256_in_prefix": "19e3d9d6e593064a279763fa38a571e67aebdb0a8c032a206b2ffc270fb3f679", "size_in_bytes": 2585}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_base.cpython-311.pyc", "path_type": "hardlink", "sha256": "7c1385e382bea3020711512810e09c7653efaf3ad20e693ea9b9aa0b56338dfd", "sha256_in_prefix": "7c1385e382bea3020711512810e09c7653efaf3ad20e693ea9b9aa0b56338dfd", "size_in_bytes": 8989}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_copy.cpython-311.pyc", "path_type": "hardlink", "sha256": "5d7a396dc7ca2e3d2acd61d51643901b1972a0373fc63dccdaf91e22e93a0e8f", "sha256_in_prefix": "5d7a396dc7ca2e3d2acd61d51643901b1972a0373fc63dccdaf91e22e93a0e8f", "size_in_bytes": 2981}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_errors.cpython-311.pyc", "path_type": "hardlink", "sha256": "48ad63d91dee7c065c804b4de80d5de7bc0a62390f4e0860a009d10146121b53", "sha256_in_prefix": "48ad63d91dee7c065c804b4de80d5de7bc0a62390f4e0860a009d10146121b53", "size_in_bytes": 2801}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "38ce65fd4a72ed0c31f07549f6bed92d328c160f3c90724f90acad4101246fd3", "sha256_in_prefix": "38ce65fd4a72ed0c31f07549f6bed92d328c160f3c90724f90acad4101246fd3", "size_in_bytes": 4577}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_osfs.cpython-311.pyc", "path_type": "hardlink", "sha256": "6945d94ceb2ca99948510d7e0fe8190b3a743960951908022b69e2d22cc8351e", "sha256_in_prefix": "6945d94ceb2ca99948510d7e0fe8190b3a743960951908022b69e2d22cc8351e", "size_in_bytes": 10955}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_path.cpython-311.pyc", "path_type": "hardlink", "sha256": "7348b537e1cc7db1f2f19c46caaae9958aa5f6429f61d8b05d8fddef7abd3565", "sha256_in_prefix": "7348b537e1cc7db1f2f19c46caaae9958aa5f6429f61d8b05d8fddef7abd3565", "size_in_bytes": 3313}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_subfs.cpython-311.pyc", "path_type": "hardlink", "sha256": "d89875564de8fcf383dd6bbb92354d3974ce15f055fa66dae8c2c097a98df8d0", "sha256_in_prefix": "d89875564de8fcf383dd6bbb92354d3974ce15f055fa66dae8c2c097a98df8d0", "size_in_bytes": 8084}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_tempfs.cpython-311.pyc", "path_type": "hardlink", "sha256": "588ee245c6da3849419841b26c8280f3ccf5d3e7cb0d5ccae0a373b571b554ab", "sha256_in_prefix": "588ee245c6da3849419841b26c8280f3ccf5d3e7cb0d5ccae0a373b571b554ab", "size_in_bytes": 2126}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_tools.cpython-311.pyc", "path_type": "hardlink", "sha256": "501da70469516320ecc9321fd9b3b48f67600296b189c9f9d5e6a5759ec69212", "sha256_in_prefix": "501da70469516320ecc9321fd9b3b48f67600296b189c9f9d5e6a5759ec69212", "size_in_bytes": 1837}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_walk.cpython-311.pyc", "path_type": "hardlink", "sha256": "89b3b4d53ca3e8092752a4372f54bbaf1a86c09cb3c5b352cf83a09ece1e92e8", "sha256_in_prefix": "89b3b4d53ca3e8092752a4372f54bbaf1a86c09cb3c5b352cf83a09ece1e92e8", "size_in_bytes": 3521}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/__pycache__/_zipfs.cpython-311.pyc", "path_type": "hardlink", "sha256": "ce9634347a134bf4dba8fb55ae2f04ea1611cbd0e8b780cb1dae5c0295a00f87", "sha256_in_prefix": "ce9634347a134bf4dba8fb55ae2f04ea1611cbd0e8b780cb1dae5c0295a00f87", "size_in_bytes": 12361}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/_base.py", "path_type": "hardlink", "sha256": "a7be0eef144469d7cf420ccf27d98fdde86ed191c382c997969b0bf429d346ca", "sha256_in_prefix": "a7be0eef144469d7cf420ccf27d98fdde86ed191c382c997969b0bf429d346ca", "size_in_bytes": 4010}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/_copy.py", "path_type": "hardlink", "sha256": "89f312b3e03f6f3d406b8b4842b95477d1ed7494397e9e4cdc1ea66d8b83b572", "sha256_in_prefix": "89f312b3e03f6f3d406b8b4842b95477d1ed7494397e9e4cdc1ea66d8b83b572", "size_in_bytes": 1361}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/_errors.py", "path_type": "hardlink", "sha256": "f98ce2441d414f523cd32a66b9fbc247e33fe17a1eac21f2055a97f9c4672335", "sha256_in_prefix": "f98ce2441d414f523cd32a66b9fbc247e33fe17a1eac21f2055a97f9c4672335", "size_in_bytes": 641}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/_info.py", "path_type": "hardlink", "sha256": "a5b57b6c34c9e45f26b3a6a52b7e09d056585b39913bb153d0d337c91037733a", "sha256_in_prefix": "a5b57b6c34c9e45f26b3a6a52b7e09d056585b39913bb153d0d337c91037733a", "size_in_bytes": 2013}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/_osfs.py", "path_type": "hardlink", "sha256": "4642821362317116a3ee0caa156d742e1126ffe54962d5f1b12e6cd030978a13", "sha256_in_prefix": "4642821362317116a3ee0caa156d742e1126ffe54962d5f1b12e6cd030978a13", "size_in_bytes": 5737}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/_path.py", "path_type": "hardlink", "sha256": "7eb3fa64b98c78ff44dcd8b0a026db8013d6a506cb05187b4fed2f384f84b8fa", "sha256_in_prefix": "7eb3fa64b98c78ff44dcd8b0a026db8013d6a506cb05187b4fed2f384f84b8fa", "size_in_bytes": 1745}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/_subfs.py", "path_type": "hardlink", "sha256": "bd1a2d430c8b55f20d6d1f3cc4805051babd8f82a6835fe626f1219e9bcafede", "sha256_in_prefix": "bd1a2d430c8b55f20d6d1f3cc4805051babd8f82a6835fe626f1219e9bcafede", "size_in_bytes": 3028}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/_tempfs.py", "path_type": "hardlink", "sha256": "f45517742053c05b59305c7c82162e65562841d0dbd2d0c1fa35cdbb70fe432d", "sha256_in_prefix": "f45517742053c05b59305c7c82162e65562841d0dbd2d0c1fa35cdbb70fe432d", "size_in_bytes": 924}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/_tools.py", "path_type": "hardlink", "sha256": "afbe5da5a769ec2f44750eabee9250299942643509cd5ab68a46ff2d737ec022", "sha256_in_prefix": "afbe5da5a769ec2f44750eabee9250299942643509cd5ab68a46ff2d737ec022", "size_in_bytes": 972}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/_walk.py", "path_type": "hardlink", "sha256": "28c43e19abd662be12b00e55f2884b3e6cf76e88a5bd8d8fd092ab2e8c56e8d5", "sha256_in_prefix": "28c43e19abd662be12b00e55f2884b3e6cf76e88a5bd8d8fd092ab2e8c56e8d5", "size_in_bytes": 1655}, {"_path": "lib/python3.11/site-packages/fontTools/misc/filesystem/_zipfs.py", "path_type": "hardlink", "sha256": "8b7aa895b903467b41fe82f7bfbf4ab8481f5655688de70f067040d17d383d67", "sha256_in_prefix": "8b7aa895b903467b41fe82f7bfbf4ab8481f5655688de70f067040d17d383d67", "size_in_bytes": 6301}, {"_path": "lib/python3.11/site-packages/fontTools/misc/fixedTools.py", "path_type": "hardlink", "sha256": "82ca2d4c23892f2322b35dcce3f8d027cf903e86f61a5d93b4d2615ba1444752", "sha256_in_prefix": "82ca2d4c23892f2322b35dcce3f8d027cf903e86f61a5d93b4d2615ba1444752", "size_in_bytes": 7647}, {"_path": "lib/python3.11/site-packages/fontTools/misc/intTools.py", "path_type": "hardlink", "sha256": "97aa639385189577322ed7c2d0374e0b944be9427c8a1451d33462628c39c40f", "sha256_in_prefix": "97aa639385189577322ed7c2d0374e0b944be9427c8a1451d33462628c39c40f", "size_in_bytes": 586}, {"_path": "lib/python3.11/site-packages/fontTools/misc/iterTools.py", "path_type": "hardlink", "sha256": "d7b1fa2cf66cce9df66d32a8368ae9eae645d4f2a3e3b0406dee501bc8ab523a", "sha256_in_prefix": "d7b1fa2cf66cce9df66d32a8368ae9eae645d4f2a3e3b0406dee501bc8ab523a", "size_in_bytes": 390}, {"_path": "lib/python3.11/site-packages/fontTools/misc/lazyTools.py", "path_type": "hardlink", "sha256": "042e8c985f8ecc9dc6ac10fc4d80d9f9509237850ec74a4dd2bde8178192a22c", "sha256_in_prefix": "042e8c985f8ecc9dc6ac10fc4d80d9f9509237850ec74a4dd2bde8178192a22c", "size_in_bytes": 1020}, {"_path": "lib/python3.11/site-packages/fontTools/misc/loggingTools.py", "path_type": "hardlink", "sha256": "34e6113ac2cae53cce34ae7debb386755a273725c2ea43ff0a63ebeccd8f5bf7", "sha256_in_prefix": "34e6113ac2cae53cce34ae7debb386755a273725c2ea43ff0a63ebeccd8f5bf7", "size_in_bytes": 19933}, {"_path": "lib/python3.11/site-packages/fontTools/misc/macCreatorType.py", "path_type": "hardlink", "sha256": "25ef63b6a52bec43dba47dd0c65565de98b3a10fb500e3cc04872d1c83133379", "sha256_in_prefix": "25ef63b6a52bec43dba47dd0c65565de98b3a10fb500e3cc04872d1c83133379", "size_in_bytes": 1593}, {"_path": "lib/python3.11/site-packages/fontTools/misc/macRes.py", "path_type": "hardlink", "sha256": "193fe99df3f0d8d0afbce17cea71cb0273ad67a48c1ea12e2e7b5aa655f3bc73", "sha256_in_prefix": "193fe99df3f0d8d0afbce17cea71cb0273ad67a48c1ea12e2e7b5aa655f3bc73", "size_in_bytes": 8579}, {"_path": "lib/python3.11/site-packages/fontTools/misc/plistlib/__init__.py", "path_type": "hardlink", "sha256": "d477e11cfb7702ceaed9e4529457e5e977675efff2a50226790756230eb02bb6", "sha256_in_prefix": "d477e11cfb7702ceaed9e4529457e5e977675efff2a50226790756230eb02bb6", "size_in_bytes": 21113}, {"_path": "lib/python3.11/site-packages/fontTools/misc/plistlib/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "271770923fb5cf8d32228a19c14b8755f6f4432683df28569a9fa26fa8ef5add", "sha256_in_prefix": "271770923fb5cf8d32228a19c14b8755f6f4432683df28569a9fa26fa8ef5add", "size_in_bytes": 29123}, {"_path": "lib/python3.11/site-packages/fontTools/misc/plistlib/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/fontTools/misc/psCharStrings.py", "path_type": "hardlink", "sha256": "667f26afb3514df1096e76976ce9d26abac504f72f3ccd00ba90311760bcd0b6", "sha256_in_prefix": "667f26afb3514df1096e76976ce9d26abac504f72f3ccd00ba90311760bcd0b6", "size_in_bytes": 43468}, {"_path": "lib/python3.11/site-packages/fontTools/misc/psLib.py", "path_type": "hardlink", "sha256": "8a820f9b9c773079015c5dafccd902e225596286eb916732bc586662c8ceacf6", "sha256_in_prefix": "8a820f9b9c773079015c5dafccd902e225596286eb916732bc586662c8ceacf6", "size_in_bytes": 12099}, {"_path": "lib/python3.11/site-packages/fontTools/misc/psOperators.py", "path_type": "hardlink", "sha256": "f522e5e4f3c1ba52e8d17c60fddaa524c8ad34805d88629d91784e5ac3526191", "sha256_in_prefix": "f522e5e4f3c1ba52e8d17c60fddaa524c8ad34805d88629d91784e5ac3526191", "size_in_bytes": 15700}, {"_path": "lib/python3.11/site-packages/fontTools/misc/py23.py", "path_type": "hardlink", "sha256": "68f542114cff75e820c0b0427934ac71c5fa42025abd9aaf755b6e869ceb3ef0", "sha256_in_prefix": "68f542114cff75e820c0b0427934ac71c5fa42025abd9aaf755b6e869ceb3ef0", "size_in_bytes": 2238}, {"_path": "lib/python3.11/site-packages/fontTools/misc/roundTools.py", "path_type": "hardlink", "sha256": "d514976748328b5a96e36b73e964813090f51653ddb60a8a7d68b154df5b77bf", "sha256_in_prefix": "d514976748328b5a96e36b73e964813090f51653ddb60a8a7d68b154df5b77bf", "size_in_bytes": 3173}, {"_path": "lib/python3.11/site-packages/fontTools/misc/sstruct.py", "path_type": "hardlink", "sha256": "bd438377608abc72ed8ebef29f52bde07ba2ff2c4e3d62a696002604c9b72834", "sha256_in_prefix": "bd438377608abc72ed8ebef29f52bde07ba2ff2c4e3d62a696002604c9b72834", "size_in_bytes": 7009}, {"_path": "lib/python3.11/site-packages/fontTools/misc/symfont.py", "path_type": "hardlink", "sha256": "c79670a8af4893da2b1baa927ed8151b2801144d704d29e0acc2b659ed59e403", "sha256_in_prefix": "c79670a8af4893da2b1baa927ed8151b2801144d704d29e0acc2b659ed59e403", "size_in_bytes": 6977}, {"_path": "lib/python3.11/site-packages/fontTools/misc/testTools.py", "path_type": "hardlink", "sha256": "def8ff2a59544151225456d2d12cd399eb8abf8e3e2fe762b08d5d6785e139fc", "sha256_in_prefix": "def8ff2a59544151225456d2d12cd399eb8abf8e3e2fe762b08d5d6785e139fc", "size_in_bytes": 7052}, {"_path": "lib/python3.11/site-packages/fontTools/misc/textTools.py", "path_type": "hardlink", "sha256": "c0d8c7e73975bfda8d7e64e5e012f9d883b6206d47e71637a3fa6c96a3cf4637", "sha256_in_prefix": "c0d8c7e73975bfda8d7e64e5e012f9d883b6206d47e71637a3fa6c96a3cf4637", "size_in_bytes": 3483}, {"_path": "lib/python3.11/site-packages/fontTools/misc/timeTools.py", "path_type": "hardlink", "sha256": "7bd879a60ccbd38b410d7982bfff1e4460786e8155f06297952e9dab7820129c", "sha256_in_prefix": "7bd879a60ccbd38b410d7982bfff1e4460786e8155f06297952e9dab7820129c", "size_in_bytes": 2234}, {"_path": "lib/python3.11/site-packages/fontTools/misc/transform.py", "path_type": "hardlink", "sha256": "391f1d3ec030f3bcfbee091940cab4d225240d62c8c58bfed765e2287d7e7ab9", "sha256_in_prefix": "391f1d3ec030f3bcfbee091940cab4d225240d62c8c58bfed765e2287d7e7ab9", "size_in_bytes": 15798}, {"_path": "lib/python3.11/site-packages/fontTools/misc/treeTools.py", "path_type": "hardlink", "sha256": "b4b5a4c320c779950f54e18d9c9783e0f9fbc766d07997adc0929a1005b62763", "sha256_in_prefix": "b4b5a4c320c779950f54e18d9c9783e0f9fbc766d07997adc0929a1005b62763", "size_in_bytes": 1269}, {"_path": "lib/python3.11/site-packages/fontTools/misc/vector.py", "path_type": "hardlink", "sha256": "ea5a997038c08072454208f30fe50b43f3eb8a000c7de64a68166601f702d228", "sha256_in_prefix": "ea5a997038c08072454208f30fe50b43f3eb8a000c7de64a68166601f702d228", "size_in_bytes": 4062}, {"_path": "lib/python3.11/site-packages/fontTools/misc/visitor.py", "path_type": "hardlink", "sha256": "73d610b35e1b5df2f54a6ab0a00844182bd7966c9920a479fd5af89fb896691b", "sha256_in_prefix": "73d610b35e1b5df2f54a6ab0a00844182bd7966c9920a479fd5af89fb896691b", "size_in_bytes": 5610}, {"_path": "lib/python3.11/site-packages/fontTools/misc/xmlReader.py", "path_type": "hardlink", "sha256": "8a0bade3f775dd14f859aae58aa56fbae3f26ced6e49755ea0139e5b88f4fdee", "sha256_in_prefix": "8a0bade3f775dd14f859aae58aa56fbae3f26ced6e49755ea0139e5b88f4fdee", "size_in_bytes": 6580}, {"_path": "lib/python3.11/site-packages/fontTools/misc/xmlWriter.py", "path_type": "hardlink", "sha256": "0ab35741f34945db79087280678faacb7875c8978beb7a2dd7b91790d6de23f1", "sha256_in_prefix": "0ab35741f34945db79087280678faacb7875c8978beb7a2dd7b91790d6de23f1", "size_in_bytes": 6829}, {"_path": "lib/python3.11/site-packages/fontTools/mtiLib/__init__.py", "path_type": "hardlink", "sha256": "13363035a10d2dff74ea1d531c17aaea74911d42a9380631bb33bdc7d3c7ce1f", "sha256_in_prefix": "13363035a10d2dff74ea1d531c17aaea74911d42a9380631bb33bdc7d3c7ce1f", "size_in_bytes": 46602}, {"_path": "lib/python3.11/site-packages/fontTools/mtiLib/__main__.py", "path_type": "hardlink", "sha256": "81df17f3d8e764e7beef9da4eee691d655a88a3bbedb3213e58c77e4a9745de9", "sha256_in_prefix": "81df17f3d8e764e7beef9da4eee691d655a88a3bbedb3213e58c77e4a9745de9", "size_in_bytes": 94}, {"_path": "lib/python3.11/site-packages/fontTools/mtiLib/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "609583d836645d55c9ef8507a8d2da97c582719948ae60a6956c57125261594e", "sha256_in_prefix": "609583d836645d55c9ef8507a8d2da97c582719948ae60a6956c57125261594e", "size_in_bytes": 83480}, {"_path": "lib/python3.11/site-packages/fontTools/mtiLib/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "784a6af87c17c6d3575ef72965ff02b784e5141869c4bf4750f2a19b09c5bd14", "sha256_in_prefix": "784a6af87c17c6d3575ef72965ff02b784e5141869c4bf4750f2a19b09c5bd14", "size_in_bytes": 380}, {"_path": "lib/python3.11/site-packages/fontTools/otlLib/__init__.py", "path_type": "hardlink", "sha256": "0f695e516fb782c5133857096060b5f1e741623209f34e2eb78aa2b49616b1a4", "sha256_in_prefix": "0f695e516fb782c5133857096060b5f1e741623209f34e2eb78aa2b49616b1a4", "size_in_bytes": 45}, {"_path": "lib/python3.11/site-packages/fontTools/otlLib/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "1c7551839c70d510e7bc55856e3c38e1b7f5537018c37fefc1b58c5f990eb657", "sha256_in_prefix": "1c7551839c70d510e7bc55856e3c38e1b7f5537018c37fefc1b58c5f990eb657", "size_in_bytes": 218}, {"_path": "lib/python3.11/site-packages/fontTools/otlLib/__pycache__/builder.cpython-311.pyc", "path_type": "hardlink", "sha256": "f32170aa2716ba02e176477f079e8f8f8234860fc3c7036a66faf03b7f9fa393", "sha256_in_prefix": "f32170aa2716ba02e176477f079e8f8f8234860fc3c7036a66faf03b7f9fa393", "size_in_bytes": 171346}, {"_path": "lib/python3.11/site-packages/fontTools/otlLib/__pycache__/error.cpython-311.pyc", "path_type": "hardlink", "sha256": "10d2e4e49ac8c80ffa4846673c48e72e782718770ba0e0ac2c4e62878438659e", "sha256_in_prefix": "10d2e4e49ac8c80ffa4846673c48e72e782718770ba0e0ac2c4e62878438659e", "size_in_bytes": 984}, {"_path": "lib/python3.11/site-packages/fontTools/otlLib/__pycache__/maxContextCalc.cpython-311.pyc", "path_type": "hardlink", "sha256": "39f33c59bfa953b80ab8796650bd0fff7e290021b0d2a6c35e950ebfe654699a", "sha256_in_prefix": "39f33c59bfa953b80ab8796650bd0fff7e290021b0d2a6c35e950ebfe654699a", "size_in_bytes": 3806}, {"_path": "lib/python3.11/site-packages/fontTools/otlLib/builder.py", "path_type": "hardlink", "sha256": "307b902ae977365defcd7998ed66fac79e1935b2465334540fbf952a9386ee51", "sha256_in_prefix": "307b902ae977365defcd7998ed66fac79e1935b2465334540fbf952a9386ee51", "size_in_bytes": 128727}, {"_path": "lib/python3.11/site-packages/fontTools/otlLib/error.py", "path_type": "hardlink", "sha256": "72d86e841b8ec196299132e2e6014fba95319179021def8bfd882413b375c022", "sha256_in_prefix": "72d86e841b8ec196299132e2e6014fba95319179021def8bfd882413b375c022", "size_in_bytes": 335}, {"_path": "lib/python3.11/site-packages/fontTools/otlLib/maxContextCalc.py", "path_type": "hardlink", "sha256": "ddeb382adf384da678f6c0367afd73ae5c0f262909080102c7929abf087207e2", "sha256_in_prefix": "ddeb382adf384da678f6c0367afd73ae5c0f262909080102c7929abf087207e2", "size_in_bytes": 3175}, {"_path": "lib/python3.11/site-packages/fontTools/otlLib/optimize/__init__.py", "path_type": "hardlink", "sha256": "514411a4d90753645ccc246df86cfbb0489813d010abd0472d76443b2bec9d7e", "sha256_in_prefix": "514411a4d90753645ccc246df86cfbb0489813d010abd0472d76443b2bec9d7e", "size_in_bytes": 1530}, {"_path": "lib/python3.11/site-packages/fontTools/otlLib/optimize/__main__.py", "path_type": "hardlink", "sha256": "06f3f8ef6900f4ac416fd44ccb27a13cd7af01fa66816f4c7dd6b39148803b73", "sha256_in_prefix": "06f3f8ef6900f4ac416fd44ccb27a13cd7af01fa66816f4c7dd6b39148803b73", "size_in_bytes": 104}, {"_path": "lib/python3.11/site-packages/fontTools/otlLib/optimize/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "fee5cd66219fb661333160d011cfb7a97872927716f5aebb3ea12e5a5684fc6f", "sha256_in_prefix": "fee5cd66219fb661333160d011cfb7a97872927716f5aebb3ea12e5a5684fc6f", "size_in_bytes": 2743}, {"_path": "lib/python3.11/site-packages/fontTools/otlLib/optimize/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "3c5592107d2087f8f92803768bf86e45127375929466873d7a15fe6ccfa0cf3a", "sha256_in_prefix": "3c5592107d2087f8f92803768bf86e45127375929466873d7a15fe6ccfa0cf3a", "size_in_bytes": 400}, {"_path": "lib/python3.11/site-packages/fontTools/otlLib/optimize/__pycache__/gpos.cpython-311.pyc", "path_type": "hardlink", "sha256": "2030a800096a75e4bb4568f89ee28e70c8b4c6ad54ce3a8a7736976b00015317", "sha256_in_prefix": "2030a800096a75e4bb4568f89ee28e70c8b4c6ad54ce3a8a7736976b00015317", "size_in_bytes": 21024}, {"_path": "lib/python3.11/site-packages/fontTools/otlLib/optimize/gpos.py", "path_type": "hardlink", "sha256": "86d3a048fef8dc364350a17779601e27e91da8d60e9e919776557eadb1172750", "sha256_in_prefix": "86d3a048fef8dc364350a17779601e27e91da8d60e9e919776557eadb1172750", "size_in_bytes": 17668}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__init__.py", "path_type": "hardlink", "sha256": "0c90569a85ff1da6aeeea9609af5b27db852ceb5f6a8beb7e919ecec21b4d699", "sha256_in_prefix": "0c90569a85ff1da6aeeea9609af5b27db852ceb5f6a8beb7e919ecec21b4d699", "size_in_bytes": 75}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "e379174a1b76dbe9e92ee81fe2ef5b326b8708433d464d9688a4f75a9d7b3755", "sha256_in_prefix": "e379174a1b76dbe9e92ee81fe2ef5b326b8708433d464d9688a4f75a9d7b3755", "size_in_bytes": 246}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/areaPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "d23a252638d88f88e53762646651649957077b40087e9f52e8738163ff5dd05d", "sha256_in_prefix": "d23a252638d88f88e53762646651649957077b40087e9f52e8738163ff5dd05d", "size_in_bytes": 3168}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/basePen.cpython-311.pyc", "path_type": "hardlink", "sha256": "484f410e9b4d462e1f0f2645cb81af84ed62a18b79a35c240bbe6309afd77212", "sha256_in_prefix": "484f410e9b4d462e1f0f2645cb81af84ed62a18b79a35c240bbe6309afd77212", "size_in_bytes": 22349}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/boundsPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "66dc893ef3aafd7f7821a22ceba8760031f94f33c07e0d67058604a3963b6a39", "sha256_in_prefix": "66dc893ef3aafd7f7821a22ceba8760031f94f33c07e0d67058604a3963b6a39", "size_in_bytes": 5224}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/cairoPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "6134dd394659503ebeb1e328046aa3e28695f9ab55cff5f7ddfc16cd3593e1b6", "sha256_in_prefix": "6134dd394659503ebeb1e328046aa3e28695f9ab55cff5f7ddfc16cd3593e1b6", "size_in_bytes": 1669}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/cocoaPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "152d76a9166faf250691b02936283f50c341d8917ecc2a10a6de525202e7b099", "sha256_in_prefix": "152d76a9166faf250691b02936283f50c341d8917ecc2a10a6de525202e7b099", "size_in_bytes": 1829}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/cu2quPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "b6af019e0f2e9d45267b43f5189b1bf492b2b094cf43ba50372322c572c0c61f", "sha256_in_prefix": "b6af019e0f2e9d45267b43f5189b1bf492b2b094cf43ba50372322c572c0c61f", "size_in_bytes": 17116}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/explicitClosingLinePen.cpython-311.pyc", "path_type": "hardlink", "sha256": "f0eab74030395ccd09a5da7987ed02447897b552b25f1fccbbc10db9518cec96", "sha256_in_prefix": "f0eab74030395ccd09a5da7987ed02447897b552b25f1fccbbc10db9518cec96", "size_in_bytes": 3924}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/filterPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "c9ae9a980c6ba28a35cb320b1891762be1676666f60a83221e86f5fe5dc8f010", "sha256_in_prefix": "c9ae9a980c6ba28a35cb320b1891762be1676666f60a83221e86f5fe5dc8f010", "size_in_bytes": 19793}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/freetypePen.cpython-311.pyc", "path_type": "hardlink", "sha256": "ffe42f86c7be110085a460677aadd822e86fcc213d62755bf498705657f62d83", "sha256_in_prefix": "ffe42f86c7be110085a460677aadd822e86fcc213d62755bf498705657f62d83", "size_in_bytes": 24701}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/hashPointPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "520b5dcae34ca3fa02b25778739da28f4f7447bec0404697a6e97e6ea0194a6f", "sha256_in_prefix": "520b5dcae34ca3fa02b25778739da28f4f7447bec0404697a6e97e6ea0194a6f", "size_in_bytes": 5313}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/momentsPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "cca3292c9e094a4e9b15f069a1b4903115c6723ca6e252a30b66dc0944786c13", "sha256_in_prefix": "cca3292c9e094a4e9b15f069a1b4903115c6723ca6e252a30b66dc0944786c13", "size_in_bytes": 40485}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/perimeterPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "fcbb3b31350cddb4ca7c107eb7fda458ad70cc81ac1abc73dd76624e39a1769a", "sha256_in_prefix": "fcbb3b31350cddb4ca7c107eb7fda458ad70cc81ac1abc73dd76624e39a1769a", "size_in_bytes": 4371}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/pointInsidePen.cpython-311.pyc", "path_type": "hardlink", "sha256": "8cb03b2e3147caf389d9044020fe320921996352056c8e7fa373dcf260fbc025", "sha256_in_prefix": "8cb03b2e3147caf389d9044020fe320921996352056c8e7fa373dcf260fbc025", "size_in_bytes": 7900}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/pointPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "c816c3698ec7d4bdc66f8c4589e174ebf15570ee6230734da33edc042fd38cb4", "sha256_in_prefix": "c816c3698ec7d4bdc66f8c4589e174ebf15570ee6230734da33edc042fd38cb4", "size_in_bytes": 27816}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/qtPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "01886644b75b12ea1aa9c6bfd95a1b9492de6fcfdc5f2903e70d8e6cd194fed4", "sha256_in_prefix": "01886644b75b12ea1aa9c6bfd95a1b9492de6fcfdc5f2903e70d8e6cd194fed4", "size_in_bytes": 1916}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/qu2cuPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "6e307dbaf814ce444f1f4b450c67f2f5d539e79f962a69ede28380143596e6dc", "sha256_in_prefix": "6e307dbaf814ce444f1f4b450c67f2f5d539e79f962a69ede28380143596e6dc", "size_in_bytes": 4485}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/quartzPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "0b4aa71827406e5b8aeddf770b20f2a9d2fe79540e9e46daec521a304a9b1c51", "sha256_in_prefix": "0b4aa71827406e5b8aeddf770b20f2a9d2fe79540e9e46daec521a304a9b1c51", "size_in_bytes": 2724}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/recordingPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "f3037cc86e132c5221178fcffe88d00d0ee21fd51ac395cd0093670888dd9c3a", "sha256_in_prefix": "f3037cc86e132c5221178fcffe88d00d0ee21fd51ac395cd0093670888dd9c3a", "size_in_bytes": 16358}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/reportLabPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "b788d460f4a1dff26cd7efcb71c3de0f9c4b5af615643c809c4298279e53c601", "sha256_in_prefix": "b788d460f4a1dff26cd7efcb71c3de0f9c4b5af615643c809c4298279e53c601", "size_in_bytes": 3986}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/reverseContourPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "420432fb201bfb0409ab01bafcc658363b89fa2b48eed50309dbaf78fa8bb1e3", "sha256_in_prefix": "420432fb201bfb0409ab01bafcc658363b89fa2b48eed50309dbaf78fa8bb1e3", "size_in_bytes": 3995}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/roundingPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "3f2766763c18ce38d93607648cee49687230607d8efba7cc9786e861c1160a15", "sha256_in_prefix": "3f2766763c18ce38d93607648cee49687230607d8efba7cc9786e861c1160a15", "size_in_bytes": 7453}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/statisticsPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "c83389a665df3bbfcf44119f7a2c17a72171bbbc5e028b9161c059594349ba66", "sha256_in_prefix": "c83389a665df3bbfcf44119f7a2c17a72171bbbc5e028b9161c059594349ba66", "size_in_bytes": 14837}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/svgPathPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "5de3abf25c021db072b2ae3e0d0370dc16c2719c8d323b153e5bc107ed93db04", "sha256_in_prefix": "5de3abf25c021db072b2ae3e0d0370dc16c2719c8d323b153e5bc107ed93db04", "size_in_bytes": 12504}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/t2CharStringPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "8da8306d3ad6471e820512620bb0e5c43fc126510b436bf3aa84b73ca34ffd94", "sha256_in_prefix": "8da8306d3ad6471e820512620bb0e5c43fc126510b436bf3aa84b73ca34ffd94", "size_in_bytes": 5113}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/teePen.cpython-311.pyc", "path_type": "hardlink", "sha256": "a61cfd4c2c44cb13a56245335b525d90253061f6d4726e9044bf766b00cd72f7", "sha256_in_prefix": "a61cfd4c2c44cb13a56245335b525d90253061f6d4726e9044bf766b00cd72f7", "size_in_bytes": 3135}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/transformPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "a79ee1ab09f841f60253ab9b3d2806d9fcd77d16d70896206a1971e5b4ee6311", "sha256_in_prefix": "a79ee1ab09f841f60253ab9b3d2806d9fcd77d16d70896206a1971e5b4ee6311", "size_in_bytes": 7056}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/ttGlyphPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "aa516b38e1f31bd15a88dfef80ac8f8365234b2c7e87ab528674c14a6f5feb87", "sha256_in_prefix": "aa516b38e1f31bd15a88dfef80ac8f8365234b2c7e87ab528674c14a6f5feb87", "size_in_bytes": 18179}, {"_path": "lib/python3.11/site-packages/fontTools/pens/__pycache__/wxPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "a29ce3f12cb8e26e47c165fc23b49ff6e14a567367d64a4faf86422d5f2f0b07", "sha256_in_prefix": "a29ce3f12cb8e26e47c165fc23b49ff6e14a567367d64a4faf86422d5f2f0b07", "size_in_bytes": 2043}, {"_path": "lib/python3.11/site-packages/fontTools/pens/areaPen.py", "path_type": "hardlink", "sha256": "6355a49aacdc0b8cff6e918047421950aaed1c5b712944019abe7eeb8ff308e9", "sha256_in_prefix": "6355a49aacdc0b8cff6e918047421950aaed1c5b712944019abe7eeb8ff308e9", "size_in_bytes": 1472}, {"_path": "lib/python3.11/site-packages/fontTools/pens/basePen.py", "path_type": "hardlink", "sha256": "7881922ab2a6eb0e0b2c7b86e97268419dde39bb68295e4fe9a178813e2c93b5", "sha256_in_prefix": "7881922ab2a6eb0e0b2c7b86e97268419dde39bb68295e4fe9a178813e2c93b5", "size_in_bytes": 17073}, {"_path": "lib/python3.11/site-packages/fontTools/pens/boundsPen.py", "path_type": "hardlink", "sha256": "c04de8c0e400f037e11fecc1182de526f9d5c29fa8c88b74299ac4a976e64bd2", "sha256_in_prefix": "c04de8c0e400f037e11fecc1182de526f9d5c29fa8c88b74299ac4a976e64bd2", "size_in_bytes": 3129}, {"_path": "lib/python3.11/site-packages/fontTools/pens/cairoPen.py", "path_type": "hardlink", "sha256": "c2eb8e275a900d2b7f2b7cec70cda7ba44721d94d9330333cc25c28ab7eafea4", "sha256_in_prefix": "c2eb8e275a900d2b7f2b7cec70cda7ba44721d94d9330333cc25c28ab7eafea4", "size_in_bytes": 592}, {"_path": "lib/python3.11/site-packages/fontTools/pens/cocoaPen.py", "path_type": "hardlink", "sha256": "209450700c51b9539343dd1b07f0608e79b3ee9c7f493e6e2c5f425be63428f6", "sha256_in_prefix": "209450700c51b9539343dd1b07f0608e79b3ee9c7f493e6e2c5f425be63428f6", "size_in_bytes": 612}, {"_path": "lib/python3.11/site-packages/fontTools/pens/cu2quPen.py", "path_type": "hardlink", "sha256": "80c530154b26ffe5b30650e34cc42236712e23685ea2619e389057f35cd85cfa", "sha256_in_prefix": "80c530154b26ffe5b30650e34cc42236712e23685ea2619e389057f35cd85cfa", "size_in_bytes": 13007}, {"_path": "lib/python3.11/site-packages/fontTools/pens/explicitClosingLinePen.py", "path_type": "hardlink", "sha256": "90a2ad7598b069ff028f8ff2b6b20374607618ca4f3c35e6e4dc1bc395838305", "sha256_in_prefix": "90a2ad7598b069ff028f8ff2b6b20374607618ca4f3c35e6e4dc1bc395838305", "size_in_bytes": 3219}, {"_path": "lib/python3.11/site-packages/fontTools/pens/filterPen.py", "path_type": "hardlink", "sha256": "44482ca5769a4af1775d4c2a7b8d1a6dedd7fc4efe59b04fd7722a2d45012c2c", "sha256_in_prefix": "44482ca5769a4af1775d4c2a7b8d1a6dedd7fc4efe59b04fd7722a2d45012c2c", "size_in_bytes": 14703}, {"_path": "lib/python3.11/site-packages/fontTools/pens/freetypePen.py", "path_type": "hardlink", "sha256": "1c3fa05c949b80898974173cb08064d07581763bf758a1687ece8f8020ac18e6", "sha256_in_prefix": "1c3fa05c949b80898974173cb08064d07581763bf758a1687ece8f8020ac18e6", "size_in_bytes": 19908}, {"_path": "lib/python3.11/site-packages/fontTools/pens/hashPointPen.py", "path_type": "hardlink", "sha256": "80496b172428390a7765ba4a1d158fc02eb503d3a04f626cf1cad5503f014006", "sha256_in_prefix": "80496b172428390a7765ba4a1d158fc02eb503d3a04f626cf1cad5503f014006", "size_in_bytes": 3573}, {"_path": "lib/python3.11/site-packages/fontTools/pens/momentsPen.c", "path_type": "hardlink", "sha256": "9d795ac72f5c7a3cb53165ba3420dc93bd412827e773f136e41ae66175cf0b5f", "sha256_in_prefix": "9d795ac72f5c7a3cb53165ba3420dc93bd412827e773f136e41ae66175cf0b5f", "size_in_bytes": 565310}, {"_path": "lib/python3.11/site-packages/fontTools/pens/momentsPen.cpython-311-darwin.so", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/fonttools_1758132623042/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold", "sha256": "edcbc8418e34631ea246ae8b0754fadff4bdd218e5badd3491bd0af338e59b83", "sha256_in_prefix": "a219144baaa5db5f69efe5696e40eab89405c3445d2bb44e818b45f2d998e4a3", "size_in_bytes": 107616}, {"_path": "lib/python3.11/site-packages/fontTools/pens/momentsPen.py", "path_type": "hardlink", "sha256": "9232d55e119ee7901b97ffd8af58286f46e5d1d1deedf3d2c32afb4d12676ee8", "sha256_in_prefix": "9232d55e119ee7901b97ffd8af58286f46e5d1d1deedf3d2c32afb4d12676ee8", "size_in_bytes": 25658}, {"_path": "lib/python3.11/site-packages/fontTools/pens/perimeterPen.py", "path_type": "hardlink", "sha256": "96be8dceb216c62e135c124f6dc26c2b3a800567d07a29760609bd060503dcde", "sha256_in_prefix": "96be8dceb216c62e135c124f6dc26c2b3a800567d07a29760609bd060503dcde", "size_in_bytes": 2153}, {"_path": "lib/python3.11/site-packages/fontTools/pens/pointInsidePen.py", "path_type": "hardlink", "sha256": "9e8114bc140878085e0cc270cefbdf9c488a866c1b4b58b445013d8e293a1a5e", "sha256_in_prefix": "9e8114bc140878085e0cc270cefbdf9c488a866c1b4b58b445013d8e293a1a5e", "size_in_bytes": 6355}, {"_path": "lib/python3.11/site-packages/fontTools/pens/pointPen.py", "path_type": "hardlink", "sha256": "a1e13fb9a6d508d2752e99391e7dde06669f69aa3742a28c8cae85032d21281a", "sha256_in_prefix": "a1e13fb9a6d508d2752e99391e7dde06669f69aa3742a28c8cae85032d21281a", "size_in_bytes": 24197}, {"_path": "lib/python3.11/site-packages/fontTools/pens/qtPen.py", "path_type": "hardlink", "sha256": "41134b22bcb6ad097813fedcb76b6ed74faa2c79de429d1757b15f699fad70bf", "sha256_in_prefix": "41134b22bcb6ad097813fedcb76b6ed74faa2c79de429d1757b15f699fad70bf", "size_in_bytes": 634}, {"_path": "lib/python3.11/site-packages/fontTools/pens/qu2cuPen.py", "path_type": "hardlink", "sha256": "a51493e37fab529ce538ff3767f46bd08bc84010b1e91588ea79cd6a2b5070b9", "sha256_in_prefix": "a51493e37fab529ce538ff3767f46bd08bc84010b1e91588ea79cd6a2b5070b9", "size_in_bytes": 3985}, {"_path": "lib/python3.11/site-packages/fontTools/pens/quartzPen.py", "path_type": "hardlink", "sha256": "107e3cd8acffc6ca98855468befe8dfd3d425da4af3b350a3cbc5368df79ead5", "sha256_in_prefix": "107e3cd8acffc6ca98855468befe8dfd3d425da4af3b350a3cbc5368df79ead5", "size_in_bytes": 1287}, {"_path": "lib/python3.11/site-packages/fontTools/pens/recordingPen.py", "path_type": "hardlink", "sha256": "560159e0d3219d9b75a924f31445347266be830de405ee3b6df4b13d81fbdebb", "sha256_in_prefix": "560159e0d3219d9b75a924f31445347266be830de405ee3b6df4b13d81fbdebb", "size_in_bytes": 12489}, {"_path": "lib/python3.11/site-packages/fontTools/pens/reportLabPen.py", "path_type": "hardlink", "sha256": "9297cc7ce2d7b76bce439b263ec53cd9fb7cd05a423d627340b97b10d387f04c", "sha256_in_prefix": "9297cc7ce2d7b76bce439b263ec53cd9fb7cd05a423d627340b97b10d387f04c", "size_in_bytes": 2066}, {"_path": "lib/python3.11/site-packages/fontTools/pens/reverseContourPen.py", "path_type": "hardlink", "sha256": "a33eb865184b02f4fb0d8300c062a82f365740af25f358d18989d36645ba6be6", "sha256_in_prefix": "a33eb865184b02f4fb0d8300c062a82f365740af25f358d18989d36645ba6be6", "size_in_bytes": 4022}, {"_path": "lib/python3.11/site-packages/fontTools/pens/roundingPen.py", "path_type": "hardlink", "sha256": "be1fc58e291177cdbe4b823c82580630612e1ab8f92240a3453ff0999f237ea6", "sha256_in_prefix": "be1fc58e291177cdbe4b823c82580630612e1ab8f92240a3453ff0999f237ea6", "size_in_bytes": 4620}, {"_path": "lib/python3.11/site-packages/fontTools/pens/statisticsPen.py", "path_type": "hardlink", "sha256": "a6258ae8d8e35aa93d30b44e8de136fb812cc5560cc8d53b5101460ffb6b13d8", "sha256_in_prefix": "a6258ae8d8e35aa93d30b44e8de136fb812cc5560cc8d53b5101460ffb6b13d8", "size_in_bytes": 9808}, {"_path": "lib/python3.11/site-packages/fontTools/pens/svgPathPen.py", "path_type": "hardlink", "sha256": "4f76fa4994bd07db1558c2bd992143b631debe242cff23893992aae5283965d8", "sha256_in_prefix": "4f76fa4994bd07db1558c2bd992143b631debe242cff23893992aae5283965d8", "size_in_bytes": 8572}, {"_path": "lib/python3.11/site-packages/fontTools/pens/t2CharStringPen.py", "path_type": "hardlink", "sha256": "1a01a495be57b027abd30dfbba38112d15f1f84bb3745b32098bb30b1e27f90b", "sha256_in_prefix": "1a01a495be57b027abd30dfbba38112d15f1f84bb3745b32098bb30b1e27f90b", "size_in_bytes": 2931}, {"_path": "lib/python3.11/site-packages/fontTools/pens/teePen.py", "path_type": "hardlink", "sha256": "3f501124e08c27a3312be3c1d7267e8a9b370947e769d5989d0fdda3a5486ac4", "sha256_in_prefix": "3f501124e08c27a3312be3c1d7267e8a9b370947e769d5989d0fdda3a5486ac4", "size_in_bytes": 1290}, {"_path": "lib/python3.11/site-packages/fontTools/pens/transformPen.py", "path_type": "hardlink", "sha256": "b34914c907677a65301ef62bd9215ba059a1e166354bd38704bf0be0f24a4701", "sha256_in_prefix": "b34914c907677a65301ef62bd9215ba059a1e166354bd38704bf0be0f24a4701", "size_in_bytes": 4056}, {"_path": "lib/python3.11/site-packages/fontTools/pens/ttGlyphPen.py", "path_type": "hardlink", "sha256": "c8bb41f84e694d0479f4e2956324adb5606ed710b6bd1f1ecd269184832d5708", "sha256_in_prefix": "c8bb41f84e694d0479f4e2956324adb5606ed710b6bd1f1ecd269184832d5708", "size_in_bytes": 11870}, {"_path": "lib/python3.11/site-packages/fontTools/pens/wxPen.py", "path_type": "hardlink", "sha256": "5bd4511e50561e9f82542e04c6f937cad06645a078f8b8093cfe41bfba3d040d", "sha256_in_prefix": "5bd4511e50561e9f82542e04c6f937cad06645a078f8b8093cfe41bfba3d040d", "size_in_bytes": 680}, {"_path": "lib/python3.11/site-packages/fontTools/qu2cu/__init__.py", "path_type": "hardlink", "sha256": "25f9b52658d76edf75c38832bd99fa8f3126567851c79d2c8767c3a2782b3ac1", "sha256_in_prefix": "25f9b52658d76edf75c38832bd99fa8f3126567851c79d2c8767c3a2782b3ac1", "size_in_bytes": 618}, {"_path": "lib/python3.11/site-packages/fontTools/qu2cu/__main__.py", "path_type": "hardlink", "sha256": "f4559fe92219691682f1288bd0b8630160b6c88758f4dffdc25464a3c9b59764", "sha256_in_prefix": "f4559fe92219691682f1288bd0b8630160b6c88758f4dffdc25464a3c9b59764", "size_in_bytes": 93}, {"_path": "lib/python3.11/site-packages/fontTools/qu2cu/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "347560a82b3f3cded65c9727763cc49282a11eef4da4da8aaba1e513aaf5a231", "sha256_in_prefix": "347560a82b3f3cded65c9727763cc49282a11eef4da4da8aaba1e513aaf5a231", "size_in_bytes": 197}, {"_path": "lib/python3.11/site-packages/fontTools/qu2cu/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "68f7f429170d4439c7ba247e0500e47442f06e52a31269df87818b329ab8259a", "sha256_in_prefix": "68f7f429170d4439c7ba247e0500e47442f06e52a31269df87818b329ab8259a", "size_in_bytes": 380}, {"_path": "lib/python3.11/site-packages/fontTools/qu2cu/__pycache__/benchmark.cpython-311.pyc", "path_type": "hardlink", "sha256": "cdd673eeb18c69bd75d84934b8b5a772076db28a43a3c2dfb4d37f5741b6e9e9", "sha256_in_prefix": "cdd673eeb18c69bd75d84934b8b5a772076db28a43a3c2dfb4d37f5741b6e9e9", "size_in_bytes": 3623}, {"_path": "lib/python3.11/site-packages/fontTools/qu2cu/__pycache__/cli.cpython-311.pyc", "path_type": "hardlink", "sha256": "dd674177610ab8a9b2473005a0433741385cdbd844f170af90f89bcdfb3260f3", "sha256_in_prefix": "dd674177610ab8a9b2473005a0433741385cdbd844f170af90f89bcdfb3260f3", "size_in_bytes": 5955}, {"_path": "lib/python3.11/site-packages/fontTools/qu2cu/__pycache__/qu2cu.cpython-311.pyc", "path_type": "hardlink", "sha256": "c8c5c166ca72fd6116b7d0e5208f2c38d63217c78f8e4bde0c93fd983bdb93fe", "sha256_in_prefix": "c8c5c166ca72fd6116b7d0e5208f2c38d63217c78f8e4bde0c93fd983bdb93fe", "size_in_bytes": 16282}, {"_path": "lib/python3.11/site-packages/fontTools/qu2cu/benchmark.py", "path_type": "hardlink", "sha256": "18c72bff8afb2fa2bd4a6275de2b6dfbf5ca8672aa4955033e55d41ba2199a63", "sha256_in_prefix": "18c72bff8afb2fa2bd4a6275de2b6dfbf5ca8672aa4955033e55d41ba2199a63", "size_in_bytes": 1400}, {"_path": "lib/python3.11/site-packages/fontTools/qu2cu/cli.py", "path_type": "hardlink", "sha256": "536ae8a189d55446a51910161851e4f8aa7a3a4cacf30b7375a58b8f0d5b9e06", "sha256_in_prefix": "536ae8a189d55446a51910161851e4f8aa7a3a4cacf30b7375a58b8f0d5b9e06", "size_in_bytes": 3714}, {"_path": "lib/python3.11/site-packages/fontTools/qu2cu/qu2cu.c", "path_type": "hardlink", "sha256": "93ee82d9edbf9bfd5c06e6284a21c2635641712aad66389f7cb0a08622426033", "sha256_in_prefix": "93ee82d9edbf9bfd5c06e6284a21c2635641712aad66389f7cb0a08622426033", "size_in_bytes": 690421}, {"_path": "lib/python3.11/site-packages/fontTools/qu2cu/qu2cu.cpython-311-darwin.so", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/fonttools_1758132623042/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold", "sha256": "0622aa0505437abb7c0f8bea6e94a3e29e23b98dcbad79807eb1cb7d21deaa27", "sha256_in_prefix": "5c756b9520310586ef0ba720a0d93ae0a12d6d260ecea2a4adb9ee49dd6ed207", "size_in_bytes": 133576}, {"_path": "lib/python3.11/site-packages/fontTools/qu2cu/qu2cu.py", "path_type": "hardlink", "sha256": "218b699301dd7ca39726beb963fa49f4baedfcc809688480286300b398a51523", "sha256_in_prefix": "218b699301dd7ca39726beb963fa49f4baedfcc809688480286300b398a51523", "size_in_bytes": 12288}, {"_path": "lib/python3.11/site-packages/fontTools/subset/__init__.py", "path_type": "hardlink", "sha256": "42685bb6bd3b8786b5e9d1d650fe3a8c5f0daf881de278c809c355045c9869a7", "sha256_in_prefix": "42685bb6bd3b8786b5e9d1d650fe3a8c5f0daf881de278c809c355045c9869a7", "size_in_bytes": 137742}, {"_path": "lib/python3.11/site-packages/fontTools/subset/__main__.py", "path_type": "hardlink", "sha256": "6e1b5f3f64aa3f893bf7da71b64b058270be5c63500ebdcb70ee2573c4f97b98", "sha256_in_prefix": "6e1b5f3f64aa3f893bf7da71b64b058270be5c63500ebdcb70ee2573c4f97b98", "size_in_bytes": 95}, {"_path": "lib/python3.11/site-packages/fontTools/subset/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "78fe8780e43d2697d64ffa3a6ba44d75235dee1360c1d7b24470909d17a1776f", "sha256_in_prefix": "78fe8780e43d2697d64ffa3a6ba44d75235dee1360c1d7b24470909d17a1776f", "size_in_bytes": 217996}, {"_path": "lib/python3.11/site-packages/fontTools/subset/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "6f8a72800f971f64ee87ce88cdd25e2d2fc98e370e6ad3708efa909dc1c08195", "sha256_in_prefix": "6f8a72800f971f64ee87ce88cdd25e2d2fc98e370e6ad3708efa909dc1c08195", "size_in_bytes": 382}, {"_path": "lib/python3.11/site-packages/fontTools/subset/__pycache__/cff.cpython-311.pyc", "path_type": "hardlink", "sha256": "bb36d7d280c446e1e4d99e7790b381af11e1f3141f9ff2b04ce7958c5bc1af49", "sha256_in_prefix": "bb36d7d280c446e1e4d99e7790b381af11e1f3141f9ff2b04ce7958c5bc1af49", "size_in_bytes": 10563}, {"_path": "lib/python3.11/site-packages/fontTools/subset/__pycache__/svg.cpython-311.pyc", "path_type": "hardlink", "sha256": "04a233f59bbee3d290f6b46d3042496566411f938be29926558900fc2da164e7", "sha256_in_prefix": "04a233f59bbee3d290f6b46d3042496566411f938be29926558900fc2da164e7", "size_in_bytes": 11154}, {"_path": "lib/python3.11/site-packages/fontTools/subset/__pycache__/util.cpython-311.pyc", "path_type": "hardlink", "sha256": "61461404e48134a4729f48c5cb5148726d0d06c517968e15aea932adea7ee7db", "sha256_in_prefix": "61461404e48134a4729f48c5cb5148726d0d06c517968e15aea932adea7ee7db", "size_in_bytes": 1361}, {"_path": "lib/python3.11/site-packages/fontTools/subset/cff.py", "path_type": "hardlink", "sha256": "aea31124e957e4569c5752d6f1a0e554e82580433ced390c03d6ddb187a76ac9", "sha256_in_prefix": "aea31124e957e4569c5752d6f1a0e554e82580433ced390c03d6ddb187a76ac9", "size_in_bytes": 6145}, {"_path": "lib/python3.11/site-packages/fontTools/subset/svg.py", "path_type": "hardlink", "sha256": "f1d2c1cd096722de3f7ce28414301594a4ee71d1ef71b09cc86f7e6ba501659d", "sha256_in_prefix": "f1d2c1cd096722de3f7ce28414301594a4ee71d1ef71b09cc86f7e6ba501659d", "size_in_bytes": 9384}, {"_path": "lib/python3.11/site-packages/fontTools/subset/util.py", "path_type": "hardlink", "sha256": "f525c561be447fd679f2e5e660f0908a5f01da2dd0eda141ff57c50c54a9a5d5", "sha256_in_prefix": "f525c561be447fd679f2e5e660f0908a5f01da2dd0eda141ff57c50c54a9a5d5", "size_in_bytes": 754}, {"_path": "lib/python3.11/site-packages/fontTools/svgLib/__init__.py", "path_type": "hardlink", "sha256": "20608bc126d4f232e1aba1c8daf49d3d080db3acc3522e7bef84e05f93025cf6", "sha256_in_prefix": "20608bc126d4f232e1aba1c8daf49d3d080db3acc3522e7bef84e05f93025cf6", "size_in_bytes": 75}, {"_path": "lib/python3.11/site-packages/fontTools/svgLib/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "44c21867f8d169bdf9ab56a4f36624258d31773dbf5b2fed7994f19cf80753af", "sha256_in_prefix": "44c21867f8d169bdf9ab56a4f36624258d31773dbf5b2fed7994f19cf80753af", "size_in_bytes": 278}, {"_path": "lib/python3.11/site-packages/fontTools/svgLib/path/__init__.py", "path_type": "hardlink", "sha256": "0bcd9f87bc47e991ec48556757ce3cfb17837b3a68931d44c1399ac890a8b855", "sha256_in_prefix": "0bcd9f87bc47e991ec48556757ce3cfb17837b3a68931d44c1399ac890a8b855", "size_in_bytes": 1996}, {"_path": "lib/python3.11/site-packages/fontTools/svgLib/path/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "0c543a583d626ac20a6ac1412eb5ad555057724d0e5a80889cb77f9fde3b296a", "sha256_in_prefix": "0c543a583d626ac20a6ac1412eb5ad555057724d0e5a80889cb77f9fde3b296a", "size_in_bytes": 3268}, {"_path": "lib/python3.11/site-packages/fontTools/svgLib/path/__pycache__/arc.cpython-311.pyc", "path_type": "hardlink", "sha256": "92e754b34ed04f3247ced91b4abc7356a0a6c80c667d7e1bd784712ec80a8a89", "sha256_in_prefix": "92e754b34ed04f3247ced91b4abc7356a0a6c80c667d7e1bd784712ec80a8a89", "size_in_bytes": 6707}, {"_path": "lib/python3.11/site-packages/fontTools/svgLib/path/__pycache__/parser.cpython-311.pyc", "path_type": "hardlink", "sha256": "2869a28dedcb5e29ba3584ffab34bff55601b08400c069776eb49096a1d9e888", "sha256_in_prefix": "2869a28dedcb5e29ba3584ffab34bff55601b08400c069776eb49096a1d9e888", "size_in_bytes": 10672}, {"_path": "lib/python3.11/site-packages/fontTools/svgLib/path/__pycache__/shapes.cpython-311.pyc", "path_type": "hardlink", "sha256": "f5dbe4aa632af93bd27fa2bee7394c48d3bbdfc4d65818c6f150e089e05e2f4a", "sha256_in_prefix": "f5dbe4aa632af93bd27fa2bee7394c48d3bbdfc4d65818c6f150e089e05e2f4a", "size_in_bytes": 12361}, {"_path": "lib/python3.11/site-packages/fontTools/svgLib/path/arc.py", "path_type": "hardlink", "sha256": "f9fe589baab8b43590efab0c3524d44d680bffb01f817a23bc186d052edb5b04", "sha256_in_prefix": "f9fe589baab8b43590efab0c3524d44d680bffb01f817a23bc186d052edb5b04", "size_in_bytes": 5812}, {"_path": "lib/python3.11/site-packages/fontTools/svgLib/path/parser.py", "path_type": "hardlink", "sha256": "f13ea890cb2dbe033db9f6f6cc17304b3b2eba861ba9f9d48cd6206fa923ce75", "sha256_in_prefix": "f13ea890cb2dbe033db9f6f6cc17304b3b2eba861ba9f9d48cd6206fa923ce75", "size_in_bytes": 10788}, {"_path": "lib/python3.11/site-packages/fontTools/svgLib/path/shapes.py", "path_type": "hardlink", "sha256": "c6f05421c90ac93f492f2eeafd93f9d2be938ef6403721dd64fef0143cc4adc2", "sha256_in_prefix": "c6f05421c90ac93f492f2eeafd93f9d2be938ef6403721dd64fef0143cc4adc2", "size_in_bytes": 5322}, {"_path": "lib/python3.11/site-packages/fontTools/t1Lib/__init__.py", "path_type": "hardlink", "sha256": "a78db2ef4c0421bb97d08231646efe6ff23e807b68d552f2d202ec0efc427e4c", "sha256_in_prefix": "a78db2ef4c0421bb97d08231646efe6ff23e807b68d552f2d202ec0efc427e4c", "size_in_bytes": 20865}, {"_path": "lib/python3.11/site-packages/fontTools/t1Lib/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "f187777022b59d5ff0ea8dd844cc21999a08276ca69b0e165d02fca7eb84dad6", "sha256_in_prefix": "f187777022b59d5ff0ea8dd844cc21999a08276ca69b0e165d02fca7eb84dad6", "size_in_bytes": 31280}, {"_path": "lib/python3.11/site-packages/fontTools/tfmLib.py", "path_type": "hardlink", "sha256": "50c6e433bdc95d12554bdb7607e049735deb4a322669606ecc2a1e84bc07161b", "sha256_in_prefix": "50c6e433bdc95d12554bdb7607e049735deb4a322669606ecc2a1e84bc07161b", "size_in_bytes": 14270}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/__init__.py", "path_type": "hardlink", "sha256": "d64eeaa7dcf4e200379ba1afc43688363aab29bcce91d4c0ff8467a96ffe2eb0", "sha256_in_prefix": "d64eeaa7dcf4e200379ba1afc43688363aab29bcce91d4c0ff8467a96ffe2eb0", "size_in_bytes": 661}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/__main__.py", "path_type": "hardlink", "sha256": "94730f5ac9f38ca3ee3056af7fa8b582993d29ec62024e2ace00dde7431b6f2d", "sha256_in_prefix": "94730f5ac9f38ca3ee3056af7fa8b582993d29ec62024e2ace00dde7431b6f2d", "size_in_bytes": 4733}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "feff1d890965045d864e53bc59e6582757322efca5887e210dafc46364152094", "sha256_in_prefix": "feff1d890965045d864e53bc59e6582757322efca5887e210dafc46364152094", "size_in_bytes": 1669}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "ddfc0ea11a237b978bc146a5df327daecff4a7fcdd5e687096b47aa29a35eba4", "sha256_in_prefix": "ddfc0ea11a237b978bc146a5df327daecff4a7fcdd5e687096b47aa29a35eba4", "size_in_bytes": 5815}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/macUtils.cpython-311.pyc", "path_type": "hardlink", "sha256": "655abd01b3f6474014bb0d08690f041a540ad130ec5f752a233848613f4bb5ff", "sha256_in_prefix": "655abd01b3f6474014bb0d08690f041a540ad130ec5f752a233848613f4bb5ff", "size_in_bytes": 3119}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/removeOverlaps.cpython-311.pyc", "path_type": "hardlink", "sha256": "b2f69dac07961125db4fbf0696da26a149e40f1c33736cd059cea1934d74fd86", "sha256_in_prefix": "b2f69dac07961125db4fbf0696da26a149e40f1c33736cd059cea1934d74fd86", "size_in_bytes": 17017}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/reorderGlyphs.cpython-311.pyc", "path_type": "hardlink", "sha256": "52f9b6d82ad953cd9acc943325f06b2b34952ef4dc0aef54cdd7c70ad093db8c", "sha256_in_prefix": "52f9b6d82ad953cd9acc943325f06b2b34952ef4dc0aef54cdd7c70ad093db8c", "size_in_bytes": 14842}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/scaleUpem.cpython-311.pyc", "path_type": "hardlink", "sha256": "8067a823139ef18fa0858178f71715f1e928532c30ac5e6639ad0f1941714e5e", "sha256_in_prefix": "8067a823139ef18fa0858178f71715f1e928532c30ac5e6639ad0f1941714e5e", "size_in_bytes": 19418}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/sfnt.cpython-311.pyc", "path_type": "hardlink", "sha256": "45dbfbb4cafcc71d3f03a1ba3973fd9b60022de8048bd3151d509ae99ac48d67", "sha256_in_prefix": "45dbfbb4cafcc71d3f03a1ba3973fd9b60022de8048bd3151d509ae99ac48d67", "size_in_bytes": 31441}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/standardGlyphOrder.cpython-311.pyc", "path_type": "hardlink", "sha256": "8406f72ee5e41a5bccc1f57954e301be53304288a337bf13c0e0d387ec31a24a", "sha256_in_prefix": "8406f72ee5e41a5bccc1f57954e301be53304288a337bf13c0e0d387ec31a24a", "size_in_bytes": 2330}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/ttCollection.cpython-311.pyc", "path_type": "hardlink", "sha256": "af593ebf5844eebbd5c901a16ed41d02382a44fca7667b46e1cc0aa2dddce889", "sha256_in_prefix": "af593ebf5844eebbd5c901a16ed41d02382a44fca7667b46e1cc0aa2dddce889", "size_in_bytes": 6467}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/ttFont.cpython-311.pyc", "path_type": "hardlink", "sha256": "ee8914023fc883aaf093c4926c31ff9f9c465f6a21b596c10e48e79255bdcbb9", "sha256_in_prefix": "ee8914023fc883aaf093c4926c31ff9f9c465f6a21b596c10e48e79255bdcbb9", "size_in_bytes": 48937}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/ttGlyphSet.cpython-311.pyc", "path_type": "hardlink", "sha256": "a331dd2bedd1d9ebfa6d980b3fb8d7976ff8479102cc10e10ce07be788d283aa", "sha256_in_prefix": "a331dd2bedd1d9ebfa6d980b3fb8d7976ff8479102cc10e10ce07be788d283aa", "size_in_bytes": 27518}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/ttVisitor.cpython-311.pyc", "path_type": "hardlink", "sha256": "d48fa848954d391c69f7480793a8ce707570d9a8fc86a6184bb0bd0570727e65", "sha256_in_prefix": "d48fa848954d391c69f7480793a8ce707570d9a8fc86a6184bb0bd0570727e65", "size_in_bytes": 1995}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/__pycache__/woff2.cpython-311.pyc", "path_type": "hardlink", "sha256": "5935aa74a1d49e28563d1845f818857e9833014f0761da5373fed8586b0df19a", "sha256_in_prefix": "5935aa74a1d49e28563d1845f818857e9833014f0761da5373fed8586b0df19a", "size_in_bytes": 79825}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/macUtils.py", "path_type": "hardlink", "sha256": "963de8785a728d5ee4a3f26a9e5ba77884e602d95cd75f49fafc134e0dacef70", "sha256_in_prefix": "963de8785a728d5ee4a3f26a9e5ba77884e602d95cd75f49fafc134e0dacef70", "size_in_bytes": 1737}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/removeOverlaps.py", "path_type": "hardlink", "sha256": "601b63d4f5fe7768cc8028961ae23a89b821580a545aa1f6b6b2465cdc6b6e34", "sha256_in_prefix": "601b63d4f5fe7768cc8028961ae23a89b821580a545aa1f6b6b2465cdc6b6e34", "size_in_bytes": 12612}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/reorderGlyphs.py", "path_type": "hardlink", "sha256": "4dbc4bc6a3d350688a457dee946142c159b69448ac158957e9c68e349affe286", "sha256_in_prefix": "4dbc4bc6a3d350688a457dee946142c159b69448ac158957e9c68e349affe286", "size_in_bytes": 10371}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/scaleUpem.py", "path_type": "hardlink", "sha256": "53ff8d1a4c1f4bd19121a7247445e319867ec12a267143d741a8439de2de02b2", "sha256_in_prefix": "53ff8d1a4c1f4bd19121a7247445e319867ec12a267143d741a8439de2de02b2", "size_in_bytes": 14618}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/sfnt.py", "path_type": "hardlink", "sha256": "c1e9a47f3f77765028ca8f955739a0e4e2e8492c8c1503730534cfdca7a6fb19", "sha256_in_prefix": "c1e9a47f3f77765028ca8f955739a0e4e2e8492c8c1503730534cfdca7a6fb19", "size_in_bytes": 22792}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/standardGlyphOrder.py", "path_type": "hardlink", "sha256": "ec063f7d559db706788afe6e59dc8a01471b1108920edd65378b2ac7dc57c04d", "sha256_in_prefix": "ec063f7d559db706788afe6e59dc8a01471b1108920edd65378b2ac7dc57c04d", "size_in_bytes": 5785}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/B_A_S_E_.py", "path_type": "hardlink", "sha256": "1fbd40f6927ce749af8dbad61eacbc8852360f183bd74d9846d0247dd0a8a228", "sha256_in_prefix": "1fbd40f6927ce749af8dbad61eacbc8852360f183bd74d9846d0247dd0a8a228", "size_in_bytes": 369}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/BitmapGlyphMetrics.py", "path_type": "hardlink", "sha256": "f607063d5cecc446275413bb60b59f78eba1b7d3da0a5d3d1a66c0a8362a2a2d", "sha256_in_prefix": "f607063d5cecc446275413bb60b59f78eba1b7d3da0a5d3d1a66c0a8362a2a2d", "size_in_bytes": 1769}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/C_B_D_T_.py", "path_type": "hardlink", "sha256": "e4b35d73c14c8abd640b97efa79887c167deb84f91baa771000ac55daa8f8d0d", "sha256_in_prefix": "e4b35d73c14c8abd640b97efa79887c167deb84f91baa771000ac55daa8f8d0d", "size_in_bytes": 3646}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/C_B_L_C_.py", "path_type": "hardlink", "sha256": "617970a2fa021d8c7c4c72d00fd88153f546a1a07d2c539b10ab4be9d743df6d", "sha256_in_prefix": "617970a2fa021d8c7c4c72d00fd88153f546a1a07d2c539b10ab4be9d743df6d", "size_in_bytes": 520}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/C_F_F_.py", "path_type": "hardlink", "sha256": "ca0de652d601b9d826a46ec1cf8ef98ff0cd9c27a5b20ad3b0cf031f5b91e1e9", "sha256_in_prefix": "ca0de652d601b9d826a46ec1cf8ef98ff0cd9c27a5b20ad3b0cf031f5b91e1e9", "size_in_bytes": 1978}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/C_F_F__2.py", "path_type": "hardlink", "sha256": "6281df250745f9ecf1e0ec114366363bbad12443e33a47f71f1e58d755ead003", "sha256_in_prefix": "6281df250745f9ecf1e0ec114366363bbad12443e33a47f71f1e58d755ead003", "size_in_bytes": 807}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/C_O_L_R_.py", "path_type": "hardlink", "sha256": "487c0554d566a14411d9ef3b2ae4d27be27d2df79e812e1fda112979edbdff6a", "sha256_in_prefix": "487c0554d566a14411d9ef3b2ae4d27be27d2df79e812e1fda112979edbdff6a", "size_in_bytes": 5993}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/C_P_A_L_.py", "path_type": "hardlink", "sha256": "a1d163a8ce069e35f24181840be7b41afaa6b358d0e731c71b748383bcbe048d", "sha256_in_prefix": "a1d163a8ce069e35f24181840be7b41afaa6b358d0e731c71b748383bcbe048d", "size_in_bytes": 11942}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/D_S_I_G_.py", "path_type": "hardlink", "sha256": "02040f33d09dae8d4ff9e8498d37ec0bd9914d3b52735e80b749e7a5bd573862", "sha256_in_prefix": "02040f33d09dae8d4ff9e8498d37ec0bd9914d3b52735e80b749e7a5bd573862", "size_in_bytes": 5517}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/D__e_b_g.py", "path_type": "hardlink", "sha256": "2839df90d3949e6dc5d77c03fe3dd83413af61a759e3419fff4d7bd21164269d", "sha256_in_prefix": "2839df90d3949e6dc5d77c03fe3dd83413af61a759e3419fff4d7bd21164269d", "size_in_bytes": 1134}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/DefaultTable.py", "path_type": "hardlink", "sha256": "70eb6090b58f63daa6387d8148fb7873821449d00d5932b1dab2b5093c50e21d", "sha256_in_prefix": "70eb6090b58f63daa6387d8148fb7873821449d00d5932b1dab2b5093c50e21d", "size_in_bytes": 1487}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/E_B_D_T_.py", "path_type": "hardlink", "sha256": "b8ea66b76e6038941e3b5bb52060323d68159a1fb8bd266aad0107bce6306f08", "sha256_in_prefix": "b8ea66b76e6038941e3b5bb52060323d68159a1fb8bd266aad0107bce6306f08", "size_in_bytes": 32534}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/E_B_L_C_.py", "path_type": "hardlink", "sha256": "2df115cc183fc96afd7610a1cd2d14d86fbbc0d3b0cf00742d6a1721460d28a3", "sha256_in_prefix": "2df115cc183fc96afd7610a1cd2d14d86fbbc0d3b0cf00742d6a1721460d28a3", "size_in_bytes": 30054}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/F_F_T_M_.py", "path_type": "hardlink", "sha256": "ff8e74bdd6c41fb63ed3facec1bdd0d2183e42ad96f02fec1e58f2eeb66daaa3", "sha256_in_prefix": "ff8e74bdd6c41fb63ed3facec1bdd0d2183e42ad96f02fec1e58f2eeb66daaa3", "size_in_bytes": 1683}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/F__e_a_t.py", "path_type": "hardlink", "sha256": "72defd19ffffe402e5a9f4819fac2fc3a7dacdbdf539def547abc8a7aa3d5c5e", "sha256_in_prefix": "72defd19ffffe402e5a9f4819fac2fc3a7dacdbdf539def547abc8a7aa3d5c5e", "size_in_bytes": 5483}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/G_D_E_F_.py", "path_type": "hardlink", "sha256": "4178882c50919cf359730a6e6faa233794bd58feb2e7a2ec5e2db9a5458b829e", "sha256_in_prefix": "4178882c50919cf359730a6e6faa233794bd58feb2e7a2ec5e2db9a5458b829e", "size_in_bytes": 299}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/G_M_A_P_.py", "path_type": "hardlink", "sha256": "7ef210ba6a243822daf0315eabfc62d3af45f5144eb17495983bd6b7183269c4", "sha256_in_prefix": "7ef210ba6a243822daf0315eabfc62d3af45f5144eb17495983bd6b7183269c4", "size_in_bytes": 4720}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/G_P_K_G_.py", "path_type": "hardlink", "sha256": "5e2e078f63b1676219815c8143e4328a27b484f663a57664adaa3e13911d4d63", "sha256_in_prefix": "5e2e078f63b1676219815c8143e4328a27b484f663a57664adaa3e13911d4d63", "size_in_bytes": 4646}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/G_P_O_S_.py", "path_type": "hardlink", "sha256": "5243f79a59f2bd083e8e3e9904e87a8fe3a279583f828250df59e52efa0b1922", "sha256_in_prefix": "5243f79a59f2bd083e8e3e9904e87a8fe3a279583f828250df59e52efa0b1922", "size_in_bytes": 397}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/G_S_U_B_.py", "path_type": "hardlink", "sha256": "73014c28efa9830b27d47f10f496f9f19e99ac1ac2a0dd2ca8907462e9c17d29", "sha256_in_prefix": "73014c28efa9830b27d47f10f496f9f19e99ac1ac2a0dd2ca8907462e9c17d29", "size_in_bytes": 294}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/G_V_A_R_.py", "path_type": "hardlink", "sha256": "d77a0ed9d0fe2f8c9f92b06e47e28ddab738d3087994b225c7f921c0ccf9187e", "sha256_in_prefix": "d77a0ed9d0fe2f8c9f92b06e47e28ddab738d3087994b225c7f921c0ccf9187e", "size_in_bytes": 94}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/G__l_a_t.py", "path_type": "hardlink", "sha256": "5e1dc8cc581896f363ac03a7ec96bbdc3ad6ad04c9809c660c5494292eb21dd3", "sha256_in_prefix": "5e1dc8cc581896f363ac03a7ec96bbdc3ad6ad04c9809c660c5494292eb21dd3", "size_in_bytes": 8645}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/G__l_o_c.py", "path_type": "hardlink", "sha256": "e43b311b3686ec7c8956f2e529079f7f5957b7e5cf59a1cd35a7fe118c2c2a1e", "sha256_in_prefix": "e43b311b3686ec7c8956f2e529079f7f5957b7e5cf59a1cd35a7fe118c2c2a1e", "size_in_bytes": 2685}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/H_V_A_R_.py", "path_type": "hardlink", "sha256": "ea43cb0d4193f04bacb00df2f602abfe6ae06393cdbfb61a2b557491e3170fdc", "sha256_in_prefix": "ea43cb0d4193f04bacb00df2f602abfe6ae06393cdbfb61a2b557491e3170fdc", "size_in_bytes": 313}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/J_S_T_F_.py", "path_type": "hardlink", "sha256": "43d4c47f73aec832316659a8cfd6b773e98332524ae98050f4a7189a2c0545b5", "sha256_in_prefix": "43d4c47f73aec832316659a8cfd6b773e98332524ae98050f4a7189a2c0545b5", "size_in_bytes": 315}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/L_T_S_H_.py", "path_type": "hardlink", "sha256": "22eeacc8916e8498f4ffb01a9f634f943b900c8abe0332f0b0e41b5d54e794bd", "sha256_in_prefix": "22eeacc8916e8498f4ffb01a9f634f943b900c8abe0332f0b0e41b5d54e794bd", "size_in_bytes": 2189}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/M_A_T_H_.py", "path_type": "hardlink", "sha256": "f9356ef4d95cb3ed6c8641256c893e096b54c1750c7727071648ef3c4f02fdfb", "sha256_in_prefix": "f9356ef4d95cb3ed6c8641256c893e096b54c1750c7727071648ef3c4f02fdfb", "size_in_bytes": 342}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/M_E_T_A_.py", "path_type": "hardlink", "sha256": "b00ea8a24723721c3c518544b92f1011773ad88f7f466b3d72efe3280e8c90d2", "sha256_in_prefix": "b00ea8a24723721c3c518544b92f1011773ad88f7f466b3d72efe3280e8c90d2", "size_in_bytes": 11989}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/M_V_A_R_.py", "path_type": "hardlink", "sha256": "ebb704ba24f0e72e56d5993df0bfd2fd298920d21fcbf9b35829321dcba3cfde", "sha256_in_prefix": "ebb704ba24f0e72e56d5993df0bfd2fd298920d21fcbf9b35829321dcba3cfde", "size_in_bytes": 308}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/O_S_2f_2.py", "path_type": "hardlink", "sha256": "d4fab65eee2860e25e3d31c2fe14ca8374487ca7a5cb78fa4f5bbf94c4c4a43f", "sha256_in_prefix": "d4fab65eee2860e25e3d31c2fe14ca8374487ca7a5cb78fa4f5bbf94c4c4a43f", "size_in_bytes": 28030}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/S_I_N_G_.py", "path_type": "hardlink", "sha256": "0850f2f11d9f0de627ee871fad9afb522ed4f43d21e06e7909d3df639e60f819", "sha256_in_prefix": "0850f2f11d9f0de627ee871fad9afb522ed4f43d21e06e7909d3df639e60f819", "size_in_bytes": 3317}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/S_T_A_T_.py", "path_type": "hardlink", "sha256": "cbd362582b67959b4c8f0e0af7f49d03ce176be74993b1b979bd9d49ee9c8967", "sha256_in_prefix": "cbd362582b67959b4c8f0e0af7f49d03ce176be74993b1b979bd9d49ee9c8967", "size_in_bytes": 498}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/S_V_G_.py", "path_type": "hardlink", "sha256": "bd3e904d6e40aedb24554c673c41ff6712b3e03178bf5a4a6f294de831b4477a", "sha256_in_prefix": "bd3e904d6e40aedb24554c673c41ff6712b3e03178bf5a4a6f294de831b4477a", "size_in_bytes": 7676}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/S__i_l_f.py", "path_type": "hardlink", "sha256": "94f415d917617094607c3cc7a7f764812c555147647009d8d41cfb575f06b7d5", "sha256_in_prefix": "94f415d917617094607c3cc7a7f764812c555147647009d8d41cfb575f06b7d5", "size_in_bytes": 34985}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/S__i_l_l.py", "path_type": "hardlink", "sha256": "563b67ed223cdef68b188b907f67be8e165214e5dbf6f5c1e23c2ace78ea9cc7", "sha256_in_prefix": "563b67ed223cdef68b188b907f67be8e165214e5dbf6f5c1e23c2ace78ea9cc7", "size_in_bytes": 3224}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I_B_.py", "path_type": "hardlink", "sha256": "dd6844b723678d8ba6728c03d06a63b9bae09d2f91ce8b99c42c447b8c8b0e8f", "sha256_in_prefix": "dd6844b723678d8ba6728c03d06a63b9bae09d2f91ce8b99c42c447b8c8b0e8f", "size_in_bytes": 341}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I_C_.py", "path_type": "hardlink", "sha256": "84057d1eafc02ec59a76e703a70d6d0d1444bc52fb871ee89e7505d2c5d37a55", "sha256_in_prefix": "84057d1eafc02ec59a76e703a70d6d0d1444bc52fb871ee89e7505d2c5d37a55", "size_in_bytes": 381}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I_D_.py", "path_type": "hardlink", "sha256": "4ec757f86db1c5540ef6c484d7013fc43471fa0a2be588974c7794b6133008f6", "sha256_in_prefix": "4ec757f86db1c5540ef6c484d7013fc43471fa0a2be588974c7794b6133008f6", "size_in_bytes": 341}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I_J_.py", "path_type": "hardlink", "sha256": "c7c4e5be2e9a4f1a10708d7650bee6ba8585d50eb58810ec7804b599174e62b8", "sha256_in_prefix": "c7c4e5be2e9a4f1a10708d7650bee6ba8585d50eb58810ec7804b599174e62b8", "size_in_bytes": 341}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I_P_.py", "path_type": "hardlink", "sha256": "fa2976b9c4c13a0855058edc9a578774b65cdd6dc22a1efe88f3d3d00dca0739", "sha256_in_prefix": "fa2976b9c4c13a0855058edc9a578774b65cdd6dc22a1efe88f3d3d00dca0739", "size_in_bytes": 341}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I_S_.py", "path_type": "hardlink", "sha256": "b5506797adefc99508abdde833a7448c7097bcf9fdbede6fbcbde31b9f5bd0b8", "sha256_in_prefix": "b5506797adefc99508abdde833a7448c7097bcf9fdbede6fbcbde31b9f5bd0b8", "size_in_bytes": 341}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I_V_.py", "path_type": "hardlink", "sha256": "8945b1cf6312aedc3b9b3b90663df440026b08f9f22781a29dc15fc921543408", "sha256_in_prefix": "8945b1cf6312aedc3b9b3b90663df440026b08f9f22781a29dc15fc921543408", "size_in_bytes": 855}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I__0.py", "path_type": "hardlink", "sha256": "3beda823478182de263f5e7e5301f4ff4afb6168b71041211bee1eb6a0ee7862", "sha256_in_prefix": "3beda823478182de263f5e7e5301f4ff4afb6168b71041211bee1eb6a0ee7862", "size_in_bytes": 2505}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I__1.py", "path_type": "hardlink", "sha256": "9d25219e2f9fbd899e2975b8ccb7cfdc51bfdcb414d9028f292d7f80a6399588", "sha256_in_prefix": "9d25219e2f9fbd899e2975b8ccb7cfdc51bfdcb414d9028f292d7f80a6399588", "size_in_bytes": 6971}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I__2.py", "path_type": "hardlink", "sha256": "ab6aee6fe77bee259688133a6b03b4f930a5f97abb91a94fa1b1d80b640439f7", "sha256_in_prefix": "ab6aee6fe77bee259688133a6b03b4f930a5f97abb91a94fa1b1d80b640439f7", "size_in_bytes": 496}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I__3.py", "path_type": "hardlink", "sha256": "d0b72fbc2cd5649cf2cfb8b8ce322452e61712a5f0382b3d59e0ac80316a289f", "sha256_in_prefix": "d0b72fbc2cd5649cf2cfb8b8ce322452e61712a5f0382b3d59e0ac80316a289f", "size_in_bytes": 543}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/T_S_I__5.py", "path_type": "hardlink", "sha256": "861bc99fa8e25ecf24b81b6e9fc92b3544d74e58c7f9e2b16b15ccd53fbb4973", "sha256_in_prefix": "861bc99fa8e25ecf24b81b6e9fc92b3544d74e58c7f9e2b16b15ccd53fbb4973", "size_in_bytes": 1905}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/T_T_F_A_.py", "path_type": "hardlink", "sha256": "2ee4f4c3ffc032d6b09ec04ca1b844316f60a76ca4d26039651cf0178e5cd142", "sha256_in_prefix": "2ee4f4c3ffc032d6b09ec04ca1b844316f60a76ca4d26039651cf0178e5cd142", "size_in_bytes": 392}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/TupleVariation.py", "path_type": "hardlink", "sha256": "e174c34d13d958f83dff52b949581d368c6d810bda86d88ee0b34eedf93570ae", "sha256_in_prefix": "e174c34d13d958f83dff52b949581d368c6d810bda86d88ee0b34eedf93570ae", "size_in_bytes": 32235}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/V_A_R_C_.py", "path_type": "hardlink", "sha256": "de3157e7427a5fe09ce1dc308b3b4a2ac0d34575474d79557501f7dbc50dd7e9", "sha256_in_prefix": "de3157e7427a5fe09ce1dc308b3b4a2ac0d34575474d79557501f7dbc50dd7e9", "size_in_bytes": 289}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/V_D_M_X_.py", "path_type": "hardlink", "sha256": "45b1e5eefbcef697234ff90abdc0a6701c908f7f67e0f9957aae79c03e42d788", "sha256_in_prefix": "45b1e5eefbcef697234ff90abdc0a6701c908f7f67e0f9957aae79c03e42d788", "size_in_bytes": 10437}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/V_O_R_G_.py", "path_type": "hardlink", "sha256": "0a7dcec6356d70ef94be9eb53f9736df7e95f6211bb86afabd60179d221a8a19", "sha256_in_prefix": "0a7dcec6356d70ef94be9eb53f9736df7e95f6211bb86afabd60179d221a8a19", "size_in_bytes": 5965}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/V_V_A_R_.py", "path_type": "hardlink", "sha256": "0acb70ead73f538f84993ae2448b414a9bd324e0c08cc1508dfc936b12f3b1b2", "sha256_in_prefix": "0acb70ead73f538f84993ae2448b414a9bd324e0c08cc1508dfc936b12f3b1b2", "size_in_bytes": 319}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__init__.py", "path_type": "hardlink", "sha256": "7903dcb8709f46e1adb7a9ce6b42b057abed50d2879f0b90c80ef148df123e87", "sha256_in_prefix": "7903dcb8709f46e1adb7a9ce6b42b057abed50d2879f0b90c80ef148df123e87", "size_in_bytes": 2651}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/B_A_S_E_.cpython-311.pyc", "path_type": "hardlink", "sha256": "24daf989366c8f965aadf3f70a1a3607cc2ebd99cf9df29029deca4fcbdc7e0b", "sha256_in_prefix": "24daf989366c8f965aadf3f70a1a3607cc2ebd99cf9df29029deca4fcbdc7e0b", "size_in_bytes": 768}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/BitmapGlyphMetrics.cpython-311.pyc", "path_type": "hardlink", "sha256": "85a788454f604bc60e76575ad90b0fac21a864c5cd1473705b08b315cc6451fd", "sha256_in_prefix": "85a788454f604bc60e76575ad90b0fac21a864c5cd1473705b08b315cc6451fd", "size_in_bytes": 3107}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/C_B_D_T_.cpython-311.pyc", "path_type": "hardlink", "sha256": "fe39f4777b02eb5d41479f47b8d3e11b7b982ca5b2502dbf9555f3e0a8144822", "sha256_in_prefix": "fe39f4777b02eb5d41479f47b8d3e11b7b982ca5b2502dbf9555f3e0a8144822", "size_in_bytes": 6327}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/C_B_L_C_.cpython-311.pyc", "path_type": "hardlink", "sha256": "6e037663111882f2e10ea7b3914f44c743e8d2bb6a25611e1a4d2c933ba267c2", "sha256_in_prefix": "6e037663111882f2e10ea7b3914f44c743e8d2bb6a25611e1a4d2c933ba267c2", "size_in_bytes": 867}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/C_F_F_.cpython-311.pyc", "path_type": "hardlink", "sha256": "45c2fef4aef43503e66e6bb9a459f1dded21d5fe0a6721a2907c26db410eccda", "sha256_in_prefix": "45c2fef4aef43503e66e6bb9a459f1dded21d5fe0a6721a2907c26db410eccda", "size_in_bytes": 3911}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/C_F_F__2.cpython-311.pyc", "path_type": "hardlink", "sha256": "308297f53929dc03d6f509999da5a9dca62efcfa9356a196955d63e29bfe7145", "sha256_in_prefix": "308297f53929dc03d6f509999da5a9dca62efcfa9356a196955d63e29bfe7145", "size_in_bytes": 1742}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/C_O_L_R_.cpython-311.pyc", "path_type": "hardlink", "sha256": "13d8394319cb688a76bba226a8d64162d3b2419606c9517597a34d58f1df0956", "sha256_in_prefix": "13d8394319cb688a76bba226a8d64162d3b2419606c9517597a34d58f1df0956", "size_in_bytes": 9481}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/C_P_A_L_.cpython-311.pyc", "path_type": "hardlink", "sha256": "2a5f17067c8ae6e0ffeea4aeee13a7b030400f393e4afa94f360a3d98f3c5373", "sha256_in_prefix": "2a5f17067c8ae6e0ffeea4aeee13a7b030400f393e4afa94f360a3d98f3c5373", "size_in_bytes": 18807}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/D_S_I_G_.cpython-311.pyc", "path_type": "hardlink", "sha256": "177e85c1b2c6c184a0d35544aa44128970bb02e225a1c17ed3973b217b397fc9", "sha256_in_prefix": "177e85c1b2c6c184a0d35544aa44128970bb02e225a1c17ed3973b217b397fc9", "size_in_bytes": 8389}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/D__e_b_g.cpython-311.pyc", "path_type": "hardlink", "sha256": "4bf1e5050b8b8c07161c196e92dd3d74603eedd7f141c29267a9c239736f4a48", "sha256_in_prefix": "4bf1e5050b8b8c07161c196e92dd3d74603eedd7f141c29267a9c239736f4a48", "size_in_bytes": 2822}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/DefaultTable.cpython-311.pyc", "path_type": "hardlink", "sha256": "c9363e73f896c46bf18f679cd979a86cfa141a6277d4a3380680bbe33b8ffb24", "sha256_in_prefix": "c9363e73f896c46bf18f679cd979a86cfa141a6277d4a3380680bbe33b8ffb24", "size_in_bytes": 3494}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/E_B_D_T_.cpython-311.pyc", "path_type": "hardlink", "sha256": "eeab4219d71a8c41454356c8ac45bcaeb6bed8c6e837c91b342a370f503ea5e0", "sha256_in_prefix": "eeab4219d71a8c41454356c8ac45bcaeb6bed8c6e837c91b342a370f503ea5e0", "size_in_bytes": 40408}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/E_B_L_C_.cpython-311.pyc", "path_type": "hardlink", "sha256": "95ef0ee72d6241b606c017a968093a7bd11bf6e489d5dc1ce37ab68ebdbd429a", "sha256_in_prefix": "95ef0ee72d6241b606c017a968093a7bd11bf6e489d5dc1ce37ab68ebdbd429a", "size_in_bytes": 40322}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/F_F_T_M_.cpython-311.pyc", "path_type": "hardlink", "sha256": "40af7c4b4be7fa8c30af851a6dbf21bb41e389573519dcb5d19b19b2c1434b33", "sha256_in_prefix": "40af7c4b4be7fa8c30af851a6dbf21bb41e389573519dcb5d19b19b2c1434b33", "size_in_bytes": 2947}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/F__e_a_t.cpython-311.pyc", "path_type": "hardlink", "sha256": "5d8e333677e164b012940756512d5c24e2e8b304b2c6259ef59d4417ccdacfd9", "sha256_in_prefix": "5d8e333677e164b012940756512d5c24e2e8b304b2c6259ef59d4417ccdacfd9", "size_in_bytes": 9282}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/G_D_E_F_.cpython-311.pyc", "path_type": "hardlink", "sha256": "a6a2075b8203d74b82feb77abaabfb8d9066ab9e471b9e0457e3b9d9fbfb40c3", "sha256_in_prefix": "a6a2075b8203d74b82feb77abaabfb8d9066ab9e471b9e0457e3b9d9fbfb40c3", "size_in_bytes": 695}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/G_M_A_P_.cpython-311.pyc", "path_type": "hardlink", "sha256": "ced808bc80d8627fe2a0573638887c3d9eea8c4101b8fbcdc4d2e24329abed2e", "sha256_in_prefix": "ced808bc80d8627fe2a0573638887c3d9eea8c4101b8fbcdc4d2e24329abed2e", "size_in_bytes": 8023}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/G_P_K_G_.cpython-311.pyc", "path_type": "hardlink", "sha256": "29069fc5427f099bb323f00b5778ffb8a214c6249a4e077432beb954dcf8dd30", "sha256_in_prefix": "29069fc5427f099bb323f00b5778ffb8a214c6249a4e077432beb954dcf8dd30", "size_in_bytes": 7506}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/G_P_O_S_.cpython-311.pyc", "path_type": "hardlink", "sha256": "0f18d2c2dadb898481c1db06750adb58f3185e4a82a3e29f9ff8aba4ad8388c2", "sha256_in_prefix": "0f18d2c2dadb898481c1db06750adb58f3185e4a82a3e29f9ff8aba4ad8388c2", "size_in_bytes": 796}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/G_S_U_B_.cpython-311.pyc", "path_type": "hardlink", "sha256": "7ec419755c02043cfba59fb79b0b61d1cfd77849c0ba6a8925a3d2f070a64856", "sha256_in_prefix": "7ec419755c02043cfba59fb79b0b61d1cfd77849c0ba6a8925a3d2f070a64856", "size_in_bytes": 690}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/G_V_A_R_.cpython-311.pyc", "path_type": "hardlink", "sha256": "f768829d401c289ef46df7895b7c183f86f8d2513535901182c25756694fd497", "sha256_in_prefix": "f768829d401c289ef46df7895b7c183f86f8d2513535901182c25756694fd497", "size_in_bytes": 491}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/G__l_a_t.cpython-311.pyc", "path_type": "hardlink", "sha256": "5cb0e7b6b9ed2a6b4bee913578d431287dea6f9f26302f4d1028389102f13f11", "sha256_in_prefix": "5cb0e7b6b9ed2a6b4bee913578d431287dea6f9f26302f4d1028389102f13f11", "size_in_bytes": 13953}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/G__l_o_c.cpython-311.pyc", "path_type": "hardlink", "sha256": "dbf5fa68064ffaa62f7c9ad2946edb6b7b6a7841eb820c87e6ca7649530ad6aa", "sha256_in_prefix": "dbf5fa68064ffaa62f7c9ad2946edb6b7b6a7841eb820c87e6ca7649530ad6aa", "size_in_bytes": 5304}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/H_V_A_R_.cpython-311.pyc", "path_type": "hardlink", "sha256": "929f4965e40841fb56153225ae7733368b1f2493d8e094ea52a133a6f5f9153b", "sha256_in_prefix": "929f4965e40841fb56153225ae7733368b1f2493d8e094ea52a133a6f5f9153b", "size_in_bytes": 709}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/J_S_T_F_.cpython-311.pyc", "path_type": "hardlink", "sha256": "54c247728b302a6240a15e09ea5c5a33817ae0e828bbee9acde9d612cac47eca", "sha256_in_prefix": "54c247728b302a6240a15e09ea5c5a33817ae0e828bbee9acde9d612cac47eca", "size_in_bytes": 711}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/L_T_S_H_.cpython-311.pyc", "path_type": "hardlink", "sha256": "f4aeeccb24c3755092256d9e9babb81e8aff21efbadfaf6de6df65545b0e2451", "sha256_in_prefix": "f4aeeccb24c3755092256d9e9babb81e8aff21efbadfaf6de6df65545b0e2451", "size_in_bytes": 3543}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/M_A_T_H_.cpython-311.pyc", "path_type": "hardlink", "sha256": "cb02e8315b75c8e0c8267f43808d450ab27b4764d6494c83597ed031b08466f2", "sha256_in_prefix": "cb02e8315b75c8e0c8267f43808d450ab27b4764d6494c83597ed031b08466f2", "size_in_bytes": 738}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/M_E_T_A_.cpython-311.pyc", "path_type": "hardlink", "sha256": "0faf5f3ff69f2534a335fe503808e89bcea9a281141ac543122aa9fcfaa3e9b8", "sha256_in_prefix": "0faf5f3ff69f2534a335fe503808e89bcea9a281141ac543122aa9fcfaa3e9b8", "size_in_bytes": 15253}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/M_V_A_R_.cpython-311.pyc", "path_type": "hardlink", "sha256": "6cb833be65edaeb62b5dc0aff65f30c4ea39ca0066639286bb2c288733477c23", "sha256_in_prefix": "6cb833be65edaeb62b5dc0aff65f30c4ea39ca0066639286bb2c288733477c23", "size_in_bytes": 704}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/O_S_2f_2.cpython-311.pyc", "path_type": "hardlink", "sha256": "71bc9e536af7d658e83f3ce5eb964d2a21cd2d81d006ce38a4cabc53a1878f62", "sha256_in_prefix": "71bc9e536af7d658e83f3ce5eb964d2a21cd2d81d006ce38a4cabc53a1878f62", "size_in_bytes": 33454}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/S_I_N_G_.cpython-311.pyc", "path_type": "hardlink", "sha256": "666cafb11bb840f44aead25ea2790339d9149849313646644118d18c53deb258", "sha256_in_prefix": "666cafb11bb840f44aead25ea2790339d9149849313646644118d18c53deb258", "size_in_bytes": 5635}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/S_T_A_T_.cpython-311.pyc", "path_type": "hardlink", "sha256": "c5fab187197dda14c4902502b75c86ac369c3d5dafacc0ac582193ea966afdaa", "sha256_in_prefix": "c5fab187197dda14c4902502b75c86ac369c3d5dafacc0ac582193ea966afdaa", "size_in_bytes": 897}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/S_V_G_.cpython-311.pyc", "path_type": "hardlink", "sha256": "9ec41c4c7621324938de62d0dbbcc1a804e421cf1c053f6fa0cb74f71061d339", "sha256_in_prefix": "9ec41c4c7621324938de62d0dbbcc1a804e421cf1c053f6fa0cb74f71061d339", "size_in_bytes": 10273}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/S__i_l_f.cpython-311.pyc", "path_type": "hardlink", "sha256": "8399497dd8be6c11f515350001cb2ff5da759e65bb155344280e33f9640e984b", "sha256_in_prefix": "8399497dd8be6c11f515350001cb2ff5da759e65bb155344280e33f9640e984b", "size_in_bytes": 58270}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/S__i_l_l.cpython-311.pyc", "path_type": "hardlink", "sha256": "ebfb9e6a241f87da946ce5fd93156f38eae13cb21e10dd653233f86a8fa78385", "sha256_in_prefix": "ebfb9e6a241f87da946ce5fd93156f38eae13cb21e10dd653233f86a8fa78385", "size_in_bytes": 6212}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I_B_.cpython-311.pyc", "path_type": "hardlink", "sha256": "3898fbcd32424b2ed93691c6b93084c1dc0703a081cbcc91b6c307e13f2f3182", "sha256_in_prefix": "3898fbcd32424b2ed93691c6b93084c1dc0703a081cbcc91b6c307e13f2f3182", "size_in_bytes": 742}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I_C_.cpython-311.pyc", "path_type": "hardlink", "sha256": "377fd7eae89d2f7d43bb53e275b70378322a81294e8e6f932bebc2a052d59d36", "sha256_in_prefix": "377fd7eae89d2f7d43bb53e275b70378322a81294e8e6f932bebc2a052d59d36", "size_in_bytes": 784}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I_D_.cpython-311.pyc", "path_type": "hardlink", "sha256": "91d926ce0f244f4b062a838d4ef0e4ff2a8c7117c769bba1f10b565d68f4b040", "sha256_in_prefix": "91d926ce0f244f4b062a838d4ef0e4ff2a8c7117c769bba1f10b565d68f4b040", "size_in_bytes": 742}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I_J_.cpython-311.pyc", "path_type": "hardlink", "sha256": "0cba69f6cd378c12d8b3399ec78ed612d4d2ccc55f781298eedd9fad22438ab7", "sha256_in_prefix": "0cba69f6cd378c12d8b3399ec78ed612d4d2ccc55f781298eedd9fad22438ab7", "size_in_bytes": 742}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I_P_.cpython-311.pyc", "path_type": "hardlink", "sha256": "e9fb35be4e6096fe9eb7dd14fcc6849d3ff4fc7a94aa6528ce1705e2b8e57eb9", "sha256_in_prefix": "e9fb35be4e6096fe9eb7dd14fcc6849d3ff4fc7a94aa6528ce1705e2b8e57eb9", "size_in_bytes": 742}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I_S_.cpython-311.pyc", "path_type": "hardlink", "sha256": "2a690202a7df1bcf125756bc9d758f1720ccb3a23992c587afb788e4f0a5b5b6", "sha256_in_prefix": "2a690202a7df1bcf125756bc9d758f1720ccb3a23992c587afb788e4f0a5b5b6", "size_in_bytes": 742}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I_V_.cpython-311.pyc", "path_type": "hardlink", "sha256": "fa548c5afa176451f2d06ac0881b27e29d45f9a4cd957db85a02cf2a645a751a", "sha256_in_prefix": "fa548c5afa176451f2d06ac0881b27e29d45f9a4cd957db85a02cf2a645a751a", "size_in_bytes": 2067}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I__0.cpython-311.pyc", "path_type": "hardlink", "sha256": "2063dbd1ea043af2810643b51b7862642ca81fb617e6d3895775c45eaadf27dd", "sha256_in_prefix": "2063dbd1ea043af2810643b51b7862642ca81fb617e6d3895775c45eaadf27dd", "size_in_bytes": 3927}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I__1.cpython-311.pyc", "path_type": "hardlink", "sha256": "4d27098660446901e43452d089f1ae7b0dff63fa9958755a7401eb6cbf99044c", "sha256_in_prefix": "4d27098660446901e43452d089f1ae7b0dff63fa9958755a7401eb6cbf99044c", "size_in_bytes": 7259}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I__2.cpython-311.pyc", "path_type": "hardlink", "sha256": "5cf6eecf69754b5e30e998ceac98f3d7fa1493cc276c0b06f064c7744d4adbe4", "sha256_in_prefix": "5cf6eecf69754b5e30e998ceac98f3d7fa1493cc276c0b06f064c7744d4adbe4", "size_in_bytes": 959}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I__3.cpython-311.pyc", "path_type": "hardlink", "sha256": "2628eb386dfbac4dce84d4d53937f7635e845d4186113fd6cfa12589cf43def9", "sha256_in_prefix": "2628eb386dfbac4dce84d4d53937f7635e845d4186113fd6cfa12589cf43def9", "size_in_bytes": 980}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_S_I__5.cpython-311.pyc", "path_type": "hardlink", "sha256": "c43f99add8853b64dfa2eff780b54eb39000f6efe1a0f1a7a66c633956d83779", "sha256_in_prefix": "c43f99add8853b64dfa2eff780b54eb39000f6efe1a0f1a7a66c633956d83779", "size_in_bytes": 3714}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/T_T_F_A_.cpython-311.pyc", "path_type": "hardlink", "sha256": "1235da4e6e97e3b3e311c63d00c4179fecb02b79f58e51665c76c724b3f3bda9", "sha256_in_prefix": "1235da4e6e97e3b3e311c63d00c4179fecb02b79f58e51665c76c724b3f3bda9", "size_in_bytes": 798}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/TupleVariation.cpython-311.pyc", "path_type": "hardlink", "sha256": "cc0b1b2225c51277624c81c703c62464aa1350495b8b164ad59e4397923752fc", "sha256_in_prefix": "cc0b1b2225c51277624c81c703c62464aa1350495b8b164ad59e4397923752fc", "size_in_bytes": 41010}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/V_A_R_C_.cpython-311.pyc", "path_type": "hardlink", "sha256": "8aa6a7bcffca9d8c4b9a7dc1f4bef0fa8beada9f25809000a48eb1176fb5475d", "sha256_in_prefix": "8aa6a7bcffca9d8c4b9a7dc1f4bef0fa8beada9f25809000a48eb1176fb5475d", "size_in_bytes": 685}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/V_D_M_X_.cpython-311.pyc", "path_type": "hardlink", "sha256": "403514e67be328074aa2f09c78e50c1dfeadd232b34bd95e9e0ff866b857cbf3", "sha256_in_prefix": "403514e67be328074aa2f09c78e50c1dfeadd232b34bd95e9e0ff866b857cbf3", "size_in_bytes": 12239}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/V_O_R_G_.cpython-311.pyc", "path_type": "hardlink", "sha256": "46c43a089ceba28fd882b4af465ee977a0b138771a705390d27001b26de99517", "sha256_in_prefix": "46c43a089ceba28fd882b4af465ee977a0b138771a705390d27001b26de99517", "size_in_bytes": 9826}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/V_V_A_R_.cpython-311.pyc", "path_type": "hardlink", "sha256": "f2dce0998220f1ee5e30f8df2ec4bee1c9270e6ab3c7ac0723fc9328677aa2b8", "sha256_in_prefix": "f2dce0998220f1ee5e30f8df2ec4bee1c9270e6ab3c7ac0723fc9328677aa2b8", "size_in_bytes": 715}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "6f7a7ded95e7ce25d6d4cb5dea7998efb7b608cec1c1ff5763304ede9e438f25", "sha256_in_prefix": "6f7a7ded95e7ce25d6d4cb5dea7998efb7b608cec1c1ff5763304ede9e438f25", "size_in_bytes": 5202}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_a_n_k_r.cpython-311.pyc", "path_type": "hardlink", "sha256": "8b07e5a3d71bd5ab04d31b57b2da86e4cc7d9de3a050b5d4a083a8f82967e00f", "sha256_in_prefix": "8b07e5a3d71bd5ab04d31b57b2da86e4cc7d9de3a050b5d4a083a8f82967e00f", "size_in_bytes": 882}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_a_v_a_r.cpython-311.pyc", "path_type": "hardlink", "sha256": "a9d6cb13a7072b0263d2fc570818a48ac79dceda7d33801e28eb7a71d2c25aab", "sha256_in_prefix": "a9d6cb13a7072b0263d2fc570818a48ac79dceda7d33801e28eb7a71d2c25aab", "size_in_bytes": 11472}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_b_s_l_n.cpython-311.pyc", "path_type": "hardlink", "sha256": "5cebacc3b38a8e0127501acc2983990f36318fac6c2c256b0663bb9fc4399918", "sha256_in_prefix": "5cebacc3b38a8e0127501acc2983990f36318fac6c2c256b0663bb9fc4399918", "size_in_bytes": 782}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_c_i_d_g.cpython-311.pyc", "path_type": "hardlink", "sha256": "1e099b60fcbcd7aaa46c59d0e9d65521e5e3e0cb8ac9b00ed11a70732a26595f", "sha256_in_prefix": "1e099b60fcbcd7aaa46c59d0e9d65521e5e3e0cb8ac9b00ed11a70732a26595f", "size_in_bytes": 1296}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_c_m_a_p.cpython-311.pyc", "path_type": "hardlink", "sha256": "ee1982a658778543d5058fa95bec0c4300cd3cf2ac16ff53f4c05754f83efaf3", "sha256_in_prefix": "ee1982a658778543d5058fa95bec0c4300cd3cf2ac16ff53f4c05754f83efaf3", "size_in_bytes": 69200}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_c_v_a_r.cpython-311.pyc", "path_type": "hardlink", "sha256": "567c1b65d11624b0dbe07186b61d390310935a64b78f3e25550426f2b59ea9d4", "sha256_in_prefix": "567c1b65d11624b0dbe07186b61d390310935a64b78f3e25550426f2b59ea9d4", "size_in_bytes": 5847}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_c_v_t.cpython-311.pyc", "path_type": "hardlink", "sha256": "b3d8583450f14b70d58bf90a65464f0c771d7abd5df799f99bce5a864802688f", "sha256_in_prefix": "b3d8583450f14b70d58bf90a65464f0c771d7abd5df799f99bce5a864802688f", "size_in_bytes": 3665}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_f_e_a_t.cpython-311.pyc", "path_type": "hardlink", "sha256": "a8745ff32f678dc773ed8dd5575e318ed58a5ea03422c8c2c8a06b8dbf20ce05", "sha256_in_prefix": "a8745ff32f678dc773ed8dd5575e318ed58a5ea03422c8c2c8a06b8dbf20ce05", "size_in_bytes": 868}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_f_p_g_m.cpython-311.pyc", "path_type": "hardlink", "sha256": "b4b8f8f73cf2311935fe7d858574ed499ace54250ef4e8b1cd103f0c9e416b16", "sha256_in_prefix": "b4b8f8f73cf2311935fe7d858574ed499ace54250ef4e8b1cd103f0c9e416b16", "size_in_bytes": 3008}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_f_v_a_r.cpython-311.pyc", "path_type": "hardlink", "sha256": "3da6333659fcc426503c7fddfa85c77c16ec7e311af8e56ea3f857a0a6a5c1f4", "sha256_in_prefix": "3da6333659fcc426503c7fddfa85c77c16ec7e311af8e56ea3f857a0a6a5c1f4", "size_in_bytes": 15309}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_g_a_s_p.cpython-311.pyc", "path_type": "hardlink", "sha256": "2fdd6373e5abd1bfb0c509f68d93e70eb2e5bc072dbc81e38bb0c27cf6734590", "sha256_in_prefix": "2fdd6373e5abd1bfb0c509f68d93e70eb2e5bc072dbc81e38bb0c27cf6734590", "size_in_bytes": 3596}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_g_c_i_d.cpython-311.pyc", "path_type": "hardlink", "sha256": "a9cd901ead50486d17920785ed6c5394f801aa9f0e452c2933537736b2a284f0", "sha256_in_prefix": "a9cd901ead50486d17920785ed6c5394f801aa9f0e452c2933537736b2a284f0", "size_in_bytes": 676}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_g_l_y_f.cpython-311.pyc", "path_type": "hardlink", "sha256": "25f631b20efc23a4da275fba09864cbcbf902561c8b2f77467542c3b043a223f", "sha256_in_prefix": "25f631b20efc23a4da275fba09864cbcbf902561c8b2f77467542c3b043a223f", "size_in_bytes": 107319}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_g_v_a_r.cpython-311.pyc", "path_type": "hardlink", "sha256": "212cf4c72e49971db806619704238922a89706d8e5fcc8ae5ebba3c0996d9b84", "sha256_in_prefix": "212cf4c72e49971db806619704238922a89706d8e5fcc8ae5ebba3c0996d9b84", "size_in_bytes": 16995}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_h_d_m_x.cpython-311.pyc", "path_type": "hardlink", "sha256": "b710bcdecf89c13c486fbfa0dc5b4a78e2e6618afec20a05ca2d8a35059157ef", "sha256_in_prefix": "b710bcdecf89c13c486fbfa0dc5b4a78e2e6618afec20a05ca2d8a35059157ef", "size_in_bytes": 8328}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_h_e_a_d.cpython-311.pyc", "path_type": "hardlink", "sha256": "9497bf224aa0aa810858a18e9aa7384876afe81cd32fcf6a189440636f25b429", "sha256_in_prefix": "9497bf224aa0aa810858a18e9aa7384876afe81cd32fcf6a189440636f25b429", "size_in_bytes": 6114}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_h_h_e_a.cpython-311.pyc", "path_type": "hardlink", "sha256": "f12fdd7b1ee5968be6752cbae58a2853d4516b442bc7d08921e4c6fb1f1fc96a", "sha256_in_prefix": "f12fdd7b1ee5968be6752cbae58a2853d4516b442bc7d08921e4c6fb1f1fc96a", "size_in_bytes": 7034}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_h_m_t_x.cpython-311.pyc", "path_type": "hardlink", "sha256": "3c0d6137cc2ac202bd7ea7b4ca26018b6567d5aac0f50e5beae2501018f65ae8", "sha256_in_prefix": "3c0d6137cc2ac202bd7ea7b4ca26018b6567d5aac0f50e5beae2501018f65ae8", "size_in_bytes": 8232}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_k_e_r_n.cpython-311.pyc", "path_type": "hardlink", "sha256": "8595c4d2641ea659b59220492dee8e4110264de92c24f1a491148462f2a41e74", "sha256_in_prefix": "8595c4d2641ea659b59220492dee8e4110264de92c24f1a491148462f2a41e74", "size_in_bytes": 15502}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_l_c_a_r.cpython-311.pyc", "path_type": "hardlink", "sha256": "ea0b635807463777ab65b978d5f5ec4593ed1b477090ffd05e2875cdf12cd141", "sha256_in_prefix": "ea0b635807463777ab65b978d5f5ec4593ed1b477090ffd05e2875cdf12cd141", "size_in_bytes": 789}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_l_o_c_a.cpython-311.pyc", "path_type": "hardlink", "sha256": "4979fe1194b0fc6656e4f39571641cacce95856e8abe9f1bc76000d79a84d243", "sha256_in_prefix": "4979fe1194b0fc6656e4f39571641cacce95856e8abe9f1bc76000d79a84d243", "size_in_bytes": 4423}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_l_t_a_g.cpython-311.pyc", "path_type": "hardlink", "sha256": "b98e8aa970082b9fd8e8dbeaa82bdc09a4666b34dc6459c75b1844620a42c8aa", "sha256_in_prefix": "b98e8aa970082b9fd8e8dbeaa82bdc09a4666b34dc6459c75b1844620a42c8aa", "size_in_bytes": 4912}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_m_a_x_p.cpython-311.pyc", "path_type": "hardlink", "sha256": "7a172d3f06fa7fa2b0d618093f783bdba76d5f2c06eae137bb5711672016cbd5", "sha256_in_prefix": "7a172d3f06fa7fa2b0d618093f783bdba76d5f2c06eae137bb5711672016cbd5", "size_in_bytes": 7060}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_m_e_t_a.cpython-311.pyc", "path_type": "hardlink", "sha256": "dc8b840eb1a5a2909136f0fee75a74e2f8b7d6f26b818e1d6e58eef80e0c9548", "sha256_in_prefix": "dc8b840eb1a5a2909136f0fee75a74e2f8b7d6f26b818e1d6e58eef80e0c9548", "size_in_bytes": 6162}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_m_o_r_t.cpython-311.pyc", "path_type": "hardlink", "sha256": "b26f1ec624fff2538be84858f711d295ae0fff362a9da771bbd3fb8281e94a83", "sha256_in_prefix": "b26f1ec624fff2538be84858f711d295ae0fff362a9da771bbd3fb8281e94a83", "size_in_bytes": 804}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_m_o_r_x.cpython-311.pyc", "path_type": "hardlink", "sha256": "c1d5b1feccb6531359947671b31bd32c938902ec3129220f7a35da40f7461160", "sha256_in_prefix": "c1d5b1feccb6531359947671b31bd32c938902ec3129220f7a35da40f7461160", "size_in_bytes": 865}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_n_a_m_e.cpython-311.pyc", "path_type": "hardlink", "sha256": "1cb05255aa33f4ff2ac39bb585b3335db891bae9dea3dbf75e41542d6f43e785", "sha256_in_prefix": "1cb05255aa33f4ff2ac39bb585b3335db891bae9dea3dbf75e41542d6f43e785", "size_in_bytes": 50618}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_o_p_b_d.cpython-311.pyc", "path_type": "hardlink", "sha256": "e73cb1bf2c989ca3f222898ab90070f5ee38ef8265d69a81a9896071ea575b95", "sha256_in_prefix": "e73cb1bf2c989ca3f222898ab90070f5ee38ef8265d69a81a9896071ea575b95", "size_in_bytes": 765}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_p_o_s_t.cpython-311.pyc", "path_type": "hardlink", "sha256": "03414755fc4a88695af215c72f1298b3c139e5154ea8ef29e39599bf2362bbe9", "sha256_in_prefix": "03414755fc4a88695af215c72f1298b3c139e5154ea8ef29e39599bf2362bbe9", "size_in_bytes": 16074}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_p_r_e_p.cpython-311.pyc", "path_type": "hardlink", "sha256": "0ab121efcf36f8734a710deef1b76ffe943b9617deef3af17fbc9094ceefc82c", "sha256_in_prefix": "0ab121efcf36f8734a710deef1b76ffe943b9617deef3af17fbc9094ceefc82c", "size_in_bytes": 873}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_p_r_o_p.cpython-311.pyc", "path_type": "hardlink", "sha256": "0a4b2b3e4c88c3d4ce951d14a08b9badaa76b3e98980d6c1d6bd80eb5da10e41", "sha256_in_prefix": "0a4b2b3e4c88c3d4ce951d14a08b9badaa76b3e98980d6c1d6bd80eb5da10e41", "size_in_bytes": 741}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_s_b_i_x.cpython-311.pyc", "path_type": "hardlink", "sha256": "53d709faa6554f89f284a1ba04391e6c860f847ceff4ca3d9e369142e003c884", "sha256_in_prefix": "53d709faa6554f89f284a1ba04391e6c860f847ceff4ca3d9e369142e003c884", "size_in_bytes": 6740}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_t_r_a_k.cpython-311.pyc", "path_type": "hardlink", "sha256": "22e5aaa164e6111f8fb2b0c173191c3884b978749ad231b92c047bb1e8c7ed93", "sha256_in_prefix": "22e5aaa164e6111f8fb2b0c173191c3884b978749ad231b92c047bb1e8c7ed93", "size_in_bytes": 17614}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_v_h_e_a.cpython-311.pyc", "path_type": "hardlink", "sha256": "07700c197d3fcd59e1caf5f2296edde5d92132c98e801599c41969ba4cf83fcd", "sha256_in_prefix": "07700c197d3fcd59e1caf5f2296edde5d92132c98e801599c41969ba4cf83fcd", "size_in_bytes": 6512}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/_v_m_t_x.cpython-311.pyc", "path_type": "hardlink", "sha256": "f9197d89f780ee03394f9fbd346fee72f96481e9008cb04eea5130f178b05067", "sha256_in_prefix": "f9197d89f780ee03394f9fbd346fee72f96481e9008cb04eea5130f178b05067", "size_in_bytes": 969}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/asciiTable.cpython-311.pyc", "path_type": "hardlink", "sha256": "5de1b2105f7b63383c15dde556d8830cd37bd580821427b5764c35aed9124a13", "sha256_in_prefix": "5de1b2105f7b63383c15dde556d8830cd37bd580821427b5764c35aed9124a13", "size_in_bytes": 1764}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/grUtils.cpython-311.pyc", "path_type": "hardlink", "sha256": "5070f77f952ad3453513e089b8ef470cef707e177713a9051d71bbdd7a0149d1", "sha256_in_prefix": "5070f77f952ad3453513e089b8ef470cef707e177713a9051d71bbdd7a0149d1", "size_in_bytes": 4645}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/otBase.cpython-311.pyc", "path_type": "hardlink", "sha256": "c777e82392a904b71a6f1c11a8e38199617edbd22d166eb24d3a732f28e162f4", "sha256_in_prefix": "c777e82392a904b71a6f1c11a8e38199617edbd22d166eb24d3a732f28e162f4", "size_in_bytes": 68383}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/otConverters.cpython-311.pyc", "path_type": "hardlink", "sha256": "a0fd7a809683274b3da94db8872e5323342baf18c7654c2f9d1b3442cfbf4027", "sha256_in_prefix": "a0fd7a809683274b3da94db8872e5323342baf18c7654c2f9d1b3442cfbf4027", "size_in_bytes": 117946}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/otData.cpython-311.pyc", "path_type": "hardlink", "sha256": "c86985894a7820c4fe9daa7c4d5aff046d8e70f745310a11371dfd1ed603d51b", "sha256_in_prefix": "c86985894a7820c4fe9daa7c4d5aff046d8e70f745310a11371dfd1ed603d51b", "size_in_bytes": 86227}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/otTables.cpython-311.pyc", "path_type": "hardlink", "sha256": "93c7c3e75765413188c90b6e267dbcacc56a6b876b2ce5133e7a48807318531e", "sha256_in_prefix": "93c7c3e75765413188c90b6e267dbcacc56a6b876b2ce5133e7a48807318531e", "size_in_bytes": 133565}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/otTraverse.cpython-311.pyc", "path_type": "hardlink", "sha256": "5b5f371ca5f1e70ce65254291dcc63139773ab2d42fffcdcfdb9d0e3cbe2e094", "sha256_in_prefix": "5b5f371ca5f1e70ce65254291dcc63139773ab2d42fffcdcfdb9d0e3cbe2e094", "size_in_bytes": 7413}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/sbixGlyph.cpython-311.pyc", "path_type": "hardlink", "sha256": "8c899dbe690944db44d213ea52d758d2871d36359292adde7786bc5147f92b76", "sha256_in_prefix": "8c899dbe690944db44d213ea52d758d2871d36359292adde7786bc5147f92b76", "size_in_bytes": 6923}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/sbixStrike.cpython-311.pyc", "path_type": "hardlink", "sha256": "41e8c3a87513858ac56f2818ea2733fabd974dec2f344a186c2e13d0846d3e9b", "sha256_in_prefix": "41e8c3a87513858ac56f2818ea2733fabd974dec2f344a186c2e13d0846d3e9b", "size_in_bytes": 7437}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/__pycache__/ttProgram.cpython-311.pyc", "path_type": "hardlink", "sha256": "e365fe0c9eb08cd1d930009af282d6fbe7539c6c29cd682c131bf6b02d0154be", "sha256_in_prefix": "e365fe0c9eb08cd1d930009af282d6fbe7539c6c29cd682c131bf6b02d0154be", "size_in_bytes": 27019}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_a_n_k_r.py", "path_type": "hardlink", "sha256": "3290332227e6222ff7831da83fa3c2dd1da5bb7e84c2caf6f96d6b5e3b33d948", "sha256_in_prefix": "3290332227e6222ff7831da83fa3c2dd1da5bb7e84c2caf6f96d6b5e3b33d948", "size_in_bytes": 483}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_a_v_a_r.py", "path_type": "hardlink", "sha256": "628740cc2b08cb000a578f0fe23412aeb527618596b98e920e73544406cce9f5", "sha256_in_prefix": "628740cc2b08cb000a578f0fe23412aeb527618596b98e920e73544406cce9f5", "size_in_bytes": 7175}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_b_s_l_n.py", "path_type": "hardlink", "sha256": "ffce3ca3b490ab3b730437c761e8bef34bbd96dc481d50735eed5d6072d5e7b3", "sha256_in_prefix": "ffce3ca3b490ab3b730437c761e8bef34bbd96dc481d50735eed5d6072d5e7b3", "size_in_bytes": 465}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_c_i_d_g.py", "path_type": "hardlink", "sha256": "cadf2b54869da49483502a151f87597ad362cb40339b91120311e3076057fde0", "sha256_in_prefix": "cadf2b54869da49483502a151f87597ad362cb40339b91120311e3076057fde0", "size_in_bytes": 913}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_c_m_a_p.py", "path_type": "hardlink", "sha256": "afcf9b07f1341108798784c65f99d39c39f04d4b57b910778aea840e837f20e3", "sha256_in_prefix": "afcf9b07f1341108798784c65f99d39c39f04d4b57b910778aea840e837f20e3", "size_in_bytes": 62202}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_c_v_a_r.py", "path_type": "hardlink", "sha256": "df96b2936917d6970b1b0cb1d32e08d65fabecb1e074abf4ba5571f2804fb5e2", "sha256_in_prefix": "df96b2936917d6970b1b0cb1d32e08d65fabecb1e074abf4ba5571f2804fb5e2", "size_in_bytes": 3527}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_c_v_t.py", "path_type": "hardlink", "sha256": "d7f46111c4e6856416429ec7b23f14b01c8a997088a6967221b200ac6cb01043", "sha256_in_prefix": "d7f46111c4e6856416429ec7b23f14b01c8a997088a6967221b200ac6cb01043", "size_in_bytes": 1618}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_f_e_a_t.py", "path_type": "hardlink", "sha256": "162d579e3864086d2da78dc072fa486a2bc3f98445a6e7e8e9f78622b7a7403a", "sha256_in_prefix": "162d579e3864086d2da78dc072fa486a2bc3f98445a6e7e8e9f78622b7a7403a", "size_in_bytes": 469}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_f_p_g_m.py", "path_type": "hardlink", "sha256": "b991d9cea2fa39d2e7fc7c6c92ffb17f75ee1387f2692bff8db00b123c1762e8", "sha256_in_prefix": "b991d9cea2fa39d2e7fc7c6c92ffb17f75ee1387f2692bff8db00b123c1762e8", "size_in_bytes": 1633}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_f_v_a_r.py", "path_type": "hardlink", "sha256": "ad5df71f60601d49775aec9db28bb51be1e2e2e012069a5668b8f714c9a22e3b", "sha256_in_prefix": "ad5df71f60601d49775aec9db28bb51be1e2e2e012069a5668b8f714c9a22e3b", "size_in_bytes": 8837}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_g_a_s_p.py", "path_type": "hardlink", "sha256": "62f840543bddb2c3767e33cc4df4ab3b858109f4ee87d4f6714e73aae0d59d2c", "sha256_in_prefix": "62f840543bddb2c3767e33cc4df4ab3b858109f4ee87d4f6714e73aae0d59d2c", "size_in_bytes": 2203}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_g_c_i_d.py", "path_type": "hardlink", "sha256": "009e2e57b3d31db9ecc384dfc3c73613387454ca31de8007d2a861abbcbc85d3", "sha256_in_prefix": "009e2e57b3d31db9ecc384dfc3c73613387454ca31de8007d2a861abbcbc85d3", "size_in_bytes": 362}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_g_l_y_f.py", "path_type": "hardlink", "sha256": "74357ae6596c10323d7ca715282e533a2697a574b2307344cadb98386b7b69d3", "sha256_in_prefix": "74357ae6596c10323d7ca715282e533a2697a574b2307344cadb98386b7b69d3", "size_in_bytes": 85584}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_g_v_a_r.py", "path_type": "hardlink", "sha256": "13d5822a37884d47dde617092d0d2b8d0141b59771c357acc05956a7553a6c3e", "sha256_in_prefix": "13d5822a37884d47dde617092d0d2b8d0141b59771c357acc05956a7553a6c3e", "size_in_bytes": 12196}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_h_d_m_x.py", "path_type": "hardlink", "sha256": "c0cacee03d3840d4fcbb7d29f0057e686ddb9dd5c2ab8c253cdbed6ddf22a7b7", "sha256_in_prefix": "c0cacee03d3840d4fcbb7d29f0057e686ddb9dd5c2ab8c253cdbed6ddf22a7b7", "size_in_bytes": 4252}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_h_e_a_d.py", "path_type": "hardlink", "sha256": "c98d864c5aba327ea737c11e81b315251314588a83605967dc5853937ce2c3ab", "sha256_in_prefix": "c98d864c5aba327ea737c11e81b315251314588a83605967dc5853937ce2c3ab", "size_in_bytes": 4926}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_h_h_e_a.py", "path_type": "hardlink", "sha256": "5f8b75685d4c64cbb3de984255217029c3537a865dc7ed38db016d1dcfa72bdc", "sha256_in_prefix": "5f8b75685d4c64cbb3de984255217029c3537a865dc7ed38db016d1dcfa72bdc", "size_in_bytes": 4767}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_h_m_t_x.py", "path_type": "hardlink", "sha256": "adbc6bddccbdfbd266d070862164225fa7c61f98aeeb2a23a7d91f816ad6d8ab", "sha256_in_prefix": "adbc6bddccbdfbd266d070862164225fa7c61f98aeeb2a23a7d91f816ad6d8ab", "size_in_bytes": 6192}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_k_e_r_n.py", "path_type": "hardlink", "sha256": "0d034b983fc711d0ca3dfa78b5a98e77d5b74f96b595714ce6d0da5ab2a5d7ae", "sha256_in_prefix": "0d034b983fc711d0ca3dfa78b5a98e77d5b74f96b595714ce6d0da5ab2a5d7ae", "size_in_bytes": 10794}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_l_c_a_r.py", "path_type": "hardlink", "sha256": "f16eb114e8feb26d34dcc0975f86c81f1b3d9ed7d5bd3d055d89653f16b7cf82", "sha256_in_prefix": "f16eb114e8feb26d34dcc0975f86c81f1b3d9ed7d5bd3d055d89653f16b7cf82", "size_in_bytes": 390}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_l_o_c_a.py", "path_type": "hardlink", "sha256": "cb18b02ca5cb663363bb95d898b6fa12134b79cd5dedecc40c37b576ccdc787a", "sha256_in_prefix": "cb18b02ca5cb663363bb95d898b6fa12134b79cd5dedecc40c37b576ccdc787a", "size_in_bytes": 2180}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_l_t_a_g.py", "path_type": "hardlink", "sha256": "f58a40a6323ead9e1edc7793f0f8fea2c887977b802c3f495e0e4eee9aa4f4bd", "sha256_in_prefix": "f58a40a6323ead9e1edc7793f0f8fea2c887977b802c3f495e0e4eee9aa4f4bd", "size_in_bytes": 2552}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_m_a_x_p.py", "path_type": "hardlink", "sha256": "7080c8656b1ef5ccf0c2c9e5c48877ab08306976edecf4008d7280726303b296", "sha256_in_prefix": "7080c8656b1ef5ccf0c2c9e5c48877ab08306976edecf4008d7280726303b296", "size_in_bytes": 5264}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_m_e_t_a.py", "path_type": "hardlink", "sha256": "0340993c4015c58ae9cad8d750640909375dc06f0aaef5156ed05edc0d4caa02", "sha256_in_prefix": "0340993c4015c58ae9cad8d750640909375dc06f0aaef5156ed05edc0d4caa02", "size_in_bytes": 3913}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_m_o_r_t.py", "path_type": "hardlink", "sha256": "bb7e6d62a9f7723ccac5e085d0550578bb5a7f7ea68c30d2374f2eba4d0a99ef", "sha256_in_prefix": "bb7e6d62a9f7723ccac5e085d0550578bb5a7f7ea68c30d2374f2eba4d0a99ef", "size_in_bytes": 487}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_m_o_r_x.py", "path_type": "hardlink", "sha256": "3b06a656920eed11039c5afde47b853e863fd14ea2f7340f6f5d4ad6c153bc36", "sha256_in_prefix": "3b06a656920eed11039c5afde47b853e863fd14ea2f7340f6f5d4ad6c153bc36", "size_in_bytes": 548}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_n_a_m_e.py", "path_type": "hardlink", "sha256": "f3aff47d4780e7f73e198e599e4a942348f25b0321d669fac9539fe8a2aa2255", "sha256_in_prefix": "f3aff47d4780e7f73e198e599e4a942348f25b0321d669fac9539fe8a2aa2255", "size_in_bytes": 41266}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_o_p_b_d.py", "path_type": "hardlink", "sha256": "4cd66fff6613ae3e1d1b377ac00f496fe28667df6e9f5efbb39a772e57f143be", "sha256_in_prefix": "4cd66fff6613ae3e1d1b377ac00f496fe28667df6e9f5efbb39a772e57f143be", "size_in_bytes": 448}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_p_o_s_t.py", "path_type": "hardlink", "sha256": "f6c8955d28ac5867537e3fe60b513d7540f3f49766d62dd0cc8fa5de2dd558c1", "sha256_in_prefix": "f6c8955d28ac5867537e3fe60b513d7540f3f49766d62dd0cc8fa5de2dd558c1", "size_in_bytes": 11671}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_p_r_e_p.py", "path_type": "hardlink", "sha256": "09c2abe07aecc24ba92e66c976b24b4ccfb3f5780b79f432bfce3aee3f55d33b", "sha256_in_prefix": "09c2abe07aecc24ba92e66c976b24b4ccfb3f5780b79f432bfce3aee3f55d33b", "size_in_bytes": 427}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_p_r_o_p.py", "path_type": "hardlink", "sha256": "120f31e6a5b25c3ccf7b331a7c5bb4b34ab23c31e3fac3ec1711ad13a876f6aa", "sha256_in_prefix": "120f31e6a5b25c3ccf7b331a7c5bb4b34ab23c31e3fac3ec1711ad13a876f6aa", "size_in_bytes": 427}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_s_b_i_x.py", "path_type": "hardlink", "sha256": "b6490a6cd28d624521649b8dd2497bab7ef1e4a2b93a8bc1d3acb6f286cf57a0", "sha256_in_prefix": "b6490a6cd28d624521649b8dd2497bab7ef1e4a2b93a8bc1d3acb6f286cf57a0", "size_in_bytes": 4865}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_t_r_a_k.py", "path_type": "hardlink", "sha256": "aebacf64b10b15803917c3c446869f212f5c6ff77f8bac6da40cc711bb051d2c", "sha256_in_prefix": "aebacf64b10b15803917c3c446869f212f5c6ff77f8bac6da40cc711bb051d2c", "size_in_bytes": 11379}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_v_h_e_a.py", "path_type": "hardlink", "sha256": "16e50b201978390c9478b3ce14463c6ee074a409d08466b5fb96ba90df62e527", "sha256_in_prefix": "16e50b201978390c9478b3ce14463c6ee074a409d08466b5fb96ba90df62e527", "size_in_bytes": 4459}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/_v_m_t_x.py", "path_type": "hardlink", "sha256": "014bb1b7242f316ad304d6ce21a2fab8a701fc336935bd185f6f0922e4c7c3f6", "sha256_in_prefix": "014bb1b7242f316ad304d6ce21a2fab8a701fc336935bd185f6f0922e4c7c3f6", "size_in_bytes": 500}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/asciiTable.py", "path_type": "hardlink", "sha256": "e1cebd8ec022ad49c3129ca57fd09806808a4f3c1b99f6ed5003abb4f9e91e36", "sha256_in_prefix": "e1cebd8ec022ad49c3129ca57fd09806808a4f3c1b99f6ed5003abb4f9e91e36", "size_in_bytes": 637}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/grUtils.py", "path_type": "hardlink", "sha256": "85c389e6824f39ddae2569d603bab047b01f2f7ed8679cd44fb83ca3904157cd", "sha256_in_prefix": "85c389e6824f39ddae2569d603bab047b01f2f7ed8679cd44fb83ca3904157cd", "size_in_bytes": 2270}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/otBase.py", "path_type": "hardlink", "sha256": "707768617f8809af06788fad561172d4af7e087684c6228ef87063987d0e91b5", "sha256_in_prefix": "707768617f8809af06788fad561172d4af7e087684c6228ef87063987d0e91b5", "size_in_bytes": 53330}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/otConverters.py", "path_type": "hardlink", "sha256": "8a113f58c48a00a49a05b32f9c56038f678cc5fecfbd130c6bccc6c1fa18b987", "sha256_in_prefix": "8a113f58c48a00a49a05b32f9c56038f678cc5fecfbd130c6bccc6c1fa18b987", "size_in_bytes": 74202}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/otData.py", "path_type": "hardlink", "sha256": "f975d1c1d55f3fe5b3ee806330fa64bb4034407f65c3f7c518dcd996df4dd26a", "sha256_in_prefix": "f975d1c1d55f3fe5b3ee806330fa64bb4034407f65c3f7c518dcd996df4dd26a", "size_in_bytes": 197262}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/otTables.py", "path_type": "hardlink", "sha256": "d94d38a2dff6213941671d90b699c83ad0467ed3c5b3d657d8559fcf8bf3d46d", "sha256_in_prefix": "d94d38a2dff6213941671d90b699c83ad0467ed3c5b3d657d8559fcf8bf3d46d", "size_in_bytes": 96987}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/otTraverse.py", "path_type": "hardlink", "sha256": "1f39c45409557fff1ecaa8ec3b679d8042ed3255e78e752a71c2b73f2b6f5541", "sha256_in_prefix": "1f39c45409557fff1ecaa8ec3b679d8042ed3255e78e752a71c2b73f2b6f5541", "size_in_bytes": 5518}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/sbixGlyph.py", "path_type": "hardlink", "sha256": "b631143d545fc7a82be7299ef14cad193b55ae298a379aa66f34f51993e35e23", "sha256_in_prefix": "b631143d545fc7a82be7299ef14cad193b55ae298a379aa66f34f51993e35e23", "size_in_bytes": 5796}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/sbixStrike.py", "path_type": "hardlink", "sha256": "74bf0ef4af11e12e915500cff8f56320f06bbdba9b13dcf0adad2e44bd272ead", "sha256_in_prefix": "74bf0ef4af11e12e915500cff8f56320f06bbdba9b13dcf0adad2e44bd272ead", "size_in_bytes": 6651}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/table_API_readme.txt", "path_type": "hardlink", "sha256": "7999514cb5242f373ff4eb77a5d7e1c8c6f76a1534fc88a9c74bd26f33951b2f", "sha256_in_prefix": "7999514cb5242f373ff4eb77a5d7e1c8c6f76a1534fc88a9c74bd26f33951b2f", "size_in_bytes": 2748}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/tables/ttProgram.py", "path_type": "hardlink", "sha256": "b60b7181df849ceabed8f5256048a1a7ee8d1eeffb1e7139af179202d9973ad5", "sha256_in_prefix": "b60b7181df849ceabed8f5256048a1a7ee8d1eeffb1e7139af179202d9973ad5", "size_in_bytes": 35888}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/ttCollection.py", "path_type": "hardlink", "sha256": "691a61d8c9012b791df7e2422ea849d4437da5f7cdfe5557e965a63934dec1cf", "sha256_in_prefix": "691a61d8c9012b791df7e2422ea849d4437da5f7cdfe5557e965a63934dec1cf", "size_in_bytes": 3963}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/ttFont.py", "path_type": "hardlink", "sha256": "f08efd92c21db5f31ff0657f9e16b01219733af40c4f207df25ad6ce82400e1d", "sha256_in_prefix": "f08efd92c21db5f31ff0657f9e16b01219733af40c4f207df25ad6ce82400e1d", "size_in_bytes": 40669}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/ttGlyphSet.py", "path_type": "hardlink", "sha256": "7140613066b986ccde56a3a6d8aa4e75e261f8bb22a84ed135dc8850f676bcef", "sha256_in_prefix": "7140613066b986ccde56a3a6d8aa4e75e261f8bb22a84ed135dc8850f676bcef", "size_in_bytes": 17476}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/ttVisitor.py", "path_type": "hardlink", "sha256": "fed6a1e02e364efe8f9bd41e2cd430c150b1a88e1534402a6026e64e1a7a72f6", "sha256_in_prefix": "fed6a1e02e364efe8f9bd41e2cd430c150b1a88e1534402a6026e64e1a7a72f6", "size_in_bytes": 1025}, {"_path": "lib/python3.11/site-packages/fontTools/ttLib/woff2.py", "path_type": "hardlink", "sha256": "e8b3c849e050d5db9bcca8d6ad47189bfbe01130b8e814cb6385e41b9daabd20", "sha256_in_prefix": "e8b3c849e050d5db9bcca8d6ad47189bfbe01130b8e814cb6385e41b9daabd20", "size_in_bytes": 60921}, {"_path": "lib/python3.11/site-packages/fontTools/ttx.py", "path_type": "hardlink", "sha256": "171b86b9bba35821895924eb2448e8347fbedb97d520fcbe651b58cbd1fa8939", "sha256_in_prefix": "171b86b9bba35821895924eb2448e8347fbedb97d520fcbe651b58cbd1fa8939", "size_in_bytes": 17277}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/__init__.py", "path_type": "hardlink", "sha256": "655ade5987cbf948057aceb6a705b9947ca3d2f7ef55b970688951095df856af", "sha256_in_prefix": "655ade5987cbf948057aceb6a705b9947ca3d2f7ef55b970688951095df856af", "size_in_bytes": 98961}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "12ea747824c36b7bbe667d374a8ae1998b1c9ce6cb14a4a94cc87b3098660c1e", "sha256_in_prefix": "12ea747824c36b7bbe667d374a8ae1998b1c9ce6cb14a4a94cc87b3098660c1e", "size_in_bytes": 108075}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/converters.cpython-311.pyc", "path_type": "hardlink", "sha256": "93f44bb64174ca32796b44fa0c36748fb7ee89eb45da631ecdc37b93d573df18", "sha256_in_prefix": "93f44bb64174ca32796b44fa0c36748fb7ee89eb45da631ecdc37b93d573df18", "size_in_bytes": 13862}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/errors.cpython-311.pyc", "path_type": "hardlink", "sha256": "bbe6e4f6401080c934d4131f19f9d617b108bd058975917e00a41c23045f8ead", "sha256_in_prefix": "bbe6e4f6401080c934d4131f19f9d617b108bd058975917e00a41c23045f8ead", "size_in_bytes": 1649}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/etree.cpython-311.pyc", "path_type": "hardlink", "sha256": "341de99f56a09c56751e1af93b42916d097fa57480484c20a278f2bd64fc3f20", "sha256_in_prefix": "341de99f56a09c56751e1af93b42916d097fa57480484c20a278f2bd64fc3f20", "size_in_bytes": 423}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/filenames.cpython-311.pyc", "path_type": "hardlink", "sha256": "be271d34ffe9ac3dad1a5f5820cbdf51d0bb489e80ebee1f61919cb47290929d", "sha256_in_prefix": "be271d34ffe9ac3dad1a5f5820cbdf51d0bb489e80ebee1f61919cb47290929d", "size_in_bytes": 9563}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/glifLib.cpython-311.pyc", "path_type": "hardlink", "sha256": "9f1a2e288eab98a8a39910d60058eeb745ae2dccdabbe1bb8eef828ae2d4fdcf", "sha256_in_prefix": "9f1a2e288eab98a8a39910d60058eeb745ae2dccdabbe1bb8eef828ae2d4fdcf", "size_in_bytes": 91513}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/kerning.cpython-311.pyc", "path_type": "hardlink", "sha256": "b71d3d9625dbc9985f44c4f8441c92500d06de0ab9364baaa0537c3bc59c2d44", "sha256_in_prefix": "b71d3d9625dbc9985f44c4f8441c92500d06de0ab9364baaa0537c3bc59c2d44", "size_in_bytes": 5051}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/plistlib.cpython-311.pyc", "path_type": "hardlink", "sha256": "b67f9fca12ba22d94d5722373120da147de5519284afe5dddee4d7f671c30837", "sha256_in_prefix": "b67f9fca12ba22d94d5722373120da147de5519284afe5dddee4d7f671c30837", "size_in_bytes": 2623}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/pointPen.cpython-311.pyc", "path_type": "hardlink", "sha256": "2e622cc2d8edaa9d86b221d0535043a099af7498b5cc06463ae4f8460ae3fb6b", "sha256_in_prefix": "2e622cc2d8edaa9d86b221d0535043a099af7498b5cc06463ae4f8460ae3fb6b", "size_in_bytes": 439}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "ca3d9326c4f86d54b23ef9e3576fad61fa971a0b60a13d8f145eec11a591c065", "sha256_in_prefix": "ca3d9326c4f86d54b23ef9e3576fad61fa971a0b60a13d8f145eec11a591c065", "size_in_bytes": 5843}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/__pycache__/validators.cpython-311.pyc", "path_type": "hardlink", "sha256": "f558fc6ad290770501affe22e433689d3d96f0f781cb111d861d4afdba49dcd9", "sha256_in_prefix": "f558fc6ad290770501affe22e433689d3d96f0f781cb111d861d4afdba49dcd9", "size_in_bytes": 36288}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/converters.py", "path_type": "hardlink", "sha256": "62704aafc926ca3c0b72af0b743e3ab9b18e8322fd3f1b7d6af96f667f5a9ca2", "sha256_in_prefix": "62704aafc926ca3c0b72af0b743e3ab9b18e8322fd3f1b7d6af96f667f5a9ca2", "size_in_bytes": 13444}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/errors.py", "path_type": "hardlink", "sha256": "f5ff25e4d685023dc164f6ba05baadd3a14be1a7df7cbb8ccf2e273dff9e3a51", "sha256_in_prefix": "f5ff25e4d685023dc164f6ba05baadd3a14be1a7df7cbb8ccf2e273dff9e3a51", "size_in_bytes": 845}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/etree.py", "path_type": "hardlink", "sha256": "4f7b232d3823300aba57261189c58f68c215049d984aec19c55e95739c99b502", "sha256_in_prefix": "4f7b232d3823300aba57261189c58f68c215049d984aec19c55e95739c99b502", "size_in_bytes": 231}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/filenames.py", "path_type": "hardlink", "sha256": "868c94873cd030369e7244fb51dade21200dab8fa02d1da31b2939c80c98b4e0", "sha256_in_prefix": "868c94873cd030369e7244fb51dade21200dab8fa02d1da31b2939c80c98b4e0", "size_in_bytes": 10654}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/glifLib.py", "path_type": "hardlink", "sha256": "63ec737f8a9b4c83a5dfe7552d7beede2142203b4012efe4948776fd4367816b", "sha256_in_prefix": "63ec737f8a9b4c83a5dfe7552d7beede2142203b4012efe4948776fd4367816b", "size_in_bytes": 77170}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/kerning.py", "path_type": "hardlink", "sha256": "a3505e243559fc26593f398e3f044a4ea825626840fc964f8f0ab624b81d4089", "sha256_in_prefix": "a3505e243559fc26593f398e3f044a4ea825626840fc964f8f0ab624b81d4089", "size_in_bytes": 4836}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/plistlib.py", "path_type": "hardlink", "sha256": "8f3306386bc73ba5ef4be20ef214b4e2eafbafcfafd9d9d5abebca328259bea4", "sha256_in_prefix": "8f3306386bc73ba5ef4be20ef214b4e2eafbafcfafd9d9d5abebca328259bea4", "size_in_bytes": 1510}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/pointPen.py", "path_type": "hardlink", "sha256": "0ae444726dc862d7993410c077f651015e17a81b32d2cd3b8dd59ce1e9fdafef", "sha256_in_prefix": "0ae444726dc862d7993410c077f651015e17a81b32d2cd3b8dd59ce1e9fdafef", "size_in_bytes": 244}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/utils.py", "path_type": "hardlink", "sha256": "9d9a0926a1d74122fe2d7604e7cfd61c0f7b5e56d39046d891d1f718bdf64a64", "sha256_in_prefix": "9d9a0926a1d74122fe2d7604e7cfd61c0f7b5e56d39046d891d1f718bdf64a64", "size_in_bytes": 3192}, {"_path": "lib/python3.11/site-packages/fontTools/ufoLib/validators.py", "path_type": "hardlink", "sha256": "31606a70b4e11b2629b2deb5428b6103f05295cea319584fbc589acbea686c26", "sha256_in_prefix": "31606a70b4e11b2629b2deb5428b6103f05295cea319584fbc589acbea686c26", "size_in_bytes": 32387}, {"_path": "lib/python3.11/site-packages/fontTools/unicode.py", "path_type": "hardlink", "sha256": "659ece3265af23257520bd64ea2a13cda440877b54be6ea0bcaed08056ce2076", "sha256_in_prefix": "659ece3265af23257520bd64ea2a13cda440877b54be6ea0bcaed08056ce2076", "size_in_bytes": 1237}, {"_path": "lib/python3.11/site-packages/fontTools/unicodedata/Blocks.py", "path_type": "hardlink", "sha256": "e812faeafaebe545b2955c776e2732e94da43be41c34d2a998f2904c68205045", "sha256_in_prefix": "e812faeafaebe545b2955c776e2732e94da43be41c34d2a998f2904c68205045", "size_in_bytes": 32415}, {"_path": "lib/python3.11/site-packages/fontTools/unicodedata/Mirrored.py", "path_type": "hardlink", "sha256": "91d870096396680ae67cd9036a1d1386ffbaeccf705b3e3947920c3e1ab2cc53", "sha256_in_prefix": "91d870096396680ae67cd9036a1d1386ffbaeccf705b3e3947920c3e1ab2cc53", "size_in_bytes": 9242}, {"_path": "lib/python3.11/site-packages/fontTools/unicodedata/OTTags.py", "path_type": "hardlink", "sha256": "c0e3e96ccb0d729fe076f3c57884ed8153274cdf13252340b1511dbbf9ee3d71", "sha256_in_prefix": "c0e3e96ccb0d729fe076f3c57884ed8153274cdf13252340b1511dbbf9ee3d71", "size_in_bytes": 1196}, {"_path": "lib/python3.11/site-packages/fontTools/unicodedata/ScriptExtensions.py", "path_type": "hardlink", "sha256": "61366bd9b3ad7878b3ffb238d74f0f472d08b38905a1ffb7db216ea0a8c51e37", "sha256_in_prefix": "61366bd9b3ad7878b3ffb238d74f0f472d08b38905a1ffb7db216ea0a8c51e37", "size_in_bytes": 28207}, {"_path": "lib/python3.11/site-packages/fontTools/unicodedata/Scripts.py", "path_type": "hardlink", "sha256": "2349d8d3ca2fb19e291d4e7061c8636adf438e7c5ca684f3697027e6815a28d2", "sha256_in_prefix": "2349d8d3ca2fb19e291d4e7061c8636adf438e7c5ca684f3697027e6815a28d2", "size_in_bytes": 130271}, {"_path": "lib/python3.11/site-packages/fontTools/unicodedata/__init__.py", "path_type": "hardlink", "sha256": "86de5c230be0292a27bd1003ce28d7cc1a7ab998e14ef1686c1c1a6dae2881a3", "sha256_in_prefix": "86de5c230be0292a27bd1003ce28d7cc1a7ab998e14ef1686c1c1a6dae2881a3", "size_in_bytes": 9033}, {"_path": "lib/python3.11/site-packages/fontTools/unicodedata/__pycache__/Blocks.cpython-311.pyc", "path_type": "hardlink", "sha256": "502b6ff813ec4f2285d01ee47084d13a3403269e47a95531056ae95f4a73c0f8", "sha256_in_prefix": "502b6ff813ec4f2285d01ee47084d13a3403269e47a95531056ae95f4a73c0f8", "size_in_bytes": 8438}, {"_path": "lib/python3.11/site-packages/fontTools/unicodedata/__pycache__/Mirrored.cpython-311.pyc", "path_type": "hardlink", "sha256": "4e54b42b460dd83b0c315180a4103b041e1b4417faba678ab3d6bfc3281f9b75", "sha256_in_prefix": "4e54b42b460dd83b0c315180a4103b041e1b4417faba678ab3d6bfc3281f9b75", "size_in_bytes": 12316}, {"_path": "lib/python3.11/site-packages/fontTools/unicodedata/__pycache__/OTTags.cpython-311.pyc", "path_type": "hardlink", "sha256": "f2a24f749b9103e08741a0e229ac3140778aaf3347654d9db0b109356c1ebe97", "sha256_in_prefix": "f2a24f749b9103e08741a0e229ac3140778aaf3347654d9db0b109356c1ebe97", "size_in_bytes": 1060}, {"_path": "lib/python3.11/site-packages/fontTools/unicodedata/__pycache__/ScriptExtensions.cpython-311.pyc", "path_type": "hardlink", "sha256": "c2e820e86d68b2acff222257fc1c161b8bf58de92f5121acc0881facba66a491", "sha256_in_prefix": "c2e820e86d68b2acff222257fc1c161b8bf58de92f5121acc0881facba66a491", "size_in_bytes": 10694}, {"_path": "lib/python3.11/site-packages/fontTools/unicodedata/__pycache__/Scripts.cpython-311.pyc", "path_type": "hardlink", "sha256": "e51ea4bee06c6385a4c8413357d5db8e32df5cc4d37fde71406915d69eb54776", "sha256_in_prefix": "e51ea4bee06c6385a4c8413357d5db8e32df5cc4d37fde71406915d69eb54776", "size_in_bytes": 24019}, {"_path": "lib/python3.11/site-packages/fontTools/unicodedata/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "4403120b180b6f43022f8574b182a8f2016f6dd5e108a6020b24b7940e6f0d33", "sha256_in_prefix": "4403120b180b6f43022f8574b182a8f2016f6dd5e108a6020b24b7940e6f0d33", "size_in_bytes": 9364}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__init__.py", "path_type": "hardlink", "sha256": "49e0154a8974aaf9b0e08a3986fb955b1c31fe89ca69daabff5797c233541a49", "sha256_in_prefix": "49e0154a8974aaf9b0e08a3986fb955b1c31fe89ca69daabff5797c233541a49", "size_in_bytes": 57322}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__main__.py", "path_type": "hardlink", "sha256": "c1b7580b96cf8d60b103423848a70b3bcf2097e50cb6c612f0cc5d5bdc924e46", "sha256_in_prefix": "c1b7580b96cf8d60b103423848a70b3bcf2097e50cb6c612f0cc5d5bdc924e46", "size_in_bytes": 95}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "548fbdcf2f9cffb0696f62755adb5b39ee3370123bcb610f6aef6848bf70d6b3", "sha256_in_prefix": "548fbdcf2f9cffb0696f62755adb5b39ee3370123bcb610f6aef6848bf70d6b3", "size_in_bytes": 76017}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "65f5c76a9a6f35c615560b0d51cbdad8d664b9d70ed976daa8f9e4372a6ff86d", "sha256_in_prefix": "65f5c76a9a6f35c615560b0d51cbdad8d664b9d70ed976daa8f9e4372a6ff86d", "size_in_bytes": 382}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/avarPlanner.cpython-311.pyc", "path_type": "hardlink", "sha256": "a778da2abe777800d7e3aec51e9bf0551d73162122974b473bd377714a1de0c7", "sha256_in_prefix": "a778da2abe777800d7e3aec51e9bf0551d73162122974b473bd377714a1de0c7", "size_in_bytes": 470}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/builder.cpython-311.pyc", "path_type": "hardlink", "sha256": "218c916ca762f081a8731ca9ec59693c5c16efd0ddf6972ff34e62390d3f453e", "sha256_in_prefix": "218c916ca762f081a8731ca9ec59693c5c16efd0ddf6972ff34e62390d3f453e", "size_in_bytes": 13038}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/cff.cpython-311.pyc", "path_type": "hardlink", "sha256": "8b8949dbe37a0ef12099af87fbd9ff3e8cf9116aa49682d0416e72daa3d3a42c", "sha256_in_prefix": "8b8949dbe37a0ef12099af87fbd9ff3e8cf9116aa49682d0416e72daa3d3a42c", "size_in_bytes": 29084}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/errors.cpython-311.pyc", "path_type": "hardlink", "sha256": "763f97dd77a3a8725883006169084e4b8b3b97149dcd96289e15c2d449648c2e", "sha256_in_prefix": "763f97dd77a3a8725883006169084e4b8b3b97149dcd96289e15c2d449648c2e", "size_in_bytes": 14282}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/featureVars.cpython-311.pyc", "path_type": "hardlink", "sha256": "550b9e4d765ab39862cd5945dd765951f92930745739508abc896c8a1704a4c0", "sha256_in_prefix": "550b9e4d765ab39862cd5945dd765951f92930745739508abc896c8a1704a4c0", "size_in_bytes": 30893}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/hvar.cpython-311.pyc", "path_type": "hardlink", "sha256": "6654ce00f62f3c4d306fb2621115c6d3e0a0015b545d3bbb3877a2bb7a464caf", "sha256_in_prefix": "6654ce00f62f3c4d306fb2621115c6d3e0a0015b545d3bbb3877a2bb7a464caf", "size_in_bytes": 5336}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/interpolatable.cpython-311.pyc", "path_type": "hardlink", "sha256": "ed12f407051cee51890ece792eda78ce52532d437c5db30832eeac1181949397", "sha256_in_prefix": "ed12f407051cee51890ece792eda78ce52532d437c5db30832eeac1181949397", "size_in_bytes": 48361}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/interpolatableHelpers.cpython-311.pyc", "path_type": "hardlink", "sha256": "ed38e3d72902dd3efd175725941d45e1cdd199775863569cf67a386feec9c67b", "sha256_in_prefix": "ed38e3d72902dd3efd175725941d45e1cdd199775863569cf67a386feec9c67b", "size_in_bytes": 22065}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/interpolatablePlot.cpython-311.pyc", "path_type": "hardlink", "sha256": "168c1fea3a347f4c6f2e2ede73ff81d1524b2677031ec7297d2021b1476af365", "sha256_in_prefix": "168c1fea3a347f4c6f2e2ede73ff81d1524b2677031ec7297d2021b1476af365", "size_in_bytes": 56152}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/interpolatableTestContourOrder.cpython-311.pyc", "path_type": "hardlink", "sha256": "c2787067c5344b12f7e60aee207a21eb5d74a2cabf377adf20dddd9b3aa6bfe0", "sha256_in_prefix": "c2787067c5344b12f7e60aee207a21eb5d74a2cabf377adf20dddd9b3aa6bfe0", "size_in_bytes": 2480}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/interpolatableTestStartingPoint.cpython-311.pyc", "path_type": "hardlink", "sha256": "1362c35e39de7999a25f25cbf98689e5dbb62beb4093560aae12dd552c22c5d9", "sha256_in_prefix": "1362c35e39de7999a25f25cbf98689e5dbb62beb4093560aae12dd552c22c5d9", "size_in_bytes": 5081}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/interpolate_layout.cpython-311.pyc", "path_type": "hardlink", "sha256": "f740ec7d861f61ae605b0b87d5b0ab79ffa269767929fd146b5619da87cff41b", "sha256_in_prefix": "f740ec7d861f61ae605b0b87d5b0ab79ffa269767929fd146b5619da87cff41b", "size_in_bytes": 6390}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/iup.cpython-311.pyc", "path_type": "hardlink", "sha256": "ea7ec143f25f1aadac8c2d391de7ce7dad272f2c3c1712a282d06e39c79bfe31", "sha256_in_prefix": "ea7ec143f25f1aadac8c2d391de7ce7dad272f2c3c1712a282d06e39c79bfe31", "size_in_bytes": 17533}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/merger.cpython-311.pyc", "path_type": "hardlink", "sha256": "e621c7368da58b10097c55929601f2e8f7f8c827c3126eb41f30ddda3e53040c", "sha256_in_prefix": "e621c7368da58b10097c55929601f2e8f7f8c827c3126eb41f30ddda3e53040c", "size_in_bytes": 96549}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/models.cpython-311.pyc", "path_type": "hardlink", "sha256": "3eefe75e1bcc0e09338facea59a12584da31fb1ce4f26fbb017729af4b329a64", "sha256_in_prefix": "3eefe75e1bcc0e09338facea59a12584da31fb1ce4f26fbb017729af4b329a64", "size_in_bytes": 35639}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/multiVarStore.cpython-311.pyc", "path_type": "hardlink", "sha256": "92f5a1a39155c23dddfa330c9e4a0e8281adeebe7381bf34579dc09376c54f50", "sha256_in_prefix": "92f5a1a39155c23dddfa330c9e4a0e8281adeebe7381bf34579dc09376c54f50", "size_in_bytes": 15305}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/mutator.cpython-311.pyc", "path_type": "hardlink", "sha256": "5b64217eb24c82a3767d8bd316f86b6d7ac1b721de77d5ad9e49d679f835d721", "sha256_in_prefix": "5b64217eb24c82a3767d8bd316f86b6d7ac1b721de77d5ad9e49d679f835d721", "size_in_bytes": 25095}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/mvar.cpython-311.pyc", "path_type": "hardlink", "sha256": "30572b09e7a927fee50385264c3af4b902f2252cefec3894cab42e6809f89dd6", "sha256_in_prefix": "30572b09e7a927fee50385264c3af4b902f2252cefec3894cab42e6809f89dd6", "size_in_bytes": 1351}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/plot.cpython-311.pyc", "path_type": "hardlink", "sha256": "d399b3b5bc1b8b4268d4219e917884a9752dd72ca2236023e60eabfa82d442ff", "sha256_in_prefix": "d399b3b5bc1b8b4268d4219e917884a9752dd72ca2236023e60eabfa82d442ff", "size_in_bytes": 16550}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/stat.cpython-311.pyc", "path_type": "hardlink", "sha256": "88ddc83dc858f5ccf74ca0d60d2973b27319bf061c71bf40943a2a7ec10ef893", "sha256_in_prefix": "88ddc83dc858f5ccf74ca0d60d2973b27319bf061c71bf40943a2a7ec10ef893", "size_in_bytes": 7640}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/__pycache__/varStore.cpython-311.pyc", "path_type": "hardlink", "sha256": "dd75f55d66b351986fbe1815c05a228a93ab306c2de0f15f010e610252db5593", "sha256_in_prefix": "dd75f55d66b351986fbe1815c05a228a93ab306c2de0f15f010e610252db5593", "size_in_bytes": 33497}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/avar/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/avar/__main__.py", "path_type": "hardlink", "sha256": "7b0d5f26983cd46a586dd92ba7823b9ed59d6e19037a0e5f367d52ae9176e789", "sha256_in_prefix": "7b0d5f26983cd46a586dd92ba7823b9ed59d6e19037a0e5f367d52ae9176e789", "size_in_bytes": 1770}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/avar/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "b04010edf2570322b221c3aa974b7e1ed15c8a4d61baa1b6424cfe9234e61abb", "sha256_in_prefix": "b04010edf2570322b221c3aa974b7e1ed15c8a4d61baa1b6424cfe9234e61abb", "size_in_bytes": 171}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/avar/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "4497046fc5537050d19f1c3f5c1b980f40b34239ce1000098fc43dd4b21b85bb", "sha256_in_prefix": "4497046fc5537050d19f1c3f5c1b980f40b34239ce1000098fc43dd4b21b85bb", "size_in_bytes": 2817}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/avar/__pycache__/build.cpython-311.pyc", "path_type": "hardlink", "sha256": "b61f7497786126f6d3a3f4dc8d7640d7304308e36ebc3c685b7df6223bcdc69f", "sha256_in_prefix": "b61f7497786126f6d3a3f4dc8d7640d7304308e36ebc3c685b7df6223bcdc69f", "size_in_bytes": 3525}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/avar/__pycache__/map.cpython-311.pyc", "path_type": "hardlink", "sha256": "3f05777494f7bb72afc48e3292f1307ce9704984dff34fc0692abcb2a55e562e", "sha256_in_prefix": "3f05777494f7bb72afc48e3292f1307ce9704984dff34fc0692abcb2a55e562e", "size_in_bytes": 5227}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/avar/__pycache__/plan.cpython-311.pyc", "path_type": "hardlink", "sha256": "2a3bfeb9f3c1cef5c869f0795ce62dfbf0122a930c136fbe549311512db321ac", "sha256_in_prefix": "2a3bfeb9f3c1cef5c869f0795ce62dfbf0122a930c136fbe549311512db321ac", "size_in_bytes": 33655}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/avar/__pycache__/unbuild.cpython-311.pyc", "path_type": "hardlink", "sha256": "3f04ff39b6aaab17e2eb41040982d6baffab681871ba511ce3ddd74dfc50446f", "sha256_in_prefix": "3f04ff39b6aaab17e2eb41040982d6baffab681871ba511ce3ddd74dfc50446f", "size_in_bytes": 15417}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/avar/build.py", "path_type": "hardlink", "sha256": "61232c44c8c690d5b6ce12a7dbd1e0a2f34d1be8e08854ac2bbcd81f8ba7d96b", "sha256_in_prefix": "61232c44c8c690d5b6ce12a7dbd1e0a2f34d1be8e08854ac2bbcd81f8ba7d96b", "size_in_bytes": 2087}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/avar/map.py", "path_type": "hardlink", "sha256": "501784953e344875ef26b43b49f947e4c3582934e89bb44b1f0ab6bdaadbfe81", "sha256_in_prefix": "501784953e344875ef26b43b49f947e4c3582934e89bb44b1f0ab6bdaadbfe81", "size_in_bytes": 2867}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/avar/plan.py", "path_type": "hardlink", "sha256": "a832998d0aba39f79706bdd3dc3667ae66aec8c9b97a4d2b9b9615a2c00efd40", "sha256_in_prefix": "a832998d0aba39f79706bdd3dc3667ae66aec8c9b97a4d2b9b9615a2c00efd40", "size_in_bytes": 27354}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/avar/unbuild.py", "path_type": "hardlink", "sha256": "877a9bd0904ded29e08a33ebc234e31848ee79e10d9613d863d172374d852c49", "sha256_in_prefix": "877a9bd0904ded29e08a33ebc234e31848ee79e10d9613d863d172374d852c49", "size_in_bytes": 10467}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/avarPlanner.py", "path_type": "hardlink", "sha256": "09a6c6e71079ec531dc2637569ca5532fc8eb9c54fc4275174948ada0bbb81be", "sha256_in_prefix": "09a6c6e71079ec531dc2637569ca5532fc8eb9c54fc4275174948ada0bbb81be", "size_in_bytes": 109}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/builder.py", "path_type": "hardlink", "sha256": "99228e09c9e7c3e5b3999b35e456b2a2a08387bed3b3ba3d4eb7bda6c87c0947", "sha256_in_prefix": "99228e09c9e7c3e5b3999b35e456b2a2a08387bed3b3ba3d4eb7bda6c87c0947", "size_in_bytes": 6609}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/cff.py", "path_type": "hardlink", "sha256": "11581a41ca11388ad842c46e7ed9f116e18695d10f61bac8879c81724ca5242e", "sha256_in_prefix": "11581a41ca11388ad842c46e7ed9f116e18695d10f61bac8879c81724ca5242e", "size_in_bytes": 22901}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/errors.py", "path_type": "hardlink", "sha256": "74ca3c7868fbe88ec7e21ac112235b62b1acd89d4ad52c1db14c931e99153ab4", "sha256_in_prefix": "74ca3c7868fbe88ec7e21ac112235b62b1acd89d4ad52c1db14c931e99153ab4", "size_in_bytes": 6934}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/featureVars.py", "path_type": "hardlink", "sha256": "929e203e32b2c8646eef2056c607753783a42a753d578e50c225921ece8e59dd", "sha256_in_prefix": "929e203e32b2c8646eef2056c607753783a42a753d578e50c225921ece8e59dd", "size_in_bytes": 26180}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/hvar.py", "path_type": "hardlink", "sha256": "d48bcbe419de4e483c8c9622707d1349056207a0f4bc111eb0b7657eaa0b057e", "sha256_in_prefix": "d48bcbe419de4e483c8c9622707d1349056207a0f4bc111eb0b7657eaa0b057e", "size_in_bytes": 3695}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/instancer/__init__.py", "path_type": "hardlink", "sha256": "7c777ceb859f103c851f7cd71a5753df86b0df9db8d78ddd64f203a80f795790", "sha256_in_prefix": "7c777ceb859f103c851f7cd71a5753df86b0df9db8d78ddd64f203a80f795790", "size_in_bytes": 75554}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/instancer/__main__.py", "path_type": "hardlink", "sha256": "cdf50bc1c3f4d45869952d4895c3203509cbc64e5155f98eba29d68ea7a277e8", "sha256_in_prefix": "cdf50bc1c3f4d45869952d4895c3203509cbc64e5155f98eba29d68ea7a277e8", "size_in_bytes": 104}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/instancer/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "7b04a56aa71ab06be814097880f22021a6dc2e525d809f6f3b278ba84b3aaede", "sha256_in_prefix": "7b04a56aa71ab06be814097880f22021a6dc2e525d809f6f3b278ba84b3aaede", "size_in_bytes": 94580}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/instancer/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "7fa74af09117872c99a7bc16823d8e177d163947af2d01abbc7884a3e7aa7146", "sha256_in_prefix": "7fa74af09117872c99a7bc16823d8e177d163947af2d01abbc7884a3e7aa7146", "size_in_bytes": 400}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/instancer/__pycache__/featureVars.cpython-311.pyc", "path_type": "hardlink", "sha256": "30ca8bf4cdc59ad0a5c572ea7806b158d2c9dd006ca2e9763d190912d286b3e1", "sha256_in_prefix": "30ca8bf4cdc59ad0a5c572ea7806b158d2c9dd006ca2e9763d190912d286b3e1", "size_in_bytes": 7440}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/instancer/__pycache__/names.cpython-311.pyc", "path_type": "hardlink", "sha256": "adf1231cbe5ebccb5d5dd75c4131937d978da227d40c1586ca90545bd878bdd5", "sha256_in_prefix": "adf1231cbe5ebccb5d5dd75c4131937d978da227d40c1586ca90545bd878bdd5", "size_in_bytes": 20201}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/instancer/__pycache__/solver.cpython-311.pyc", "path_type": "hardlink", "sha256": "893c5747dda1590cca42296d13d2711f932708c2681dd61d93481554529e1fe1", "sha256_in_prefix": "893c5747dda1590cca42296d13d2711f932708c2681dd61d93481554529e1fe1", "size_in_bytes": 5641}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/instancer/featureVars.py", "path_type": "hardlink", "sha256": "a0fa929671cb3034ed3ac990322ea09332e8c7bca60abaa544092f0bf109e1b7", "sha256_in_prefix": "a0fa929671cb3034ed3ac990322ea09332e8c7bca60abaa544092f0bf109e1b7", "size_in_bytes": 7110}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/instancer/names.py", "path_type": "hardlink", "sha256": "20f46a7a5fccf33554d23977d16b1f82e7f1526f4f0414034026375476a97877", "sha256_in_prefix": "20f46a7a5fccf33554d23977d16b1f82e7f1526f4f0414034026375476a97877", "size_in_bytes": 14950}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/instancer/solver.py", "path_type": "hardlink", "sha256": "b8c78fc17d01553e45f78914bc3825b08e3f1749c41faec5ed116e27ab50c10d", "sha256_in_prefix": "b8c78fc17d01553e45f78914bc3825b08e3f1749c41faec5ed116e27ab50c10d", "size_in_bytes": 11002}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/interpolatable.py", "path_type": "hardlink", "sha256": "06196afcb84467eb177cb358f1a1440a116bb0ab93da4ce69ee31f1b9aa2d2fe", "sha256_in_prefix": "06196afcb84467eb177cb358f1a1440a116bb0ab93da4ce69ee31f1b9aa2d2fe", "size_in_bytes": 45221}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/interpolatableHelpers.py", "path_type": "hardlink", "sha256": "7131604ea0e3820482a8d7d333beffff96fd4a36bf83bc5658c881fa95c3c7ce", "sha256_in_prefix": "7131604ea0e3820482a8d7d333beffff96fd4a36bf83bc5658c881fa95c3c7ce", "size_in_bytes": 11672}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/interpolatablePlot.py", "path_type": "hardlink", "sha256": "c37f773fa9862d18589088d2c4cc30deaca86310146730979663c16c8ffce02d", "sha256_in_prefix": "c37f773fa9862d18589088d2c4cc30deaca86310146730979663c16c8ffce02d", "size_in_bytes": 44375}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/interpolatableTestContourOrder.py", "path_type": "hardlink", "sha256": "98727d472ed19bb1f7cc70f0114404b44203b1e894ccec6383832f796fc54a25", "sha256_in_prefix": "98727d472ed19bb1f7cc70f0114404b44203b1e894ccec6383832f796fc54a25", "size_in_bytes": 3021}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/interpolatableTestStartingPoint.py", "path_type": "hardlink", "sha256": "2ba398281b298a6e815dcf75a5f2d36c62f28b95d9ba47ccb8173a851a44346f", "sha256_in_prefix": "2ba398281b298a6e815dcf75a5f2d36c62f28b95d9ba47ccb8173a851a44346f", "size_in_bytes": 4296}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/interpolate_layout.py", "path_type": "hardlink", "sha256": "db6563199b95d988807b63297537f4c4f573d71d86f386dc38bd2f39e0691903", "sha256_in_prefix": "db6563199b95d988807b63297537f4c4f573d71d86f386dc38bd2f39e0691903", "size_in_bytes": 3689}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/iup.c", "path_type": "hardlink", "sha256": "23bb27ac00a2ff09686432faec5ca993eec37dfe69c4985a56696e0170545bfe", "sha256_in_prefix": "23bb27ac00a2ff09686432faec5ca993eec37dfe69c4985a56696e0170545bfe", "size_in_bytes": 827723}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/iup.cpython-311-darwin.so", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/fonttools_1758132623042/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold", "sha256": "8c61db26f41d28604233f16174011f04a25e69c0e365475cc3df3e6f3b0d2d07", "sha256_in_prefix": "9a37b5062b3e1b59b5a1673da843fa979003d603c3a60528dfe0b8587e134357", "size_in_bytes": 182144}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/iup.py", "path_type": "hardlink", "sha256": "98aabf1915ae520e324e6c36577da7bb4bf6afe4b3ccdef14bbac86d5d2662e7", "sha256_in_prefix": "98aabf1915ae520e324e6c36577da7bb4bf6afe4b3ccdef14bbac86d5d2662e7", "size_in_bytes": 14984}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/merger.py", "path_type": "hardlink", "sha256": "139f68962e00c2a599f85827b92b4c481bec07e1477bee797ac5d3614a86789f", "sha256_in_prefix": "139f68962e00c2a599f85827b92b4c481bec07e1477bee797ac5d3614a86789f", "size_in_bytes": 60802}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/models.py", "path_type": "hardlink", "sha256": "b23fc43658e1fea70c6df6334483a54601eaead14e98bd365afe963bcba87e2b", "sha256_in_prefix": "b23fc43658e1fea70c6df6334483a54601eaead14e98bd365afe963bcba87e2b", "size_in_bytes": 22398}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/multiVarStore.py", "path_type": "hardlink", "sha256": "79012e58d634d58179cc3a72d54c0db6f398c83e9cd052f1a47f901695f58bbf", "sha256_in_prefix": "79012e58d634d58179cc3a72d54c0db6f398c83e9cd052f1a47f901695f58bbf", "size_in_bytes": 8305}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/mutator.py", "path_type": "hardlink", "sha256": "9335e22c5c512e0536a5c1f33b387dbb49f42a43b70ee064d3ac59fd13e15b3f", "sha256_in_prefix": "9335e22c5c512e0536a5c1f33b387dbb49f42a43b70ee064d3ac59fd13e15b3f", "size_in_bytes": 19804}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/mvar.py", "path_type": "hardlink", "sha256": "2d357beef1ffdc4720fea2813b9c50ce32ce9498abfe9a44afb98f55946069df", "sha256_in_prefix": "2d357beef1ffdc4720fea2813b9c50ce32ce9498abfe9a44afb98f55946069df", "size_in_bytes": 2449}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/plot.py", "path_type": "hardlink", "sha256": "368499909e6777135c0ef248bdde6943dfe35fa5f5a0cd4ad86fed478b1d3d5b", "sha256_in_prefix": "368499909e6777135c0ef248bdde6943dfe35fa5f5a0cd4ad86fed478b1d3d5b", "size_in_bytes": 7494}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/stat.py", "path_type": "hardlink", "sha256": "5ee34a299c46941ae5e0e1850c0c155e1a41c098b6dd4dc174798d4caa099ef1", "sha256_in_prefix": "5ee34a299c46941ae5e0e1850c0c155e1a41c098b6dd4dc174798d4caa099ef1", "size_in_bytes": 4811}, {"_path": "lib/python3.11/site-packages/fontTools/varLib/varStore.py", "path_type": "hardlink", "sha256": "d9003d48323a8d0c90ff3abcd8e3b06b714191f97e92c692a352869951696bd4", "sha256_in_prefix": "d9003d48323a8d0c90ff3abcd8e3b06b714191f97e92c692a352869951696bd4", "size_in_bytes": 24069}, {"_path": "lib/python3.11/site-packages/fontTools/voltLib/__init__.py", "path_type": "hardlink", "sha256": "659d40b13c755650e7e342aea5c7be7ccde678e5ae832dd166b6815bd2c6fbd3", "sha256_in_prefix": "659d40b13c755650e7e342aea5c7be7ccde678e5ae832dd166b6815bd2c6fbd3", "size_in_bytes": 151}, {"_path": "lib/python3.11/site-packages/fontTools/voltLib/__main__.py", "path_type": "hardlink", "sha256": "b95b4004bccc787b6f28bf337ad7f8ae90b8681f01918af940b49e80d8d96592", "sha256_in_prefix": "b95b4004bccc787b6f28bf337ad7f8ae90b8681f01918af940b49e80d8d96592", "size_in_bytes": 5928}, {"_path": "lib/python3.11/site-packages/fontTools/voltLib/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "c395e983934f1a4543e15e0df7d596f3d71e9d6894826904e06f80db0366b507", "sha256_in_prefix": "c395e983934f1a4543e15e0df7d596f3d71e9d6894826904e06f80db0366b507", "size_in_bytes": 278}, {"_path": "lib/python3.11/site-packages/fontTools/voltLib/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "1956ed7e4cb53cb041f549cb190a00ebed0a0c44b4718db5b9352211359c2cbb", "sha256_in_prefix": "1956ed7e4cb53cb041f549cb190a00ebed0a0c44b4718db5b9352211359c2cbb", "size_in_bytes": 8956}, {"_path": "lib/python3.11/site-packages/fontTools/voltLib/__pycache__/ast.cpython-311.pyc", "path_type": "hardlink", "sha256": "e9d24eefe63553e56a4bcd9b752a6c6923af8d18c247f6818e4d5f8b1b3f1ed2", "sha256_in_prefix": "e9d24eefe63553e56a4bcd9b752a6c6923af8d18c247f6818e4d5f8b1b3f1ed2", "size_in_bytes": 29587}, {"_path": "lib/python3.11/site-packages/fontTools/voltLib/__pycache__/error.cpython-311.pyc", "path_type": "hardlink", "sha256": "a5443b8bcd254f721e6701f477d7ab1a27576d2c8c72c07ddae72449d09210cc", "sha256_in_prefix": "a5443b8bcd254f721e6701f477d7ab1a27576d2c8c72c07ddae72449d09210cc", "size_in_bytes": 1034}, {"_path": "lib/python3.11/site-packages/fontTools/voltLib/__pycache__/lexer.cpython-311.pyc", "path_type": "hardlink", "sha256": "1bde1688037b84bd0108d86840092dc6e82d7df351bee490e1e6e111af30b401", "sha256_in_prefix": "1bde1688037b84bd0108d86840092dc6e82d7df351bee490e1e6e111af30b401", "size_in_bytes": 5503}, {"_path": "lib/python3.11/site-packages/fontTools/voltLib/__pycache__/parser.cpython-311.pyc", "path_type": "hardlink", "sha256": "ab3f06e1da73a85f5ecd5c445e8d89f73d3109af637c81f33abdea03c4794c9e", "sha256_in_prefix": "ab3f06e1da73a85f5ecd5c445e8d89f73d3109af637c81f33abdea03c4794c9e", "size_in_bytes": 37438}, {"_path": "lib/python3.11/site-packages/fontTools/voltLib/__pycache__/voltToFea.cpython-311.pyc", "path_type": "hardlink", "sha256": "45241ea84876627dc887b2c89b85d00dede461b477c2dbb9bc844b80f17b3997", "sha256_in_prefix": "45241ea84876627dc887b2c89b85d00dede461b477c2dbb9bc844b80f17b3997", "size_in_bytes": 50020}, {"_path": "lib/python3.11/site-packages/fontTools/voltLib/ast.py", "path_type": "hardlink", "sha256": "6ab03d5b71aaa3a3aa963c0d34a9cc0288f3f82e4b4ad6c2e52823261edbb8a9", "sha256_in_prefix": "6ab03d5b71aaa3a3aa963c0d34a9cc0288f3f82e4b4ad6c2e52823261edbb8a9", "size_in_bytes": 13300}, {"_path": "lib/python3.11/site-packages/fontTools/voltLib/error.py", "path_type": "hardlink", "sha256": "a617103908fec4eb29097bbd84125046148e043cf11d18197777d640e14d273c", "sha256_in_prefix": "a617103908fec4eb29097bbd84125046148e043cf11d18197777d640e14d273c", "size_in_bytes": 395}, {"_path": "lib/python3.11/site-packages/fontTools/voltLib/lexer.py", "path_type": "hardlink", "sha256": "3afb844ce4af952eafee2095789dc8747d8283bd67dce268132881dfe87abe11", "sha256_in_prefix": "3afb844ce4af952eafee2095789dc8747d8283bd67dce268132881dfe87abe11", "size_in_bytes": 3368}, {"_path": "lib/python3.11/site-packages/fontTools/voltLib/parser.py", "path_type": "hardlink", "sha256": "ae4c362070593ecae11950bb2b0ed5e74d66d2ee76921d4948ce475e9fb17213", "sha256_in_prefix": "ae4c362070593ecae11950bb2b0ed5e74d66d2ee76921d4948ce475e9fb17213", "size_in_bytes": 25396}, {"_path": "lib/python3.11/site-packages/fontTools/voltLib/voltToFea.py", "path_type": "hardlink", "sha256": "676caf9da64b417ccf2d3f3a52d6b4cd1b17218823e8d9ef66d496b79c66c36b", "sha256_in_prefix": "676caf9da64b417ccf2d3f3a52d6b4cd1b17218823e8d9ef66d496b79c66c36b", "size_in_bytes": 36549}, {"_path": "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "46be1299847a19a5e841ce6f740927a08d53ea329b158392beaf2203f567c6b4", "sha256_in_prefix": "46be1299847a19a5e841ce6f740927a08d53ea329b158392beaf2203f567c6b4", "size_in_bytes": 111615}, {"_path": "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "f60d66fa7937968b6f336ddbc589df7b813823e38c364a3c76b908561f916f11", "sha256_in_prefix": "f60d66fa7937968b6f336ddbc589df7b813823e38c364a3c76b908561f916f11", "size_in_bytes": 51520}, {"_path": "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "9a99a83f4370ced80864e3ca8f3c6d5e12203e9375332d371b67792389f5359e", "sha256_in_prefix": "9a99a83f4370ced80864e3ca8f3c6d5e12203e9375332d371b67792389f5359e", "size_in_bytes": 111}, {"_path": "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "b5c15578f6021c834db27a33f9d7dfa33a0635b2a54f71e3cb5fcbcfea7e8f7c", "sha256_in_prefix": "b5c15578f6021c834db27a33f9d7dfa33a0635b2a54f71e3cb5fcbcfea7e8f7c", "size_in_bytes": 97}, {"_path": "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "f2454775dc5f156038e05483e26069982fb8b82ca7427928cfff5a3496f6dbb6", "sha256_in_prefix": "f2454775dc5f156038e05483e26069982fb8b82ca7427928cfff5a3496f6dbb6", "size_in_bytes": 147}, {"_path": "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "6787208f83f659ccbc2223b2fde952ffa6f7e8aca62f1a8a2bf5bc51bb1b2383", "sha256_in_prefix": "6787208f83f659ccbc2223b2fde952ffa6f7e8aca62f1a8a2bf5bc51bb1b2383", "size_in_bytes": 1072}, {"_path": "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/licenses/LICENSE.external", "path_type": "hardlink", "sha256": "94a83aaee0729a0f302d34acc4acecbd9d58366f262429075fe557e4a54b2e69", "sha256_in_prefix": "94a83aaee0729a0f302d34acc4acecbd9d58366f262429075fe557e4a54b2e69", "size_in_bytes": 20022}, {"_path": "lib/python3.11/site-packages/fonttools-4.60.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "ad1811ca5ad7cde92a58eb2b87283389bd76a50ed620b7fb5068ea1309081433", "sha256_in_prefix": "ad1811ca5ad7cde92a58eb2b87283389bd76a50ed620b7fb5068ea1309081433", "size_in_bytes": 10}, {"_path": "share/man/man1/ttx.1", "path_type": "hardlink", "sha256": "70b6e6fe938e8f50bbe93d905ef0f1cf00e3f6093e193779473b6f4ccb28b85c", "sha256_in_prefix": "70b6e6fe938e8f50bbe93d905ef0f1cf00e3f6093e193779473b6f4ccb28b85c", "size_in_bytes": 5377}], "paths_version": 1}, "requested_spec": "None", "sha256": "a0adeb0a808a838e0045466e55ec48613be6d453ccb513262bb06073185dcf1b", "size": 2851913, "subdir": "osx-64", "timestamp": 1758133016000, "url": "https://conda.anaconda.org/conda-forge/osx-64/fonttools-4.60.0-py311he13f9b5_0.conda", "version": "4.60.0"}