{"build": "h58003a5_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libuv-1.51.0-h58003a5_1", "files": ["include/uv.h", "include/uv/darwin.h", "include/uv/errno.h", "include/uv/threadpool.h", "include/uv/unix.h", "include/uv/version.h", "lib/libuv.1.dylib", "lib/libuv.a", "lib/libuv.dylib", "lib/pkgconfig/libuv.pc"], "fn": "libuv-1.51.0-h58003a5_1.conda", "license": "MIT", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libuv-1.51.0-h58003a5_1", "type": 1}, "md5": "fbfc6cf607ae1e1e498734e256561dc3", "name": "libuv", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libuv-1.51.0-h58003a5_1.conda", "paths_data": {"paths": [{"_path": "include/uv.h", "path_type": "hardlink", "sha256": "fddec8c096616675b4578d85222663c8624dce3ee67f5be1c33ba83eb02a827c", "sha256_in_prefix": "fddec8c096616675b4578d85222663c8624dce3ee67f5be1c33ba83eb02a827c", "size_in_bytes": 72810}, {"_path": "include/uv/darwin.h", "path_type": "hardlink", "sha256": "222b6dd3ce67cfbb735b14b1662f065fc23570d6969acf463b39d946b7590d8c", "sha256_in_prefix": "222b6dd3ce67cfbb735b14b1662f065fc23570d6969acf463b39d946b7590d8c", "size_in_bytes": 3213}, {"_path": "include/uv/errno.h", "path_type": "hardlink", "sha256": "4242edf790e4569ef349026ab4c6e31093334ee5fc02b16ed7a0d9fbacdffd16", "sha256_in_prefix": "4242edf790e4569ef349026ab4c6e31093334ee5fc02b16ed7a0d9fbacdffd16", "size_in_bytes": 11216}, {"_path": "include/uv/threadpool.h", "path_type": "hardlink", "sha256": "09ae41099af710289155be012df45c2fce04da6a02e813278b4558935e645938", "sha256_in_prefix": "09ae41099af710289155be012df45c2fce04da6a02e813278b4558935e645938", "size_in_bytes": 1505}, {"_path": "include/uv/unix.h", "path_type": "hardlink", "sha256": "a246095a1476d76631f5ec85be171aee8107eb0423007546ec91c58887c28f02", "sha256_in_prefix": "a246095a1476d76631f5ec85be171aee8107eb0423007546ec91c58887c28f02", "size_in_bytes": 20057}, {"_path": "include/uv/version.h", "path_type": "hardlink", "sha256": "aef7fb55ffcece76db9cb7f87d0632e432369747f329679779616cd0721981da", "sha256_in_prefix": "aef7fb55ffcece76db9cb7f87d0632e432369747f329679779616cd0721981da", "size_in_bytes": 1831}, {"_path": "lib/libuv.1.dylib", "path_type": "hardlink", "sha256": "844af5dfe44bd5a1f4fdc4e272991dde97626da4435b3aed9006b24eb068a3d8", "sha256_in_prefix": "844af5dfe44bd5a1f4fdc4e272991dde97626da4435b3aed9006b24eb068a3d8", "size_in_bytes": 237648}, {"_path": "lib/libuv.a", "path_type": "hardlink", "sha256": "05441c6af55d1569fb1adebd59964fa327453c6d456dd6726f7dac019218886f", "sha256_in_prefix": "05441c6af55d1569fb1adebd59964fa327453c6d456dd6726f7dac019218886f", "size_in_bytes": 1228464}, {"_path": "lib/libuv.dylib", "path_type": "softlink", "sha256": "844af5dfe44bd5a1f4fdc4e272991dde97626da4435b3aed9006b24eb068a3d8", "size_in_bytes": 237648}, {"_path": "lib/pkgconfig/libuv.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/libuv_1753948166965/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_pla", "sha256": "b34ca398b8269c448dee4663a4eee1925ed58010396fe4710da62d3313cf50bc", "sha256_in_prefix": "452fd4ed7599f5ae26c296adae74d8b0680cae32ab6f1fb3373cd7167bc3fd09", "size_in_bytes": 558}], "paths_version": 1}, "requested_spec": "None", "sha256": "d90dd0eee6f195a5bd14edab4c5b33be3635b674b0b6c010fb942b956aa2254c", "size": 422612, "subdir": "osx-64", "timestamp": 1753948458000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libuv-1.51.0-h58003a5_1.conda", "version": "1.51.0"}