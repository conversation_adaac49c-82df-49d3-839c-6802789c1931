{"build": "ha90c15b_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["openssl >=3.1.1,<4.0a0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libevent-2.1.12-ha90c15b_1", "files": ["include/evdns.h", "include/event.h", "include/event2/buffer.h", "include/event2/buffer_compat.h", "include/event2/bufferevent.h", "include/event2/bufferevent_compat.h", "include/event2/bufferevent_ssl.h", "include/event2/bufferevent_struct.h", "include/event2/dns.h", "include/event2/dns_compat.h", "include/event2/dns_struct.h", "include/event2/event-config.h", "include/event2/event.h", "include/event2/event_compat.h", "include/event2/event_struct.h", "include/event2/http.h", "include/event2/http_compat.h", "include/event2/http_struct.h", "include/event2/keyvalq_struct.h", "include/event2/listener.h", "include/event2/rpc.h", "include/event2/rpc_compat.h", "include/event2/rpc_struct.h", "include/event2/tag.h", "include/event2/tag_compat.h", "include/event2/thread.h", "include/event2/util.h", "include/event2/visibility.h", "include/evhttp.h", "include/evrpc.h", "include/evutil.h", "lib/cmake/libevent/LibeventConfig.cmake", "lib/cmake/libevent/LibeventConfigVersion.cmake", "lib/cmake/libevent/LibeventTargets-shared-release.cmake", "lib/cmake/libevent/LibeventTargets-shared.cmake", "lib/libevent-2.1.7.dylib", "lib/libevent.dylib", "lib/libevent_core-2.1.7.dylib", "lib/libevent_core.dylib", "lib/libevent_extra-2.1.7.dylib", "lib/libevent_extra.dylib", "lib/libevent_openssl-2.1.7.dylib", "lib/libevent_openssl.dylib", "lib/libevent_pthreads-2.1.7.dylib", "lib/libevent_pthreads.dylib", "lib/pkgconfig/libevent.pc", "lib/pkgconfig/libevent_core.pc", "lib/pkgconfig/libevent_extra.pc", "lib/pkgconfig/libevent_openssl.pc", "lib/pkgconfig/libevent_pthreads.pc"], "fn": "libevent-2.1.12-ha90c15b_1.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libevent-2.1.12-ha90c15b_1", "type": 1}, "md5": "e38e467e577bd193a7d5de7c2c540b04", "name": "libevent", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libevent-2.1.12-ha90c15b_1.conda", "paths_data": {"paths": [{"_path": "include/evdns.h", "path_type": "hardlink", "sha256": "23a401ad32cfca6653e1529bb392c99f9eae5471d1d8126798b6a876da6f7c3c", "sha256_in_prefix": "23a401ad32cfca6653e1529bb392c99f9eae5471d1d8126798b6a876da6f7c3c", "size_in_bytes": 2019}, {"_path": "include/event.h", "path_type": "hardlink", "sha256": "6b6109226e90afb12183e08529b4e7feceb84b29643eb20519eb32411a3344e7", "sha256_in_prefix": "6b6109226e90afb12183e08529b4e7feceb84b29643eb20519eb32411a3344e7", "size_in_bytes": 2744}, {"_path": "include/event2/buffer.h", "path_type": "hardlink", "sha256": "ca0e01cd9996615a5a1f755e23371941703be4fb4439b25f8408184a06b08e4f", "sha256_in_prefix": "ca0e01cd9996615a5a1f755e23371941703be4fb4439b25f8408184a06b08e4f", "size_in_bytes": 39189}, {"_path": "include/event2/buffer_compat.h", "path_type": "hardlink", "sha256": "37f5a043b9c032f6917f7e1889bed07185eca6b3a3cb51e08caab99f9b30860a", "sha256_in_prefix": "37f5a043b9c032f6917f7e1889bed07185eca6b3a3cb51e08caab99f9b30860a", "size_in_bytes": 4741}, {"_path": "include/event2/bufferevent.h", "path_type": "hardlink", "sha256": "8f2094670cc294b29b947339741ed65787118359fae4aeb00ad38108836f64e6", "sha256_in_prefix": "8f2094670cc294b29b947339741ed65787118359fae4aeb00ad38108836f64e6", "size_in_bytes": 34683}, {"_path": "include/event2/bufferevent_compat.h", "path_type": "hardlink", "sha256": "87b760fbb0404ac0b6547827b14291725f3c77c8cb69b7d908395eaceb6eec06", "sha256_in_prefix": "87b760fbb0404ac0b6547827b14291725f3c77c8cb69b7d908395eaceb6eec06", "size_in_bytes": 4538}, {"_path": "include/event2/bufferevent_ssl.h", "path_type": "hardlink", "sha256": "9ca09315885b7acc1996634508bbfe3839e338c4fe09d0daa1baaf0312346950", "sha256_in_prefix": "9ca09315885b7acc1996634508bbfe3839e338c4fe09d0daa1baaf0312346950", "size_in_bytes": 4848}, {"_path": "include/event2/bufferevent_struct.h", "path_type": "hardlink", "sha256": "593efa39fd986b9fb1926764b0d51107a334777b8b82743d6e7c7c8bdf021d9f", "sha256_in_prefix": "593efa39fd986b9fb1926764b0d51107a334777b8b82743d6e7c7c8bdf021d9f", "size_in_bytes": 4135}, {"_path": "include/event2/dns.h", "path_type": "hardlink", "sha256": "bc40ab5ec25934e3a4ee8182f375f6c71f24b165e66ac8532f0079ed2c4c1133", "sha256_in_prefix": "bc40ab5ec25934e3a4ee8182f375f6c71f24b165e66ac8532f0079ed2c4c1133", "size_in_bytes": 28018}, {"_path": "include/event2/dns_compat.h", "path_type": "hardlink", "sha256": "c97eb16f1821df8bfd23ca645e025cf71ed5d9f77a9ac854806ba1a2b80e3a74", "sha256_in_prefix": "c97eb16f1821df8bfd23ca645e025cf71ed5d9f77a9ac854806ba1a2b80e3a74", "size_in_bytes": 12588}, {"_path": "include/event2/dns_struct.h", "path_type": "hardlink", "sha256": "70bfb11dd140f26cb96ee7fc3bbc62c56f2e6d0a261f0bf7c1b389733d170e02", "sha256_in_prefix": "70bfb11dd140f26cb96ee7fc3bbc62c56f2e6d0a261f0bf7c1b389733d170e02", "size_in_bytes": 2596}, {"_path": "include/event2/event-config.h", "path_type": "hardlink", "sha256": "2c87bb044917b44131bae3164fc1c9b20bc65492d6b2a0d31955388f0e57ea0b", "sha256_in_prefix": "2c87bb044917b44131bae3164fc1c9b20bc65492d6b2a0d31955388f0e57ea0b", "size_in_bytes": 15758}, {"_path": "include/event2/event.h", "path_type": "hardlink", "sha256": "a93b3346f3f653236a45799c2f67dabe0f5fe71612130d3e512efe53bfc2e2e6", "sha256_in_prefix": "a93b3346f3f653236a45799c2f67dabe0f5fe71612130d3e512efe53bfc2e2e6", "size_in_bytes": 62337}, {"_path": "include/event2/event_compat.h", "path_type": "hardlink", "sha256": "ca01dc1e74024f619cdf603dbc224113146243176dd05123c900b402623496eb", "sha256_in_prefix": "ca01dc1e74024f619cdf603dbc224113146243176dd05123c900b402623496eb", "size_in_bytes": 7639}, {"_path": "include/event2/event_struct.h", "path_type": "hardlink", "sha256": "7b52b49ccf92b2a93d330907a8834b6b7d7082ce7a6c21a923d68d712d4d1f0e", "sha256_in_prefix": "7b52b49ccf92b2a93d330907a8834b6b7d7082ce7a6c21a923d68d712d4d1f0e", "size_in_bytes": 5024}, {"_path": "include/event2/http.h", "path_type": "hardlink", "sha256": "8eae9570fe42f81d8821b827c79b450fa41b8f6490c59187854ba6ef074c8be7", "sha256_in_prefix": "8eae9570fe42f81d8821b827c79b450fa41b8f6490c59187854ba6ef074c8be7", "size_in_bytes": 42809}, {"_path": "include/event2/http_compat.h", "path_type": "hardlink", "sha256": "7268edcd165578ae3af6dbf726824b90ec62e4bcd245241c289525ad2a58d968", "sha256_in_prefix": "7268edcd165578ae3af6dbf726824b90ec62e4bcd245241c289525ad2a58d968", "size_in_bytes": 3265}, {"_path": "include/event2/http_struct.h", "path_type": "hardlink", "sha256": "06037f7be8eab59daed8207df0bc3388c80922c8589d5700915f0285d9f27f76", "sha256_in_prefix": "06037f7be8eab59daed8207df0bc3388c80922c8589d5700915f0285d9f27f76", "size_in_bytes": 4809}, {"_path": "include/event2/keyvalq_struct.h", "path_type": "hardlink", "sha256": "353f7341da0baf99451947a814ca97c76f409871bbe2db02fbccdbae0533348a", "sha256_in_prefix": "353f7341da0baf99451947a814ca97c76f409871bbe2db02fbccdbae0533348a", "size_in_bytes": 2603}, {"_path": "include/event2/listener.h", "path_type": "hardlink", "sha256": "71e54b24e55967a21cbb0bc96a1ee59c09d78fee004f61ab9b5e273c28a39459", "sha256_in_prefix": "71e54b24e55967a21cbb0bc96a1ee59c09d78fee004f61ab9b5e273c28a39459", "size_in_bytes": 7991}, {"_path": "include/event2/rpc.h", "path_type": "hardlink", "sha256": "477d073a8d7f0fdf202dd820fea62d0f9cf4be44decbca58f32e82e9d4a7e24c", "sha256_in_prefix": "477d073a8d7f0fdf202dd820fea62d0f9cf4be44decbca58f32e82e9d4a7e24c", "size_in_bytes": 21764}, {"_path": "include/event2/rpc_compat.h", "path_type": "hardlink", "sha256": "95eb790804950275eb12b1d4f60831fa77031ce3d08f853609cd562ed283b25d", "sha256_in_prefix": "95eb790804950275eb12b1d4f60831fa77031ce3d08f853609cd562ed283b25d", "size_in_bytes": 2351}, {"_path": "include/event2/rpc_struct.h", "path_type": "hardlink", "sha256": "d1041c44af37449a32d7c41c4d51e895fc872682fb1ab444a2240de743ad94fa", "sha256_in_prefix": "d1041c44af37449a32d7c41c4d51e895fc872682fb1ab444a2240de743ad94fa", "size_in_bytes": 3598}, {"_path": "include/event2/tag.h", "path_type": "hardlink", "sha256": "ec36b62473831ce153a8e58df2b5eadbd9976751158c5fac49da9448a6367853", "sha256_in_prefix": "ec36b62473831ce153a8e58df2b5eadbd9976751158c5fac49da9448a6367853", "size_in_bytes": 4914}, {"_path": "include/event2/tag_compat.h", "path_type": "hardlink", "sha256": "478ca6534915377b6895cf82181f772365b2ff097e68fe9c432b77d731110caa", "sha256_in_prefix": "478ca6534915377b6895cf82181f772365b2ff097e68fe9c432b77d731110caa", "size_in_bytes": 2141}, {"_path": "include/event2/thread.h", "path_type": "hardlink", "sha256": "9bbc4767426833fb71f3324fcd550c135d8fdd554e56d5876717f81ef732be1e", "sha256_in_prefix": "9bbc4767426833fb71f3324fcd550c135d8fdd554e56d5876717f81ef732be1e", "size_in_bytes": 9952}, {"_path": "include/event2/util.h", "path_type": "hardlink", "sha256": "c438dbc2b6085084366e70ef9bad6b02936ca34d2b6944f7b6dc7645edec66e8", "sha256_in_prefix": "c438dbc2b6085084366e70ef9bad6b02936ca34d2b6944f7b6dc7645edec66e8", "size_in_bytes": 29420}, {"_path": "include/event2/visibility.h", "path_type": "hardlink", "sha256": "7a8a2ae1c0cbdc01282b994caa72bc33cc4e66cca399a791864dbec4ac8a674d", "sha256_in_prefix": "7a8a2ae1c0cbdc01282b994caa72bc33cc4e66cca399a791864dbec4ac8a674d", "size_in_bytes": 2871}, {"_path": "include/evhttp.h", "path_type": "hardlink", "sha256": "c51a557d4fca95be83691a9dad87711f0bf2fdb921832d0eafab47a2d62bcaa0", "sha256_in_prefix": "c51a557d4fca95be83691a9dad87711f0bf2fdb921832d0eafab47a2d62bcaa0", "size_in_bytes": 2035}, {"_path": "include/evrpc.h", "path_type": "hardlink", "sha256": "941b0c0be213217c788ae542038d5415a97cb80ccfacf81a989c834aeb8ac3f2", "sha256_in_prefix": "941b0c0be213217c788ae542038d5415a97cb80ccfacf81a989c834aeb8ac3f2", "size_in_bytes": 2015}, {"_path": "include/evutil.h", "path_type": "hardlink", "sha256": "1ff1e5990aaa53eb2fb160523f5f8bd58196317436d5c48d402304d8a32f261a", "sha256_in_prefix": "1ff1e5990aaa53eb2fb160523f5f8bd58196317436d5c48d402304d8a32f261a", "size_in_bytes": 1782}, {"_path": "lib/cmake/libevent/LibeventConfig.cmake", "path_type": "hardlink", "sha256": "e9b34b8b3bd602ff90cea037b766eda9fc6dd4ea64e742476d6002c108b61ee8", "sha256_in_prefix": "e9b34b8b3bd602ff90cea037b766eda9fc6dd4ea64e742476d6002c108b61ee8", "size_in_bytes": 7203}, {"_path": "lib/cmake/libevent/LibeventConfigVersion.cmake", "path_type": "hardlink", "sha256": "f138abc60ade2e844b7324241d20cbc2f04c7ffc10b64b77d88e5a8cc9276cb3", "sha256_in_prefix": "f138abc60ade2e844b7324241d20cbc2f04c7ffc10b64b77d88e5a8cc9276cb3", "size_in_bytes": 369}, {"_path": "lib/cmake/libevent/LibeventTargets-shared-release.cmake", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/libevent_1685725980594/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_", "sha256": "4a19c88c01a8e5c05487b085bd5266a568b6b887541b05183437c2cb2482157e", "sha256_in_prefix": "6c7fac965fc8b7f304911b71768ba476af3a03727d1db13fff8689109ae8121d", "size_in_bytes": 3470}, {"_path": "lib/cmake/libevent/LibeventTargets-shared.cmake", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/libevent_1685725980594/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_", "sha256": "5dd642509bef3a0a66ee919f44ff151b0314fe5524ae0eaf1e84063c23f00812", "sha256_in_prefix": "a7312cb486ebc077ae66b76d1ab139bcc38bbe3d8097e134030566f923fba592", "size_in_bytes": 5595}, {"_path": "lib/libevent-2.1.7.dylib", "path_type": "hardlink", "sha256": "f4e323a2212db612b234290fca05287d8e16d843dabe6c3d05e537810c5a8470", "sha256_in_prefix": "f4e323a2212db612b234290fca05287d8e16d843dabe6c3d05e537810c5a8470", "size_in_bytes": 276752}, {"_path": "lib/libevent.dylib", "path_type": "softlink", "sha256": "f4e323a2212db612b234290fca05287d8e16d843dabe6c3d05e537810c5a8470", "size_in_bytes": 276752}, {"_path": "lib/libevent_core-2.1.7.dylib", "path_type": "hardlink", "sha256": "9d0e8ce1cb5252857ca67311a201d849128e55362ae5ed1022fcbca79ee35fb6", "sha256_in_prefix": "9d0e8ce1cb5252857ca67311a201d849128e55362ae5ed1022fcbca79ee35fb6", "size_in_bytes": 164824}, {"_path": "lib/libevent_core.dylib", "path_type": "softlink", "sha256": "9d0e8ce1cb5252857ca67311a201d849128e55362ae5ed1022fcbca79ee35fb6", "size_in_bytes": 164824}, {"_path": "lib/libevent_extra-2.1.7.dylib", "path_type": "hardlink", "sha256": "cc1fd3dfc85e4061a48f4b683c2d43967d8b51a4a91919bd64c75a0c618a3405", "sha256_in_prefix": "cc1fd3dfc85e4061a48f4b683c2d43967d8b51a4a91919bd64c75a0c618a3405", "size_in_bytes": 131912}, {"_path": "lib/libevent_extra.dylib", "path_type": "softlink", "sha256": "cc1fd3dfc85e4061a48f4b683c2d43967d8b51a4a91919bd64c75a0c618a3405", "size_in_bytes": 131912}, {"_path": "lib/libevent_openssl-2.1.7.dylib", "path_type": "hardlink", "sha256": "270a1da5ec0fbbf867c24b23e41d86e2d0c78c8e0ac63b902435b567d647a780", "sha256_in_prefix": "270a1da5ec0fbbf867c24b23e41d86e2d0c78c8e0ac63b902435b567d647a780", "size_in_bytes": 32528}, {"_path": "lib/libevent_openssl.dylib", "path_type": "softlink", "sha256": "270a1da5ec0fbbf867c24b23e41d86e2d0c78c8e0ac63b902435b567d647a780", "size_in_bytes": 32528}, {"_path": "lib/libevent_pthreads-2.1.7.dylib", "path_type": "hardlink", "sha256": "00e77f5de8d8dc06c54f02fe7bf8ba9c4902a635bc8f3cb629eb020ded31436e", "sha256_in_prefix": "00e77f5de8d8dc06c54f02fe7bf8ba9c4902a635bc8f3cb629eb020ded31436e", "size_in_bytes": 14488}, {"_path": "lib/libevent_pthreads.dylib", "path_type": "softlink", "sha256": "00e77f5de8d8dc06c54f02fe7bf8ba9c4902a635bc8f3cb629eb020ded31436e", "size_in_bytes": 14488}, {"_path": "lib/pkgconfig/libevent.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/libevent_1685725980594/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_", "sha256": "e37606e60cb8e76b77d43333c6b0f3e9c4713b163c04b9472fec424574f435b0", "sha256_in_prefix": "00bc93fe2581dfa16db61f7fdcd976b34a9cb7e8b3475f82feb762e3245b8092", "size_in_bytes": 1299}, {"_path": "lib/pkgconfig/libevent_core.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/libevent_1685725980594/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_", "sha256": "47b76813a88f066eca66e6394d947ad303a640173c8129b19c6da7a3093f59ab", "sha256_in_prefix": "bf951af2fefd9b889a496fbd2f735b8459d1eae1af39fa60d8c10b49fd91c211", "size_in_bytes": 1263}, {"_path": "lib/pkgconfig/libevent_extra.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/libevent_1685725980594/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_", "sha256": "7e366516000e5ec8fe48d7fb80fced4530876242144f86e84a54ebef550c524b", "sha256_in_prefix": "819b440e59553cb54c34caf5c6db0ce658fcebcd7daa60b43d930368e227b80c", "size_in_bytes": 1266}, {"_path": "lib/pkgconfig/libevent_openssl.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/libevent_1685725980594/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_", "sha256": "ce1c2abda20324d12bf4e1cfbd635be07417e27435fafedfd51d77b272df6e6a", "sha256_in_prefix": "326e223bbe52c6b73331deb1c98321e5216ef2b285143c2de11bdbb981489135", "size_in_bytes": 1879}, {"_path": "lib/pkgconfig/libevent_pthreads.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/libevent_1685725980594/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_", "sha256": "33e7d0d3dacfd086e7f3a35639af9cae06cd677ad898d80d343688c5c89806f1", "sha256_in_prefix": "c19e78a8c209dbe84c5010ed764fa02d1e80d73f0cab508b3a48bf5d85e45c38", "size_in_bytes": 1336}], "paths_version": 1}, "requested_spec": "None", "sha256": "e0bd9af2a29f8dd74309c0ae4f17a7c2b8c4b89f875ff1d6540c941eefbd07fb", "size": 372661, "subdir": "osx-64", "timestamp": 1685726378000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libevent-2.1.12-ha90c15b_1.conda", "version": "2.1.12"}