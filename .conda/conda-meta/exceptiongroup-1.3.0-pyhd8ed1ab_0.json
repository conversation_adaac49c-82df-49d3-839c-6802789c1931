{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.9", "typing_extensions >=4.6.0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/exceptiongroup-1.3.0-pyhd8ed1ab_0", "files": ["lib/python3.11/site-packages/exceptiongroup-1.3.0.dist-info/INSTALLER", "lib/python3.11/site-packages/exceptiongroup-1.3.0.dist-info/METADATA", "lib/python3.11/site-packages/exceptiongroup-1.3.0.dist-info/RECORD", "lib/python3.11/site-packages/exceptiongroup-1.3.0.dist-info/REQUESTED", "lib/python3.11/site-packages/exceptiongroup-1.3.0.dist-info/WHEEL", "lib/python3.11/site-packages/exceptiongroup-1.3.0.dist-info/direct_url.json", "lib/python3.11/site-packages/exceptiongroup-1.3.0.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/exceptiongroup/__init__.py", "lib/python3.11/site-packages/exceptiongroup/_catch.py", "lib/python3.11/site-packages/exceptiongroup/_exceptions.py", "lib/python3.11/site-packages/exceptiongroup/_formatting.py", "lib/python3.11/site-packages/exceptiongroup/_suppress.py", "lib/python3.11/site-packages/exceptiongroup/_version.py", "lib/python3.11/site-packages/exceptiongroup/py.typed", "lib/python3.11/site-packages/exceptiongroup/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/exceptiongroup/__pycache__/_catch.cpython-311.pyc", "lib/python3.11/site-packages/exceptiongroup/__pycache__/_exceptions.cpython-311.pyc", "lib/python3.11/site-packages/exceptiongroup/__pycache__/_formatting.cpython-311.pyc", "lib/python3.11/site-packages/exceptiongroup/__pycache__/_suppress.cpython-311.pyc", "lib/python3.11/site-packages/exceptiongroup/__pycache__/_version.cpython-311.pyc"], "fn": "exceptiongroup-1.3.0-pyhd8ed1ab_0.conda", "license": "MIT and PSF-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/exceptiongroup-1.3.0-pyhd8ed1ab_0", "type": 1}, "md5": "72e42d28960d875c7654614f8b50939a", "name": "exceptiongroup", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/exceptiongroup-1.3.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/exceptiongroup-1.3.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "a2202afa5957a8e695d01ddd24ddf5e928ed0a9b6142fb908e656e54e6db69e4", "sha256_in_prefix": "a2202afa5957a8e695d01ddd24ddf5e928ed0a9b6142fb908e656e54e6db69e4", "size_in_bytes": 6725}, {"_path": "site-packages/exceptiongroup-1.3.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "712a908930c4ea55e624f358cf97a6f404bc2249cfffaa2f7ed2372942f58b6e", "sha256_in_prefix": "712a908930c4ea55e624f358cf97a6f404bc2249cfffaa2f7ed2372942f58b6e", "size_in_bytes": 1545}, {"_path": "site-packages/exceptiongroup-1.3.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/exceptiongroup-1.3.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1b68144734c4b66791f27add5d425f3620775585718a03d0f9b110ba3a4d88db", "sha256_in_prefix": "1b68144734c4b66791f27add5d425f3620775585718a03d0f9b110ba3a4d88db", "size_in_bytes": 82}, {"_path": "site-packages/exceptiongroup-1.3.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "767bbbd63bcab5b820c3faa1b13907d8850d6a7436e58cbb3d03d15d6ad3eb0f", "sha256_in_prefix": "767bbbd63bcab5b820c3faa1b13907d8850d6a7436e58cbb3d03d15d6ad3eb0f", "size_in_bytes": 110}, {"_path": "site-packages/exceptiongroup-1.3.0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "6e5070d765031e0ad403a1cbf90ae6d19a0228f802e320b7acff460aa72ed47c", "sha256_in_prefix": "6e5070d765031e0ad403a1cbf90ae6d19a0228f802e320b7acff460aa72ed47c", "size_in_bytes": 3704}, {"_path": "site-packages/exceptiongroup/__init__.py", "path_type": "hardlink", "sha256": "ec31d2d210e4f9122cdc841cdd26d9541d3ed4c86248227d5e0bc4c849682fb3", "sha256_in_prefix": "ec31d2d210e4f9122cdc841cdd26d9541d3ed4c86248227d5e0bc4c849682fb3", "size_in_bytes": 1049}, {"_path": "site-packages/exceptiongroup/_catch.py", "path_type": "hardlink", "sha256": "09a25ecf713e264afeec1ed14f77f3bac74b5a7bb279e928a12167ecac96b7db", "sha256_in_prefix": "09a25ecf713e264afeec1ed14f77f3bac74b5a7bb279e928a12167ecac96b7db", "size_in_bytes": 4680}, {"_path": "site-packages/exceptiongroup/_exceptions.py", "path_type": "hardlink", "sha256": "c0fc0fb19eb8497129b6ec1be17ad321ad4c73bf2ea85e6f982ad74dd9652e7e", "sha256_in_prefix": "c0fc0fb19eb8497129b6ec1be17ad321ad4c73bf2ea85e6f982ad74dd9652e7e", "size_in_bytes": 11463}, {"_path": "site-packages/exceptiongroup/_formatting.py", "path_type": "hardlink", "sha256": "e140ba6d43772be171117f7afe8cf381e5f4e5f0b2a6b97aa82551f9e7c1990d", "sha256_in_prefix": "e140ba6d43772be171117f7afe8cf381e5f4e5f0b2a6b97aa82551f9e7c1990d", "size_in_bytes": 21032}, {"_path": "site-packages/exceptiongroup/_suppress.py", "path_type": "hardlink", "sha256": "2d7d753d1369721c1f356c04318f769d837517fe6a15e9d0712f048c838d5ca1", "sha256_in_prefix": "2d7d753d1369721c1f356c04318f769d837517fe6a15e9d0712f048c838d5ca1", "size_in_bytes": 1772}, {"_path": "site-packages/exceptiongroup/_version.py", "path_type": "hardlink", "sha256": "a83b5c3d974acf12e977cbd597a7e920520c916b761cafdc3bd80b0f068711d9", "sha256_in_prefix": "a83b5c3d974acf12e977cbd597a7e920520c916b761cafdc3bd80b0f068711d9", "size_in_bytes": 511}, {"_path": "site-packages/exceptiongroup/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/exceptiongroup/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/exceptiongroup/__pycache__/_catch.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/exceptiongroup/__pycache__/_exceptions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/exceptiongroup/__pycache__/_formatting.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/exceptiongroup/__pycache__/_suppress.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/exceptiongroup/__pycache__/_version.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "ce61f4f99401a4bd455b89909153b40b9c823276aefcbb06f2044618696009ca", "size": 21284, "subdir": "noarch", "timestamp": 1746947398000, "url": "https://conda.anaconda.org/conda-forge/noarch/exceptiongroup-1.3.0-pyhd8ed1ab_0.conda", "version": "1.3.0"}