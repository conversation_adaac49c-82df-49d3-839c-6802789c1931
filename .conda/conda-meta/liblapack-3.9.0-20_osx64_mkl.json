{"build": "20_osx64_mkl", "build_number": 20, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": ["blas * mkl", "libcblas 3.9.0 20_osx64_mkl", "liblapacke 3.9.0 20_osx64_mkl"], "depends": ["libblas 3.9.0 20_osx64_mkl"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/liblapack-3.9.0-20_osx64_mkl", "files": ["lib/liblapack.3.dylib", "lib/liblapack.dylib"], "fn": "liblapack-3.9.0-20_osx64_mkl.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/liblapack-3.9.0-20_osx64_mkl", "type": 1}, "md5": "58f08e12ad487fac4a08f90ff0b87aec", "name": "liblapack", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/liblapack-3.9.0-20_osx64_mkl.conda", "paths_data": {"paths": [{"_path": "lib/liblapack.3.dylib", "path_type": "softlink", "sha256": "d367ace211c4c67a12f17bf876a379d98e0854cf063714fbfbef3564b05f9794", "size_in_bytes": 18789024}, {"_path": "lib/liblapack.dylib", "path_type": "softlink", "sha256": "d367ace211c4c67a12f17bf876a379d98e0854cf063714fbfbef3564b05f9794", "size_in_bytes": 18789024}], "paths_version": 1}, "requested_spec": "None", "sha256": "fdccac604746f9620fefaee313707aa2f500f73e51f8e3a4b690d5d4c90ce3dc", "size": 14699, "subdir": "osx-64", "timestamp": 1700568690000, "track_features": "blas_mkl", "url": "https://conda.anaconda.org/conda-forge/osx-64/liblapack-3.9.0-20_osx64_mkl.conda", "version": "3.9.0"}