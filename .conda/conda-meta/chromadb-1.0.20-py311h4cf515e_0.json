{"build": "py311h4cf515e_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": ["__osx >=10.13"], "depends": ["__osx >=10.13", "bcrypt >=4.0.1", "fastapi 0.115.9", "grpcio >=1.58.0", "httpx >=0.27.0", "importlib-resources", "jsonschema >=4.19.0", "libcxx >=19", "mmh3 >=4.0.1", "numpy >=1.22.5", "onnxruntime >=1.14.1", "opentelemetry-api >=1.2.0", "opentelemetry-exporter-otlp-proto-grpc >=1.2.0", "opentelemetry-instrumentation-fastapi >=0.41b0", "opentelemetry-sdk >=1.2.0", "or<PERSON><PERSON> >=3.9.12", "overrides >=7.3.1", "posthog >=2.4.0,<6.0.0", "pulsar-client >=3.1.0", "pybase64 >=1.4.1", "pydantic >=1.9", "pypika >=0.48.9", "python >=3.11,<3.12.0a0", "python-build >=1.0.3", "python-kubernetes >=28.1.0", "python_abi 3.11.* *_cp311", "pyyaml >=6.0.0", "requests >=2.28", "rich >=10.11.0", "tenacity >=8.2.3", "tokenizers >=0.13.2", "tqdm >=4.65.0", "typer >=0.9.0", "typing-extensions >=4.5.0", "typing_extensions >=4.5.0", "uvicorn >=0.18.3", "uvicorn-standard >=0.18.3"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/chromadb-1.0.20-py311h4cf515e_0", "files": ["bin/chroma", "lib/python3.11/site-packages/chromadb-1.0.20.dist-info/INSTALLER", "lib/python3.11/site-packages/chromadb-1.0.20.dist-info/METADATA", "lib/python3.11/site-packages/chromadb-1.0.20.dist-info/RECORD", "lib/python3.11/site-packages/chromadb-1.0.20.dist-info/REQUESTED", "lib/python3.11/site-packages/chromadb-1.0.20.dist-info/WHEEL", "lib/python3.11/site-packages/chromadb-1.0.20.dist-info/direct_url.json", "lib/python3.11/site-packages/chromadb-1.0.20.dist-info/entry_points.txt", "lib/python3.11/site-packages/chromadb-1.0.20.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/chromadb/__init__.py", "lib/python3.11/site-packages/chromadb/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/__pycache__/app.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/__pycache__/base_types.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/__pycache__/config.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/__pycache__/errors.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/__pycache__/serde.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/__pycache__/types.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/api/__init__.py", "lib/python3.11/site-packages/chromadb/api/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/api/__pycache__/async_api.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/api/__pycache__/async_client.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/api/__pycache__/async_fastapi.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/api/__pycache__/base_http_client.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/api/__pycache__/client.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/api/__pycache__/collection_configuration.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/api/__pycache__/configuration.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/api/__pycache__/fastapi.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/api/__pycache__/rust.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/api/__pycache__/segment.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/api/__pycache__/shared_system_client.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/api/__pycache__/types.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/api/async_api.py", "lib/python3.11/site-packages/chromadb/api/async_client.py", "lib/python3.11/site-packages/chromadb/api/async_fastapi.py", "lib/python3.11/site-packages/chromadb/api/base_http_client.py", "lib/python3.11/site-packages/chromadb/api/client.py", "lib/python3.11/site-packages/chromadb/api/collection_configuration.py", "lib/python3.11/site-packages/chromadb/api/configuration.py", "lib/python3.11/site-packages/chromadb/api/fastapi.py", "lib/python3.11/site-packages/chromadb/api/models/AsyncCollection.py", "lib/python3.11/site-packages/chromadb/api/models/Collection.py", "lib/python3.11/site-packages/chromadb/api/models/CollectionCommon.py", "lib/python3.11/site-packages/chromadb/api/models/__pycache__/AsyncCollection.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/api/models/__pycache__/Collection.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/api/models/__pycache__/CollectionCommon.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/api/rust.py", "lib/python3.11/site-packages/chromadb/api/segment.py", "lib/python3.11/site-packages/chromadb/api/shared_system_client.py", "lib/python3.11/site-packages/chromadb/api/types.py", "lib/python3.11/site-packages/chromadb/app.py", "lib/python3.11/site-packages/chromadb/auth/__init__.py", "lib/python3.11/site-packages/chromadb/auth/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/auth/basic_authn/__init__.py", "lib/python3.11/site-packages/chromadb/auth/basic_authn/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/auth/simple_rbac_authz/__init__.py", "lib/python3.11/site-packages/chromadb/auth/simple_rbac_authz/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/auth/token_authn/__init__.py", "lib/python3.11/site-packages/chromadb/auth/token_authn/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/auth/utils/__init__.py", "lib/python3.11/site-packages/chromadb/auth/utils/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/base_types.py", "lib/python3.11/site-packages/chromadb/chromadb_rust_bindings.pyi", "lib/python3.11/site-packages/chromadb/cli/__init__.py", "lib/python3.11/site-packages/chromadb/cli/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/cli/__pycache__/cli.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/cli/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/cli/cli.py", "lib/python3.11/site-packages/chromadb/cli/utils.py", "lib/python3.11/site-packages/chromadb/config.py", "lib/python3.11/site-packages/chromadb/db/__init__.py", "lib/python3.11/site-packages/chromadb/db/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/db/__pycache__/base.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/db/__pycache__/migrations.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/db/__pycache__/system.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/db/base.py", "lib/python3.11/site-packages/chromadb/db/impl/__init__.py", "lib/python3.11/site-packages/chromadb/db/impl/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/db/impl/__pycache__/sqlite.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/db/impl/__pycache__/sqlite_pool.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/db/impl/grpc/__pycache__/client.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/db/impl/grpc/__pycache__/server.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/db/impl/grpc/client.py", "lib/python3.11/site-packages/chromadb/db/impl/grpc/server.py", "lib/python3.11/site-packages/chromadb/db/impl/sqlite.py", "lib/python3.11/site-packages/chromadb/db/impl/sqlite_pool.py", "lib/python3.11/site-packages/chromadb/db/migrations.py", "lib/python3.11/site-packages/chromadb/db/mixins/__pycache__/embeddings_queue.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/db/mixins/__pycache__/sysdb.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/db/mixins/embeddings_queue.py", "lib/python3.11/site-packages/chromadb/db/mixins/sysdb.py", "lib/python3.11/site-packages/chromadb/db/system.py", "lib/python3.11/site-packages/chromadb/errors.py", "lib/python3.11/site-packages/chromadb/execution/__init__.py", "lib/python3.11/site-packages/chromadb/execution/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/execution/executor/__pycache__/abstract.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/execution/executor/__pycache__/distributed.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/execution/executor/__pycache__/local.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/execution/executor/abstract.py", "lib/python3.11/site-packages/chromadb/execution/executor/distributed.py", "lib/python3.11/site-packages/chromadb/execution/executor/local.py", "lib/python3.11/site-packages/chromadb/execution/expression/__pycache__/operator.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/execution/expression/__pycache__/plan.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/execution/expression/operator.py", "lib/python3.11/site-packages/chromadb/execution/expression/plan.py", "lib/python3.11/site-packages/chromadb/experimental/density_relevance.ipynb", "lib/python3.11/site-packages/chromadb/ingest/__init__.py", "lib/python3.11/site-packages/chromadb/ingest/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/ingest/impl/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/ingest/impl/utils.py", "lib/python3.11/site-packages/chromadb/log_config.yml", "lib/python3.11/site-packages/chromadb/logservice/__pycache__/logservice.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/logservice/logservice.py", "lib/python3.11/site-packages/chromadb/migrations/__init__.py", "lib/python3.11/site-packages/chromadb/migrations/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/migrations/embeddings_queue/00001-embeddings.sqlite.sql", "lib/python3.11/site-packages/chromadb/migrations/embeddings_queue/00002-embeddings-queue-config.sqlite.sql", "lib/python3.11/site-packages/chromadb/migrations/metadb/00001-embedding-metadata.sqlite.sql", "lib/python3.11/site-packages/chromadb/migrations/metadb/00002-embedding-metadata.sqlite.sql", "lib/python3.11/site-packages/chromadb/migrations/metadb/00003-full-text-tokenize.sqlite.sql", "lib/python3.11/site-packages/chromadb/migrations/metadb/00004-metadata-indices.sqlite.sql", "lib/python3.11/site-packages/chromadb/migrations/metadb/00005-max-seq-id-int.sqlite.sql", "lib/python3.11/site-packages/chromadb/migrations/sysdb/00001-collections.sqlite.sql", "lib/python3.11/site-packages/chromadb/migrations/sysdb/00002-segments.sqlite.sql", "lib/python3.11/site-packages/chromadb/migrations/sysdb/00003-collection-dimension.sqlite.sql", "lib/python3.11/site-packages/chromadb/migrations/sysdb/00004-tenants-databases.sqlite.sql", "lib/python3.11/site-packages/chromadb/migrations/sysdb/00005-remove-topic.sqlite.sql", "lib/python3.11/site-packages/chromadb/migrations/sysdb/00006-collection-segment-metadata.sqlite.sql", "lib/python3.11/site-packages/chromadb/migrations/sysdb/00007-collection-config.sqlite.sql", "lib/python3.11/site-packages/chromadb/migrations/sysdb/00008-maintenance-log.sqlite.sql", "lib/python3.11/site-packages/chromadb/migrations/sysdb/00009-segment-collection-not-null.sqlite.sql", "lib/python3.11/site-packages/chromadb/proto/__init__.py", "lib/python3.11/site-packages/chromadb/proto/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/proto/__pycache__/convert.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/proto/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/proto/convert.py", "lib/python3.11/site-packages/chromadb/proto/utils.py", "lib/python3.11/site-packages/chromadb/py.typed", "lib/python3.11/site-packages/chromadb/quota/__init__.py", "lib/python3.11/site-packages/chromadb/quota/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/quota/simple_quota_enforcer/__init__.py", "lib/python3.11/site-packages/chromadb/quota/simple_quota_enforcer/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/rate_limit/__init__.py", "lib/python3.11/site-packages/chromadb/rate_limit/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/rate_limit/simple_rate_limit/__init__.py", "lib/python3.11/site-packages/chromadb/rate_limit/simple_rate_limit/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/segment/__init__.py", "lib/python3.11/site-packages/chromadb/segment/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/segment/distributed/__init__.py", "lib/python3.11/site-packages/chromadb/segment/distributed/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/segment/impl/__init__.py", "lib/python3.11/site-packages/chromadb/segment/impl/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/segment/impl/distributed/__pycache__/segment_directory.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/segment/impl/distributed/segment_directory.py", "lib/python3.11/site-packages/chromadb/segment/impl/manager/__init__.py", "lib/python3.11/site-packages/chromadb/segment/impl/manager/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/segment/impl/manager/__pycache__/distributed.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/segment/impl/manager/__pycache__/local.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/segment/impl/manager/cache/__init__.py", "lib/python3.11/site-packages/chromadb/segment/impl/manager/cache/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/segment/impl/manager/cache/__pycache__/cache.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/segment/impl/manager/cache/cache.py", "lib/python3.11/site-packages/chromadb/segment/impl/manager/distributed.py", "lib/python3.11/site-packages/chromadb/segment/impl/manager/local.py", "lib/python3.11/site-packages/chromadb/segment/impl/metadata/__pycache__/sqlite.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/segment/impl/metadata/sqlite.py", "lib/python3.11/site-packages/chromadb/segment/impl/vector/__pycache__/batch.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/segment/impl/vector/__pycache__/brute_force_index.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/segment/impl/vector/__pycache__/hnsw_params.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/segment/impl/vector/__pycache__/local_hnsw.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/segment/impl/vector/__pycache__/local_persistent_hnsw.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/segment/impl/vector/batch.py", "lib/python3.11/site-packages/chromadb/segment/impl/vector/brute_force_index.py", "lib/python3.11/site-packages/chromadb/segment/impl/vector/hnsw_params.py", "lib/python3.11/site-packages/chromadb/segment/impl/vector/local_hnsw.py", "lib/python3.11/site-packages/chromadb/segment/impl/vector/local_persistent_hnsw.py", "lib/python3.11/site-packages/chromadb/serde.py", "lib/python3.11/site-packages/chromadb/server/__init__.py", "lib/python3.11/site-packages/chromadb/server/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/server/fastapi/__init__.py", "lib/python3.11/site-packages/chromadb/server/fastapi/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/server/fastapi/__pycache__/types.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/server/fastapi/types.py", "lib/python3.11/site-packages/chromadb/telemetry/README.md", "lib/python3.11/site-packages/chromadb/telemetry/__init__.py", "lib/python3.11/site-packages/chromadb/telemetry/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__init__.py", "lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__pycache__/fastapi.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__pycache__/grpc.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/fastapi.py", "lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/grpc.py", "lib/python3.11/site-packages/chromadb/telemetry/product/__init__.py", "lib/python3.11/site-packages/chromadb/telemetry/product/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/telemetry/product/__pycache__/events.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/telemetry/product/__pycache__/posthog.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/telemetry/product/events.py", "lib/python3.11/site-packages/chromadb/telemetry/product/posthog.py", "lib/python3.11/site-packages/chromadb/test/__init__.py", "lib/python3.11/site-packages/chromadb/test/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/__pycache__/conftest.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/__pycache__/test_api.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/__pycache__/test_chroma.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/__pycache__/test_cli.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/__pycache__/test_client.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/__pycache__/test_config.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/__pycache__/test_multithreaded.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/api/__pycache__/test_collection.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/api/__pycache__/test_delete_database.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/api/__pycache__/test_get_database.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/api/__pycache__/test_invalid_update.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/api/__pycache__/test_limit_offset.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/api/__pycache__/test_list_databases.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/api/__pycache__/test_numpy_list_inputs.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/api/__pycache__/test_types.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/api/test_collection.py", "lib/python3.11/site-packages/chromadb/test/api/test_delete_database.py", "lib/python3.11/site-packages/chromadb/test/api/test_get_database.py", "lib/python3.11/site-packages/chromadb/test/api/test_invalid_update.py", "lib/python3.11/site-packages/chromadb/test/api/test_limit_offset.py", "lib/python3.11/site-packages/chromadb/test/api/test_list_databases.py", "lib/python3.11/site-packages/chromadb/test/api/test_numpy_list_inputs.py", "lib/python3.11/site-packages/chromadb/test/api/test_types.py", "lib/python3.11/site-packages/chromadb/test/auth/__pycache__/test_auth_utils.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/auth/test_auth_utils.py", "lib/python3.11/site-packages/chromadb/test/client/__init__.py", "lib/python3.11/site-packages/chromadb/test/client/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/client/__pycache__/create_http_client_with_basic_auth.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/client/__pycache__/test_cloud_client.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/client/__pycache__/test_create_http_client.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/client/__pycache__/test_database_tenant.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/client/__pycache__/test_database_tenant_auth.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/client/__pycache__/test_multiple_clients_concurrency.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/client/create_http_client_with_basic_auth.py", "lib/python3.11/site-packages/chromadb/test/client/test_cloud_client.py", "lib/python3.11/site-packages/chromadb/test/client/test_create_http_client.py", "lib/python3.11/site-packages/chromadb/test/client/test_database_tenant.py", "lib/python3.11/site-packages/chromadb/test/client/test_database_tenant_auth.py", "lib/python3.11/site-packages/chromadb/test/client/test_multiple_clients_concurrency.py", "lib/python3.11/site-packages/chromadb/test/configurations/__pycache__/test_collection_configuration.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/configurations/__pycache__/test_configurations.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/configurations/test_collection_configuration.py", "lib/python3.11/site-packages/chromadb/test/configurations/test_configurations.py", "lib/python3.11/site-packages/chromadb/test/conftest.py", "lib/python3.11/site-packages/chromadb/test/data_loader/__pycache__/test_data_loader.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/data_loader/test_data_loader.py", "lib/python3.11/site-packages/chromadb/test/db/__pycache__/test_log_purge.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/db/test_log_purge.py", "lib/python3.11/site-packages/chromadb/test/distributed/README.md", "lib/python3.11/site-packages/chromadb/test/distributed/__pycache__/test_log_backpressure.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/distributed/__pycache__/test_log_failover.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/distributed/__pycache__/test_repair_collection_log_offset.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/distributed/__pycache__/test_reroute.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/distributed/__pycache__/test_sanity.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/distributed/test_log_backpressure.py", "lib/python3.11/site-packages/chromadb/test/distributed/test_log_failover.py", "lib/python3.11/site-packages/chromadb/test/distributed/test_repair_collection_log_offset.py", "lib/python3.11/site-packages/chromadb/test/distributed/test_reroute.py", "lib/python3.11/site-packages/chromadb/test/distributed/test_sanity.py", "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_custom_ef.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_default_ef.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_ef.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_morph_ef.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_multimodal_ef.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_ollama_ef.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_onnx_mini_lm_l6_v2.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_openai_ef.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_voyageai_ef.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/ef/test_custom_ef.py", "lib/python3.11/site-packages/chromadb/test/ef/test_default_ef.py", "lib/python3.11/site-packages/chromadb/test/ef/test_ef.py", "lib/python3.11/site-packages/chromadb/test/ef/test_morph_ef.py", "lib/python3.11/site-packages/chromadb/test/ef/test_multimodal_ef.py", "lib/python3.11/site-packages/chromadb/test/ef/test_ollama_ef.py", "lib/python3.11/site-packages/chromadb/test/ef/test_onnx_mini_lm_l6_v2.py", "lib/python3.11/site-packages/chromadb/test/ef/test_openai_ef.py", "lib/python3.11/site-packages/chromadb/test/ef/test_voyageai_ef.py", "lib/python3.11/site-packages/chromadb/test/openssl.cnf", "lib/python3.11/site-packages/chromadb/test/property/__pycache__/invariants.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/property/__pycache__/strategies.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_add.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_base64_conversion.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_client_url.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_collections.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_collections_with_database_tenant.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_collections_with_database_tenant_overwrite.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_cross_version_persist.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_embeddings.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_filtering.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_fork.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_persist.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_restart_persist.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/property/invariants.py", "lib/python3.11/site-packages/chromadb/test/property/strategies.py", "lib/python3.11/site-packages/chromadb/test/property/test_add.py", "lib/python3.11/site-packages/chromadb/test/property/test_base64_conversion.py", "lib/python3.11/site-packages/chromadb/test/property/test_client_url.py", "lib/python3.11/site-packages/chromadb/test/property/test_collections.py", "lib/python3.11/site-packages/chromadb/test/property/test_collections_with_database_tenant.py", "lib/python3.11/site-packages/chromadb/test/property/test_collections_with_database_tenant_overwrite.py", "lib/python3.11/site-packages/chromadb/test/property/test_cross_version_persist.py", "lib/python3.11/site-packages/chromadb/test/property/test_embeddings.py", "lib/python3.11/site-packages/chromadb/test/property/test_filtering.py", "lib/python3.11/site-packages/chromadb/test/property/test_fork.py", "lib/python3.11/site-packages/chromadb/test/property/test_persist.py", "lib/python3.11/site-packages/chromadb/test/property/test_restart_persist.py", "lib/python3.11/site-packages/chromadb/test/segment/distributed/__pycache__/test_memberlist_provider.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/segment/distributed/__pycache__/test_rendezvous_hash.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/segment/distributed/test_memberlist_provider.py", "lib/python3.11/site-packages/chromadb/test/segment/distributed/test_rendezvous_hash.py", "lib/python3.11/site-packages/chromadb/test/stress/__pycache__/test_many_collections.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/stress/test_many_collections.py", "lib/python3.11/site-packages/chromadb/test/test_api.py", "lib/python3.11/site-packages/chromadb/test/test_chroma.py", "lib/python3.11/site-packages/chromadb/test/test_cli.py", "lib/python3.11/site-packages/chromadb/test/test_client.py", "lib/python3.11/site-packages/chromadb/test/test_config.py", "lib/python3.11/site-packages/chromadb/test/test_multithreaded.py", "lib/python3.11/site-packages/chromadb/test/utils/__pycache__/cross_version.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/utils/__pycache__/distance_functions.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/utils/__pycache__/test_embedding_function_schemas.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/utils/__pycache__/test_result_df_transform.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/utils/__pycache__/wait_for_version_increase.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/test/utils/cross_version.py", "lib/python3.11/site-packages/chromadb/test/utils/distance_functions.py", "lib/python3.11/site-packages/chromadb/test/utils/test_embedding_function_schemas.py", "lib/python3.11/site-packages/chromadb/test/utils/test_result_df_transform.py", "lib/python3.11/site-packages/chromadb/test/utils/wait_for_version_increase.py", "lib/python3.11/site-packages/chromadb/types.py", "lib/python3.11/site-packages/chromadb/utils/__init__.py", "lib/python3.11/site-packages/chromadb/utils/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/__pycache__/async_to_sync.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/__pycache__/batch_utils.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/__pycache__/data_loaders.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/__pycache__/delete_file.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/__pycache__/directory.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/__pycache__/distance_functions.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/__pycache__/fastapi.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/__pycache__/lru_cache.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/__pycache__/messageid.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/__pycache__/read_write_lock.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/__pycache__/rendezvous_hash.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/__pycache__/results.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/async_to_sync.py", "lib/python3.11/site-packages/chromadb/utils/batch_utils.py", "lib/python3.11/site-packages/chromadb/utils/data_loaders.py", "lib/python3.11/site-packages/chromadb/utils/delete_file.py", "lib/python3.11/site-packages/chromadb/utils/directory.py", "lib/python3.11/site-packages/chromadb/utils/distance_functions.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__init__.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/amazon_bedrock_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/baseten_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/chroma_langchain_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/cloudflare_workers_ai_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/cohere_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/google_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/huggingface_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/instructor_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/jina_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/mistral_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/morph_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/ollama_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/onnx_mini_lm_l6_v2.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/open_clip_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/openai_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/roboflow_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/sentence_transformer_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/text2vec_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/together_ai_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/voyageai_embedding_function.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/amazon_bedrock_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/baseten_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/chroma_langchain_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/cloudflare_workers_ai_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/cohere_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/google_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/huggingface_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/instructor_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/jina_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/mistral_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/morph_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/ollama_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/onnx_mini_lm_l6_v2.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/open_clip_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/openai_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/roboflow_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/schemas/README.md", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/schemas/__init__.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/schemas/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/schemas/__pycache__/registry.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/schemas/__pycache__/schema_utils.cpython-311.pyc", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/schemas/registry.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/schemas/schema_utils.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/sentence_transformer_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/text2vec_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/together_ai_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/embedding_functions/voyageai_embedding_function.py", "lib/python3.11/site-packages/chromadb/utils/fastapi.py", "lib/python3.11/site-packages/chromadb/utils/lru_cache.py", "lib/python3.11/site-packages/chromadb/utils/messageid.py", "lib/python3.11/site-packages/chromadb/utils/read_write_lock.py", "lib/python3.11/site-packages/chromadb/utils/rendezvous_hash.py", "lib/python3.11/site-packages/chromadb/utils/results.py", "lib/python3.11/site-packages/chromadb_rust_bindings/__init__.py", "lib/python3.11/site-packages/chromadb_rust_bindings/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/chromadb_rust_bindings/chromadb_rust_bindings.abi3.so", "lib/python3.11/site-packages/schemas/embedding_functions/README.md", "lib/python3.11/site-packages/schemas/embedding_functions/amazon_bedrock.json", "lib/python3.11/site-packages/schemas/embedding_functions/base_schema.json", "lib/python3.11/site-packages/schemas/embedding_functions/chroma_langchain.json", "lib/python3.11/site-packages/schemas/embedding_functions/cloudflare_workers_ai.json", "lib/python3.11/site-packages/schemas/embedding_functions/cohere.json", "lib/python3.11/site-packages/schemas/embedding_functions/default.json", "lib/python3.11/site-packages/schemas/embedding_functions/google_generative_ai.json", "lib/python3.11/site-packages/schemas/embedding_functions/google_palm.json", "lib/python3.11/site-packages/schemas/embedding_functions/google_vertex.json", "lib/python3.11/site-packages/schemas/embedding_functions/huggingface.json", "lib/python3.11/site-packages/schemas/embedding_functions/huggingface_server.json", "lib/python3.11/site-packages/schemas/embedding_functions/instructor.json", "lib/python3.11/site-packages/schemas/embedding_functions/jina.json", "lib/python3.11/site-packages/schemas/embedding_functions/mistral.json", "lib/python3.11/site-packages/schemas/embedding_functions/morph.json", "lib/python3.11/site-packages/schemas/embedding_functions/ollama.json", "lib/python3.11/site-packages/schemas/embedding_functions/onnx_mini_lm_l6_v2.json", "lib/python3.11/site-packages/schemas/embedding_functions/open_clip.json", "lib/python3.11/site-packages/schemas/embedding_functions/openai.json", "lib/python3.11/site-packages/schemas/embedding_functions/roboflow.json", "lib/python3.11/site-packages/schemas/embedding_functions/sentence_transformer.json", "lib/python3.11/site-packages/schemas/embedding_functions/text2vec.json", "lib/python3.11/site-packages/schemas/embedding_functions/together_ai.json", "lib/python3.11/site-packages/schemas/embedding_functions/transformers.json", "lib/python3.11/site-packages/schemas/embedding_functions/voyageai.json"], "fn": "chromadb-1.0.20-py311h4cf515e_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/chromadb-1.0.20-py311h4cf515e_0", "type": 1}, "md5": "8cfed07e5df17dbf0d12030bde84a3d9", "name": "chromadb", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/chromadb-1.0.20-py311h4cf515e_0.conda", "paths_data": {"paths": [{"_path": "bin/chroma", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/chromadb_1757677250475/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_", "sha256": "54302376c0b26d7e5c31f9b6a270a81a4637e198fdbeeddb492192e9146c81c1", "sha256_in_prefix": "0beb602894db44561df95c65795ec7d62f69cd9a83a41c71c86addfe36020462", "size_in_bytes": 436}, {"_path": "lib/python3.11/site-packages/chromadb-1.0.20.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.11/site-packages/chromadb-1.0.20.dist-info/METADATA", "path_type": "hardlink", "sha256": "5d5675fa1e064b2a7574b95e2174096cf169386f2bd56128a1420d7cee2d4011", "sha256_in_prefix": "5d5675fa1e064b2a7574b95e2174096cf169386f2bd56128a1420d7cee2d4011", "size_in_bytes": 7251}, {"_path": "lib/python3.11/site-packages/chromadb-1.0.20.dist-info/RECORD", "path_type": "hardlink", "sha256": "fbc7286ec31d8a1da1a2b1e295324c8b2fe6cd983d7415873e2ab78d1b9675d5", "sha256_in_prefix": "fbc7286ec31d8a1da1a2b1e295324c8b2fe6cd983d7415873e2ab78d1b9675d5", "size_in_bytes": 36880}, {"_path": "lib/python3.11/site-packages/chromadb-1.0.20.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/chromadb-1.0.20.dist-info/WHEEL", "path_type": "hardlink", "sha256": "68ff6134f1bf8943e87d9fcebed104979273c5b138e051b74e8cd6b27e100cf8", "sha256_in_prefix": "68ff6134f1bf8943e87d9fcebed104979273c5b138e051b74e8cd6b27e100cf8", "size_in_bytes": 104}, {"_path": "lib/python3.11/site-packages/chromadb-1.0.20.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "555f89160f2a41593806d69ade95b9b09c6ef81b3d6cd262a7161f97e55aa4c6", "sha256_in_prefix": "555f89160f2a41593806d69ade95b9b09c6ef81b3d6cd262a7161f97e55aa4c6", "size_in_bytes": 96}, {"_path": "lib/python3.11/site-packages/chromadb-1.0.20.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "bdbfff5915c633d079b50d58b2b729dcb2206a05b161fc8db1dd0d4bc0bfd3c5", "sha256_in_prefix": "bdbfff5915c633d079b50d58b2b729dcb2206a05b161fc8db1dd0d4bc0bfd3c5", "size_in_bytes": 46}, {"_path": "lib/python3.11/site-packages/chromadb-1.0.20.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "sha256_in_prefix": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "size_in_bytes": 11357}, {"_path": "lib/python3.11/site-packages/chromadb/__init__.py", "path_type": "hardlink", "sha256": "b43389932dc9089ff65262e2e14a78568ee6cad03e4f95bd4404d2ef09f36e38", "sha256_in_prefix": "b43389932dc9089ff65262e2e14a78568ee6cad03e4f95bd4404d2ef09f36e38", "size_in_bytes": 13571}, {"_path": "lib/python3.11/site-packages/chromadb/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "7e8c1cf3e11b06b67ec68f9dab176be7f1efa6fa17242d1827e727b11722fed1", "sha256_in_prefix": "7e8c1cf3e11b06b67ec68f9dab176be7f1efa6fa17242d1827e727b11722fed1", "size_in_bytes": 15751}, {"_path": "lib/python3.11/site-packages/chromadb/__pycache__/app.cpython-311.pyc", "path_type": "hardlink", "sha256": "338947208aded226c246afe89b937017cf3d76f85e5634b24355f6edcdbda7af", "sha256_in_prefix": "338947208aded226c246afe89b937017cf3d76f85e5634b24355f6edcdbda7af", "size_in_bytes": 484}, {"_path": "lib/python3.11/site-packages/chromadb/__pycache__/base_types.cpython-311.pyc", "path_type": "hardlink", "sha256": "1ab86b5e3e6cedaf43ff36051382f9e04ee0966ed8bf161fecba1a097c40b1f7", "sha256_in_prefix": "1ab86b5e3e6cedaf43ff36051382f9e04ee0966ed8bf161fecba1a097c40b1f7", "size_in_bytes": 1910}, {"_path": "lib/python3.11/site-packages/chromadb/__pycache__/config.cpython-311.pyc", "path_type": "hardlink", "sha256": "3269f8b5ae6c444e32a4e1d8029b989ebb46a1dd26042bf7fb7208845d65f5cb", "sha256_in_prefix": "3269f8b5ae6c444e32a4e1d8029b989ebb46a1dd26042bf7fb7208845d65f5cb", "size_in_bytes": 22706}, {"_path": "lib/python3.11/site-packages/chromadb/__pycache__/errors.cpython-311.pyc", "path_type": "hardlink", "sha256": "29084d9593bddee32cc995f7ee8b46ecb0c0ba99f8583921e7fa9da42b5b6bbc", "sha256_in_prefix": "29084d9593bddee32cc995f7ee8b46ecb0c0ba99f8583921e7fa9da42b5b6bbc", "size_in_bytes": 10026}, {"_path": "lib/python3.11/site-packages/chromadb/__pycache__/serde.cpython-311.pyc", "path_type": "hardlink", "sha256": "15df6e1ebae2d35fe6f66d534a9163adec91486e16ce20cc5de408c94d6a4636", "sha256_in_prefix": "15df6e1ebae2d35fe6f66d534a9163adec91486e16ce20cc5de408c94d6a4636", "size_in_bytes": 3236}, {"_path": "lib/python3.11/site-packages/chromadb/__pycache__/types.cpython-311.pyc", "path_type": "hardlink", "sha256": "e2f19896e520d5cc07f8255bf7de7eda4ec6f8370450418480b10bf6fc509335", "sha256_in_prefix": "e2f19896e520d5cc07f8255bf7de7eda4ec6f8370450418480b10bf6fc509335", "size_in_bytes": 14713}, {"_path": "lib/python3.11/site-packages/chromadb/api/__init__.py", "path_type": "hardlink", "sha256": "0e541bd430cd3caf027349e984519742863f6e69a3ba85334875ac85ec6c3572", "sha256_in_prefix": "0e541bd430cd3caf027349e984519742863f6e69a3ba85334875ac85ec6c3572", "size_in_bytes": 23106}, {"_path": "lib/python3.11/site-packages/chromadb/api/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "495671bf957b2604f4bd3aca295afdcce460f22840bd78d288a37b67f3b705c0", "sha256_in_prefix": "495671bf957b2604f4bd3aca295afdcce460f22840bd78d288a37b67f3b705c0", "size_in_bytes": 28887}, {"_path": "lib/python3.11/site-packages/chromadb/api/__pycache__/async_api.cpython-311.pyc", "path_type": "hardlink", "sha256": "59cd32b406860938e326f1d9e1debd937e1a4c41eb74f07dc3e2bdd994dc7bb0", "sha256_in_prefix": "59cd32b406860938e326f1d9e1debd937e1a4c41eb74f07dc3e2bdd994dc7bb0", "size_in_bytes": 29254}, {"_path": "lib/python3.11/site-packages/chromadb/api/__pycache__/async_client.cpython-311.pyc", "path_type": "hardlink", "sha256": "cddc26757153ce862da9b3a1aacbd9513ae609ed00680439a4fd213615dea657", "sha256_in_prefix": "cddc26757153ce862da9b3a1aacbd9513ae609ed00680439a4fd213615dea657", "size_in_bytes": 22559}, {"_path": "lib/python3.11/site-packages/chromadb/api/__pycache__/async_fastapi.cpython-311.pyc", "path_type": "hardlink", "sha256": "81eaf9416b39fce117453afe34ad2986df2adb0a7c0d977095e68ef0df0355ca", "sha256_in_prefix": "81eaf9416b39fce117453afe34ad2986df2adb0a7c0d977095e68ef0df0355ca", "size_in_bytes": 31286}, {"_path": "lib/python3.11/site-packages/chromadb/api/__pycache__/base_http_client.cpython-311.pyc", "path_type": "hardlink", "sha256": "9bf5258f383c904ed607d113bd6e204e3ec4802ce1ea29b1629c4167075a11eb", "sha256_in_prefix": "9bf5258f383c904ed607d113bd6e204e3ec4802ce1ea29b1629c4167075a11eb", "size_in_bytes": 5697}, {"_path": "lib/python3.11/site-packages/chromadb/api/__pycache__/client.cpython-311.pyc", "path_type": "hardlink", "sha256": "27ae3b6d122997c4414b5b35246c6c066721d5efe676ce715fe58289e8bbd2dd", "sha256_in_prefix": "27ae3b6d122997c4414b5b35246c6c066721d5efe676ce715fe58289e8bbd2dd", "size_in_bytes": 21503}, {"_path": "lib/python3.11/site-packages/chromadb/api/__pycache__/collection_configuration.cpython-311.pyc", "path_type": "hardlink", "sha256": "0c70b93ab0999a79baaffbcdf576b682181cb41b2d5a0b7111e8bb00d028f689", "sha256_in_prefix": "0c70b93ab0999a79baaffbcdf576b682181cb41b2d5a0b7111e8bb00d028f689", "size_in_bytes": 31078}, {"_path": "lib/python3.11/site-packages/chromadb/api/__pycache__/configuration.cpython-311.pyc", "path_type": "hardlink", "sha256": "5fa60eb5340a7e53b7f91d8a07a0dd255855c52f697ed02ffa83f34c8f323d67", "sha256_in_prefix": "5fa60eb5340a7e53b7f91d8a07a0dd255855c52f697ed02ffa83f34c8f323d67", "size_in_bytes": 20900}, {"_path": "lib/python3.11/site-packages/chromadb/api/__pycache__/fastapi.cpython-311.pyc", "path_type": "hardlink", "sha256": "042cbe6f9538d3f04177262ba50d0af1bae03af42c73e8d04771b99818674ebe", "sha256_in_prefix": "042cbe6f9538d3f04177262ba50d0af1bae03af42c73e8d04771b99818674ebe", "size_in_bytes": 28426}, {"_path": "lib/python3.11/site-packages/chromadb/api/__pycache__/rust.cpython-311.pyc", "path_type": "hardlink", "sha256": "e7acf3f14ea92b1e39306b611b418d8721a67ddc02e569590e5162dee995db3f", "sha256_in_prefix": "e7acf3f14ea92b1e39306b611b418d8721a67ddc02e569590e5162dee995db3f", "size_in_bytes": 22160}, {"_path": "lib/python3.11/site-packages/chromadb/api/__pycache__/segment.cpython-311.pyc", "path_type": "hardlink", "sha256": "b812734108b1b79fe7b0008f66d8eb75584bdcaccd10fdb0a481d8b13fc51233", "sha256_in_prefix": "b812734108b1b79fe7b0008f66d8eb75584bdcaccd10fdb0a481d8b13fc51233", "size_in_bytes": 40094}, {"_path": "lib/python3.11/site-packages/chromadb/api/__pycache__/shared_system_client.cpython-311.pyc", "path_type": "hardlink", "sha256": "5a8de1ac030852bdb054941b4dbfce1d727333396c7a01ae2a9ccab3abc92e8f", "sha256_in_prefix": "5a8de1ac030852bdb054941b4dbfce1d727333396c7a01ae2a9ccab3abc92e8f", "size_in_bytes": 5128}, {"_path": "lib/python3.11/site-packages/chromadb/api/__pycache__/types.cpython-311.pyc", "path_type": "hardlink", "sha256": "a1ac600120820bd679a0f52addd591c6312f915f3c39853758e073e64e8cf7b4", "sha256_in_prefix": "a1ac600120820bd679a0f52addd591c6312f915f3c39853758e073e64e8cf7b4", "size_in_bytes": 52462}, {"_path": "lib/python3.11/site-packages/chromadb/api/async_api.py", "path_type": "hardlink", "sha256": "16dfe8a50f7bde7616bee804decdb6b9dc8b954cf59547f9b0351a2e5794dcea", "sha256_in_prefix": "16dfe8a50f7bde7616bee804decdb6b9dc8b954cf59547f9b0351a2e5794dcea", "size_in_bytes": 23228}, {"_path": "lib/python3.11/site-packages/chromadb/api/async_client.py", "path_type": "hardlink", "sha256": "8e40060dcc845b4865dc692b110e5eb1c309db1e7f99244beff3956f60d6603e", "sha256_in_prefix": "8e40060dcc845b4865dc692b110e5eb1c309db1e7f99244beff3956f60d6603e", "size_in_bytes": 16687}, {"_path": "lib/python3.11/site-packages/chromadb/api/async_fastapi.py", "path_type": "hardlink", "sha256": "672eadb4985e1f965dbad07a3f967b36be2147a2700c2b6bbc536c477df96b78", "sha256_in_prefix": "672eadb4985e1f965dbad07a3f967b36be2147a2700c2b6bbc536c477df96b78", "size_in_bytes": 23533}, {"_path": "lib/python3.11/site-packages/chromadb/api/base_http_client.py", "path_type": "hardlink", "sha256": "7106b750e4e3377fb85618ef1bdbe6b8a964e4922284fd8a0200c48d5fe88416", "sha256_in_prefix": "7106b750e4e3377fb85618ef1bdbe6b8a964e4922284fd8a0200c48d5fe88416", "size_in_bytes": 3694}, {"_path": "lib/python3.11/site-packages/chromadb/api/client.py", "path_type": "hardlink", "sha256": "8e2208d9ab0a156b573048b8ddce33641b1b5bd999e62c921967704ff3f1b685", "sha256_in_prefix": "8e2208d9ab0a156b573048b8ddce33641b1b5bd999e62c921967704ff3f1b685", "size_in_bytes": 16817}, {"_path": "lib/python3.11/site-packages/chromadb/api/collection_configuration.py", "path_type": "hardlink", "sha256": "3d9c30fad43c294dcad147d82435ba3a43042c85a1b0ad601c848cacddcf1034", "sha256_in_prefix": "3d9c30fad43c294dcad147d82435ba3a43042c85a1b0ad601c848cacddcf1034", "size_in_bytes": 29413}, {"_path": "lib/python3.11/site-packages/chromadb/api/configuration.py", "path_type": "hardlink", "sha256": "da5b60c7b225bd397a8b676edb7c8c9747fe62f898f4989bf7177b473e27fbcd", "sha256_in_prefix": "da5b60c7b225bd397a8b676edb7c8c9747fe62f898f4989bf7177b473e27fbcd", "size_in_bytes": 15173}, {"_path": "lib/python3.11/site-packages/chromadb/api/fastapi.py", "path_type": "hardlink", "sha256": "171a2c247d0ee0d4f1ff2c96cbaa9791d2c535f62a6716bde7f405e6ab063ee6", "sha256_in_prefix": "171a2c247d0ee0d4f1ff2c96cbaa9791d2c535f62a6716bde7f405e6ab063ee6", "size_in_bytes": 22630}, {"_path": "lib/python3.11/site-packages/chromadb/api/models/AsyncCollection.py", "path_type": "hardlink", "sha256": "03028a3941dc68f346e4949298639b45e2694b6ffaa3c0f9b4b4278353635b70", "sha256_in_prefix": "03028a3941dc68f346e4949298639b45e2694b6ffaa3c0f9b4b4278353635b70", "size_in_bytes": 15313}, {"_path": "lib/python3.11/site-packages/chromadb/api/models/Collection.py", "path_type": "hardlink", "sha256": "c7e901b4c51b4b8e027697e977df687c2d66e01a9f2bc6623a837ac74e602ff7", "sha256_in_prefix": "c7e901b4c51b4b8e027697e977df687c2d66e01a9f2bc6623a837ac74e602ff7", "size_in_bytes": 15293}, {"_path": "lib/python3.11/site-packages/chromadb/api/models/CollectionCommon.py", "path_type": "hardlink", "sha256": "180654658f14ed5e82aa5f99f224053ba09bb3ddf111f9fef4376b2eb9b90541", "sha256_in_prefix": "180654658f14ed5e82aa5f99f224053ba09bb3ddf111f9fef4376b2eb9b90541", "size_in_bytes": 18725}, {"_path": "lib/python3.11/site-packages/chromadb/api/models/__pycache__/AsyncCollection.cpython-311.pyc", "path_type": "hardlink", "sha256": "af9f75878ad297c690fbf0bf25dda7a040106bfaa559d166d4c4ec05b20b4566", "sha256_in_prefix": "af9f75878ad297c690fbf0bf25dda7a040106bfaa559d166d4c4ec05b20b4566", "size_in_bytes": 16722}, {"_path": "lib/python3.11/site-packages/chromadb/api/models/__pycache__/Collection.cpython-311.pyc", "path_type": "hardlink", "sha256": "084801fa22e92594d751eacbf9549769450912cf1983b146c680e087955ee846", "sha256_in_prefix": "084801fa22e92594d751eacbf9549769450912cf1983b146c680e087955ee846", "size_in_bytes": 16357}, {"_path": "lib/python3.11/site-packages/chromadb/api/models/__pycache__/CollectionCommon.cpython-311.pyc", "path_type": "hardlink", "sha256": "9e863d6b073f0e7225e25692adb90ccbbf3a09411e1f76c7ec5d5be04a87d744", "sha256_in_prefix": "9e863d6b073f0e7225e25692adb90ccbbf3a09411e1f76c7ec5d5be04a87d744", "size_in_bytes": 21971}, {"_path": "lib/python3.11/site-packages/chromadb/api/rust.py", "path_type": "hardlink", "sha256": "4f04c5486b82440d888ccfe3fb1f1e60a91b53ca0f1496898c5f53074e0c1771", "sha256_in_prefix": "4f04c5486b82440d888ccfe3fb1f1e60a91b53ca0f1496898c5f53074e0c1771", "size_in_bytes": 18711}, {"_path": "lib/python3.11/site-packages/chromadb/api/segment.py", "path_type": "hardlink", "sha256": "53fea4ec1ad901d645b30e31f19ddb09575535ad47af0ca6771a65c771a03462", "sha256_in_prefix": "53fea4ec1ad901d645b30e31f19ddb09575535ad47af0ca6771a65c771a03462", "size_in_bytes": 34430}, {"_path": "lib/python3.11/site-packages/chromadb/api/shared_system_client.py", "path_type": "hardlink", "sha256": "99941482efa00c35144eae1c38bc071cae6e85fe75f54e43fa00e34802c9ec6d", "sha256_in_prefix": "99941482efa00c35144eae1c38bc071cae6e85fe75f54e43fa00e34802c9ec6d", "size_in_bytes": 3499}, {"_path": "lib/python3.11/site-packages/chromadb/api/types.py", "path_type": "hardlink", "sha256": "e7c710605b8e749deec5f0da32d20a16319a2016279fdbfe891ca6f220ac1928", "sha256_in_prefix": "e7c710605b8e749deec5f0da32d20a16319a2016279fdbfe891ca6f220ac1928", "size_in_bytes": 35266}, {"_path": "lib/python3.11/site-packages/chromadb/app.py", "path_type": "hardlink", "sha256": "1b30d8ef4633014aab5a0a86f64c43e08b41e0e9329e388620419c864d2184d0", "sha256_in_prefix": "1b30d8ef4633014aab5a0a86f64c43e08b41e0e9329e388620419c864d2184d0", "size_in_bytes": 168}, {"_path": "lib/python3.11/site-packages/chromadb/auth/__init__.py", "path_type": "hardlink", "sha256": "fac03384d845bbfe3003bb826e43bafa7e6454df4c168d5bd43c52f4a3b6a1e8", "sha256_in_prefix": "fac03384d845bbfe3003bb826e43bafa7e6454df4c168d5bd43c52f4a3b6a1e8", "size_in_bytes": 8151}, {"_path": "lib/python3.11/site-packages/chromadb/auth/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "68a32e85e6effe3b08c7ef089b29ed98184c39c5db1745da16151fe59b9a12a3", "sha256_in_prefix": "68a32e85e6effe3b08c7ef089b29ed98184c39c5db1745da16151fe59b9a12a3", "size_in_bytes": 12701}, {"_path": "lib/python3.11/site-packages/chromadb/auth/basic_authn/__init__.py", "path_type": "hardlink", "sha256": "d4f666f0b0ec62e85464c2c5142fbdbd0372fb019be569878ae1fa384c413438", "sha256_in_prefix": "d4f666f0b0ec62e85464c2c5142fbdbd0372fb019be569878ae1fa384c413438", "size_in_bytes": 5092}, {"_path": "lib/python3.11/site-packages/chromadb/auth/basic_authn/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "9ed34209cd59c46b68a11a9860aedfc876bdd6cf0d7d295c0464165af42e0830", "sha256_in_prefix": "9ed34209cd59c46b68a11a9860aedfc876bdd6cf0d7d295c0464165af42e0830", "size_in_bytes": 8152}, {"_path": "lib/python3.11/site-packages/chromadb/auth/simple_rbac_authz/__init__.py", "path_type": "hardlink", "sha256": "2dd5b1c6fe44285ee19d7fb7344045ed195d0d398148156a3f52265bc7420452", "sha256_in_prefix": "2dd5b1c6fe44285ee19d7fb7344045ed195d0d398148156a3f52265bc7420452", "size_in_bytes": 2643}, {"_path": "lib/python3.11/site-packages/chromadb/auth/simple_rbac_authz/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "6af2635f74ab9cec6858be49f7f670211708e3ab5c82f771564d04823f4be600", "sha256_in_prefix": "6af2635f74ab9cec6858be49f7f670211708e3ab5c82f771564d04823f4be600", "size_in_bytes": 3746}, {"_path": "lib/python3.11/site-packages/chromadb/auth/token_authn/__init__.py", "path_type": "hardlink", "sha256": "153dd2f2472f17d2e9fe156fa4cc8cb07a89d4aad80f0d8ae9f91c14013dab13", "sha256_in_prefix": "153dd2f2472f17d2e9fe156fa4cc8cb07a89d4aad80f0d8ae9f91c14013dab13", "size_in_bytes": 8328}, {"_path": "lib/python3.11/site-packages/chromadb/auth/token_authn/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "664afe7217889897dc700ccbfbd668ff430c496170501393471a3231af355966", "sha256_in_prefix": "664afe7217889897dc700ccbfbd668ff430c496170501393471a3231af355966", "size_in_bytes": 11671}, {"_path": "lib/python3.11/site-packages/chromadb/auth/utils/__init__.py", "path_type": "hardlink", "sha256": "9407a79944622f937f71e9bac54f75e6024fe3fd04bb5c80b565da24359b032c", "sha256_in_prefix": "9407a79944622f937f71e9bac54f75e6024fe3fd04bb5c80b565da24359b032c", "size_in_bytes": 3727}, {"_path": "lib/python3.11/site-packages/chromadb/auth/utils/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "6d1ac41d3d0f8a4ecf410f6fb268b6af68e80a3fe2f8fe30d1c6189fbe71b463", "sha256_in_prefix": "6d1ac41d3d0f8a4ecf410f6fb268b6af68e80a3fe2f8fe30d1c6189fbe71b463", "size_in_bytes": 3308}, {"_path": "lib/python3.11/site-packages/chromadb/base_types.py", "path_type": "hardlink", "sha256": "a416e04d039437edabb130481c6bc2a5e684e0808f8883421bc2bfe4132524b3", "sha256_in_prefix": "a416e04d039437edabb130481c6bc2a5e684e0808f8883421bc2bfe4132524b3", "size_in_bytes": 1258}, {"_path": "lib/python3.11/site-packages/chromadb/chromadb_rust_bindings.pyi", "path_type": "hardlink", "sha256": "73cfca6278497f7a2e93d80b92fce448f309f3f8b09a26e5eef3e0f3f2484338", "sha256_in_prefix": "73cfca6278497f7a2e93d80b92fce448f309f3f8b09a26e5eef3e0f3f2484338", "size_in_bytes": 5724}, {"_path": "lib/python3.11/site-packages/chromadb/cli/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/chromadb/cli/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "b248aee9cbe595ff32c1c3e00f49adecd63adfaee68b8bc01fe3fd83da22d305", "sha256_in_prefix": "b248aee9cbe595ff32c1c3e00f49adecd63adfaee68b8bc01fe3fd83da22d305", "size_in_bytes": 162}, {"_path": "lib/python3.11/site-packages/chromadb/cli/__pycache__/cli.cpython-311.pyc", "path_type": "hardlink", "sha256": "d64a8d59b549023d2e531dbc7232bccce27f4a6adca9ea84fe0de8a6c56bbb15", "sha256_in_prefix": "d64a8d59b549023d2e531dbc7232bccce27f4a6adca9ea84fe0de8a6c56bbb15", "size_in_bytes": 2968}, {"_path": "lib/python3.11/site-packages/chromadb/cli/__pycache__/utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "64bb94cc90292b04f0b883adf657acaa194bac05502d192fdadb764111a52ca4", "sha256_in_prefix": "64bb94cc90292b04f0b883adf657acaa194bac05502d192fdadb764111a52ca4", "size_in_bytes": 2549}, {"_path": "lib/python3.11/site-packages/chromadb/cli/cli.py", "path_type": "hardlink", "sha256": "a005eea5a24c39f3e1abdb6d679bd0d04ce4c0a3fba13fa26218caf309499fde", "sha256_in_prefix": "a005eea5a24c39f3e1abdb6d679bd0d04ce4c0a3fba13fa26218caf309499fde", "size_in_bytes": 1494}, {"_path": "lib/python3.11/site-packages/chromadb/cli/utils.py", "path_type": "hardlink", "sha256": "5b5d7080cd1e998024beb36529559f1be6bbcf8ec3b3fec2012f45e559b94acd", "sha256_in_prefix": "5b5d7080cd1e998024beb36529559f1be6bbcf8ec3b3fec2012f45e559b94acd", "size_in_bytes": 1247}, {"_path": "lib/python3.11/site-packages/chromadb/config.py", "path_type": "hardlink", "sha256": "950c2f2ce5c4ef0f47d085e413353ada0807faa8233b1ac06dd1d9374ec652b3", "sha256_in_prefix": "950c2f2ce5c4ef0f47d085e413353ada0807faa8233b1ac06dd1d9374ec652b3", "size_in_bytes": 18495}, {"_path": "lib/python3.11/site-packages/chromadb/db/__init__.py", "path_type": "hardlink", "sha256": "043f2b8c5fea25d358ab727c4fb283b83bba470f606da9f1f593391f0d01eecf", "sha256_in_prefix": "043f2b8c5fea25d358ab727c4fb283b83bba470f606da9f1f593391f0d01eecf", "size_in_bytes": 3040}, {"_path": "lib/python3.11/site-packages/chromadb/db/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "662c0c205f863dddb5af0f02cc56a693ca750c0e4eed217bf8a8d5ba4b0fcdc4", "sha256_in_prefix": "662c0c205f863dddb5af0f02cc56a693ca750c0e4eed217bf8a8d5ba4b0fcdc4", "size_in_bytes": 5455}, {"_path": "lib/python3.11/site-packages/chromadb/db/__pycache__/base.cpython-311.pyc", "path_type": "hardlink", "sha256": "49295f21743c229dd3676a0629d0e47a6f09110ec287683f5d2152d18952335b", "sha256_in_prefix": "49295f21743c229dd3676a0629d0e47a6f09110ec287683f5d2152d18952335b", "size_in_bytes": 10680}, {"_path": "lib/python3.11/site-packages/chromadb/db/__pycache__/migrations.cpython-311.pyc", "path_type": "hardlink", "sha256": "ee8caae0261edab26147f4537602b36ff7233a55d3d05d9bc360e1aef7753bd1", "sha256_in_prefix": "ee8caae0261edab26147f4537602b36ff7233a55d3d05d9bc360e1aef7753bd1", "size_in_bytes": 17048}, {"_path": "lib/python3.11/site-packages/chromadb/db/__pycache__/system.cpython-311.pyc", "path_type": "hardlink", "sha256": "bffd0ba0ed00913f760ec4f9d3728b1ff0ddc46766b639b7acbe09fcb27bed5c", "sha256_in_prefix": "bffd0ba0ed00913f760ec4f9d3728b1ff0ddc46766b639b7acbe09fcb27bed5c", "size_in_bytes": 8646}, {"_path": "lib/python3.11/site-packages/chromadb/db/base.py", "path_type": "hardlink", "sha256": "1373e41360b198530fb3b3d0ca1395e57991c244cfa461c7bfb52282981e070d", "sha256_in_prefix": "1373e41360b198530fb3b3d0ca1395e57991c244cfa461c7bfb52282981e070d", "size_in_bytes": 5765}, {"_path": "lib/python3.11/site-packages/chromadb/db/impl/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/chromadb/db/impl/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "56921f815f827d1534a73433de62ac19da08dd0be7081490f7b69ee8eb270352", "sha256_in_prefix": "56921f815f827d1534a73433de62ac19da08dd0be7081490f7b69ee8eb270352", "size_in_bytes": 166}, {"_path": "lib/python3.11/site-packages/chromadb/db/impl/__pycache__/sqlite.cpython-311.pyc", "path_type": "hardlink", "sha256": "721c741a448808dd538153ecdc518d2ff861679b178e63681cd418d681e74144", "sha256_in_prefix": "721c741a448808dd538153ecdc518d2ff861679b178e63681cd418d681e74144", "size_in_bytes": 16784}, {"_path": "lib/python3.11/site-packages/chromadb/db/impl/__pycache__/sqlite_pool.cpython-311.pyc", "path_type": "hardlink", "sha256": "ea1e548ba1bd3912c62210abfde85b5d474df34966def09e0568bbdd4983b805", "sha256_in_prefix": "ea1e548ba1bd3912c62210abfde85b5d474df34966def09e0568bbdd4983b805", "size_in_bytes": 10140}, {"_path": "lib/python3.11/site-packages/chromadb/db/impl/grpc/__pycache__/client.cpython-311.pyc", "path_type": "hardlink", "sha256": "bcf69803375c4b71b6880d01b6313a0008b33839d700cc7af670df689c4ac109", "sha256_in_prefix": "bcf69803375c4b71b6880d01b6313a0008b33839d700cc7af670df689c4ac109", "size_in_bytes": 28063}, {"_path": "lib/python3.11/site-packages/chromadb/db/impl/grpc/__pycache__/server.cpython-311.pyc", "path_type": "hardlink", "sha256": "3fdf45ea817d0e35ef33870c824a15eb19a7e9ee9af1aa229f38ee0daa5cb0a0", "sha256_in_prefix": "3fdf45ea817d0e35ef33870c824a15eb19a7e9ee9af1aa229f38ee0daa5cb0a0", "size_in_bytes": 26671}, {"_path": "lib/python3.11/site-packages/chromadb/db/impl/grpc/client.py", "path_type": "hardlink", "sha256": "fc92a89e8a3cb16e659e1f96d127a53a67021e11368ec5889b81fbfeff739d8d", "sha256_in_prefix": "fc92a89e8a3cb16e659e1f96d127a53a67021e11368ec5889b81fbfeff739d8d", "size_in_bytes": 20313}, {"_path": "lib/python3.11/site-packages/chromadb/db/impl/grpc/server.py", "path_type": "hardlink", "sha256": "42dc4029f7ea36f06b6f432424cbe6f4dd868386b12dba2fe2148fdd3adc6af4", "sha256_in_prefix": "42dc4029f7ea36f06b6f432424cbe6f4dd868386b12dba2fe2148fdd3adc6af4", "size_in_bytes": 20693}, {"_path": "lib/python3.11/site-packages/chromadb/db/impl/sqlite.py", "path_type": "hardlink", "sha256": "f32ce944e961633f9f9a037cd80559b338b14d98195d2c0a3bf7c7ff8e966c47", "sha256_in_prefix": "f32ce944e961633f9f9a037cd80559b338b14d98195d2c0a3bf7c7ff8e966c47", "size_in_bytes": 9430}, {"_path": "lib/python3.11/site-packages/chromadb/db/impl/sqlite_pool.py", "path_type": "hardlink", "sha256": "635a7969617ead3d69a310217c55b1def62fad2ea22cec49a6fa742d84272e82", "sha256_in_prefix": "635a7969617ead3d69a310217c55b1def62fad2ea22cec49a6fa742d84272e82", "size_in_bytes": 5372}, {"_path": "lib/python3.11/site-packages/chromadb/db/migrations.py", "path_type": "hardlink", "sha256": "1c2da9617624b97c636fe987471a9c883f179a49e4250f4c51b46e204de91a76", "sha256_in_prefix": "1c2da9617624b97c636fe987471a9c883f179a49e4250f4c51b46e204de91a76", "size_in_bytes": 9630}, {"_path": "lib/python3.11/site-packages/chromadb/db/mixins/__pycache__/embeddings_queue.cpython-311.pyc", "path_type": "hardlink", "sha256": "79064935c0ec8725e6b3f22b1c2e85247b91c6db9b48754de9e2ec42e5380ca4", "sha256_in_prefix": "79064935c0ec8725e6b3f22b1c2e85247b91c6db9b48754de9e2ec42e5380ca4", "size_in_bytes": 27746}, {"_path": "lib/python3.11/site-packages/chromadb/db/mixins/__pycache__/sysdb.cpython-311.pyc", "path_type": "hardlink", "sha256": "89477579833947b2a0a8d28415e47e622afae734b746e72f4cd500560bbaa74f", "sha256_in_prefix": "89477579833947b2a0a8d28415e47e622afae734b746e72f4cd500560bbaa74f", "size_in_bytes": 49273}, {"_path": "lib/python3.11/site-packages/chromadb/db/mixins/embeddings_queue.py", "path_type": "hardlink", "sha256": "662088a8acd22af98d03d36a6d4b7cf827c262789363f9c18e7ffbed5be9da4f", "sha256_in_prefix": "662088a8acd22af98d03d36a6d4b7cf827c262789363f9c18e7ffbed5be9da4f", "size_in_bytes": 19325}, {"_path": "lib/python3.11/site-packages/chromadb/db/mixins/sysdb.py", "path_type": "hardlink", "sha256": "97a81d8bc3aa81963cfc9b5765c4140076c93c4b7840a4571146eed83d4ee8d4", "sha256_in_prefix": "97a81d8bc3aa81963cfc9b5765c4140076c93c4b7840a4571146eed83d4ee8d4", "size_in_bytes": 36799}, {"_path": "lib/python3.11/site-packages/chromadb/db/system.py", "path_type": "hardlink", "sha256": "5f597df743bf6306a9f23bc9c3ed5bfc1830588f0868a69430eca14e9be251be", "sha256_in_prefix": "5f597df743bf6306a9f23bc9c3ed5bfc1830588f0868a69430eca14e9be251be", "size_in_bytes": 6135}, {"_path": "lib/python3.11/site-packages/chromadb/errors.py", "path_type": "hardlink", "sha256": "c78b7b5ea813eed4e4f0a43b50f86162f147b9ed9878f6ca11ce85ec2645f171", "sha256_in_prefix": "c78b7b5ea813eed4e4f0a43b50f86162f147b9ed9878f6ca11ce85ec2645f171", "size_in_bytes": 4018}, {"_path": "lib/python3.11/site-packages/chromadb/execution/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/chromadb/execution/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "676b4fde0cf68c9ab3f0b0bd4233d2df3324f8fa4516b8e5958fef8f9b9d7d2e", "sha256_in_prefix": "676b4fde0cf68c9ab3f0b0bd4233d2df3324f8fa4516b8e5958fef8f9b9d7d2e", "size_in_bytes": 168}, {"_path": "lib/python3.11/site-packages/chromadb/execution/executor/__pycache__/abstract.cpython-311.pyc", "path_type": "hardlink", "sha256": "3ee91909bd51b80c435cde94ed5c454190bbb0f15a48c328cfa03e52b43a2135", "sha256_in_prefix": "3ee91909bd51b80c435cde94ed5c454190bbb0f15a48c328cfa03e52b43a2135", "size_in_bytes": 1349}, {"_path": "lib/python3.11/site-packages/chromadb/execution/executor/__pycache__/distributed.cpython-311.pyc", "path_type": "hardlink", "sha256": "0f391fedfc77a516301ce6c0458ac76b937d88179f36a36e479cde156954d8a5", "sha256_in_prefix": "0f391fedfc77a516301ce6c0458ac76b937d88179f36a36e479cde156954d8a5", "size_in_bytes": 15069}, {"_path": "lib/python3.11/site-packages/chromadb/execution/executor/__pycache__/local.cpython-311.pyc", "path_type": "hardlink", "sha256": "238c4e4f69f13efeb5a3a865e089789aa9f8dca572cecb739a209af1e11857b4", "sha256_in_prefix": "238c4e4f69f13efeb5a3a865e089789aa9f8dca572cecb739a209af1e11857b4", "size_in_bytes": 13169}, {"_path": "lib/python3.11/site-packages/chromadb/execution/executor/abstract.py", "path_type": "hardlink", "sha256": "b61c3e8f0357d45da2bb0621ddb245ac3c6d7017bee5a886766c1d98f15b2b6a", "sha256_in_prefix": "b61c3e8f0357d45da2bb0621ddb245ac3c6d7017bee5a886766c1d98f15b2b6a", "size_in_bytes": 470}, {"_path": "lib/python3.11/site-packages/chromadb/execution/executor/distributed.py", "path_type": "hardlink", "sha256": "5ed0136b1cc37a8de7061975d4be89d5bc9e9d688103f41a36930d41aa180c80", "sha256_in_prefix": "5ed0136b1cc37a8de7061975d4be89d5bc9e9d688103f41a36930d41aa180c80", "size_in_bytes": 9035}, {"_path": "lib/python3.11/site-packages/chromadb/execution/executor/local.py", "path_type": "hardlink", "sha256": "c628ee0abe28cef3235f64f458ff42430b2cf407ed85c485ed0403d8825bfb38", "sha256_in_prefix": "c628ee0abe28cef3235f64f458ff42430b2cf407ed85c485ed0403d8825bfb38", "size_in_bytes": 7518}, {"_path": "lib/python3.11/site-packages/chromadb/execution/expression/__pycache__/operator.cpython-311.pyc", "path_type": "hardlink", "sha256": "c24577b245a926d85e1a590bd691b8dd4599442393b1047a7427048e45b9b88a", "sha256_in_prefix": "c24577b245a926d85e1a590bd691b8dd4599442393b1047a7427048e45b9b88a", "size_in_bytes": 3663}, {"_path": "lib/python3.11/site-packages/chromadb/execution/expression/__pycache__/plan.cpython-311.pyc", "path_type": "hardlink", "sha256": "040d0f590719853cfb3e6be6a2a4c3ad06f144776b65bfb20188f06d3e7b0d96", "sha256_in_prefix": "040d0f590719853cfb3e6be6a2a4c3ad06f144776b65bfb20188f06d3e7b0d96", "size_in_bytes": 1732}, {"_path": "lib/python3.11/site-packages/chromadb/execution/expression/operator.py", "path_type": "hardlink", "sha256": "0512c184bf71b38ba5d423cc6e902746008fe3b090eef4e805071ea6e781f503", "sha256_in_prefix": "0512c184bf71b38ba5d423cc6e902746008fe3b090eef4e805071ea6e781f503", "size_in_bytes": 1439}, {"_path": "lib/python3.11/site-packages/chromadb/execution/expression/plan.py", "path_type": "hardlink", "sha256": "323d498b03a7c347983fc088f91fb7f97c10772215e0a25aea3ac993203df30d", "sha256_in_prefix": "323d498b03a7c347983fc088f91fb7f97c10772215e0a25aea3ac993203df30d", "size_in_bytes": 550}, {"_path": "lib/python3.11/site-packages/chromadb/experimental/density_relevance.ipynb", "path_type": "hardlink", "sha256": "192ea721bec4df13c176b14280eaec203f07e53622f735a24834362766991d65", "sha256_in_prefix": "192ea721bec4df13c176b14280eaec203f07e53622f735a24834362766991d65", "size_in_bytes": 368700}, {"_path": "lib/python3.11/site-packages/chromadb/ingest/__init__.py", "path_type": "hardlink", "sha256": "b35d8a57592b914072073f423d8ce316486c7d49b5a45d4dba6d2873d7067d50", "sha256_in_prefix": "b35d8a57592b914072073f423d8ce316486c7d49b5a45d4dba6d2873d7067d50", "size_in_bytes": 4299}, {"_path": "lib/python3.11/site-packages/chromadb/ingest/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "db8b7df06b6bdeeb3519fbbaa586c10385a31f88799e65fdef06948248b40f47", "sha256_in_prefix": "db8b7df06b6bdeeb3519fbbaa586c10385a31f88799e65fdef06948248b40f47", "size_in_bytes": 6738}, {"_path": "lib/python3.11/site-packages/chromadb/ingest/impl/__pycache__/utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "2540fbffd2c045316dcd4bda43df4474f6f9b40adb308779a917dabdce1d4048", "sha256_in_prefix": "2540fbffd2c045316dcd4bda43df4474f6f9b40adb308779a917dabdce1d4048", "size_in_bytes": 3238}, {"_path": "lib/python3.11/site-packages/chromadb/ingest/impl/utils.py", "path_type": "hardlink", "sha256": "e204a2db8a9835683e78281eb01dd4fcdab1d2567d830c93255be6f519e0ba50", "sha256_in_prefix": "e204a2db8a9835683e78281eb01dd4fcdab1d2567d830c93255be6f519e0ba50", "size_in_bytes": 1833}, {"_path": "lib/python3.11/site-packages/chromadb/log_config.yml", "path_type": "hardlink", "sha256": "90aea0442fa1f0ab2e04771ae07b3fa8f97f20cd02bd928e1e8a8a480a20546f", "sha256_in_prefix": "90aea0442fa1f0ab2e04771ae07b3fa8f97f20cd02bd928e1e8a8a480a20546f", "size_in_bytes": 921}, {"_path": "lib/python3.11/site-packages/chromadb/logservice/__pycache__/logservice.cpython-311.pyc", "path_type": "hardlink", "sha256": "18ce14907308102522c09786158857aa2f818b4628fea5013db4a944cab00067", "sha256_in_prefix": "18ce14907308102522c09786158857aa2f818b4628fea5013db4a944cab00067", "size_in_bytes": 10316}, {"_path": "lib/python3.11/site-packages/chromadb/logservice/logservice.py", "path_type": "hardlink", "sha256": "4741d91125814761584a8fdcc300555e1bed904dcb52b8c415e3504319185bfa", "sha256_in_prefix": "4741d91125814761584a8fdcc300555e1bed904dcb52b8c415e3504319185bfa", "size_in_bytes": 5870}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "4df17a27fa2997a6394388ce20916792cbda952c6b4053639b0f7028bd00a42d", "sha256_in_prefix": "4df17a27fa2997a6394388ce20916792cbda952c6b4053639b0f7028bd00a42d", "size_in_bytes": 169}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/embeddings_queue/00001-embeddings.sqlite.sql", "path_type": "hardlink", "sha256": "fbcbdac621c6bdebe561ae0bd3eac840737af822f4edc0d567b94e2066eecf28", "sha256_in_prefix": "fbcbdac621c6bdebe561ae0bd3eac840737af822f4edc0d567b94e2066eecf28", "size_in_bytes": 261}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/embeddings_queue/00002-embeddings-queue-config.sqlite.sql", "path_type": "hardlink", "sha256": "788fe9b405526821be2f4e305da3891724871859a2ea8f48c1f8182745658fd9", "sha256_in_prefix": "788fe9b405526821be2f4e305da3891724871859a2ea8f48c1f8182745658fd9", "size_in_bytes": 95}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/metadb/00001-embedding-metadata.sqlite.sql", "path_type": "hardlink", "sha256": "0e7477e62bd40830f28f9986f0703597817877503044324efe8c9ecc85392845", "sha256_in_prefix": "0e7477e62bd40830f28f9986f0703597817877503044324efe8c9ecc85392845", "size_in_bytes": 600}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/metadb/00002-embedding-metadata.sqlite.sql", "path_type": "hardlink", "sha256": "337de70bb89c42bf01747642fbe9310f8ce38635ab50f2ce82ee98ac664e759b", "sha256_in_prefix": "337de70bb89c42bf01747642fbe9310f8ce38635ab50f2ce82ee98ac664e759b", "size_in_bytes": 334}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/metadb/00003-full-text-tokenize.sqlite.sql", "path_type": "hardlink", "sha256": "13fd5839823c8f1e236431c2f716a0aef02c5bfd80c35c15963496924ea8dda4", "sha256_in_prefix": "13fd5839823c8f1e236431c2f716a0aef02c5bfd80c35c15963496924ea8dda4", "size_in_bytes": 236}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/metadb/00004-metadata-indices.sqlite.sql", "path_type": "hardlink", "sha256": "46e874a3bf99ae95611fd8f781b9aecccc8979dc4e06c91e86a6b56dac378443", "sha256_in_prefix": "46e874a3bf99ae95611fd8f781b9aecccc8979dc4e06c91e86a6b56dac378443", "size_in_bytes": 387}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/metadb/00005-max-seq-id-int.sqlite.sql", "path_type": "hardlink", "sha256": "363f44b68fcda609e239e1e23263d655a0439a490394da3d690279939a716e45", "sha256_in_prefix": "363f44b68fcda609e239e1e23263d655a0439a490394da3d690279939a716e45", "size_in_bytes": 1449}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/sysdb/00001-collections.sqlite.sql", "path_type": "hardlink", "sha256": "dfa5720fb290880c9a7abac9262024cd32da703a312471b5d64eb29613a055bd", "sha256_in_prefix": "dfa5720fb290880c9a7abac9262024cd32da703a312471b5d64eb29613a055bd", "size_in_bytes": 355}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/sysdb/00002-segments.sqlite.sql", "path_type": "hardlink", "sha256": "e459a7f668f9c9a38562e92211feeccc4549d5a04aca2972397291496d564a1c", "sha256_in_prefix": "e459a7f668f9c9a38562e92211feeccc4549d5a04aca2972397291496d564a1c", "size_in_bytes": 385}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/sysdb/00003-collection-dimension.sqlite.sql", "path_type": "hardlink", "sha256": "465c9dd473bf084e6acddcfd15c2587ecaf325b3043af49aaec87e4006bde8f3", "sha256_in_prefix": "465c9dd473bf084e6acddcfd15c2587ecaf325b3043af49aaec87e4006bde8f3", "size_in_bytes": 54}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/sysdb/00004-tenants-databases.sqlite.sql", "path_type": "hardlink", "sha256": "0da5897ebd71a72ee540c1011265671f5ef6eb21da9eaaa1e58408d8a0480dc3", "sha256_in_prefix": "0da5897ebd71a72ee540c1011265671f5ef6eb21da9eaaa1e58408d8a0480dc3", "size_in_bytes": 1228}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/sysdb/00005-remove-topic.sqlite.sql", "path_type": "hardlink", "sha256": "60a348031c4bcf98d611ca6362fa8b754cc854463ba65258aab366327a237343", "sha256_in_prefix": "60a348031c4bcf98d611ca6362fa8b754cc854463ba65258aab366327a237343", "size_in_bytes": 152}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/sysdb/00006-collection-segment-metadata.sqlite.sql", "path_type": "hardlink", "sha256": "5768db7d49f555920dacc7012f173cfdd2eb4c3f71af910d3fd653661105a694", "sha256_in_prefix": "5768db7d49f555920dacc7012f173cfdd2eb4c3f71af910d3fd653661105a694", "size_in_bytes": 396}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/sysdb/00007-collection-config.sqlite.sql", "path_type": "hardlink", "sha256": "e787b736706f173e9b279505e0e7027f57a27031dfe7668a167677030715d5b9", "sha256_in_prefix": "e787b736706f173e9b279505e0e7027f57a27031dfe7668a167677030715d5b9", "size_in_bytes": 106}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/sysdb/00008-maintenance-log.sqlite.sql", "path_type": "hardlink", "sha256": "66ad31eefddd3070be27963a053319f0fe9003e5dd1f38b50c42e2ed970812ad", "sha256_in_prefix": "66ad31eefddd3070be27963a053319f0fe9003e5dd1f38b50c42e2ed970812ad", "size_in_bytes": 248}, {"_path": "lib/python3.11/site-packages/chromadb/migrations/sysdb/00009-segment-collection-not-null.sqlite.sql", "path_type": "hardlink", "sha256": "b5564781c2b24dfdc79024a2e8f17ad9830127cc9b43876d9428757c093bc99e", "sha256_in_prefix": "b5564781c2b24dfdc79024a2e8f17ad9830127cc9b43876d9428757c093bc99e", "size_in_bytes": 327}, {"_path": "lib/python3.11/site-packages/chromadb/proto/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/chromadb/proto/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "d00b9bd145315168bc5fbe9c2bfd2146bf5fffc5bb317d3ec143d0d3aaa46c69", "sha256_in_prefix": "d00b9bd145315168bc5fbe9c2bfd2146bf5fffc5bb317d3ec143d0d3aaa46c69", "size_in_bytes": 164}, {"_path": "lib/python3.11/site-packages/chromadb/proto/__pycache__/convert.cpython-311.pyc", "path_type": "hardlink", "sha256": "fbddea77bf3b318471b6d5343290d6300a5aba5faa1ccadb3c84a32e2202e6ad", "sha256_in_prefix": "fbddea77bf3b318471b6d5343290d6300a5aba5faa1ccadb3c84a32e2202e6ad", "size_in_bytes": 35044}, {"_path": "lib/python3.11/site-packages/chromadb/proto/__pycache__/utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "85347f8612bf645f0e0be7819ce38a33d67610d15ad2c041fb4d1c5c6b73af18", "sha256_in_prefix": "85347f8612bf645f0e0be7819ce38a33d67610d15ad2c041fb4d1c5c6b73af18", "size_in_bytes": 4340}, {"_path": "lib/python3.11/site-packages/chromadb/proto/convert.py", "path_type": "hardlink", "sha256": "c5b6fa94452ae69d80ad4bf89f0912457cd28567294ad6b1a3ebb5b2032f57b0", "sha256_in_prefix": "c5b6fa94452ae69d80ad4bf89f0912457cd28567294ad6b1a3ebb5b2032f57b0", "size_in_bytes": 26615}, {"_path": "lib/python3.11/site-packages/chromadb/proto/utils.py", "path_type": "hardlink", "sha256": "456f6444db1f70bb1f0435d6a70c103986d0c1c20e2f1b3c26f4fefcc4c7228b", "sha256_in_prefix": "456f6444db1f70bb1f0435d6a70c103986d0c1c20e2f1b3c26f4fefcc4c7228b", "size_in_bytes": 2570}, {"_path": "lib/python3.11/site-packages/chromadb/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/chromadb/quota/__init__.py", "path_type": "hardlink", "sha256": "45d4b1baaf0696bf4c2a62ca6781df3be26c3c9f6ac5d9bac55a0fc4c20a6e5c", "sha256_in_prefix": "45d4b1baaf0696bf4c2a62ca6781df3be26c3c9f6ac5d9bac55a0fc4c20a6e5c", "size_in_bytes": 1817}, {"_path": "lib/python3.11/site-packages/chromadb/quota/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "fe468ca94d88f862df287135d33e221f27110e6dd50734f300f30a22c1ec5838", "sha256_in_prefix": "fe468ca94d88f862df287135d33e221f27110e6dd50734f300f30a22c1ec5838", "size_in_bytes": 3458}, {"_path": "lib/python3.11/site-packages/chromadb/quota/simple_quota_enforcer/__init__.py", "path_type": "hardlink", "sha256": "026ed31c5eb499ec5018bbd3a5206c60e7f3361bc47b83385a5538dfb1f381ef", "sha256_in_prefix": "026ed31c5eb499ec5018bbd3a5206c60e7f3361bc47b83385a5538dfb1f381ef", "size_in_bytes": 1495}, {"_path": "lib/python3.11/site-packages/chromadb/quota/simple_quota_enforcer/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "b2383b4e2140f2d6ca1bb38955ccb26503dfe3e6f436ed8ba4efefa2d6cd1db1", "sha256_in_prefix": "b2383b4e2140f2d6ca1bb38955ccb26503dfe3e6f436ed8ba4efefa2d6cd1db1", "size_in_bytes": 3071}, {"_path": "lib/python3.11/site-packages/chromadb/rate_limit/__init__.py", "path_type": "hardlink", "sha256": "24a33388f099911dc4dd76e49814dae266283221f5cb099154e833ff5c3fd3f7", "sha256_in_prefix": "24a33388f099911dc4dd76e49814dae266283221f5cb099154e833ff5c3fd3f7", "size_in_bytes": 889}, {"_path": "lib/python3.11/site-packages/chromadb/rate_limit/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "498748fec7cb5a343814b07142f8dadab1e52d0637599cf8425dd634fbd43ea1", "sha256_in_prefix": "498748fec7cb5a343814b07142f8dadab1e52d0637599cf8425dd634fbd43ea1", "size_in_bytes": 2329}, {"_path": "lib/python3.11/site-packages/chromadb/rate_limit/simple_rate_limit/__init__.py", "path_type": "hardlink", "sha256": "d2c0782017ddcce433286301cca9152430ab0b2e5e9a4113603247d74b744771", "sha256_in_prefix": "d2c0782017ddcce433286301cca9152430ab0b2e5e9a4113603247d74b744771", "size_in_bytes": 1177}, {"_path": "lib/python3.11/site-packages/chromadb/rate_limit/simple_rate_limit/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "7ab3c83f8d686fa6ef6db51ea8135b0da0fd7e28138b6dab2e0a5bdf4c7807e0", "sha256_in_prefix": "7ab3c83f8d686fa6ef6db51ea8135b0da0fd7e28138b6dab2e0a5bdf4c7807e0", "size_in_bytes": 3231}, {"_path": "lib/python3.11/site-packages/chromadb/segment/__init__.py", "path_type": "hardlink", "sha256": "f5f0ccae3894d2f6113a2a1c84439fc9993b7d5530ecb6c16747106d528c959c", "sha256_in_prefix": "f5f0ccae3894d2f6113a2a1c84439fc9993b7d5530ecb6c16747106d528c959c", "size_in_bytes": 4020}, {"_path": "lib/python3.11/site-packages/chromadb/segment/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "0c11ddde2c1b5bbf43cf242f91e8b3887d948ea384f22589d8e3fdaaa326ee29", "sha256_in_prefix": "0c11ddde2c1b5bbf43cf242f91e8b3887d948ea384f22589d8e3fdaaa326ee29", "size_in_bytes": 7047}, {"_path": "lib/python3.11/site-packages/chromadb/segment/distributed/__init__.py", "path_type": "hardlink", "sha256": "05999860221af14ca6af227f183a83dec778f22fae72afd536dc275ec2916caf", "sha256_in_prefix": "05999860221af14ca6af227f183a83dec778f22fae72afd536dc275ec2916caf", "size_in_bytes": 2703}, {"_path": "lib/python3.11/site-packages/chromadb/segment/distributed/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "43300f9af49fac8e08cabe3abff7a7ef7c89983e78dd7a9d0611b119f191fb08", "sha256_in_prefix": "43300f9af49fac8e08cabe3abff7a7ef7c89983e78dd7a9d0611b119f191fb08", "size_in_bytes": 5133}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "de0416b67c3602e6611456109144d9cec7a1fba2d75d5c5398f6c5bfd83a8abe", "sha256_in_prefix": "de0416b67c3602e6611456109144d9cec7a1fba2d75d5c5398f6c5bfd83a8abe", "size_in_bytes": 171}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/distributed/__pycache__/segment_directory.cpython-311.pyc", "path_type": "hardlink", "sha256": "35a9b7930fc2e28d5c1183b78eaacdcb8eab441cfbc8f8331e3691ea3c0db03e", "sha256_in_prefix": "35a9b7930fc2e28d5c1183b78eaacdcb8eab441cfbc8f8331e3691ea3c0db03e", "size_in_bytes": 19257}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/distributed/segment_directory.py", "path_type": "hardlink", "sha256": "b38b18dee496e5d2eb378d569b5f8b3084655108d1b26d162d3dbc9e3bdec07f", "sha256_in_prefix": "b38b18dee496e5d2eb378d569b5f8b3084655108d1b26d162d3dbc9e3bdec07f", "size_in_bytes": 13393}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/manager/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/manager/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "dc6d78c8c52ca4f267228ed4fd744236df00e65235c0a27e72897561aff975bf", "sha256_in_prefix": "dc6d78c8c52ca4f267228ed4fd744236df00e65235c0a27e72897561aff975bf", "size_in_bytes": 179}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/manager/__pycache__/distributed.cpython-311.pyc", "path_type": "hardlink", "sha256": "62b79f75a2bda6f710dc581a67288cda2b11dfeea876c6911485e62e94403a2d", "sha256_in_prefix": "62b79f75a2bda6f710dc581a67288cda2b11dfeea876c6911485e62e94403a2d", "size_in_bytes": 4938}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/manager/__pycache__/local.cpython-311.pyc", "path_type": "hardlink", "sha256": "b7381255bb6bcf154a64683f61d7e491cef4c81bd1b37597a94d7208f35c80b1", "sha256_in_prefix": "b7381255bb6bcf154a64683f61d7e491cef4c81bd1b37597a94d7208f35c80b1", "size_in_bytes": 16201}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/manager/cache/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/manager/cache/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "2943b8742ae9b89937e5ddecf678e1d185d3d9ee6f5d8337d27916d4ab24cf18", "sha256_in_prefix": "2943b8742ae9b89937e5ddecf678e1d185d3d9ee6f5d8337d27916d4ab24cf18", "size_in_bytes": 185}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/manager/cache/__pycache__/cache.cpython-311.pyc", "path_type": "hardlink", "sha256": "590638c818cc666675282edfc2eb500fa97dd8d4c4ddbf8f858d147d5491c9d2", "sha256_in_prefix": "590638c818cc666675282edfc2eb500fa97dd8d4c4ddbf8f858d147d5491c9d2", "size_in_bytes": 8670}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/manager/cache/cache.py", "path_type": "hardlink", "sha256": "2ab331f17b450d530965908d52e60766b4f08d54eabd7e66811136514490200b", "sha256_in_prefix": "2ab331f17b450d530965908d52e60766b4f08d54eabd7e66811136514490200b", "size_in_bytes": 3435}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/manager/distributed.py", "path_type": "hardlink", "sha256": "8ba74319d7a1bc47b7da1d385c03c8a01f0a0b2a6e8f71a06417b6493bfa6c34", "sha256_in_prefix": "8ba74319d7a1bc47b7da1d385c03c8a01f0a0b2a6e8f71a06417b6493bfa6c34", "size_in_bytes": 2986}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/manager/local.py", "path_type": "hardlink", "sha256": "4bb4ef45e6a74f8d5c3f95dc5daef21662e88c56def39cb7569fc1e29df2feb5", "sha256_in_prefix": "4bb4ef45e6a74f8d5c3f95dc5daef21662e88c56def39cb7569fc1e29df2feb5", "size_in_bytes": 10593}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/metadata/__pycache__/sqlite.cpython-311.pyc", "path_type": "hardlink", "sha256": "232acb1e8fb1269c7b7897584e73911cbd6106a64013689a02562bb0600f25e2", "sha256_in_prefix": "232acb1e8fb1269c7b7897584e73911cbd6106a64013689a02562bb0600f25e2", "size_in_bytes": 39675}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/metadata/sqlite.py", "path_type": "hardlink", "sha256": "973961b8b83baf6a91a84ff047bf8e5c33f5e85edac8f330e2b2d17027542249", "sha256_in_prefix": "973961b8b83baf6a91a84ff047bf8e5c33f5e85edac8f330e2b2d17027542249", "size_in_bytes": 25786}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/vector/__pycache__/batch.cpython-311.pyc", "path_type": "hardlink", "sha256": "d377f858f6f7b3521ad29df1259a72c0ed63ce728f7577cecb5afc49694dbb13", "sha256_in_prefix": "d377f858f6f7b3521ad29df1259a72c0ed63ce728f7577cecb5afc49694dbb13", "size_in_bytes": 6398}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/vector/__pycache__/brute_force_index.cpython-311.pyc", "path_type": "hardlink", "sha256": "0f234e876a8730fd154ea26abad7f8f4d9ecc0356b9c15bd1b967d49689b609f", "sha256_in_prefix": "0f234e876a8730fd154ea26abad7f8f4d9ecc0356b9c15bd1b967d49689b609f", "size_in_bytes": 8544}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/vector/__pycache__/hnsw_params.cpython-311.pyc", "path_type": "hardlink", "sha256": "545dcd17f1598b5a74f2cf94c3af6f467f5d63a0fc31768b6597b8ea132b9206", "sha256_in_prefix": "545dcd17f1598b5a74f2cf94c3af6f467f5d63a0fc31768b6597b8ea132b9206", "size_in_bytes": 6977}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/vector/__pycache__/local_hnsw.cpython-311.pyc", "path_type": "hardlink", "sha256": "8c0210962e2ff4fa29ba9e05be4e268e8d03a73da360e43ebc30ff014b687238", "sha256_in_prefix": "8c0210962e2ff4fa29ba9e05be4e268e8d03a73da360e43ebc30ff014b687238", "size_in_bytes": 17598}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/vector/__pycache__/local_persistent_hnsw.cpython-311.pyc", "path_type": "hardlink", "sha256": "c8ce8ccaebfe1fa89031dafa251e4befddca265cf9e3d4ecad315104e3a8df08", "sha256_in_prefix": "c8ce8ccaebfe1fa89031dafa251e4befddca265cf9e3d4ecad315104e3a8df08", "size_in_bytes": 29361}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/vector/batch.py", "path_type": "hardlink", "sha256": "66e77d0ac959e46f8a5a747a704c88254fc2932238abceddb2f33b78ddb06128", "sha256_in_prefix": "66e77d0ac959e46f8a5a747a704c88254fc2932238abceddb2f33b78ddb06128", "size_in_bytes": 4213}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/vector/brute_force_index.py", "path_type": "hardlink", "sha256": "175946a71e457bbec938a182353e05e317dc512279596c5a5a770714dce76cf2", "sha256_in_prefix": "175946a71e457bbec938a182353e05e317dc512279596c5a5a770714dce76cf2", "size_in_bytes": 5420}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/vector/hnsw_params.py", "path_type": "hardlink", "sha256": "5ac3a0c20948f0dedf6122e94cb1162ce5d0870a49b118110f5f6e9cee3ec8de", "sha256_in_prefix": "5ac3a0c20948f0dedf6122e94cb1162ce5d0870a49b118110f5f6e9cee3ec8de", "size_in_bytes": 3162}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/vector/local_hnsw.py", "path_type": "hardlink", "sha256": "e75e8a5746d206af222584daa60b5e14833d3f7d6d465efe3b5f2e5d26649186", "sha256_in_prefix": "e75e8a5746d206af222584daa60b5e14833d3f7d6d465efe3b5f2e5d26649186", "size_in_bytes": 12046}, {"_path": "lib/python3.11/site-packages/chromadb/segment/impl/vector/local_persistent_hnsw.py", "path_type": "hardlink", "sha256": "2bc13d0dcbdf62eb7ea868d734470e837fc96cb4468242faeeeef3c9460e5557", "sha256_in_prefix": "2bc13d0dcbdf62eb7ea868d734470e837fc96cb4468242faeeeef3c9460e5557", "size_in_bytes": 22149}, {"_path": "lib/python3.11/site-packages/chromadb/serde.py", "path_type": "hardlink", "sha256": "84e4037a6cb8c73dcde6162393556fd163028f1c80a30f4d592bbebf22476d8d", "sha256_in_prefix": "84e4037a6cb8c73dcde6162393556fd163028f1c80a30f4d592bbebf22476d8d", "size_in_bytes": 1501}, {"_path": "lib/python3.11/site-packages/chromadb/server/__init__.py", "path_type": "hardlink", "sha256": "29f007c498a9a9aa4565dda28cd1fdf8bf7140c0e0f10df8f7133c858ef6852f", "sha256_in_prefix": "29f007c498a9a9aa4565dda28cd1fdf8bf7140c0e0f10df8f7133c858ef6852f", "size_in_bytes": 172}, {"_path": "lib/python3.11/site-packages/chromadb/server/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "5d7b8f49999db7266fdcff3445b399400d0cfb2b938d81f65c5ddc4afc5dd18f", "sha256_in_prefix": "5d7b8f49999db7266fdcff3445b399400d0cfb2b938d81f65c5ddc4afc5dd18f", "size_in_bytes": 740}, {"_path": "lib/python3.11/site-packages/chromadb/server/fastapi/__init__.py", "path_type": "hardlink", "sha256": "e484ea46eb12ceadb29d3f41a1569f643cd64ce70d5703fbe4be11bcc02c594d", "sha256_in_prefix": "e484ea46eb12ceadb29d3f41a1569f643cd64ce70d5703fbe4be11bcc02c594d", "size_in_bytes": 70679}, {"_path": "lib/python3.11/site-packages/chromadb/server/fastapi/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "6bbd4184e18154525beb458b6ff3fd223f23b3e5cdecaf25b8e233e9ae286eb0", "sha256_in_prefix": "6bbd4184e18154525beb458b6ff3fd223f23b3e5cdecaf25b8e233e9ae286eb0", "size_in_bytes": 83471}, {"_path": "lib/python3.11/site-packages/chromadb/server/fastapi/__pycache__/types.cpython-311.pyc", "path_type": "hardlink", "sha256": "ef4c1e60959665bd924e64f68d6ba02363d5153405a0c9d8d9d410ed6d97c218", "sha256_in_prefix": "ef4c1e60959665bd924e64f68d6ba02363d5153405a0c9d8d9d410ed6d97c218", "size_in_bytes": 4972}, {"_path": "lib/python3.11/site-packages/chromadb/server/fastapi/types.py", "path_type": "hardlink", "sha256": "7d6101e56a93ab8652b4ee20409722b10680c5cfc806ebbc1a2cc9e18bb9e726", "sha256_in_prefix": "7d6101e56a93ab8652b4ee20409722b10680c5cfc806ebbc1a2cc9e18bb9e726", "size_in_bytes": 2506}, {"_path": "lib/python3.11/site-packages/chromadb/telemetry/README.md", "path_type": "hardlink", "sha256": "e0fd46f991353a125fcafe569b7296f0d98638d2ed513d74b50ae3fa29f56837", "sha256_in_prefix": "e0fd46f991353a125fcafe569b7296f0d98638d2ed513d74b50ae3fa29f56837", "size_in_bytes": 587}, {"_path": "lib/python3.11/site-packages/chromadb/telemetry/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/chromadb/telemetry/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "a74ab44e5dbe221f580c7e8e5afe9270d2a89d9847e73da5d087f1e3203939aa", "sha256_in_prefix": "a74ab44e5dbe221f580c7e8e5afe9270d2a89d9847e73da5d087f1e3203939aa", "size_in_bytes": 168}, {"_path": "lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__init__.py", "path_type": "hardlink", "sha256": "e20b84a671463b2b0b13c591c382464f608013acde05ac24803448eb8529daf4", "sha256_in_prefix": "e20b84a671463b2b0b13c591c382464f608013acde05ac24803448eb8529daf4", "size_in_bytes": 6021}, {"_path": "lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "c52f05492af85eed387c8b50dc428d134e01aed1ebd100c76a4db9828831938f", "sha256_in_prefix": "c52f05492af85eed387c8b50dc428d134e01aed1ebd100c76a4db9828831938f", "size_in_bytes": 8605}, {"_path": "lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__pycache__/fastapi.cpython-311.pyc", "path_type": "hardlink", "sha256": "d93b819e227d140e899f72ccded147c5b99792a472a172ef15b6be85360d45df", "sha256_in_prefix": "d93b819e227d140e899f72ccded147c5b99792a472a172ef15b6be85360d45df", "size_in_bytes": 895}, {"_path": "lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__pycache__/grpc.cpython-311.pyc", "path_type": "hardlink", "sha256": "1e8fb6cb9b10d8d8dd1170fb7144883a456ca6d4c881d3a647dc0ea919d232ca", "sha256_in_prefix": "1e8fb6cb9b10d8d8dd1170fb7144883a456ca6d4c881d3a647dc0ea919d232ca", "size_in_bytes": 5581}, {"_path": "lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/fastapi.py", "path_type": "hardlink", "sha256": "fa1f8ecdc80ff00f6728b0aa2784cf37ed4a044e69df8c0b07e354621d6f29d8", "sha256_in_prefix": "fa1f8ecdc80ff00f6728b0aa2784cf37ed4a044e69df8c0b07e354621d6f29d8", "size_in_bytes": 405}, {"_path": "lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/grpc.py", "path_type": "hardlink", "sha256": "51c54a91da5a9e68a79a3aedfffa612025be5bbd6ec3a052769931ba1955f596", "sha256_in_prefix": "51c54a91da5a9e68a79a3aedfffa612025be5bbd6ec3a052769931ba1955f596", "size_in_bytes": 3731}, {"_path": "lib/python3.11/site-packages/chromadb/telemetry/product/__init__.py", "path_type": "hardlink", "sha256": "64fd8ca534d843e9e362da864053eb266e83eaa1961b427ce287bd2dc49c072a", "sha256_in_prefix": "64fd8ca534d843e9e362da864053eb266e83eaa1961b427ce287bd2dc49c072a", "size_in_bytes": 2936}, {"_path": "lib/python3.11/site-packages/chromadb/telemetry/product/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "a0ae9fc3f99e2b354bb2b34da02ebe8d8f3941cdffde3998f4efd51f375589e4", "sha256_in_prefix": "a0ae9fc3f99e2b354bb2b34da02ebe8d8f3941cdffde3998f4efd51f375589e4", "size_in_bytes": 5478}, {"_path": "lib/python3.11/site-packages/chromadb/telemetry/product/__pycache__/events.cpython-311.pyc", "path_type": "hardlink", "sha256": "d2621d561e2bef59ab1d72de83449d47be29a1582a8b3d730c1345ab780190be", "sha256_in_prefix": "d2621d561e2bef59ab1d72de83449d47be29a1582a8b3d730c1345ab780190be", "size_in_bytes": 11982}, {"_path": "lib/python3.11/site-packages/chromadb/telemetry/product/__pycache__/posthog.cpython-311.pyc", "path_type": "hardlink", "sha256": "d81c4ccc9f87f87ca5e0a127fb011116cace3354773825fe6688275b3e5b38c5", "sha256_in_prefix": "d81c4ccc9f87f87ca5e0a127fb011116cace3354773825fe6688275b3e5b38c5", "size_in_bytes": 3667}, {"_path": "lib/python3.11/site-packages/chromadb/telemetry/product/events.py", "path_type": "hardlink", "sha256": "1ca0ef499eae2d313206d80557c55efb37840d0d592f87668718c5b155079f24", "sha256_in_prefix": "1ca0ef499eae2d313206d80557c55efb37840d0d592f87668718c5b155079f24", "size_in_bytes": 8665}, {"_path": "lib/python3.11/site-packages/chromadb/telemetry/product/posthog.py", "path_type": "hardlink", "sha256": "97fdd610b85ae78be79c97b251000cfd29eb80d01097c9db8c2c7178ad976308", "sha256_in_prefix": "97fdd610b85ae78be79c97b251000cfd29eb80d01097c9db8c2c7178ad976308", "size_in_bytes": 2176}, {"_path": "lib/python3.11/site-packages/chromadb/test/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/chromadb/test/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "ffdf1639e9fa40d2cebc360951d985d447cd46eb2de46d65474b7c26971cbcf0", "sha256_in_prefix": "ffdf1639e9fa40d2cebc360951d985d447cd46eb2de46d65474b7c26971cbcf0", "size_in_bytes": 163}, {"_path": "lib/python3.11/site-packages/chromadb/test/__pycache__/conftest.cpython-311.pyc", "path_type": "hardlink", "sha256": "ecf1154a9425796fe1d1de8c76cd434a0a5ee7de93af13a3695c9d6e8e84f8ff", "sha256_in_prefix": "ecf1154a9425796fe1d1de8c76cd434a0a5ee7de93af13a3695c9d6e8e84f8ff", "size_in_bytes": 47828}, {"_path": "lib/python3.11/site-packages/chromadb/test/__pycache__/test_api.cpython-311.pyc", "path_type": "hardlink", "sha256": "aeb10b38ff4366be50c765617e61745a12d18994fa7caff81f3f42bd6e5ece4e", "sha256_in_prefix": "aeb10b38ff4366be50c765617e61745a12d18994fa7caff81f3f42bd6e5ece4e", "size_in_bytes": 102009}, {"_path": "lib/python3.11/site-packages/chromadb/test/__pycache__/test_chroma.cpython-311.pyc", "path_type": "hardlink", "sha256": "1f21d6d2c3f1f3bae5fe0501bec94bd15a3c839f5cdb9d001079d71c304c96c3", "sha256_in_prefix": "1f21d6d2c3f1f3bae5fe0501bec94bd15a3c839f5cdb9d001079d71c304c96c3", "size_in_bytes": 6677}, {"_path": "lib/python3.11/site-packages/chromadb/test/__pycache__/test_cli.cpython-311.pyc", "path_type": "hardlink", "sha256": "185999eb293ee0a6aa46a18d3babb6a1d2c3b46e870c7fb306cc795ae9ad0094", "sha256_in_prefix": "185999eb293ee0a6aa46a18d3babb6a1d2c3b46e870c7fb306cc795ae9ad0094", "size_in_bytes": 9485}, {"_path": "lib/python3.11/site-packages/chromadb/test/__pycache__/test_client.cpython-311.pyc", "path_type": "hardlink", "sha256": "84d246f6bd49f17e1bcd5bdaa6016c83c5a567a29ddbdfe15b32b7f70ce4bf17", "sha256_in_prefix": "84d246f6bd49f17e1bcd5bdaa6016c83c5a567a29ddbdfe15b32b7f70ce4bf17", "size_in_bytes": 7106}, {"_path": "lib/python3.11/site-packages/chromadb/test/__pycache__/test_config.cpython-311.pyc", "path_type": "hardlink", "sha256": "b05f8f42ade0a7007ea1cc5aeb3f5f5fac7ac1973c26f753657babd4f596428c", "sha256_in_prefix": "b05f8f42ade0a7007ea1cc5aeb3f5f5fac7ac1973c26f753657babd4f596428c", "size_in_bytes": 9846}, {"_path": "lib/python3.11/site-packages/chromadb/test/__pycache__/test_multithreaded.cpython-311.pyc", "path_type": "hardlink", "sha256": "2d5b01decf83381a424be0ad530fa803ee4c4addb5d2526c4c9f3964c4e53dfe", "sha256_in_prefix": "2d5b01decf83381a424be0ad530fa803ee4c4addb5d2526c4c9f3964c4e53dfe", "size_in_bytes": 13430}, {"_path": "lib/python3.11/site-packages/chromadb/test/api/__pycache__/test_collection.cpython-311.pyc", "path_type": "hardlink", "sha256": "578cfdf364b36c6fcfd405ef6065b07e38293b5924f705b479b09e4a8b56ce59", "sha256_in_prefix": "578cfdf364b36c6fcfd405ef6065b07e38293b5924f705b479b09e4a8b56ce59", "size_in_bytes": 4423}, {"_path": "lib/python3.11/site-packages/chromadb/test/api/__pycache__/test_delete_database.cpython-311.pyc", "path_type": "hardlink", "sha256": "c1e98920b8108a40faf7cdbfe2d6a706dbd468dff9329e1ddd2f9021f2e53fc2", "sha256_in_prefix": "c1e98920b8108a40faf7cdbfe2d6a706dbd468dff9329e1ddd2f9021f2e53fc2", "size_in_bytes": 5738}, {"_path": "lib/python3.11/site-packages/chromadb/test/api/__pycache__/test_get_database.cpython-311.pyc", "path_type": "hardlink", "sha256": "e183b2f3a6989b37e194a5803d24d8aa2e78db576298bcc23de8885e93273d71", "sha256_in_prefix": "e183b2f3a6989b37e194a5803d24d8aa2e78db576298bcc23de8885e93273d71", "size_in_bytes": 924}, {"_path": "lib/python3.11/site-packages/chromadb/test/api/__pycache__/test_invalid_update.cpython-311.pyc", "path_type": "hardlink", "sha256": "59b79ec39c55dbd020a57a3a19e242b16b1935788f48a9f764628988d7a83dec", "sha256_in_prefix": "59b79ec39c55dbd020a57a3a19e242b16b1935788f48a9f764628988d7a83dec", "size_in_bytes": 1200}, {"_path": "lib/python3.11/site-packages/chromadb/test/api/__pycache__/test_limit_offset.cpython-311.pyc", "path_type": "hardlink", "sha256": "1ff891f65f2b43fce9f08b9f835ee18722744af5b868405fbcdb0608c57c3f84", "sha256_in_prefix": "1ff891f65f2b43fce9f08b9f835ee18722744af5b868405fbcdb0608c57c3f84", "size_in_bytes": 3208}, {"_path": "lib/python3.11/site-packages/chromadb/test/api/__pycache__/test_list_databases.cpython-311.pyc", "path_type": "hardlink", "sha256": "dd460dfa998bfa0c313286620e91ff76f9ed9ab526a56c0233c2c4261e29bb3f", "sha256_in_prefix": "dd460dfa998bfa0c313286620e91ff76f9ed9ab526a56c0233c2c4261e29bb3f", "size_in_bytes": 6090}, {"_path": "lib/python3.11/site-packages/chromadb/test/api/__pycache__/test_numpy_list_inputs.cpython-311.pyc", "path_type": "hardlink", "sha256": "8acdda42ef279333fae1b19316efbeacc0dfedb78987adb5ca0a7c91ed91c27c", "sha256_in_prefix": "8acdda42ef279333fae1b19316efbeacc0dfedb78987adb5ca0a7c91ed91c27c", "size_in_bytes": 3235}, {"_path": "lib/python3.11/site-packages/chromadb/test/api/__pycache__/test_types.cpython-311.pyc", "path_type": "hardlink", "sha256": "39951f9d60d71ff4a0ec1ce52b0497f9b39e6438fe8805e7258e381c7e293278", "sha256_in_prefix": "39951f9d60d71ff4a0ec1ce52b0497f9b39e6438fe8805e7258e381c7e293278", "size_in_bytes": 8397}, {"_path": "lib/python3.11/site-packages/chromadb/test/api/test_collection.py", "path_type": "hardlink", "sha256": "61106d12e2352d51ccc9619711d8f2cc69005480131f54ec14c00b0427661456", "sha256_in_prefix": "61106d12e2352d51ccc9619711d8f2cc69005480131f54ec14c00b0427661456", "size_in_bytes": 2211}, {"_path": "lib/python3.11/site-packages/chromadb/test/api/test_delete_database.py", "path_type": "hardlink", "sha256": "331cece4ef3b9cc046f260b8145f8ab166bc526b9d5dc75077dfb5b7ef8ac5ab", "sha256_in_prefix": "331cece4ef3b9cc046f260b8145f8ab166bc526b9d5dc75077dfb5b7ef8ac5ab", "size_in_bytes": 2671}, {"_path": "lib/python3.11/site-packages/chromadb/test/api/test_get_database.py", "path_type": "hardlink", "sha256": "aabc8812b59163ed6f809d13c0c83b4b4f702033ffc9aed7b0cd4a75c2b4dbe1", "sha256_in_prefix": "aabc8812b59163ed6f809d13c0c83b4b4f702033ffc9aed7b0cd4a75c2b4dbe1", "size_in_bytes": 290}, {"_path": "lib/python3.11/site-packages/chromadb/test/api/test_invalid_update.py", "path_type": "hardlink", "sha256": "87c1399a8cfa1331717291f0f3ea0771fed4e7976f968b517e402c2840845545", "sha256_in_prefix": "87c1399a8cfa1331717291f0f3ea0771fed4e7976f968b517e402c2840845545", "size_in_bytes": 608}, {"_path": "lib/python3.11/site-packages/chromadb/test/api/test_limit_offset.py", "path_type": "hardlink", "sha256": "b2d6cfc8f8813efaa72d26ffd42a2cc42dcb28dabc44da52e2b74cffdd7c8b38", "sha256_in_prefix": "b2d6cfc8f8813efaa72d26ffd42a2cc42dcb28dabc44da52e2b74cffdd7c8b38", "size_in_bytes": 2025}, {"_path": "lib/python3.11/site-packages/chromadb/test/api/test_list_databases.py", "path_type": "hardlink", "sha256": "c15074fc5dfdce43777bf38ce9e2e950e94d2f41860183e3aa5cf4d15bb566ea", "sha256_in_prefix": "c15074fc5dfdce43777bf38ce9e2e950e94d2f41860183e3aa5cf4d15bb566ea", "size_in_bytes": 3173}, {"_path": "lib/python3.11/site-packages/chromadb/test/api/test_numpy_list_inputs.py", "path_type": "hardlink", "sha256": "49be7100320235a9941cb865e377d1377b98a85e3a16154f23867972a4ee01bf", "sha256_in_prefix": "49be7100320235a9941cb865e377d1377b98a85e3a16154f23867972a4ee01bf", "size_in_bytes": 2085}, {"_path": "lib/python3.11/site-packages/chromadb/test/api/test_types.py", "path_type": "hardlink", "sha256": "f0acb03d4b263f52aa3724a8a637b532fd82b21b52c00882f1b907277d42c4ff", "sha256_in_prefix": "f0acb03d4b263f52aa3724a8a637b532fd82b21b52c00882f1b907277d42c4ff", "size_in_bytes": 3219}, {"_path": "lib/python3.11/site-packages/chromadb/test/auth/__pycache__/test_auth_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "c516509d0b37ad7701a12c3debb6b40472d18c2065e88009e821a7eba8e4013b", "sha256_in_prefix": "c516509d0b37ad7701a12c3debb6b40472d18c2065e88009e821a7eba8e4013b", "size_in_bytes": 2990}, {"_path": "lib/python3.11/site-packages/chromadb/test/auth/test_auth_utils.py", "path_type": "hardlink", "sha256": "01b519f721c8571aaf6a08e7af28f8bcc2ad399d2365fd9f62c870885ee71414", "sha256_in_prefix": "01b519f721c8571aaf6a08e7af28f8bcc2ad399d2365fd9f62c870885ee71414", "size_in_bytes": 3039}, {"_path": "lib/python3.11/site-packages/chromadb/test/client/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/chromadb/test/client/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "d961d9cb59d5bac151fd81f2b7632546f0f4dd9f15395d107e97fc021e5258be", "sha256_in_prefix": "d961d9cb59d5bac151fd81f2b7632546f0f4dd9f15395d107e97fc021e5258be", "size_in_bytes": 170}, {"_path": "lib/python3.11/site-packages/chromadb/test/client/__pycache__/create_http_client_with_basic_auth.cpython-311.pyc", "path_type": "hardlink", "sha256": "761ad3a0f7fb477d9ce29a4ceb61501ef065bd21ebf4fe72700517170297084f", "sha256_in_prefix": "761ad3a0f7fb477d9ce29a4ceb61501ef065bd21ebf4fe72700517170297084f", "size_in_bytes": 1035}, {"_path": "lib/python3.11/site-packages/chromadb/test/client/__pycache__/test_cloud_client.cpython-311.pyc", "path_type": "hardlink", "sha256": "0a6490d95172ff9928bd95c4890ce648a87c7f3afad9fe01877599faccf7c873", "sha256_in_prefix": "0a6490d95172ff9928bd95c4890ce648a87c7f3afad9fe01877599faccf7c873", "size_in_bytes": 19639}, {"_path": "lib/python3.11/site-packages/chromadb/test/client/__pycache__/test_create_http_client.cpython-311.pyc", "path_type": "hardlink", "sha256": "457e4b9cf0ea5e3118b76146d571d860895b9b262d14bf4a7e79ad8a9399eb9d", "sha256_in_prefix": "457e4b9cf0ea5e3118b76146d571d860895b9b262d14bf4a7e79ad8a9399eb9d", "size_in_bytes": 612}, {"_path": "lib/python3.11/site-packages/chromadb/test/client/__pycache__/test_database_tenant.cpython-311.pyc", "path_type": "hardlink", "sha256": "d86e5e07bc766cfa2ce4c4abe2b56412d54fab2e4171ccad6f4516fbdb69ea05", "sha256_in_prefix": "d86e5e07bc766cfa2ce4c4abe2b56412d54fab2e4171ccad6f4516fbdb69ea05", "size_in_bytes": 9949}, {"_path": "lib/python3.11/site-packages/chromadb/test/client/__pycache__/test_database_tenant_auth.cpython-311.pyc", "path_type": "hardlink", "sha256": "deed46ba37e6394d22cb479835c2a3c34d3c978427e8801f11092fdc1bcb686e", "sha256_in_prefix": "deed46ba37e6394d22cb479835c2a3c34d3c978427e8801f11092fdc1bcb686e", "size_in_bytes": 2899}, {"_path": "lib/python3.11/site-packages/chromadb/test/client/__pycache__/test_multiple_clients_concurrency.cpython-311.pyc", "path_type": "hardlink", "sha256": "b9ba755cbec620c5f6376099d0a1d6e6e2b75d0bd35a4417aacd54ca7c7944a2", "sha256_in_prefix": "b9ba755cbec620c5f6376099d0a1d6e6e2b75d0bd35a4417aacd54ca7c7944a2", "size_in_bytes": 3426}, {"_path": "lib/python3.11/site-packages/chromadb/test/client/create_http_client_with_basic_auth.py", "path_type": "hardlink", "sha256": "921f1ce495fa321a0af24d3e12d55b7e9f65ff3ac6fe0ebdf469dd8d88f9d9d9", "sha256_in_prefix": "921f1ce495fa321a0af24d3e12d55b7e9f65ff3ac6fe0ebdf469dd8d88f9d9d9", "size_in_bytes": 786}, {"_path": "lib/python3.11/site-packages/chromadb/test/client/test_cloud_client.py", "path_type": "hardlink", "sha256": "e30069326e76779cf25720b960789a4b90dc968acac8122f48e35eb8e78c5088", "sha256_in_prefix": "e30069326e76779cf25720b960789a4b90dc968acac8122f48e35eb8e78c5088", "size_in_bytes": 12467}, {"_path": "lib/python3.11/site-packages/chromadb/test/client/test_create_http_client.py", "path_type": "hardlink", "sha256": "f68fb478cc6cf6c1ad196a48b2cec1d1395f382cbcafafb1632da3f03e04b593", "sha256_in_prefix": "f68fb478cc6cf6c1ad196a48b2cec1d1395f382cbcafafb1632da3f03e04b593", "size_in_bytes": 557}, {"_path": "lib/python3.11/site-packages/chromadb/test/client/test_database_tenant.py", "path_type": "hardlink", "sha256": "ea5c39e600579cef39897dda2747d477f8059fbcaa4dbfbd2319b39e9d13ba16", "sha256_in_prefix": "ea5c39e600579cef39897dda2747d477f8059fbcaa4dbfbd2319b39e9d13ba16", "size_in_bytes": 7171}, {"_path": "lib/python3.11/site-packages/chromadb/test/client/test_database_tenant_auth.py", "path_type": "hardlink", "sha256": "a239000939148b627cfe11ae526304adec07eabcf6201683db56a7079630504a", "sha256_in_prefix": "a239000939148b627cfe11ae526304adec07eabcf6201683db56a7079630504a", "size_in_bytes": 1369}, {"_path": "lib/python3.11/site-packages/chromadb/test/client/test_multiple_clients_concurrency.py", "path_type": "hardlink", "sha256": "7481a625a97b74681022597deabec3c73beaa318f2792638720d9b27c9382178", "sha256_in_prefix": "7481a625a97b74681022597deabec3c73beaa318f2792638720d9b27c9382178", "size_in_bytes": 2006}, {"_path": "lib/python3.11/site-packages/chromadb/test/configurations/__pycache__/test_collection_configuration.cpython-311.pyc", "path_type": "hardlink", "sha256": "0f2c55828d6301e9b23c4bf7aab01b7092bdf24d1c67ab422831ff8b44d3a186", "sha256_in_prefix": "0f2c55828d6301e9b23c4bf7aab01b7092bdf24d1c67ab422831ff8b44d3a186", "size_in_bytes": 72443}, {"_path": "lib/python3.11/site-packages/chromadb/test/configurations/__pycache__/test_configurations.cpython-311.pyc", "path_type": "hardlink", "sha256": "affa0b64a00f1dcc83a301554b2dad2aaa7c29a16178a55e52975c10b1671c41", "sha256_in_prefix": "affa0b64a00f1dcc83a301554b2dad2aaa7c29a16178a55e52975c10b1671c41", "size_in_bytes": 7079}, {"_path": "lib/python3.11/site-packages/chromadb/test/configurations/test_collection_configuration.py", "path_type": "hardlink", "sha256": "b9819f1b6acd9da81f5e32bf4da3dabde563a99c8a1f21e42832e83cf274b967", "sha256_in_prefix": "b9819f1b6acd9da81f5e32bf4da3dabde563a99c8a1f21e42832e83cf274b967", "size_in_bytes": 57022}, {"_path": "lib/python3.11/site-packages/chromadb/test/configurations/test_configurations.py", "path_type": "hardlink", "sha256": "19ef9c65cd3d59240dd8537aa0187862ef8c352676d0d6e06634e37a529cea74", "sha256_in_prefix": "19ef9c65cd3d59240dd8537aa0187862ef8c352676d0d6e06634e37a529cea74", "size_in_bytes": 3680}, {"_path": "lib/python3.11/site-packages/chromadb/test/conftest.py", "path_type": "hardlink", "sha256": "b739ea7dbbb8c8886210a3973cea1cafc79ea6fee4b648ca304b58a7340bc3ed", "sha256_in_prefix": "b739ea7dbbb8c8886210a3973cea1cafc79ea6fee4b648ca304b58a7340bc3ed", "size_in_bytes": 37341}, {"_path": "lib/python3.11/site-packages/chromadb/test/data_loader/__pycache__/test_data_loader.cpython-311.pyc", "path_type": "hardlink", "sha256": "ea09104425d66b4ae0682deafc2029c95c333890010e3da55ac6567a05dd3146", "sha256_in_prefix": "ea09104425d66b4ae0682deafc2029c95c333890010e3da55ac6567a05dd3146", "size_in_bytes": 7309}, {"_path": "lib/python3.11/site-packages/chromadb/test/data_loader/test_data_loader.py", "path_type": "hardlink", "sha256": "82cde2783c352e2d3ffe6ecae78f214f0c4331bd0cdd5b88d6e2b234b3df654d", "sha256_in_prefix": "82cde2783c352e2d3ffe6ecae78f214f0c4331bd0cdd5b88d6e2b234b3df654d", "size_in_bytes": 3708}, {"_path": "lib/python3.11/site-packages/chromadb/test/db/__pycache__/test_log_purge.cpython-311.pyc", "path_type": "hardlink", "sha256": "f7ddd592f56595e129d271a02ab3ba4268b1cff519994692efd3abc8bd75bb6d", "sha256_in_prefix": "f7ddd592f56595e129d271a02ab3ba4268b1cff519994692efd3abc8bd75bb6d", "size_in_bytes": 2507}, {"_path": "lib/python3.11/site-packages/chromadb/test/db/test_log_purge.py", "path_type": "hardlink", "sha256": "7f8c3c3d4d404dbbfe00a37a9a511b747a60690c2d17c094029da4b51b94e6c4", "sha256_in_prefix": "7f8c3c3d4d404dbbfe00a37a9a511b747a60690c2d17c094029da4b51b94e6c4", "size_in_bytes": 1773}, {"_path": "lib/python3.11/site-packages/chromadb/test/distributed/README.md", "path_type": "hardlink", "sha256": "13f45d671e09b757e9f95ef60a7679941e4040b7bfb1347ee7700ab94172c4d4", "sha256_in_prefix": "13f45d671e09b757e9f95ef60a7679941e4040b7bfb1347ee7700ab94172c4d4", "size_in_bytes": 233}, {"_path": "lib/python3.11/site-packages/chromadb/test/distributed/__pycache__/test_log_backpressure.cpython-311.pyc", "path_type": "hardlink", "sha256": "351594d8d135a46684321faedecf1b65c10f569f31ef80b38a026ea0a0c39f35", "sha256_in_prefix": "351594d8d135a46684321faedecf1b65c10f569f31ef80b38a026ea0a0c39f35", "size_in_bytes": 3254}, {"_path": "lib/python3.11/site-packages/chromadb/test/distributed/__pycache__/test_log_failover.cpython-311.pyc", "path_type": "hardlink", "sha256": "fb819e2ff776a1195310bb3905d2a7e6d0fe841279189c2f0895a2c62363cb2c", "sha256_in_prefix": "fb819e2ff776a1195310bb3905d2a7e6d0fe841279189c2f0895a2c62363cb2c", "size_in_bytes": 22581}, {"_path": "lib/python3.11/site-packages/chromadb/test/distributed/__pycache__/test_repair_collection_log_offset.cpython-311.pyc", "path_type": "hardlink", "sha256": "c13d8d6880d48fa112afe7d0e217f7256453f083ff6445f6437dc184e265e80e", "sha256_in_prefix": "c13d8d6880d48fa112afe7d0e217f7256453f083ff6445f6437dc184e265e80e", "size_in_bytes": 4607}, {"_path": "lib/python3.11/site-packages/chromadb/test/distributed/__pycache__/test_reroute.cpython-311.pyc", "path_type": "hardlink", "sha256": "e0e4289136ecc5a45ba00b7b25bb3c85fb1fdf0cdd0054a139fed16936e03125", "sha256_in_prefix": "e0e4289136ecc5a45ba00b7b25bb3c85fb1fdf0cdd0054a139fed16936e03125", "size_in_bytes": 4170}, {"_path": "lib/python3.11/site-packages/chromadb/test/distributed/__pycache__/test_sanity.cpython-311.pyc", "path_type": "hardlink", "sha256": "bdae8e766c0914a4dd57e00100c30c906f07fd12f552dc686bfbed22423da985", "sha256_in_prefix": "bdae8e766c0914a4dd57e00100c30c906f07fd12f552dc686bfbed22423da985", "size_in_bytes": 4220}, {"_path": "lib/python3.11/site-packages/chromadb/test/distributed/test_log_backpressure.py", "path_type": "hardlink", "sha256": "d091e7b45ef50b3d4db8974bcc7b545765a235adf186a5e57f8516881a0ed492", "sha256_in_prefix": "d091e7b45ef50b3d4db8974bcc7b545765a235adf186a5e57f8516881a0ed492", "size_in_bytes": 1631}, {"_path": "lib/python3.11/site-packages/chromadb/test/distributed/test_log_failover.py", "path_type": "hardlink", "sha256": "c648b57b52edbc7ffc1d40aad0872835630a096eecc7bbc49a034fc6de0254a1", "sha256_in_prefix": "c648b57b52edbc7ffc1d40aad0872835630a096eecc7bbc49a034fc6de0254a1", "size_in_bytes": 14779}, {"_path": "lib/python3.11/site-packages/chromadb/test/distributed/test_repair_collection_log_offset.py", "path_type": "hardlink", "sha256": "3b83eb9f88d8547792a49e38405d80abb8dea4b766f8237c2e57e3c537346a91", "sha256_in_prefix": "3b83eb9f88d8547792a49e38405d80abb8dea4b766f8237c2e57e3c537346a91", "size_in_bytes": 2592}, {"_path": "lib/python3.11/site-packages/chromadb/test/distributed/test_reroute.py", "path_type": "hardlink", "sha256": "b478b27293521e7af5728f3de735a02ac936c7fc975b2e41557927ffcfd581cc", "sha256_in_prefix": "b478b27293521e7af5728f3de735a02ac936c7fc975b2e41557927ffcfd581cc", "size_in_bytes": 2442}, {"_path": "lib/python3.11/site-packages/chromadb/test/distributed/test_sanity.py", "path_type": "hardlink", "sha256": "a55f535420e3f166e765d051f182fd566da63d99793a7fefa6b2cb064073d900", "sha256_in_prefix": "a55f535420e3f166e765d051f182fd566da63d99793a7fefa6b2cb064073d900", "size_in_bytes": 2882}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_custom_ef.cpython-311.pyc", "path_type": "hardlink", "sha256": "d06a9c3c9ae82c237e3116bf831ce54ce379c80f66f0c6e07e99311cc55ab51f", "sha256_in_prefix": "d06a9c3c9ae82c237e3116bf831ce54ce379c80f66f0c6e07e99311cc55ab51f", "size_in_bytes": 6436}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_default_ef.cpython-311.pyc", "path_type": "hardlink", "sha256": "8717a348559423ee47df17e66c92d143fd2bf03844df5fcfbdb96120528b384f", "sha256_in_prefix": "8717a348559423ee47df17e66c92d143fd2bf03844df5fcfbdb96120528b384f", "size_in_bytes": 6091}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_ef.cpython-311.pyc", "path_type": "hardlink", "sha256": "4b6c958b80655bb9d66d76e1a2414f130dc9f0867dcdecdfce6efd9f75df3e51", "sha256_in_prefix": "4b6c958b80655bb9d66d76e1a2414f130dc9f0867dcdecdfce6efd9f75df3e51", "size_in_bytes": 5588}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_morph_ef.cpython-311.pyc", "path_type": "hardlink", "sha256": "ea25f0215ef2eb976967aaebdbfb9a86eeb15f5b74f9c470b14807492b98a390", "sha256_in_prefix": "ea25f0215ef2eb976967aaebdbfb9a86eeb15f5b74f9c470b14807492b98a390", "size_in_bytes": 5890}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_multimodal_ef.cpython-311.pyc", "path_type": "hardlink", "sha256": "62f588c658215724d4c5cd94c6a81250dd266c940ae108f6eed0cc78abf0d7f1", "sha256_in_prefix": "62f588c658215724d4c5cd94c6a81250dd266c940ae108f6eed0cc78abf0d7f1", "size_in_bytes": 10174}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_ollama_ef.cpython-311.pyc", "path_type": "hardlink", "sha256": "55acb576354a42a10ecb0fe51b43eae36a1311023a34391341c4333ee916ba6f", "sha256_in_prefix": "55acb576354a42a10ecb0fe51b43eae36a1311023a34391341c4333ee916ba6f", "size_in_bytes": 3904}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_onnx_mini_lm_l6_v2.cpython-311.pyc", "path_type": "hardlink", "sha256": "a9ce2ee77e87ee8532616027c28a273e7650dbbc46a269f0e66401248b1957cf", "sha256_in_prefix": "a9ce2ee77e87ee8532616027c28a273e7650dbbc46a269f0e66401248b1957cf", "size_in_bytes": 11358}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_openai_ef.cpython-311.pyc", "path_type": "hardlink", "sha256": "5c4c89c0bf407bfba15b4b26c5e6f813ce54325f1f22ef66db9f85e0360b2c87", "sha256_in_prefix": "5c4c89c0bf407bfba15b4b26c5e6f813ce54325f1f22ef66db9f85e0360b2c87", "size_in_bytes": 2674}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/__pycache__/test_voyageai_ef.cpython-311.pyc", "path_type": "hardlink", "sha256": "be003c32c7a7528d8b5872ba5e5d587fc5d50640f14035eef94258ecf745cce1", "sha256_in_prefix": "be003c32c7a7528d8b5872ba5e5d587fc5d50640f14035eef94258ecf745cce1", "size_in_bytes": 1241}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/test_custom_ef.py", "path_type": "hardlink", "sha256": "b01deacea814a8c2a391a0815162198921938648f2a237293631cc49b4d84a89", "sha256_in_prefix": "b01deacea814a8c2a391a0815162198921938648f2a237293631cc49b4d84a89", "size_in_bytes": 3303}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/test_default_ef.py", "path_type": "hardlink", "sha256": "7e1b35344da04dc17dccb5cd0db2e807055af5dcd22cde07f69240b078e3867d", "sha256_in_prefix": "7e1b35344da04dc17dccb5cd0db2e807055af5dcd22cde07f69240b078e3867d", "size_in_bytes": 2660}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/test_ef.py", "path_type": "hardlink", "sha256": "5bbf9159e46ef193524d20156168a8230a2f176994c82f5d157394d8b6bde407", "sha256_in_prefix": "5bbf9159e46ef193524d20156168a8230a2f176994c82f5d157394d8b6bde407", "size_in_bytes": 3430}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/test_morph_ef.py", "path_type": "hardlink", "sha256": "05284a14f6320fc0d01e63442552e54c65bbc8d01e0ef6e2eb8cf483057a8548", "sha256_in_prefix": "05284a14f6320fc0d01e63442552e54c65bbc8d01e0ef6e2eb8cf483057a8548", "size_in_bytes": 4157}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/test_multimodal_ef.py", "path_type": "hardlink", "sha256": "a498be41ebc941f6719eeb47c7d169d1a0eaf67478403afcb27a8d43d3e8f317", "sha256_in_prefix": "a498be41ebc941f6719eeb47c7d169d1a0eaf67478403afcb27a8d43d3e8f317", "size_in_bytes": 6174}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/test_ollama_ef.py", "path_type": "hardlink", "sha256": "488c4148c9cafae6ddb75526d625f04a88da8d1df012d7f2329b9413057af1ee", "sha256_in_prefix": "488c4148c9cafae6ddb75526d625f04a88da8d1df012d7f2329b9413057af1ee", "size_in_bytes": 1835}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/test_onnx_mini_lm_l6_v2.py", "path_type": "hardlink", "sha256": "41282e4b1dc7f34564096c019f65fe414501304aff91f7b6d41ef6289541605b", "sha256_in_prefix": "41282e4b1dc7f34564096c019f65fe414501304aff91f7b6d41ef6289541605b", "size_in_bytes": 7542}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/test_openai_ef.py", "path_type": "hardlink", "sha256": "9448ca7cce21b2b3a14c32e793e3a2b029cfd0b9401ae2a13bb846ff80f7f2cc", "sha256_in_prefix": "9448ca7cce21b2b3a14c32e793e3a2b029cfd0b9401ae2a13bb846ff80f7f2cc", "size_in_bytes": 1246}, {"_path": "lib/python3.11/site-packages/chromadb/test/ef/test_voyageai_ef.py", "path_type": "hardlink", "sha256": "0c46513fe3934dc16d13071de5fbc05ce7036cd29688f70c1ac50fbc42fd0ab6", "sha256_in_prefix": "0c46513fe3934dc16d13071de5fbc05ce7036cd29688f70c1ac50fbc42fd0ab6", "size_in_bytes": 603}, {"_path": "lib/python3.11/site-packages/chromadb/test/openssl.cnf", "path_type": "hardlink", "sha256": "4019bf7436a7e718d32797f8a19842afbc66be60504fedf4be6a3ba36728aece", "sha256_in_prefix": "4019bf7436a7e718d32797f8a19842afbc66be60504fedf4be6a3ba36728aece", "size_in_bytes": 189}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/__pycache__/invariants.cpython-311.pyc", "path_type": "hardlink", "sha256": "cb45bad42b0515c76c7e0f7496c22fa938a3139d120c0e2ed5828967ef26b374", "sha256_in_prefix": "cb45bad42b0515c76c7e0f7496c22fa938a3139d120c0e2ed5828967ef26b374", "size_in_bytes": 31740}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/__pycache__/strategies.cpython-311.pyc", "path_type": "hardlink", "sha256": "add421de46ceb43eeb75c6ff4b1483ad0479e22804be317740ffdad7cd6bac56", "sha256_in_prefix": "add421de46ceb43eeb75c6ff4b1483ad0479e22804be317740ffdad7cd6bac56", "size_in_bytes": 33105}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_add.cpython-311.pyc", "path_type": "hardlink", "sha256": "60c9ca1da12b64be59885e09b473a64ae40f04bd6e7de3c094409d3adf28e8d0", "sha256_in_prefix": "60c9ca1da12b64be59885e09b473a64ae40f04bd6e7de3c094409d3adf28e8d0", "size_in_bytes": 15186}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_base64_conversion.cpython-311.pyc", "path_type": "hardlink", "sha256": "fb75e828af6377c9316353443d8f34e7d00c8385cb3b37972bec1120a8f0eff6", "sha256_in_prefix": "fb75e828af6377c9316353443d8f34e7d00c8385cb3b37972bec1120a8f0eff6", "size_in_bytes": 4412}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_client_url.cpython-311.pyc", "path_type": "hardlink", "sha256": "a6119e3528db6851ea32785ffc9db2609b5e37d98602c68faadc8523e29b46cb", "sha256_in_prefix": "a6119e3528db6851ea32785ffc9db2609b5e37d98602c68faadc8523e29b46cb", "size_in_bytes": 7216}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_collections.cpython-311.pyc", "path_type": "hardlink", "sha256": "9722b0f92b9d900796bd5b1e53d3c72498c39f1e90a4e7bbb3b771be3b228d18", "sha256_in_prefix": "9722b0f92b9d900796bd5b1e53d3c72498c39f1e90a4e7bbb3b771be3b228d18", "size_in_bytes": 16480}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_collections_with_database_tenant.cpython-311.pyc", "path_type": "hardlink", "sha256": "3c0fb1b8d958ac7133c3e46cdeeeff416100754d7c63215a9c26cbb70d89a77a", "sha256_in_prefix": "3c0fb1b8d958ac7133c3e46cdeeeff416100754d7c63215a9c26cbb70d89a77a", "size_in_bytes": 10038}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_collections_with_database_tenant_overwrite.cpython-311.pyc", "path_type": "hardlink", "sha256": "e94725dc886475cb0c05aff58b4685f359d36e0dda03e1b07621100e3ed92524", "sha256_in_prefix": "e94725dc886475cb0c05aff58b4685f359d36e0dda03e1b07621100e3ed92524", "size_in_bytes": 11045}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_cross_version_persist.cpython-311.pyc", "path_type": "hardlink", "sha256": "4b8daddf4318b303a5920c2d0df2ea8cd27a164f3f3bb24ddb755a46c5be690c", "sha256_in_prefix": "4b8daddf4318b303a5920c2d0df2ea8cd27a164f3f3bb24ddb755a46c5be690c", "size_in_bytes": 16760}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_embeddings.cpython-311.pyc", "path_type": "hardlink", "sha256": "eb297aa76cdbb2c0a4d1064a21dbbc8a0b97a245d070e8d66529b480c79a86d9", "sha256_in_prefix": "eb297aa76cdbb2c0a4d1064a21dbbc8a0b97a245d070e8d66529b480c79a86d9", "size_in_bytes": 65037}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_filtering.cpython-311.pyc", "path_type": "hardlink", "sha256": "8b676aeec698559d875e1a1fd4e4523bd1fe1ce799a0f351d5db7dfd126a2a58", "sha256_in_prefix": "8b676aeec698559d875e1a1fd4e4523bd1fe1ce799a0f351d5db7dfd126a2a58", "size_in_bytes": 26850}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_fork.cpython-311.pyc", "path_type": "hardlink", "sha256": "94f3565a3cf912068ad72b964f395253021c19038cc91478c9b0340ade79fc9e", "sha256_in_prefix": "94f3565a3cf912068ad72b964f395253021c19038cc91478c9b0340ade79fc9e", "size_in_bytes": 14737}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_persist.cpython-311.pyc", "path_type": "hardlink", "sha256": "92001cd0b63d1fee8d938f9ef0214ec85f2020ebed4abd6d480164824383d403", "sha256_in_prefix": "92001cd0b63d1fee8d938f9ef0214ec85f2020ebed4abd6d480164824383d403", "size_in_bytes": 26926}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/__pycache__/test_restart_persist.cpython-311.pyc", "path_type": "hardlink", "sha256": "d1d7fa122eebc52306b583772aebcc1aee7000229767d09df7d4b9ad485b1734", "sha256_in_prefix": "d1d7fa122eebc52306b583772aebcc1aee7000229767d09df7d4b9ad485b1734", "size_in_bytes": 4920}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/invariants.py", "path_type": "hardlink", "sha256": "fdb5dd84da6d78e3d79d4b268856dbf4374640f1706f9bf2e368c278a9c94b8f", "sha256_in_prefix": "fdb5dd84da6d78e3d79d4b268856dbf4374640f1706f9bf2e368c278a9c94b8f", "size_in_bytes": 22856}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/strategies.py", "path_type": "hardlink", "sha256": "ae8d22fa79e3cbb0b8785831c4f7e9b845598ca5376f9c1c6924b418728e9737", "sha256_in_prefix": "ae8d22fa79e3cbb0b8785831c4f7e9b845598ca5376f9c1c6924b418728e9737", "size_in_bytes": 25453}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/test_add.py", "path_type": "hardlink", "sha256": "dbbe0db44d96a312b0d55903406eefc35a8a567dcea87e10bfebd33f1fd8979d", "sha256_in_prefix": "dbbe0db44d96a312b0d55903406eefc35a8a567dcea87e10bfebd33f1fd8979d", "size_in_bytes": 11886}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/test_base64_conversion.py", "path_type": "hardlink", "sha256": "4f007323236aa1921feb04fe7882dbb4831ff3eadb18ab62635e435f0a39b3e7", "sha256_in_prefix": "4f007323236aa1921feb04fe7882dbb4831ff3eadb18ab62635e435f0a39b3e7", "size_in_bytes": 2822}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/test_client_url.py", "path_type": "hardlink", "sha256": "3e34fdbc706749ac10f49d958664b55036d9a4028c2e8e76677233b922cd9d1e", "sha256_in_prefix": "3e34fdbc706749ac10f49d958664b55036d9a4028c2e8e76677233b922cd9d1e", "size_in_bytes": 3881}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/test_collections.py", "path_type": "hardlink", "sha256": "ca784d4e9bb817df0f8b22b7861980203fe852062486cd107b51dc1a0982c908", "sha256_in_prefix": "ca784d4e9bb817df0f8b22b7861980203fe852062486cd107b51dc1a0982c908", "size_in_bytes": 12327}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/test_collections_with_database_tenant.py", "path_type": "hardlink", "sha256": "c6f5cd58027191e7a1dd498fa20e5c02f2f313609ebbc81103cd479d7cdde3e9", "sha256_in_prefix": "c6f5cd58027191e7a1dd498fa20e5c02f2f313609ebbc81103cd479d7cdde3e9", "size_in_bytes": 6047}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/test_collections_with_database_tenant_overwrite.py", "path_type": "hardlink", "sha256": "c7b4801f03738e63b87680c5807d5c08612333db0348e086c31a89aa9f77f752", "sha256_in_prefix": "c7b4801f03738e63b87680c5807d5c08612333db0348e086c31a89aa9f77f752", "size_in_bytes": 7713}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/test_cross_version_persist.py", "path_type": "hardlink", "sha256": "67ebebd709452c09de122d61decbe85330022f490aa678e0141ce8851909f832", "sha256_in_prefix": "67ebebd709452c09de122d61decbe85330022f490aa678e0141ce8851909f832", "size_in_bytes": 13569}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/test_embeddings.py", "path_type": "hardlink", "sha256": "04181304d87b358f9c2ecd0e1f76e7735b914ed1eeda1b69d067bdce119b6b26", "sha256_in_prefix": "04181304d87b358f9c2ecd0e1f76e7735b914ed1eeda1b69d067bdce119b6b26", "size_in_bytes": 52364}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/test_filtering.py", "path_type": "hardlink", "sha256": "8accd4c3772b6271a8ffa9e687e4a54159a6abee93eaf3b815bd03b44f77a0c7", "sha256_in_prefix": "8accd4c3772b6271a8ffa9e687e4a54159a6abee93eaf3b815bd03b44f77a0c7", "size_in_bytes": 19506}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/test_fork.py", "path_type": "hardlink", "sha256": "2b1fbcdf44ce8a0a90af8fda7c2c2c9fd6062dca157287a1a2eb9e0b9eb3cc35", "sha256_in_prefix": "2b1fbcdf44ce8a0a90af8fda7c2c2c9fd6062dca157287a1a2eb9e0b9eb3cc35", "size_in_bytes": 10042}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/test_persist.py", "path_type": "hardlink", "sha256": "a48548075fdea5fcce8a261c52fd28fc3075541caab155338a6f3822e8b40636", "sha256_in_prefix": "a48548075fdea5fcce8a261c52fd28fc3075541caab155338a6f3822e8b40636", "size_in_bytes": 23573}, {"_path": "lib/python3.11/site-packages/chromadb/test/property/test_restart_persist.py", "path_type": "hardlink", "sha256": "52752ddfe5721cef37b466effc381e1078311e9e8f7f801b5c2e413fea69f9da", "sha256_in_prefix": "52752ddfe5721cef37b466effc381e1078311e9e8f7f801b5c2e413fea69f9da", "size_in_bytes": 3347}, {"_path": "lib/python3.11/site-packages/chromadb/test/segment/distributed/__pycache__/test_memberlist_provider.cpython-311.pyc", "path_type": "hardlink", "sha256": "84ba2fcd6c611108a3d6931a1258f1de6ef21aace7c82f4457cbd1c4fe8d0bb7", "sha256_in_prefix": "84ba2fcd6c611108a3d6931a1258f1de6ef21aace7c82f4457cbd1c4fe8d0bb7", "size_in_bytes": 6507}, {"_path": "lib/python3.11/site-packages/chromadb/test/segment/distributed/__pycache__/test_rendezvous_hash.cpython-311.pyc", "path_type": "hardlink", "sha256": "115fff409ec5ef83c68fc124d73f39b04334cf801512136ea0ba58a19d7fbdd4", "sha256_in_prefix": "115fff409ec5ef83c68fc124d73f39b04334cf801512136ea0ba58a19d7fbdd4", "size_in_bytes": 3935}, {"_path": "lib/python3.11/site-packages/chromadb/test/segment/distributed/test_memberlist_provider.py", "path_type": "hardlink", "sha256": "764a962b28233e3576f08474e0490f34c7b1fbd1efbe7e9758731a3a89e11f33", "sha256_in_prefix": "764a962b28233e3576f08474e0490f34c7b1fbd1efbe7e9758731a3a89e11f33", "size_in_bytes": 3836}, {"_path": "lib/python3.11/site-packages/chromadb/test/segment/distributed/test_rendezvous_hash.py", "path_type": "hardlink", "sha256": "ecec8ed23e7066bac958c66595c631d12d54c8726df2cde4a57c0032aa0ad59d", "sha256_in_prefix": "ecec8ed23e7066bac958c66595c631d12d54c8726df2cde4a57c0032aa0ad59d", "size_in_bytes": 2253}, {"_path": "lib/python3.11/site-packages/chromadb/test/stress/__pycache__/test_many_collections.cpython-311.pyc", "path_type": "hardlink", "sha256": "884621e64409a505910a252e55df6ad8a61d0b9b889b0b1d02483e4e37ab14b6", "sha256_in_prefix": "884621e64409a505910a252e55df6ad8a61d0b9b889b0b1d02483e4e37ab14b6", "size_in_bytes": 1930}, {"_path": "lib/python3.11/site-packages/chromadb/test/stress/test_many_collections.py", "path_type": "hardlink", "sha256": "5b3a8d871c4fbcd50a5151286a344bec3898b718ad146f3c00c1258f1d2c2b37", "sha256_in_prefix": "5b3a8d871c4fbcd50a5151286a344bec3898b718ad146f3c00c1258f1d2c2b37", "size_in_bytes": 1117}, {"_path": "lib/python3.11/site-packages/chromadb/test/test_api.py", "path_type": "hardlink", "sha256": "5337abd5d4010361dfd8bb654fd69ab284d784a783dc3ebc09bf875d677e5b28", "sha256_in_prefix": "5337abd5d4010361dfd8bb654fd69ab284d784a783dc3ebc09bf875d677e5b28", "size_in_bytes": 56218}, {"_path": "lib/python3.11/site-packages/chromadb/test/test_chroma.py", "path_type": "hardlink", "sha256": "60c61fc408bf8471b0768714b0b10dcee08592e0291349a56b9e1a64f80c218f", "sha256_in_prefix": "60c61fc408bf8471b0768714b0b10dcee08592e0291349a56b9e1a64f80c218f", "size_in_bytes": 4329}, {"_path": "lib/python3.11/site-packages/chromadb/test/test_cli.py", "path_type": "hardlink", "sha256": "6a1e6f1e2c9e4d0d4d572e3e5e76001525f88b4e34254e5177e2992216812f56", "sha256_in_prefix": "6a1e6f1e2c9e4d0d4d572e3e5e76001525f88b4e34254e5177e2992216812f56", "size_in_bytes": 5049}, {"_path": "lib/python3.11/site-packages/chromadb/test/test_client.py", "path_type": "hardlink", "sha256": "02db94aa378790faee4f3c2c04699333b5743ddf75582f5487f90d5e269010ce", "sha256_in_prefix": "02db94aa378790faee4f3c2c04699333b5743ddf75582f5487f90d5e269010ce", "size_in_bytes": 3563}, {"_path": "lib/python3.11/site-packages/chromadb/test/test_config.py", "path_type": "hardlink", "sha256": "fc6a59cd87d73da82d59fdec57d68704a6c8d2079dce3ff19dec0ccc0d94c5ab", "sha256_in_prefix": "fc6a59cd87d73da82d59fdec57d68704a6c8d2079dce3ff19dec0ccc0d94c5ab", "size_in_bytes": 4127}, {"_path": "lib/python3.11/site-packages/chromadb/test/test_multithreaded.py", "path_type": "hardlink", "sha256": "f34d4bebfa1d2a8ae7a09b8fecd5e5f513f847bd22d45cec949052a999f9074c", "sha256_in_prefix": "f34d4bebfa1d2a8ae7a09b8fecd5e5f513f847bd22d45cec949052a999f9074c", "size_in_bytes": 8545}, {"_path": "lib/python3.11/site-packages/chromadb/test/utils/__pycache__/cross_version.cpython-311.pyc", "path_type": "hardlink", "sha256": "40dade05118a98da1c70ae9d6bebf3bbf3e8e6f95419ecba5864713088442f03", "sha256_in_prefix": "40dade05118a98da1c70ae9d6bebf3bbf3e8e6f95419ecba5864713088442f03", "size_in_bytes": 4397}, {"_path": "lib/python3.11/site-packages/chromadb/test/utils/__pycache__/distance_functions.cpython-311.pyc", "path_type": "hardlink", "sha256": "a7078aba695e0f4021463d4267fc082f4cd671a891d320a5cec64726c6c08803", "sha256_in_prefix": "a7078aba695e0f4021463d4267fc082f4cd671a891d320a5cec64726c6c08803", "size_in_bytes": 655}, {"_path": "lib/python3.11/site-packages/chromadb/test/utils/__pycache__/test_embedding_function_schemas.cpython-311.pyc", "path_type": "hardlink", "sha256": "cc03d816841dffa4395671cc00d493d5c3c24fd88247604574e03bbaab3639eb", "sha256_in_prefix": "cc03d816841dffa4395671cc00d493d5c3c24fd88247604574e03bbaab3639eb", "size_in_bytes": 6525}, {"_path": "lib/python3.11/site-packages/chromadb/test/utils/__pycache__/test_result_df_transform.cpython-311.pyc", "path_type": "hardlink", "sha256": "cbde71d093cdf5b957d8fe33225e21266e48ce7c2c620453e53652dd49615116", "sha256_in_prefix": "cbde71d093cdf5b957d8fe33225e21266e48ce7c2c620453e53652dd49615116", "size_in_bytes": 9341}, {"_path": "lib/python3.11/site-packages/chromadb/test/utils/__pycache__/wait_for_version_increase.cpython-311.pyc", "path_type": "hardlink", "sha256": "2dde8438b0026f4629ebfc03bf935c6a8aa2f1ff89284c62e297c25f51db9152", "sha256_in_prefix": "2dde8438b0026f4629ebfc03bf935c6a8aa2f1ff89284c62e297c25f51db9152", "size_in_bytes": 1695}, {"_path": "lib/python3.11/site-packages/chromadb/test/utils/cross_version.py", "path_type": "hardlink", "sha256": "ec6d2518ea4e81b070ec287120b5bad987bf2789b8a8736a3961f9f82e99814a", "sha256_in_prefix": "ec6d2518ea4e81b070ec287120b5bad987bf2789b8a8736a3961f9f82e99814a", "size_in_bytes": 2372}, {"_path": "lib/python3.11/site-packages/chromadb/test/utils/distance_functions.py", "path_type": "hardlink", "sha256": "e08101057ae9ad6fae1e134e88edb2cccbd53b84886990024f10ba2565d12543", "sha256_in_prefix": "e08101057ae9ad6fae1e134e88edb2cccbd53b84886990024f10ba2565d12543", "size_in_bytes": 184}, {"_path": "lib/python3.11/site-packages/chromadb/test/utils/test_embedding_function_schemas.py", "path_type": "hardlink", "sha256": "aac130c66b3983846d21ad0ce5d4ae9a98a6cc2227dc419a23596b34b544e3be", "sha256_in_prefix": "aac130c66b3983846d21ad0ce5d4ae9a98a6cc2227dc419a23596b34b544e3be", "size_in_bytes": 4627}, {"_path": "lib/python3.11/site-packages/chromadb/test/utils/test_result_df_transform.py", "path_type": "hardlink", "sha256": "ec87deeec0709535def4dc18f93847812b2882d8fbe0c1d4994021b7a4b4a739", "sha256_in_prefix": "ec87deeec0709535def4dc18f93847812b2882d8fbe0c1d4994021b7a4b4a739", "size_in_bytes": 5946}, {"_path": "lib/python3.11/site-packages/chromadb/test/utils/wait_for_version_increase.py", "path_type": "hardlink", "sha256": "ab9685a0e4ae1c4d1343ecf9522666924dad10ef60473609e4bcbc955bc071e7", "sha256_in_prefix": "ab9685a0e4ae1c4d1343ecf9522666924dad10ef60473609e4bcbc955bc071e7", "size_in_bytes": 964}, {"_path": "lib/python3.11/site-packages/chromadb/types.py", "path_type": "hardlink", "sha256": "17a0d53e777ca07708fd2a287c992363ceff5bbe494981ea7583e2c3005291ac", "sha256_in_prefix": "17a0d53e777ca07708fd2a287c992363ceff5bbe494981ea7583e2c3005291ac", "size_in_bytes": 9317}, {"_path": "lib/python3.11/site-packages/chromadb/utils/__init__.py", "path_type": "hardlink", "sha256": "faddb9594e2844a5d4dc8e0f17c1bfb9533650dceed527e37d2d432f63f45954", "sha256_in_prefix": "faddb9594e2844a5d4dc8e0f17c1bfb9533650dceed527e37d2d432f63f45954", "size_in_bytes": 378}, {"_path": "lib/python3.11/site-packages/chromadb/utils/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "bbbd0022019ecfa5668556f8538847d0b1a96b9975e5f2e9e9b4b4243d8ad76e", "sha256_in_prefix": "bbbd0022019ecfa5668556f8538847d0b1a96b9975e5f2e9e9b4b4243d8ad76e", "size_in_bytes": 984}, {"_path": "lib/python3.11/site-packages/chromadb/utils/__pycache__/async_to_sync.cpython-311.pyc", "path_type": "hardlink", "sha256": "cb1ec77d9d4f67efded15396adc11f72df44930c7fa5910ba9789b015af5da7b", "sha256_in_prefix": "cb1ec77d9d4f67efded15396adc11f72df44930c7fa5910ba9789b015af5da7b", "size_in_bytes": 3334}, {"_path": "lib/python3.11/site-packages/chromadb/utils/__pycache__/batch_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "82cab66469827747e6949b86ffb1222cb6c4896a133d58510c6a9cec048bceb9", "sha256_in_prefix": "82cab66469827747e6949b86ffb1222cb6c4896a133d58510c6a9cec048bceb9", "size_in_bytes": 1866}, {"_path": "lib/python3.11/site-packages/chromadb/utils/__pycache__/data_loaders.cpython-311.pyc", "path_type": "hardlink", "sha256": "b2dfd48eb76d1c9ab63d08c7d634785b1b8a50892ca7b735f54e8fbcfc609300", "sha256_in_prefix": "b2dfd48eb76d1c9ab63d08c7d634785b1b8a50892ca7b735f54e8fbcfc609300", "size_in_bytes": 3077}, {"_path": "lib/python3.11/site-packages/chromadb/utils/__pycache__/delete_file.cpython-311.pyc", "path_type": "hardlink", "sha256": "761e6e103580d3417ab6861c1220a13141748c9dc6ea7a4e9663b2a5f83b48c9", "sha256_in_prefix": "761e6e103580d3417ab6861c1220a13141748c9dc6ea7a4e9663b2a5f83b48c9", "size_in_bytes": 1876}, {"_path": "lib/python3.11/site-packages/chromadb/utils/__pycache__/directory.cpython-311.pyc", "path_type": "hardlink", "sha256": "322a4ffd7ef586b7b47643e67c3dca05df523574b994db9baf556d9c9a13f63a", "sha256_in_prefix": "322a4ffd7ef586b7b47643e67c3dca05df523574b994db9baf556d9c9a13f63a", "size_in_bytes": 1118}, {"_path": "lib/python3.11/site-packages/chromadb/utils/__pycache__/distance_functions.cpython-311.pyc", "path_type": "hardlink", "sha256": "ad20ab9d8b4ae3899e9a2fc0288eeda7344008655efb8a0ffb3358c61ae2ec08", "sha256_in_prefix": "ad20ab9d8b4ae3899e9a2fc0288eeda7344008655efb8a0ffb3358c61ae2ec08", "size_in_bytes": 1970}, {"_path": "lib/python3.11/site-packages/chromadb/utils/__pycache__/fastapi.cpython-311.pyc", "path_type": "hardlink", "sha256": "3093a5ccf3e4f7aa5494e58c27e87d7ad31d467bfffc86219b3ab5e1dfd8dfd3", "sha256_in_prefix": "3093a5ccf3e4f7aa5494e58c27e87d7ad31d467bfffc86219b3ab5e1dfd8dfd3", "size_in_bytes": 1221}, {"_path": "lib/python3.11/site-packages/chromadb/utils/__pycache__/lru_cache.cpython-311.pyc", "path_type": "hardlink", "sha256": "6b091f89265c1b7849152bf8a87b766d2889a4fbe7817a5fd544c19fa69ecf05", "sha256_in_prefix": "6b091f89265c1b7849152bf8a87b766d2889a4fbe7817a5fd544c19fa69ecf05", "size_in_bytes": 2288}, {"_path": "lib/python3.11/site-packages/chromadb/utils/__pycache__/messageid.cpython-311.pyc", "path_type": "hardlink", "sha256": "b151272a86f4b8b6efd19c149f05ec38d3bbd16d5049ec36d6e13a91e7d57fef", "sha256_in_prefix": "b151272a86f4b8b6efd19c149f05ec38d3bbd16d5049ec36d6e13a91e7d57fef", "size_in_bytes": 753}, {"_path": "lib/python3.11/site-packages/chromadb/utils/__pycache__/read_write_lock.cpython-311.pyc", "path_type": "hardlink", "sha256": "4e13a26fecff18ba7d3130abd7d91034b6c55099133735dcf37903093c962691", "sha256_in_prefix": "4e13a26fecff18ba7d3130abd7d91034b6c55099133735dcf37903093c962691", "size_in_bytes": 4924}, {"_path": "lib/python3.11/site-packages/chromadb/utils/__pycache__/rendezvous_hash.cpython-311.pyc", "path_type": "hardlink", "sha256": "30c9783511b6f9e8f0d1ab60a31584de7b2d7aecc30ff6fd6cc736da85a2b0f6", "sha256_in_prefix": "30c9783511b6f9e8f0d1ab60a31584de7b2d7aecc30ff6fd6cc736da85a2b0f6", "size_in_bytes": 2981}, {"_path": "lib/python3.11/site-packages/chromadb/utils/__pycache__/results.cpython-311.pyc", "path_type": "hardlink", "sha256": "8ca872384d5f2466215cbcba22999153b9b62d8a00780fc97ab150788ffcb89b", "sha256_in_prefix": "8ca872384d5f2466215cbcba22999153b9b62d8a00780fc97ab150788ffcb89b", "size_in_bytes": 5416}, {"_path": "lib/python3.11/site-packages/chromadb/utils/async_to_sync.py", "path_type": "hardlink", "sha256": "34b16ce2515a26e86b53f5c4404143ba67589ff133678fd628087088e3b77218", "sha256_in_prefix": "34b16ce2515a26e86b53f5c4404143ba67589ff133678fd628087088e3b77218", "size_in_bytes": 1678}, {"_path": "lib/python3.11/site-packages/chromadb/utils/batch_utils.py", "path_type": "hardlink", "sha256": "ae205d3dfdbe5b9ac195a1f35381dda530ed81bad0e877a431271d3d30620840", "sha256_in_prefix": "ae205d3dfdbe5b9ac195a1f35381dda530ed81bad0e877a431271d3d30620840", "size_in_bytes": 1239}, {"_path": "lib/python3.11/site-packages/chromadb/utils/data_loaders.py", "path_type": "hardlink", "sha256": "53e740a0b8a98479ba1594c0c144df3feef22de1fc4c62c9348590c1ebaa2ea8", "sha256_in_prefix": "53e740a0b8a98479ba1594c0c144df3feef22de1fc4c62c9348590c1ebaa2ea8", "size_in_bytes": 1387}, {"_path": "lib/python3.11/site-packages/chromadb/utils/delete_file.py", "path_type": "hardlink", "sha256": "154aa57647aa1501393b64be687e3311bdbb270653436fcf2bbb2a3111e937be", "sha256_in_prefix": "154aa57647aa1501393b64be687e3311bdbb270653436fcf2bbb2a3111e937be", "size_in_bytes": 1130}, {"_path": "lib/python3.11/site-packages/chromadb/utils/directory.py", "path_type": "hardlink", "sha256": "0713374689ad3b5f992b9d2458c68d2415204ee96011835c602ed868313bda00", "sha256_in_prefix": "0713374689ad3b5f992b9d2458c68d2415204ee96011835c602ed868313bda00", "size_in_bytes": 622}, {"_path": "lib/python3.11/site-packages/chromadb/utils/distance_functions.py", "path_type": "hardlink", "sha256": "a851532c12d33038bfbad32b969d3b7d08b44b1166b6b9ea04e8344df003118e", "sha256_in_prefix": "a851532c12d33038bfbad32b969d3b7d08b44b1166b6b9ea04e8344df003118e", "size_in_bytes": 957}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__init__.py", "path_type": "hardlink", "sha256": "2696f6bb99648f4297e469a0e12abdb97a5f6cc06f7782066e4fdba122ea6278", "sha256_in_prefix": "2696f6bb99648f4297e469a0e12abdb97a5f6cc06f7782066e4fdba122ea6278", "size_in_bytes": 8295}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "9a9f6e1c0874e9440a9f18d540957e677e18649081ab96899c70918e1e503e70", "sha256_in_prefix": "9a9f6e1c0874e9440a9f18d540957e677e18649081ab96899c70918e1e503e70", "size_in_bytes": 8945}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/amazon_bedrock_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "8fcb9366de14770976a9411c0ca2de39ada2f1a09895c9257ddf8ab3ea5bca5d", "sha256_in_prefix": "8fcb9366de14770976a9411c0ca2de39ada2f1a09895c9257ddf8ab3ea5bca5d", "size_in_bytes": 6927}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/baseten_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "74286a109721958b98dd74abea72e649a6f5396dd64b1a16dce61cbb3a5238a0", "sha256_in_prefix": "74286a109721958b98dd74abea72e649a6f5396dd64b1a16dce61cbb3a5238a0", "size_in_bytes": 4753}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/chroma_langchain_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "3225bf6f8f8fbc297165a7471d64332844cec0a440fce9a2fefe6fe75ac50229", "sha256_in_prefix": "3225bf6f8f8fbc297165a7471d64332844cec0a440fce9a2fefe6fe75ac50229", "size_in_bytes": 8879}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/cloudflare_workers_ai_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "787b4955d47314f70bf81d1646adb32343306d3fb43e5c95ae5102454cffc509", "sha256_in_prefix": "787b4955d47314f70bf81d1646adb32343306d3fb43e5c95ae5102454cffc509", "size_in_bytes": 7877}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/cohere_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "743827fc48fe5e6f065e7de2b12da7d33bd07fc67d68d694c92b2ffd03a92885", "sha256_in_prefix": "743827fc48fe5e6f065e7de2b12da7d33bd07fc67d68d694c92b2ffd03a92885", "size_in_bytes": 9759}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/google_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "38dbb81ee618320f4f3f6e42c0f1206ee201db17097160742d3e37c7d99659d4", "sha256_in_prefix": "38dbb81ee618320f4f3f6e42c0f1206ee201db17097160742d3e37c7d99659d4", "size_in_bytes": 18279}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/huggingface_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "4534b19c7bf99c91a8b20a58c3b5b536da516dd9666cbd19e6aad329a4e1f2e4", "sha256_in_prefix": "4534b19c7bf99c91a8b20a58c3b5b536da516dd9666cbd19e6aad329a4e1f2e4", "size_in_bytes": 12042}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/instructor_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "a2adefeb99eca36fb11c2c4e072199ee229eea409129f96fc2e51175e6a8a022", "sha256_in_prefix": "a2adefeb99eca36fb11c2c4e072199ee229eea409129f96fc2e51175e6a8a022", "size_in_bytes": 6519}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/jina_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "fc208cde1598a5948390669ec81f9ee8fc128a27464da929b6166a92adfcb488", "sha256_in_prefix": "fc208cde1598a5948390669ec81f9ee8fc128a27464da929b6166a92adfcb488", "size_in_bytes": 9844}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/mistral_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "fe6896822afc047380bc2f1cb48e66d850f7718a39febeca4c993a622fbacdb0", "sha256_in_prefix": "fe6896822afc047380bc2f1cb48e66d850f7718a39febeca4c993a622fbacdb0", "size_in_bytes": 5702}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/morph_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "eb448d60a757f98211eb1a2468cd27c4c6dec59ff06f89ec19ff276fac805e75", "sha256_in_prefix": "eb448d60a757f98211eb1a2468cd27c4c6dec59ff06f89ec19ff276fac805e75", "size_in_bytes": 6978}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/ollama_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "58b6eda1a8d5f0ae96d8675cf2d3ea4b2c81631c123372cccf61762c36154130", "sha256_in_prefix": "58b6eda1a8d5f0ae96d8675cf2d3ea4b2c81631c123372cccf61762c36154130", "size_in_bytes": 6399}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/onnx_mini_lm_l6_v2.cpython-311.pyc", "path_type": "hardlink", "sha256": "d2ae269628e17c0fee0982e1ea417866ad36855ed398a7c40b7ec1efacaf50a1", "sha256_in_prefix": "d2ae269628e17c0fee0982e1ea417866ad36855ed398a7c40b7ec1efacaf50a1", "size_in_bytes": 20107}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/open_clip_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "b5fc3d7126089a02324c5f9116c9de8d148a8b938e9c3d3e560021485e7e55e0", "sha256_in_prefix": "b5fc3d7126089a02324c5f9116c9de8d148a8b938e9c3d3e560021485e7e55e0", "size_in_bytes": 10132}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/openai_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "5990c54c674c29fe64699769ddd062622253443972f32276a4276350739abdf7", "sha256_in_prefix": "5990c54c674c29fe64699769ddd062622253443972f32276a4276350739abdf7", "size_in_bytes": 9616}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/roboflow_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "c3e8880041252c95c0dc79544687bdc442c368fb44a154f9ab0f1b1936909255", "sha256_in_prefix": "c3e8880041252c95c0dc79544687bdc442c368fb44a154f9ab0f1b1936909255", "size_in_bytes": 7550}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/sentence_transformer_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "6e60ad76dbeba56d97b891f2a4955a06fdef049153614c18b8c585a86bc18a51", "sha256_in_prefix": "6e60ad76dbeba56d97b891f2a4955a06fdef049153614c18b8c585a86bc18a51", "size_in_bytes": 6500}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/text2vec_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "c759d8ee094f23e0cbaa848773c2b32ff07ff3800f4226fe5e7a3c54b751a02b", "sha256_in_prefix": "c759d8ee094f23e0cbaa848773c2b32ff07ff3800f4226fe5e7a3c54b751a02b", "size_in_bytes": 5439}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/together_ai_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "11da5c5e086a647f1725807c424f2f5aa637cd93f02dd0618b33d7e032e9ac2c", "sha256_in_prefix": "11da5c5e086a647f1725807c424f2f5aa637cd93f02dd0618b33d7e032e9ac2c", "size_in_bytes": 7187}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/__pycache__/voyageai_embedding_function.cpython-311.pyc", "path_type": "hardlink", "sha256": "f478851a1729881fccffc8e806a381f3a5d8f352e2d462cb58aa393c72140daa", "sha256_in_prefix": "f478851a1729881fccffc8e806a381f3a5d8f352e2d462cb58aa393c72140daa", "size_in_bytes": 6882}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/amazon_bedrock_embedding_function.py", "path_type": "hardlink", "sha256": "a9a9734fe8d40e7ac46ae131b15950031db5b563f3d5061db53496ff51ed8641", "sha256_in_prefix": "a9a9734fe8d40e7ac46ae131b15950031db5b563f3d5061db53496ff51ed8641", "size_in_bytes": 4787}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/baseten_embedding_function.py", "path_type": "hardlink", "sha256": "8c6add9d4f23221c3d22a54a580c7c3345efce67c92e66e143af7bbac2d065ea", "sha256_in_prefix": "8c6add9d4f23221c3d22a54a580c7c3345efce67c92e66e143af7bbac2d065ea", "size_in_bytes": 3724}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/chroma_langchain_embedding_function.py", "path_type": "hardlink", "sha256": "8e982f226f9ab90a24d3ab83ccda47b544397e53d5e1084b1293ff8459219964", "sha256_in_prefix": "8e982f226f9ab90a24d3ab83ccda47b544397e53d5e1084b1293ff8459219964", "size_in_bytes": 6158}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/cloudflare_workers_ai_embedding_function.py", "path_type": "hardlink", "sha256": "192356615212008626d1522cc6c1de201a28dbddee188492897d89ead88c8136", "sha256_in_prefix": "192356615212008626d1522cc6c1de201a28dbddee188492897d89ead88c8136", "size_in_bytes": 5227}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/cohere_embedding_function.py", "path_type": "hardlink", "sha256": "2f33347dc38a7a5cef0ef67623fc7de397ef186fca293f2841ba778917119de4", "sha256_in_prefix": "2f33347dc38a7a5cef0ef67623fc7de397ef186fca293f2841ba778917119de4", "size_in_bytes": 6094}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/google_embedding_function.py", "path_type": "hardlink", "sha256": "5c7b64ef5bd3c844d12a49aa6a8cafab8cc5e881dc151928a03ac38816f041a9", "sha256_in_prefix": "5c7b64ef5bd3c844d12a49aa6a8cafab8cc5e881dc151928a03ac38816f041a9", "size_in_bytes": 14335}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/huggingface_embedding_function.py", "path_type": "hardlink", "sha256": "ed57d7baad00ecb0b8fce3ca58c857660e9829dac0193a0ac30c4dbeac3b1139", "sha256_in_prefix": "ed57d7baad00ecb0b8fce3ca58c857660e9829dac0193a0ac30c4dbeac3b1139", "size_in_bytes": 8402}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/instructor_embedding_function.py", "path_type": "hardlink", "sha256": "002c1b750367ed6ce131cf3fcf54a062b9d9de4e420a785f0461005617638721", "sha256_in_prefix": "002c1b750367ed6ce131cf3fcf54a062b9d9de4e420a785f0461005617638721", "size_in_bytes": 4244}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/jina_embedding_function.py", "path_type": "hardlink", "sha256": "0018f384407db5ea9e49e3cd40b4062476c0132af5530741640cae55de57743d", "sha256_in_prefix": "0018f384407db5ea9e49e3cd40b4062476c0132af5530741640cae55de57743d", "size_in_bytes": 7498}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/mistral_embedding_function.py", "path_type": "hardlink", "sha256": "7f5ae7c7e2a120f9303ee4b4395c130ac6b767c6a56c8830470d6523d3a2a31f", "sha256_in_prefix": "7f5ae7c7e2a120f9303ee4b4395c130ac6b767c6a56c8830470d6523d3a2a31f", "size_in_bytes": 3123}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/morph_embedding_function.py", "path_type": "hardlink", "sha256": "725406504d7a3caaed7576e407bc9df3f0776dc7735d83e2fdb304b5154f99f2", "sha256_in_prefix": "725406504d7a3caaed7576e407bc9df3f0776dc7735d83e2fdb304b5154f99f2", "size_in_bytes": 5291}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/ollama_embedding_function.py", "path_type": "hardlink", "sha256": "cedde9ca0578379a0dda2a06886d3c435e9e8f609d8b08e0f6aa666fdd6d5022", "sha256_in_prefix": "cedde9ca0578379a0dda2a06886d3c435e9e8f609d8b08e0f6aa666fdd6d5022", "size_in_bytes": 4027}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/onnx_mini_lm_l6_v2.py", "path_type": "hardlink", "sha256": "3bee881392b56e7ec2dea1e9ddf1eb86a7cae2d56ab7131be1b3744108481d5e", "sha256_in_prefix": "3bee881392b56e7ec2dea1e9ddf1eb86a7cae2d56ab7131be1b3744108481d5e", "size_in_bytes": 13873}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/open_clip_embedding_function.py", "path_type": "hardlink", "sha256": "43f626043aafa7064a984ba3da0ecb54ebe2638ea30323ea57b2fbc4d9c86e27", "sha256_in_prefix": "43f626043aafa7064a984ba3da0ecb54ebe2638ea30323ea57b2fbc4d9c86e27", "size_in_bytes": 6070}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/openai_embedding_function.py", "path_type": "hardlink", "sha256": "351e817e6db0b2b6e577d5f4cb707940d8a18014cdef029d56a4ef15c83d9e34", "sha256_in_prefix": "351e817e6db0b2b6e577d5f4cb707940d8a18014cdef029d56a4ef15c83d9e34", "size_in_bytes": 8221}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/roboflow_embedding_function.py", "path_type": "hardlink", "sha256": "cc52aa8219fd3f75c1e67beeea3a4283c17e15ac264abe1ce421a71561bfcf70", "sha256_in_prefix": "cc52aa8219fd3f75c1e67beeea3a4283c17e15ac264abe1ce421a71561bfcf70", "size_in_bytes": 5015}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/schemas/README.md", "path_type": "hardlink", "sha256": "70071daf33e1b86685b3eef4cfc5309adc3d401855c0f411a665b3c98a0b8b72", "sha256_in_prefix": "70071daf33e1b86685b3eef4cfc5309adc3d401855c0f411a665b3c98a0b8b72", "size_in_bytes": 1659}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/schemas/__init__.py", "path_type": "hardlink", "sha256": "dd700a0ee091728828dd6f909530c862651a93e54dd8d58b8ffb9eea1ccb0b88", "sha256_in_prefix": "dd700a0ee091728828dd6f909530c862651a93e54dd8d58b8ffb9eea1ccb0b88", "size_in_bytes": 469}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/schemas/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "811a6454ff9961d0fd2a668823df2a691a2ecc48e559b816664cd223958229d5", "sha256_in_prefix": "811a6454ff9961d0fd2a668823df2a691a2ecc48e559b816664cd223958229d5", "size_in_bytes": 665}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/schemas/__pycache__/registry.cpython-311.pyc", "path_type": "hardlink", "sha256": "43e983dbf43bbdb8f091c0c6b985d712abda0432902d64d4a7750cde81d50f04", "sha256_in_prefix": "43e983dbf43bbdb8f091c0c6b985d712abda0432902d64d4a7750cde81d50f04", "size_in_bytes": 2776}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/schemas/__pycache__/schema_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "f166b84998b5282eb756334bc19c394c5d1e1e5d46321c76dc00b8b1ee8f78f7", "sha256_in_prefix": "f166b84998b5282eb756334bc19c394c5d1e1e5d46321c76dc00b8b1ee8f78f7", "size_in_bytes": 4399}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/schemas/registry.py", "path_type": "hardlink", "sha256": "179e3671ed79198000ceb6fdae6b84624ca90c47352ca15a84f826cca937f612", "sha256_in_prefix": "179e3671ed79198000ceb6fdae6b84624ca90c47352ca15a84f826cca937f612", "size_in_bytes": 1599}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/schemas/schema_utils.py", "path_type": "hardlink", "sha256": "c9b483e6cecce94819d0f0397186e3e20bc97055c3694f7c1cbb916616097648", "sha256_in_prefix": "c9b483e6cecce94819d0f0397186e3e20bc97055c3694f7c1cbb916616097648", "size_in_bytes": 2590}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/sentence_transformer_embedding_function.py", "path_type": "hardlink", "sha256": "2b7c4adcaeb2d8b91123322f3610177ca42563d03a00ff5ca5e53cfdb77789f9", "sha256_in_prefix": "2b7c4adcaeb2d8b91123322f3610177ca42563d03a00ff5ca5e53cfdb77789f9", "size_in_bytes": 4579}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/text2vec_embedding_function.py", "path_type": "hardlink", "sha256": "07a971550fd46e782fb6c21090eeec4d692065175a505eee51cca09240447d23", "sha256_in_prefix": "07a971550fd46e782fb6c21090eeec4d692065175a505eee51cca09240447d23", "size_in_bytes": 3081}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/together_ai_embedding_function.py", "path_type": "hardlink", "sha256": "4bd33196d3eef08088c4df7e184c607fb981e0fb0eb6f87553f384c1875e1ae7", "sha256_in_prefix": "4bd33196d3eef08088c4df7e184c607fb981e0fb0eb6f87553f384c1875e1ae7", "size_in_bytes": 4460}, {"_path": "lib/python3.11/site-packages/chromadb/utils/embedding_functions/voyageai_embedding_function.py", "path_type": "hardlink", "sha256": "f2e5023d7ae9821ea57e18ddfce80fc37147b109d89496585eabec96c79e60fd", "sha256_in_prefix": "f2e5023d7ae9821ea57e18ddfce80fc37147b109d89496585eabec96c79e60fd", "size_in_bytes": 4721}, {"_path": "lib/python3.11/site-packages/chromadb/utils/fastapi.py", "path_type": "hardlink", "sha256": "2c862f523c015715ac5dabc2e74c0b9e48a9a7ee7f3b0a25c29fdfe8cbca71c4", "sha256_in_prefix": "2c862f523c015715ac5dabc2e74c0b9e48a9a7ee7f3b0a25c29fdfe8cbca71c4", "size_in_bytes": 504}, {"_path": "lib/python3.11/site-packages/chromadb/utils/lru_cache.py", "path_type": "hardlink", "sha256": "eeccd8f05b20bb33430f9797534466263c660315c06026a0efb96fd2959a81d4", "sha256_in_prefix": "eeccd8f05b20bb33430f9797534466263c660315c06026a0efb96fd2959a81d4", "size_in_bytes": 1076}, {"_path": "lib/python3.11/site-packages/chromadb/utils/messageid.py", "path_type": "hardlink", "sha256": "eaeb651938f51e36d114a04df7bc9d5267a4631dff97ad95bf7ee28e428ff0e5", "sha256_in_prefix": "eaeb651938f51e36d114a04df7bc9d5267a4631dff97ad95bf7ee28e428ff0e5", "size_in_bytes": 272}, {"_path": "lib/python3.11/site-packages/chromadb/utils/read_write_lock.py", "path_type": "hardlink", "sha256": "03167b19d602f60d3964b9b32ca4f0d39d7f11bbc443c17d08862938d7180db2", "sha256_in_prefix": "03167b19d602f60d3964b9b32ca4f0d39d7f11bbc443c17d08862938d7180db2", "size_in_bytes": 2010}, {"_path": "lib/python3.11/site-packages/chromadb/utils/rendezvous_hash.py", "path_type": "hardlink", "sha256": "eab8694d578106367d4707de747daa9a6ded7036e41184ffa30c5deb801b07e4", "sha256_in_prefix": "eab8694d578106367d4707de747daa9a6ded7036e41184ffa30c5deb801b07e4", "size_in_bytes": 2155}, {"_path": "lib/python3.11/site-packages/chromadb/utils/results.py", "path_type": "hardlink", "sha256": "d173599117e521dd02fc49f976066015a94a3c209daf607f30dfbba685a7e6e6", "sha256_in_prefix": "d173599117e521dd02fc49f976066015a94a3c209daf607f30dfbba685a7e6e6", "size_in_bytes": 3869}, {"_path": "lib/python3.11/site-packages/chromadb_rust_bindings/__init__.py", "path_type": "hardlink", "sha256": "1101d89e23b96639de997164981f9f69ada00ad47c1c891edbdabafe3685b648", "sha256_in_prefix": "1101d89e23b96639de997164981f9f69ada00ad47c1c891edbdabafe3685b648", "size_in_bytes": 171}, {"_path": "lib/python3.11/site-packages/chromadb_rust_bindings/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "66fb096a2754522ec1ac45aac4958fc3b9ae929302c0e7a7109a183727a732c3", "sha256_in_prefix": "66fb096a2754522ec1ac45aac4958fc3b9ae929302c0e7a7109a183727a732c3", "size_in_bytes": 352}, {"_path": "lib/python3.11/site-packages/chromadb_rust_bindings/chromadb_rust_bindings.abi3.so", "path_type": "hardlink", "sha256": "2be8417018050d5c3ae35eb11a3c88f8c04804c023cfc2181cbc7fbec3031f63", "sha256_in_prefix": "2be8417018050d5c3ae35eb11a3c88f8c04804c023cfc2181cbc7fbec3031f63", "size_in_bytes": 47668056}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/README.md", "path_type": "hardlink", "sha256": "803f97695464b35b40f0b9c15434bf9fafbcc39949ad76d704a7884ac0af5551", "sha256_in_prefix": "803f97695464b35b40f0b9c15434bf9fafbcc39949ad76d704a7884ac0af5551", "size_in_bytes": 1969}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/amazon_bedrock.json", "path_type": "hardlink", "sha256": "241a23f324f84e1885f690dd49929410a4058d96c77e5818baebff6528123cf8", "sha256_in_prefix": "241a23f324f84e1885f690dd49929410a4058d96c77e5818baebff6528123cf8", "size_in_bytes": 731}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/base_schema.json", "path_type": "hardlink", "sha256": "2ec701223879976953f38742506a88f6bc755fd81b8c9b1e255bba64d1cc5d6d", "sha256_in_prefix": "2ec701223879976953f38742506a88f6bc755fd81b8c9b1e255bba64d1cc5d6d", "size_in_bytes": 734}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/chroma_langchain.json", "path_type": "hardlink", "sha256": "daf452884c8c4198f5d6f29beb91d5da9cec6d1baf53bb4b493026d7489e0b8b", "sha256_in_prefix": "daf452884c8c4198f5d6f29beb91d5da9cec6d1baf53bb4b493026d7489e0b8b", "size_in_bytes": 478}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/cloudflare_workers_ai.json", "path_type": "hardlink", "sha256": "294ab220679d28a0f3b1ce6b924ca3fc36ab953821163939a2dcf141e41d30c2", "sha256_in_prefix": "294ab220679d28a0f3b1ce6b924ca3fc36ab953821163939a2dcf141e41d30c2", "size_in_bytes": 1063}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/cohere.json", "path_type": "hardlink", "sha256": "238c36677f646319a95b0d5aaf1eaa70cdabec7a728a935cccdc402362979180", "sha256_in_prefix": "238c36677f646319a95b0d5aaf1eaa70cdabec7a728a935cccdc402362979180", "size_in_bytes": 681}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/default.json", "path_type": "hardlink", "sha256": "2605b15ffd58750a7096806353bae96949a7baed118e461b91ac48fa36fb8746", "sha256_in_prefix": "2605b15ffd58750a7096806353bae96949a7baed118e461b91ac48fa36fb8746", "size_in_bytes": 296}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/google_generative_ai.json", "path_type": "hardlink", "sha256": "d631d9ecb0dc912b45d00666c51e6b7c8352856c7c104393d404ee83d3d6330a", "sha256_in_prefix": "d631d9ecb0dc912b45d00666c51e6b7c8352856c7c104393d404ee83d3d6330a", "size_in_bytes": 805}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/google_palm.json", "path_type": "hardlink", "sha256": "f0e69382245a652f1c1483a25e944cae084de22c0843ec78adc2b0c0f8b7a4ab", "sha256_in_prefix": "f0e69382245a652f1c1483a25e944cae084de22c0843ec78adc2b0c0f8b7a4ab", "size_in_bytes": 628}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/google_vertex.json", "path_type": "hardlink", "sha256": "d198d0d0d953debbd063405ffe5f747329330cd2dcfe547a9f602c662955a1ea", "sha256_in_prefix": "d198d0d0d953debbd063405ffe5f747329330cd2dcfe547a9f602c662955a1ea", "size_in_bytes": 862}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/huggingface.json", "path_type": "hardlink", "sha256": "a27aa319b0c6ae4ae51e42e77acbc88e430c3b6509fd1d501eda16d9197802e5", "sha256_in_prefix": "a27aa319b0c6ae4ae51e42e77acbc88e430c3b6509fd1d501eda16d9197802e5", "size_in_bytes": 696}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/huggingface_server.json", "path_type": "hardlink", "sha256": "0d22c4f0968932ef25f2d6bb9e13ca7196f6714e6d4dfc5e7eae274f02d9ab64", "sha256_in_prefix": "0d22c4f0968932ef25f2d6bb9e13ca7196f6714e6d4dfc5e7eae274f02d9ab64", "size_in_bytes": 650}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/instructor.json", "path_type": "hardlink", "sha256": "d9744a7eaa36900cb290d98fb321f808f6a1199ea88b12910a26fee13edb792c", "sha256_in_prefix": "d9744a7eaa36900cb290d98fb321f808f6a1199ea88b12910a26fee13edb792c", "size_in_bytes": 731}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/jina.json", "path_type": "hardlink", "sha256": "92677527f5a7ca59f1a67a232d24a66f3b2f70cd61e0cf8b2c1e3e172062bc5c", "sha256_in_prefix": "92677527f5a7ca59f1a67a232d24a66f3b2f70cd61e0cf8b2c1e3e172062bc5c", "size_in_bytes": 1363}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/mistral.json", "path_type": "hardlink", "sha256": "1f2c9a9497f29706338f144e72e6f7f49422b3eb6e8ac81fc92d603c6e747c64", "sha256_in_prefix": "1f2c9a9497f29706338f144e72e6f7f49422b3eb6e8ac81fc92d603c6e747c64", "size_in_bytes": 595}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/morph.json", "path_type": "hardlink", "sha256": "b0067ccab6f8fa7c6a5aae3b12ff8102106ecf9070f5ac558f2f11db7c05352f", "sha256_in_prefix": "b0067ccab6f8fa7c6a5aae3b12ff8102106ecf9070f5ac558f2f11db7c05352f", "size_in_bytes": 1050}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/ollama.json", "path_type": "hardlink", "sha256": "f395788dadd5dfe31db4b4a733bbeb9d1425a53bb7a1b6b6200ca4c12143f55a", "sha256_in_prefix": "f395788dadd5dfe31db4b4a733bbeb9d1425a53bb7a1b6b6200ca4c12143f55a", "size_in_bytes": 652}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/onnx_mini_lm_l6_v2.json", "path_type": "hardlink", "sha256": "c6e0ff014832c6b98c26f78a2ef0d0d2d031c37efaee495bc2b18716132f7024", "sha256_in_prefix": "c6e0ff014832c6b98c26f78a2ef0d0d2d031c37efaee495bc2b18716132f7024", "size_in_bytes": 529}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/open_clip.json", "path_type": "hardlink", "sha256": "0ac085a19ca2bd11559c8e98b89de761973539e42f903273dfff064271c29e9d", "sha256_in_prefix": "0ac085a19ca2bd11559c8e98b89de761973539e42f903273dfff064271c29e9d", "size_in_bytes": 742}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/openai.json", "path_type": "hardlink", "sha256": "48df2b9093a4738f5ef9ec006c1c5793ed148e4043ffa9ecc75a82a71fb1f806", "sha256_in_prefix": "48df2b9093a4738f5ef9ec006c1c5793ed148e4043ffa9ecc75a82a71fb1f806", "size_in_bytes": 2005}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/roboflow.json", "path_type": "hardlink", "sha256": "8c6e716dd99dec7afe6fcb845bb5a70c569a518784d1781b738cfa1cc7ad505b", "sha256_in_prefix": "8c6e716dd99dec7afe6fcb845bb5a70c569a518784d1781b738cfa1cc7ad505b", "size_in_bytes": 606}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/sentence_transformer.json", "path_type": "hardlink", "sha256": "19022e4da4dc6ef0b0ca89dbd232b2e5bd3d46947a2e8f5bf0301fd655b64971", "sha256_in_prefix": "19022e4da4dc6ef0b0ca89dbd232b2e5bd3d46947a2e8f5bf0301fd655b64971", "size_in_bytes": 1244}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/text2vec.json", "path_type": "hardlink", "sha256": "d6a96415e1a2ad0798740e68418f58e9bd710cf4a189616a961269da85b503e4", "sha256_in_prefix": "d6a96415e1a2ad0798740e68418f58e9bd710cf4a189616a961269da85b503e4", "size_in_bytes": 451}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/together_ai.json", "path_type": "hardlink", "sha256": "b091dc1ff43c527bc6917b9eab78c312d3b63017bf2ab612ac77ea0e01b24574", "sha256_in_prefix": "b091dc1ff43c527bc6917b9eab78c312d3b63017bf2ab612ac77ea0e01b24574", "size_in_bytes": 700}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/transformers.json", "path_type": "hardlink", "sha256": "514c5ac55a0e630218864221bcf02f1147bd5bbb18a8d26f3069ba6d6efa3870", "sha256_in_prefix": "514c5ac55a0e630218864221bcf02f1147bd5bbb18a8d26f3069ba6d6efa3870", "size_in_bytes": 836}, {"_path": "lib/python3.11/site-packages/schemas/embedding_functions/voyageai.json", "path_type": "hardlink", "sha256": "2e6e9f6fe4b300576bad5ca0c14a00819c8e4bac78219041c0045ca595e63653", "sha256_in_prefix": "2e6e9f6fe4b300576bad5ca0c14a00819c8e4bac78219041c0045ca595e63653", "size_in_bytes": 878}], "paths_version": 1}, "requested_spec": "chromadb", "sha256": "cb257b3a67b693740d6c75423d1cb70d75714dfa5098d9b4d6b628bf4f85a005", "size": 12561424, "subdir": "osx-64", "timestamp": 1757678894000, "url": "https://conda.anaconda.org/conda-forge/osx-64/chromadb-1.0.20-py311h4cf515e_0.conda", "version": "1.0.20"}