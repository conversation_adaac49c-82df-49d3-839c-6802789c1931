{"build": "py311h13e5629_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13", "python >=3.11,<3.12.0a0", "python_abi 3.11.* *_cp311"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/unicodedata2-16.0.0-py311h13e5629_1", "files": ["lib/python3.11/site-packages/unicodedata2-16.0.0.dist-info/INSTALLER", "lib/python3.11/site-packages/unicodedata2-16.0.0.dist-info/METADATA", "lib/python3.11/site-packages/unicodedata2-16.0.0.dist-info/RECORD", "lib/python3.11/site-packages/unicodedata2-16.0.0.dist-info/REQUESTED", "lib/python3.11/site-packages/unicodedata2-16.0.0.dist-info/WHEEL", "lib/python3.11/site-packages/unicodedata2-16.0.0.dist-info/direct_url.json", "lib/python3.11/site-packages/unicodedata2-16.0.0.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/unicodedata2-16.0.0.dist-info/top_level.txt", "lib/python3.11/site-packages/unicodedata2.cpython-311-darwin.so"], "fn": "unicodedata2-16.0.0-py311h13e5629_1.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/unicodedata2-16.0.0-py311h13e5629_1", "type": 1}, "md5": "4199c0fe9c425eddb08f5741fcb772c5", "name": "unicodedata2", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/unicodedata2-16.0.0-py311h13e5629_1.conda", "paths_data": {"paths": [{"_path": "lib/python3.11/site-packages/unicodedata2-16.0.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.11/site-packages/unicodedata2-16.0.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "27271848761c8a6d377ea4bc71c4e349ec9e879d75c6416b6258db60487359d1", "sha256_in_prefix": "27271848761c8a6d377ea4bc71c4e349ec9e879d75c6416b6258db60487359d1", "size_in_bytes": 3260}, {"_path": "lib/python3.11/site-packages/unicodedata2-16.0.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "d58b3c9eff407747a41a20766b1a6dfa842f72e740b6b2ab11dc90596a0943cd", "sha256_in_prefix": "d58b3c9eff407747a41a20766b1a6dfa842f72e740b6b2ab11dc90596a0943cd", "size_in_bytes": 817}, {"_path": "lib/python3.11/site-packages/unicodedata2-16.0.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/unicodedata2-16.0.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "9a99a83f4370ced80864e3ca8f3c6d5e12203e9375332d371b67792389f5359e", "sha256_in_prefix": "9a99a83f4370ced80864e3ca8f3c6d5e12203e9375332d371b67792389f5359e", "size_in_bytes": 111}, {"_path": "lib/python3.11/site-packages/unicodedata2-16.0.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "a85ca32f5aa14f03a2b203a5501e6f4d2822bf81269db48f2dd96995e0d919bb", "sha256_in_prefix": "a85ca32f5aa14f03a2b203a5501e6f4d2822bf81269db48f2dd96995e0d919bb", "size_in_bytes": 100}, {"_path": "lib/python3.11/site-packages/unicodedata2-16.0.0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "cb5e8e7e5f4a3988e1063c142c60dc2df75605f4c46515e776e3aca6df976e14", "sha256_in_prefix": "cb5e8e7e5f4a3988e1063c142c60dc2df75605f4c46515e776e3aca6df976e14", "size_in_bytes": 11325}, {"_path": "lib/python3.11/site-packages/unicodedata2-16.0.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "2efe3a3c550aa72f5a669a41449f07023f185c6a2da7d9c8bff8b9c93cb523d7", "sha256_in_prefix": "2efe3a3c550aa72f5a669a41449f07023f185c6a2da7d9c8bff8b9c93cb523d7", "size_in_bytes": 13}, {"_path": "lib/python3.11/site-packages/unicodedata2.cpython-311-darwin.so", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/unicodedata2_1756494513990/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "0491037a63871211624056ba236bb43bc1ba59485727d14302fcaa4346f6444f", "sha256_in_prefix": "a6492dd7d3bac6ec68ceb210ba51085dffce1df2729aabc4e165c5cf979697e1", "size_in_bytes": 1448280}], "paths_version": 1}, "requested_spec": "None", "sha256": "cdd1276ff295078efb932f21305abda5392e8e43e7787050ea2d5ccbc04981ef", "size": 400393, "subdir": "osx-64", "timestamp": 1756494700000, "url": "https://conda.anaconda.org/conda-forge/osx-64/unicodedata2-16.0.0-py311h13e5629_1.conda", "version": "16.0.0"}