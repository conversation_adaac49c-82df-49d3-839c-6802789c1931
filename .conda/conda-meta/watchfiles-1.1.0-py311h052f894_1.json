{"build": "py311h052f894_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": ["__osx >=10.13"], "depends": ["__osx >=10.13", "anyio >=3.0.0", "python >=3.11,<3.12.0a0", "python_abi 3.11.* *_cp311"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/watchfiles-1.1.0-py311h052f894_1", "files": ["bin/watchfiles", "lib/python3.11/site-packages/watchfiles-1.1.0.dist-info/INSTALLER", "lib/python3.11/site-packages/watchfiles-1.1.0.dist-info/METADATA", "lib/python3.11/site-packages/watchfiles-1.1.0.dist-info/RECORD", "lib/python3.11/site-packages/watchfiles-1.1.0.dist-info/REQUESTED", "lib/python3.11/site-packages/watchfiles-1.1.0.dist-info/WHEEL", "lib/python3.11/site-packages/watchfiles-1.1.0.dist-info/direct_url.json", "lib/python3.11/site-packages/watchfiles-1.1.0.dist-info/entry_points.txt", "lib/python3.11/site-packages/watchfiles-1.1.0.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/watchfiles/__init__.py", "lib/python3.11/site-packages/watchfiles/__main__.py", "lib/python3.11/site-packages/watchfiles/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/watchfiles/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/watchfiles/__pycache__/cli.cpython-311.pyc", "lib/python3.11/site-packages/watchfiles/__pycache__/filters.cpython-311.pyc", "lib/python3.11/site-packages/watchfiles/__pycache__/main.cpython-311.pyc", "lib/python3.11/site-packages/watchfiles/__pycache__/run.cpython-311.pyc", "lib/python3.11/site-packages/watchfiles/__pycache__/version.cpython-311.pyc", "lib/python3.11/site-packages/watchfiles/_rust_notify.cpython-311-darwin.so", "lib/python3.11/site-packages/watchfiles/_rust_notify.pyi", "lib/python3.11/site-packages/watchfiles/cli.py", "lib/python3.11/site-packages/watchfiles/filters.py", "lib/python3.11/site-packages/watchfiles/main.py", "lib/python3.11/site-packages/watchfiles/py.typed", "lib/python3.11/site-packages/watchfiles/run.py", "lib/python3.11/site-packages/watchfiles/version.py"], "fn": "watchfiles-1.1.0-py311h052f894_1.conda", "license": "MIT", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/watchfiles-1.1.0-py311h052f894_1", "type": 1}, "md5": "3ebeb79b9265fc2f2512126f422d9292", "name": "watchfiles", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/watchfiles-1.1.0-py311h052f894_1.conda", "paths_data": {"paths": [{"_path": "bin/watchfiles", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/watchfiles_1757403570528/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehol", "sha256": "c0e387cc742126d9c50f1f4b9816b6c14963d68b929e86486d625df606c8fbb2", "sha256_in_prefix": "b81ef6776a784dad5f81da0bd0890527af3f21b16b02fde092496833e0ac8b5c", "size_in_bytes": 465}, {"_path": "lib/python3.11/site-packages/watchfiles-1.1.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.11/site-packages/watchfiles-1.1.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "c7ba03eae6726297c040d7012c0bbf0d7100c1781293e8f97cc8c7bdd9fe9702", "sha256_in_prefix": "c7ba03eae6726297c040d7012c0bbf0d7100c1781293e8f97cc8c7bdd9fe9702", "size_in_bytes": 4874}, {"_path": "lib/python3.11/site-packages/watchfiles-1.1.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "4a1664bc1f81c8fe72dc921a5a916b0d5d6437394912a5ac532d96960171a0e1", "sha256_in_prefix": "4a1664bc1f81c8fe72dc921a5a916b0d5d6437394912a5ac532d96960171a0e1", "size_in_bytes": 1926}, {"_path": "lib/python3.11/site-packages/watchfiles-1.1.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/watchfiles-1.1.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "ea9685fc6eea3b4c17c158f72734ffd7a6c26d29524cd0a933fda0fcb7688832", "sha256_in_prefix": "ea9685fc6eea3b4c17c158f72734ffd7a6c26d29524cd0a933fda0fcb7688832", "size_in_bytes": 106}, {"_path": "lib/python3.11/site-packages/watchfiles-1.1.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "9ad2842271b67bf97d381c6664a8049f34fde579864cd28cd5c740d408e9ecaa", "sha256_in_prefix": "9ad2842271b67bf97d381c6664a8049f34fde579864cd28cd5c740d408e9ecaa", "size_in_bytes": 98}, {"_path": "lib/python3.11/site-packages/watchfiles-1.1.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "b350e96b677f28a072fa32911165bad19dc6a11677269084a3ff62603b7a84e4", "sha256_in_prefix": "b350e96b677f28a072fa32911165bad19dc6a11677269084a3ff62603b7a84e4", "size_in_bytes": 48}, {"_path": "lib/python3.11/site-packages/watchfiles-1.1.0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "36b6f98a7a42de39e14f1c6eb59831cdb94cc11b05eead31c81fb8f851d1750b", "sha256_in_prefix": "36b6f98a7a42de39e14f1c6eb59831cdb94cc11b05eead31c81fb8f851d1750b", "size_in_bytes": 1110}, {"_path": "lib/python3.11/site-packages/watchfiles/__init__.py", "path_type": "hardlink", "sha256": "21194cf4a39279d3331757ef2ebef21073d54be50511135386507eb66588f325", "sha256_in_prefix": "21194cf4a39279d3331757ef2ebef21073d54be50511135386507eb66588f325", "size_in_bytes": 364}, {"_path": "lib/python3.11/site-packages/watchfiles/__main__.py", "path_type": "hardlink", "sha256": "26012b6248ac92287c63aa11c28c002ed47eaf042100076a3985a342b69120f2", "sha256_in_prefix": "26012b6248ac92287c63aa11c28c002ed47eaf042100076a3985a342b69120f2", "size_in_bytes": 59}, {"_path": "lib/python3.11/site-packages/watchfiles/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "ad576da227a081ba298fbb8b18b313ecb0e421dd2f8f2f48ac69045081dbe0cd", "sha256_in_prefix": "ad576da227a081ba298fbb8b18b313ecb0e421dd2f8f2f48ac69045081dbe0cd", "size_in_bytes": 597}, {"_path": "lib/python3.11/site-packages/watchfiles/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "a9689d2cf1ddb3592525ca2702f351de41d0bc00920ad303a924b76a73f4495c", "sha256_in_prefix": "a9689d2cf1ddb3592525ca2702f351de41d0bc00920ad303a924b76a73f4495c", "size_in_bytes": 284}, {"_path": "lib/python3.11/site-packages/watchfiles/__pycache__/cli.cpython-311.pyc", "path_type": "hardlink", "sha256": "600e0e03f04c902d53100b8eb5affba26f94b25814d865e713a44f576dfac8db", "sha256_in_prefix": "600e0e03f04c902d53100b8eb5affba26f94b25814d865e713a44f576dfac8db", "size_in_bytes": 11284}, {"_path": "lib/python3.11/site-packages/watchfiles/__pycache__/filters.cpython-311.pyc", "path_type": "hardlink", "sha256": "67c345f72407057960cfbe2acf8a53407e0c9044f87c0b699b9bb4d0a3b921f0", "sha256_in_prefix": "67c345f72407057960cfbe2acf8a53407e0c9044f87c0b699b9bb4d0a3b921f0", "size_in_bytes": 8067}, {"_path": "lib/python3.11/site-packages/watchfiles/__pycache__/main.cpython-311.pyc", "path_type": "hardlink", "sha256": "f290599a0b9987b3deb67d0789036d037e4fbf0788724f572fec79d1c142909f", "sha256_in_prefix": "f290599a0b9987b3deb67d0789036d037e4fbf0788724f572fec79d1c142909f", "size_in_bytes": 19147}, {"_path": "lib/python3.11/site-packages/watchfiles/__pycache__/run.cpython-311.pyc", "path_type": "hardlink", "sha256": "bd61ff867e3c42ce9803e5747d76cdf622c0c1892e5d6d9da1bfac7fa456c81d", "sha256_in_prefix": "bd61ff867e3c42ce9803e5747d76cdf622c0c1892e5d6d9da1bfac7fa456c81d", "size_in_bytes": 21333}, {"_path": "lib/python3.11/site-packages/watchfiles/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "d9e12cb1dac08f830cc0158efd5c4f18e87a65eda5858d07b3e1dc6f368d81f1", "sha256_in_prefix": "d9e12cb1dac08f830cc0158efd5c4f18e87a65eda5858d07b3e1dc6f368d81f1", "size_in_bytes": 262}, {"_path": "lib/python3.11/site-packages/watchfiles/_rust_notify.cpython-311-darwin.so", "path_type": "hardlink", "sha256": "7098fea6830b75ac36b098d53477281052d91ed408bdec66b3e9a328958340ca", "sha256_in_prefix": "7098fea6830b75ac36b098d53477281052d91ed408bdec66b3e9a328958340ca", "size_in_bytes": 929848}, {"_path": "lib/python3.11/site-packages/watchfiles/_rust_notify.pyi", "path_type": "hardlink", "sha256": "ab915091780124414fb7d4427fb9b2e303f944cd45c12569a9fdb6d5cb3279b8", "sha256_in_prefix": "ab915091780124414fb7d4427fb9b2e303f944cd45c12569a9fdb6d5cb3279b8", "size_in_bytes": 4753}, {"_path": "lib/python3.11/site-packages/watchfiles/cli.py", "path_type": "hardlink", "sha256": "0c7308d0b7d3ee13ab59a8bf63844ffefbd3bd576d7036a2a22c572e2bf6a46e", "sha256_in_prefix": "0c7308d0b7d3ee13ab59a8bf63844ffefbd3bd576d7036a2a22c572e2bf6a46e", "size_in_bytes": 7707}, {"_path": "lib/python3.11/site-packages/watchfiles/filters.py", "path_type": "hardlink", "sha256": "534cd718e7a0f5d307913e75fb9e812a946b588bbde653ead070d1fd9078a031", "sha256_in_prefix": "534cd718e7a0f5d307913e75fb9e812a946b588bbde653ead070d1fd9078a031", "size_in_bytes": 5139}, {"_path": "lib/python3.11/site-packages/watchfiles/main.py", "path_type": "hardlink", "sha256": "fa96c9045040df85445ccb7c54671a3d3407023b0684f7fb3ecbb580ffc79ca9", "sha256_in_prefix": "fa96c9045040df85445ccb7c54671a3d3407023b0684f7fb3ecbb580ffc79ca9", "size_in_bytes": 15235}, {"_path": "lib/python3.11/site-packages/watchfiles/py.typed", "path_type": "hardlink", "sha256": "312e0d6b7b68f554c63f2ffcc0140cffa98d29a5f8a88a62e7ec3bfcb641fbc2", "sha256_in_prefix": "312e0d6b7b68f554c63f2ffcc0140cffa98d29a5f8a88a62e7ec3bfcb641fbc2", "size_in_bytes": 69}, {"_path": "lib/python3.11/site-packages/watchfiles/run.py", "path_type": "hardlink", "sha256": "4cb5dbdb2ff1631faddf1caccd54161e81b21bb4426f5d3b43436821c4969a34", "sha256_in_prefix": "4cb5dbdb2ff1631faddf1caccd54161e81b21bb4426f5d3b43436821c4969a34", "size_in_bytes": 15348}, {"_path": "lib/python3.11/site-packages/watchfiles/version.py", "path_type": "hardlink", "sha256": "3515949e4677d836a6b0d295db411eb5a8081932c330c527a83595185140d964", "sha256_in_prefix": "3515949e4677d836a6b0d295db411eb5a8081932c330c527a83595185140d964", "size_in_bytes": 85}], "paths_version": 1}, "requested_spec": "None", "sha256": "329cb8005a113a94204c8317009fac059a936f83c4575faaf9ad43efa939bfa4", "size": 382734, "subdir": "osx-64", "timestamp": 1757403892000, "url": "https://conda.anaconda.org/conda-forge/osx-64/watchfiles-1.1.0-py311h052f894_1.conda", "version": "1.1.0"}