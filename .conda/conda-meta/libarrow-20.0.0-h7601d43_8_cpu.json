{"build": "h7601d43_8_cpu", "build_number": 8, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": ["parquet-cpp <0.0a0", "apache-arrow-proc * cpu", "arrow-cpp <0.0a0"], "depends": ["__osx >=10.14", "aws-crt-cpp >=0.32.10,<0.32.11.0a0", "aws-sdk-cpp >=1.11.510,<1.11.511.0a0", "azure-core-cpp >=1.14.0,<1.14.1.0a0", "azure-identity-cpp >=1.10.0,<1.10.1.0a0", "azure-storage-blobs-cpp >=12.13.0,<12.13.1.0a0", "azure-storage-files-datalake-cpp >=12.12.0,<12.12.1.0a0", "bzip2 >=1.0.8,<2.0a0", "glog >=0.7.1,<0.8.0a0", "libabseil * cxx17*", "libabseil >=20250127.1,<20250128.0a0", "libbrotlidec >=1.1.0,<1.2.0a0", "libbrotlienc >=1.1.0,<1.2.0a0", "libcxx >=18", "libgoogle-cloud >=2.36.0,<2.37.0a0", "libgoogle-cloud-storage >=2.36.0,<2.37.0a0", "libopentelemetry-cpp >=1.21.0,<1.22.0a0", "libprotobuf >=5.29.3,<5.29.4.0a0", "libre2-11 >=2024.7.2", "libutf8proc >=2.10.0,<2.11.0a0", "libzlib >=1.3.1,<2.0a0", "lz4-c >=1.10.0,<1.11.0a0", "orc >=2.1.2,<2.1.3.0a0", "re2", "snappy >=1.2.1,<1.3.0a0", "zstd >=1.5.7,<1.6.0a0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libarrow-20.0.0-h7601d43_8_cpu", "files": ["etc/conda/activate.d/libarrow_activate.sh", "include/arrow/acero/accumulation_queue.h", "include/arrow/acero/aggregate_node.h", "include/arrow/acero/api.h", "include/arrow/acero/asof_join_node.h", "include/arrow/acero/backpressure_handler.h", "include/arrow/acero/benchmark_util.h", "include/arrow/acero/bloom_filter.h", "include/arrow/acero/exec_plan.h", "include/arrow/acero/hash_join.h", "include/arrow/acero/hash_join_dict.h", "include/arrow/acero/hash_join_node.h", "include/arrow/acero/map_node.h", "include/arrow/acero/options.h", "include/arrow/acero/order_by_impl.h", "include/arrow/acero/partition_util.h", "include/arrow/acero/pch.h", "include/arrow/acero/query_context.h", "include/arrow/acero/schema_util.h", "include/arrow/acero/task_util.h", "include/arrow/acero/test_nodes.h", "include/arrow/acero/time_series_util.h", "include/arrow/acero/tpch_node.h", "include/arrow/acero/type_fwd.h", "include/arrow/acero/util.h", "include/arrow/acero/visibility.h", "include/arrow/adapters/orc/adapter.h", "include/arrow/adapters/orc/options.h", "include/arrow/api.h", "include/arrow/array.h", "include/arrow/array/array_base.h", "include/arrow/array/array_binary.h", "include/arrow/array/array_decimal.h", "include/arrow/array/array_dict.h", "include/arrow/array/array_nested.h", "include/arrow/array/array_primitive.h", "include/arrow/array/array_run_end.h", "include/arrow/array/builder_adaptive.h", "include/arrow/array/builder_base.h", "include/arrow/array/builder_binary.h", "include/arrow/array/builder_decimal.h", "include/arrow/array/builder_dict.h", "include/arrow/array/builder_nested.h", "include/arrow/array/builder_primitive.h", "include/arrow/array/builder_run_end.h", "include/arrow/array/builder_time.h", "include/arrow/array/builder_union.h", "include/arrow/array/concatenate.h", "include/arrow/array/data.h", "include/arrow/array/diff.h", "include/arrow/array/statistics.h", "include/arrow/array/util.h", "include/arrow/array/validate.h", "include/arrow/buffer.h", "include/arrow/buffer_builder.h", "include/arrow/builder.h", "include/arrow/c/abi.h", "include/arrow/c/bridge.h", "include/arrow/c/dlpack.h", "include/arrow/c/dlpack_abi.h", "include/arrow/c/helpers.h", "include/arrow/chunk_resolver.h", "include/arrow/chunked_array.h", "include/arrow/compare.h", "include/arrow/compute/api.h", "include/arrow/compute/api_aggregate.h", "include/arrow/compute/api_scalar.h", "include/arrow/compute/api_vector.h", "include/arrow/compute/cast.h", "include/arrow/compute/exec.h", "include/arrow/compute/expression.h", "include/arrow/compute/function.h", "include/arrow/compute/function_options.h", "include/arrow/compute/kernel.h", "include/arrow/compute/ordering.h", "include/arrow/compute/registry.h", "include/arrow/compute/row/grouper.h", "include/arrow/compute/type_fwd.h", "include/arrow/compute/util.h", "include/arrow/config.h", "include/arrow/csv/api.h", "include/arrow/csv/chunker.h", "include/arrow/csv/column_builder.h", "include/arrow/csv/column_decoder.h", "include/arrow/csv/converter.h", "include/arrow/csv/invalid_row.h", "include/arrow/csv/options.h", "include/arrow/csv/parser.h", "include/arrow/csv/reader.h", "include/arrow/csv/test_common.h", "include/arrow/csv/type_fwd.h", "include/arrow/csv/writer.h", "include/arrow/dataset/api.h", "include/arrow/dataset/dataset.h", "include/arrow/dataset/dataset_writer.h", "include/arrow/dataset/discovery.h", "include/arrow/dataset/file_base.h", "include/arrow/dataset/file_csv.h", "include/arrow/dataset/file_ipc.h", "include/arrow/dataset/file_json.h", "include/arrow/dataset/file_orc.h", "include/arrow/dataset/file_parquet.h", "include/arrow/dataset/parquet_encryption_config.h", "include/arrow/dataset/partition.h", "include/arrow/dataset/pch.h", "include/arrow/dataset/plan.h", "include/arrow/dataset/projector.h", "include/arrow/dataset/scanner.h", "include/arrow/dataset/type_fwd.h", "include/arrow/dataset/visibility.h", "include/arrow/datum.h", "include/arrow/device.h", "include/arrow/device_allocation_type_set.h", "include/arrow/engine/api.h", "include/arrow/engine/pch.h", "include/arrow/engine/substrait/api.h", "include/arrow/engine/substrait/extension_set.h", "include/arrow/engine/substrait/extension_types.h", "include/arrow/engine/substrait/options.h", "include/arrow/engine/substrait/relation.h", "include/arrow/engine/substrait/serde.h", "include/arrow/engine/substrait/test_plan_builder.h", "include/arrow/engine/substrait/test_util.h", "include/arrow/engine/substrait/type_fwd.h", "include/arrow/engine/substrait/util.h", "include/arrow/engine/substrait/visibility.h", "include/arrow/extension/bool8.h", "include/arrow/extension/fixed_shape_tensor.h", "include/arrow/extension/json.h", "include/arrow/extension/opaque.h", "include/arrow/extension/uuid.h", "include/arrow/extension_type.h", "include/arrow/filesystem/api.h", "include/arrow/filesystem/azurefs.h", "include/arrow/filesystem/filesystem.h", "include/arrow/filesystem/filesystem_library.h", "include/arrow/filesystem/gcsfs.h", "include/arrow/filesystem/hdfs.h", "include/arrow/filesystem/localfs.h", "include/arrow/filesystem/mockfs.h", "include/arrow/filesystem/path_util.h", "include/arrow/filesystem/s3_test_util.h", "include/arrow/filesystem/s3fs.h", "include/arrow/filesystem/test_util.h", "include/arrow/filesystem/type_fwd.h", "include/arrow/flight/api.h", "include/arrow/flight/client.h", "include/arrow/flight/client_auth.h", "include/arrow/flight/client_cookie_middleware.h", "include/arrow/flight/client_middleware.h", "include/arrow/flight/client_tracing_middleware.h", "include/arrow/flight/middleware.h", "include/arrow/flight/otel_logging.h", "include/arrow/flight/pch.h", "include/arrow/flight/platform.h", "include/arrow/flight/server.h", "include/arrow/flight/server_auth.h", "include/arrow/flight/server_middleware.h", "include/arrow/flight/server_tracing_middleware.h", "include/arrow/flight/sql/api.h", "include/arrow/flight/sql/client.h", "include/arrow/flight/sql/column_metadata.h", "include/arrow/flight/sql/server.h", "include/arrow/flight/sql/server_session_middleware.h", "include/arrow/flight/sql/server_session_middleware_factory.h", "include/arrow/flight/sql/types.h", "include/arrow/flight/sql/visibility.h", "include/arrow/flight/test_auth_handlers.h", "include/arrow/flight/test_definitions.h", "include/arrow/flight/test_flight_server.h", "include/arrow/flight/test_util.h", "include/arrow/flight/transport.h", "include/arrow/flight/transport_server.h", "include/arrow/flight/type_fwd.h", "include/arrow/flight/types.h", "include/arrow/flight/types_async.h", "include/arrow/flight/visibility.h", "include/arrow/integration/json_integration.h", "include/arrow/io/api.h", "include/arrow/io/buffered.h", "include/arrow/io/caching.h", "include/arrow/io/compressed.h", "include/arrow/io/concurrency.h", "include/arrow/io/file.h", "include/arrow/io/hdfs.h", "include/arrow/io/interfaces.h", "include/arrow/io/memory.h", "include/arrow/io/mman.h", "include/arrow/io/slow.h", "include/arrow/io/stdio.h", "include/arrow/io/test_common.h", "include/arrow/io/transform.h", "include/arrow/io/type_fwd.h", "include/arrow/ipc/api.h", "include/arrow/ipc/dictionary.h", "include/arrow/ipc/feather.h", "include/arrow/ipc/json_simple.h", "include/arrow/ipc/message.h", "include/arrow/ipc/options.h", "include/arrow/ipc/reader.h", "include/arrow/ipc/test_common.h", "include/arrow/ipc/type_fwd.h", "include/arrow/ipc/util.h", "include/arrow/ipc/writer.h", "include/arrow/json/api.h", "include/arrow/json/chunked_builder.h", "include/arrow/json/chunker.h", "include/arrow/json/converter.h", "include/arrow/json/object_parser.h", "include/arrow/json/object_writer.h", "include/arrow/json/options.h", "include/arrow/json/parser.h", "include/arrow/json/rapidjson_defs.h", "include/arrow/json/reader.h", "include/arrow/json/test_common.h", "include/arrow/json/type_fwd.h", "include/arrow/memory_pool.h", "include/arrow/memory_pool_test.h", "include/arrow/pch.h", "include/arrow/pretty_print.h", "include/arrow/record_batch.h", "include/arrow/result.h", "include/arrow/scalar.h", "include/arrow/sparse_tensor.h", "include/arrow/status.h", "include/arrow/stl.h", "include/arrow/stl_allocator.h", "include/arrow/stl_iterator.h", "include/arrow/table.h", "include/arrow/table_builder.h", "include/arrow/telemetry/logging.h", "include/arrow/tensor.h", "include/arrow/tensor/converter.h", "include/arrow/testing/async_test_util.h", "include/arrow/testing/builder.h", "include/arrow/testing/executor_util.h", "include/arrow/testing/extension_type.h", "include/arrow/testing/fixed_width_test_util.h", "include/arrow/testing/future_util.h", "include/arrow/testing/generator.h", "include/arrow/testing/gtest_compat.h", "include/arrow/testing/gtest_util.h", "include/arrow/testing/matchers.h", "include/arrow/testing/math.h", "include/arrow/testing/pch.h", "include/arrow/testing/process.h", "include/arrow/testing/random.h", "include/arrow/testing/uniform_real.h", "include/arrow/testing/util.h", "include/arrow/testing/visibility.h", "include/arrow/type.h", "include/arrow/type_fwd.h", "include/arrow/type_traits.h", "include/arrow/util/algorithm.h", "include/arrow/util/align_util.h", "include/arrow/util/aligned_storage.h", "include/arrow/util/async_generator.h", "include/arrow/util/async_generator_fwd.h", "include/arrow/util/async_util.h", "include/arrow/util/base64.h", "include/arrow/util/basic_decimal.h", "include/arrow/util/benchmark_util.h", "include/arrow/util/binary_view_util.h", "include/arrow/util/bit_block_counter.h", "include/arrow/util/bit_run_reader.h", "include/arrow/util/bit_util.h", "include/arrow/util/bitmap.h", "include/arrow/util/bitmap_builders.h", "include/arrow/util/bitmap_generate.h", "include/arrow/util/bitmap_ops.h", "include/arrow/util/bitmap_reader.h", "include/arrow/util/bitmap_visit.h", "include/arrow/util/bitmap_writer.h", "include/arrow/util/bitset_stack.h", "include/arrow/util/bpacking.h", "include/arrow/util/bpacking64_default.h", "include/arrow/util/bpacking_avx2.h", "include/arrow/util/bpacking_avx512.h", "include/arrow/util/bpacking_default.h", "include/arrow/util/bpacking_neon.h", "include/arrow/util/byte_size.h", "include/arrow/util/cancel.h", "include/arrow/util/checked_cast.h", "include/arrow/util/compare.h", "include/arrow/util/compression.h", "include/arrow/util/concurrent_map.h", "include/arrow/util/config.h", "include/arrow/util/converter.h", "include/arrow/util/counting_semaphore.h", "include/arrow/util/cpu_info.h", "include/arrow/util/crc32.h", "include/arrow/util/debug.h", "include/arrow/util/decimal.h", "include/arrow/util/delimiting.h", "include/arrow/util/dict_util.h", "include/arrow/util/dispatch.h", "include/arrow/util/double_conversion.h", "include/arrow/util/endian.h", "include/arrow/util/float16.h", "include/arrow/util/formatting.h", "include/arrow/util/functional.h", "include/arrow/util/future.h", "include/arrow/util/hash_util.h", "include/arrow/util/hashing.h", "include/arrow/util/int_util.h", "include/arrow/util/int_util_overflow.h", "include/arrow/util/io_util.h", "include/arrow/util/iterator.h", "include/arrow/util/key_value_metadata.h", "include/arrow/util/launder.h", "include/arrow/util/list_util.h", "include/arrow/util/logger.h", "include/arrow/util/logging.h", "include/arrow/util/macros.h", "include/arrow/util/map.h", "include/arrow/util/math_constants.h", "include/arrow/util/memory.h", "include/arrow/util/mutex.h", "include/arrow/util/parallel.h", "include/arrow/util/pcg_random.h", "include/arrow/util/prefetch.h", "include/arrow/util/print.h", "include/arrow/util/queue.h", "include/arrow/util/range.h", "include/arrow/util/ree_util.h", "include/arrow/util/regex.h", "include/arrow/util/rows_to_batches.h", "include/arrow/util/simd.h", "include/arrow/util/small_vector.h", "include/arrow/util/sort.h", "include/arrow/util/spaced.h", "include/arrow/util/span.h", "include/arrow/util/stopwatch.h", "include/arrow/util/string.h", "include/arrow/util/string_builder.h", "include/arrow/util/task_group.h", "include/arrow/util/tdigest.h", "include/arrow/util/test_common.h", "include/arrow/util/thread_pool.h", "include/arrow/util/time.h", "include/arrow/util/tracing.h", "include/arrow/util/trie.h", "include/arrow/util/type_fwd.h", "include/arrow/util/type_traits.h", "include/arrow/util/ubsan.h", "include/arrow/util/union_util.h", "include/arrow/util/unreachable.h", "include/arrow/util/uri.h", "include/arrow/util/utf8.h", "include/arrow/util/value_parsing.h", "include/arrow/util/vector.h", "include/arrow/util/visibility.h", "include/arrow/util/windows_compatibility.h", "include/arrow/util/windows_fixup.h", "include/arrow/vendored/ProducerConsumerQueue.h", "include/arrow/vendored/datetime.h", "include/arrow/vendored/datetime/date.h", "include/arrow/vendored/datetime/ios.h", "include/arrow/vendored/datetime/tz.h", "include/arrow/vendored/datetime/tz_private.h", "include/arrow/vendored/datetime/visibility.h", "include/arrow/vendored/double-conversion/bignum-dtoa.h", "include/arrow/vendored/double-conversion/bignum.h", "include/arrow/vendored/double-conversion/cached-powers.h", "include/arrow/vendored/double-conversion/diy-fp.h", "include/arrow/vendored/double-conversion/double-conversion.h", "include/arrow/vendored/double-conversion/double-to-string.h", "include/arrow/vendored/double-conversion/fast-dtoa.h", "include/arrow/vendored/double-conversion/fixed-dtoa.h", "include/arrow/vendored/double-conversion/ieee.h", "include/arrow/vendored/double-conversion/string-to-double.h", "include/arrow/vendored/double-conversion/strtod.h", "include/arrow/vendored/double-conversion/utils.h", "include/arrow/vendored/pcg/pcg_extras.hpp", "include/arrow/vendored/pcg/pcg_random.hpp", "include/arrow/vendored/pcg/pcg_uint128.hpp", "include/arrow/vendored/portable-snippets/debug-trap.h", "include/arrow/vendored/portable-snippets/safe-math.h", "include/arrow/vendored/strptime.h", "include/arrow/vendored/xxhash.h", "include/arrow/vendored/xxhash/xxhash.h", "include/arrow/visit_array_inline.h", "include/arrow/visit_data_inline.h", "include/arrow/visit_scalar_inline.h", "include/arrow/visit_type_inline.h", "include/arrow/visitor.h", "include/arrow/visitor_generate.h", "lib/cmake/Arrow/ArrowConfig.cmake", "lib/cmake/Arrow/ArrowConfigVersion.cmake", "lib/cmake/Arrow/ArrowOptions.cmake", "lib/cmake/Arrow/ArrowTargets-release.cmake", "lib/cmake/Arrow/ArrowTargets.cmake", "lib/cmake/Arrow/FindAWSSDKAlt.cmake", "lib/cmake/Arrow/FindAzure.cmake", "lib/cmake/Arrow/FindBrotliAlt.cmake", "lib/cmake/Arrow/FindOpenSSLAlt.cmake", "lib/cmake/Arrow/FindProtobufAlt.cmake", "lib/cmake/Arrow/FindSnappyAlt.cmake", "lib/cmake/Arrow/FindabslAlt.cmake", "lib/cmake/Arrow/FindglogAlt.cmake", "lib/cmake/Arrow/Findlz4Alt.cmake", "lib/cmake/Arrow/FindorcAlt.cmake", "lib/cmake/Arrow/Findre2Alt.cmake", "lib/cmake/Arrow/Findutf8proc.cmake", "lib/cmake/Arrow/FindzstdAlt.cmake", "lib/cmake/Arrow/arrow-config.cmake", "lib/libarrow.2000.0.0.dylib", "lib/libarrow.2000.dylib", "lib/libarrow.dylib", "lib/pkgconfig/arrow-compute.pc", "lib/pkgconfig/arrow-csv.pc", "lib/pkgconfig/arrow-filesystem.pc", "lib/pkgconfig/arrow-json.pc", "lib/pkgconfig/arrow-orc.pc", "lib/pkgconfig/arrow.pc", "share/arrow/gdb/gdb_arrow.py", "share/doc/arrow/LICENSE.txt", "share/doc/arrow/NOTICE.txt", "share/doc/arrow/README.md", "share/gdb/auto-load/replace_this_section_with_absolute_slashed_path_to_CONDA_PREFIX/lib/libarrow.2000.0.0.dylib-gdb.py"], "fn": "libarrow-20.0.0-h7601d43_8_cpu.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libarrow-20.0.0-h7601d43_8_cpu", "type": 1}, "md5": "7ededea979af3f519bdb7ebd7d970fb4", "name": "libarrow", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libarrow-20.0.0-h7601d43_8_cpu.conda", "paths_data": {"paths": [{"_path": "etc/conda/activate.d/libarrow_activate.sh", "path_type": "hardlink", "sha256": "bd5638943324f5cda6244737a027a49d08179ac6384b074bd1c15bbdee7c6ea8", "sha256_in_prefix": "bd5638943324f5cda6244737a027a49d08179ac6384b074bd1c15bbdee7c6ea8", "size_in_bytes": 3883}, {"_path": "include/arrow/acero/accumulation_queue.h", "path_type": "hardlink", "sha256": "7f3d28907eac66a712a41976c37bce53dc126bd425388017ce29266f798834f1", "sha256_in_prefix": "7f3d28907eac66a712a41976c37bce53dc126bd425388017ce29266f798834f1", "size_in_bytes": 5985}, {"_path": "include/arrow/acero/aggregate_node.h", "path_type": "hardlink", "sha256": "f5f866050194a61b2a2ddedb36688e25a120e4d271572580e511acf54693c51d", "sha256_in_prefix": "f5f866050194a61b2a2ddedb36688e25a120e4d271572580e511acf54693c51d", "size_in_bytes": 2201}, {"_path": "include/arrow/acero/api.h", "path_type": "hardlink", "sha256": "7d1b8a1076ca0d85910b04872dceef483fba9906afc8eb33b65b8247b7af1429", "sha256_in_prefix": "7d1b8a1076ca0d85910b04872dceef483fba9906afc8eb33b65b8247b7af1429", "size_in_bytes": 1151}, {"_path": "include/arrow/acero/asof_join_node.h", "path_type": "hardlink", "sha256": "2a8eabd700e3c60d35144f7ec4a92db71ed6cc20337f8dc6c5ba6f18780a669f", "sha256_in_prefix": "2a8eabd700e3c60d35144f7ec4a92db71ed6cc20337f8dc6c5ba6f18780a669f", "size_in_bytes": 1490}, {"_path": "include/arrow/acero/backpressure_handler.h", "path_type": "hardlink", "sha256": "0ac49645e9ebb5b65888d9dff9c7580a030b9aee4a5003d428d28c0d6b6da03e", "sha256_in_prefix": "0ac49645e9ebb5b65888d9dff9c7580a030b9aee4a5003d428d28c0d6b6da03e", "size_in_bytes": 2810}, {"_path": "include/arrow/acero/benchmark_util.h", "path_type": "hardlink", "sha256": "4f96cd69b1754c3029dbc4bb57fbedfd52039fa97905ed3339508785c4dc15ff", "sha256_in_prefix": "4f96cd69b1754c3029dbc4bb57fbedfd52039fa97905ed3339508785c4dc15ff", "size_in_bytes": 1943}, {"_path": "include/arrow/acero/bloom_filter.h", "path_type": "hardlink", "sha256": "6c5cf303342bb3d78fa76b423d022e93539af601bf372a7bd8cfc73347616a43", "sha256_in_prefix": "6c5cf303342bb3d78fa76b423d022e93539af601bf372a7bd8cfc73347616a43", "size_in_bytes": 11978}, {"_path": "include/arrow/acero/exec_plan.h", "path_type": "hardlink", "sha256": "534280ded9cdbd56fbe46d174052d56c6cd708675d746c91856df331af285897", "sha256_in_prefix": "534280ded9cdbd56fbe46d174052d56c6cd708675d746c91856df331af285897", "size_in_bytes": 35909}, {"_path": "include/arrow/acero/hash_join.h", "path_type": "hardlink", "sha256": "ce39635231f6010948e78573dbfee1bc92008d381a4f404f0bf2d50fbc4d7769", "sha256_in_prefix": "ce39635231f6010948e78573dbfee1bc92008d381a4f404f0bf2d50fbc4d7769", "size_in_bytes": 3022}, {"_path": "include/arrow/acero/hash_join_dict.h", "path_type": "hardlink", "sha256": "fc128998add9fca749b981e1e0a402b93ff5ad7954a21aed12018bb442787e04", "sha256_in_prefix": "fc128998add9fca749b981e1e0a402b93ff5ad7954a21aed12018bb442787e04", "size_in_bytes": 15360}, {"_path": "include/arrow/acero/hash_join_node.h", "path_type": "hardlink", "sha256": "1574fe69e5cbee7353b95ef97fda1781d1b2a8c2bfef61a7a86526f5c9819e4a", "sha256_in_prefix": "1574fe69e5cbee7353b95ef97fda1781d1b2a8c2bfef61a7a86526f5c9819e4a", "size_in_bytes": 4378}, {"_path": "include/arrow/acero/map_node.h", "path_type": "hardlink", "sha256": "05dd47716d0de5ace8215b61d804de1f181329df579a6124cf8d9804dc3978ad", "sha256_in_prefix": "05dd47716d0de5ace8215b61d804de1f181329df579a6124cf8d9804dc3978ad", "size_in_bytes": 2628}, {"_path": "include/arrow/acero/options.h", "path_type": "hardlink", "sha256": "e8e5408b98b7877a43eb86362943ba938bc49e4c43bce4a1a0334458d428e1ef", "sha256_in_prefix": "e8e5408b98b7877a43eb86362943ba938bc49e4c43bce4a1a0334458d428e1ef", "size_in_bytes": 37394}, {"_path": "include/arrow/acero/order_by_impl.h", "path_type": "hardlink", "sha256": "750aa994ff806563d944a8a8f0b9938d85a5098cfd5665bebac52bb5a2e977fc", "sha256_in_prefix": "750aa994ff806563d944a8a8f0b9938d85a5098cfd5665bebac52bb5a2e977fc", "size_in_bytes": 1691}, {"_path": "include/arrow/acero/partition_util.h", "path_type": "hardlink", "sha256": "0d2fc99575139fe3acf899152c826fcbde05b2a3c7bb714f78fe346c6d912e81", "sha256_in_prefix": "0d2fc99575139fe3acf899152c826fcbde05b2a3c7bb714f78fe346c6d912e81", "size_in_bytes": 7436}, {"_path": "include/arrow/acero/pch.h", "path_type": "hardlink", "sha256": "f155d7235d2b507ce542202d871fb28c73100a918bddd800895686cd3b9b3cf1", "sha256_in_prefix": "f155d7235d2b507ce542202d871fb28c73100a918bddd800895686cd3b9b3cf1", "size_in_bytes": 1094}, {"_path": "include/arrow/acero/query_context.h", "path_type": "hardlink", "sha256": "0f7eb8686452dee59ef25818a823518d5bece6c29eb4b38e5f3002769e4665e8", "sha256_in_prefix": "0f7eb8686452dee59ef25818a823518d5bece6c29eb4b38e5f3002769e4665e8", "size_in_bytes": 6212}, {"_path": "include/arrow/acero/schema_util.h", "path_type": "hardlink", "sha256": "280fe1576c72d9345c70cca4b12cd0add1fdfeb746a37b50c87388beb5966014", "sha256_in_prefix": "280fe1576c72d9345c70cca4b12cd0add1fdfeb746a37b50c87388beb5966014", "size_in_bytes": 7961}, {"_path": "include/arrow/acero/task_util.h", "path_type": "hardlink", "sha256": "ea9a882ee61f715c2df47a958517d7155268382f90fddb64f264394b18e0c1b6", "sha256_in_prefix": "ea9a882ee61f715c2df47a958517d7155268382f90fddb64f264394b18e0c1b6", "size_in_bytes": 3706}, {"_path": "include/arrow/acero/test_nodes.h", "path_type": "hardlink", "sha256": "c4a78b599642f22a24bde5573e3b1e3b530ebd631c6f2fb4c623084b2d2ac3c1", "sha256_in_prefix": "c4a78b599642f22a24bde5573e3b1e3b530ebd631c6f2fb4c623084b2d2ac3c1", "size_in_bytes": 2877}, {"_path": "include/arrow/acero/time_series_util.h", "path_type": "hardlink", "sha256": "5bdcb3a1a4c6901da3b589bcc360989274b0d448cc6ec7537e612eb8bdb332d9", "sha256_in_prefix": "5bdcb3a1a4c6901da3b589bcc360989274b0d448cc6ec7537e612eb8bdb332d9", "size_in_bytes": 1210}, {"_path": "include/arrow/acero/tpch_node.h", "path_type": "hardlink", "sha256": "977ce87311d37c69974e3cb0271c285c2224f6d8f351181675829281293c0c04", "sha256_in_prefix": "977ce87311d37c69974e3cb0271c285c2224f6d8f351181675829281293c0c04", "size_in_bytes": 2671}, {"_path": "include/arrow/acero/type_fwd.h", "path_type": "hardlink", "sha256": "e332e1b4b25fffb3125e0ae140865519e2f18d3ec9ac40c09fdc96ef90d31657", "sha256_in_prefix": "e332e1b4b25fffb3125e0ae140865519e2f18d3ec9ac40c09fdc96ef90d31657", "size_in_bytes": 1103}, {"_path": "include/arrow/acero/util.h", "path_type": "hardlink", "sha256": "6f284c123e57a00532fbddc08cbaf1fe9f7f89465d627e6e27f703902250b794", "sha256_in_prefix": "6f284c123e57a00532fbddc08cbaf1fe9f7f89465d627e6e27f703902250b794", "size_in_bytes": 6121}, {"_path": "include/arrow/acero/visibility.h", "path_type": "hardlink", "sha256": "13ee06d8ee05d9869b5e714d2589ec23655b568281b4eec05ea87f48fb898ba9", "sha256_in_prefix": "13ee06d8ee05d9869b5e714d2589ec23655b568281b4eec05ea87f48fb898ba9", "size_in_bytes": 1616}, {"_path": "include/arrow/adapters/orc/adapter.h", "path_type": "hardlink", "sha256": "1b949218660c4911082c2e3792a2f97eaa3de1ce2d2a0ba48ad3b5e66db5eed6", "sha256_in_prefix": "1b949218660c4911082c2e3792a2f97eaa3de1ce2d2a0ba48ad3b5e66db5eed6", "size_in_bytes": 11031}, {"_path": "include/arrow/adapters/orc/options.h", "path_type": "hardlink", "sha256": "14cc5d6b9612b2446b07a87d16f700b8cc65e6a75abd6acd6073e56a78ed938f", "sha256_in_prefix": "14cc5d6b9612b2446b07a87d16f700b8cc65e6a75abd6acd6073e56a78ed938f", "size_in_bytes": 3696}, {"_path": "include/arrow/api.h", "path_type": "hardlink", "sha256": "1ace8789105853937bf9aefd8634e5f5631275ae79d577542a95ad845636bf5b", "sha256_in_prefix": "1ace8789105853937bf9aefd8634e5f5631275ae79d577542a95ad845636bf5b", "size_in_bytes": 2491}, {"_path": "include/arrow/array.h", "path_type": "hardlink", "sha256": "3f9a16ea1bc3da3f7b6cb6924c4e3f507b95e98dfc0d3c09570c3711bdf17534", "sha256_in_prefix": "3f9a16ea1bc3da3f7b6cb6924c4e3f507b95e98dfc0d3c09570c3711bdf17534", "size_in_bytes": 1981}, {"_path": "include/arrow/array/array_base.h", "path_type": "hardlink", "sha256": "2b05650ea3e22beda42d8dc2f3fedcf9948684a233051e235906a4ae7f959acf", "sha256_in_prefix": "2b05650ea3e22beda42d8dc2f3fedcf9948684a233051e235906a4ae7f959acf", "size_in_bytes": 12147}, {"_path": "include/arrow/array/array_binary.h", "path_type": "hardlink", "sha256": "26fb41f03a11d3fb6a7d2152ffd9cc46b277f65b757139b9c9787e0cb921aa35", "sha256_in_prefix": "26fb41f03a11d3fb6a7d2152ffd9cc46b277f65b757139b9c9787e0cb921aa35", "size_in_bytes": 11247}, {"_path": "include/arrow/array/array_decimal.h", "path_type": "hardlink", "sha256": "c517eb6752053b4f449a41c4a250b0ac9e25b178cba390d775f1f9fefda04b2c", "sha256_in_prefix": "c517eb6752053b4f449a41c4a250b0ac9e25b178cba390d775f1f9fefda04b2c", "size_in_bytes": 3105}, {"_path": "include/arrow/array/array_dict.h", "path_type": "hardlink", "sha256": "e8031b4a7668323fa7850859846e11367c72f5554f9360ef66355b9702148606", "sha256_in_prefix": "e8031b4a7668323fa7850859846e11367c72f5554f9360ef66355b9702148606", "size_in_bytes": 7611}, {"_path": "include/arrow/array/array_nested.h", "path_type": "hardlink", "sha256": "c724a21796f569bf7b1a27ca331e85b986566f6ffa7b762f48c7ce39119edc9e", "sha256_in_prefix": "c724a21796f569bf7b1a27ca331e85b986566f6ffa7b762f48c7ce39119edc9e", "size_in_bytes": 37605}, {"_path": "include/arrow/array/array_primitive.h", "path_type": "hardlink", "sha256": "fd4f3fa531e0f9a1d8e636921631d9e21a913d77588b2293a6bdb2dce70971d8", "sha256_in_prefix": "fd4f3fa531e0f9a1d8e636921631d9e21a913d77588b2293a6bdb2dce70971d8", "size_in_bytes": 8184}, {"_path": "include/arrow/array/array_run_end.h", "path_type": "hardlink", "sha256": "e33b37b5c52b2200ce85210ecb0275bc663696c1fee50b84067f3b9b19c36e2f", "sha256_in_prefix": "e33b37b5c52b2200ce85210ecb0275bc663696c1fee50b84067f3b9b19c36e2f", "size_in_bytes": 5101}, {"_path": "include/arrow/array/builder_adaptive.h", "path_type": "hardlink", "sha256": "f760e98886435d223fc8eacc7ed8fb3fad339422e3366c1f18ce6e6dd6d758ce", "sha256_in_prefix": "f760e98886435d223fc8eacc7ed8fb3fad339422e3366c1f18ce6e6dd6d758ce", "size_in_bytes": 6861}, {"_path": "include/arrow/array/builder_base.h", "path_type": "hardlink", "sha256": "08ff644bca4315de17c8941d808941a77a53217f66343d4bbe1f39adee080bcc", "sha256_in_prefix": "08ff644bca4315de17c8941d808941a77a53217f66343d4bbe1f39adee080bcc", "size_in_bytes": 13723}, {"_path": "include/arrow/array/builder_binary.h", "path_type": "hardlink", "sha256": "d3506b4b090540d004cb8155622f04b1b77609a7b2c4dd381835285ec4141615", "sha256_in_prefix": "d3506b4b090540d004cb8155622f04b1b77609a7b2c4dd381835285ec4141615", "size_in_bytes": 32718}, {"_path": "include/arrow/array/builder_decimal.h", "path_type": "hardlink", "sha256": "0c5c72165a73591652f7374186c8c82397c550e318f5b5c79f7108ac8be638ca", "sha256_in_prefix": "0c5c72165a73591652f7374186c8c82397c550e318f5b5c79f7108ac8be638ca", "size_in_bytes": 5051}, {"_path": "include/arrow/array/builder_dict.h", "path_type": "hardlink", "sha256": "1598ef091203995bb09b3c7f1c27032ba66336864a084b1257e7c62342bdef86", "sha256_in_prefix": "1598ef091203995bb09b3c7f1c27032ba66336864a084b1257e7c62342bdef86", "size_in_bytes": 27899}, {"_path": "include/arrow/array/builder_nested.h", "path_type": "hardlink", "sha256": "d489ff33ca6391aa93baf35995b197586203df7096d9e061cb88a7d2866c6af0", "sha256_in_prefix": "d489ff33ca6391aa93baf35995b197586203df7096d9e061cb88a7d2866c6af0", "size_in_bytes": 31231}, {"_path": "include/arrow/array/builder_primitive.h", "path_type": "hardlink", "sha256": "d9c5de452e32634e02cc03c23686c5608ae4404ae52b2eafd35bfa55ea4736c9", "sha256_in_prefix": "d9c5de452e32634e02cc03c23686c5608ae4404ae52b2eafd35bfa55ea4736c9", "size_in_bytes": 20926}, {"_path": "include/arrow/array/builder_run_end.h", "path_type": "hardlink", "sha256": "49921db1428ad6a01cf697689cf19fd00fda8a4647731c7389c7b344647984bb", "sha256_in_prefix": "49921db1428ad6a01cf697689cf19fd00fda8a4647731c7389c7b344647984bb", "size_in_bytes": 11416}, {"_path": "include/arrow/array/builder_time.h", "path_type": "hardlink", "sha256": "f0cda27d99c382e8d222d5cab1ebf205db4ce88932dc89b2788740a995777de7", "sha256_in_prefix": "f0cda27d99c382e8d222d5cab1ebf205db4ce88932dc89b2788740a995777de7", "size_in_bytes": 2548}, {"_path": "include/arrow/array/builder_union.h", "path_type": "hardlink", "sha256": "f01179df6b0031cec9c5621b48dfb25fa67d7ea63d8e69ac21ad39e033ef6d61", "sha256_in_prefix": "f01179df6b0031cec9c5621b48dfb25fa67d7ea63d8e69ac21ad39e033ef6d61", "size_in_bytes": 10144}, {"_path": "include/arrow/array/concatenate.h", "path_type": "hardlink", "sha256": "c01cbe0814f3f4c7910a67277d71af9179ef491029bcf39c7c27fae00e36cacf", "sha256_in_prefix": "c01cbe0814f3f4c7910a67277d71af9179ef491029bcf39c7c27fae00e36cacf", "size_in_bytes": 2059}, {"_path": "include/arrow/array/data.h", "path_type": "hardlink", "sha256": "940919c9c877b9ed71cb9e366564863fa652fe207acfc5b8a81c3e233acf27a1", "sha256_in_prefix": "940919c9c877b9ed71cb9e366564863fa652fe207acfc5b8a81c3e233acf27a1", "size_in_bytes": 25160}, {"_path": "include/arrow/array/diff.h", "path_type": "hardlink", "sha256": "6d834acb6a0b031b6de956035af09fab66e725354d8c6e4a313b0697e813fe43", "sha256_in_prefix": "6d834acb6a0b031b6de956035af09fab66e725354d8c6e4a313b0697e813fe43", "size_in_bytes": 3344}, {"_path": "include/arrow/array/statistics.h", "path_type": "hardlink", "sha256": "ee2354766f6f39da0dda00e8afe014740c18700cd3ba1663b3e7cea419da4c84", "sha256_in_prefix": "ee2354766f6f39da0dda00e8afe014740c18700cd3ba1663b3e7cea419da4c84", "size_in_bytes": 5325}, {"_path": "include/arrow/array/util.h", "path_type": "hardlink", "sha256": "a951ef09a56500bcfc589c008f2330b019b927688df3d092823ecda669aa9642", "sha256_in_prefix": "a951ef09a56500bcfc589c008f2330b019b927688df3d092823ecda669aa9642", "size_in_bytes": 3652}, {"_path": "include/arrow/array/validate.h", "path_type": "hardlink", "sha256": "25d0dbdd7260e139807e9bffce0bb62137cbd87f67a35d13422b7e1cf8fd332c", "sha256_in_prefix": "25d0dbdd7260e139807e9bffce0bb62137cbd87f67a35d13422b7e1cf8fd332c", "size_in_bytes": 1710}, {"_path": "include/arrow/buffer.h", "path_type": "hardlink", "sha256": "fdb2ad5a4f128f439d74652311c66f6edf40249be0518a85f11ac8a7f3ed3016", "sha256_in_prefix": "fdb2ad5a4f128f439d74652311c66f6edf40249be0518a85f11ac8a7f3ed3016", "size_in_bytes": 23221}, {"_path": "include/arrow/buffer_builder.h", "path_type": "hardlink", "sha256": "b575882f01d6d0c2a9bdeecd214d8494494f634cb4a282126bcd83aba51d8555", "sha256_in_prefix": "b575882f01d6d0c2a9bdeecd214d8494494f634cb4a282126bcd83aba51d8555", "size_in_bytes": 17371}, {"_path": "include/arrow/builder.h", "path_type": "hardlink", "sha256": "981c4c928dbbd6527b5db92ed210a2c63f78dd8c7e7768b84391e6d967f08863", "sha256_in_prefix": "981c4c928dbbd6527b5db92ed210a2c63f78dd8c7e7768b84391e6d967f08863", "size_in_bytes": 1546}, {"_path": "include/arrow/c/abi.h", "path_type": "hardlink", "sha256": "1828f1ce60368cd590d19595910fa19db09b30728f8a43b5f89933623f7a7276", "sha256_in_prefix": "1828f1ce60368cd590d19595910fa19db09b30728f8a43b5f89933623f7a7276", "size_in_bytes": 20318}, {"_path": "include/arrow/c/bridge.h", "path_type": "hardlink", "sha256": "e9ee99a08b06fa913c150e5a5389d127bd6f54bdd4f64ebdee0ecf60a0ec573e", "sha256_in_prefix": "e9ee99a08b06fa913c150e5a5389d127bd6f54bdd4f64ebdee0ecf60a0ec573e", "size_in_bytes": 21789}, {"_path": "include/arrow/c/dlpack.h", "path_type": "hardlink", "sha256": "fc721af40291da6c1b85fd5a0a1229305fd70e916b3da7f9f0bb777d5c564567", "sha256_in_prefix": "fc721af40291da6c1b85fd5a0a1229305fd70e916b3da7f9f0bb777d5c564567", "size_in_bytes": 1817}, {"_path": "include/arrow/c/dlpack_abi.h", "path_type": "hardlink", "sha256": "9a3a7d596abcaafea09068ab4f8cb4a3704bfd923d5721d0a49e5a1293c57ad2", "sha256_in_prefix": "9a3a7d596abcaafea09068ab4f8cb4a3704bfd923d5721d0a49e5a1293c57ad2", "size_in_bytes": 9920}, {"_path": "include/arrow/c/helpers.h", "path_type": "hardlink", "sha256": "7f4439d7d3f0a258851e9c6c1e9f90bdb3fa7e954c3761dadf94e4f9104ae96b", "sha256_in_prefix": "7f4439d7d3f0a258851e9c6c1e9f90bdb3fa7e954c3761dadf94e4f9104ae96b", "size_in_bytes": 6279}, {"_path": "include/arrow/chunk_resolver.h", "path_type": "hardlink", "sha256": "a159bb0e96d64668c985ec63fe1e81b62b29081c4564f2072b5925f60a01186f", "sha256_in_prefix": "a159bb0e96d64668c985ec63fe1e81b62b29081c4564f2072b5925f60a01186f", "size_in_bytes": 12841}, {"_path": "include/arrow/chunked_array.h", "path_type": "hardlink", "sha256": "cfa2c0f4e077ba1b669fb6597b9c1f8b7026de271543e2ff7bcb3728432eab5f", "sha256_in_prefix": "cfa2c0f4e077ba1b669fb6597b9c1f8b7026de271543e2ff7bcb3728432eab5f", "size_in_bytes": 10647}, {"_path": "include/arrow/compare.h", "path_type": "hardlink", "sha256": "53972b6979d7002094cd0f079866328537a136b39ecdc5543f5001025c48eb61", "sha256_in_prefix": "53972b6979d7002094cd0f079866328537a136b39ecdc5543f5001025c48eb61", "size_in_bytes": 5555}, {"_path": "include/arrow/compute/api.h", "path_type": "hardlink", "sha256": "210297cffe980417c728e92eaa45c887d653658c9582aeda1014c8ccc9191222", "sha256_in_prefix": "210297cffe980417c728e92eaa45c887d653658c9582aeda1014c8ccc9191222", "size_in_bytes": 2071}, {"_path": "include/arrow/compute/api_aggregate.h", "path_type": "hardlink", "sha256": "0ddeb9f4edd352db54ab05742c7422be7e16b74e32e73227fb533c259082d2a4", "sha256_in_prefix": "0ddeb9f4edd352db54ab05742c7422be7e16b74e32e73227fb533c259082d2a4", "size_in_bytes": 21945}, {"_path": "include/arrow/compute/api_scalar.h", "path_type": "hardlink", "sha256": "fd2ea2e516cca86473e8c5ac6c1ae69f605405127ec2d09dcabf88de958f1567", "sha256_in_prefix": "fd2ea2e516cca86473e8c5ac6c1ae69f605405127ec2d09dcabf88de958f1567", "size_in_bytes": 69726}, {"_path": "include/arrow/compute/api_vector.h", "path_type": "hardlink", "sha256": "c67e51dbc1c730c4ffbd3e4b68398a63cb9e5150f531128fd0f07b1d2532f177", "sha256_in_prefix": "c67e51dbc1c730c4ffbd3e4b68398a63cb9e5150f531128fd0f07b1d2532f177", "size_in_bytes": 34508}, {"_path": "include/arrow/compute/cast.h", "path_type": "hardlink", "sha256": "5f0f63d3700800c53f8598aa93d776643e314e611291f5da0ecb999224f2a4bb", "sha256_in_prefix": "5f0f63d3700800c53f8598aa93d776643e314e611291f5da0ecb999224f2a4bb", "size_in_bytes": 4245}, {"_path": "include/arrow/compute/exec.h", "path_type": "hardlink", "sha256": "d9b697bead455b0c9f41f4d5e2beaa845f35f0b1183b1ca7fc3893a84e403029", "sha256_in_prefix": "d9b697bead455b0c9f41f4d5e2beaa845f35f0b1183b1ca7fc3893a84e403029", "size_in_bytes": 17975}, {"_path": "include/arrow/compute/expression.h", "path_type": "hardlink", "sha256": "9655fff35b9423227cbcf98ff3eda6022a69cb0e1c54d84219fa87458dfb14e3", "sha256_in_prefix": "9655fff35b9423227cbcf98ff3eda6022a69cb0e1c54d84219fa87458dfb14e3", "size_in_bytes": 11184}, {"_path": "include/arrow/compute/function.h", "path_type": "hardlink", "sha256": "92b4d768ba30bd3d5c2a179cb3bd2eacf41cc7be2f4022788ecc2d044e17b390", "sha256_in_prefix": "92b4d768ba30bd3d5c2a179cb3bd2eacf41cc7be2f4022788ecc2d044e17b390", "size_in_bytes": 16345}, {"_path": "include/arrow/compute/function_options.h", "path_type": "hardlink", "sha256": "43dae39173eb53df978bae3f7cc2cf6c16d6fdbca18c916cbc7a693f50ae99d0", "sha256_in_prefix": "43dae39173eb53df978bae3f7cc2cf6c16d6fdbca18c916cbc7a693f50ae99d0", "size_in_bytes": 3088}, {"_path": "include/arrow/compute/kernel.h", "path_type": "hardlink", "sha256": "cb0b3117cef0d9e238962f1b7bb5a2b9ae5b5e9d0d62131becb4bc2333c53b75", "sha256_in_prefix": "cb0b3117cef0d9e238962f1b7bb5a2b9ae5b5e9d0d62131becb4bc2333c53b75", "size_in_bytes": 31406}, {"_path": "include/arrow/compute/ordering.h", "path_type": "hardlink", "sha256": "f15c375730e2d661a05702864196a4cfd4d58f4038d30c5c2f5dc4beea8d5635", "sha256_in_prefix": "f15c375730e2d661a05702864196a4cfd4d58f4038d30c5c2f5dc4beea8d5635", "size_in_bytes": 4129}, {"_path": "include/arrow/compute/registry.h", "path_type": "hardlink", "sha256": "c7b2c789a34456f674554b2c645b1ab01e7667503145f964748e6d475861cea7", "sha256_in_prefix": "c7b2c789a34456f674554b2c645b1ab01e7667503145f964748e6d475861cea7", "size_in_bytes": 4837}, {"_path": "include/arrow/compute/row/grouper.h", "path_type": "hardlink", "sha256": "9ca58be1b61416106220afdfc248cb4019082739206c0f80eb9305c44b742180", "sha256_in_prefix": "9ca58be1b61416106220afdfc248cb4019082739206c0f80eb9305c44b742180", "size_in_bytes": 7458}, {"_path": "include/arrow/compute/type_fwd.h", "path_type": "hardlink", "sha256": "b621bb32b2c47d700f179dc92a59c5749ee4ce384d3fd6bfc426f6129b77a337", "sha256_in_prefix": "b621bb32b2c47d700f179dc92a59c5749ee4ce384d3fd6bfc426f6129b77a337", "size_in_bytes": 1555}, {"_path": "include/arrow/compute/util.h", "path_type": "hardlink", "sha256": "785fc15f669fb536b7a942706990374064da8eb0efe277fa1ca5ece9d3a68ee8", "sha256_in_prefix": "785fc15f669fb536b7a942706990374064da8eb0efe277fa1ca5ece9d3a68ee8", "size_in_bytes": 8863}, {"_path": "include/arrow/config.h", "path_type": "hardlink", "sha256": "f258b2288d0224ed06f85cf923ef908c80308749b8868b1fc803b0bd556cd2c5", "sha256_in_prefix": "f258b2288d0224ed06f85cf923ef908c80308749b8868b1fc803b0bd556cd2c5", "size_in_bytes": 3044}, {"_path": "include/arrow/csv/api.h", "path_type": "hardlink", "sha256": "2dbc1684fc88b22ffbde1bec4abefb44d47db94c6b5725ccfff869ed0712a26d", "sha256_in_prefix": "2dbc1684fc88b22ffbde1bec4abefb44d47db94c6b5725ccfff869ed0712a26d", "size_in_bytes": 907}, {"_path": "include/arrow/csv/chunker.h", "path_type": "hardlink", "sha256": "9d3b3c85dcb80f7373de86569b624cb80d369e863fd29591616abf469b6acc76", "sha256_in_prefix": "9d3b3c85dcb80f7373de86569b624cb80d369e863fd29591616abf469b6acc76", "size_in_bytes": 1171}, {"_path": "include/arrow/csv/column_builder.h", "path_type": "hardlink", "sha256": "ee86bd60283651cda607b1311c8c9822f6ddb79e79a8b5e2534cb815ea642125", "sha256_in_prefix": "ee86bd60283651cda607b1311c8c9822f6ddb79e79a8b5e2534cb815ea642125", "size_in_bytes": 2890}, {"_path": "include/arrow/csv/column_decoder.h", "path_type": "hardlink", "sha256": "d7489d70f244d95fd36ef823ccfaa6172d5d77fa92196beef5e0e47a74ee0b3d", "sha256_in_prefix": "d7489d70f244d95fd36ef823ccfaa6172d5d77fa92196beef5e0e47a74ee0b3d", "size_in_bytes": 2358}, {"_path": "include/arrow/csv/converter.h", "path_type": "hardlink", "sha256": "723b67cff8591719bf7568c0323af58aaaa4d5e81723661bf01774c42f267791", "sha256_in_prefix": "723b67cff8591719bf7568c0323af58aaaa4d5e81723661bf01774c42f267791", "size_in_bytes": 2789}, {"_path": "include/arrow/csv/invalid_row.h", "path_type": "hardlink", "sha256": "8131e311b8e4a5e7bab322c603c84563bb29c7544e3099ad31cc215efdb5c794", "sha256_in_prefix": "8131e311b8e4a5e7bab322c603c84563bb29c7544e3099ad31cc215efdb5c794", "size_in_bytes": 1889}, {"_path": "include/arrow/csv/options.h", "path_type": "hardlink", "sha256": "fc79234a88803d6efbcf90075559d36b8e76cb529f2609d7597cf63683cf018c", "sha256_in_prefix": "fc79234a88803d6efbcf90075559d36b8e76cb529f2609d7597cf63683cf018c", "size_in_bytes": 7980}, {"_path": "include/arrow/csv/parser.h", "path_type": "hardlink", "sha256": "f0fa65461dd0c5c924f153f233bd0ffdfd4c05be163069cd569a1e27d90e7475", "sha256_in_prefix": "f0fa65461dd0c5c924f153f233bd0ffdfd4c05be163069cd569a1e27d90e7475", "size_in_bytes": 8616}, {"_path": "include/arrow/csv/reader.h", "path_type": "hardlink", "sha256": "e35ea9b77c8d42c827e11848c9132c992266befd6cc37a2e428b6e6d71bdd604", "sha256_in_prefix": "e35ea9b77c8d42c827e11848c9132c992266befd6cc37a2e428b6e6d71bdd604", "size_in_bytes": 4606}, {"_path": "include/arrow/csv/test_common.h", "path_type": "hardlink", "sha256": "b84633c3c1113af77540c050f7c77831a67b06a325c367b47e5032cfe76ed19e", "sha256_in_prefix": "b84633c3c1113af77540c050f7c77831a67b06a325c367b47e5032cfe76ed19e", "size_in_bytes": 1972}, {"_path": "include/arrow/csv/type_fwd.h", "path_type": "hardlink", "sha256": "a6d55b7a782663f6bb633d70d122a628bd7acb2c3dc847b29b4434727442495e", "sha256_in_prefix": "a6d55b7a782663f6bb633d70d122a628bd7acb2c3dc847b29b4434727442495e", "size_in_bytes": 984}, {"_path": "include/arrow/csv/writer.h", "path_type": "hardlink", "sha256": "635cc4ad9e47d6bd90ce302d6b74d7a45add9766eda1aadd085f14482006b468", "sha256_in_prefix": "635cc4ad9e47d6bd90ce302d6b74d7a45add9766eda1aadd085f14482006b468", "size_in_bytes": 3549}, {"_path": "include/arrow/dataset/api.h", "path_type": "hardlink", "sha256": "a7b8be6e77092e199f0647e32564bbebce2f0fd2e47b59bab5bec742aed3a67e", "sha256_in_prefix": "a7b8be6e77092e199f0647e32564bbebce2f0fd2e47b59bab5bec742aed3a67e", "size_in_bytes": 1322}, {"_path": "include/arrow/dataset/dataset.h", "path_type": "hardlink", "sha256": "36c5dd48562b3b8069252115d9ee32251acdfa174bc4c25ea70032caa156b7ed", "sha256_in_prefix": "36c5dd48562b3b8069252115d9ee32251acdfa174bc4c25ea70032caa156b7ed", "size_in_bytes": 20327}, {"_path": "include/arrow/dataset/dataset_writer.h", "path_type": "hardlink", "sha256": "4d057be5bfd48a07c68c8a419cf93cb5e3a770ce56ae829f295d79a227014516", "sha256_in_prefix": "4d057be5bfd48a07c68c8a419cf93cb5e3a770ce56ae829f295d79a227014516", "size_in_bytes": 4589}, {"_path": "include/arrow/dataset/discovery.h", "path_type": "hardlink", "sha256": "c7bfb934103211e4161a559a9c90cb640a1692c2a233033dead943c7f33a9f97", "sha256_in_prefix": "c7bfb934103211e4161a559a9c90cb640a1692c2a233033dead943c7f33a9f97", "size_in_bytes": 11236}, {"_path": "include/arrow/dataset/file_base.h", "path_type": "hardlink", "sha256": "da87b9bfc432eaffd4b6125abe0f6b8d4fd6b90bf05dc25e9e05b0737b162ea9", "sha256_in_prefix": "da87b9bfc432eaffd4b6125abe0f6b8d4fd6b90bf05dc25e9e05b0737b162ea9", "size_in_bytes": 20203}, {"_path": "include/arrow/dataset/file_csv.h", "path_type": "hardlink", "sha256": "ecf96f416ff6149e5144df951f8f8e070e5c67a9e4774284d2c8f54d0bc265ea", "sha256_in_prefix": "ecf96f416ff6149e5144df951f8f8e070e5c67a9e4774284d2c8f54d0bc265ea", "size_in_bytes": 5016}, {"_path": "include/arrow/dataset/file_ipc.h", "path_type": "hardlink", "sha256": "ebe6edbd785f959b001fdd13df0324c33664b5ee93e22c737821149ffe6e616f", "sha256_in_prefix": "ebe6edbd785f959b001fdd13df0324c33664b5ee93e22c737821149ffe6e616f", "size_in_bytes": 4083}, {"_path": "include/arrow/dataset/file_json.h", "path_type": "hardlink", "sha256": "b0f8ce78c3ad6d965bbce8af9ce75be0cbd828796da539d8f1f38d92107d3d9b", "sha256_in_prefix": "b0f8ce78c3ad6d965bbce8af9ce75be0cbd828796da539d8f1f38d92107d3d9b", "size_in_bytes": 3523}, {"_path": "include/arrow/dataset/file_orc.h", "path_type": "hardlink", "sha256": "3fb9c00fd9da7159e00c48c7f0285046dd004260e0e19d70071dfad0b0cea128", "sha256_in_prefix": "3fb9c00fd9da7159e00c48c7f0285046dd004260e0e19d70071dfad0b0cea128", "size_in_bytes": 2452}, {"_path": "include/arrow/dataset/file_parquet.h", "path_type": "hardlink", "sha256": "7db8a18b7851daedcadaa64783a1daae3e1aa6c5b05d75ff08ed1b91563df018", "sha256_in_prefix": "7db8a18b7851daedcadaa64783a1daae3e1aa6c5b05d75ff08ed1b91563df018", "size_in_bytes": 16878}, {"_path": "include/arrow/dataset/parquet_encryption_config.h", "path_type": "hardlink", "sha256": "529a349393228d968c6916633e9e5783c4d1b75a7c670876736b5d8a68d57b50", "sha256_in_prefix": "529a349393228d968c6916633e9e5783c4d1b75a7c670876736b5d8a68d57b50", "size_in_bytes": 3425}, {"_path": "include/arrow/dataset/partition.h", "path_type": "hardlink", "sha256": "df0acd7a40fff9f3ced585bdd596be4f89ae09f41e017ed265121cb0237f7332", "sha256_in_prefix": "df0acd7a40fff9f3ced585bdd596be4f89ae09f41e017ed265121cb0237f7332", "size_in_bytes": 16815}, {"_path": "include/arrow/dataset/pch.h", "path_type": "hardlink", "sha256": "88013f3db56d2877e1ca0cfb3b1f59da7961b08adf6a07868b118a8e5ccd46f8", "sha256_in_prefix": "88013f3db56d2877e1ca0cfb3b1f59da7961b08adf6a07868b118a8e5ccd46f8", "size_in_bytes": 1194}, {"_path": "include/arrow/dataset/plan.h", "path_type": "hardlink", "sha256": "223b91f4adac583f39ff61e9555a09fb76140aafbe50f6e51d4e3a7b15f9a918", "sha256_in_prefix": "223b91f4adac583f39ff61e9555a09fb76140aafbe50f6e51d4e3a7b15f9a918", "size_in_bytes": 1181}, {"_path": "include/arrow/dataset/projector.h", "path_type": "hardlink", "sha256": "29f6628ead3d1edd19d9c247b2b8e0fac1374a26784ca6a29df95178afb7f5c8", "sha256_in_prefix": "29f6628ead3d1edd19d9c247b2b8e0fac1374a26784ca6a29df95178afb7f5c8", "size_in_bytes": 1135}, {"_path": "include/arrow/dataset/scanner.h", "path_type": "hardlink", "sha256": "aa8a89491d9eafbd3b02528b8a3fcf4ba1832a987cd987478b48aee549840354", "sha256_in_prefix": "aa8a89491d9eafbd3b02528b8a3fcf4ba1832a987cd987478b48aee549840354", "size_in_bytes": 25917}, {"_path": "include/arrow/dataset/type_fwd.h", "path_type": "hardlink", "sha256": "60e51247074d0255c9ee67852e896900543f99294e6ecd85f35cdc3b2d03a08e", "sha256_in_prefix": "60e51247074d0255c9ee67852e896900543f99294e6ecd85f35cdc3b2d03a08e", "size_in_bytes": 3170}, {"_path": "include/arrow/dataset/visibility.h", "path_type": "hardlink", "sha256": "72499ffec108d16068e16ec32201f542b3aaf36b24387b6892c97d077c98bf35", "sha256_in_prefix": "72499ffec108d16068e16ec32201f542b3aaf36b24387b6892c97d077c98bf35", "size_in_bytes": 1586}, {"_path": "include/arrow/datum.h", "path_type": "hardlink", "sha256": "5d8699fd446b02d56a1ccabeff662d5e4fc44de438c9958b5409ec8be93631a7", "sha256_in_prefix": "5d8699fd446b02d56a1ccabeff662d5e4fc44de438c9958b5409ec8be93631a7", "size_in_bytes": 11511}, {"_path": "include/arrow/device.h", "path_type": "hardlink", "sha256": "98bcfdf6d6fbe15763c57b4ab7a45909828943c4d8cfddee682149d59888b4cc", "sha256_in_prefix": "98bcfdf6d6fbe15763c57b4ab7a45909828943c4d8cfddee682149d59888b4cc", "size_in_bytes": 15344}, {"_path": "include/arrow/device_allocation_type_set.h", "path_type": "hardlink", "sha256": "ca7a19f97c8594e023874d4f53e475d6613f10ec6ec37c7373de2fe4e5dad2ee", "sha256_in_prefix": "ca7a19f97c8594e023874d4f53e475d6613f10ec6ec37c7373de2fe4e5dad2ee", "size_in_bytes": 3306}, {"_path": "include/arrow/engine/api.h", "path_type": "hardlink", "sha256": "3913343392907aeae3106f04a1ae48795fd980a0513e55b289ccafdce4569006", "sha256_in_prefix": "3913343392907aeae3106f04a1ae48795fd980a0513e55b289ccafdce4569006", "size_in_bytes": 886}, {"_path": "include/arrow/engine/pch.h", "path_type": "hardlink", "sha256": "f155d7235d2b507ce542202d871fb28c73100a918bddd800895686cd3b9b3cf1", "sha256_in_prefix": "f155d7235d2b507ce542202d871fb28c73100a918bddd800895686cd3b9b3cf1", "size_in_bytes": 1094}, {"_path": "include/arrow/engine/substrait/api.h", "path_type": "hardlink", "sha256": "5bd341d51026d19571ced457600f860fb1fc5cb40d5c5a184fb4dd1851e83531", "sha256_in_prefix": "5bd341d51026d19571ced457600f860fb1fc5cb40d5c5a184fb4dd1851e83531", "size_in_bytes": 1079}, {"_path": "include/arrow/engine/substrait/extension_set.h", "path_type": "hardlink", "sha256": "144e9c71ec9cb90bfb0827bf165e2dead20c4727e825f582954852bc781c9bdd", "sha256_in_prefix": "144e9c71ec9cb90bfb0827bf165e2dead20c4727e825f582954852bc781c9bdd", "size_in_bytes": 21552}, {"_path": "include/arrow/engine/substrait/extension_types.h", "path_type": "hardlink", "sha256": "c79648bb29cd87a585b77c118d6fbecd4b2e0b749e0cb935a9183dff186cc163", "sha256_in_prefix": "c79648bb29cd87a585b77c118d6fbecd4b2e0b749e0cb935a9183dff186cc163", "size_in_bytes": 3075}, {"_path": "include/arrow/engine/substrait/options.h", "path_type": "hardlink", "sha256": "76dbd4b72ff3a0399c17055f969a620f37a562478e8423bbe2e445ea2cd04b39", "sha256_in_prefix": "76dbd4b72ff3a0399c17055f969a620f37a562478e8423bbe2e445ea2cd04b39", "size_in_bytes": 5820}, {"_path": "include/arrow/engine/substrait/relation.h", "path_type": "hardlink", "sha256": "57754a1650dd13ad5ed4e4bc2db262c2f9b9c34baae5bcc12e12aa9a098a6b0b", "sha256_in_prefix": "57754a1650dd13ad5ed4e4bc2db262c2f9b9c34baae5bcc12e12aa9a098a6b0b", "size_in_bytes": 2385}, {"_path": "include/arrow/engine/substrait/serde.h", "path_type": "hardlink", "sha256": "9a3c5fb85a3868f8428b079fa4a0093089649c5e143874abea0594fffd52c027", "sha256_in_prefix": "9a3c5fb85a3868f8428b079fa4a0093089649c5e143874abea0594fffd52c027", "size_in_bytes": 16528}, {"_path": "include/arrow/engine/substrait/test_plan_builder.h", "path_type": "hardlink", "sha256": "44415aefd0f500e2088e9d887b3ef78b0e60127cc6f5169cf6df16c2218bb2e2", "sha256_in_prefix": "44415aefd0f500e2088e9d887b3ef78b0e60127cc6f5169cf6df16c2218bb2e2", "size_in_bytes": 3003}, {"_path": "include/arrow/engine/substrait/test_util.h", "path_type": "hardlink", "sha256": "20765e62b939d12c7d6a725f0b6e4358fe977ac22d284cb00d652abd425c8c44", "sha256_in_prefix": "20765e62b939d12c7d6a725f0b6e4358fe977ac22d284cb00d652abd425c8c44", "size_in_bytes": 1517}, {"_path": "include/arrow/engine/substrait/type_fwd.h", "path_type": "hardlink", "sha256": "3fd6118c04294a0a088c30b4b22632c6843370f568debf72f39aa388cb6e741b", "sha256_in_prefix": "3fd6118c04294a0a088c30b4b22632c6843370f568debf72f39aa388cb6e741b", "size_in_bytes": 1028}, {"_path": "include/arrow/engine/substrait/util.h", "path_type": "hardlink", "sha256": "fdd46240168831635bb181bb92e5e1b3774c938748eb7fa9334b92c583cebe01", "sha256_in_prefix": "fdd46240168831635bb181bb92e5e1b3774c938748eb7fa9334b92c583cebe01", "size_in_bytes": 3570}, {"_path": "include/arrow/engine/substrait/visibility.h", "path_type": "hardlink", "sha256": "191cc7e94f9408f4fc77ad1ccb03a415f71a9cf4a08992820cfe97dab2296ccb", "sha256_in_prefix": "191cc7e94f9408f4fc77ad1ccb03a415f71a9cf4a08992820cfe97dab2296ccb", "size_in_bytes": 1740}, {"_path": "include/arrow/extension/bool8.h", "path_type": "hardlink", "sha256": "56c1d3b55cabaa4e942a0be27da77b2e8bae89ea0066e66cfee56f7a0746ab84", "sha256_in_prefix": "56c1d3b55cabaa4e942a0be27da77b2e8bae89ea0066e66cfee56f7a0746ab84", "size_in_bytes": 2145}, {"_path": "include/arrow/extension/fixed_shape_tensor.h", "path_type": "hardlink", "sha256": "54eaaf4d29f00c8be785bb2d617e679ea59f86d67b31a0fe95217cf4112a9611", "sha256_in_prefix": "54eaaf4d29f00c8be785bb2d617e679ea59f86d67b31a0fe95217cf4112a9611", "size_in_bytes": 5610}, {"_path": "include/arrow/extension/json.h", "path_type": "hardlink", "sha256": "827252cc25678bfa092b128ca12341c2cb81835049ce4fe0a06204fee4d23096", "sha256_in_prefix": "827252cc25678bfa092b128ca12341c2cb81835049ce4fe0a06204fee4d23096", "size_in_bytes": 2109}, {"_path": "include/arrow/extension/opaque.h", "path_type": "hardlink", "sha256": "b8c56a49271ecbfd771e8e95f3abdf92ea01c999e2f6e7e1e4118a817e1b4d99", "sha256_in_prefix": "b8c56a49271ecbfd771e8e95f3abdf92ea01c999e2f6e7e1e4118a817e1b4d99", "size_in_bytes": 2920}, {"_path": "include/arrow/extension/uuid.h", "path_type": "hardlink", "sha256": "13f067a7928d292c55baf7614078d84fed0729af66cd53db480423b99f4ddcf7", "sha256_in_prefix": "13f067a7928d292c55baf7614078d84fed0729af66cd53db480423b99f4ddcf7", "size_in_bytes": 2278}, {"_path": "include/arrow/extension_type.h", "path_type": "hardlink", "sha256": "614bd4ccfcfed10886fa33a3b3ffd37f23c4a6c1d61a418ee4f3151b5bcc8e46", "sha256_in_prefix": "614bd4ccfcfed10886fa33a3b3ffd37f23c4a6c1d61a418ee4f3151b5bcc8e46", "size_in_bytes": 6639}, {"_path": "include/arrow/filesystem/api.h", "path_type": "hardlink", "sha256": "5e0cb618e66d055c038d36973c3c8f3e54bd070b7d8235979b923f41bc916c5a", "sha256_in_prefix": "5e0cb618e66d055c038d36973c3c8f3e54bd070b7d8235979b923f41bc916c5a", "size_in_bytes": 1383}, {"_path": "include/arrow/filesystem/azurefs.h", "path_type": "hardlink", "sha256": "33eee347c02ac8fbbcb935dbc3820d2dbf51b192ab3e66653ceed3eccb2443ff", "sha256_in_prefix": "33eee347c02ac8fbbcb935dbc3820d2dbf51b192ab3e66653ceed3eccb2443ff", "size_in_bytes": 15299}, {"_path": "include/arrow/filesystem/filesystem.h", "path_type": "hardlink", "sha256": "1fb3045f5db9f5a56b58cb20b165f6eadb8210f48917e888e75277b0ab1879cd", "sha256_in_prefix": "1fb3045f5db9f5a56b58cb20b165f6eadb8210f48917e888e75277b0ab1879cd", "size_in_bytes": 29585}, {"_path": "include/arrow/filesystem/filesystem_library.h", "path_type": "hardlink", "sha256": "6b16a87fe1bd1b1063cd785122de1acc1ec707c549e3d32d198b0bee948ed00d", "sha256_in_prefix": "6b16a87fe1bd1b1063cd785122de1acc1ec707c549e3d32d198b0bee948ed00d", "size_in_bytes": 1725}, {"_path": "include/arrow/filesystem/gcsfs.h", "path_type": "hardlink", "sha256": "e47487a80f5ad53db356764ed6b3592a75e32d5caad5a026e8346642683fbf9c", "sha256_in_prefix": "e47487a80f5ad53db356764ed6b3592a75e32d5caad5a026e8346642683fbf9c", "size_in_bytes": 10372}, {"_path": "include/arrow/filesystem/hdfs.h", "path_type": "hardlink", "sha256": "267f75a637e4e91331f8cb805ac10028b4f2290edb0cf340e6330457369f4a07", "sha256_in_prefix": "267f75a637e4e91331f8cb805ac10028b4f2290edb0cf340e6330457369f4a07", "size_in_bytes": 4133}, {"_path": "include/arrow/filesystem/localfs.h", "path_type": "hardlink", "sha256": "78884fae900185e433db5584f38e542e579393cddee04b499c44b88c02d6ea63", "sha256_in_prefix": "78884fae900185e433db5584f38e542e579393cddee04b499c44b88c02d6ea63", "size_in_bytes": 4972}, {"_path": "include/arrow/filesystem/mockfs.h", "path_type": "hardlink", "sha256": "92886eeecf6cf71b5def9b064c4d8afebb075bcf6cc033ad492485c418b131c7", "sha256_in_prefix": "92886eeecf6cf71b5def9b064c4d8afebb075bcf6cc033ad492485c418b131c7", "size_in_bytes": 4768}, {"_path": "include/arrow/filesystem/path_util.h", "path_type": "hardlink", "sha256": "86b0d51e4e05f4cee8180041e31db029f40c8d29520084b421a2d5bf68c7ae5e", "sha256_in_prefix": "86b0d51e4e05f4cee8180041e31db029f40c8d29520084b421a2d5bf68c7ae5e", "size_in_bytes": 5698}, {"_path": "include/arrow/filesystem/s3_test_util.h", "path_type": "hardlink", "sha256": "db607ad347ebf381aae2b1802dd89d20fd08cc3b3d1b768eaa42cde32fbac6b2", "sha256_in_prefix": "db607ad347ebf381aae2b1802dd89d20fd08cc3b3d1b768eaa42cde32fbac6b2", "size_in_bytes": 2962}, {"_path": "include/arrow/filesystem/s3fs.h", "path_type": "hardlink", "sha256": "57e4b1edd5b64c7b6d8ce7314cf0220fee42f95912020a75f069ce707bf5f3dd", "sha256_in_prefix": "57e4b1edd5b64c7b6d8ce7314cf0220fee42f95912020a75f069ce707bf5f3dd", "size_in_bytes": 17923}, {"_path": "include/arrow/filesystem/test_util.h", "path_type": "hardlink", "sha256": "9204f75a02af1b669c369897590a952f573a5217e6ce0d66f61ea8d6787774a8", "sha256_in_prefix": "9204f75a02af1b669c369897590a952f573a5217e6ce0d66f61ea8d6787774a8", "size_in_bytes": 11729}, {"_path": "include/arrow/filesystem/type_fwd.h", "path_type": "hardlink", "sha256": "cf3b43111e7959bb78ad59e477e45e7833be627ae97a67ed15e16d67b64689d6", "sha256_in_prefix": "cf3b43111e7959bb78ad59e477e45e7833be627ae97a67ed15e16d67b64689d6", "size_in_bytes": 1462}, {"_path": "include/arrow/flight/api.h", "path_type": "hardlink", "sha256": "628b4b4d09fe28297acb904883c72810567d9fb3ccb49d36972ecf7398265fb5", "sha256_in_prefix": "628b4b4d09fe28297acb904883c72810567d9fb3ccb49d36972ecf7398265fb5", "size_in_bytes": 1257}, {"_path": "include/arrow/flight/client.h", "path_type": "hardlink", "sha256": "36d16ab9639a69f05c99d201e1e9fdb9ae7148425a081902d591e1014fc683ad", "sha256_in_prefix": "36d16ab9639a69f05c99d201e1e9fdb9ae7148425a081902d591e1014fc683ad", "size_in_bytes": 17798}, {"_path": "include/arrow/flight/client_auth.h", "path_type": "hardlink", "sha256": "6b70e49bf8cf3aeab336c0c0e1e7a3b8c53008404c6af33cb92ef0f358a16d16", "sha256_in_prefix": "6b70e49bf8cf3aeab336c0c0e1e7a3b8c53008404c6af33cb92ef0f358a16d16", "size_in_bytes": 2216}, {"_path": "include/arrow/flight/client_cookie_middleware.h", "path_type": "hardlink", "sha256": "e739023f64b130542e4d7f0df4d1f13af7b927f79fdab14eebec58e137e7ca09", "sha256_in_prefix": "e739023f64b130542e4d7f0df4d1f13af7b927f79fdab14eebec58e137e7ca09", "size_in_bytes": 1204}, {"_path": "include/arrow/flight/client_middleware.h", "path_type": "hardlink", "sha256": "68067009a86e88184ff3988c3deef1356be274147d29e1c6b68d9802a262a08e", "sha256_in_prefix": "68067009a86e88184ff3988c3deef1356be274147d29e1c6b68d9802a262a08e", "size_in_bytes": 2948}, {"_path": "include/arrow/flight/client_tracing_middleware.h", "path_type": "hardlink", "sha256": "774b1399439fab933d14c96220a2be7e526447afbbafaf4d8d4d93a7185fa96a", "sha256_in_prefix": "774b1399439fab933d14c96220a2be7e526447afbbafaf4d8d4d93a7185fa96a", "size_in_bytes": 1217}, {"_path": "include/arrow/flight/middleware.h", "path_type": "hardlink", "sha256": "24f41df099c855cc234c7eb2381724e01591f9657de5fa6701d847c8462f7ca1", "sha256_in_prefix": "24f41df099c855cc234c7eb2381724e01591f9657de5fa6701d847c8462f7ca1", "size_in_bytes": 2254}, {"_path": "include/arrow/flight/otel_logging.h", "path_type": "hardlink", "sha256": "ae24bdb193360b7987e9531b1128b327a9469ae76a74985f74263dfdc249a8c0", "sha256_in_prefix": "ae24bdb193360b7987e9531b1128b327a9469ae76a74985f74263dfdc249a8c0", "size_in_bytes": 1139}, {"_path": "include/arrow/flight/pch.h", "path_type": "hardlink", "sha256": "0e9da7ad9dedfca3e39b4708332bbdd7705b0a8ac91b9ae66eda5fc83374f5ba", "sha256_in_prefix": "0e9da7ad9dedfca3e39b4708332bbdd7705b0a8ac91b9ae66eda5fc83374f5ba", "size_in_bytes": 1192}, {"_path": "include/arrow/flight/platform.h", "path_type": "hardlink", "sha256": "d597f355aa25940668b06c87ff526fcc403c88d474862f5c506cf97b22d3d737", "sha256_in_prefix": "d597f355aa25940668b06c87ff526fcc403c88d474862f5c506cf97b22d3d737", "size_in_bytes": 1209}, {"_path": "include/arrow/flight/server.h", "path_type": "hardlink", "sha256": "180715d3e4c7b81ba3f9b5dfc2a62b6753f66f06602904892dbbbc4e896d46f5", "sha256_in_prefix": "180715d3e4c7b81ba3f9b5dfc2a62b6753f66f06602904892dbbbc4e896d46f5", "size_in_bytes": 13185}, {"_path": "include/arrow/flight/server_auth.h", "path_type": "hardlink", "sha256": "c57928b7f7e69c4674c971c406689ecc0402c4ceef41e9b5568d55d2f130716e", "sha256_in_prefix": "c57928b7f7e69c4674c971c406689ecc0402c4ceef41e9b5568d55d2f130716e", "size_in_bytes": 4457}, {"_path": "include/arrow/flight/server_middleware.h", "path_type": "hardlink", "sha256": "9115e6d797359a33ddba84bb37ad66d131bc9c622137f89e96c8caf72165b20b", "sha256_in_prefix": "9115e6d797359a33ddba84bb37ad66d131bc9c622137f89e96c8caf72165b20b", "size_in_bytes": 3155}, {"_path": "include/arrow/flight/server_tracing_middleware.h", "path_type": "hardlink", "sha256": "cd1d05159606c0002a8735613d50e3c977d975af7398bb5ea9abb003976047fc", "sha256_in_prefix": "cd1d05159606c0002a8735613d50e3c977d975af7398bb5ea9abb003976047fc", "size_in_bytes": 2186}, {"_path": "include/arrow/flight/sql/api.h", "path_type": "hardlink", "sha256": "398bea6af5150f2c22b1ccc8da345fb2e152912d00b8033f675948694fe9e44b", "sha256_in_prefix": "398bea6af5150f2c22b1ccc8da345fb2e152912d00b8033f675948694fe9e44b", "size_in_bytes": 853}, {"_path": "include/arrow/flight/sql/client.h", "path_type": "hardlink", "sha256": "02adf80e0ff6cd67600d30cc249a74ff736bc142db721a2312eb4872dcb30067", "sha256_in_prefix": "02adf80e0ff6cd67600d30cc249a74ff736bc142db721a2312eb4872dcb30067", "size_in_bytes": 24476}, {"_path": "include/arrow/flight/sql/column_metadata.h", "path_type": "hardlink", "sha256": "7e7147b9329d2827596c83aad4b4e22113a0db5b0c7d9d2a83e4fe44d82bd13c", "sha256_in_prefix": "7e7147b9329d2827596c83aad4b4e22113a0db5b0c7d9d2a83e4fe44d82bd13c", "size_in_bytes": 7366}, {"_path": "include/arrow/flight/sql/server.h", "path_type": "hardlink", "sha256": "f7354d0a6ca7d10b111add196fad1de76659a65bd5a868dff4c0f9e9052c9343", "sha256_in_prefix": "f7354d0a6ca7d10b111add196fad1de76659a65bd5a868dff4c0f9e9052c9343", "size_in_bytes": 36835}, {"_path": "include/arrow/flight/sql/server_session_middleware.h", "path_type": "hardlink", "sha256": "0f552e03692aa12481d44cb05d72a1f8b7ac3fb96f222d5fed4bbb943c7b4ee8", "sha256_in_prefix": "0f552e03692aa12481d44cb05d72a1f8b7ac3fb96f222d5fed4bbb943c7b4ee8", "size_in_bytes": 3400}, {"_path": "include/arrow/flight/sql/server_session_middleware_factory.h", "path_type": "hardlink", "sha256": "ae402ab80d8bcc202c528dd4cfc3636b4a740fc6a5e69d110eeb96dd25d64ee7", "sha256_in_prefix": "ae402ab80d8bcc202c528dd4cfc3636b4a740fc6a5e69d110eeb96dd25d64ee7", "size_in_bytes": 2215}, {"_path": "include/arrow/flight/sql/types.h", "path_type": "hardlink", "sha256": "6eb90d748b500eaed626943b434224b83db2a7c9ecf22aa4d1a13a4951ca672b", "sha256_in_prefix": "6eb90d748b500eaed626943b434224b83db2a7c9ecf22aa4d1a13a4951ca672b", "size_in_bytes": 40848}, {"_path": "include/arrow/flight/sql/visibility.h", "path_type": "hardlink", "sha256": "a0e5c087670607bd2b0e4118328dad452d855ecd7d3f2c5f1a28a98ea228c017", "sha256_in_prefix": "a0e5c087670607bd2b0e4118328dad452d855ecd7d3f2c5f1a28a98ea228c017", "size_in_bytes": 1636}, {"_path": "include/arrow/flight/test_auth_handlers.h", "path_type": "hardlink", "sha256": "5e4bcc5ae72ff4642396ddadb6f631b21ca6e2452e6d474c87eb629a5408b752", "sha256_in_prefix": "5e4bcc5ae72ff4642396ddadb6f631b21ca6e2452e6d474c87eb629a5408b752", "size_in_bytes": 3315}, {"_path": "include/arrow/flight/test_definitions.h", "path_type": "hardlink", "sha256": "53f1e106735d3599a88eb698cc8081981bee79fd1b4d3e5e633157a7c8861737", "sha256_in_prefix": "53f1e106735d3599a88eb698cc8081981bee79fd1b4d3e5e633157a7c8861737", "size_in_bytes": 13110}, {"_path": "include/arrow/flight/test_flight_server.h", "path_type": "hardlink", "sha256": "49b46164fd14e082e76e0ee5610bc67979af3ccfc1e9b6a2d7615333f1c3e114", "sha256_in_prefix": "49b46164fd14e082e76e0ee5610bc67979af3ccfc1e9b6a2d7615333f1c3e114", "size_in_bytes": 3930}, {"_path": "include/arrow/flight/test_util.h", "path_type": "hardlink", "sha256": "1343a50cbc1c9277af29fe0bcf3a9d5378dfc54315fe6708271cb853fba9efb4", "sha256_in_prefix": "1343a50cbc1c9277af29fe0bcf3a9d5378dfc54315fe6708271cb853fba9efb4", "size_in_bytes": 6860}, {"_path": "include/arrow/flight/transport.h", "path_type": "hardlink", "sha256": "6435dcf9ff28d344c55844b0b06535332eeb47d39f3375fb3998c37065d3c080", "sha256_in_prefix": "6435dcf9ff28d344c55844b0b06535332eeb47d39f3375fb3998c37065d3c080", "size_in_bytes": 12181}, {"_path": "include/arrow/flight/transport_server.h", "path_type": "hardlink", "sha256": "8957579ab6f6a5e9a1e28e81c70bc1ece64057851ea16adb85ee1e3d9e4f8b8b", "sha256_in_prefix": "8957579ab6f6a5e9a1e28e81c70bc1ece64057851ea16adb85ee1e3d9e4f8b8b", "size_in_bytes": 5268}, {"_path": "include/arrow/flight/type_fwd.h", "path_type": "hardlink", "sha256": "b5014033740d28f77307856a5067445058cd3d85d564b029c069d2bacd86431f", "sha256_in_prefix": "b5014033740d28f77307856a5067445058cd3d85d564b029c069d2bacd86431f", "size_in_bytes": 1797}, {"_path": "include/arrow/flight/types.h", "path_type": "hardlink", "sha256": "1341f1a6d895af55016755dc2895598adccc4c20b2bccdac0863eba6aeb14012", "sha256_in_prefix": "1341f1a6d895af55016755dc2895598adccc4c20b2bccdac0863eba6aeb14012", "size_in_bytes": 46681}, {"_path": "include/arrow/flight/types_async.h", "path_type": "hardlink", "sha256": "de7210ab00983b822bdccb766c6ec1367b57c6e34761034da73f9897711a1534", "sha256_in_prefix": "de7210ab00983b822bdccb766c6ec1367b57c6e34761034da73f9897711a1534", "size_in_bytes": 2599}, {"_path": "include/arrow/flight/visibility.h", "path_type": "hardlink", "sha256": "37593be1cc3246f39a6056bfb428dd8148e249d3c1866cb6d14b951aed304e0d", "sha256_in_prefix": "37593be1cc3246f39a6056bfb428dd8148e249d3c1866cb6d14b951aed304e0d", "size_in_bytes": 1596}, {"_path": "include/arrow/integration/json_integration.h", "path_type": "hardlink", "sha256": "a3168f5268d41b4008eabdbf50cbc85f9bfbce5f6aaa2955700661c956b2db87", "sha256_in_prefix": "a3168f5268d41b4008eabdbf50cbc85f9bfbce5f6aaa2955700661c956b2db87", "size_in_bytes": 3828}, {"_path": "include/arrow/io/api.h", "path_type": "hardlink", "sha256": "3e7e236524ec2d6f0c0253325d4a2498976ea57e78bb5e7819823902f0f90720", "sha256_in_prefix": "3e7e236524ec2d6f0c0253325d4a2498976ea57e78bb5e7819823902f0f90720", "size_in_bytes": 996}, {"_path": "include/arrow/io/buffered.h", "path_type": "hardlink", "sha256": "3c768cc0231a5eedef023843a59302db1b7984aa2825755e31f7e1a03cd3c4d7", "sha256_in_prefix": "3c768cc0231a5eedef023843a59302db1b7984aa2825755e31f7e1a03cd3c4d7", "size_in_bytes": 5912}, {"_path": "include/arrow/io/caching.h", "path_type": "hardlink", "sha256": "0008e8c8ac10d3a9b65e2825152e8287f71d836a78fb0900ec66a4188fde5f51", "sha256_in_prefix": "0008e8c8ac10d3a9b65e2825152e8287f71d836a78fb0900ec66a4188fde5f51", "size_in_bytes": 6708}, {"_path": "include/arrow/io/compressed.h", "path_type": "hardlink", "sha256": "dc9c483a8d6af15863204adfc1533964b564c3040a5ddf854f9e75ee3e7c7ad0", "sha256_in_prefix": "dc9c483a8d6af15863204adfc1533964b564c3040a5ddf854f9e75ee3e7c7ad0", "size_in_bytes": 3774}, {"_path": "include/arrow/io/concurrency.h", "path_type": "hardlink", "sha256": "4a622bd0e58280c511de3f678156e3309856394ad4d798d07ff8f3dab530eebe", "sha256_in_prefix": "4a622bd0e58280c511de3f678156e3309856394ad4d798d07ff8f3dab530eebe", "size_in_bytes": 7934}, {"_path": "include/arrow/io/file.h", "path_type": "hardlink", "sha256": "f99124956d50d2c8f7a580902d0d5e6e2aca777b36188def50422ccc5afc9955", "sha256_in_prefix": "f99124956d50d2c8f7a580902d0d5e6e2aca777b36188def50422ccc5afc9955", "size_in_bytes": 7625}, {"_path": "include/arrow/io/hdfs.h", "path_type": "hardlink", "sha256": "dacddfe3d8200188120ac5f94a8aa7a26c2c5dddb8fc86615be552049725a938", "sha256_in_prefix": "dacddfe3d8200188120ac5f94a8aa7a26c2c5dddb8fc86615be552049725a938", "size_in_bytes": 8559}, {"_path": "include/arrow/io/interfaces.h", "path_type": "hardlink", "sha256": "4080474c95286c493072a9ca313fc610abb902bce978698afbcbfbcf8a861c84", "sha256_in_prefix": "4080474c95286c493072a9ca313fc610abb902bce978698afbcbfbcf8a861c84", "size_in_bytes": 13428}, {"_path": "include/arrow/io/memory.h", "path_type": "hardlink", "sha256": "498e77e4310510e217a23b6daec1a632d2b93c29701b1e6c1caf04fd39421824", "sha256_in_prefix": "498e77e4310510e217a23b6daec1a632d2b93c29701b1e6c1caf04fd39421824", "size_in_bytes": 6321}, {"_path": "include/arrow/io/mman.h", "path_type": "hardlink", "sha256": "aa82c100615cbe9613cbde866bb14d58324a4f7ba1c6914017785b5c86834a26", "sha256_in_prefix": "aa82c100615cbe9613cbde866bb14d58324a4f7ba1c6914017785b5c86834a26", "size_in_bytes": 4111}, {"_path": "include/arrow/io/slow.h", "path_type": "hardlink", "sha256": "f3e663409ab8f44409e1eb10eaa1c78e5282799360e0148d0fb8ab7be66d2d84", "sha256_in_prefix": "f3e663409ab8f44409e1eb10eaa1c78e5282799360e0148d0fb8ab7be66d2d84", "size_in_bytes": 3942}, {"_path": "include/arrow/io/stdio.h", "path_type": "hardlink", "sha256": "76a3131e825b9a25dcc8d6b67cdeb4b52590b31d063e98595422dd1a264db7c2", "sha256_in_prefix": "76a3131e825b9a25dcc8d6b67cdeb4b52590b31d063e98595422dd1a264db7c2", "size_in_bytes": 2095}, {"_path": "include/arrow/io/test_common.h", "path_type": "hardlink", "sha256": "485e7da1d6f4137aed1a3bb98c006c43365c522f609794f01ee2a0e1fd044396", "sha256_in_prefix": "485e7da1d6f4137aed1a3bb98c006c43365c522f609794f01ee2a0e1fd044396", "size_in_bytes": 2146}, {"_path": "include/arrow/io/transform.h", "path_type": "hardlink", "sha256": "5bd5d6a27c3af55ca6400690a6d7d6ee30feeabcbb542a5f3d796407b699cce1", "sha256_in_prefix": "5bd5d6a27c3af55ca6400690a6d7d6ee30feeabcbb542a5f3d796407b699cce1", "size_in_bytes": 1890}, {"_path": "include/arrow/io/type_fwd.h", "path_type": "hardlink", "sha256": "3e2ec416916f057b05375c4a3b26634d23fde71343b3a5b98716f91ae7285551", "sha256_in_prefix": "3e2ec416916f057b05375c4a3b26634d23fde71343b3a5b98716f91ae7285551", "size_in_bytes": 2315}, {"_path": "include/arrow/ipc/api.h", "path_type": "hardlink", "sha256": "a2591dbbcda64d2f219b00f9dc304924be90434601a6586cfacfa6e2e3889d24", "sha256_in_prefix": "a2591dbbcda64d2f219b00f9dc304924be90434601a6586cfacfa6e2e3889d24", "size_in_bytes": 1007}, {"_path": "include/arrow/ipc/dictionary.h", "path_type": "hardlink", "sha256": "5138d93c81bc98b64e93d216d909d1f51646af59e97b164ea75d377eff8eef41", "sha256_in_prefix": "5138d93c81bc98b64e93d216d909d1f51646af59e97b164ea75d377eff8eef41", "size_in_bytes": 6104}, {"_path": "include/arrow/ipc/feather.h", "path_type": "hardlink", "sha256": "b829f1c0eede507d7c909fa55b3f485b04a3e808df7a3aaa2dda227c9f94043a", "sha256_in_prefix": "b829f1c0eede507d7c909fa55b3f485b04a3e808df7a3aaa2dda227c9f94043a", "size_in_bytes": 4918}, {"_path": "include/arrow/ipc/json_simple.h", "path_type": "hardlink", "sha256": "223163c7a67b87f58b5edd696952096e850e4d1e4614185652109b9bf9bd94d9", "sha256_in_prefix": "223163c7a67b87f58b5edd696952096e850e4d1e4614185652109b9bf9bd94d9", "size_in_bytes": 2455}, {"_path": "include/arrow/ipc/message.h", "path_type": "hardlink", "sha256": "2ad3026c80b6278fb98b23c6e528a3aaefcc00bc62b8a5816198469f0d23c4e4", "sha256_in_prefix": "2ad3026c80b6278fb98b23c6e528a3aaefcc00bc62b8a5816198469f0d23c4e4", "size_in_bytes": 20011}, {"_path": "include/arrow/ipc/options.h", "path_type": "hardlink", "sha256": "5f605b09a434dd2d6ea9e7602d16ef2f27dbd4f1d9ed61910630cb2c26d03061", "sha256_in_prefix": "5f605b09a434dd2d6ea9e7602d16ef2f27dbd4f1d9ed61910630cb2c26d03061", "size_in_bytes": 6888}, {"_path": "include/arrow/ipc/reader.h", "path_type": "hardlink", "sha256": "36a76baaa00422d3b579c61420d44eefeaa12a5632f821d224a188da17579514", "sha256_in_prefix": "36a76baaa00422d3b579c61420d44eefeaa12a5632f821d224a188da17579514", "size_in_bytes": 24106}, {"_path": "include/arrow/ipc/test_common.h", "path_type": "hardlink", "sha256": "fe458e47ff982ad8a57022162ba238598a3c7dc46de9e04c7f110ce240ed636d", "sha256_in_prefix": "fe458e47ff982ad8a57022162ba238598a3c7dc46de9e04c7f110ce240ed636d", "size_in_bytes": 6351}, {"_path": "include/arrow/ipc/type_fwd.h", "path_type": "hardlink", "sha256": "4f2f044fb9cb238249793a833323f4a441fd4158fdc6cec1a49919ae7ae968f6", "sha256_in_prefix": "4f2f044fb9cb238249793a833323f4a441fd4158fdc6cec1a49919ae7ae968f6", "size_in_bytes": 1440}, {"_path": "include/arrow/ipc/util.h", "path_type": "hardlink", "sha256": "c1391f0bd605299940023cb396641565c5bdd283a3fc96ad8c3378ab3d08c478", "sha256_in_prefix": "c1391f0bd605299940023cb396641565c5bdd283a3fc96ad8c3378ab3d08c478", "size_in_bytes": 1414}, {"_path": "include/arrow/ipc/writer.h", "path_type": "hardlink", "sha256": "86e9bc13fa2b906fd7dfcbe0c9fc8a8466f2bdc2c9dc09171329320630172188", "sha256_in_prefix": "86e9bc13fa2b906fd7dfcbe0c9fc8a8466f2bdc2c9dc09171329320630172188", "size_in_bytes": 18870}, {"_path": "include/arrow/json/api.h", "path_type": "hardlink", "sha256": "5d15b57cfe37cd5ab0cb5c9a6da29cb4d2bd30366a9f19281c31f57f1e41dd8e", "sha256_in_prefix": "5d15b57cfe37cd5ab0cb5c9a6da29cb4d2bd30366a9f19281c31f57f1e41dd8e", "size_in_bytes": 879}, {"_path": "include/arrow/json/chunked_builder.h", "path_type": "hardlink", "sha256": "0c3b8cc2b2263040b0e8c85f9dc9f6c4c3a390570a515d4ed79f7bfdf485480b", "sha256_in_prefix": "0c3b8cc2b2263040b0e8c85f9dc9f6c4c3a390570a515d4ed79f7bfdf485480b", "size_in_bytes": 2365}, {"_path": "include/arrow/json/chunker.h", "path_type": "hardlink", "sha256": "76464e731b05d50dde939f0fec8a00f1fde5432050a45bc64857b29cd5763a57", "sha256_in_prefix": "76464e731b05d50dde939f0fec8a00f1fde5432050a45bc64857b29cd5763a57", "size_in_bytes": 1119}, {"_path": "include/arrow/json/converter.h", "path_type": "hardlink", "sha256": "de55ec3f70527692cf224140267616f6f3fc05b5f79de55847f5b4cc5282950d", "sha256_in_prefix": "de55ec3f70527692cf224140267616f6f3fc05b5f79de55847f5b4cc5282950d", "size_in_bytes": 3134}, {"_path": "include/arrow/json/object_parser.h", "path_type": "hardlink", "sha256": "63fe8e71ec9ad3a694c9ea3ed64d38edd9b6f8950c25adbfc3d8b267e8282114", "sha256_in_prefix": "63fe8e71ec9ad3a694c9ea3ed64d38edd9b6f8950c25adbfc3d8b267e8282114", "size_in_bytes": 1627}, {"_path": "include/arrow/json/object_writer.h", "path_type": "hardlink", "sha256": "52b22b8c2908cfb4399ff16957934d3c3f7a8071dd4e4bd325a7a4b86047c13a", "sha256_in_prefix": "52b22b8c2908cfb4399ff16957934d3c3f7a8071dd4e4bd325a7a4b86047c13a", "size_in_bytes": 1428}, {"_path": "include/arrow/json/options.h", "path_type": "hardlink", "sha256": "132a50803c0b6506eb3e7021e399d23dfa46198af1bcb81fa757801bf974a774", "sha256_in_prefix": "132a50803c0b6506eb3e7021e399d23dfa46198af1bcb81fa757801bf974a774", "size_in_bytes": 2227}, {"_path": "include/arrow/json/parser.h", "path_type": "hardlink", "sha256": "de82333b9914b364da91ceedfdde661fb6e9d6e21c73533ea9bb8798fa12237e", "sha256_in_prefix": "de82333b9914b364da91ceedfdde661fb6e9d6e21c73533ea9bb8798fa12237e", "size_in_bytes": 3383}, {"_path": "include/arrow/json/rapidjson_defs.h", "path_type": "hardlink", "sha256": "9412657ee61621e410f1ac0f7776e4e2325cf3579faff2b32b01bc2a5c3bb75b", "sha256_in_prefix": "9412657ee61621e410f1ac0f7776e4e2325cf3579faff2b32b01bc2a5c3bb75b", "size_in_bytes": 1474}, {"_path": "include/arrow/json/reader.h", "path_type": "hardlink", "sha256": "28d3bd742c9cd9166ced6c544845bb6e9098061fe1d42dd4e766076310673f43", "sha256_in_prefix": "28d3bd742c9cd9166ced6c544845bb6e9098061fe1d42dd4e766076310673f43", "size_in_bytes": 5212}, {"_path": "include/arrow/json/test_common.h", "path_type": "hardlink", "sha256": "622898fe3b30a69ecdbba216d58da5061a9648546835a344cb58c7779aa46074", "sha256_in_prefix": "622898fe3b30a69ecdbba216d58da5061a9648546835a344cb58c7779aa46074", "size_in_bytes": 10874}, {"_path": "include/arrow/json/type_fwd.h", "path_type": "hardlink", "sha256": "a3d6a2801e65a2c92724515e8b5936e690d56198240b67a5991317d42e9a4e3a", "sha256_in_prefix": "a3d6a2801e65a2c92724515e8b5936e690d56198240b67a5991317d42e9a4e3a", "size_in_bytes": 942}, {"_path": "include/arrow/memory_pool.h", "path_type": "hardlink", "sha256": "779b8c966469e02dd3aef2e3e3bfbde4baaaf3455fd45d38142e7341c463b7dd", "sha256_in_prefix": "779b8c966469e02dd3aef2e3e3bfbde4baaaf3455fd45d38142e7341c463b7dd", "size_in_bytes": 11392}, {"_path": "include/arrow/memory_pool_test.h", "path_type": "hardlink", "sha256": "aafedcb24ea16623b610b145fb5cae929a698c44c30d7d27b811416cf147b4c5", "sha256_in_prefix": "aafedcb24ea16623b610b145fb5cae929a698c44c30d7d27b811416cf147b4c5", "size_in_bytes": 3350}, {"_path": "include/arrow/pch.h", "path_type": "hardlink", "sha256": "31a47d6eacb67056436e3abc01e92af46875bf32d395664e487bbe1a158fd60f", "sha256_in_prefix": "31a47d6eacb67056436e3abc01e92af46875bf32d395664e487bbe1a158fd60f", "size_in_bytes": 1286}, {"_path": "include/arrow/pretty_print.h", "path_type": "hardlink", "sha256": "64396ba0f46bf7faf20a4ee1feb8c0f292fb04d81a250f479d16f63d953ade58", "sha256_in_prefix": "64396ba0f46bf7faf20a4ee1feb8c0f292fb04d81a250f479d16f63d953ade58", "size_in_bytes": 5529}, {"_path": "include/arrow/record_batch.h", "path_type": "hardlink", "sha256": "37bd727360761d1fae8b9bab1438eafd3f262dcf699ca93e3b2cc8a4c2925df0", "sha256_in_prefix": "37bd727360761d1fae8b9bab1438eafd3f262dcf699ca93e3b2cc8a4c2925df0", "size_in_bytes": 18533}, {"_path": "include/arrow/result.h", "path_type": "hardlink", "sha256": "f15dbf3bc8aa641c62d1be01f5f73da431f8f98a97beb7508b5d2bc88b18df23", "sha256_in_prefix": "f15dbf3bc8aa641c62d1be01f5f73da431f8f98a97beb7508b5d2bc88b18df23", "size_in_bytes": 18185}, {"_path": "include/arrow/scalar.h", "path_type": "hardlink", "sha256": "ed282e4af278c28863a95f8548a6e80b5a60685541d3d74d7e4333dfa26370cf", "sha256_in_prefix": "ed282e4af278c28863a95f8548a6e80b5a60685541d3d74d7e4333dfa26370cf", "size_in_bytes": 36543}, {"_path": "include/arrow/sparse_tensor.h", "path_type": "hardlink", "sha256": "75de9e4260a37c29a623bea182c0b7ed1faa3c9d7520c85a7d56b14a8d97245b", "sha256_in_prefix": "75de9e4260a37c29a623bea182c0b7ed1faa3c9d7520c85a7d56b14a8d97245b", "size_in_bytes": 25205}, {"_path": "include/arrow/status.h", "path_type": "hardlink", "sha256": "058d01fb316ef37733bdcd1bbc116427de9b5a36750f12452b59d8ae0060da44", "sha256_in_prefix": "058d01fb316ef37733bdcd1bbc116427de9b5a36750f12452b59d8ae0060da44", "size_in_bytes": 16401}, {"_path": "include/arrow/stl.h", "path_type": "hardlink", "sha256": "32d1165c6df1d5860d8bbc4f743386551aa99cb56f35016213f64902c3c52758", "sha256_in_prefix": "32d1165c6df1d5860d8bbc4f743386551aa99cb56f35016213f64902c3c52758", "size_in_bytes": 19343}, {"_path": "include/arrow/stl_allocator.h", "path_type": "hardlink", "sha256": "4c16ef8dbb90207f72f3c148d926aa00bee9388b77c19d7130ac17a9e28d8891", "sha256_in_prefix": "4c16ef8dbb90207f72f3c148d926aa00bee9388b77c19d7130ac17a9e28d8891", "size_in_bytes": 4956}, {"_path": "include/arrow/stl_iterator.h", "path_type": "hardlink", "sha256": "45e94d42b0031eea4a593b850420a4a95972b861d7537c81ea072c0e9429adaf", "sha256_in_prefix": "45e94d42b0031eea4a593b850420a4a95972b861d7537c81ea072c0e9429adaf", "size_in_bytes": 9953}, {"_path": "include/arrow/table.h", "path_type": "hardlink", "sha256": "5288b15c69394b5724577e6eb578db03e294050ad27aabdfae14a6936da4efad", "sha256_in_prefix": "5288b15c69394b5724577e6eb578db03e294050ad27aabdfae14a6936da4efad", "size_in_bytes": 14647}, {"_path": "include/arrow/table_builder.h", "path_type": "hardlink", "sha256": "2d170b08bda252b8fabc5e1ff408cfb30563aadaa5330ef3ffc54101f509782a", "sha256_in_prefix": "2d170b08bda252b8fabc5e1ff408cfb30563aadaa5330ef3ffc54101f509782a", "size_in_bytes": 3763}, {"_path": "include/arrow/telemetry/logging.h", "path_type": "hardlink", "sha256": "e1791aadcacf2e922a8ff11cbd8898c55fc060ce5d5b8f1397127055f918aa3e", "sha256_in_prefix": "e1791aadcacf2e922a8ff11cbd8898c55fc060ce5d5b8f1397127055f918aa3e", "size_in_bytes": 3400}, {"_path": "include/arrow/tensor.h", "path_type": "hardlink", "sha256": "9a03e42797f99e0974a8391e61ffae93e06d5fb1b2afed0352e5f5a81e9869d1", "sha256_in_prefix": "9a03e42797f99e0974a8391e61ffae93e06d5fb1b2afed0352e5f5a81e9869d1", "size_in_bytes": 9093}, {"_path": "include/arrow/tensor/converter.h", "path_type": "hardlink", "sha256": "459ab44ebcbf922674f39fddfc2be17b032c779ec89c66f64c27a2bde69ff8ea", "sha256_in_prefix": "459ab44ebcbf922674f39fddfc2be17b032c779ec89c66f64c27a2bde69ff8ea", "size_in_bytes": 2891}, {"_path": "include/arrow/testing/async_test_util.h", "path_type": "hardlink", "sha256": "22b1d67cf788ca1ae079318750f2edf762ddb287e6157ea48639e05ac66fddd6", "sha256_in_prefix": "22b1d67cf788ca1ae079318750f2edf762ddb287e6157ea48639e05ac66fddd6", "size_in_bytes": 2262}, {"_path": "include/arrow/testing/builder.h", "path_type": "hardlink", "sha256": "e31d1b58e79d695a26594d26edd17df62acebf77e547efe9f883287d30d9b70a", "sha256_in_prefix": "e31d1b58e79d695a26594d26edd17df62acebf77e547efe9f883287d30d9b70a", "size_in_bytes": 8556}, {"_path": "include/arrow/testing/executor_util.h", "path_type": "hardlink", "sha256": "dfcfeb17e57ff73175b6d24cb2990f888fb7e155354438e0d400c14bc9541479", "sha256_in_prefix": "dfcfeb17e57ff73175b6d24cb2990f888fb7e155354438e0d400c14bc9541479", "size_in_bytes": 1885}, {"_path": "include/arrow/testing/extension_type.h", "path_type": "hardlink", "sha256": "e65ff6f3e49da0ed2beabfa756a917b1f4911564cb4cf80e144a57cd98aa87a5", "sha256_in_prefix": "e65ff6f3e49da0ed2beabfa756a917b1f4911564cb4cf80e144a57cd98aa87a5", "size_in_bytes": 7430}, {"_path": "include/arrow/testing/fixed_width_test_util.h", "path_type": "hardlink", "sha256": "83ac81ed1933894ec71213499f13a1927da71391de5da157ded6c15f7abdfec1", "sha256_in_prefix": "83ac81ed1933894ec71213499f13a1927da71391de5da157ded6c15f7abdfec1", "size_in_bytes": 3091}, {"_path": "include/arrow/testing/future_util.h", "path_type": "hardlink", "sha256": "a88862e35ece18c5923144831e39064d877e8a165bab0f19488470274d6f6ca8", "sha256_in_prefix": "a88862e35ece18c5923144831e39064d877e8a165bab0f19488470274d6f6ca8", "size_in_bytes": 6246}, {"_path": "include/arrow/testing/generator.h", "path_type": "hardlink", "sha256": "5f7306754db9c08d668c9339b36b6518396f0c080b2fc7e89b4bf916ef1a2238", "sha256_in_prefix": "5f7306754db9c08d668c9339b36b6518396f0c080b2fc7e89b4bf916ef1a2238", "size_in_bytes": 13180}, {"_path": "include/arrow/testing/gtest_compat.h", "path_type": "hardlink", "sha256": "d0da87dfd9b2ee6d45329b2b4189f1431e1b744135d1209765acac37aca34050", "sha256_in_prefix": "d0da87dfd9b2ee6d45329b2b4189f1431e1b744135d1209765acac37aca34050", "size_in_bytes": 1311}, {"_path": "include/arrow/testing/gtest_util.h", "path_type": "hardlink", "sha256": "8e75466cce779e75cee37dda50d9991e5332890d5fb443484cb6eda9117a474f", "sha256_in_prefix": "8e75466cce779e75cee37dda50d9991e5332890d5fb443484cb6eda9117a474f", "size_in_bytes": 24496}, {"_path": "include/arrow/testing/matchers.h", "path_type": "hardlink", "sha256": "df2b3b508e98a4578cbc50a08e617f5569f5c3b1f386a6ebd9cfbf12e241a675", "sha256_in_prefix": "df2b3b508e98a4578cbc50a08e617f5569f5c3b1f386a6ebd9cfbf12e241a675", "size_in_bytes": 16852}, {"_path": "include/arrow/testing/math.h", "path_type": "hardlink", "sha256": "611a0d55956b785d63f06d46f2869a17378aed5198036436e63d7480537f8530", "sha256_in_prefix": "611a0d55956b785d63f06d46f2869a17378aed5198036436e63d7480537f8530", "size_in_bytes": 1210}, {"_path": "include/arrow/testing/pch.h", "path_type": "hardlink", "sha256": "c0a3cde2b66755c41b9a99f4d92c79b526bbf8c121a54475c3e609e9daedc913", "sha256_in_prefix": "c0a3cde2b66755c41b9a99f4d92c79b526bbf8c121a54475c3e609e9daedc913", "size_in_bytes": 1164}, {"_path": "include/arrow/testing/process.h", "path_type": "hardlink", "sha256": "0333d6dcb876478b139be454522e0e7776926daf73a0b712ff31c1c73b6fe332", "sha256_in_prefix": "0333d6dcb876478b139be454522e0e7776926daf73a0b712ff31c1c73b6fe332", "size_in_bytes": 1372}, {"_path": "include/arrow/testing/random.h", "path_type": "hardlink", "sha256": "50cc62a10391be864eb2875933a4feba3cdae56b982b002775bbe7388983b2a4", "sha256_in_prefix": "50cc62a10391be864eb2875933a4feba3cdae56b982b002775bbe7388983b2a4", "size_in_bytes": 37046}, {"_path": "include/arrow/testing/uniform_real.h", "path_type": "hardlink", "sha256": "f86ff627d72f7afa02b41e79bec0ac5ad2643141cb20cc8ec1d4fa1bc656e396", "sha256_in_prefix": "f86ff627d72f7afa02b41e79bec0ac5ad2643141cb20cc8ec1d4fa1bc656e396", "size_in_bytes": 2970}, {"_path": "include/arrow/testing/util.h", "path_type": "hardlink", "sha256": "c4408f49717c2db4bd1560cb222beb24c5e98cc5344dffaf67a60ca6da379984", "sha256_in_prefix": "c4408f49717c2db4bd1560cb222beb24c5e98cc5344dffaf67a60ca6da379984", "size_in_bytes": 5582}, {"_path": "include/arrow/testing/visibility.h", "path_type": "hardlink", "sha256": "fb08dcd3440887281725aeed9276c82faf39010d709f208fafe12d5739339aad", "sha256_in_prefix": "fb08dcd3440887281725aeed9276c82faf39010d709f208fafe12d5739339aad", "size_in_bytes": 1606}, {"_path": "include/arrow/type.h", "path_type": "hardlink", "sha256": "5ef38df1021a233b8c1a0ddce83399edc56c8c6110ffa913472fc5f3a1008106", "sha256_in_prefix": "5ef38df1021a233b8c1a0ddce83399edc56c8c6110ffa913472fc5f3a1008106", "size_in_bytes": 96939}, {"_path": "include/arrow/type_fwd.h", "path_type": "hardlink", "sha256": "5fe76b130302657879c6df378a57cdbd12bb7f99f2117667776493e0d5007dfb", "sha256_in_prefix": "5fe76b130302657879c6df378a57cdbd12bb7f99f2117667776493e0d5007dfb", "size_in_bytes": 23504}, {"_path": "include/arrow/type_traits.h", "path_type": "hardlink", "sha256": "46693c89479ee52bfd692805bc7e95ae57c3acac10d5bf43532da5c4a1129bb9", "sha256_in_prefix": "46693c89479ee52bfd692805bc7e95ae57c3acac10d5bf43532da5c4a1129bb9", "size_in_bytes": 55275}, {"_path": "include/arrow/util/algorithm.h", "path_type": "hardlink", "sha256": "d38e44573b02f6b4e1951545682a019a6b56666172e72dbc3d1f726a99fdb176", "sha256_in_prefix": "d38e44573b02f6b4e1951545682a019a6b56666172e72dbc3d1f726a99fdb176", "size_in_bytes": 1229}, {"_path": "include/arrow/util/align_util.h", "path_type": "hardlink", "sha256": "0c6d8bdb8291793894f271695cb8a06df9642a8b8a5933d47faa2c42cea6c626", "sha256_in_prefix": "0c6d8bdb8291793894f271695cb8a06df9642a8b8a5933d47faa2c42cea6c626", "size_in_bytes": 10669}, {"_path": "include/arrow/util/aligned_storage.h", "path_type": "hardlink", "sha256": "7b4a424c167d867f550154e710db9b6c48a2ff08ad17bdf151e05934841def72", "sha256_in_prefix": "7b4a424c167d867f550154e710db9b6c48a2ff08ad17bdf151e05934841def72", "size_in_bytes": 4254}, {"_path": "include/arrow/util/async_generator.h", "path_type": "hardlink", "sha256": "6af547dc93da0e4fefac487cba13cba2954ddcb4a7fb6f72b2ddb71a8a6d651f", "sha256_in_prefix": "6af547dc93da0e4fefac487cba13cba2954ddcb4a7fb6f72b2ddb71a8a6d651f", "size_in_bytes": 78200}, {"_path": "include/arrow/util/async_generator_fwd.h", "path_type": "hardlink", "sha256": "43cb2e798656c1dc646950da6ee8251ccdd7b8ffbe35ead882af14c6c5478056", "sha256_in_prefix": "43cb2e798656c1dc646950da6ee8251ccdd7b8ffbe35ead882af14c6c5478056", "size_in_bytes": 1728}, {"_path": "include/arrow/util/async_util.h", "path_type": "hardlink", "sha256": "d679c0259db688aef04b39af643a373cc86e5aa248b76a8a7655f34f286808ae", "sha256_in_prefix": "d679c0259db688aef04b39af643a373cc86e5aa248b76a8a7655f34f286808ae", "size_in_bytes": 19759}, {"_path": "include/arrow/util/base64.h", "path_type": "hardlink", "sha256": "ab370113df1c83c4f1e623c902f4383dd7f6c9ce91dabfb8c86252d7f0c421e6", "sha256_in_prefix": "ab370113df1c83c4f1e623c902f4383dd7f6c9ce91dabfb8c86252d7f0c421e6", "size_in_bytes": 1095}, {"_path": "include/arrow/util/basic_decimal.h", "path_type": "hardlink", "sha256": "436cdf92c51d61cf764cbf29a3cd48fda40e9b01d3c1918f8ea5ef24cedc81e9", "sha256_in_prefix": "436cdf92c51d61cf764cbf29a3cd48fda40e9b01d3c1918f8ea5ef24cedc81e9", "size_in_bytes": 33569}, {"_path": "include/arrow/util/benchmark_util.h", "path_type": "hardlink", "sha256": "486de07f013ec06359030a4bdd3bdf7e74a2646336733b55e714419dbab2d8cc", "sha256_in_prefix": "486de07f013ec06359030a4bdd3bdf7e74a2646336733b55e714419dbab2d8cc", "size_in_bytes": 7641}, {"_path": "include/arrow/util/binary_view_util.h", "path_type": "hardlink", "sha256": "fac140417f5c9df5a6999268f2cb455f9be42648bb4f6525a00bc3cd83b430df", "sha256_in_prefix": "fac140417f5c9df5a6999268f2cb455f9be42648bb4f6525a00bc3cd83b430df", "size_in_bytes": 4625}, {"_path": "include/arrow/util/bit_block_counter.h", "path_type": "hardlink", "sha256": "89221e9b38b3c55a24c02d197ba4a34a2f9a97f9eb3f65625c5e893e82155167", "sha256_in_prefix": "89221e9b38b3c55a24c02d197ba4a34a2f9a97f9eb3f65625c5e893e82155167", "size_in_bytes": 20162}, {"_path": "include/arrow/util/bit_run_reader.h", "path_type": "hardlink", "sha256": "2160f0c3a0e6f0e16c091949d2112924a88730ade750cde9a9b774f666617084", "sha256_in_prefix": "2160f0c3a0e6f0e16c091949d2112924a88730ade750cde9a9b774f666617084", "size_in_bytes": 16616}, {"_path": "include/arrow/util/bit_util.h", "path_type": "hardlink", "sha256": "4b44db45e65eb7c3293c5664f708df6337caa6406abad86490593642dc7307b5", "sha256_in_prefix": "4b44db45e65eb7c3293c5664f708df6337caa6406abad86490593642dc7307b5", "size_in_bytes": 12108}, {"_path": "include/arrow/util/bitmap.h", "path_type": "hardlink", "sha256": "a83a0d97e4bc405a19db6d07b00b4037eb3e5e6e497278ce5cd38675a22cb0bd", "sha256_in_prefix": "a83a0d97e4bc405a19db6d07b00b4037eb3e5e6e497278ce5cd38675a22cb0bd", "size_in_bytes": 17462}, {"_path": "include/arrow/util/bitmap_builders.h", "path_type": "hardlink", "sha256": "dbcae307d006c06f95333b9e9fd14046cb1d32e974449c766663c540bf525751", "sha256_in_prefix": "dbcae307d006c06f95333b9e9fd14046cb1d32e974449c766663c540bf525751", "size_in_bytes": 1596}, {"_path": "include/arrow/util/bitmap_generate.h", "path_type": "hardlink", "sha256": "9ba66c370c751a1b0492d42beb73715c74245f607b36d8b4d755d8b0f836c5fa", "sha256_in_prefix": "9ba66c370c751a1b0492d42beb73715c74245f607b36d8b4d755d8b0f836c5fa", "size_in_bytes": 3661}, {"_path": "include/arrow/util/bitmap_ops.h", "path_type": "hardlink", "sha256": "7cf3c3fdcbd779b60bac0790382098c9d5350301747da3790f040a57ca29f84b", "sha256_in_prefix": "7cf3c3fdcbd779b60bac0790382098c9d5350301747da3790f040a57ca29f84b", "size_in_bytes": 10877}, {"_path": "include/arrow/util/bitmap_reader.h", "path_type": "hardlink", "sha256": "a4bacc0d6855a3e41bdd5d662c0480cff688e906710c746bdfb12dab1a8677d1", "sha256_in_prefix": "a4bacc0d6855a3e41bdd5d662c0480cff688e906710c746bdfb12dab1a8677d1", "size_in_bytes": 8353}, {"_path": "include/arrow/util/bitmap_visit.h", "path_type": "hardlink", "sha256": "9b29fc93ae95aef6672fa47a556e880cf4df3baf15c636c9f14a7922e4a314be", "sha256_in_prefix": "9b29fc93ae95aef6672fa47a556e880cf4df3baf15c636c9f14a7922e4a314be", "size_in_bytes": 3470}, {"_path": "include/arrow/util/bitmap_writer.h", "path_type": "hardlink", "sha256": "6b88285e12e5634a9c7ef6316df6c60ff1d9f00bb5c0571b575b5517704f697b", "sha256_in_prefix": "6b88285e12e5634a9c7ef6316df6c60ff1d9f00bb5c0571b575b5517704f697b", "size_in_bytes": 9383}, {"_path": "include/arrow/util/bitset_stack.h", "path_type": "hardlink", "sha256": "0f8f486594b364e33686a87a6fe7d3d2f8112127f59909e5e281b99e72c16780", "sha256_in_prefix": "0f8f486594b364e33686a87a6fe7d3d2f8112127f59909e5e281b99e72c16780", "size_in_bytes": 2776}, {"_path": "include/arrow/util/bpacking.h", "path_type": "hardlink", "sha256": "aa28985e064b5997185fab26ef9fef050eaaa47b52d40c1ab0be7d6102f63ed9", "sha256_in_prefix": "aa28985e064b5997185fab26ef9fef050eaaa47b52d40c1ab0be7d6102f63ed9", "size_in_bytes": 1175}, {"_path": "include/arrow/util/bpacking64_default.h", "path_type": "hardlink", "sha256": "abb91ffc15bada4e39bf5a8cb6726d2c83e4f15b492002dce675e4626212cb7c", "sha256_in_prefix": "abb91ffc15bada4e39bf5a8cb6726d2c83e4f15b492002dce675e4626212cb7c", "size_in_bytes": 196990}, {"_path": "include/arrow/util/bpacking_avx2.h", "path_type": "hardlink", "sha256": "ca6409190739e16df36eb4a892d8db01c0675b06eafd2a618972cb23e1ba7c78", "sha256_in_prefix": "ca6409190739e16df36eb4a892d8db01c0675b06eafd2a618972cb23e1ba7c78", "size_in_bytes": 1009}, {"_path": "include/arrow/util/bpacking_avx512.h", "path_type": "hardlink", "sha256": "67fac042988a2441fef50487517a5b0e6662020226ec23c21df3e7c252190c01", "sha256_in_prefix": "67fac042988a2441fef50487517a5b0e6662020226ec23c21df3e7c252190c01", "size_in_bytes": 1011}, {"_path": "include/arrow/util/bpacking_default.h", "path_type": "hardlink", "sha256": "9c38b883925dc96c176bf27712a136d9b1bd4781bb0b377a5bbe45f6ca515395", "sha256_in_prefix": "9c38b883925dc96c176bf27712a136d9b1bd4781bb0b377a5bbe45f6ca515395", "size_in_bytes": 103760}, {"_path": "include/arrow/util/bpacking_neon.h", "path_type": "hardlink", "sha256": "bc4f95e04f1da6a4a393b76af246a00f4efe9e1450286bdc60c85cfdd1389ea8", "sha256_in_prefix": "bc4f95e04f1da6a4a393b76af246a00f4efe9e1450286bdc60c85cfdd1389ea8", "size_in_bytes": 1009}, {"_path": "include/arrow/util/byte_size.h", "path_type": "hardlink", "sha256": "3ddd9cff76b421e48e51ebe13c89573640e6828074ea0e1cf580acb91c3048a3", "sha256_in_prefix": "3ddd9cff76b421e48e51ebe13c89573640e6828074ea0e1cf580acb91c3048a3", "size_in_bytes": 3997}, {"_path": "include/arrow/util/cancel.h", "path_type": "hardlink", "sha256": "4a0032e52d130484a70b2a69e11fce23e8cf510f7deeab6c2ce49d5e8f25e5b3", "sha256_in_prefix": "4a0032e52d130484a70b2a69e11fce23e8cf510f7deeab6c2ce49d5e8f25e5b3", "size_in_bytes": 3659}, {"_path": "include/arrow/util/checked_cast.h", "path_type": "hardlink", "sha256": "491f5083c36e2d2049c36c3551f81e1af09f4fc93bc2b6cdec1cc00d03997c05", "sha256_in_prefix": "491f5083c36e2d2049c36c3551f81e1af09f4fc93bc2b6cdec1cc00d03997c05", "size_in_bytes": 2076}, {"_path": "include/arrow/util/compare.h", "path_type": "hardlink", "sha256": "38bad24b2965918e12bf4d082bedfb03677af20af83b09d626cc67d5a17dc535", "sha256_in_prefix": "38bad24b2965918e12bf4d082bedfb03677af20af83b09d626cc67d5a17dc535", "size_in_bytes": 1982}, {"_path": "include/arrow/util/compression.h", "path_type": "hardlink", "sha256": "7ef954468589b203bc1ebe97b3f54d6aa88e6ad9881a7f64b5552462fee922ee", "sha256_in_prefix": "7ef954468589b203bc1ebe97b3f54d6aa88e6ad9881a7f64b5552462fee922ee", "size_in_bytes": 8427}, {"_path": "include/arrow/util/concurrent_map.h", "path_type": "hardlink", "sha256": "c0c8bd5831df46e27f6921607093e9b15c0627dbc82796a0699deb55497019ee", "sha256_in_prefix": "c0c8bd5831df46e27f6921607093e9b15c0627dbc82796a0699deb55497019ee", "size_in_bytes": 1775}, {"_path": "include/arrow/util/config.h", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/apache-arrow_1751095169775/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "dbbad6b11f2bd5c92777de6cc09d3b756ba0fbbfa172c0f8a41a438a926e9c77", "sha256_in_prefix": "021a91ea0238fbc2e2e0e41833e1454ed86dea4b1ef20f2edfdc7a1044bbc7aa", "size_in_bytes": 3976}, {"_path": "include/arrow/util/converter.h", "path_type": "hardlink", "sha256": "3c82dfa2ce959672cae9f38531f2c852188a977a35749a3d4f81c95de47b5791", "sha256_in_prefix": "3c82dfa2ce959672cae9f38531f2c852188a977a35749a3d4f81c95de47b5791", "size_in_bytes": 14637}, {"_path": "include/arrow/util/counting_semaphore.h", "path_type": "hardlink", "sha256": "8971d86a0aa2ffe6b2ef74f5b8f9afee91b7df8498df80d440b49db43ff8fed0", "sha256_in_prefix": "8971d86a0aa2ffe6b2ef74f5b8f9afee91b7df8498df80d440b49db43ff8fed0", "size_in_bytes": 2251}, {"_path": "include/arrow/util/cpu_info.h", "path_type": "hardlink", "sha256": "32a2dd25a6c1664cc38e249c690ee27fd766a001af5d3d906af1a81a4868de55", "sha256_in_prefix": "32a2dd25a6c1664cc38e249c690ee27fd766a001af5d3d906af1a81a4868de55", "size_in_bytes": 3964}, {"_path": "include/arrow/util/crc32.h", "path_type": "hardlink", "sha256": "e2037433e4919f168629c8b60133db316646d931b76142d78da4e969d57451d9", "sha256_in_prefix": "e2037433e4919f168629c8b60133db316646d931b76142d78da4e969d57451d9", "size_in_bytes": 1337}, {"_path": "include/arrow/util/debug.h", "path_type": "hardlink", "sha256": "08f07fa033ae67fbbcf5ef7033c6c69fcf261af0a581f6bb503c43a61eaff6c6", "sha256_in_prefix": "08f07fa033ae67fbbcf5ef7033c6c69fcf261af0a581f6bb503c43a61eaff6c6", "size_in_bytes": 971}, {"_path": "include/arrow/util/decimal.h", "path_type": "hardlink", "sha256": "a3363fa51b0181fb46ef7ab3d0a2843dc8454391d14dbe68c4271321d5842fb8", "sha256_in_prefix": "a3363fa51b0181fb46ef7ab3d0a2843dc8454391d14dbe68c4271321d5842fb8", "size_in_bytes": 20831}, {"_path": "include/arrow/util/delimiting.h", "path_type": "hardlink", "sha256": "2587bd61c58c7854ff212ba88f1fd5815a8e60bbd9d938a2476b0d9fe59d7814", "sha256_in_prefix": "2587bd61c58c7854ff212ba88f1fd5815a8e60bbd9d938a2476b0d9fe59d7814", "size_in_bytes": 7317}, {"_path": "include/arrow/util/dict_util.h", "path_type": "hardlink", "sha256": "1e2a6f015950d50eb33677aef6d60ec1552fe8d2e4941bb621f6757a8792a558", "sha256_in_prefix": "1e2a6f015950d50eb33677aef6d60ec1552fe8d2e4941bb621f6757a8792a558", "size_in_bytes": 986}, {"_path": "include/arrow/util/dispatch.h", "path_type": "hardlink", "sha256": "83a47dc3c6ac0934720d3168c548a9bdd39e87a61efc5bd90463fa670836b773", "sha256_in_prefix": "83a47dc3c6ac0934720d3168c548a9bdd39e87a61efc5bd90463fa670836b773", "size_in_bytes": 3235}, {"_path": "include/arrow/util/double_conversion.h", "path_type": "hardlink", "sha256": "db7414d93157e21a41667a2a303c932b16681fb994f6a918daf905d4a2fc6d6e", "sha256_in_prefix": "db7414d93157e21a41667a2a303c932b16681fb994f6a918daf905d4a2fc6d6e", "size_in_bytes": 1243}, {"_path": "include/arrow/util/endian.h", "path_type": "hardlink", "sha256": "8e9e10a10f6bdaf6fea2282b95bf408505bb2f18318e3edd7ac423ce412b7bb8", "sha256_in_prefix": "8e9e10a10f6bdaf6fea2282b95bf408505bb2f18318e3edd7ac423ce412b7bb8", "size_in_bytes": 8176}, {"_path": "include/arrow/util/float16.h", "path_type": "hardlink", "sha256": "57a398caf313c9b45625684d7e33fe36caebc4130f883900fbb45691f0556520", "sha256_in_prefix": "57a398caf313c9b45625684d7e33fe36caebc4130f883900fbb45691f0556520", "size_in_bytes": 7230}, {"_path": "include/arrow/util/formatting.h", "path_type": "hardlink", "sha256": "efcdb028de992a51ceedc40b0bc08a085f524e2c6f2c68d7ae9fc2c11a97c95b", "sha256_in_prefix": "efcdb028de992a51ceedc40b0bc08a085f524e2c6f2c68d7ae9fc2c11a97c95b", "size_in_bytes": 22554}, {"_path": "include/arrow/util/functional.h", "path_type": "hardlink", "sha256": "e258ca5d2597dc6fe5053d817cb5ee1b8e29cd9c1529e6a88e92d60a78aa2b27", "sha256_in_prefix": "e258ca5d2597dc6fe5053d817cb5ee1b8e29cd9c1529e6a88e92d60a78aa2b27", "size_in_bytes": 5612}, {"_path": "include/arrow/util/future.h", "path_type": "hardlink", "sha256": "b6c4950c41f67615cacaf20a97a47d0550689693dd657a1145fdbe6116ed7718", "sha256_in_prefix": "b6c4950c41f67615cacaf20a97a47d0550689693dd657a1145fdbe6116ed7718", "size_in_bytes": 32296}, {"_path": "include/arrow/util/hash_util.h", "path_type": "hardlink", "sha256": "0a388d54f5093f15ef549cbbcacefd6886fb601e819bee674c9011e031ecc5cb", "sha256_in_prefix": "0a388d54f5093f15ef549cbbcacefd6886fb601e819bee674c9011e031ecc5cb", "size_in_bytes": 1918}, {"_path": "include/arrow/util/hashing.h", "path_type": "hardlink", "sha256": "4bcf234d3439936dcbecd392a844a9fbf285bc74bf7e0d06c9f885f4d030bd5f", "sha256_in_prefix": "4bcf234d3439936dcbecd392a844a9fbf285bc74bf7e0d06c9f885f4d030bd5f", "size_in_bytes": 33214}, {"_path": "include/arrow/util/int_util.h", "path_type": "hardlink", "sha256": "cd3380ab9ecce2951ee3af56a675ba23984db717b7bc64479595a19e00350f33", "sha256_in_prefix": "cd3380ab9ecce2951ee3af56a675ba23984db717b7bc64479595a19e00350f33", "size_in_bytes": 4859}, {"_path": "include/arrow/util/int_util_overflow.h", "path_type": "hardlink", "sha256": "02dbe41bbbf7fb5815cd6e52ac5add56462e5c5b53efafe7c6b2adcc86f3ffd5", "sha256_in_prefix": "02dbe41bbbf7fb5815cd6e52ac5add56462e5c5b53efafe7c6b2adcc86f3ffd5", "size_in_bytes": 4895}, {"_path": "include/arrow/util/io_util.h", "path_type": "hardlink", "sha256": "53a5530a1d322949983dac36a06f8296525de09d3619c7ba6f4a937ea162f44e", "sha256_in_prefix": "53a5530a1d322949983dac36a06f8296525de09d3619c7ba6f4a937ea162f44e", "size_in_bytes": 13709}, {"_path": "include/arrow/util/iterator.h", "path_type": "hardlink", "sha256": "d3199fa3aaeff03c0220ddc89af2a716405786a4169156eaa7ee146dd781dcad", "sha256_in_prefix": "d3199fa3aaeff03c0220ddc89af2a716405786a4169156eaa7ee146dd781dcad", "size_in_bytes": 18354}, {"_path": "include/arrow/util/key_value_metadata.h", "path_type": "hardlink", "sha256": "c2353ab9019c4a6cbe605a8cb3aaf02cfec4e17fb42050633eb599b2d8acb734", "sha256_in_prefix": "c2353ab9019c4a6cbe605a8cb3aaf02cfec4e17fb42050633eb599b2d8acb734", "size_in_bytes": 3590}, {"_path": "include/arrow/util/launder.h", "path_type": "hardlink", "sha256": "0b7acd051878adeb94a7c62e45d19053de563dcf2f9786c063ecf92d78038ae0", "sha256_in_prefix": "0b7acd051878adeb94a7c62e45d19053de563dcf2f9786c063ecf92d78038ae0", "size_in_bytes": 1046}, {"_path": "include/arrow/util/list_util.h", "path_type": "hardlink", "sha256": "fce9adb03a9efa69d9ffbb555b1076c8776008986988c13f44fde75c7cca6dd2", "sha256_in_prefix": "fce9adb03a9efa69d9ffbb555b1076c8776008986988c13f44fde75c7cca6dd2", "size_in_bytes": 2028}, {"_path": "include/arrow/util/logger.h", "path_type": "hardlink", "sha256": "a7d8b874d8277b7e8b5a91663526016204d0e241526a8db4749e342e045164a4", "sha256_in_prefix": "a7d8b874d8277b7e8b5a91663526016204d0e241526a8db4749e342e045164a4", "size_in_bytes": 6693}, {"_path": "include/arrow/util/logging.h", "path_type": "hardlink", "sha256": "798d6c67540272fcb99692707ce08bdabb5180b8dcf15f320dff6eb126bdf9de", "sha256_in_prefix": "798d6c67540272fcb99692707ce08bdabb5180b8dcf15f320dff6eb126bdf9de", "size_in_bytes": 9694}, {"_path": "include/arrow/util/macros.h", "path_type": "hardlink", "sha256": "76a9c588352b154caa1f234fe3112be785a06801175fc804e191bbfa2de77d94", "sha256_in_prefix": "76a9c588352b154caa1f234fe3112be785a06801175fc804e191bbfa2de77d94", "size_in_bytes": 9336}, {"_path": "include/arrow/util/map.h", "path_type": "hardlink", "sha256": "29b281dd035cdda591ff4614d52eda17d7dd2345420011b1105d55112da83aa5", "sha256_in_prefix": "29b281dd035cdda591ff4614d52eda17d7dd2345420011b1105d55112da83aa5", "size_in_bytes": 2476}, {"_path": "include/arrow/util/math_constants.h", "path_type": "hardlink", "sha256": "dac7d6a1573cb321f3f17dba5e00667a3cd74ad97b866bca88e87deb6da85190", "sha256_in_prefix": "dac7d6a1573cb321f3f17dba5e00667a3cd74ad97b866bca88e87deb6da85190", "size_in_bytes": 1112}, {"_path": "include/arrow/util/memory.h", "path_type": "hardlink", "sha256": "aacc4582f8ffc28ccee4ec48b3a7c775c6a6eda89fa68cea735684f353fdd58a", "sha256_in_prefix": "aacc4582f8ffc28ccee4ec48b3a7c775c6a6eda89fa68cea735684f353fdd58a", "size_in_bytes": 1566}, {"_path": "include/arrow/util/mutex.h", "path_type": "hardlink", "sha256": "9f86ecac72b643ccdb62c404c8d685a8dbbffefbea830a3501facb2c2c5f9295", "sha256_in_prefix": "9f86ecac72b643ccdb62c404c8d685a8dbbffefbea830a3501facb2c2c5f9295", "size_in_bytes": 2554}, {"_path": "include/arrow/util/parallel.h", "path_type": "hardlink", "sha256": "ff8df04d54abb49c0e911b772897091750f2607c21a166d63ecac0b307ed3fe4", "sha256_in_prefix": "ff8df04d54abb49c0e911b772897091750f2607c21a166d63ecac0b307ed3fe4", "size_in_bytes": 3817}, {"_path": "include/arrow/util/pcg_random.h", "path_type": "hardlink", "sha256": "9db5e8c1f089162cb81a355f17d23c56f07a7f1932479ccd07514a76716c6134", "sha256_in_prefix": "9db5e8c1f089162cb81a355f17d23c56f07a7f1932479ccd07514a76716c6134", "size_in_bytes": 1252}, {"_path": "include/arrow/util/prefetch.h", "path_type": "hardlink", "sha256": "bda13814f76c71bb4ed1c3dbce5f05d4fcc1d4d0ced7ccad6251266421c38c7b", "sha256_in_prefix": "bda13814f76c71bb4ed1c3dbce5f05d4fcc1d4d0ced7ccad6251266421c38c7b", "size_in_bytes": 1251}, {"_path": "include/arrow/util/print.h", "path_type": "hardlink", "sha256": "5f409fb96cc392af02347684507dc8dbb622e2fff37683a8eec76b4da77c5abd", "sha256_in_prefix": "5f409fb96cc392af02347684507dc8dbb622e2fff37683a8eec76b4da77c5abd", "size_in_bytes": 2444}, {"_path": "include/arrow/util/queue.h", "path_type": "hardlink", "sha256": "5fdbd16505f760bfdad8bcf07becdc34d1e0b91ec5a066260fe4344c7aac0813", "sha256_in_prefix": "5fdbd16505f760bfdad8bcf07becdc34d1e0b91ec5a066260fe4344c7aac0813", "size_in_bytes": 1017}, {"_path": "include/arrow/util/range.h", "path_type": "hardlink", "sha256": "ca17b9a498992222d43bcb58af8d3c63dc84b05ac577b16b04c7932f684038a6", "sha256_in_prefix": "ca17b9a498992222d43bcb58af8d3c63dc84b05ac577b16b04c7932f684038a6", "size_in_bytes": 8526}, {"_path": "include/arrow/util/ree_util.h", "path_type": "hardlink", "sha256": "c1a4c13907f05861e1a006074f2ca1527336052c0eaaca1ffc7fda907bd4827a", "sha256_in_prefix": "c1a4c13907f05861e1a006074f2ca1527336052c0eaaca1ffc7fda907bd4827a", "size_in_bytes": 22395}, {"_path": "include/arrow/util/regex.h", "path_type": "hardlink", "sha256": "4e3f760adb4e8761f14b410a43ff7eb3130102c42b0ce50a34fd2780824574ff", "sha256_in_prefix": "4e3f760adb4e8761f14b410a43ff7eb3130102c42b0ce50a34fd2780824574ff", "size_in_bytes": 1742}, {"_path": "include/arrow/util/rows_to_batches.h", "path_type": "hardlink", "sha256": "3d93682de3027c925d78756f527cb450773902d4b485cb540a2ef3533b49a5e1", "sha256_in_prefix": "3d93682de3027c925d78756f527cb450773902d4b485cb540a2ef3533b49a5e1", "size_in_bytes": 7120}, {"_path": "include/arrow/util/simd.h", "path_type": "hardlink", "sha256": "3e92a6f9a5a96582183f4367c86ad071e38ef66dffec96cde2eae8d088484fdc", "sha256_in_prefix": "3e92a6f9a5a96582183f4367c86ad071e38ef66dffec96cde2eae8d088484fdc", "size_in_bytes": 1679}, {"_path": "include/arrow/util/small_vector.h", "path_type": "hardlink", "sha256": "74334d305a4d76d21bc4b3f72fe87f6efdc0f2b698bf8215bb22c4cd454c81c9", "sha256_in_prefix": "74334d305a4d76d21bc4b3f72fe87f6efdc0f2b698bf8215bb22c4cd454c81c9", "size_in_bytes": 14421}, {"_path": "include/arrow/util/sort.h", "path_type": "hardlink", "sha256": "71766f04dfc471790de63d31857da8362b1b0a14f640a5cff4acc38235d6dbf3", "sha256_in_prefix": "71766f04dfc471790de63d31857da8362b1b0a14f640a5cff4acc38235d6dbf3", "size_in_bytes": 2466}, {"_path": "include/arrow/util/spaced.h", "path_type": "hardlink", "sha256": "efdd051424dd640fb3eaa2ae109339ff01b6e12a934d5b728fb3ca9cb05eeff4", "sha256_in_prefix": "efdd051424dd640fb3eaa2ae109339ff01b6e12a934d5b728fb3ca9cb05eeff4", "size_in_bytes": 3567}, {"_path": "include/arrow/util/span.h", "path_type": "hardlink", "sha256": "d5e76f7a1279ebbb107d99f8806be7e24715a4b00953dada5c76ea13f743a594", "sha256_in_prefix": "d5e76f7a1279ebbb107d99f8806be7e24715a4b00953dada5c76ea13f743a594", "size_in_bytes": 4298}, {"_path": "include/arrow/util/stopwatch.h", "path_type": "hardlink", "sha256": "00319b104535c7e7efa7f36c21d4c71f9056d1bf630c1f2b4c08f558e8240a57", "sha256_in_prefix": "00319b104535c7e7efa7f36c21d4c71f9056d1bf630c1f2b4c08f558e8240a57", "size_in_bytes": 1401}, {"_path": "include/arrow/util/string.h", "path_type": "hardlink", "sha256": "858b60e1dde419004775dd2f1ae2894e555eb9e08281fc83de2abe7de300de9f", "sha256_in_prefix": "858b60e1dde419004775dd2f1ae2894e555eb9e08281fc83de2abe7de300de9f", "size_in_bytes": 5756}, {"_path": "include/arrow/util/string_builder.h", "path_type": "hardlink", "sha256": "1064f46b3445e1d075710a309b67ebbb04918cf6ee1ede61d239daf095b24f09", "sha256_in_prefix": "1064f46b3445e1d075710a309b67ebbb04918cf6ee1ede61d239daf095b24f09", "size_in_bytes": 2653}, {"_path": "include/arrow/util/task_group.h", "path_type": "hardlink", "sha256": "7c8df7d0da094fcbbce0011403aa52c56ac4ed404a9f62da3380df3c5a1a96a0", "sha256_in_prefix": "7c8df7d0da094fcbbce0011403aa52c56ac4ed404a9f62da3380df3c5a1a96a0", "size_in_bytes": 4362}, {"_path": "include/arrow/util/tdigest.h", "path_type": "hardlink", "sha256": "2fa9d28fe1559582edc0a27de165fdaa9b3d614e9883e7b7c703fa0b4a84ee9c", "sha256_in_prefix": "2fa9d28fe1559582edc0a27de165fdaa9b3d614e9883e7b7c703fa0b4a84ee9c", "size_in_bytes": 3058}, {"_path": "include/arrow/util/test_common.h", "path_type": "hardlink", "sha256": "66788b4fc4ef0147421364f662bb650ca7700cd3c1a53e5ef55d4488f1c7f4b5", "sha256_in_prefix": "66788b4fc4ef0147421364f662bb650ca7700cd3c1a53e5ef55d4488f1c7f4b5", "size_in_bytes": 2837}, {"_path": "include/arrow/util/thread_pool.h", "path_type": "hardlink", "sha256": "e33b4bc242474094264e6ab0cbc2060e6028f17e0dfa8dea8ba7fdd5a8339244", "sha256_in_prefix": "e33b4bc242474094264e6ab0cbc2060e6028f17e0dfa8dea8ba7fdd5a8339244", "size_in_bytes": 24426}, {"_path": "include/arrow/util/time.h", "path_type": "hardlink", "sha256": "e178bc27369895617155a7a798227ba2b320bb872e2842ef6ed322b33b891d40", "sha256_in_prefix": "e178bc27369895617155a7a798227ba2b320bb872e2842ef6ed322b33b891d40", "size_in_bytes": 2988}, {"_path": "include/arrow/util/tracing.h", "path_type": "hardlink", "sha256": "b157c2fd18f683091629548a4f497414e3b9736106b1f630964657e1df677310", "sha256_in_prefix": "b157c2fd18f683091629548a4f497414e3b9736106b1f630964657e1df677310", "size_in_bytes": 1286}, {"_path": "include/arrow/util/trie.h", "path_type": "hardlink", "sha256": "581bebc983b6b0d7683dcf9407e5e64375b349e77bf6a22c4a0ed8582aefc0d6", "sha256_in_prefix": "581bebc983b6b0d7683dcf9407e5e64375b349e77bf6a22c4a0ed8582aefc0d6", "size_in_bytes": 7121}, {"_path": "include/arrow/util/type_fwd.h", "path_type": "hardlink", "sha256": "25e6759ae22254ac477005f306290c0a693fed2c45cff22a9b3b93eb263517e0", "sha256_in_prefix": "25e6759ae22254ac477005f306290c0a693fed2c45cff22a9b3b93eb263517e0", "size_in_bytes": 1803}, {"_path": "include/arrow/util/type_traits.h", "path_type": "hardlink", "sha256": "17419d83fddf68cd0c99904e5ceb29473530bb19e329b15a5692624c468e5c65", "sha256_in_prefix": "17419d83fddf68cd0c99904e5ceb29473530bb19e329b15a5692624c468e5c65", "size_in_bytes": 1731}, {"_path": "include/arrow/util/ubsan.h", "path_type": "hardlink", "sha256": "9b5f0fc6763e074c78b707b08f236dd94532e6a1e7b62a25327582d099244e63", "sha256_in_prefix": "9b5f0fc6763e074c78b707b08f236dd94532e6a1e7b62a25327582d099244e63", "size_in_bytes": 2817}, {"_path": "include/arrow/util/union_util.h", "path_type": "hardlink", "sha256": "3d2b2c062c3ebfe3c37a7feaef973a3a434ee4fc3220f8466dff4f3098fb3f63", "sha256_in_prefix": "3d2b2c062c3ebfe3c37a7feaef973a3a434ee4fc3220f8466dff4f3098fb3f63", "size_in_bytes": 1211}, {"_path": "include/arrow/util/unreachable.h", "path_type": "hardlink", "sha256": "3b54c6e28cc2613dffc6f0e9268b8a5ab945003204a487a6436f32e03a88c2ee", "sha256_in_prefix": "3b54c6e28cc2613dffc6f0e9268b8a5ab945003204a487a6436f32e03a88c2ee", "size_in_bytes": 1070}, {"_path": "include/arrow/util/uri.h", "path_type": "hardlink", "sha256": "0f6e3379b6b315caca1adee21a991c190f3b0ee17ed806e33ca5640e5abd36e9", "sha256_in_prefix": "0f6e3379b6b315caca1adee21a991c190f3b0ee17ed806e33ca5640e5abd36e9", "size_in_bytes": 3886}, {"_path": "include/arrow/util/utf8.h", "path_type": "hardlink", "sha256": "7e5199efcea41e8df75e0ff3c34cd503d1804fc8d874f5074d58483c71a33a3f", "sha256_in_prefix": "7e5199efcea41e8df75e0ff3c34cd503d1804fc8d874f5074d58483c71a33a3f", "size_in_bytes": 2031}, {"_path": "include/arrow/util/value_parsing.h", "path_type": "hardlink", "sha256": "ca96e7208c5f1437c39842e29c4892d9161291e69b60301fb8a3d6e58b267d1c", "sha256_in_prefix": "ca96e7208c5f1437c39842e29c4892d9161291e69b60301fb8a3d6e58b267d1c", "size_in_bytes": 29995}, {"_path": "include/arrow/util/vector.h", "path_type": "hardlink", "sha256": "c35971646dc253482ad99ac1c9e53c417d80d09793b68a0675a64e354b1595fb", "sha256_in_prefix": "c35971646dc253482ad99ac1c9e53c417d80d09793b68a0675a64e354b1595fb", "size_in_bytes": 5697}, {"_path": "include/arrow/util/visibility.h", "path_type": "hardlink", "sha256": "0c511d97c4c2af7d2bddbeef969833248880e4db0aede5bd52678be3b3e070b9", "sha256_in_prefix": "0c511d97c4c2af7d2bddbeef969833248880e4db0aede5bd52678be3b3e070b9", "size_in_bytes": 2835}, {"_path": "include/arrow/util/windows_compatibility.h", "path_type": "hardlink", "sha256": "0a199ef5f591a984737c86cbc3b57fc9e88859ddc5e1d15e1ba2261c7af85eac", "sha256_in_prefix": "0a199ef5f591a984737c86cbc3b57fc9e88859ddc5e1d15e1ba2261c7af85eac", "size_in_bytes": 1255}, {"_path": "include/arrow/util/windows_fixup.h", "path_type": "hardlink", "sha256": "863a21eb3bc1f2ef0e55442a2ed75cae6a21333a00a0bcba5c914bc5c7c584ad", "sha256_in_prefix": "863a21eb3bc1f2ef0e55442a2ed75cae6a21333a00a0bcba5c914bc5c7c584ad", "size_in_bytes": 1435}, {"_path": "include/arrow/vendored/ProducerConsumerQueue.h", "path_type": "hardlink", "sha256": "073d64b373438175cb7d3f13314904dfc46930e4b029146dc14d5edfb635094c", "sha256_in_prefix": "073d64b373438175cb7d3f13314904dfc46930e4b029146dc14d5edfb635094c", "size_in_bytes": 6101}, {"_path": "include/arrow/vendored/datetime.h", "path_type": "hardlink", "sha256": "b6c15bcfc2ca045cd1cd310e00a6725916dd14b7e70a765108af53ca2d4f00db", "sha256_in_prefix": "b6c15bcfc2ca045cd1cd310e00a6725916dd14b7e70a765108af53ca2d4f00db", "size_in_bytes": 1103}, {"_path": "include/arrow/vendored/datetime/date.h", "path_type": "hardlink", "sha256": "7dafed9a4307c26c7dbd91e31f995956164509811c1e2e719ae796dc5d6d0c4e", "sha256_in_prefix": "7dafed9a4307c26c7dbd91e31f995956164509811c1e2e719ae796dc5d6d0c4e", "size_in_bytes": 237808}, {"_path": "include/arrow/vendored/datetime/ios.h", "path_type": "hardlink", "sha256": "427bb48aecb6f9e8a7f4a915a122f5b7bd7f5bf5456647630d5b0e9d84e73f7f", "sha256_in_prefix": "427bb48aecb6f9e8a7f4a915a122f5b7bd7f5bf5456647630d5b0e9d84e73f7f", "size_in_bytes": 1641}, {"_path": "include/arrow/vendored/datetime/tz.h", "path_type": "hardlink", "sha256": "9b9249bfb2c4ed5ba4a7c879d2bf74b027db392003d9b3152154145313597834", "sha256_in_prefix": "9b9249bfb2c4ed5ba4a7c879d2bf74b027db392003d9b3152154145313597834", "size_in_bytes": 85347}, {"_path": "include/arrow/vendored/datetime/tz_private.h", "path_type": "hardlink", "sha256": "a4390a5d87737f3439ba1fa37148544412ea1e8d34b74527962994762339dc2b", "sha256_in_prefix": "a4390a5d87737f3439ba1fa37148544412ea1e8d34b74527962994762339dc2b", "size_in_bytes": 10706}, {"_path": "include/arrow/vendored/datetime/visibility.h", "path_type": "hardlink", "sha256": "54218ace140e80bd73c065ca97fee52d42df4b2d0eb0fb7c14b587c00e2c3ad5", "sha256_in_prefix": "54218ace140e80bd73c065ca97fee52d42df4b2d0eb0fb7c14b587c00e2c3ad5", "size_in_bytes": 1002}, {"_path": "include/arrow/vendored/double-conversion/bignum-dtoa.h", "path_type": "hardlink", "sha256": "8a61a1720d11cb032c14d3184eaa7aae55f0d8764220095af120bf9fdda00aa1", "sha256_in_prefix": "8a61a1720d11cb032c14d3184eaa7aae55f0d8764220095af120bf9fdda00aa1", "size_in_bytes": 4358}, {"_path": "include/arrow/vendored/double-conversion/bignum.h", "path_type": "hardlink", "sha256": "46743608f2fc3ede9f5421a1ffc543175d5efc6cabaf03b4207d2e3274dcb04b", "sha256_in_prefix": "46743608f2fc3ede9f5421a1ffc543175d5efc6cabaf03b4207d2e3274dcb04b", "size_in_bytes": 5949}, {"_path": "include/arrow/vendored/double-conversion/cached-powers.h", "path_type": "hardlink", "sha256": "8e3c1f4776ee7bb98d944e656d3ac54762a582345ec363a49a305aee840ed108", "sha256_in_prefix": "8e3c1f4776ee7bb98d944e656d3ac54762a582345ec363a49a305aee840ed108", "size_in_bytes": 3079}, {"_path": "include/arrow/vendored/double-conversion/diy-fp.h", "path_type": "hardlink", "sha256": "27e460a87dbb8eca53e546ed87da5303d340647e9eee7d4e561c4ca1d822f127", "sha256_in_prefix": "27e460a87dbb8eca53e546ed87da5303d340647e9eee7d4e561c4ca1d822f127", "size_in_bytes": 5088}, {"_path": "include/arrow/vendored/double-conversion/double-conversion.h", "path_type": "hardlink", "sha256": "2754e5e7ef1a158d00f529da03dcf943dd239ccc70e798a594f22e13e8dd0f94", "sha256_in_prefix": "2754e5e7ef1a158d00f529da03dcf943dd239ccc70e798a594f22e13e8dd0f94", "size_in_bytes": 1804}, {"_path": "include/arrow/vendored/double-conversion/double-to-string.h", "path_type": "hardlink", "sha256": "0beb4a462d08b8bc9c5e04ba082d68885902ae83a8ffe00ed153a399f7b4b651", "sha256_in_prefix": "0beb4a462d08b8bc9c5e04ba082d68885902ae83a8ffe00ed153a399f7b4b651", "size_in_bytes": 23925}, {"_path": "include/arrow/vendored/double-conversion/fast-dtoa.h", "path_type": "hardlink", "sha256": "640868db97ea78fdedd913345e0a9f85305740822270008ba5dc871cc457dc95", "sha256_in_prefix": "640868db97ea78fdedd913345e0a9f85305740822270008ba5dc871cc457dc95", "size_in_bytes": 4122}, {"_path": "include/arrow/vendored/double-conversion/fixed-dtoa.h", "path_type": "hardlink", "sha256": "1cb9e9c641e32a5766f850620d16c00d89632414986d018fe06cfeb156e24895", "sha256_in_prefix": "1cb9e9c641e32a5766f850620d16c00d89632414986d018fe06cfeb156e24895", "size_in_bytes": 2828}, {"_path": "include/arrow/vendored/double-conversion/ieee.h", "path_type": "hardlink", "sha256": "095280f515d28efe19ca0a831cc885f87da1521dd01d0be9d466580b73008641", "sha256_in_prefix": "095280f515d28efe19ca0a831cc885f87da1521dd01d0be9d466580b73008641", "size_in_bytes": 15281}, {"_path": "include/arrow/vendored/double-conversion/string-to-double.h", "path_type": "hardlink", "sha256": "525e9bfb6474a6351a01634cdca8b8907f77dcbaab5bafd77cfcf8052884daff", "sha256_in_prefix": "525e9bfb6474a6351a01634cdca8b8907f77dcbaab5bafd77cfcf8052884daff", "size_in_bytes": 10906}, {"_path": "include/arrow/vendored/double-conversion/strtod.h", "path_type": "hardlink", "sha256": "eb10919b8eef99c821609ba0e668614d56ec6779b763ab507cc7a1132559371d", "sha256_in_prefix": "eb10919b8eef99c821609ba0e668614d56ec6779b763ab507cc7a1132559371d", "size_in_bytes": 3096}, {"_path": "include/arrow/vendored/double-conversion/utils.h", "path_type": "hardlink", "sha256": "c0545be5c1800623685120a7bca99dbff28831c06d5f53d7f3db4f15fbe06d02", "sha256_in_prefix": "c0545be5c1800623685120a7bca99dbff28831c06d5f53d7f3db4f15fbe06d02", "size_in_bytes": 15614}, {"_path": "include/arrow/vendored/pcg/pcg_extras.hpp", "path_type": "hardlink", "sha256": "144633abc345c4f7dd2722ece2456d4c12e4683ea23bbd48373f442676b14dd7", "sha256_in_prefix": "144633abc345c4f7dd2722ece2456d4c12e4683ea23bbd48373f442676b14dd7", "size_in_bytes": 19784}, {"_path": "include/arrow/vendored/pcg/pcg_random.hpp", "path_type": "hardlink", "sha256": "ed3695de7661730a5fe97c6567a72877519a5b9826fa2527ebbb767e230f35b0", "sha256_in_prefix": "ed3695de7661730a5fe97c6567a72877519a5b9826fa2527ebbb767e230f35b0", "size_in_bytes": 73501}, {"_path": "include/arrow/vendored/pcg/pcg_uint128.hpp", "path_type": "hardlink", "sha256": "afc7b132d1f6d52f298e2cf2659bcff10f2501dc64285db66448aead24c5b733", "sha256_in_prefix": "afc7b132d1f6d52f298e2cf2659bcff10f2501dc64285db66448aead24c5b733", "size_in_bytes": 28411}, {"_path": "include/arrow/vendored/portable-snippets/debug-trap.h", "path_type": "hardlink", "sha256": "f4aa6127d811b434fd0d747d899eda4b6df16b64fcb4b98bb05109320d292c34", "sha256_in_prefix": "f4aa6127d811b434fd0d747d899eda4b6df16b64fcb4b98bb05109320d292c34", "size_in_bytes": 3081}, {"_path": "include/arrow/vendored/portable-snippets/safe-math.h", "path_type": "hardlink", "sha256": "abdc96877e1bb05bb5bd2a8b4ee2379ff7085384e563df0b9357a5c4a1ef64fd", "sha256_in_prefix": "abdc96877e1bb05bb5bd2a8b4ee2379ff7085384e563df0b9357a5c4a1ef64fd", "size_in_bytes": 48167}, {"_path": "include/arrow/vendored/strptime.h", "path_type": "hardlink", "sha256": "ab52198b90afc94a7f3cdcdb438fd72eba00576e15128bc1133d93929c1427d7", "sha256_in_prefix": "ab52198b90afc94a7f3cdcdb438fd72eba00576e15128bc1133d93929c1427d7", "size_in_bytes": 1212}, {"_path": "include/arrow/vendored/xxhash.h", "path_type": "hardlink", "sha256": "314c2dcb3bbbc63931f6605c4bae526837022bbb607aa4208fe29810cc5c1d67", "sha256_in_prefix": "314c2dcb3bbbc63931f6605c4bae526837022bbb607aa4208fe29810cc5c1d67", "size_in_bytes": 844}, {"_path": "include/arrow/vendored/xxhash/xxhash.h", "path_type": "hardlink", "sha256": "be275e9db21a503c37f24683cdb4908f2370a3e35ab96e02c4ea73dc8e399c43", "sha256_in_prefix": "be275e9db21a503c37f24683cdb4908f2370a3e35ab96e02c4ea73dc8e399c43", "size_in_bytes": 253096}, {"_path": "include/arrow/visit_array_inline.h", "path_type": "hardlink", "sha256": "5ee423b8c13c5d9789a7b5bce98b82b2ea15560986d4dba55c0034289926981d", "sha256_in_prefix": "5ee423b8c13c5d9789a7b5bce98b82b2ea15560986d4dba55c0034289926981d", "size_in_bytes": 2446}, {"_path": "include/arrow/visit_data_inline.h", "path_type": "hardlink", "sha256": "e0c91d155b2b8e13324c336b49c42d39857f9f0cea47675d492db26276f22edd", "sha256_in_prefix": "e0c91d155b2b8e13324c336b49c42d39857f9f0cea47675d492db26276f22edd", "size_in_bytes": 12460}, {"_path": "include/arrow/visit_scalar_inline.h", "path_type": "hardlink", "sha256": "2af358d23f2713d82cffcd392d7315dc04e0bf1bd4a96e1478aa57531477acc0", "sha256_in_prefix": "2af358d23f2713d82cffcd392d7315dc04e0bf1bd4a96e1478aa57531477acc0", "size_in_bytes": 2419}, {"_path": "include/arrow/visit_type_inline.h", "path_type": "hardlink", "sha256": "3b4054471d93a30a9d3bad56f43244af970a3f906c56452df01a5cf15ba25de7", "sha256_in_prefix": "3b4054471d93a30a9d3bad56f43244af970a3f906c56452df01a5cf15ba25de7", "size_in_bytes": 4387}, {"_path": "include/arrow/visitor.h", "path_type": "hardlink", "sha256": "34aa2cf7c8f9e2e63db5d5f372d23f9ff9f0151ad734ec1a9f12c3a8364e370e", "sha256_in_prefix": "34aa2cf7c8f9e2e63db5d5f372d23f9ff9f0151ad734ec1a9f12c3a8364e370e", "size_in_bytes": 8690}, {"_path": "include/arrow/visitor_generate.h", "path_type": "hardlink", "sha256": "9f660a656fb9858ec80904b01140592188767a0f59a137c3e785d177d3a5343a", "sha256_in_prefix": "9f660a656fb9858ec80904b01140592188767a0f59a137c3e785d177d3a5343a", "size_in_bytes": 3324}, {"_path": "lib/cmake/Arrow/ArrowConfig.cmake", "path_type": "hardlink", "sha256": "82def66ded2e5cdcf99dc42f2382db2d90f7436559fbdd5e98d80c5b30ae9113", "sha256_in_prefix": "82def66ded2e5cdcf99dc42f2382db2d90f7436559fbdd5e98d80c5b30ae9113", "size_in_bytes": 9932}, {"_path": "lib/cmake/Arrow/ArrowConfigVersion.cmake", "path_type": "hardlink", "sha256": "4eb556774449e5062cea02eaaab6b824572fe8dadcdfb258fc6e518fac98006b", "sha256_in_prefix": "4eb556774449e5062cea02eaaab6b824572fe8dadcdfb258fc6e518fac98006b", "size_in_bytes": 2765}, {"_path": "lib/cmake/Arrow/ArrowOptions.cmake", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/apache-arrow_1751095169775/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "4cb88535780bb8e6b6086fdecc0cdc9d6fe6a9c637d06359ab120e2ad10925af", "sha256_in_prefix": "612c21c57358baad1a57325f0cbbddbc38f803c0625aabc3070de8b1248cd3fc", "size_in_bytes": 9716}, {"_path": "lib/cmake/Arrow/ArrowTargets-release.cmake", "path_type": "hardlink", "sha256": "a0ca403457cf592e5a1a43fb97239003dc81c07ef85a8ecf6f6bf8d07de1702b", "sha256_in_prefix": "a0ca403457cf592e5a1a43fb97239003dc81c07ef85a8ecf6f6bf8d07de1702b", "size_in_bytes": 897}, {"_path": "lib/cmake/Arrow/ArrowTargets.cmake", "path_type": "hardlink", "sha256": "027532335363ce0efbbd34236c95549d54d407c2510a8e213dfd1f39abb17ebc", "sha256_in_prefix": "027532335363ce0efbbd34236c95549d54d407c2510a8e213dfd1f39abb17ebc", "size_in_bytes": 4177}, {"_path": "lib/cmake/Arrow/FindAWSSDKAlt.cmake", "path_type": "hardlink", "sha256": "ffc9a52e248efe94845ce5d722c6eef62663af3d25360d319bcebbf3e9e0656d", "sha256_in_prefix": "ffc9a52e248efe94845ce5d722c6eef62663af3d25360d319bcebbf3e9e0656d", "size_in_bytes": 1874}, {"_path": "lib/cmake/Arrow/FindAzure.cmake", "path_type": "hardlink", "sha256": "d33e6ff3c2655271240b7912628da8600b964e4b91b09d25a46b95baea44e391", "sha256_in_prefix": "d33e6ff3c2655271240b7912628da8600b964e4b91b09d25a46b95baea44e391", "size_in_bytes": 1628}, {"_path": "lib/cmake/Arrow/FindBrotliAlt.cmake", "path_type": "hardlink", "sha256": "65a4f339f9c42cdde86c69aae07974562252e6d720ff5a461159981a70e39ffd", "sha256_in_prefix": "65a4f339f9c42cdde86c69aae07974562252e6d720ff5a461159981a70e39ffd", "size_in_bytes": 6800}, {"_path": "lib/cmake/Arrow/FindOpenSSLAlt.cmake", "path_type": "hardlink", "sha256": "e0d446311233d73dff666764bad25aaf7bcc74e6246d2c5801e3a880394cdbc0", "sha256_in_prefix": "e0d446311233d73dff666764bad25aaf7bcc74e6246d2c5801e3a880394cdbc0", "size_in_bytes": 1846}, {"_path": "lib/cmake/Arrow/FindProtobufAlt.cmake", "path_type": "hardlink", "sha256": "fd9f2c4e2154e0d7a12577b576d8daa62a0f88689a557628f25cce785f3fac5d", "sha256_in_prefix": "fd9f2c4e2154e0d7a12577b576d8daa62a0f88689a557628f25cce785f3fac5d", "size_in_bytes": 2213}, {"_path": "lib/cmake/Arrow/FindSnappyAlt.cmake", "path_type": "hardlink", "sha256": "8975f90162fa4d10739199c01c1cf60638b53ecc71f0f2eaea36c731d259bfca", "sha256_in_prefix": "8975f90162fa4d10739199c01c1cf60638b53ecc71f0f2eaea36c731d259bfca", "size_in_bytes": 3658}, {"_path": "lib/cmake/Arrow/FindabslAlt.cmake", "path_type": "hardlink", "sha256": "81b3d0aaeaed4f37e8b226ad3c61dd9597a2345c5b45efe8ac5982a2c23e32f6", "sha256_in_prefix": "81b3d0aaeaed4f37e8b226ad3c61dd9597a2345c5b45efe8ac5982a2c23e32f6", "size_in_bytes": 1555}, {"_path": "lib/cmake/Arrow/FindglogAlt.cmake", "path_type": "hardlink", "sha256": "77c2328616e58c02b4c2855d5ef58182e5f4b44090951dedefb55816b6f54dcb", "sha256_in_prefix": "77c2328616e58c02b4c2855d5ef58182e5f4b44090951dedefb55816b6f54dcb", "size_in_bytes": 2388}, {"_path": "lib/cmake/Arrow/Findlz4Alt.cmake", "path_type": "hardlink", "sha256": "e5d24bd091f54dad4e959c0fe21293017190c62d89e3aead983d4da2e1dc2701", "sha256_in_prefix": "e5d24bd091f54dad4e959c0fe21293017190c62d89e3aead983d4da2e1dc2701", "size_in_bytes": 3638}, {"_path": "lib/cmake/Arrow/FindorcAlt.cmake", "path_type": "hardlink", "sha256": "ae94c00a20a7054c77428245b49d784a49199f01dd7ccbbb7c5dd37916670c79", "sha256_in_prefix": "ae94c00a20a7054c77428245b49d784a49199f01dd7ccbbb7c5dd37916670c79", "size_in_bytes": 2520}, {"_path": "lib/cmake/Arrow/Findre2Alt.cmake", "path_type": "hardlink", "sha256": "ac825fd03e4f1b690035a8ebf6baa2e9fd86ed52adb3861dacbda96aa968f8c6", "sha256_in_prefix": "ac825fd03e4f1b690035a8ebf6baa2e9fd86ed52adb3861dacbda96aa968f8c6", "size_in_bytes": 3261}, {"_path": "lib/cmake/Arrow/Findutf8proc.cmake", "path_type": "hardlink", "sha256": "ff4e719507ecf9e527f9de2739dfbb66d991c8fa22f34300e1140432554a99e4", "sha256_in_prefix": "ff4e719507ecf9e527f9de2739dfbb66d991c8fa22f34300e1140432554a99e4", "size_in_bytes": 4573}, {"_path": "lib/cmake/Arrow/FindzstdAlt.cmake", "path_type": "hardlink", "sha256": "0dd0901581f591b88832b9941da7c9aed814d11bd19325a21545fef00f47d217", "sha256_in_prefix": "0dd0901581f591b88832b9941da7c9aed814d11bd19325a21545fef00f47d217", "size_in_bytes": 5077}, {"_path": "lib/cmake/Arrow/arrow-config.cmake", "path_type": "hardlink", "sha256": "545a44ed2caa47c3de839d212e5b56c6c08f1d71a62f366056ae03873a0abe86", "sha256_in_prefix": "545a44ed2caa47c3de839d212e5b56c6c08f1d71a62f366056ae03873a0abe86", "size_in_bytes": 1046}, {"_path": "lib/libarrow.2000.0.0.dylib", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/apache-arrow_1751095169775/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "90933efb407dfbab6fe05a6060bd37b28175bc24b3282abe38a8563deb573ff4", "sha256_in_prefix": "f0ed541c7556481578dc41381b90af4d9b9c5b876ed8c8030b828bb94877bda7", "size_in_bytes": 34713712}, {"_path": "lib/libarrow.2000.dylib", "path_type": "softlink", "sha256": "90933efb407dfbab6fe05a6060bd37b28175bc24b3282abe38a8563deb573ff4", "size_in_bytes": 34713712}, {"_path": "lib/libarrow.dylib", "path_type": "softlink", "sha256": "90933efb407dfbab6fe05a6060bd37b28175bc24b3282abe38a8563deb573ff4", "size_in_bytes": 34713712}, {"_path": "lib/pkgconfig/arrow-compute.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/apache-arrow_1751095169775/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "9eec1beb0b912ce762486a71f67c7a8a0926bbdd6e376c8ac429960dddb2fb7b", "sha256_in_prefix": "65472552b2ddc536189a67528493847453e47e71c5dc1f61012ba9046aebdbfd", "size_in_bytes": 1209}, {"_path": "lib/pkgconfig/arrow-csv.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/apache-arrow_1751095169775/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "72b7953b8a614fb08ce3ff1c5e6b2d0ebfbf8a66af06b6bf04a38f06294d874c", "sha256_in_prefix": "3f5d0458aa55fd0ec405fa7ff2692e88bf423e7e8581a5ac8407d5de87b0a83a", "size_in_bytes": 1203}, {"_path": "lib/pkgconfig/arrow-filesystem.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/apache-arrow_1751095169775/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "e9520b2ee8be876a2f96e8202bde60a87b61404d82fb24824ecd8c88af8e9151", "sha256_in_prefix": "70dcc379f8027089d6ec5c5d386b724b44862f2459eee0b4426a149c4d20a54d", "size_in_bytes": 1233}, {"_path": "lib/pkgconfig/arrow-json.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/apache-arrow_1751095169775/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "8c770c1d22f1dbe31fd8487516626759bdb2904efaec045afee4d2d7cb389d57", "sha256_in_prefix": "84a17bc886adf767be7850943449b78e3071cb07ac2b03b170d94c554422e3cb", "size_in_bytes": 1205}, {"_path": "lib/pkgconfig/arrow-orc.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/apache-arrow_1751095169775/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "c01d4ee172d7c08fe60acd70b5ee9088c58802dbc5fdd370ad7653222477a4a5", "sha256_in_prefix": "b9cf1c664c25b605f6495ddd814510db7ab80e6eaaffbda7bdd7cf2f7eb314e3", "size_in_bytes": 1197}, {"_path": "lib/pkgconfig/arrow.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/apache-arrow_1751095169775/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "0e63c80efa4f90f914abc26d80be12503991e202b7aaefc322fb17d0a41f7704", "sha256_in_prefix": "02722b65999058ee2515dca30659263c80826582bab9cbaf058b9a50e99d778f", "size_in_bytes": 1406}, {"_path": "share/arrow/gdb/gdb_arrow.py", "path_type": "hardlink", "sha256": "8ae05d43c81930378a91fd293ff2d1a64d16b82d58b398baaa68c61012a4dc08", "sha256_in_prefix": "8ae05d43c81930378a91fd293ff2d1a64d16b82d58b398baaa68c61012a4dc08", "size_in_bytes": 69944}, {"_path": "share/doc/arrow/LICENSE.txt", "path_type": "hardlink", "sha256": "36f65c663f1c9c910af64c73cb4302667d6513c3f0a617e42bd896d767ff4604", "sha256_in_prefix": "36f65c663f1c9c910af64c73cb4302667d6513c3f0a617e42bd896d767ff4604", "size_in_bytes": 111773}, {"_path": "share/doc/arrow/NOTICE.txt", "path_type": "hardlink", "sha256": "b62ea242642d3a18f1e29b0c1fe082415a6943fe158ee22b48cff3762f354009", "sha256_in_prefix": "b62ea242642d3a18f1e29b0c1fe082415a6943fe158ee22b48cff3762f354009", "size_in_bytes": 3032}, {"_path": "share/doc/arrow/README.md", "path_type": "hardlink", "sha256": "e82b21e5aabf9b41ad0b2689f4a3ab49ce6ecb64e45a0b72a30f5ecbbbdffbdb", "sha256_in_prefix": "e82b21e5aabf9b41ad0b2689f4a3ab49ce6ecb64e45a0b72a30f5ecbbbdffbdb", "size_in_bytes": 1244}, {"_path": "share/gdb/auto-load/replace_this_section_with_absolute_slashed_path_to_CONDA_PREFIX/lib/libarrow.2000.0.0.dylib-gdb.py", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/apache-arrow_1751095169775/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "5f384f7c6fd3922fbcb369b7f7e1952158122bffc95505d9f7498045904cb9d0", "sha256_in_prefix": "093462b4efb4ba95bc337433ebf91bb026cf863cfb77d53c05b5eb7ffb2d1e0e", "size_in_bytes": 1198}], "paths_version": 1}, "requested_spec": "None", "sha256": "e6e1a4289fb6ab6293a784076d5a3ad464eb757a3a9255acfa47e7dc961ce94f", "size": 6408703, "subdir": "osx-64", "timestamp": 1751097590000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libarrow-20.0.0-h7601d43_8_cpu.conda", "version": "20.0.0"}