{"build": "20_osx64_mkl", "build_number": 20, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": ["blas * mkl", "liblapack 3.9.0 20_osx64_mkl", "liblapacke 3.9.0 20_osx64_mkl"], "depends": ["libblas 3.9.0 20_osx64_mkl"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libcblas-3.9.0-20_osx64_mkl", "files": ["lib/libcblas.3.dylib", "lib/libcblas.dylib"], "fn": "libcblas-3.9.0-20_osx64_mkl.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libcblas-3.9.0-20_osx64_mkl", "type": 1}, "md5": "51089a4865eb4aec2bc5c7468bd07f9f", "name": "libc<PERSON>s", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libcblas-3.9.0-20_osx64_mkl.conda", "paths_data": {"paths": [{"_path": "lib/libcblas.3.dylib", "path_type": "softlink", "sha256": "d367ace211c4c67a12f17bf876a379d98e0854cf063714fbfbef3564b05f9794", "size_in_bytes": 18789024}, {"_path": "lib/libcblas.dylib", "path_type": "softlink", "sha256": "d367ace211c4c67a12f17bf876a379d98e0854cf063714fbfbef3564b05f9794", "size_in_bytes": 18789024}], "paths_version": 1}, "requested_spec": "None", "sha256": "a35e3c8f0efee2bee8926cbbf23dcb36c9cfe3100690af3b86f933bab26c4eeb", "size": 14694, "subdir": "osx-64", "timestamp": 1700568672000, "track_features": "blas_mkl", "url": "https://conda.anaconda.org/conda-forge/osx-64/libcblas-3.9.0-20_osx64_mkl.conda", "version": "3.9.0"}