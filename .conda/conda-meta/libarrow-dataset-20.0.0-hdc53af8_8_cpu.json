{"build": "hdc53af8_8_cpu", "build_number": 8, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.14", "libarrow 20.0.0 h7601d43_8_cpu", "libarrow-acero 20.0.0 hdc53af8_8_cpu", "libcxx >=18", "libparquet 20.0.0 h283e888_8_cpu"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libarrow-dataset-20.0.0-hdc53af8_8_cpu", "files": ["lib/cmake/ArrowDataset/ArrowDatasetConfig.cmake", "lib/cmake/ArrowDataset/ArrowDatasetConfigVersion.cmake", "lib/cmake/ArrowDataset/ArrowDatasetTargets-release.cmake", "lib/cmake/ArrowDataset/ArrowDatasetTargets.cmake", "lib/libarrow_dataset.2000.0.0.dylib", "lib/libarrow_dataset.2000.dylib", "lib/libarrow_dataset.dylib", "lib/pkgconfig/arrow-dataset.pc"], "fn": "libarrow-dataset-20.0.0-hdc53af8_8_cpu.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libarrow-dataset-20.0.0-hdc53af8_8_cpu", "type": 1}, "md5": "24526e090dbcaab033c494c33c3cb8a6", "name": "libarrow-dataset", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libarrow-dataset-20.0.0-hdc53af8_8_cpu.conda", "paths_data": {"paths": [{"_path": "lib/cmake/ArrowDataset/ArrowDatasetConfig.cmake", "path_type": "hardlink", "sha256": "33afca751758bda3bd6bbd7bb12927452d7e77670e3965bd11e1fc356cbf16bf", "sha256_in_prefix": "33afca751758bda3bd6bbd7bb12927452d7e77670e3965bd11e1fc356cbf16bf", "size_in_bytes": 2485}, {"_path": "lib/cmake/ArrowDataset/ArrowDatasetConfigVersion.cmake", "path_type": "hardlink", "sha256": "4eb556774449e5062cea02eaaab6b824572fe8dadcdfb258fc6e518fac98006b", "sha256_in_prefix": "4eb556774449e5062cea02eaaab6b824572fe8dadcdfb258fc6e518fac98006b", "size_in_bytes": 2765}, {"_path": "lib/cmake/ArrowDataset/ArrowDatasetTargets-release.cmake", "path_type": "hardlink", "sha256": "f15598e1d1916613280de86ea5927de06e83194fd04d2b654d61f3ae5dc8d9df", "sha256_in_prefix": "f15598e1d1916613280de86ea5927de06e83194fd04d2b654d61f3ae5dc8d9df", "size_in_bytes": 1266}, {"_path": "lib/cmake/ArrowDataset/ArrowDatasetTargets.cmake", "path_type": "hardlink", "sha256": "5305b21cb2f0b3478821c0ca19436f0fe3b2e51157955be80d6941297698419b", "sha256_in_prefix": "5305b21cb2f0b3478821c0ca19436f0fe3b2e51157955be80d6941297698419b", "size_in_bytes": 4290}, {"_path": "lib/libarrow_dataset.2000.0.0.dylib", "path_type": "hardlink", "sha256": "aaee76d0a54058a2144e89d606bb7107312f67ca07252c447c901aa60ca6e17c", "sha256_in_prefix": "aaee76d0a54058a2144e89d606bb7107312f67ca07252c447c901aa60ca6e17c", "size_in_bytes": 2769288}, {"_path": "lib/libarrow_dataset.2000.dylib", "path_type": "softlink", "sha256": "aaee76d0a54058a2144e89d606bb7107312f67ca07252c447c901aa60ca6e17c", "size_in_bytes": 2769288}, {"_path": "lib/libarrow_dataset.dylib", "path_type": "softlink", "sha256": "aaee76d0a54058a2144e89d606bb7107312f67ca07252c447c901aa60ca6e17c", "size_in_bytes": 2769288}, {"_path": "lib/pkgconfig/arrow-dataset.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/apache-arrow_1751095169775/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "070e865730c85231317a39ad11320f736847e619e275a1831317f4f6a63aaf0a", "sha256_in_prefix": "bcfa2cbc4fa8e5a57e50fa11d68790e57413f4a10e3a887b5c2ae1e2fd62340f", "size_in_bytes": 1360}], "paths_version": 1}, "requested_spec": "None", "sha256": "d08fcb73fa38891e855a0d654b2e341c00942fbab318bf9aab09eed24b4a6a30", "size": 530234, "subdir": "osx-64", "timestamp": 1751098041000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libarrow-dataset-20.0.0-hdc53af8_8_cpu.conda", "version": "20.0.0"}