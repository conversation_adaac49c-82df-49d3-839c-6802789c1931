{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["backoff >=1.10.0", "distro >=1.5.0", "monotonic >=1.5.0", "python >=3.9", "python-dateutil >=2.1.0", "requests >=2.7.0,<3.0.0", "six >=1.5.0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/posthog-5.4.0-pyhd8ed1ab_0", "files": ["lib/python3.11/site-packages/posthog-5.4.0.dist-info/INSTALLER", "lib/python3.11/site-packages/posthog-5.4.0.dist-info/METADATA", "lib/python3.11/site-packages/posthog-5.4.0.dist-info/RECORD", "lib/python3.11/site-packages/posthog-5.4.0.dist-info/REQUESTED", "lib/python3.11/site-packages/posthog-5.4.0.dist-info/WHEEL", "lib/python3.11/site-packages/posthog-5.4.0.dist-info/direct_url.json", "lib/python3.11/site-packages/posthog-5.4.0.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/posthog-5.4.0.dist-info/top_level.txt", "lib/python3.11/site-packages/posthog/__init__.py", "lib/python3.11/site-packages/posthog/ai/__init__.py", "lib/python3.11/site-packages/posthog/ai/anthropic/__init__.py", "lib/python3.11/site-packages/posthog/ai/anthropic/anthropic.py", "lib/python3.11/site-packages/posthog/ai/anthropic/anthropic_async.py", "lib/python3.11/site-packages/posthog/ai/anthropic/anthropic_providers.py", "lib/python3.11/site-packages/posthog/ai/gemini/__init__.py", "lib/python3.11/site-packages/posthog/ai/gemini/gemini.py", "lib/python3.11/site-packages/posthog/ai/langchain/__init__.py", "lib/python3.11/site-packages/posthog/ai/langchain/callbacks.py", "lib/python3.11/site-packages/posthog/ai/openai/__init__.py", "lib/python3.11/site-packages/posthog/ai/openai/openai.py", "lib/python3.11/site-packages/posthog/ai/openai/openai_async.py", "lib/python3.11/site-packages/posthog/ai/openai/openai_providers.py", "lib/python3.11/site-packages/posthog/ai/utils.py", "lib/python3.11/site-packages/posthog/client.py", "lib/python3.11/site-packages/posthog/consumer.py", "lib/python3.11/site-packages/posthog/exception_capture.py", "lib/python3.11/site-packages/posthog/exception_integrations/__init__.py", "lib/python3.11/site-packages/posthog/exception_integrations/django.py", "lib/python3.11/site-packages/posthog/exception_utils.py", "lib/python3.11/site-packages/posthog/feature_flags.py", "lib/python3.11/site-packages/posthog/integrations/__init__.py", "lib/python3.11/site-packages/posthog/integrations/django.py", "lib/python3.11/site-packages/posthog/poller.py", "lib/python3.11/site-packages/posthog/py.typed", "lib/python3.11/site-packages/posthog/request.py", "lib/python3.11/site-packages/posthog/scopes.py", "lib/python3.11/site-packages/posthog/test/__init__.py", "lib/python3.11/site-packages/posthog/test/test_before_send.py", "lib/python3.11/site-packages/posthog/test/test_client.py", "lib/python3.11/site-packages/posthog/test/test_consumer.py", "lib/python3.11/site-packages/posthog/test/test_exception_capture.py", "lib/python3.11/site-packages/posthog/test/test_feature_flag.py", "lib/python3.11/site-packages/posthog/test/test_feature_flag_result.py", "lib/python3.11/site-packages/posthog/test/test_feature_flags.py", "lib/python3.11/site-packages/posthog/test/test_module.py", "lib/python3.11/site-packages/posthog/test/test_request.py", "lib/python3.11/site-packages/posthog/test/test_scopes.py", "lib/python3.11/site-packages/posthog/test/test_size_limited_dict.py", "lib/python3.11/site-packages/posthog/test/test_types.py", "lib/python3.11/site-packages/posthog/test/test_utils.py", "lib/python3.11/site-packages/posthog/types.py", "lib/python3.11/site-packages/posthog/utils.py", "lib/python3.11/site-packages/posthog/version.py", "lib/python3.11/site-packages/posthog/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/posthog/ai/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/posthog/ai/anthropic/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/posthog/ai/anthropic/__pycache__/anthropic.cpython-311.pyc", "lib/python3.11/site-packages/posthog/ai/anthropic/__pycache__/anthropic_async.cpython-311.pyc", "lib/python3.11/site-packages/posthog/ai/anthropic/__pycache__/anthropic_providers.cpython-311.pyc", "lib/python3.11/site-packages/posthog/ai/gemini/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/posthog/ai/gemini/__pycache__/gemini.cpython-311.pyc", "lib/python3.11/site-packages/posthog/ai/langchain/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/posthog/ai/langchain/__pycache__/callbacks.cpython-311.pyc", "lib/python3.11/site-packages/posthog/ai/openai/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/posthog/ai/openai/__pycache__/openai.cpython-311.pyc", "lib/python3.11/site-packages/posthog/ai/openai/__pycache__/openai_async.cpython-311.pyc", "lib/python3.11/site-packages/posthog/ai/openai/__pycache__/openai_providers.cpython-311.pyc", "lib/python3.11/site-packages/posthog/ai/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/posthog/__pycache__/client.cpython-311.pyc", "lib/python3.11/site-packages/posthog/__pycache__/consumer.cpython-311.pyc", "lib/python3.11/site-packages/posthog/__pycache__/exception_capture.cpython-311.pyc", "lib/python3.11/site-packages/posthog/exception_integrations/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/posthog/exception_integrations/__pycache__/django.cpython-311.pyc", "lib/python3.11/site-packages/posthog/__pycache__/exception_utils.cpython-311.pyc", "lib/python3.11/site-packages/posthog/__pycache__/feature_flags.cpython-311.pyc", "lib/python3.11/site-packages/posthog/integrations/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/posthog/integrations/__pycache__/django.cpython-311.pyc", "lib/python3.11/site-packages/posthog/__pycache__/poller.cpython-311.pyc", "lib/python3.11/site-packages/posthog/__pycache__/request.cpython-311.pyc", "lib/python3.11/site-packages/posthog/__pycache__/scopes.cpython-311.pyc", "lib/python3.11/site-packages/posthog/test/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/posthog/test/__pycache__/test_before_send.cpython-311.pyc", "lib/python3.11/site-packages/posthog/test/__pycache__/test_client.cpython-311.pyc", "lib/python3.11/site-packages/posthog/test/__pycache__/test_consumer.cpython-311.pyc", "lib/python3.11/site-packages/posthog/test/__pycache__/test_exception_capture.cpython-311.pyc", "lib/python3.11/site-packages/posthog/test/__pycache__/test_feature_flag.cpython-311.pyc", "lib/python3.11/site-packages/posthog/test/__pycache__/test_feature_flag_result.cpython-311.pyc", "lib/python3.11/site-packages/posthog/test/__pycache__/test_feature_flags.cpython-311.pyc", "lib/python3.11/site-packages/posthog/test/__pycache__/test_module.cpython-311.pyc", "lib/python3.11/site-packages/posthog/test/__pycache__/test_request.cpython-311.pyc", "lib/python3.11/site-packages/posthog/test/__pycache__/test_scopes.cpython-311.pyc", "lib/python3.11/site-packages/posthog/test/__pycache__/test_size_limited_dict.cpython-311.pyc", "lib/python3.11/site-packages/posthog/test/__pycache__/test_types.cpython-311.pyc", "lib/python3.11/site-packages/posthog/test/__pycache__/test_utils.cpython-311.pyc", "lib/python3.11/site-packages/posthog/__pycache__/types.cpython-311.pyc", "lib/python3.11/site-packages/posthog/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/posthog/__pycache__/version.cpython-311.pyc"], "fn": "posthog-5.4.0-pyhd8ed1ab_0.conda", "license": "MIT", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/posthog-5.4.0-pyhd8ed1ab_0", "type": 1}, "md5": "f7a928dab31db9e91bafb6cffabd780f", "name": "posthog", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/posthog-5.4.0-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/posthog-5.4.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/posthog-5.4.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "070b00cd32c291e863336dec62ce81c7299eb9daa1f10720f38b32d29870fa71", "sha256_in_prefix": "070b00cd32c291e863336dec62ce81c7299eb9daa1f10720f38b32d29870fa71", "size_in_bytes": 5683}, {"_path": "site-packages/posthog-5.4.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "f00d6317b051b300dc9ec311d8d14ff1089d0e5c2cd17f6ed9a2aedefb3529fe", "sha256_in_prefix": "f00d6317b051b300dc9ec311d8d14ff1089d0e5c2cd17f6ed9a2aedefb3529fe", "size_in_bytes": 7008}, {"_path": "site-packages/posthog-5.4.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/posthog-5.4.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "ff309ddcdd65ebd02bc724dbf2bcc4a0ff53a5b61792a44548d383e4ebb19d3b", "sha256_in_prefix": "ff309ddcdd65ebd02bc724dbf2bcc4a0ff53a5b61792a44548d383e4ebb19d3b", "size_in_bytes": 91}, {"_path": "site-packages/posthog-5.4.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "8204c4febd64cec7513aae64c9f42a125141ad4be66cd25ebb49fc83d3d42fb1", "sha256_in_prefix": "8204c4febd64cec7513aae64c9f42a125141ad4be66cd25ebb49fc83d3d42fb1", "size_in_bytes": 103}, {"_path": "site-packages/posthog-5.4.0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "c067fd241a2d0e44b28058f8de6e3da222a5167a4c9e7f7b91e899285fb8d2f1", "sha256_in_prefix": "c067fd241a2d0e44b28058f8de6e3da222a5167a4c9e7f7b91e899285fb8d2f1", "size_in_bytes": 2450}, {"_path": "site-packages/posthog-5.4.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "ec504bb118c850754a42c5c8868cee23793e9ae9f5b5aa69f2264ef449943c4c", "sha256_in_prefix": "ec504bb118c850754a42c5c8868cee23793e9ae9f5b5aa69f2264ef449943c4c", "size_in_bytes": 8}, {"_path": "site-packages/posthog/__init__.py", "path_type": "hardlink", "sha256": "f78a5e6e93f908b47ce664a2d5206e2ae7dfac997fe9f176934d50e78fdd0540", "sha256_in_prefix": "f78a5e6e93f908b47ce664a2d5206e2ae7dfac997fe9f176934d50e78fdd0540", "size_in_bytes": 19635}, {"_path": "site-packages/posthog/ai/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/posthog/ai/anthropic/__init__.py", "path_type": "hardlink", "sha256": "7c58433a24734d7cc6425827ad10cbfb8c8a0bc118225f0d5b86b640d47ac515", "sha256_in_prefix": "7c58433a24734d7cc6425827ad10cbfb8c8a0bc118225f0d5b86b640d47ac515", "size_in_bytes": 368}, {"_path": "site-packages/posthog/ai/anthropic/anthropic.py", "path_type": "hardlink", "sha256": "2a40cd2b5a9f4651a976d29ebb0da3e950daf37b8c4fc410bda4c7e08b48b427", "sha256_in_prefix": "2a40cd2b5a9f4651a976d29ebb0da3e950daf37b8c4fc410bda4c7e08b48b427", "size_in_bytes": 7310}, {"_path": "site-packages/posthog/ai/anthropic/anthropic_async.py", "path_type": "hardlink", "sha256": "899d784cc05e7000f892ec0803d1d21f0f5bcf9b92b4abd902d7057b2527077b", "sha256_in_prefix": "899d784cc05e7000f892ec0803d1d21f0f5bcf9b92b4abd902d7057b2527077b", "size_in_bytes": 7430}, {"_path": "site-packages/posthog/ai/anthropic/anthropic_providers.py", "path_type": "hardlink", "sha256": "b38bfb9d239c195d18297fd693babad36990ae3d6cefa807d5456ad567095b9e", "sha256_in_prefix": "b38bfb9d239c195d18297fd693babad36990ae3d6cefa807d5456ad567095b9e", "size_in_bytes": 1936}, {"_path": "site-packages/posthog/ai/gemini/__init__.py", "path_type": "hardlink", "sha256": "6cc3419c9e8d3bf3c2402c26c4a222c3869d16e110d3a84514100bb7e6835bed", "sha256_in_prefix": "6cc3419c9e8d3bf3c2402c26c4a222c3869d16e110d3a84514100bb7e6835bed", "size_in_bytes": 174}, {"_path": "site-packages/posthog/ai/gemini/gemini.py", "path_type": "hardlink", "sha256": "289f6b83999f4c3460be61b0d9b3689a94f08cb6463cbfe2f0e685361355ee83", "sha256_in_prefix": "289f6b83999f4c3460be61b0d9b3689a94f08cb6463cbfe2f0e685361355ee83", "size_in_bytes": 13134}, {"_path": "site-packages/posthog/ai/langchain/__init__.py", "path_type": "hardlink", "sha256": "f42a80c0bca74c68f7012011f340b73e675376b6069aef7db73d092fe1cf1602", "sha256_in_prefix": "f42a80c0bca74c68f7012011f340b73e675376b6069aef7db73d092fe1cf1602", "size_in_bytes": 70}, {"_path": "site-packages/posthog/ai/langchain/callbacks.py", "path_type": "hardlink", "sha256": "26f4e318a5e49b8b2e46f6074c5d68ab8d88eb710f13939682e19f82b80264c3", "sha256_in_prefix": "26f4e318a5e49b8b2e46f6074c5d68ab8d88eb710f13939682e19f82b80264c3", "size_in_bytes": 28835}, {"_path": "site-packages/posthog/ai/openai/__init__.py", "path_type": "hardlink", "sha256": "fdf959c64c9a0d999ef61c49b18df5b0c96ae273f576d73be4798d823fb6d4a8", "sha256_in_prefix": "fdf959c64c9a0d999ef61c49b18df5b0c96ae273f576d73be4798d823fb6d4a8", "size_in_bytes": 197}, {"_path": "site-packages/posthog/ai/openai/openai.py", "path_type": "hardlink", "sha256": "5a51a1e31d2ca667fe719f3547eea5c480966eafae956f3a21e795114d6adbd9", "sha256_in_prefix": "5a51a1e31d2ca667fe719f3547eea5c480966eafae956f3a21e795114d6adbd9", "size_in_bytes": 23376}, {"_path": "site-packages/posthog/ai/openai/openai_async.py", "path_type": "hardlink", "sha256": "21f589ae2f759e30fadd6876e831300de1bce03f46263f237a449691c93aea51", "sha256_in_prefix": "21f589ae2f759e30fadd6876e831300de1bce03f46263f237a449691c93aea51", "size_in_bytes": 23751}, {"_path": "site-packages/posthog/ai/openai/openai_providers.py", "path_type": "hardlink", "sha256": "fbd1dcbbbf6c2e6f426c0fdfa6645d0f7041d1c14c72da39333b874afb7aaa04", "sha256_in_prefix": "fbd1dcbbbf6c2e6f426c0fdfa6645d0f7041d1c14c72da39333b874afb7aaa04", "size_in_bytes": 3830}, {"_path": "site-packages/posthog/ai/utils.py", "path_type": "hardlink", "sha256": "47a80ce9c10eb4496aa9d8b66a7c126a8cf80f2593e780c1955d036a9c3d1ae6", "sha256_in_prefix": "47a80ce9c10eb4496aa9d8b66a7c126a8cf80f2593e780c1955d036a9c3d1ae6", "size_in_bytes": 19520}, {"_path": "site-packages/posthog/client.py", "path_type": "hardlink", "sha256": "151be6a3c9532c4858f1728ddad88bff66cc6cb0ec3a06e369835130c08aa992", "sha256_in_prefix": "151be6a3c9532c4858f1728ddad88bff66cc6cb0ec3a06e369835130c08aa992", "size_in_bytes": 48957}, {"_path": "site-packages/posthog/consumer.py", "path_type": "hardlink", "sha256": "7ddb5e319f9d78918ca5a4261f2ce7c3f730406d95be5775b6637d2d49193eb6", "sha256_in_prefix": "7ddb5e319f9d78918ca5a4261f2ce7c3f730406d95be5775b6637d2d49193eb6", "size_in_bytes": 4608}, {"_path": "site-packages/posthog/exception_capture.py", "path_type": "hardlink", "sha256": "6b39e076d92f58c9155be518dcaa597f55a6911ac26b1717304c728bd733508b", "sha256_in_prefix": "6b39e076d92f58c9155be518dcaa597f55a6911ac26b1717304c728bd733508b", "size_in_bytes": 2706}, {"_path": "site-packages/posthog/exception_integrations/__init__.py", "path_type": "hardlink", "sha256": "5dcae1737ec45dcd2649f914845ce0971d270af1a2bd9b68841a8167655d22c5", "sha256_in_prefix": "5dcae1737ec45dcd2649f914845ce0971d270af1a2bd9b68841a8167655d22c5", "size_in_bytes": 187}, {"_path": "site-packages/posthog/exception_integrations/django.py", "path_type": "hardlink", "sha256": "c72d664eef132ba97296d7a9f8785b876dd84460706753aa1255dccb2be798c6", "sha256_in_prefix": "c72d664eef132ba97296d7a9f8785b876dd84460706753aa1255dccb2be798c6", "size_in_bytes": 3699}, {"_path": "site-packages/posthog/exception_utils.py", "path_type": "hardlink", "sha256": "4fbbe1f3c5563b5816112e55904f2e47bcca39ecfa2810be9110e748f40a0340", "sha256_in_prefix": "4fbbe1f3c5563b5816112e55904f2e47bcca39ecfa2810be9110e748f40a0340", "size_in_bytes": 29680}, {"_path": "site-packages/posthog/feature_flags.py", "path_type": "hardlink", "sha256": "af270455a3df68da80cfd08c3aa7cc0c70d271d6a6444e2a742a20bd42534ea5", "sha256_in_prefix": "af270455a3df68da80cfd08c3aa7cc0c70d271d6a6444e2a742a20bd42534ea5", "size_in_bytes": 13625}, {"_path": "site-packages/posthog/integrations/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/posthog/integrations/django.py", "path_type": "hardlink", "sha256": "c5b41f203a6d0d5512cbc104a54ca4e715dbee1977fad0cf8e136f2691dba73d", "sha256_in_prefix": "c5b41f203a6d0d5512cbc104a54ca4e715dbee1977fad0cf8e136f2691dba73d", "size_in_bytes": 4925}, {"_path": "site-packages/posthog/poller.py", "path_type": "hardlink", "sha256": "8c1cf9adf1ff927fdb073ef0081e3a169beca38b6dc78bb3a88656bd70421664", "sha256_in_prefix": "8c1cf9adf1ff927fdb073ef0081e3a169beca38b6dc78bb3a88656bd70421664", "size_in_bytes": 595}, {"_path": "site-packages/posthog/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/posthog/request.py", "path_type": "hardlink", "sha256": "66ca9accefffc4fa7617acd3d456afc2a0873f40f28bf2e5c1f4121e4a279a18", "sha256_in_prefix": "66ca9accefffc4fa7617acd3d456afc2a0873f40f28bf2e5c1f4121e4a279a18", "size_in_bytes": 6106}, {"_path": "site-packages/posthog/scopes.py", "path_type": "hardlink", "sha256": "8e8e1724959567d00ac97d050f04ab6e1cb93ab4d712b05eed0d58a62211efd1", "sha256_in_prefix": "8e8e1724959567d00ac97d050f04ab6e1cb93ab4d712b05eed0d58a62211efd1", "size_in_bytes": 8185}, {"_path": "site-packages/posthog/test/__init__.py", "path_type": "hardlink", "sha256": "55880ceb13db25bbd2fb184870388146cd0c142f55fe34fae6e35e7ab0b3feb3", "sha256_in_prefix": "55880ceb13db25bbd2fb184870388146cd0c142f55fe34fae6e35e7ab0b3feb3", "size_in_bytes": 299}, {"_path": "site-packages/posthog/test/test_before_send.py", "path_type": "hardlink", "sha256": "1954b167b9a77bf9c478882d5029a6c6b67e4d8a05c18ca300ffe4d7de5b9bb1", "sha256_in_prefix": "1954b167b9a77bf9c478882d5029a6c6b67e4d8a05c18ca300ffe4d7de5b9bb1", "size_in_bytes": 5941}, {"_path": "site-packages/posthog/test/test_client.py", "path_type": "hardlink", "sha256": "a49fc824b77d75273bdcc63f45883b228a82a2e82081ab430afd2e4eacfdd169", "sha256_in_prefix": "a49fc824b77d75273bdcc63f45883b228a82a2e82081ab430afd2e4eacfdd169", "size_in_bytes": 64635}, {"_path": "site-packages/posthog/test/test_consumer.py", "path_type": "hardlink", "sha256": "1d10d7487d083e909fa39c87b36de7fb45731721928d604a2c46bc5cdb54dff6", "sha256_in_prefix": "1d10d7487d083e909fa39c87b36de7fb45731721928d604a2c46bc5cdb54dff6", "size_in_bytes": 7080}, {"_path": "site-packages/posthog/test/test_exception_capture.py", "path_type": "hardlink", "sha256": "e893f33bafebbf9248a6a0d5117f5c9d69fdf3a6a36dc2af17328d4d6bc75196", "sha256_in_prefix": "e893f33bafebbf9248a6a0d5117f5c9d69fdf3a6a36dc2af17328d4d6bc75196", "size_in_bytes": 2130}, {"_path": "site-packages/posthog/test/test_feature_flag.py", "path_type": "hardlink", "sha256": "c8830992846d749afdd58e916f43cf969656048477f784e05a171c9e57febd81", "sha256_in_prefix": "c8830992846d749afdd58e916f43cf969656048477f784e05a171c9e57febd81", "size_in_bytes": 6815}, {"_path": "site-packages/posthog/test/test_feature_flag_result.py", "path_type": "hardlink", "sha256": "fb531fe24390f15e4fae47584a3fdad9454bc23bfff41a19ad052ecff4e9b291", "sha256_in_prefix": "fb531fe24390f15e4fae47584a3fdad9454bc23bfff41a19ad052ecff4e9b291", "size_in_bytes": 15733}, {"_path": "site-packages/posthog/test/test_feature_flags.py", "path_type": "hardlink", "sha256": "09b987ef0802b2697596837c6f5a87c4c0c35356ec86d449a53588898bfba81e", "sha256_in_prefix": "09b987ef0802b2697596837c6f5a87c4c0c35356ec86d449a53588898bfba81e", "size_in_bytes": 168693}, {"_path": "site-packages/posthog/test/test_module.py", "path_type": "hardlink", "sha256": "5178176bd49fb1cf68173e1b63fb4e755368f8b98d2f07ded3901571b0010d7f", "sha256_in_prefix": "5178176bd49fb1cf68173e1b63fb4e755368f8b98d2f07ded3901571b0010d7f", "size_in_bytes": 1405}, {"_path": "site-packages/posthog/test/test_request.py", "path_type": "hardlink", "sha256": "975f5657265073822a9a1fdba670053a3e27191a432b588efa8e5a24341f15da", "sha256_in_prefix": "975f5657265073822a9a1fdba670053a3e27191a432b588efa8e5a24341f15da", "size_in_bytes": 4449}, {"_path": "site-packages/posthog/test/test_scopes.py", "path_type": "hardlink", "sha256": "9d2b4701a220fd1440cdbfb801b3ec06bcc957c5134815f4cdc0e4ffacb862d5", "sha256_in_prefix": "9d2b4701a220fd1440cdbfb801b3ec06bcc957c5134815f4cdc0e4ffacb862d5", "size_in_bytes": 7317}, {"_path": "site-packages/posthog/test/test_size_limited_dict.py", "path_type": "hardlink", "sha256": "5a89bb064ce91e6bac1e2959cb4495dcf373870eee72e420aabc7cea37fc7aea", "sha256_in_prefix": "5a89bb064ce91e6bac1e2959cb4495dcf373870eee72e420aabc7cea37fc7aea", "size_in_bytes": 765}, {"_path": "site-packages/posthog/test/test_types.py", "path_type": "hardlink", "sha256": "72c2ee062cfa44c577e9ca60f4b548a2be0a86ae8c7e38c6c585e8771e55b6d6", "sha256_in_prefix": "72c2ee062cfa44c577e9ca60f4b548a2be0a86ae8c7e38c6c585e8771e55b6d6", "size_in_bytes": 7586}, {"_path": "site-packages/posthog/test/test_utils.py", "path_type": "hardlink", "sha256": "6ee1bdf9836c738168fe8ecf7e7e0ff3926d1e5ad79f71fd73e160d9191c6578", "sha256_in_prefix": "6ee1bdf9836c738168fe8ecf7e7e0ff3926d1e5ad79f71fd73e160d9191c6578", "size_in_bytes": 5419}, {"_path": "site-packages/posthog/types.py", "path_type": "hardlink", "sha256": "20dc5604e110734c603dc6a9e8575035253bcee40198a4a161a1b3cae1caaa5f", "sha256_in_prefix": "20dc5604e110734c603dc6a9e8575035253bcee40198a4a161a1b3cae1caaa5f", "size_in_bytes": 9128}, {"_path": "site-packages/posthog/utils.py", "path_type": "hardlink", "sha256": "e2ac3ee4f3185574baeb7ebba252d89fc22a18d29cc1f7afbf99fbc353238764", "sha256_in_prefix": "e2ac3ee4f3185574baeb7ebba252d89fc22a18d29cc1f7afbf99fbc353238764", "size_in_bytes": 5587}, {"_path": "site-packages/posthog/version.py", "path_type": "hardlink", "sha256": "23ce493a8c14a8bac2e2c0194fcd249cbba6fdf14b83e30e9421f77d42fb0281", "sha256_in_prefix": "23ce493a8c14a8bac2e2c0194fcd249cbba6fdf14b83e30e9421f77d42fb0281", "size_in_bytes": 87}, {"_path": "lib/python3.11/site-packages/posthog/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/ai/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/ai/anthropic/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/ai/anthropic/__pycache__/anthropic.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/ai/anthropic/__pycache__/anthropic_async.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/ai/anthropic/__pycache__/anthropic_providers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/ai/gemini/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/ai/gemini/__pycache__/gemini.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/ai/langchain/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/ai/langchain/__pycache__/callbacks.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/ai/openai/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/ai/openai/__pycache__/openai.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/ai/openai/__pycache__/openai_async.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/ai/openai/__pycache__/openai_providers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/ai/__pycache__/utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/__pycache__/client.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/__pycache__/consumer.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/__pycache__/exception_capture.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/exception_integrations/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/exception_integrations/__pycache__/django.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/__pycache__/exception_utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/__pycache__/feature_flags.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/integrations/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/integrations/__pycache__/django.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/__pycache__/poller.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/__pycache__/request.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/__pycache__/scopes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/test/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/test/__pycache__/test_before_send.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/test/__pycache__/test_client.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/test/__pycache__/test_consumer.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/test/__pycache__/test_exception_capture.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/test/__pycache__/test_feature_flag.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/test/__pycache__/test_feature_flag_result.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/test/__pycache__/test_feature_flags.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/test/__pycache__/test_module.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/test/__pycache__/test_request.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/test/__pycache__/test_scopes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/test/__pycache__/test_size_limited_dict.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/test/__pycache__/test_types.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/test/__pycache__/test_utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/__pycache__/types.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/__pycache__/utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/posthog/__pycache__/version.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "786708d2ea18a95ae659a8ba2ba8853fee8d34e2275bbac45c1f17eb7625fe7b", "size": 81932, "subdir": "noarch", "timestamp": 1750502296000, "url": "https://conda.anaconda.org/conda-forge/noarch/posthog-5.4.0-pyhd8ed1ab_0.conda", "version": "5.4.0"}