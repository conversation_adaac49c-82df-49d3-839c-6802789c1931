{"build": "h6912278_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": ["freetype >=2.14.1"], "depends": ["__osx >=10.13", "libpng >=1.6.50,<1.7.0a0", "libzlib >=1.3.1,<2.0a0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libfreetype6-2.14.1-h6912278_0", "files": ["lib/libfreetype.6.dylib"], "fn": "libfreetype6-2.14.1-h6912278_0.conda", "license": "GPL-2.0-only OR FTL", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libfreetype6-2.14.1-h6912278_0", "type": 1}, "md5": "dfbdc8fd781dc3111541e4234c19fdbd", "name": "libfreetype6", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libfreetype6-2.14.1-h6912278_0.conda", "paths_data": {"paths": [{"_path": "lib/libfreetype.6.dylib", "path_type": "hardlink", "sha256": "78a0567d91897991bde3f944f2e4bba5df7196b716b71d91992dd1213560e79a", "sha256_in_prefix": "78a0567d91897991bde3f944f2e4bba5df7196b716b71d91992dd1213560e79a", "size_in_bytes": 797992}], "paths_version": 1}, "requested_spec": "None", "sha256": "f5f28092e368efc773bcd1c381d123f8b211528385a9353e36f8808d00d11655", "size": 374993, "subdir": "osx-64", "timestamp": 1757945949000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libfreetype6-2.14.1-h6912278_0.conda", "version": "2.14.1"}