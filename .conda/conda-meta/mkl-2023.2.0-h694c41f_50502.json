{"build": "h694c41f_50502", "build_number": 50502, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["_openmp_mutex * *_kmp_*", "_openmp_mutex >=4.5", "tbb 2021.*"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/mkl-2023.2.0-h694c41f_50502", "files": ["lib/cmake/mkl/MKLConfig.cmake", "lib/cmake/mkl/MKLConfigVersion.cmake", "lib/libmkl_avx.2.dylib", "lib/libmkl_avx2.2.dylib", "lib/libmkl_avx512.2.dylib", "lib/libmkl_blacs_mpich_ilp64.2.dylib", "lib/libmkl_blacs_mpich_ilp64.dylib", "lib/libmkl_blacs_mpich_lp64.2.dylib", "lib/libmkl_blacs_mpich_lp64.dylib", "lib/libmkl_cdft_core.2.dylib", "lib/libmkl_cdft_core.dylib", "lib/libmkl_core.2.dylib", "lib/libmkl_core.dylib", "lib/libmkl_intel_ilp64.2.dylib", "lib/libmkl_intel_ilp64.dylib", "lib/libmkl_intel_lp64.2.dylib", "lib/libmkl_intel_lp64.dylib", "lib/libmkl_intel_thread.2.dylib", "lib/libmkl_intel_thread.dylib", "lib/libmkl_mc3.2.dylib", "lib/libmkl_rt.2.dylib", "lib/libmkl_rt.dylib", "lib/libmkl_scalapack_ilp64.2.dylib", "lib/libmkl_scalapack_ilp64.dylib", "lib/libmkl_scalapack_lp64.2.dylib", "lib/libmkl_scalapack_lp64.dylib", "lib/libmkl_sequential.2.dylib", "lib/libmkl_sequential.dylib", "lib/libmkl_tbb_thread.2.dylib", "lib/libmkl_tbb_thread.dylib", "lib/libmkl_vml_avx.2.dylib", "lib/libmkl_vml_avx2.2.dylib", "lib/libmkl_vml_avx512.2.dylib", "lib/libmkl_vml_mc3.2.dylib", "lib/mkl_msg.cat", "lib/pkgconfig/mkl-dynamic-ilp64-iomp.pc", "lib/pkgconfig/mkl-dynamic-ilp64-seq.pc", "lib/pkgconfig/mkl-dynamic-ilp64-tbb.pc", "lib/pkgconfig/mkl-dynamic-lp64-iomp.pc", "lib/pkgconfig/mkl-dynamic-lp64-seq.pc", "lib/pkgconfig/mkl-dynamic-lp64-tbb.pc", "lib/pkgconfig/mkl-sdl.pc", "lib/pkgconfig/mkl-static-ilp64-iomp.pc", "lib/pkgconfig/mkl-static-ilp64-seq.pc", "lib/pkgconfig/mkl-static-ilp64-tbb.pc", "lib/pkgconfig/mkl-static-lp64-iomp.pc", "lib/pkgconfig/mkl-static-lp64-seq.pc", "lib/pkgconfig/mkl-static-lp64-tbb.pc"], "fn": "mkl-2023.2.0-h694c41f_50502.conda", "license": "LicenseRef-ProprietaryIntel", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/mkl-2023.2.0-h694c41f_50502", "type": 1}, "md5": "0bdfc939c8542e0bc6041cbd9a900219", "name": "mkl", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/mkl-2023.2.0-h694c41f_50502.conda", "paths_data": {"paths": [{"_path": "lib/cmake/mkl/MKLConfig.cmake", "path_type": "hardlink", "sha256": "e656cff1ff6fc20f1bb122636290272569bd0c235737e1b0c610c91b8073a6a9", "sha256_in_prefix": "e656cff1ff6fc20f1bb122636290272569bd0c235737e1b0c610c91b8073a6a9", "size_in_bytes": 35488}, {"_path": "lib/cmake/mkl/MKLConfigVersion.cmake", "path_type": "hardlink", "sha256": "e748532a83177074886ccd42dec38eebe8a888b5306065330915b4309d587994", "sha256_in_prefix": "e748532a83177074886ccd42dec38eebe8a888b5306065330915b4309d587994", "size_in_bytes": 2780}, {"_path": "lib/libmkl_avx.2.dylib", "path_type": "hardlink", "sha256": "95b60f5f75fb9bb138c16a29fa0cf4d0921fa44a202fe6f31717ae29dc8ebe70", "sha256_in_prefix": "95b60f5f75fb9bb138c16a29fa0cf4d0921fa44a202fe6f31717ae29dc8ebe70", "size_in_bytes": 53089328}, {"_path": "lib/libmkl_avx2.2.dylib", "path_type": "hardlink", "sha256": "5d2e327b80f03e08220fda6df778277fab62262f9acbd423a473e78fd4895f45", "sha256_in_prefix": "5d2e327b80f03e08220fda6df778277fab62262f9acbd423a473e78fd4895f45", "size_in_bytes": 50234736}, {"_path": "lib/libmkl_avx512.2.dylib", "path_type": "hardlink", "sha256": "d6ced162b8b5813bbdf24c74bab6b4ddf2578879c5bcd67bf85c0f43505ea6cf", "sha256_in_prefix": "d6ced162b8b5813bbdf24c74bab6b4ddf2578879c5bcd67bf85c0f43505ea6cf", "size_in_bytes": 66782112}, {"_path": "lib/libmkl_blacs_mpich_ilp64.2.dylib", "path_type": "hardlink", "sha256": "8ab7b4a869dd49bc2943dbd4fb997b315e20d71ef7181b70199fa7ad8fe3c0bf", "sha256_in_prefix": "8ab7b4a869dd49bc2943dbd4fb997b315e20d71ef7181b70199fa7ad8fe3c0bf", "size_in_bytes": 535056}, {"_path": "lib/libmkl_blacs_mpich_ilp64.dylib", "path_type": "softlink", "sha256": "8ab7b4a869dd49bc2943dbd4fb997b315e20d71ef7181b70199fa7ad8fe3c0bf", "size_in_bytes": 535056}, {"_path": "lib/libmkl_blacs_mpich_lp64.2.dylib", "path_type": "hardlink", "sha256": "807e7845719616f05ac588b13010b8b43d8e190b90060da64a4da2a16fd56cdc", "sha256_in_prefix": "807e7845719616f05ac588b13010b8b43d8e190b90060da64a4da2a16fd56cdc", "size_in_bytes": 323424}, {"_path": "lib/libmkl_blacs_mpich_lp64.dylib", "path_type": "softlink", "sha256": "807e7845719616f05ac588b13010b8b43d8e190b90060da64a4da2a16fd56cdc", "size_in_bytes": 323424}, {"_path": "lib/libmkl_cdft_core.2.dylib", "path_type": "hardlink", "sha256": "7c1ea134ffe6e0216eb8bce2a48fc70b1f33a4a88a155b2f184e464400d6be11", "sha256_in_prefix": "7c1ea134ffe6e0216eb8bce2a48fc70b1f33a4a88a155b2f184e464400d6be11", "size_in_bytes": 199312}, {"_path": "lib/libmkl_cdft_core.dylib", "path_type": "softlink", "sha256": "7c1ea134ffe6e0216eb8bce2a48fc70b1f33a4a88a155b2f184e464400d6be11", "size_in_bytes": 199312}, {"_path": "lib/libmkl_core.2.dylib", "path_type": "hardlink", "sha256": "dd14590e2ee17340ae5ad454e21843db9d13d09c846f6bb53f7e6b91c5aff82e", "sha256_in_prefix": "dd14590e2ee17340ae5ad454e21843db9d13d09c846f6bb53f7e6b91c5aff82e", "size_in_bytes": 69822560}, {"_path": "lib/libmkl_core.dylib", "path_type": "softlink", "sha256": "dd14590e2ee17340ae5ad454e21843db9d13d09c846f6bb53f7e6b91c5aff82e", "size_in_bytes": 69822560}, {"_path": "lib/libmkl_intel_ilp64.2.dylib", "path_type": "hardlink", "sha256": "6d349cba90b765a8ff807a931a6bc79fe6b8d45ce15f231b0a4eb73287b55147", "sha256_in_prefix": "6d349cba90b765a8ff807a931a6bc79fe6b8d45ce15f231b0a4eb73287b55147", "size_in_bytes": 38156112}, {"_path": "lib/libmkl_intel_ilp64.dylib", "path_type": "softlink", "sha256": "6d349cba90b765a8ff807a931a6bc79fe6b8d45ce15f231b0a4eb73287b55147", "size_in_bytes": 38156112}, {"_path": "lib/libmkl_intel_lp64.2.dylib", "path_type": "hardlink", "sha256": "5e20b44eeb689bdc6ab9a5f892f6ffe06d641ad64afa74bc88af22a4832ebbff", "sha256_in_prefix": "5e20b44eeb689bdc6ab9a5f892f6ffe06d641ad64afa74bc88af22a4832ebbff", "size_in_bytes": 41712896}, {"_path": "lib/libmkl_intel_lp64.dylib", "path_type": "softlink", "sha256": "5e20b44eeb689bdc6ab9a5f892f6ffe06d641ad64afa74bc88af22a4832ebbff", "size_in_bytes": 41712896}, {"_path": "lib/libmkl_intel_thread.2.dylib", "path_type": "hardlink", "sha256": "39064a53a32702083218c0471e97687ec39d6328b04e0766572886d66b5c2895", "sha256_in_prefix": "39064a53a32702083218c0471e97687ec39d6328b04e0766572886d66b5c2895", "size_in_bytes": 68183024}, {"_path": "lib/libmkl_intel_thread.dylib", "path_type": "softlink", "sha256": "39064a53a32702083218c0471e97687ec39d6328b04e0766572886d66b5c2895", "size_in_bytes": 68183024}, {"_path": "lib/libmkl_mc3.2.dylib", "path_type": "hardlink", "sha256": "a0481ad7ddbfcbc76d7c4b98507755e036b01cad9368bfc265a6ab2ae70544e7", "sha256_in_prefix": "a0481ad7ddbfcbc76d7c4b98507755e036b01cad9368bfc265a6ab2ae70544e7", "size_in_bytes": 49791440}, {"_path": "lib/libmkl_rt.2.dylib", "path_type": "hardlink", "sha256": "d367ace211c4c67a12f17bf876a379d98e0854cf063714fbfbef3564b05f9794", "sha256_in_prefix": "d367ace211c4c67a12f17bf876a379d98e0854cf063714fbfbef3564b05f9794", "size_in_bytes": 18789024}, {"_path": "lib/libmkl_rt.dylib", "path_type": "softlink", "sha256": "d367ace211c4c67a12f17bf876a379d98e0854cf063714fbfbef3564b05f9794", "size_in_bytes": 18789024}, {"_path": "lib/libmkl_scalapack_ilp64.2.dylib", "path_type": "hardlink", "sha256": "6a2f8fadf8206218d06ea12de51a1f10a5587d7f5055a3ad9e2e9240667aefb2", "sha256_in_prefix": "6a2f8fadf8206218d06ea12de51a1f10a5587d7f5055a3ad9e2e9240667aefb2", "size_in_bytes": 7787168}, {"_path": "lib/libmkl_scalapack_ilp64.dylib", "path_type": "softlink", "sha256": "6a2f8fadf8206218d06ea12de51a1f10a5587d7f5055a3ad9e2e9240667aefb2", "size_in_bytes": 7787168}, {"_path": "lib/libmkl_scalapack_lp64.2.dylib", "path_type": "hardlink", "sha256": "579cabf6e27baa3994b7714ba1a4238c205dd70f5dfef97e65611c2d3597e5a3", "sha256_in_prefix": "579cabf6e27baa3994b7714ba1a4238c205dd70f5dfef97e65611c2d3597e5a3", "size_in_bytes": 7671616}, {"_path": "lib/libmkl_scalapack_lp64.dylib", "path_type": "softlink", "sha256": "579cabf6e27baa3994b7714ba1a4238c205dd70f5dfef97e65611c2d3597e5a3", "size_in_bytes": 7671616}, {"_path": "lib/libmkl_sequential.2.dylib", "path_type": "hardlink", "sha256": "e28020965a20975b2180b933f918d0876d6e0cd2ec276dec80e0452b580f680d", "sha256_in_prefix": "e28020965a20975b2180b933f918d0876d6e0cd2ec276dec80e0452b580f680d", "size_in_bytes": 33431920}, {"_path": "lib/libmkl_sequential.dylib", "path_type": "softlink", "sha256": "e28020965a20975b2180b933f918d0876d6e0cd2ec276dec80e0452b580f680d", "size_in_bytes": 33431920}, {"_path": "lib/libmkl_tbb_thread.2.dylib", "path_type": "hardlink", "sha256": "f333f62c3896886f6f6c2dc49cf6902f1be69aa8a0d67a359963df82aac074bb", "sha256_in_prefix": "f333f62c3896886f6f6c2dc49cf6902f1be69aa8a0d67a359963df82aac074bb", "size_in_bytes": 44858416}, {"_path": "lib/libmkl_tbb_thread.dylib", "path_type": "softlink", "sha256": "f333f62c3896886f6f6c2dc49cf6902f1be69aa8a0d67a359963df82aac074bb", "size_in_bytes": 44858416}, {"_path": "lib/libmkl_vml_avx.2.dylib", "path_type": "hardlink", "sha256": "41a6591a659ad2d0dfcffb02a4fc0a7ada05179f59b56dbe9a279d3756e866c7", "sha256_in_prefix": "41a6591a659ad2d0dfcffb02a4fc0a7ada05179f59b56dbe9a279d3756e866c7", "size_in_bytes": 15619792}, {"_path": "lib/libmkl_vml_avx2.2.dylib", "path_type": "hardlink", "sha256": "9f7fe7252953f33fae290253a1ddc7eab78b9e3513c3e9e8766fea312cfc2e5d", "sha256_in_prefix": "9f7fe7252953f33fae290253a1ddc7eab78b9e3513c3e9e8766fea312cfc2e5d", "size_in_bytes": 14548656}, {"_path": "lib/libmkl_vml_avx512.2.dylib", "path_type": "hardlink", "sha256": "960c364e025f189567943de13f6108cb6626795823b80c814d32ae166556ceba", "sha256_in_prefix": "960c364e025f189567943de13f6108cb6626795823b80c814d32ae166556ceba", "size_in_bytes": 14029920}, {"_path": "lib/libmkl_vml_mc3.2.dylib", "path_type": "hardlink", "sha256": "0982d71354ab46825b25db8849c48e1a3068e39a57a7d88f3f233f869b11e927", "sha256_in_prefix": "0982d71354ab46825b25db8849c48e1a3068e39a57a7d88f3f233f869b11e927", "size_in_bytes": 14332656}, {"_path": "lib/mkl_msg.cat", "path_type": "hardlink", "sha256": "d9673079c592f0916673b0fcb6a03ff6997e62763ecec77e8b3c0918e9b5fe8d", "sha256_in_prefix": "d9673079c592f0916673b0fcb6a03ff6997e62763ecec77e8b3c0918e9b5fe8d", "size_in_bytes": 106494}, {"_path": "lib/pkgconfig/mkl-dynamic-ilp64-iomp.pc", "path_type": "hardlink", "sha256": "0f17ef0540dd6df59d9c75c1839da7103392fc884345ea7b6494cbc77e6285ca", "sha256_in_prefix": "0f17ef0540dd6df59d9c75c1839da7103392fc884345ea7b6494cbc77e6285ca", "size_in_bytes": 1495}, {"_path": "lib/pkgconfig/mkl-dynamic-ilp64-seq.pc", "path_type": "hardlink", "sha256": "3951228b3777b8e47ec882a940b407841b7d144da784cb8bd6d2dbd65c6fec7a", "sha256_in_prefix": "3951228b3777b8e47ec882a940b407841b7d144da784cb8bd6d2dbd65c6fec7a", "size_in_bytes": 1464}, {"_path": "lib/pkgconfig/mkl-dynamic-ilp64-tbb.pc", "path_type": "hardlink", "sha256": "51528a81f610ccbf36f495bea909e0098649a85b8eb1e3ab0af20cb6d9dfbc7a", "sha256_in_prefix": "51528a81f610ccbf36f495bea909e0098649a85b8eb1e3ab0af20cb6d9dfbc7a", "size_in_bytes": 1490}, {"_path": "lib/pkgconfig/mkl-dynamic-lp64-iomp.pc", "path_type": "hardlink", "sha256": "77b396e735ae3c466d405693441e4a740dc345d26906ee4316b37e29da534fc7", "sha256_in_prefix": "77b396e735ae3c466d405693441e4a740dc345d26906ee4316b37e29da534fc7", "size_in_bytes": 1481}, {"_path": "lib/pkgconfig/mkl-dynamic-lp64-seq.pc", "path_type": "hardlink", "sha256": "ffc729fb0a86cabc5ce10e3da41965b3ed9a6fbc1784492e99e9071cd81e5a34", "sha256_in_prefix": "ffc729fb0a86cabc5ce10e3da41965b3ed9a6fbc1784492e99e9071cd81e5a34", "size_in_bytes": 1450}, {"_path": "lib/pkgconfig/mkl-dynamic-lp64-tbb.pc", "path_type": "hardlink", "sha256": "650ae09ec3d6975d5b2033520cf36c7e1116a4e4c56a5103081417fa1622772f", "sha256_in_prefix": "650ae09ec3d6975d5b2033520cf36c7e1116a4e4c56a5103081417fa1622772f", "size_in_bytes": 1476}, {"_path": "lib/pkgconfig/mkl-sdl.pc", "path_type": "hardlink", "sha256": "e028bb034feb2bdb4a5fd098c70760892cd750a9233ba2342558c56b39073c86", "sha256_in_prefix": "e028bb034feb2bdb4a5fd098c70760892cd750a9233ba2342558c56b39073c86", "size_in_bytes": 1414}, {"_path": "lib/pkgconfig/mkl-static-ilp64-iomp.pc", "path_type": "hardlink", "sha256": "549e3571c893906046eb247231656f1de439a7c58fbbabf53c8ac7f429f0304c", "sha256_in_prefix": "549e3571c893906046eb247231656f1de439a7c58fbbabf53c8ac7f429f0304c", "size_in_bytes": 1501}, {"_path": "lib/pkgconfig/mkl-static-ilp64-seq.pc", "path_type": "hardlink", "sha256": "0c23011ea98643ee82db03511bea54f8f438eaf754d87406ce000e42433d0385", "sha256_in_prefix": "0c23011ea98643ee82db03511bea54f8f438eaf754d87406ce000e42433d0385", "size_in_bytes": 1470}, {"_path": "lib/pkgconfig/mkl-static-ilp64-tbb.pc", "path_type": "hardlink", "sha256": "9fd4103b65f01f5a8fb6db8d1a036fb0df676f4cdc10c2655f1de913b2e22e20", "sha256_in_prefix": "9fd4103b65f01f5a8fb6db8d1a036fb0df676f4cdc10c2655f1de913b2e22e20", "size_in_bytes": 1502}, {"_path": "lib/pkgconfig/mkl-static-lp64-iomp.pc", "path_type": "hardlink", "sha256": "ff75f40b6512506dc397267def3f8994b5aea6e765e791ddd9135a4298936c2b", "sha256_in_prefix": "ff75f40b6512506dc397267def3f8994b5aea6e765e791ddd9135a4298936c2b", "size_in_bytes": 1487}, {"_path": "lib/pkgconfig/mkl-static-lp64-seq.pc", "path_type": "hardlink", "sha256": "d3de52d817284d42b71142430e227400eb671e1a63d8a3037a9c58eb285f3b81", "sha256_in_prefix": "d3de52d817284d42b71142430e227400eb671e1a63d8a3037a9c58eb285f3b81", "size_in_bytes": 1456}, {"_path": "lib/pkgconfig/mkl-static-lp64-tbb.pc", "path_type": "hardlink", "sha256": "90122e715eb1f6716b8228aa310193051439221278ff09d0f098229de2c486b3", "sha256_in_prefix": "90122e715eb1f6716b8228aa310193051439221278ff09d0f098229de2c486b3", "size_in_bytes": 1488}], "paths_version": 1}, "requested_spec": "None", "sha256": "1841842ed23ddd61fd46b2282294b1b9ef332f39229645e1331739ee8c2a6136", "size": 119058457, "subdir": "osx-64", "timestamp": 1757091004000, "url": "https://conda.anaconda.org/conda-forge/osx-64/mkl-2023.2.0-h694c41f_50502.conda", "version": "2023.2.0"}