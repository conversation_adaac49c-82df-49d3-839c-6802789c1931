{"build": "hdc53af8_8_cpu", "build_number": 8, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.14", "libarrow 20.0.0 h7601d43_8_cpu", "libcxx >=18"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libarrow-acero-20.0.0-hdc53af8_8_cpu", "files": ["lib/cmake/ArrowAcero/ArrowAceroConfig.cmake", "lib/cmake/ArrowAcero/ArrowAceroConfigVersion.cmake", "lib/cmake/ArrowAcero/ArrowAceroTargets-release.cmake", "lib/cmake/ArrowAcero/ArrowAceroTargets.cmake", "lib/libarrow_acero.2000.0.0.dylib", "lib/libarrow_acero.2000.dylib", "lib/libarrow_acero.dylib", "lib/pkgconfig/arrow-acero.pc"], "fn": "libarrow-acero-20.0.0-hdc53af8_8_cpu.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libarrow-acero-20.0.0-hdc53af8_8_cpu", "type": 1}, "md5": "4bf345b3fece4ee24cbe504bcd6658fa", "name": "libarrow-acero", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libarrow-acero-20.0.0-hdc53af8_8_cpu.conda", "paths_data": {"paths": [{"_path": "lib/cmake/ArrowAcero/ArrowAceroConfig.cmake", "path_type": "hardlink", "sha256": "08fb976e3de49160c4c1918bd5dcfab31915b590a8be49c9acdb3c5b119f01fa", "sha256_in_prefix": "08fb976e3de49160c4c1918bd5dcfab31915b590a8be49c9acdb3c5b119f01fa", "size_in_bytes": 2308}, {"_path": "lib/cmake/ArrowAcero/ArrowAceroConfigVersion.cmake", "path_type": "hardlink", "sha256": "4eb556774449e5062cea02eaaab6b824572fe8dadcdfb258fc6e518fac98006b", "sha256_in_prefix": "4eb556774449e5062cea02eaaab6b824572fe8dadcdfb258fc6e518fac98006b", "size_in_bytes": 2765}, {"_path": "lib/cmake/ArrowAcero/ArrowAceroTargets-release.cmake", "path_type": "hardlink", "sha256": "399f042fa2f10c2fc46b035e10d93b2977595c96efb3a6267eac8ae33a756d9b", "sha256_in_prefix": "399f042fa2f10c2fc46b035e10d93b2977595c96efb3a6267eac8ae33a756d9b", "size_in_bytes": 1240}, {"_path": "lib/cmake/ArrowAcero/ArrowAceroTargets.cmake", "path_type": "hardlink", "sha256": "0cb076ea4841ed49ff1f7342c1d75a7f7d45cfe091c9589371b4239b8830bb88", "sha256_in_prefix": "0cb076ea4841ed49ff1f7342c1d75a7f7d45cfe091c9589371b4239b8830bb88", "size_in_bytes": 4237}, {"_path": "lib/libarrow_acero.2000.0.0.dylib", "path_type": "hardlink", "sha256": "969bbbd5cdd75361f196c84c435c97144d38d20fd42f70bd074ac81c9bb23f26", "sha256_in_prefix": "969bbbd5cdd75361f196c84c435c97144d38d20fd42f70bd074ac81c9bb23f26", "size_in_bytes": 2257656}, {"_path": "lib/libarrow_acero.2000.dylib", "path_type": "softlink", "sha256": "969bbbd5cdd75361f196c84c435c97144d38d20fd42f70bd074ac81c9bb23f26", "size_in_bytes": 2257656}, {"_path": "lib/libarrow_acero.dylib", "path_type": "softlink", "sha256": "969bbbd5cdd75361f196c84c435c97144d38d20fd42f70bd074ac81c9bb23f26", "size_in_bytes": 2257656}, {"_path": "lib/pkgconfig/arrow-acero.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/apache-arrow_1751095169775/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "6d3ec5996e8921dfae0310a43d2f73f176826a38c18a58c54e3e565dc7568523", "sha256_in_prefix": "46fe78903dee1afa6fc125e886e0bcf5d4240e36767752b16a81cac43a230985", "size_in_bytes": 1262}], "paths_version": 1}, "requested_spec": "None", "sha256": "40ee033b9456794eb9de20e7767f95b7958fe143bfae498d97c20b977b99e45c", "size": 549959, "subdir": "osx-64", "timestamp": 1751097746000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libarrow-acero-20.0.0-hdc53af8_8_cpu.conda", "version": "20.0.0"}