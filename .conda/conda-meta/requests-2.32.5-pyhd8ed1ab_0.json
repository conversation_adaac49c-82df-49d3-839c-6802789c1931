{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": ["chardet >=3.0.2,<6"], "depends": ["certifi >=2017.4.17", "charset-normalizer >=2,<4", "idna >=2.5,<4", "python >=3.9", "urllib3 >=1.21.1,<3"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/requests-2.32.5-pyhd8ed1ab_0", "files": ["lib/python3.11/site-packages/requests-2.32.5.dist-info/INSTALLER", "lib/python3.11/site-packages/requests-2.32.5.dist-info/METADATA", "lib/python3.11/site-packages/requests-2.32.5.dist-info/RECORD", "lib/python3.11/site-packages/requests-2.32.5.dist-info/REQUESTED", "lib/python3.11/site-packages/requests-2.32.5.dist-info/WHEEL", "lib/python3.11/site-packages/requests-2.32.5.dist-info/direct_url.json", "lib/python3.11/site-packages/requests-2.32.5.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/requests-2.32.5.dist-info/top_level.txt", "lib/python3.11/site-packages/requests/__init__.py", "lib/python3.11/site-packages/requests/__version__.py", "lib/python3.11/site-packages/requests/_internal_utils.py", "lib/python3.11/site-packages/requests/adapters.py", "lib/python3.11/site-packages/requests/api.py", "lib/python3.11/site-packages/requests/auth.py", "lib/python3.11/site-packages/requests/certs.py", "lib/python3.11/site-packages/requests/compat.py", "lib/python3.11/site-packages/requests/cookies.py", "lib/python3.11/site-packages/requests/exceptions.py", "lib/python3.11/site-packages/requests/help.py", "lib/python3.11/site-packages/requests/hooks.py", "lib/python3.11/site-packages/requests/models.py", "lib/python3.11/site-packages/requests/packages.py", "lib/python3.11/site-packages/requests/sessions.py", "lib/python3.11/site-packages/requests/status_codes.py", "lib/python3.11/site-packages/requests/structures.py", "lib/python3.11/site-packages/requests/utils.py", "lib/python3.11/site-packages/requests/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/requests/__pycache__/__version__.cpython-311.pyc", "lib/python3.11/site-packages/requests/__pycache__/_internal_utils.cpython-311.pyc", "lib/python3.11/site-packages/requests/__pycache__/adapters.cpython-311.pyc", "lib/python3.11/site-packages/requests/__pycache__/api.cpython-311.pyc", "lib/python3.11/site-packages/requests/__pycache__/auth.cpython-311.pyc", "lib/python3.11/site-packages/requests/__pycache__/certs.cpython-311.pyc", "lib/python3.11/site-packages/requests/__pycache__/compat.cpython-311.pyc", "lib/python3.11/site-packages/requests/__pycache__/cookies.cpython-311.pyc", "lib/python3.11/site-packages/requests/__pycache__/exceptions.cpython-311.pyc", "lib/python3.11/site-packages/requests/__pycache__/help.cpython-311.pyc", "lib/python3.11/site-packages/requests/__pycache__/hooks.cpython-311.pyc", "lib/python3.11/site-packages/requests/__pycache__/models.cpython-311.pyc", "lib/python3.11/site-packages/requests/__pycache__/packages.cpython-311.pyc", "lib/python3.11/site-packages/requests/__pycache__/sessions.cpython-311.pyc", "lib/python3.11/site-packages/requests/__pycache__/status_codes.cpython-311.pyc", "lib/python3.11/site-packages/requests/__pycache__/structures.cpython-311.pyc", "lib/python3.11/site-packages/requests/__pycache__/utils.cpython-311.pyc"], "fn": "requests-2.32.5-pyhd8ed1ab_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/requests-2.32.5-pyhd8ed1ab_0", "type": 1}, "md5": "db0c6b99149880c8ba515cf4abe93ee4", "name": "requests", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/requests-2.32.5-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/requests-2.32.5.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/requests-2.32.5.dist-info/METADATA", "path_type": "hardlink", "sha256": "65b5a08da81f4915513e760965ff14b7518f65b7bb3efe0dab364bbcc4d40cb0", "sha256_in_prefix": "65b5a08da81f4915513e760965ff14b7518f65b7bb3efe0dab364bbcc4d40cb0", "size_in_bytes": 4945}, {"_path": "site-packages/requests-2.32.5.dist-info/RECORD", "path_type": "hardlink", "sha256": "38566c954ef0d57e96f39b7169666fa3ea87876f4ab845b734cdd92b5843be00", "sha256_in_prefix": "38566c954ef0d57e96f39b7169666fa3ea87876f4ab845b734cdd92b5843be00", "size_in_bytes": 2940}, {"_path": "site-packages/requests-2.32.5.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/requests-2.32.5.dist-info/WHEEL", "path_type": "hardlink", "sha256": "ff309ddcdd65ebd02bc724dbf2bcc4a0ff53a5b61792a44548d383e4ebb19d3b", "sha256_in_prefix": "ff309ddcdd65ebd02bc724dbf2bcc4a0ff53a5b61792a44548d383e4ebb19d3b", "size_in_bytes": 91}, {"_path": "site-packages/requests-2.32.5.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "0af25398dfa7c12754d0c446105c9ca78b10f1d6fcf95436fa646e5099c0059c", "sha256_in_prefix": "0af25398dfa7c12754d0c446105c9ca78b10f1d6fcf95436fa646e5099c0059c", "size_in_bytes": 104}, {"_path": "site-packages/requests-2.32.5.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "09e8a9bcec8067104652c168685ab0931e7868f9c8284b66f5ae6edae5f1130b", "sha256_in_prefix": "09e8a9bcec8067104652c168685ab0931e7868f9c8284b66f5ae6edae5f1130b", "size_in_bytes": 10142}, {"_path": "site-packages/requests-2.32.5.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "7cc4959877dbe6b6c63a8eb1bfe3bfb545fa8fe5b28b1b2c13e4a7c1c0d1c4d4", "sha256_in_prefix": "7cc4959877dbe6b6c63a8eb1bfe3bfb545fa8fe5b28b1b2c13e4a7c1c0d1c4d4", "size_in_bytes": 9}, {"_path": "site-packages/requests/__init__.py", "path_type": "hardlink", "sha256": "e3168011198f0c804fb1ad8fb23a54f6bd3aca8a0afb69992874d90215915adb", "sha256_in_prefix": "e3168011198f0c804fb1ad8fb23a54f6bd3aca8a0afb69992874d90215915adb", "size_in_bytes": 5072}, {"_path": "site-packages/requests/__version__.py", "path_type": "hardlink", "sha256": "40a0dc78af0afee8eac030dcde862b474a1d381620295390439bc56a9fc6fdc8", "sha256_in_prefix": "40a0dc78af0afee8eac030dcde862b474a1d381620295390439bc56a9fc6fdc8", "size_in_bytes": 435}, {"_path": "site-packages/requests/_internal_utils.py", "path_type": "hardlink", "sha256": "9cc4329abe21b37d93a95a3901b0ab99c24486f3d487bc57965bb2ab0b252e24", "sha256_in_prefix": "9cc4329abe21b37d93a95a3901b0ab99c24486f3d487bc57965bb2ab0b252e24", "size_in_bytes": 1495}, {"_path": "site-packages/requests/adapters.py", "path_type": "hardlink", "sha256": "f275f5d7781b6f5db7694b71d844e400dffa22c617f8cd9f4682c696e4c47119", "sha256_in_prefix": "f275f5d7781b6f5db7694b71d844e400dffa22c617f8cd9f4682c696e4c47119", "size_in_bytes": 26285}, {"_path": "site-packages/requests/api.py", "path_type": "hardlink", "sha256": "fd96fd39aeedcd5222cd32b016b3e30c463d7a3b66fce9d2444467003c46b10b", "sha256_in_prefix": "fd96fd39aeedcd5222cd32b016b3e30c463d7a3b66fce9d2444467003c46b10b", "size_in_bytes": 6449}, {"_path": "site-packages/requests/auth.py", "path_type": "hardlink", "sha256": "905ef9b6a9cb72d67d31ffe19bd4d9223e1c4169cde6ec51cfca16b31e70991d", "sha256_in_prefix": "905ef9b6a9cb72d67d31ffe19bd4d9223e1c4169cde6ec51cfca16b31e70991d", "size_in_bytes": 10186}, {"_path": "site-packages/requests/certs.py", "path_type": "hardlink", "sha256": "67d49be35d009efea35054f2b2cd23145854eb1b2df1cb442ea7f2f04bf6de0c", "sha256_in_prefix": "67d49be35d009efea35054f2b2cd23145854eb1b2df1cb442ea7f2f04bf6de0c", "size_in_bytes": 429}, {"_path": "site-packages/requests/compat.py", "path_type": "hardlink", "sha256": "27bb088d1e97a031a9e494d5ccec642b97d2a145546bf3e373b8916610161a62", "sha256_in_prefix": "27bb088d1e97a031a9e494d5ccec642b97d2a145546bf3e373b8916610161a62", "size_in_bytes": 2142}, {"_path": "site-packages/requests/cookies.py", "path_type": "hardlink", "sha256": "6cd8be8aa123e0d3d9d34fa86feac7bf392f39bccdde5129830de0ea9692dd7c", "sha256_in_prefix": "6cd8be8aa123e0d3d9d34fa86feac7bf392f39bccdde5129830de0ea9692dd7c", "size_in_bytes": 18590}, {"_path": "site-packages/requests/exceptions.py", "path_type": "hardlink", "sha256": "8c93d2d545804ecf3a4a155468ba2b4e225bd52686ba83445a020225ea7e5646", "sha256_in_prefix": "8c93d2d545804ecf3a4a155468ba2b4e225bd52686ba83445a020225ea7e5646", "size_in_bytes": 4260}, {"_path": "site-packages/requests/help.py", "path_type": "hardlink", "sha256": "80f5f977f1fb5ddf3c6830017a386a1a097d075545453b79066898bcbdcfcc84", "sha256_in_prefix": "80f5f977f1fb5ddf3c6830017a386a1a097d075545453b79066898bcbdcfcc84", "size_in_bytes": 3875}, {"_path": "site-packages/requests/hooks.py", "path_type": "hardlink", "sha256": "0a2bb2b221c0dfd57951f702057148c7cdc8ac3a6ec1f37d45c4d482fdbc7ed4", "sha256_in_prefix": "0a2bb2b221c0dfd57951f702057148c7cdc8ac3a6ec1f37d45c4d482fdbc7ed4", "size_in_bytes": 733}, {"_path": "site-packages/requests/models.py", "path_type": "hardlink", "sha256": "32365d67893bb67c3ed67cf93ca4a18e63e6ab29342fa0dc8b09c59e06ff564e", "sha256_in_prefix": "32365d67893bb67c3ed67cf93ca4a18e63e6ab29342fa0dc8b09c59e06ff564e", "size_in_bytes": 35510}, {"_path": "site-packages/requests/packages.py", "path_type": "hardlink", "sha256": "fe0d2067af355320252874631fa91a9db6a8c71d9e01beaacdc5e2383c932287", "sha256_in_prefix": "fe0d2067af355320252874631fa91a9db6a8c71d9e01beaacdc5e2383c932287", "size_in_bytes": 904}, {"_path": "site-packages/requests/sessions.py", "path_type": "hardlink", "sha256": "0a5d5da449ce7f0af3ccf6e4bbe7a67a935e37846dff4ff9f08cb6c7e2464e6f", "sha256_in_prefix": "0a5d5da449ce7f0af3ccf6e4bbe7a67a935e37846dff4ff9f08cb6c7e2464e6f", "size_in_bytes": 30503}, {"_path": "site-packages/requests/status_codes.py", "path_type": "hardlink", "sha256": "889500780db96da4ddc3ee8f7c3d1e178aa1a48343251248fb268cab1b382c42", "sha256_in_prefix": "889500780db96da4ddc3ee8f7c3d1e178aa1a48343251248fb268cab1b382c42", "size_in_bytes": 4322}, {"_path": "site-packages/requests/structures.py", "path_type": "hardlink", "sha256": "f886e6855cf4e92fb968f499b94b6167afba0fd5ce8d1b935c739a6d8d38d573", "sha256_in_prefix": "f886e6855cf4e92fb968f499b94b6167afba0fd5ce8d1b935c739a6d8d38d573", "size_in_bytes": 2912}, {"_path": "site-packages/requests/utils.py", "path_type": "hardlink", "sha256": "5aa53ceab677c2f842fad42359c8ed1ff1c4299c1607789609957a496e4311d4", "sha256_in_prefix": "5aa53ceab677c2f842fad42359c8ed1ff1c4299c1607789609957a496e4311d4", "size_in_bytes": 33213}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/__version__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/_internal_utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/adapters.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/api.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/auth.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/certs.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/compat.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/cookies.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/exceptions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/help.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/hooks.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/models.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/packages.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/sessions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/status_codes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/structures.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests/__pycache__/utils.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "requests", "sha256": "8dc54e94721e9ab545d7234aa5192b74102263d3e704e6d0c8aa7008f2da2a7b", "size": 59263, "subdir": "noarch", "timestamp": 1755614348000, "url": "https://conda.anaconda.org/conda-forge/noarch/requests-2.32.5-pyhd8ed1ab_0.conda", "version": "2.32.5"}