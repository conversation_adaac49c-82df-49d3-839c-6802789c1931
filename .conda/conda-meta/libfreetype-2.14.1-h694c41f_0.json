{"build": "h694c41f_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["libfreetype6 >=2.14.1"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libfreetype-2.14.1-h694c41f_0", "files": [], "fn": "libfreetype-2.14.1-h694c41f_0.conda", "license": "GPL-2.0-only OR FTL", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libfreetype-2.14.1-h694c41f_0", "type": 1}, "md5": "e0e2edaf5e0c71b843e25a7ecc451cc9", "name": "libfreetype", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libfreetype-2.14.1-h694c41f_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "None", "sha256": "035e23ef87759a245d51890aedba0b494a26636784910c3730d76f3dc4482b1d", "size": 7780, "subdir": "osx-64", "timestamp": 1757945952000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libfreetype-2.14.1-h694c41f_0.conda", "version": "2.14.1"}