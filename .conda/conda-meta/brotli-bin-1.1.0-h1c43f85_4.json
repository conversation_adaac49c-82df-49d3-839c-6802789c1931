{"build": "h1c43f85_4", "build_number": 4, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13", "libbrotlidec 1.1.0 h1c43f85_4", "libbrotlienc 1.1.0 h1c43f85_4"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/brotli-bin-1.1.0-h1c43f85_4", "files": ["bin/brotli"], "fn": "brotli-bin-1.1.0-h1c43f85_4.conda", "license": "MIT", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/brotli-bin-1.1.0-h1c43f85_4", "type": 1}, "md5": "718fb8aa4c8cb953982416db9a82b349", "name": "brotli-bin", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/brotli-bin-1.1.0-h1c43f85_4.conda", "paths_data": {"paths": [{"_path": "bin/brotli", "path_type": "hardlink", "sha256": "4bb8eee3b3acad6f6aa25c265e110a8f281b3f2ed5f95a68706eb30a9367725c", "sha256_in_prefix": "4bb8eee3b3acad6f6aa25c265e110a8f281b3f2ed5f95a68706eb30a9367725c", "size_in_bytes": 27840}], "paths_version": 1}, "requested_spec": "None", "sha256": "549ea0221019cfb4b370354f2c3ffbd4be1492740e1c73b2cdf9687ed6ad7364", "size": 17311, "subdir": "osx-64", "timestamp": 1756599830000, "url": "https://conda.anaconda.org/conda-forge/osx-64/brotli-bin-1.1.0-h1c43f85_4.conda", "version": "1.1.0"}