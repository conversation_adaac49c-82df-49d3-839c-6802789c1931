{"build": "pyhd8ed1ab_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.9"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/sniffio-1.3.1-pyhd8ed1ab_1", "files": ["lib/python3.11/site-packages/sniffio-1.3.1.dist-info/INSTALLER", "lib/python3.11/site-packages/sniffio-1.3.1.dist-info/LICENSE", "lib/python3.11/site-packages/sniffio-1.3.1.dist-info/LICENSE.APACHE2", "lib/python3.11/site-packages/sniffio-1.3.1.dist-info/LICENSE.MIT", "lib/python3.11/site-packages/sniffio-1.3.1.dist-info/METADATA", "lib/python3.11/site-packages/sniffio-1.3.1.dist-info/RECORD", "lib/python3.11/site-packages/sniffio-1.3.1.dist-info/REQUESTED", "lib/python3.11/site-packages/sniffio-1.3.1.dist-info/WHEEL", "lib/python3.11/site-packages/sniffio-1.3.1.dist-info/direct_url.json", "lib/python3.11/site-packages/sniffio-1.3.1.dist-info/top_level.txt", "lib/python3.11/site-packages/sniffio/__init__.py", "lib/python3.11/site-packages/sniffio/_impl.py", "lib/python3.11/site-packages/sniffio/_tests/__init__.py", "lib/python3.11/site-packages/sniffio/_tests/test_sniffio.py", "lib/python3.11/site-packages/sniffio/_version.py", "lib/python3.11/site-packages/sniffio/py.typed", "lib/python3.11/site-packages/sniffio/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/sniffio/__pycache__/_impl.cpython-311.pyc", "lib/python3.11/site-packages/sniffio/_tests/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/sniffio/_tests/__pycache__/test_sniffio.cpython-311.pyc", "lib/python3.11/site-packages/sniffio/__pycache__/_version.cpython-311.pyc"], "fn": "sniffio-1.3.1-pyhd8ed1ab_1.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/sniffio-1.3.1-pyhd8ed1ab_1", "type": 1}, "md5": "bf7a226e58dfb8346c70df36065d86c9", "name": "sniffio", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/sniffio-1.3.1-pyhd8ed1ab_1.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/sniffio-1.3.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/sniffio-1.3.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "652c878488d1456361e08c3f8607fd7ba59892a14103d15cce4ff93c85b5cc8b", "sha256_in_prefix": "652c878488d1456361e08c3f8607fd7ba59892a14103d15cce4ff93c85b5cc8b", "size_in_bytes": 185}, {"_path": "site-packages/sniffio-1.3.1.dist-info/LICENSE.APACHE2", "path_type": "hardlink", "sha256": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "sha256_in_prefix": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "size_in_bytes": 11358}, {"_path": "site-packages/sniffio-1.3.1.dist-info/LICENSE.MIT", "path_type": "hardlink", "sha256": "3e6dae555eb92787fc82d1d48355677f454c7f65aeb38d3f9e72bf9a3daf034b", "sha256_in_prefix": "3e6dae555eb92787fc82d1d48355677f454c7f65aeb38d3f9e72bf9a3daf034b", "size_in_bytes": 1046}, {"_path": "site-packages/sniffio-1.3.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "e87e83bd780638c08d4ae02ba8c36a89c6b55e2ea910d441aa20ad0b30e51fb6", "sha256_in_prefix": "e87e83bd780638c08d4ae02ba8c36a89c6b55e2ea910d441aa20ad0b30e51fb6", "size_in_bytes": 3771}, {"_path": "site-packages/sniffio-1.3.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "d1b7a695a2161c2b86d6340f4c8310fc430b394ac90ba0df5f14bdca84c3f8e0", "sha256_in_prefix": "d1b7a695a2161c2b86d6340f4c8310fc430b394ac90ba0df5f14bdca84c3f8e0", "size_in_bytes": 1567}, {"_path": "site-packages/sniffio-1.3.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/sniffio-1.3.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "3d9504c5d7fbd548bfb28ebb417a724ae1ed0a2dfe277c2f1783912ba93f4bc5", "sha256_in_prefix": "3d9504c5d7fbd548bfb28ebb417a724ae1ed0a2dfe277c2f1783912ba93f4bc5", "size_in_bytes": 91}, {"_path": "site-packages/sniffio-1.3.1.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "86391ca0f93f641aa820c023ce8a2faef4479cb9cc565e3a94edab31651bb1be", "sha256_in_prefix": "86391ca0f93f641aa820c023ce8a2faef4479cb9cc565e3a94edab31651bb1be", "size_in_bytes": 103}, {"_path": "site-packages/sniffio-1.3.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "bfd5095c6b390b275d095780a979108963aba69e96b71e86791acfb7dfa38c78", "sha256_in_prefix": "bfd5095c6b390b275d095780a979108963aba69e96b71e86791acfb7dfa38c78", "size_in_bytes": 8}, {"_path": "site-packages/sniffio/__init__.py", "path_type": "hardlink", "sha256": "f562442655eeef296e3f462d239490f4cf4e4d07db34791a75aacad6f5c60cf3", "sha256_in_prefix": "f562442655eeef296e3f462d239490f4cf4e4d07db34791a75aacad6f5c60cf3", "size_in_bytes": 335}, {"_path": "site-packages/sniffio/_impl.py", "path_type": "hardlink", "sha256": "526505319a62b8eadc8e7b8786e62260cc5e08d59faaef6406568f7f4c64e97f", "sha256_in_prefix": "526505319a62b8eadc8e7b8786e62260cc5e08d59faaef6406568f7f4c64e97f", "size_in_bytes": 2843}, {"_path": "site-packages/sniffio/_tests/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/sniffio/_tests/test_sniffio.py", "path_type": "hardlink", "sha256": "30c2596498d0ad48bde5100d34cf9aff9e4166ab80fe0bf8ac7535a5ebdc4c23", "sha256_in_prefix": "30c2596498d0ad48bde5100d34cf9aff9e4166ab80fe0bf8ac7535a5ebdc4c23", "size_in_bytes": 2058}, {"_path": "site-packages/sniffio/_version.py", "path_type": "hardlink", "sha256": "8957ace71c2c1de4734037b105a021cb1fed68db76b9c7c0ec2580a38403b7a4", "sha256_in_prefix": "8957ace71c2c1de4734037b105a021cb1fed68db76b9c7c0ec2580a38403b7a4", "size_in_bytes": 89}, {"_path": "site-packages/sniffio/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/sniffio/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/sniffio/__pycache__/_impl.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/sniffio/_tests/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/sniffio/_tests/__pycache__/test_sniffio.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/sniffio/__pycache__/_version.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "c2248418c310bdd1719b186796ae50a8a77ce555228b6acd32768e2543a15012", "size": 15019, "subdir": "noarch", "timestamp": 1733244175000, "url": "https://conda.anaconda.org/conda-forge/noarch/sniffio-1.3.1-pyhd8ed1ab_1.conda", "version": "1.3.1"}