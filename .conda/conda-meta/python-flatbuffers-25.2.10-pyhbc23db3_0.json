{"build": "pyhbc23db3_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.9"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/python-flatbuffers-25.2.10-pyhbc23db3_0", "files": ["lib/python3.11/site-packages/flatbuffers-25.2.10.dist-info/INSTALLER", "lib/python3.11/site-packages/flatbuffers-25.2.10.dist-info/METADATA", "lib/python3.11/site-packages/flatbuffers-25.2.10.dist-info/RECORD", "lib/python3.11/site-packages/flatbuffers-25.2.10.dist-info/REQUESTED", "lib/python3.11/site-packages/flatbuffers-25.2.10.dist-info/WHEEL", "lib/python3.11/site-packages/flatbuffers-25.2.10.dist-info/direct_url.json", "lib/python3.11/site-packages/flatbuffers-25.2.10.dist-info/top_level.txt", "lib/python3.11/site-packages/flatbuffers/__init__.py", "lib/python3.11/site-packages/flatbuffers/_version.py", "lib/python3.11/site-packages/flatbuffers/builder.py", "lib/python3.11/site-packages/flatbuffers/compat.py", "lib/python3.11/site-packages/flatbuffers/encode.py", "lib/python3.11/site-packages/flatbuffers/flexbuffers.py", "lib/python3.11/site-packages/flatbuffers/number_types.py", "lib/python3.11/site-packages/flatbuffers/packer.py", "lib/python3.11/site-packages/flatbuffers/table.py", "lib/python3.11/site-packages/flatbuffers/util.py", "lib/python3.11/site-packages/flatbuffers/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/flatbuffers/__pycache__/_version.cpython-311.pyc", "lib/python3.11/site-packages/flatbuffers/__pycache__/builder.cpython-311.pyc", "lib/python3.11/site-packages/flatbuffers/__pycache__/compat.cpython-311.pyc", "lib/python3.11/site-packages/flatbuffers/__pycache__/encode.cpython-311.pyc", "lib/python3.11/site-packages/flatbuffers/__pycache__/flexbuffers.cpython-311.pyc", "lib/python3.11/site-packages/flatbuffers/__pycache__/number_types.cpython-311.pyc", "lib/python3.11/site-packages/flatbuffers/__pycache__/packer.cpython-311.pyc", "lib/python3.11/site-packages/flatbuffers/__pycache__/table.cpython-311.pyc", "lib/python3.11/site-packages/flatbuffers/__pycache__/util.cpython-311.pyc"], "fn": "python-flatbuffers-25.2.10-pyhbc23db3_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/python-flatbuffers-25.2.10-pyhbc23db3_0", "type": 1}, "md5": "2c18ee679aa838a190eeaae5a14afc9e", "name": "python-flatbuffers", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/python-flatbuffers-25.2.10-pyhbc23db3_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/flatbuffers-25.2.10.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/flatbuffers-25.2.10.dist-info/METADATA", "path_type": "hardlink", "sha256": "46dcb69dbf2e024645f9b5ff35edf52936645f45b3eaa77d0a1cd0db7dc49a48", "sha256_in_prefix": "46dcb69dbf2e024645f9b5ff35edf52936645f45b3eaa77d0a1cd0db7dc49a48", "size_in_bytes": 1003}, {"_path": "site-packages/flatbuffers-25.2.10.dist-info/RECORD", "path_type": "hardlink", "sha256": "1fb8cae8186f12a762855e60f535a84a57a3c8b11e21be0cc4c50e26c874e036", "sha256_in_prefix": "1fb8cae8186f12a762855e60f535a84a57a3c8b11e21be0cc4c50e26c874e036", "size_in_bytes": 1923}, {"_path": "site-packages/flatbuffers-25.2.10.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/flatbuffers-25.2.10.dist-info/WHEEL", "path_type": "hardlink", "sha256": "f479b6381fa3d5070252af4982e86dedac8620864145374e5c3d6a83d70280f3", "sha256_in_prefix": "f479b6381fa3d5070252af4982e86dedac8620864145374e5c3d6a83d70280f3", "size_in_bytes": 109}, {"_path": "site-packages/flatbuffers-25.2.10.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "f971ed217c35f424d7b4efd7f18cda20d3fee10bd607fe79d4e2b28d414db3e7", "sha256_in_prefix": "f971ed217c35f424d7b4efd7f18cda20d3fee10bd607fe79d4e2b28d414db3e7", "size_in_bytes": 114}, {"_path": "site-packages/flatbuffers-25.2.10.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "5175562c0f32b3a1dea93cfaadf29eb28714aba967f992fc9a1642fdcab90447", "sha256_in_prefix": "5175562c0f32b3a1dea93cfaadf29eb28714aba967f992fc9a1642fdcab90447", "size_in_bytes": 12}, {"_path": "site-packages/flatbuffers/__init__.py", "path_type": "hardlink", "sha256": "abbb76ecdceeb76a959ee8cf211660ae4dc739b992db66133e6b96f79045e446", "sha256_in_prefix": "abbb76ecdceeb76a959ee8cf211660ae4dc739b992db66133e6b96f79045e446", "size_in_bytes": 751}, {"_path": "site-packages/flatbuffers/_version.py", "path_type": "hardlink", "sha256": "d665aa440cc2d4423924f3a7bf8737f0754388993f3422f4f2ef3631216a7086", "sha256_in_prefix": "d665aa440cc2d4423924f3a7bf8737f0754388993f3422f4f2ef3631216a7086", "size_in_bytes": 696}, {"_path": "site-packages/flatbuffers/builder.py", "path_type": "hardlink", "sha256": "93abde4a4a2c2af461815270bdaed5a4e8912b94ad100c70b44523955e7fbbd3", "sha256_in_prefix": "93abde4a4a2c2af461815270bdaed5a4e8912b94ad100c70b44523955e7fbbd3", "size_in_bytes": 27472}, {"_path": "site-packages/flatbuffers/compat.py", "path_type": "hardlink", "sha256": "db13fdc75a2f5fe21d3de6b21c8c2bfdd26942f270ba044f375c0ae6f8fb5ee5", "sha256_in_prefix": "db13fdc75a2f5fe21d3de6b21c8c2bfdd26942f270ba044f375c0ae6f8fb5ee5", "size_in_bytes": 2519}, {"_path": "site-packages/flatbuffers/encode.py", "path_type": "hardlink", "sha256": "48cac66f5cbcfc471c326e6f314d4f8e00216fdfcd1d3885923f3790d23b7678", "sha256_in_prefix": "48cac66f5cbcfc471c326e6f314d4f8e00216fdfcd1d3885923f3790d23b7678", "size_in_bytes": 1581}, {"_path": "site-packages/flatbuffers/flexbuffers.py", "path_type": "hardlink", "sha256": "1665c42ed4ef59d7f08a0b3a218886a7d5f26be40389463851ce20e23bf402e6", "sha256_in_prefix": "1665c42ed4ef59d7f08a0b3a218886a7d5f26be40389463851ce20e23bf402e6", "size_in_bytes": 44275}, {"_path": "site-packages/flatbuffers/number_types.py", "path_type": "hardlink", "sha256": "f082ae2a8f36dafe2042f50e3bf0830ae25f83876218138b3818f990b57b74fa", "sha256_in_prefix": "f082ae2a8f36dafe2042f50e3bf0830ae25f83876218138b3818f990b57b74fa", "size_in_bytes": 3955}, {"_path": "site-packages/flatbuffers/packer.py", "path_type": "hardlink", "sha256": "a70df2fcd33e58e11c6df4fa373002b3353d3cf1124180d969ba1f811c6c8fe8", "sha256_in_prefix": "a70df2fcd33e58e11c6df4fa373002b3353d3cf1124180d969ba1f811c6c8fe8", "size_in_bytes": 1165}, {"_path": "site-packages/flatbuffers/table.py", "path_type": "hardlink", "sha256": "f0e99e922a84cb043fa4ca93ed0e144c9c8c094f1e5610012a996b29208d4219", "sha256_in_prefix": "f0e99e922a84cb043fa4ca93ed0e144c9c8c094f1e5610012a996b29208d4219", "size_in_bytes": 5170}, {"_path": "site-packages/flatbuffers/util.py", "path_type": "hardlink", "sha256": "21bd5c4b1f5fb2017d058f388989744e38fba9fb4d867888ed9116c89c2f6461", "sha256_in_prefix": "21bd5c4b1f5fb2017d058f388989744e38fba9fb4d867888ed9116c89c2f6461", "size_in_bytes": 1669}, {"_path": "lib/python3.11/site-packages/flatbuffers/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/flatbuffers/__pycache__/_version.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/flatbuffers/__pycache__/builder.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/flatbuffers/__pycache__/compat.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/flatbuffers/__pycache__/encode.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/flatbuffers/__pycache__/flexbuffers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/flatbuffers/__pycache__/number_types.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/flatbuffers/__pycache__/packer.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/flatbuffers/__pycache__/table.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/flatbuffers/__pycache__/util.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "0d1ebed2c296e11f15b53cb97c7a8222d597658f76e12559c6b509f604b72056", "size": 34490, "subdir": "noarch", "timestamp": 1739279336000, "url": "https://conda.anaconda.org/conda-forge/noarch/python-flatbuffers-25.2.10-pyhbc23db3_0.conda", "version": "25.2.10"}