{"build": "h7a4e982_2", "build_number": 2, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13", "aws-c-common >=0.12.4,<0.12.5.0a0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/aws-checksums-0.2.7-h7a4e982_2", "files": ["bin/checksum-profile", "include/aws/checksums/checksums.h", "include/aws/checksums/crc.h", "include/aws/checksums/exports.h", "lib/cmake/aws-checksums/aws-checksums-config.cmake", "lib/cmake/aws-checksums/shared/aws-checksums-targets-release.cmake", "lib/cmake/aws-checksums/shared/aws-checksums-targets.cmake", "lib/libaws-checksums.1.0.0.dylib", "lib/libaws-checksums.dylib"], "fn": "aws-checksums-0.2.7-h7a4e982_2.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/aws-checksums-0.2.7-h7a4e982_2", "type": 1}, "md5": "a8a7aa3088b1310cebbc4777f887bd80", "name": "aws-checksums", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/aws-checksums-0.2.7-h7a4e982_2.conda", "paths_data": {"paths": [{"_path": "bin/checksum-profile", "path_type": "hardlink", "sha256": "4a7cec6f4dfc7f3f1578194b2eac4427423f0b0ce1b8d1b3213bede0c86c2401", "sha256_in_prefix": "4a7cec6f4dfc7f3f1578194b2eac4427423f0b0ce1b8d1b3213bede0c86c2401", "size_in_bytes": 15632}, {"_path": "include/aws/checksums/checksums.h", "path_type": "hardlink", "sha256": "bde060cce0b390856fde03d3929f82919c1b5491d9ac7fcf0aeac2fc4052a74b", "sha256_in_prefix": "bde060cce0b390856fde03d3929f82919c1b5491d9ac7fcf0aeac2fc4052a74b", "size_in_bytes": 913}, {"_path": "include/aws/checksums/crc.h", "path_type": "hardlink", "sha256": "c925acf3063376c6bd1ad6c708eb33dde9ab048d233f3b8e3a131a461fa4462b", "sha256_in_prefix": "c925acf3063376c6bd1ad6c708eb33dde9ab048d233f3b8e3a131a461fa4462b", "size_in_bytes": 3320}, {"_path": "include/aws/checksums/exports.h", "path_type": "hardlink", "sha256": "6d6d2202b28d993981927e36ee50774e39b4b4ecfa6460c4f473aa6134c6544f", "sha256_in_prefix": "6d6d2202b28d993981927e36ee50774e39b4b4ecfa6460c4f473aa6134c6544f", "size_in_bytes": 1005}, {"_path": "lib/cmake/aws-checksums/aws-checksums-config.cmake", "path_type": "hardlink", "sha256": "ac1243406c196f025ef849fc9bb322194c13ba1c54cd86bf1e4eac984b876b80", "sha256_in_prefix": "ac1243406c196f025ef849fc9bb322194c13ba1c54cd86bf1e4eac984b876b80", "size_in_bytes": 506}, {"_path": "lib/cmake/aws-checksums/shared/aws-checksums-targets-release.cmake", "path_type": "hardlink", "sha256": "99de17647ade59d7af623da9e9499ea9d5c461bae6e520f4d9f2408c4c8ce6f2", "sha256_in_prefix": "99de17647ade59d7af623da9e9499ea9d5c461bae6e520f4d9f2408c4c8ce6f2", "size_in_bytes": 911}, {"_path": "lib/cmake/aws-checksums/shared/aws-checksums-targets.cmake", "path_type": "hardlink", "sha256": "e084d69957954d5e4ef139935db293ecd6b94e5ed20c50bc1d55a0eb83add81b", "sha256_in_prefix": "e084d69957954d5e4ef139935db293ecd6b94e5ed20c50bc1d55a0eb83add81b", "size_in_bytes": 4321}, {"_path": "lib/libaws-checksums.1.0.0.dylib", "path_type": "hardlink", "sha256": "62fc3dd029bec6b11b5f5ca0935262ec7e4e01df4cc15dfe0494d1ce1001bb4d", "sha256_in_prefix": "62fc3dd029bec6b11b5f5ca0935262ec7e4e01df4cc15dfe0494d1ce1001bb4d", "size_in_bytes": 73648}, {"_path": "lib/libaws-checksums.dylib", "path_type": "softlink", "sha256": "62fc3dd029bec6b11b5f5ca0935262ec7e4e01df4cc15dfe0494d1ce1001bb4d", "size_in_bytes": 28}], "paths_version": 1}, "requested_spec": "None", "sha256": "523e5d6ffb58a333c6e4501e18120b53290ddad1f879e72ac7f58b15b505f92a", "size": 75320, "subdir": "osx-64", "timestamp": 1752241080000, "url": "https://conda.anaconda.org/conda-forge/osx-64/aws-checksums-0.2.7-h7a4e982_2.conda", "version": "0.2.7"}