{"build": "h7a4e982_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13", "aws-c-common >=0.12.4,<0.12.5.0a0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/aws-c-sdkutils-0.2.4-h7a4e982_1", "files": ["include/aws/sdkutils/aws_profile.h", "include/aws/sdkutils/endpoints_rule_engine.h", "include/aws/sdkutils/exports.h", "include/aws/sdkutils/partitions.h", "include/aws/sdkutils/resource_name.h", "include/aws/sdkutils/sdkutils.h", "lib/cmake/aws-c-sdkutils/aws-c-sdkutils-config.cmake", "lib/cmake/aws-c-sdkutils/shared/aws-c-sdkutils-targets-release.cmake", "lib/cmake/aws-c-sdkutils/shared/aws-c-sdkutils-targets.cmake", "lib/libaws-c-sdkutils.1.0.0.dylib", "lib/libaws-c-sdkutils.dylib"], "fn": "aws-c-sdkutils-0.2.4-h7a4e982_1.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/aws-c-sdkutils-0.2.4-h7a4e982_1", "type": 1}, "md5": "9ab61d370fc6e4caeb5525ef92e2d477", "name": "aws-c-sdkutils", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/aws-c-sdkutils-0.2.4-h7a4e982_1.conda", "paths_data": {"paths": [{"_path": "include/aws/sdkutils/aws_profile.h", "path_type": "hardlink", "sha256": "60278c0ee32e734bb33a827cdf9d32e74d4a6e9e35a4bb14c42ec1f73746eaf9", "sha256_in_prefix": "60278c0ee32e734bb33a827cdf9d32e74d4a6e9e35a4bb14c42ec1f73746eaf9", "size_in_bytes": 7027}, {"_path": "include/aws/sdkutils/endpoints_rule_engine.h", "path_type": "hardlink", "sha256": "ad4a139de7e8969c5bfc916a00d26721affb4edadd62295910628b3d230e0c07", "sha256_in_prefix": "ad4a139de7e8969c5bfc916a00d26721affb4edadd62295910628b3d230e0c07", "size_in_bytes": 11674}, {"_path": "include/aws/sdkutils/exports.h", "path_type": "hardlink", "sha256": "01c359821212668861aea0edf4fa5021519bd2b58b72043425a449fed6e8092f", "sha256_in_prefix": "01c359821212668861aea0edf4fa5021519bd2b58b72043425a449fed6e8092f", "size_in_bytes": 904}, {"_path": "include/aws/sdkutils/partitions.h", "path_type": "hardlink", "sha256": "4795ad4deec1f78e28dd54eaee5d2df1941b7feb8f4fbcc41b4b118d27ea65cb", "sha256_in_prefix": "4795ad4deec1f78e28dd54eaee5d2df1941b7feb8f4fbcc41b4b118d27ea65cb", "size_in_bytes": 1088}, {"_path": "include/aws/sdkutils/resource_name.h", "path_type": "hardlink", "sha256": "5c354e658111734d1342f5d6f22875cb40a37c9a22e5c00dcfcb52c595cd84cf", "sha256_in_prefix": "5c354e658111734d1342f5d6f22875cb40a37c9a22e5c00dcfcb52c595cd84cf", "size_in_bytes": 1226}, {"_path": "include/aws/sdkutils/sdkutils.h", "path_type": "hardlink", "sha256": "a5526ed98f91d10721616d1d99c90adfa85c910392bb2e191cb7ea2ad5ed70c9", "sha256_in_prefix": "a5526ed98f91d10721616d1d99c90adfa85c910392bb2e191cb7ea2ad5ed70c9", "size_in_bytes": 1827}, {"_path": "lib/cmake/aws-c-sdkutils/aws-c-sdkutils-config.cmake", "path_type": "hardlink", "sha256": "11c892bb4fafbe05d49d3e806e9e561831a3f3ca491fa56b232617cc47e7c644", "sha256_in_prefix": "11c892bb4fafbe05d49d3e806e9e561831a3f3ca491fa56b232617cc47e7c644", "size_in_bytes": 573}, {"_path": "lib/cmake/aws-c-sdkutils/shared/aws-c-sdkutils-targets-release.cmake", "path_type": "hardlink", "sha256": "9a526c82a350941d0e569ca74377154cc86f5989bdec72580e1d434917764a53", "sha256_in_prefix": "9a526c82a350941d0e569ca74377154cc86f5989bdec72580e1d434917764a53", "size_in_bytes": 919}, {"_path": "lib/cmake/aws-c-sdkutils/shared/aws-c-sdkutils-targets.cmake", "path_type": "hardlink", "sha256": "7aa91816f4c367b2dd59050cebde1fe8d6adc01aed658ec7c0c22d76cb20643c", "sha256_in_prefix": "7aa91816f4c367b2dd59050cebde1fe8d6adc01aed658ec7c0c22d76cb20643c", "size_in_bytes": 4325}, {"_path": "lib/libaws-c-sdkutils.1.0.0.dylib", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/bld/rattler-build_aws-c-sdkutils_1752240983/host_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeho", "sha256": "f475b57e880e907785ae4d9a7bcb8efaab23967d4d14d8275ac34f6eef73abb6", "sha256_in_prefix": "22ba8f6d3c6adb60f3ab938292060a4cd7b8ab2da98cda0ad71375670c2c8676", "size_in_bytes": 104288}, {"_path": "lib/libaws-c-sdkutils.dylib", "path_type": "softlink", "sha256": "f475b57e880e907785ae4d9a7bcb8efaab23967d4d14d8275ac34f6eef73abb6", "size_in_bytes": 29}], "paths_version": 1}, "requested_spec": "None", "sha256": "85d1b9eb67e02f6a622dcc0c854683da8ccd059d59b80a1ffa7f927eac771b93", "size": 55375, "subdir": "osx-64", "timestamp": 1752240983000, "url": "https://conda.anaconda.org/conda-forge/osx-64/aws-c-sdkutils-0.2.4-h7a4e982_1.conda", "version": "0.2.4"}