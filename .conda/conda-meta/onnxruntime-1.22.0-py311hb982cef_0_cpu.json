{"build": "py311hb982cef_0_cpu", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.15", "coloredlogs", "libcxx >=18", "numpy >=1.19,<3", "packaging", "protobuf", "python >=3.11,<3.12.0a0", "python-flatbuffers", "python_abi 3.11.* *_cp311", "sympy"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/onnxruntime-1.22.0-py311hb982cef_0_cpu", "files": ["bin/onnxruntime_test", "lib/python3.11/site-packages/onnxruntime-1.22.0.dist-info/INSTALLER", "lib/python3.11/site-packages/onnxruntime-1.22.0.dist-info/METADATA", "lib/python3.11/site-packages/onnxruntime-1.22.0.dist-info/RECORD", "lib/python3.11/site-packages/onnxruntime-1.22.0.dist-info/REQUESTED", "lib/python3.11/site-packages/onnxruntime-1.22.0.dist-info/WHEEL", "lib/python3.11/site-packages/onnxruntime-1.22.0.dist-info/direct_url.json", "lib/python3.11/site-packages/onnxruntime-1.22.0.dist-info/entry_points.txt", "lib/python3.11/site-packages/onnxruntime-1.22.0.dist-info/top_level.txt", "lib/python3.11/site-packages/onnxruntime/LICENSE", "lib/python3.11/site-packages/onnxruntime/Privacy.md", "lib/python3.11/site-packages/onnxruntime/ThirdPartyNotices.txt", "lib/python3.11/site-packages/onnxruntime/__init__.py", "lib/python3.11/site-packages/onnxruntime/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/backend/__init__.py", "lib/python3.11/site-packages/onnxruntime/backend/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/backend/__pycache__/backend.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/backend/__pycache__/backend_rep.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/backend/backend.py", "lib/python3.11/site-packages/onnxruntime/backend/backend_rep.py", "lib/python3.11/site-packages/onnxruntime/capi/__init__.py", "lib/python3.11/site-packages/onnxruntime/capi/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/capi/__pycache__/_ld_preload.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/capi/__pycache__/_pybind_state.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/capi/__pycache__/build_and_package_info.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/capi/__pycache__/convert_npz_to_onnx_adapter.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/capi/__pycache__/onnxruntime_collect_build_info.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/capi/__pycache__/onnxruntime_inference_collection.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/capi/__pycache__/onnxruntime_validation.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/capi/_ld_preload.py", "lib/python3.11/site-packages/onnxruntime/capi/_pybind_state.py", "lib/python3.11/site-packages/onnxruntime/capi/build_and_package_info.py", "lib/python3.11/site-packages/onnxruntime/capi/convert_npz_to_onnx_adapter.py", "lib/python3.11/site-packages/onnxruntime/capi/libonnxruntime.1.22.0.dylib", "lib/python3.11/site-packages/onnxruntime/capi/onnxruntime_collect_build_info.py", "lib/python3.11/site-packages/onnxruntime/capi/onnxruntime_inference_collection.py", "lib/python3.11/site-packages/onnxruntime/capi/onnxruntime_pybind11_state.so", "lib/python3.11/site-packages/onnxruntime/capi/onnxruntime_validation.py", "lib/python3.11/site-packages/onnxruntime/datasets/__init__.py", "lib/python3.11/site-packages/onnxruntime/datasets/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/datasets/logreg_iris.onnx", "lib/python3.11/site-packages/onnxruntime/datasets/mul_1.onnx", "lib/python3.11/site-packages/onnxruntime/datasets/sigmoid.onnx", "lib/python3.11/site-packages/onnxruntime/quantization/CalTableFlatBuffers/KeyValue.py", "lib/python3.11/site-packages/onnxruntime/quantization/CalTableFlatBuffers/TrtTable.py", "lib/python3.11/site-packages/onnxruntime/quantization/CalTableFlatBuffers/__init__.py", "lib/python3.11/site-packages/onnxruntime/quantization/CalTableFlatBuffers/__pycache__/KeyValue.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/CalTableFlatBuffers/__pycache__/TrtTable.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/CalTableFlatBuffers/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/__init__.py", "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/base_quantizer.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/calibrate.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/matmul_bnb4_quantizer.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/matmul_nbits_quantizer.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/onnx_model.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/onnx_quantizer.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/preprocess.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/qdq_loss_debug.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/qdq_quantizer.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/quant_utils.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/quantize.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/registry.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/shape_inference.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/static_quantize_runner.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/tensor_quant_overrides.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/base_quantizer.py", "lib/python3.11/site-packages/onnxruntime/quantization/calibrate.py", "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/__init__.py", "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/__pycache__/fusion_lpnorm.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/__pycache__/mixed_precision_overrides_utils.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/__pycache__/preprocess.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/__pycache__/quant_config.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/fusion_lpnorm.py", "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/mixed_precision_overrides_utils.py", "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/preprocess.py", "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/quant_config.py", "lib/python3.11/site-packages/onnxruntime/quantization/fusions/__init__.py", "lib/python3.11/site-packages/onnxruntime/quantization/fusions/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/fusions/__pycache__/fusion.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/fusions/__pycache__/fusion_gelu.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/fusions/__pycache__/fusion_layernorm.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/fusions/fusion.py", "lib/python3.11/site-packages/onnxruntime/quantization/fusions/fusion_gelu.py", "lib/python3.11/site-packages/onnxruntime/quantization/fusions/fusion_layernorm.py", "lib/python3.11/site-packages/onnxruntime/quantization/matmul_bnb4_quantizer.py", "lib/python3.11/site-packages/onnxruntime/quantization/matmul_nbits_quantizer.py", "lib/python3.11/site-packages/onnxruntime/quantization/onnx_model.py", "lib/python3.11/site-packages/onnxruntime/quantization/onnx_quantizer.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__init__.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/activation.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/argmax.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/attention.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/base_operator.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/binary_op.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/concat.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/conv.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/direct_q8.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/embed_layernorm.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/gather.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/gavgpool.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/gemm.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/lstm.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/matmul.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/maxpool.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/norm.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/pad.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/pooling.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/qdq_base_operator.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/resize.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/softmax.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/split.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/where.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/quantization/operators/activation.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/argmax.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/attention.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/base_operator.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/binary_op.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/concat.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/conv.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/direct_q8.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/embed_layernorm.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/gather.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/gavgpool.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/gemm.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/lstm.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/matmul.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/maxpool.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/norm.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/pad.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/pooling.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/qdq_base_operator.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/resize.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/softmax.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/split.py", "lib/python3.11/site-packages/onnxruntime/quantization/operators/where.py", "lib/python3.11/site-packages/onnxruntime/quantization/preprocess.py", "lib/python3.11/site-packages/onnxruntime/quantization/qdq_loss_debug.py", "lib/python3.11/site-packages/onnxruntime/quantization/qdq_quantizer.py", "lib/python3.11/site-packages/onnxruntime/quantization/quant_utils.py", "lib/python3.11/site-packages/onnxruntime/quantization/quantize.py", "lib/python3.11/site-packages/onnxruntime/quantization/registry.py", "lib/python3.11/site-packages/onnxruntime/quantization/shape_inference.py", "lib/python3.11/site-packages/onnxruntime/quantization/static_quantize_runner.py", "lib/python3.11/site-packages/onnxruntime/quantization/tensor_quant_overrides.py", "lib/python3.11/site-packages/onnxruntime/tools/__init__.py", "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/check_onnx_model_mobile_usability.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/convert_onnx_models_to_ort.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/file_utils.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/logger.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/make_dynamic_shape_fixed.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/offline_tuning.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/onnx_model_utils.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/onnx_randomizer.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/onnxruntime_test.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/optimize_onnx_model.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/pytorch_export_contrib_ops.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/pytorch_export_helpers.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/reduced_build_config_parser.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/symbolic_shape_infer.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/update_onnx_opset.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/check_onnx_model_mobile_usability.py", "lib/python3.11/site-packages/onnxruntime/tools/convert_onnx_models_to_ort.py", "lib/python3.11/site-packages/onnxruntime/tools/file_utils.py", "lib/python3.11/site-packages/onnxruntime/tools/logger.py", "lib/python3.11/site-packages/onnxruntime/tools/make_dynamic_shape_fixed.py", "lib/python3.11/site-packages/onnxruntime/tools/mobile_helpers/__init__.py", "lib/python3.11/site-packages/onnxruntime/tools/mobile_helpers/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/mobile_helpers/__pycache__/usability_checker.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/mobile_helpers/coreml_supported_mlprogram_ops.md", "lib/python3.11/site-packages/onnxruntime/tools/mobile_helpers/coreml_supported_neuralnetwork_ops.md", "lib/python3.11/site-packages/onnxruntime/tools/mobile_helpers/nnapi_supported_ops.md", "lib/python3.11/site-packages/onnxruntime/tools/mobile_helpers/usability_checker.py", "lib/python3.11/site-packages/onnxruntime/tools/offline_tuning.py", "lib/python3.11/site-packages/onnxruntime/tools/onnx_model_utils.py", "lib/python3.11/site-packages/onnxruntime/tools/onnx_randomizer.py", "lib/python3.11/site-packages/onnxruntime/tools/onnxruntime_test.py", "lib/python3.11/site-packages/onnxruntime/tools/optimize_onnx_model.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/__init__.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/__pycache__/operator_type_usage_processors.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/__pycache__/ort_model_processor.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/__pycache__/types.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/operator_type_usage_processors.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/__init__.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ArgType.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ArgTypeAndIndex.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Attribute.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/AttributeType.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Checkpoint.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedKernelCreateInfos.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedNodeIndexAndKernelDefHash.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedSessionState.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedSubGraphSessionState.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Dimension.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DimensionValue.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DimensionValueType.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/EdgeEnd.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/FloatProperty.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Graph.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/InferenceSession.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/IntProperty.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/KernelTypeStrArgsEntry.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/KernelTypeStrResolver.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/MapType.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Model.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ModuleState.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Node.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodeEdge.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodeType.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodesToOptimizeIndices.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OpIdKernelTypeStrArgsEntry.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OperatorSetId.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OptimizerGroup.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ParameterOptimizerState.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/PropertyBag.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizationRecord.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizationRecordContainerEntry.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizations.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/SequenceType.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Shape.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/SparseTensor.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/StringProperty.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/StringStringEntry.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Tensor.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TensorDataType.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TensorTypeAndShape.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TypeInfo.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TypeInfoValue.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ValueInfo.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__init__.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ArgType.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ArgTypeAndIndex.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Attribute.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/AttributeType.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Checkpoint.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedKernelCreateInfos.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedNodeIndexAndKernelDefHash.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedSessionState.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedSubGraphSessionState.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Dimension.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DimensionValue.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DimensionValueType.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/EdgeEnd.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/FloatProperty.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Graph.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/InferenceSession.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/IntProperty.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/KernelTypeStrArgsEntry.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/KernelTypeStrResolver.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/MapType.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Model.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ModuleState.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Node.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/NodeEdge.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/NodeType.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/NodesToOptimizeIndices.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/OpIdKernelTypeStrArgsEntry.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/OperatorSetId.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/OptimizerGroup.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ParameterOptimizerState.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/PropertyBag.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/RuntimeOptimizationRecord.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/RuntimeOptimizationRecordContainerEntry.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/RuntimeOptimizations.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/SequenceType.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Shape.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/SparseTensor.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/StringProperty.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/StringStringEntry.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Tensor.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TensorDataType.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TensorTypeAndShape.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TypeInfo.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TypeInfoValue.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ValueInfo.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_model_processor.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/types.py", "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/utils.py", "lib/python3.11/site-packages/onnxruntime/tools/pytorch_export_contrib_ops.py", "lib/python3.11/site-packages/onnxruntime/tools/pytorch_export_helpers.py", "lib/python3.11/site-packages/onnxruntime/tools/qdq_helpers/__init__.py", "lib/python3.11/site-packages/onnxruntime/tools/qdq_helpers/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/qdq_helpers/__pycache__/optimize_qdq_model.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/tools/qdq_helpers/optimize_qdq_model.py", "lib/python3.11/site-packages/onnxruntime/tools/reduced_build_config_parser.py", "lib/python3.11/site-packages/onnxruntime/tools/symbolic_shape_infer.py", "lib/python3.11/site-packages/onnxruntime/tools/update_onnx_opset.py", "lib/python3.11/site-packages/onnxruntime/transformers/__init__.py", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/affinity_helper.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/benchmark.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/benchmark_helper.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/bert_perf_test.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/bert_test_data.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/compare_bert_results.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/constants.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/convert_generation.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/convert_tf_models_to_pytorch.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/convert_to_packing_mode.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/dynamo_onnx_helper.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/float16.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_attention.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_attention_clip.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_attention_sam2.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_attention_unet.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_attention_vae.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_bart_attention.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_base.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_bias_add.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_biasgelu.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_biassplitgelu.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_conformer_attention.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_constant_fold.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_embedlayer.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_fastgelu.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_gelu.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_gelu_approximation.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_gemmfastgelu.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_gpt_attention.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_gpt_attention_megatron.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_gpt_attention_no_past.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_group_norm.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_layernorm.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_mha_mmdit.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_nhwc_conv.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_options.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_qordered_attention.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_qordered_gelu.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_qordered_layernorm.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_qordered_matmul.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_quickgelu.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_reshape.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_rotary_attention.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_shape.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_simplified_layernorm.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_skip_group_norm.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_skiplayernorm.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_transpose.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_utils.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/huggingface_models.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/import_utils.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/io_binding_helper.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/large_model_exporter.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/machine_info.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/metrics.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_exporter.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_bart.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_bert.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_bert_keras.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_bert_tf.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_clip.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_conformer.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_gpt2.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_mmdit.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_phi.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_sam2.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_t5.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_tnlr.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_unet.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_vae.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_utils.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/optimizer.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/past_helper.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/profile_result_processor.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/profiler.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/quantize_helper.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/shape_infer_helper.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/shape_optimizer.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/torch_onnx_export_helper.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/affinity_helper.py", "lib/python3.11/site-packages/onnxruntime/transformers/benchmark.py", "lib/python3.11/site-packages/onnxruntime/transformers/benchmark_helper.py", "lib/python3.11/site-packages/onnxruntime/transformers/bert_perf_test.py", "lib/python3.11/site-packages/onnxruntime/transformers/bert_test_data.py", "lib/python3.11/site-packages/onnxruntime/transformers/compare_bert_results.py", "lib/python3.11/site-packages/onnxruntime/transformers/constants.py", "lib/python3.11/site-packages/onnxruntime/transformers/convert_generation.py", "lib/python3.11/site-packages/onnxruntime/transformers/convert_tf_models_to_pytorch.py", "lib/python3.11/site-packages/onnxruntime/transformers/convert_to_packing_mode.py", "lib/python3.11/site-packages/onnxruntime/transformers/dynamo_onnx_helper.py", "lib/python3.11/site-packages/onnxruntime/transformers/float16.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_attention.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_attention_clip.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_attention_sam2.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_attention_unet.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_attention_vae.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_bart_attention.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_base.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_bias_add.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_biasgelu.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_biassplitgelu.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_conformer_attention.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_constant_fold.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_embedlayer.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_fastgelu.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_gelu.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_gelu_approximation.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_gemmfastgelu.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_gpt_attention.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_gpt_attention_megatron.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_gpt_attention_no_past.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_group_norm.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_layernorm.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_mha_mmdit.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_nhwc_conv.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_options.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_qordered_attention.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_qordered_gelu.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_qordered_layernorm.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_qordered_matmul.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_quickgelu.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_reshape.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_rotary_attention.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_shape.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_simplified_layernorm.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_skip_group_norm.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_skiplayernorm.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_transpose.py", "lib/python3.11/site-packages/onnxruntime/transformers/fusion_utils.py", "lib/python3.11/site-packages/onnxruntime/transformers/huggingface_models.py", "lib/python3.11/site-packages/onnxruntime/transformers/import_utils.py", "lib/python3.11/site-packages/onnxruntime/transformers/io_binding_helper.py", "lib/python3.11/site-packages/onnxruntime/transformers/large_model_exporter.py", "lib/python3.11/site-packages/onnxruntime/transformers/machine_info.py", "lib/python3.11/site-packages/onnxruntime/transformers/metrics.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/bart/__init__.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/bart/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/bart/__pycache__/export.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/bart/export.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/bert/__init__.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/bert/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/bert/__pycache__/eval_squad.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/bert/eval_squad.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/__init__.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/__pycache__/benchmark_gpt2.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/__pycache__/convert_to_onnx.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/__pycache__/gpt2_helper.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/__pycache__/gpt2_parity.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/__pycache__/gpt2_tester.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/__pycache__/parity_check_helper.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/benchmark_gpt2.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/convert_to_onnx.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/gpt2_helper.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/gpt2_parity.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/gpt2_tester.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/parity_check_helper.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__init__.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/benchmark.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/benchmark_all.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/benchmark_e2e.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/convert_to_onnx.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/dist_settings.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/llama_inputs.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/llama_parity.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/llama_torch.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/quant_kv_dataloader.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/benchmark.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/benchmark_all.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/benchmark_e2e.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/convert_to_onnx.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/dist_settings.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/llama_inputs.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/llama_parity.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/llama_torch.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/quant_kv_dataloader.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/__init__.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/__pycache__/benchmark_longformer.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/__pycache__/convert_to_onnx.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/__pycache__/generate_test_data.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/__pycache__/longformer_helper.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/benchmark_longformer.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/convert_to_onnx.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/generate_test_data.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/longformer_helper.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/phi2/__init__.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/phi2/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/phi2/__pycache__/convert_to_onnx.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/phi2/__pycache__/inference_example.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/phi2/convert_to_onnx.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/phi2/inference_example.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__init__.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/benchmark_sam2.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/convert_to_onnx.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/image_decoder.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/image_encoder.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/mask_decoder.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/nvtx_helper.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/prompt_encoder.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/sam2_demo.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/sam2_image_onnx_predictor.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/sam2_utils.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/benchmark_sam2.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/convert_to_onnx.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/image_decoder.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/image_encoder.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/mask_decoder.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/nvtx_helper.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/prompt_encoder.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/sam2_demo.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/sam2_image_onnx_predictor.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/sam2_utils.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__init__.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/benchmark.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/benchmark_controlnet.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/demo_txt2img.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/demo_txt2img_xl.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/demo_utils.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/diffusion_models.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/diffusion_schedulers.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_ort_cuda.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_ort_trt.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_tensorrt.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_torch.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/optimize_pipeline.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/ort_optimizer.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/pipeline_stable_diffusion.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/trt_utilities.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/benchmark.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/benchmark_controlnet.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/demo_txt2img.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/demo_txt2img_xl.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/demo_utils.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/diffusion_models.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/diffusion_schedulers.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/engine_builder.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/engine_builder_ort_cuda.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/engine_builder_ort_trt.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/engine_builder_tensorrt.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/engine_builder_torch.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/optimize_pipeline.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/ort_optimizer.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/pipeline_stable_diffusion.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/trt_utilities.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/__init__.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/__pycache__/convert_to_onnx.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/__pycache__/t5_decoder.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/__pycache__/t5_encoder.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/__pycache__/t5_encoder_decoder_init.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/__pycache__/t5_helper.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/convert_to_onnx.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/t5_decoder.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/t5_encoder.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/t5_encoder_decoder_init.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/t5_helper.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__init__.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/benchmark.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/benchmark_all.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/convert_to_onnx.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/whisper_chain.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/whisper_decoder.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/whisper_encoder.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/whisper_encoder_decoder_init.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/whisper_helper.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/whisper_inputs.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/whisper_jump_times.cpython-311.pyc", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/benchmark.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/benchmark_all.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/convert_to_onnx.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/whisper_chain.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/whisper_decoder.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/whisper_encoder.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/whisper_encoder_decoder_init.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/whisper_helper.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/whisper_inputs.py", "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/whisper_jump_times.py", "lib/python3.11/site-packages/onnxruntime/transformers/onnx_exporter.py", "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model.py", "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_bart.py", "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_bert.py", "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_bert_keras.py", "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_bert_tf.py", "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_clip.py", "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_conformer.py", "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_gpt2.py", "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_mmdit.py", "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_phi.py", "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_sam2.py", "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_t5.py", "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_tnlr.py", "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_unet.py", "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_vae.py", "lib/python3.11/site-packages/onnxruntime/transformers/onnx_utils.py", "lib/python3.11/site-packages/onnxruntime/transformers/optimizer.py", "lib/python3.11/site-packages/onnxruntime/transformers/past_helper.py", "lib/python3.11/site-packages/onnxruntime/transformers/profile_result_processor.py", "lib/python3.11/site-packages/onnxruntime/transformers/profiler.py", "lib/python3.11/site-packages/onnxruntime/transformers/quantize_helper.py", "lib/python3.11/site-packages/onnxruntime/transformers/shape_infer_helper.py", "lib/python3.11/site-packages/onnxruntime/transformers/shape_optimizer.py", "lib/python3.11/site-packages/onnxruntime/transformers/torch_onnx_export_helper.py"], "fn": "onnxruntime-1.22.0-py311hb982cef_0_cpu.conda", "license": "MIT AND BSL-1.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/onnxruntime-1.22.0-py311hb982cef_0_cpu", "type": 1}, "md5": "208bfab01b5f7067e66403b261a3824a", "name": "onnxruntime", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/onnxruntime-1.22.0-py311hb982cef_0_cpu.conda", "paths_data": {"paths": [{"_path": "bin/onnxruntime_test", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/onnxruntime_1746973159648/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeho", "sha256": "f1411b44794aa0b413ff3921c55c379ac074459a897f252ff52482d9b41214ae", "sha256_in_prefix": "4ed32cc650e15bc56978510a512969073fa669cc4019851631f98b97edaaf8fa", "size_in_bytes": 487}, {"_path": "lib/python3.11/site-packages/onnxruntime-1.22.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.11/site-packages/onnxruntime-1.22.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "6d92e8719b981b9a01984a64178504164276fae84ca2171ced2f7221ff5abd63", "sha256_in_prefix": "6d92e8719b981b9a01984a64178504164276fae84ca2171ced2f7221ff5abd63", "size_in_bytes": 4769}, {"_path": "lib/python3.11/site-packages/onnxruntime-1.22.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "d33ac1a61f1323d3e614285d9b51676c8ff64764f536b5ba54db3690d4ea7ecd", "sha256_in_prefix": "d33ac1a61f1323d3e614285d9b51676c8ff64764f536b5ba54db3690d4ea7ecd", "size_in_bytes": 58416}, {"_path": "lib/python3.11/site-packages/onnxruntime-1.22.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/onnxruntime-1.22.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "a1840f956db78a480939140530c7e650f94dd2355f974e69088bac3e79223476", "sha256_in_prefix": "a1840f956db78a480939140530c7e650f94dd2355f974e69088bac3e79223476", "size_in_bytes": 111}, {"_path": "lib/python3.11/site-packages/onnxruntime-1.22.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "3867c11c13d3fe479eaa5dff7bbe9b1a6d11343baa75b985c8864d8a7853be84", "sha256_in_prefix": "3867c11c13d3fe479eaa5dff7bbe9b1a6d11343baa75b985c8864d8a7853be84", "size_in_bytes": 351}, {"_path": "lib/python3.11/site-packages/onnxruntime-1.22.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "eea2d2e056c65f03d98df7695401a99e693d23a9ba1f90b1127c1c0b1d489a3b", "sha256_in_prefix": "eea2d2e056c65f03d98df7695401a99e693d23a9ba1f90b1127c1c0b1d489a3b", "size_in_bytes": 77}, {"_path": "lib/python3.11/site-packages/onnxruntime-1.22.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "ce4fdf2447a4ad39bd0cbc57d8bc067a0a2456a3fa6e5a8f845a0c22e87436ff", "sha256_in_prefix": "ce4fdf2447a4ad39bd0cbc57d8bc067a0a2456a3fa6e5a8f845a0c22e87436ff", "size_in_bytes": 12}, {"_path": "lib/python3.11/site-packages/onnxruntime/LICENSE", "path_type": "hardlink", "sha256": "2f07c72751aed99790b8a4869cf2311df85a860b22ded05fa22803587a48922c", "sha256_in_prefix": "2f07c72751aed99790b8a4869cf2311df85a860b22ded05fa22803587a48922c", "size_in_bytes": 1073}, {"_path": "lib/python3.11/site-packages/onnxruntime/Privacy.md", "path_type": "hardlink", "sha256": "9a15f9fd9d4c7b6dfc35f237f461863f84f8f73cf6189e330818a674d3828871", "sha256_in_prefix": "9a15f9fd9d4c7b6dfc35f237f461863f84f8f73cf6189e330818a674d3828871", "size_in_bytes": 2469}, {"_path": "lib/python3.11/site-packages/onnxruntime/ThirdPartyNotices.txt", "path_type": "hardlink", "sha256": "e9e90971a8e75a9a8ac0c6412e29c1202d079998389915aa485f46c816c3b4cc", "sha256_in_prefix": "e9e90971a8e75a9a8ac0c6412e29c1202d079998389915aa485f46c816c3b4cc", "size_in_bytes": 326866}, {"_path": "lib/python3.11/site-packages/onnxruntime/__init__.py", "path_type": "hardlink", "sha256": "70f0bec1d73b195b07f4a27490cfe97b5661efaea08e0485a6e22acbd7dec995", "sha256_in_prefix": "70f0bec1d73b195b07f4a27490cfe97b5661efaea08e0485a6e22acbd7dec995", "size_in_bytes": 13999}, {"_path": "lib/python3.11/site-packages/onnxruntime/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "b4111de0163aa0254f71d400b83799fba658b80c05243f4691ebc68b80ca16fa", "sha256_in_prefix": "b4111de0163aa0254f71d400b83799fba658b80c05243f4691ebc68b80ca16fa", "size_in_bytes": 15323}, {"_path": "lib/python3.11/site-packages/onnxruntime/backend/__init__.py", "path_type": "hardlink", "sha256": "92ad7c59c0743a5dfc0921872fe38dc2991f504d0a726a233aaea4f951ada306", "sha256_in_prefix": "92ad7c59c0743a5dfc0921872fe38dc2991f504d0a726a233aaea4f951ada306", "size_in_bytes": 328}, {"_path": "lib/python3.11/site-packages/onnxruntime/backend/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "2c7dc3268cd619a00cdcd072eb49b176d5fec18488d8cee39554c5b144855114", "sha256_in_prefix": "2c7dc3268cd619a00cdcd072eb49b176d5fec18488d8cee39554c5b144855114", "size_in_bytes": 307}, {"_path": "lib/python3.11/site-packages/onnxruntime/backend/__pycache__/backend.cpython-311.pyc", "path_type": "hardlink", "sha256": "ecfed69893ce0698b6b7f11635573232b9d578cf84939db78c1d98683bbd5bb8", "sha256_in_prefix": "ecfed69893ce0698b6b7f11635573232b9d578cf84939db78c1d98683bbd5bb8", "size_in_bytes": 8890}, {"_path": "lib/python3.11/site-packages/onnxruntime/backend/__pycache__/backend_rep.cpython-311.pyc", "path_type": "hardlink", "sha256": "08260c16a8c42244df444144138dbb5f98020451e859c2e0d21efed973eba861", "sha256_in_prefix": "08260c16a8c42244df444144138dbb5f98020451e859c2e0d21efed973eba861", "size_in_bytes": 2955}, {"_path": "lib/python3.11/site-packages/onnxruntime/backend/backend.py", "path_type": "hardlink", "sha256": "cb46362115ad5a0727274028aa3d52b52a96c5e0d5fe2abdaf3d753261f28548", "sha256_in_prefix": "cb46362115ad5a0727274028aa3d52b52a96c5e0d5fe2abdaf3d753261f28548", "size_in_bytes": 8012}, {"_path": "lib/python3.11/site-packages/onnxruntime/backend/backend_rep.py", "path_type": "hardlink", "sha256": "0976905e49deb486fcfdc53c288f5dcfe65e7cf7b8616f655a7663d49794d2a8", "sha256_in_prefix": "0976905e49deb486fcfdc53c288f5dcfe65e7cf7b8616f655a7663d49794d2a8", "size_in_bytes": 1724}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/__init__.py", "path_type": "hardlink", "sha256": "592371b77978964580ff4be1c35103e08eca38ed269c0b87c1280562a6ead0b8", "sha256_in_prefix": "592371b77978964580ff4be1c35103e08eca38ed269c0b87c1280562a6ead0b8", "size_in_bytes": 247}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "b39ee9e276085b67e97798370f70a8904413db36a002dd4265064584d290937a", "sha256_in_prefix": "b39ee9e276085b67e97798370f70a8904413db36a002dd4265064584d290937a", "size_in_bytes": 166}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/__pycache__/_ld_preload.cpython-311.pyc", "path_type": "hardlink", "sha256": "83682d1edcbcc89e9425bd929decd05fa3b2afb84b9e7b557be79ee73352f72d", "sha256_in_prefix": "83682d1edcbcc89e9425bd929decd05fa3b2afb84b9e7b557be79ee73352f72d", "size_in_bytes": 169}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/__pycache__/_pybind_state.cpython-311.pyc", "path_type": "hardlink", "sha256": "d4a0a3352ebafc410deb17e4f1147af9b9187569b5b102815be98b8080ffba2c", "sha256_in_prefix": "d4a0a3352ebafc410deb17e4f1147af9b9187569b5b102815be98b8080ffba2c", "size_in_bytes": 1302}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/__pycache__/build_and_package_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "28cd0e46a1887a6e2f578eec7f483bded0d278b7156aaf6c12ab6680d68e6fc8", "sha256_in_prefix": "28cd0e46a1887a6e2f578eec7f483bded0d278b7156aaf6c12ab6680d68e6fc8", "size_in_bytes": 237}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/__pycache__/convert_npz_to_onnx_adapter.cpython-311.pyc", "path_type": "hardlink", "sha256": "e6b3db7641eaa1972a7c3822cd31f2f82faa97fe6cac94713bce57d800771c88", "sha256_in_prefix": "e6b3db7641eaa1972a7c3822cd31f2f82faa97fe6cac94713bce57d800771c88", "size_in_bytes": 2817}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/__pycache__/onnxruntime_collect_build_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "4c85668f8d41022543972bbbf02cd77d138f66efa25cdf3a6a094f62a9125fa5", "sha256_in_prefix": "4c85668f8d41022543972bbbf02cd77d138f66efa25cdf3a6a094f62a9125fa5", "size_in_bytes": 2177}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/__pycache__/onnxruntime_inference_collection.cpython-311.pyc", "path_type": "hardlink", "sha256": "b3bb8eed223c8b5b43d6433b8988f7beca7a5e7a492c3bf64f72f47029abf7a3", "sha256_in_prefix": "b3bb8eed223c8b5b43d6433b8988f7beca7a5e7a492c3bf64f72f47029abf7a3", "size_in_bytes": 62054}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/__pycache__/onnxruntime_validation.cpython-311.pyc", "path_type": "hardlink", "sha256": "fd0a86760d5d1283a53df9dfba9b1d42d3a0be56d158b30ba4105ea5bad18055", "sha256_in_prefix": "fd0a86760d5d1283a53df9dfba9b1d42d3a0be56d158b30ba4105ea5bad18055", "size_in_bytes": 6372}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/_ld_preload.py", "path_type": "hardlink", "sha256": "fcbae721c4dae4e8d3be93414636d282176c786965be9ef116d3c1053e2ad13a", "sha256_in_prefix": "fcbae721c4dae4e8d3be93414636d282176c786965be9ef116d3c1053e2ad13a", "size_in_bytes": 406}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/_pybind_state.py", "path_type": "hardlink", "sha256": "9f0392aee0402506fd8da1b58398aea6a77896743cce8abab0739b9aac9e3c00", "sha256_in_prefix": "9f0392aee0402506fd8da1b58398aea6a77896743cce8abab0739b9aac9e3c00", "size_in_bytes": 1500}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/build_and_package_info.py", "path_type": "hardlink", "sha256": "82ecfa7111d58415efb2771b4f7231ede690c8e03d4c1ca901722a95677ac9a6", "sha256_in_prefix": "82ecfa7111d58415efb2771b4f7231ede690c8e03d4c1ca901722a95677ac9a6", "size_in_bytes": 52}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/convert_npz_to_onnx_adapter.py", "path_type": "hardlink", "sha256": "b2008e1314fe424777416aba16e8a25f63c7916024e9ca4f542b33232e207940", "sha256_in_prefix": "b2008e1314fe424777416aba16e8a25f63c7916024e9ca4f542b33232e207940", "size_in_bytes": 1533}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/libonnxruntime.1.22.0.dylib", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/onnxruntime_1746973159648/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeho", "sha256": "45bf689e5e85ad31345e795c0bba0aa6b7f0bd1db5bf2bc1c5fb4702a7735cae", "sha256_in_prefix": "c6da51d2fa72e5784cac9defc93ad03ffd244ada9c6279a1bdbb07c18d54bf66", "size_in_bytes": 24594128}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/onnxruntime_collect_build_info.py", "path_type": "hardlink", "sha256": "8fd2e904489839779f5ff3e422e9ff1355ac6d6d8fcab20d324c1b1a7bea3986", "sha256_in_prefix": "8fd2e904489839779f5ff3e422e9ff1355ac6d6d8fcab20d324c1b1a7bea3986", "size_in_bytes": 2062}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/onnxruntime_inference_collection.py", "path_type": "hardlink", "sha256": "46ff5aa2fe2b472840e7a0f64b05b86b88d4e395030bde532316828938c9056d", "sha256_in_prefix": "46ff5aa2fe2b472840e7a0f64b05b86b88d4e395030bde532316828938c9056d", "size_in_bytes": 47140}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/onnxruntime_pybind11_state.so", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/onnxruntime_1746973159648/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeho", "sha256": "c452ccc8100842fb3a5912042b5a41b36a3761335e4dbd767436038414606947", "sha256_in_prefix": "9eb7273e795cf8ea14a4938c02e5f39e105cef1656782a9175f4527ab2a1d2f3", "size_in_bytes": 28341624}, {"_path": "lib/python3.11/site-packages/onnxruntime/capi/onnxruntime_validation.py", "path_type": "hardlink", "sha256": "2a6154d656352b09e7c18548012c3ce73de2cb950540a1423553bcdd865c3b4e", "sha256_in_prefix": "2a6154d656352b09e7c18548012c3ce73de2cb950540a1423553bcdd865c3b4e", "size_in_bytes": 6583}, {"_path": "lib/python3.11/site-packages/onnxruntime/datasets/__init__.py", "path_type": "hardlink", "sha256": "28244c8b55c33ad8aa29e063d5b9ac0e3520ee582fe19d20d8b16c0ad6a4062d", "sha256_in_prefix": "28244c8b55c33ad8aa29e063d5b9ac0e3520ee582fe19d20d8b16c0ad6a4062d", "size_in_bytes": 455}, {"_path": "lib/python3.11/site-packages/onnxruntime/datasets/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "baaa28cca05128d78fea455329c86e89aabcbfd149d18e469ef3cd56799c28c2", "sha256_in_prefix": "baaa28cca05128d78fea455329c86e89aabcbfd149d18e469ef3cd56799c28c2", "size_in_bytes": 956}, {"_path": "lib/python3.11/site-packages/onnxruntime/datasets/logreg_iris.onnx", "path_type": "hardlink", "sha256": "8224784c98d73412d9fd99abcd57a38568bd590980d0fbe5916464531c52e8fc", "sha256_in_prefix": "8224784c98d73412d9fd99abcd57a38568bd590980d0fbe5916464531c52e8fc", "size_in_bytes": 670}, {"_path": "lib/python3.11/site-packages/onnxruntime/datasets/mul_1.onnx", "path_type": "hardlink", "sha256": "71f431c4e9321ec6fbeb158d02ed240459a7dcc98673fa79a4f439ce42efaf10", "sha256_in_prefix": "71f431c4e9321ec6fbeb158d02ed240459a7dcc98673fa79a4f439ce42efaf10", "size_in_bytes": 130}, {"_path": "lib/python3.11/site-packages/onnxruntime/datasets/sigmoid.onnx", "path_type": "hardlink", "sha256": "5340aba67a7e3475162ad794378af55f1718f55f9a5d74b4af60ecc7f7a624b6", "sha256_in_prefix": "5340aba67a7e3475162ad794378af55f1718f55f9a5d74b4af60ecc7f7a624b6", "size_in_bytes": 103}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/CalTableFlatBuffers/KeyValue.py", "path_type": "hardlink", "sha256": "fecc58656ee31b11d336456ad3e6e5fce4e8ab60dd31718e9e968756180dd8cb", "sha256_in_prefix": "fecc58656ee31b11d336456ad3e6e5fce4e8ab60dd31718e9e968756180dd8cb", "size_in_bytes": 2172}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/CalTableFlatBuffers/TrtTable.py", "path_type": "hardlink", "sha256": "ce303e58577f203918948f8f98040099f44278de2dc5323137b01da2a2055ba2", "sha256_in_prefix": "ce303e58577f203918948f8f98040099f44278de2dc5323137b01da2a2055ba2", "size_in_bytes": 2575}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/CalTableFlatBuffers/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/CalTableFlatBuffers/__pycache__/KeyValue.cpython-311.pyc", "path_type": "hardlink", "sha256": "707d908adaacbe70c37b90b948a2998dac19e940ab2aa047f854a6c1ddfc85fa", "sha256_in_prefix": "707d908adaacbe70c37b90b948a2998dac19e940ab2aa047f854a6c1ddfc85fa", "size_in_bytes": 4367}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/CalTableFlatBuffers/__pycache__/TrtTable.cpython-311.pyc", "path_type": "hardlink", "sha256": "91ffcb09ed8fcea38bc80e503d6447490e06689ebbce833f65075e301aa46b76", "sha256_in_prefix": "91ffcb09ed8fcea38bc80e503d6447490e06689ebbce833f65075e301aa46b76", "size_in_bytes": 5112}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/CalTableFlatBuffers/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "c5eb7518a5a5f520317d3a0399bc1b066928c64f18454b74f899e3445055bcf5", "sha256_in_prefix": "c5eb7518a5a5f520317d3a0399bc1b066928c64f18454b74f899e3445055bcf5", "size_in_bytes": 194}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/__init__.py", "path_type": "hardlink", "sha256": "5048dfa16807744339073291a58450b857cab4316f5807d7f470935e303c68c1", "sha256_in_prefix": "5048dfa16807744339073291a58450b857cab4316f5807d7f470935e303c68c1", "size_in_bytes": 628}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "edc4fa89e63c2bdb227f262e1077a8bd33ea96e8d089a79b87f6d024ac40dfbf", "sha256_in_prefix": "edc4fa89e63c2bdb227f262e1077a8bd33ea96e8d089a79b87f6d024ac40dfbf", "size_in_bytes": 956}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/base_quantizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "f3f69da5aa910a7ccd92f861a5d116a0eddacfb3771e7a7702ef2a8e5e98729b", "sha256_in_prefix": "f3f69da5aa910a7ccd92f861a5d116a0eddacfb3771e7a7702ef2a8e5e98729b", "size_in_bytes": 32841}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/calibrate.cpython-311.pyc", "path_type": "hardlink", "sha256": "5eaaac7c0de5cf15faa8d1721d2d6e9123a4c2716bb00f5c9d70030753992d98", "sha256_in_prefix": "5eaaac7c0de5cf15faa8d1721d2d6e9123a4c2716bb00f5c9d70030753992d98", "size_in_bytes": 68762}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/matmul_bnb4_quantizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "6f9fac247fa9fa0789be7628fa75f200250d2631a0cc5ad834d2d352e73fb9b6", "sha256_in_prefix": "6f9fac247fa9fa0789be7628fa75f200250d2631a0cc5ad834d2d352e73fb9b6", "size_in_bytes": 12081}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/matmul_nbits_quantizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "0de168027f3a621cba366e2f29efbecdd1524757ea0b3453421d4ffff7839f7b", "sha256_in_prefix": "0de168027f3a621cba366e2f29efbecdd1524757ea0b3453421d4ffff7839f7b", "size_in_bytes": 73162}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/onnx_model.cpython-311.pyc", "path_type": "hardlink", "sha256": "f11a669c402854fdd207185864128e7be98b68c4ff4140f73deb243236afc695", "sha256_in_prefix": "f11a669c402854fdd207185864128e7be98b68c4ff4140f73deb243236afc695", "size_in_bytes": 35321}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/onnx_quantizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "b01ec4db8e6781a22c160531f40df1e49587a9682bcb0e17e2ee053ac071e8ee", "sha256_in_prefix": "b01ec4db8e6781a22c160531f40df1e49587a9682bcb0e17e2ee053ac071e8ee", "size_in_bytes": 44948}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/preprocess.cpython-311.pyc", "path_type": "hardlink", "sha256": "67d866641f699ffe970a847c843bc1dfaf228fe0a78596be87702f4b3e4f0a7d", "sha256_in_prefix": "67d866641f699ffe970a847c843bc1dfaf228fe0a78596be87702f4b3e4f0a7d", "size_in_bytes": 5452}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/qdq_loss_debug.cpython-311.pyc", "path_type": "hardlink", "sha256": "571d58c2588ec3aeec1028c16a56561c9022ba7beca86483ed6fffca34e5c5e1", "sha256_in_prefix": "571d58c2588ec3aeec1028c16a56561c9022ba7beca86483ed6fffca34e5c5e1", "size_in_bytes": 18703}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/qdq_quantizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "08ae0a97ced4a8464818d98fbf1546899e6ec1a67db84e5978c68ec173f45d52", "sha256_in_prefix": "08ae0a97ced4a8464818d98fbf1546899e6ec1a67db84e5978c68ec173f45d52", "size_in_bytes": 64448}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/quant_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "676ff6b6d5bbd55164ecc00ce1557182367173663811a4bd88d2e9ce01d4196e", "sha256_in_prefix": "676ff6b6d5bbd55164ecc00ce1557182367173663811a4bd88d2e9ce01d4196e", "size_in_bytes": 53859}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/quantize.cpython-311.pyc", "path_type": "hardlink", "sha256": "d5d52e099f9c3d868611d65951ac86bf4f46c16b4dfe1d69118ed2f1b0d917e7", "sha256_in_prefix": "d5d52e099f9c3d868611d65951ac86bf4f46c16b4dfe1d69118ed2f1b0d917e7", "size_in_bytes": 53245}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/registry.cpython-311.pyc", "path_type": "hardlink", "sha256": "44aabece7f02af0d5420b8a06e6663ca4302f07f29700dbb5c2325e588f3ce55", "sha256_in_prefix": "44aabece7f02af0d5420b8a06e6663ca4302f07f29700dbb5c2325e588f3ce55", "size_in_bytes": 4663}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/shape_inference.cpython-311.pyc", "path_type": "hardlink", "sha256": "f4381976f6f8a738f25293116f61bc69ab0cce1674a94183de708b56d506b989", "sha256_in_prefix": "f4381976f6f8a738f25293116f61bc69ab0cce1674a94183de708b56d506b989", "size_in_bytes": 7832}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/static_quantize_runner.cpython-311.pyc", "path_type": "hardlink", "sha256": "1f687e7a8ea9767d79ef66d08ee3a99e26b7105cd0f6d879ef4c0f5bffed4c20", "sha256_in_prefix": "1f687e7a8ea9767d79ef66d08ee3a99e26b7105cd0f6d879ef4c0f5bffed4c20", "size_in_bytes": 14263}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/__pycache__/tensor_quant_overrides.cpython-311.pyc", "path_type": "hardlink", "sha256": "42e0bf4a34b61dfa4ecc0a976d322d664c08647779127562917196c35ee591b5", "sha256_in_prefix": "42e0bf4a34b61dfa4ecc0a976d322d664c08647779127562917196c35ee591b5", "size_in_bytes": 20916}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/base_quantizer.py", "path_type": "hardlink", "sha256": "47b233b966f32eae18c20a0af9ef20a74998e44f6c4b6ca59e347821b825c248", "sha256_in_prefix": "47b233b966f32eae18c20a0af9ef20a74998e44f6c4b6ca59e347821b825c248", "size_in_bytes": 27388}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/calibrate.py", "path_type": "hardlink", "sha256": "f921d19172a52ebd32ff4bcd02b7a5c32502603092153160286253dc6f4c2c9a", "sha256_in_prefix": "f921d19172a52ebd32ff4bcd02b7a5c32502603092153160286253dc6f4c2c9a", "size_in_bytes": 52762}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/__init__.py", "path_type": "hardlink", "sha256": "99a13e72b8e602904cfac188b98fcfce386542eb10afc0fe140840f5eaa6f758", "sha256_in_prefix": "99a13e72b8e602904cfac188b98fcfce386542eb10afc0fe140840f5eaa6f758", "size_in_bytes": 118}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "82f5cb2cb331eb2d37396309d8ec1ea07dc1fe6ea352e652fc5c7264a12bc7f4", "sha256_in_prefix": "82f5cb2cb331eb2d37396309d8ec1ea07dc1fe6ea352e652fc5c7264a12bc7f4", "size_in_bytes": 338}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/__pycache__/fusion_lpnorm.cpython-311.pyc", "path_type": "hardlink", "sha256": "676c5b691e53c63ff0683d7d94d3916d31d20796538c958a3b457c564bc92921", "sha256_in_prefix": "676c5b691e53c63ff0683d7d94d3916d31d20796538c958a3b457c564bc92921", "size_in_bytes": 5707}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/__pycache__/mixed_precision_overrides_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "52a3f388c00dae1073b4258b2914bdcc0bd085b6e33ba55dfcd7c7052293e1d2", "sha256_in_prefix": "52a3f388c00dae1073b4258b2914bdcc0bd085b6e33ba55dfcd7c7052293e1d2", "size_in_bytes": 19425}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/__pycache__/preprocess.cpython-311.pyc", "path_type": "hardlink", "sha256": "385b20a1954e046556d241ed271da70e073959a794d4882f6bc9d3569ccf551c", "sha256_in_prefix": "385b20a1954e046556d241ed271da70e073959a794d4882f6bc9d3569ccf551c", "size_in_bytes": 15974}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/__pycache__/quant_config.cpython-311.pyc", "path_type": "hardlink", "sha256": "b3c404c1afcae168dc9b8da2c4927c38b68e9b5229cdec739a38eea3e13a2f50", "sha256_in_prefix": "b3c404c1afcae168dc9b8da2c4927c38b68e9b5229cdec739a38eea3e13a2f50", "size_in_bytes": 20081}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/fusion_lpnorm.py", "path_type": "hardlink", "sha256": "6767db32ff39da3cc2c9691f4d36acab90883d2bed0fe1161a6507d677c7cf5f", "sha256_in_prefix": "6767db32ff39da3cc2c9691f4d36acab90883d2bed0fe1161a6507d677c7cf5f", "size_in_bytes": 5195}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/mixed_precision_overrides_utils.py", "path_type": "hardlink", "sha256": "beff0aa9254a724556906333c169615ab64de047b45c09c1f325fee0b334da07", "sha256_in_prefix": "beff0aa9254a724556906333c169615ab64de047b45c09c1f325fee0b334da07", "size_in_bytes": 18582}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/preprocess.py", "path_type": "hardlink", "sha256": "fa6218bb67dc9ccdcc14b8542a321ffbeca25a74d5d711bfb389c028b8b9b01f", "sha256_in_prefix": "fa6218bb67dc9ccdcc14b8542a321ffbeca25a74d5d711bfb389c028b8b9b01f", "size_in_bytes": 13885}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/execution_providers/qnn/quant_config.py", "path_type": "hardlink", "sha256": "9e9d6ef527fdf57ec817374669d79d7ce4d9078831c40b0b22e815119ec2cd2d", "sha256_in_prefix": "9e9d6ef527fdf57ec817374669d79d7ce4d9078831c40b0b22e815119ec2cd2d", "size_in_bytes": 19429}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/fusions/__init__.py", "path_type": "hardlink", "sha256": "df306d1eb2e5bc51a9b752dd368a4306687016f5f9247d570ab967abdddccf1d", "sha256_in_prefix": "df306d1eb2e5bc51a9b752dd368a4306687016f5f9247d570ab967abdddccf1d", "size_in_bytes": 160}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/fusions/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "e2de013c961d7293428607b6578c69de5dafa7d31ed8ccbb7ee6c348a9933263", "sha256_in_prefix": "e2de013c961d7293428607b6578c69de5dafa7d31ed8ccbb7ee6c348a9933263", "size_in_bytes": 376}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/fusions/__pycache__/fusion.cpython-311.pyc", "path_type": "hardlink", "sha256": "5b2247d9066b07a57f75dce5803f46abdb8ef0cfc1782ae35a106eca4a23897f", "sha256_in_prefix": "5b2247d9066b07a57f75dce5803f46abdb8ef0cfc1782ae35a106eca4a23897f", "size_in_bytes": 13533}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/fusions/__pycache__/fusion_gelu.cpython-311.pyc", "path_type": "hardlink", "sha256": "ba8b6c5754a6e7688d6ee3e0da6300ad2474c0bd1b9815a1b9f78602f9bf8e48", "sha256_in_prefix": "ba8b6c5754a6e7688d6ee3e0da6300ad2474c0bd1b9815a1b9f78602f9bf8e48", "size_in_bytes": 10940}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/fusions/__pycache__/fusion_layernorm.cpython-311.pyc", "path_type": "hardlink", "sha256": "e020d7c24c4390edef063063962832cd93924cfa69515a685f997533f1bce59c", "sha256_in_prefix": "e020d7c24c4390edef063063962832cd93924cfa69515a685f997533f1bce59c", "size_in_bytes": 6304}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/fusions/fusion.py", "path_type": "hardlink", "sha256": "f2f4cb00704942a8143f02b6539c9de94c14b64d4c1cfc8ace52e3fb89574112", "sha256_in_prefix": "f2f4cb00704942a8143f02b6539c9de94c14b64d4c1cfc8ace52e3fb89574112", "size_in_bytes": 11777}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/fusions/fusion_gelu.py", "path_type": "hardlink", "sha256": "a2216ab4e51cba690f6601f96dd4aa9ad7a898ce9611a7b0c0cd7cc29bf79b2c", "sha256_in_prefix": "a2216ab4e51cba690f6601f96dd4aa9ad7a898ce9611a7b0c0cd7cc29bf79b2c", "size_in_bytes": 10375}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/fusions/fusion_layernorm.py", "path_type": "hardlink", "sha256": "13819fca2fb530e741e40d7a256c504103589e495c90bda109174da743b27ae4", "sha256_in_prefix": "13819fca2fb530e741e40d7a256c504103589e495c90bda109174da743b27ae4", "size_in_bytes": 5171}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/matmul_bnb4_quantizer.py", "path_type": "hardlink", "sha256": "56f022568fb3cc009d7fbb546466b2fd937e6bf0c4481c83558924fbf338d519", "sha256_in_prefix": "56f022568fb3cc009d7fbb546466b2fd937e6bf0c4481c83558924fbf338d519", "size_in_bytes": 9029}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/matmul_nbits_quantizer.py", "path_type": "hardlink", "sha256": "6868fe2cac8a93af754bd3b5006ee57a4d4b712b713a109afc463b6602143e0f", "sha256_in_prefix": "6868fe2cac8a93af754bd3b5006ee57a4d4b712b713a109afc463b6602143e0f", "size_in_bytes": 63269}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/onnx_model.py", "path_type": "hardlink", "sha256": "4fb0949d9d559543e64b5b68ad987932e466a7e412a68e214dc4acade2740652", "sha256_in_prefix": "4fb0949d9d559543e64b5b68ad987932e466a7e412a68e214dc4acade2740652", "size_in_bytes": 23933}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/onnx_quantizer.py", "path_type": "hardlink", "sha256": "1695c08aca76501ade6a6246df85116ce3091262cce9fdef41b1dcd9aed99d70", "sha256_in_prefix": "1695c08aca76501ade6a6246df85116ce3091262cce9fdef41b1dcd9aed99d70", "size_in_bytes": 42956}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__init__.py", "path_type": "hardlink", "sha256": "7b531614058e27f1678fbea573c2e71b6dafd8ac22ff236f2a1d1aaa489d10a5", "sha256_in_prefix": "7b531614058e27f1678fbea573c2e71b6dafd8ac22ff236f2a1d1aaa489d10a5", "size_in_bytes": 83}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "a8a6917e20826ad10ee06f3518339f08fa0e18e2858fd7d649de05bd9b8be649", "sha256_in_prefix": "a8a6917e20826ad10ee06f3518339f08fa0e18e2858fd7d649de05bd9b8be649", "size_in_bytes": 184}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/activation.cpython-311.pyc", "path_type": "hardlink", "sha256": "567f8aadee27e76dad06027a0c51307e0c6a6df605eca9012fbade79cc7c3124", "sha256_in_prefix": "567f8aadee27e76dad06027a0c51307e0c6a6df605eca9012fbade79cc7c3124", "size_in_bytes": 5782}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/argmax.cpython-311.pyc", "path_type": "hardlink", "sha256": "b23dd52e081c8d13e7803361d545ede4acedfe8fcdec3dc094587767f152b379", "sha256_in_prefix": "b23dd52e081c8d13e7803361d545ede4acedfe8fcdec3dc094587767f152b379", "size_in_bytes": 1368}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/attention.cpython-311.pyc", "path_type": "hardlink", "sha256": "75d827c9510c5ead5fb7c5d3290cea25229a0bae1c079d3f63c0cbe063f9e3be", "sha256_in_prefix": "75d827c9510c5ead5fb7c5d3290cea25229a0bae1c079d3f63c0cbe063f9e3be", "size_in_bytes": 4111}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/base_operator.cpython-311.pyc", "path_type": "hardlink", "sha256": "ced2509511798db79c875d23142f4295ff3a306b9c478be3a6522c0295f7cefa", "sha256_in_prefix": "ced2509511798db79c875d23142f4295ff3a306b9c478be3a6522c0295f7cefa", "size_in_bytes": 2076}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/binary_op.cpython-311.pyc", "path_type": "hardlink", "sha256": "f544f4f5c82d4974dac7da272c781f25d5e647a496f5f8dc28b7e084971da575", "sha256_in_prefix": "f544f4f5c82d4974dac7da272c781f25d5e647a496f5f8dc28b7e084971da575", "size_in_bytes": 3548}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/concat.cpython-311.pyc", "path_type": "hardlink", "sha256": "e6b808ef4b07ebaacc90bfc94ccdd5d206dbcfa90d55056ee7259d04de8db8d6", "sha256_in_prefix": "e6b808ef4b07ebaacc90bfc94ccdd5d206dbcfa90d55056ee7259d04de8db8d6", "size_in_bytes": 3313}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/conv.cpython-311.pyc", "path_type": "hardlink", "sha256": "1a73e1f18f522c73a9fc3c11e90b13bd937b18fc52da347c628072958005cd92", "sha256_in_prefix": "1a73e1f18f522c73a9fc3c11e90b13bd937b18fc52da347c628072958005cd92", "size_in_bytes": 13076}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/direct_q8.cpython-311.pyc", "path_type": "hardlink", "sha256": "c8e3c1294967b2e21202fde1114a0f151cd8b783ceb7c443f2ad37c52783b248", "sha256_in_prefix": "c8e3c1294967b2e21202fde1114a0f151cd8b783ceb7c443f2ad37c52783b248", "size_in_bytes": 4649}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/embed_layernorm.cpython-311.pyc", "path_type": "hardlink", "sha256": "351ff70cb1d07c4290e860f64b303ff365368b6e66a6ba7aeb554e34829fa069", "sha256_in_prefix": "351ff70cb1d07c4290e860f64b303ff365368b6e66a6ba7aeb554e34829fa069", "size_in_bytes": 4667}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/gather.cpython-311.pyc", "path_type": "hardlink", "sha256": "d5ae4f87968d361ffcc1ae82bcff12c119b26ffd5a77a6ced956213b562cf138", "sha256_in_prefix": "d5ae4f87968d361ffcc1ae82bcff12c119b26ffd5a77a6ced956213b562cf138", "size_in_bytes": 4192}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/gavgpool.cpython-311.pyc", "path_type": "hardlink", "sha256": "460b3e43adcfab1ec0694bea63041f86e3619bb0ce7d3147ba5eab8516581c47", "sha256_in_prefix": "460b3e43adcfab1ec0694bea63041f86e3619bb0ce7d3147ba5eab8516581c47", "size_in_bytes": 3128}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/gemm.cpython-311.pyc", "path_type": "hardlink", "sha256": "69e854181a803ceba5f3c442c55766566bedad08a28937e177b242c015103f89", "sha256_in_prefix": "69e854181a803ceba5f3c442c55766566bedad08a28937e177b242c015103f89", "size_in_bytes": 9274}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/lstm.cpython-311.pyc", "path_type": "hardlink", "sha256": "5308efdde9260dc2138131e5c20daf45bd6a7146acbf1676103d3b1d63b89977", "sha256_in_prefix": "5308efdde9260dc2138131e5c20daf45bd6a7146acbf1676103d3b1d63b89977", "size_in_bytes": 7076}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/matmul.cpython-311.pyc", "path_type": "hardlink", "sha256": "71ce152cb1721405ae0a51516a5a5f6b47cbb654774d135e7b23d974ef8d8e8e", "sha256_in_prefix": "71ce152cb1721405ae0a51516a5a5f6b47cbb654774d135e7b23d974ef8d8e8e", "size_in_bytes": 10584}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/maxpool.cpython-311.pyc", "path_type": "hardlink", "sha256": "290a251eb306f2ec0db98964d58cce0d417354c764d320c26db9f06f2517500f", "sha256_in_prefix": "290a251eb306f2ec0db98964d58cce0d417354c764d320c26db9f06f2517500f", "size_in_bytes": 2178}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/norm.cpython-311.pyc", "path_type": "hardlink", "sha256": "e7481fb2066f889d4d49836cc7f08662ec9bf9c582a142cba9aaca60434cefc8", "sha256_in_prefix": "e7481fb2066f889d4d49836cc7f08662ec9bf9c582a142cba9aaca60434cefc8", "size_in_bytes": 2660}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/pad.cpython-311.pyc", "path_type": "hardlink", "sha256": "0aa62dc2e0178ceba48d85eb252427932d1aa175640be2dc35782bcfb4de8d10", "sha256_in_prefix": "0aa62dc2e0178ceba48d85eb252427932d1aa175640be2dc35782bcfb4de8d10", "size_in_bytes": 9088}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/pooling.cpython-311.pyc", "path_type": "hardlink", "sha256": "ac155a782563fa5cefe49d32ba5a8f5f7ff88a2ebe9b1e0d2958b6d98e9e96bc", "sha256_in_prefix": "ac155a782563fa5cefe49d32ba5a8f5f7ff88a2ebe9b1e0d2958b6d98e9e96bc", "size_in_bytes": 2891}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/qdq_base_operator.cpython-311.pyc", "path_type": "hardlink", "sha256": "8d318a8d8f671cf0a7cebdd5a66fcfbec427ee163f4808938726a73da78fe460", "sha256_in_prefix": "8d318a8d8f671cf0a7cebdd5a66fcfbec427ee163f4808938726a73da78fe460", "size_in_bytes": 1612}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/resize.cpython-311.pyc", "path_type": "hardlink", "sha256": "749a525de9be9cd95e550851c5fb27529946c3608bfd270531277ef1330db5aa", "sha256_in_prefix": "749a525de9be9cd95e550851c5fb27529946c3608bfd270531277ef1330db5aa", "size_in_bytes": 2170}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/softmax.cpython-311.pyc", "path_type": "hardlink", "sha256": "fc92e382842d24290980088616a16a2768a5fa2f3532f1ac805af8d95ba4ca6b", "sha256_in_prefix": "fc92e382842d24290980088616a16a2768a5fa2f3532f1ac805af8d95ba4ca6b", "size_in_bytes": 2957}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/split.cpython-311.pyc", "path_type": "hardlink", "sha256": "21e94e7d48cc85b5940e8b9c7999c06a059cd67f2e93aa34f29a85c7d7aa324e", "sha256_in_prefix": "21e94e7d48cc85b5940e8b9c7999c06a059cd67f2e93aa34f29a85c7d7aa324e", "size_in_bytes": 3782}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/__pycache__/where.cpython-311.pyc", "path_type": "hardlink", "sha256": "fee019713c87616550b41c9b38f09d674918139db8df3714b8a64d569f5528ae", "sha256_in_prefix": "fee019713c87616550b41c9b38f09d674918139db8df3714b8a64d569f5528ae", "size_in_bytes": 4690}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/activation.py", "path_type": "hardlink", "sha256": "63d8004592e07fedc847da426b135e541a06bb465889063c646e0a72dc3829ff", "sha256_in_prefix": "63d8004592e07fedc847da426b135e541a06bb465889063c646e0a72dc3829ff", "size_in_bytes": 4426}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/argmax.py", "path_type": "hardlink", "sha256": "86b3b40c8808df3c33956e8c7207d25b3f7b6d4bc7b96193b321f6ba11b540d1", "sha256_in_prefix": "86b3b40c8808df3c33956e8c7207d25b3f7b6d4bc7b96193b321f6ba11b540d1", "size_in_bytes": 571}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/attention.py", "path_type": "hardlink", "sha256": "45b7d496416d2cfd4ee702e5c2fe47f18a2535e8dfd17cae07845fa8629595e3", "sha256_in_prefix": "45b7d496416d2cfd4ee702e5c2fe47f18a2535e8dfd17cae07845fa8629595e3", "size_in_bytes": 2564}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/base_operator.py", "path_type": "hardlink", "sha256": "74a29cf70d2a2e7afd1ceb4a4c6f222452993f037077956576efb544998fcaf9", "sha256_in_prefix": "74a29cf70d2a2e7afd1ceb4a4c6f222452993f037077956576efb544998fcaf9", "size_in_bytes": 1092}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/binary_op.py", "path_type": "hardlink", "sha256": "407cb36bfb9b087e8a2caa7b26d586a0d2d804e2d5c61b8287572b98860bf4ca", "sha256_in_prefix": "407cb36bfb9b087e8a2caa7b26d586a0d2d804e2d5c61b8287572b98860bf4ca", "size_in_bytes": 2472}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/concat.py", "path_type": "hardlink", "sha256": "731ec69fc8e1e1fa016e8a516f824f02836c4b23f500a737fe01969d5c891344", "sha256_in_prefix": "731ec69fc8e1e1fa016e8a516f824f02836c4b23f500a737fe01969d5c891344", "size_in_bytes": 2081}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/conv.py", "path_type": "hardlink", "sha256": "be3bc477995318cfd45619453444abb19638e746b6907318cacfe54ec1b34d7e", "sha256_in_prefix": "be3bc477995318cfd45619453444abb19638e746b6907318cacfe54ec1b34d7e", "size_in_bytes": 9943}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/direct_q8.py", "path_type": "hardlink", "sha256": "ed3514d7e2409b916efffe11a91c463e05f0aaa0f7ff472e4b44944788aa28d5", "sha256_in_prefix": "ed3514d7e2409b916efffe11a91c463e05f0aaa0f7ff472e4b44944788aa28d5", "size_in_bytes": 3311}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/embed_layernorm.py", "path_type": "hardlink", "sha256": "686ab6cb0e19b63be00b9fed78023f7c09f5f14120af54a126b5d3eedd7c2464", "sha256_in_prefix": "686ab6cb0e19b63be00b9fed78023f7c09f5f14120af54a126b5d3eedd7c2464", "size_in_bytes": 3937}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/gather.py", "path_type": "hardlink", "sha256": "2cac2de86bcc9bcd327047580cf73bc8e0ecee1a52b832e279289bd7695b9b7a", "sha256_in_prefix": "2cac2de86bcc9bcd327047580cf73bc8e0ecee1a52b832e279289bd7695b9b7a", "size_in_bytes": 2166}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/gavgpool.py", "path_type": "hardlink", "sha256": "cdb2d953162af78b71f8e553656bfae005becbc32f9a4fb671740d6ca3352896", "sha256_in_prefix": "cdb2d953162af78b71f8e553656bfae005becbc32f9a4fb671740d6ca3352896", "size_in_bytes": 2383}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/gemm.py", "path_type": "hardlink", "sha256": "47a69b633ef4b298582c13e967b17e769b75a81f2fa0ca76e7b365487ab70061", "sha256_in_prefix": "47a69b633ef4b298582c13e967b17e769b75a81f2fa0ca76e7b365487ab70061", "size_in_bytes": 6069}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/lstm.py", "path_type": "hardlink", "sha256": "1cdd3fb3c00ded7503747824a1dbd773ba08e3172f4b63da9c6fe9238c613cec", "sha256_in_prefix": "1cdd3fb3c00ded7503747824a1dbd773ba08e3172f4b63da9c6fe9238c613cec", "size_in_bytes": 5117}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/matmul.py", "path_type": "hardlink", "sha256": "0f2d334bddda991e8fb65d880cdd0862a7a889982e25edb1004ebf79d38d3dbe", "sha256_in_prefix": "0f2d334bddda991e8fb65d880cdd0862a7a889982e25edb1004ebf79d38d3dbe", "size_in_bytes": 8268}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/maxpool.py", "path_type": "hardlink", "sha256": "e85d83e3ac7eeec2f96d73ea4eae0e8de2bc1dca6ca20bdd5cd0883d302611e9", "sha256_in_prefix": "e85d83e3ac7eeec2f96d73ea4eae0e8de2bc1dca6ca20bdd5cd0883d302611e9", "size_in_bytes": 927}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/norm.py", "path_type": "hardlink", "sha256": "0da2c43dc5677e4c1a0fa7a94e0ac2ee1784f838e25814ecda8b0048c613f741", "sha256_in_prefix": "0da2c43dc5677e4c1a0fa7a94e0ac2ee1784f838e25814ecda8b0048c613f741", "size_in_bytes": 1609}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/pad.py", "path_type": "hardlink", "sha256": "92ee11d6df95801111f345685eb132ff8c41754a3dec363d7ac0df999d736b01", "sha256_in_prefix": "92ee11d6df95801111f345685eb132ff8c41754a3dec363d7ac0df999d736b01", "size_in_bytes": 7779}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/pooling.py", "path_type": "hardlink", "sha256": "65323c1632f093e79aa3bfaa638e5c0ac52da1b95956f27b3c86745bd629404c", "sha256_in_prefix": "65323c1632f093e79aa3bfaa638e5c0ac52da1b95956f27b3c86745bd629404c", "size_in_bytes": 2218}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/qdq_base_operator.py", "path_type": "hardlink", "sha256": "833a553185bda598964606d9107a4a806b96a9c3a232cb18b61f93a7269ddac8", "sha256_in_prefix": "833a553185bda598964606d9107a4a806b96a9c3a232cb18b61f93a7269ddac8", "size_in_bytes": 801}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/resize.py", "path_type": "hardlink", "sha256": "5caa4667dafb624f433982c041173f98acbe8ef884f375a74f8155eb908b4cb4", "sha256_in_prefix": "5caa4667dafb624f433982c041173f98acbe8ef884f375a74f8155eb908b4cb4", "size_in_bytes": 928}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/softmax.py", "path_type": "hardlink", "sha256": "377d90c9cf445eb62c4c84fd24e009804662f838740f81551c54ac98e226bd1d", "sha256_in_prefix": "377d90c9cf445eb62c4c84fd24e009804662f838740f81551c54ac98e226bd1d", "size_in_bytes": 2640}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/split.py", "path_type": "hardlink", "sha256": "1d76742cae13f2fb116e1c8723c0e30710a62769c0df321258cf486bdca50212", "sha256_in_prefix": "1d76742cae13f2fb116e1c8723c0e30710a62769c0df321258cf486bdca50212", "size_in_bytes": 2195}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/operators/where.py", "path_type": "hardlink", "sha256": "b43afc5d219013a56e43653f9a244db0e8b0efee9766478ef7a74cd67697e1e8", "sha256_in_prefix": "b43afc5d219013a56e43653f9a244db0e8b0efee9766478ef7a74cd67697e1e8", "size_in_bytes": 3040}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/preprocess.py", "path_type": "hardlink", "sha256": "105d56a717cc3a81922596c1d3804bf4ae3e9801d8ef15a3e6d86fcf5b7ae1d7", "sha256_in_prefix": "105d56a717cc3a81922596c1d3804bf4ae3e9801d8ef15a3e6d86fcf5b7ae1d7", "size_in_bytes": 4904}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/qdq_loss_debug.py", "path_type": "hardlink", "sha256": "f8fcf18ea1e0081ba77ecb6664e67650c068bdf206f0d3ff63351833da480629", "sha256_in_prefix": "f8fcf18ea1e0081ba77ecb6664e67650c068bdf206f0d3ff63351833da480629", "size_in_bytes": 15440}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/qdq_quantizer.py", "path_type": "hardlink", "sha256": "2aaa0a508b35d2d2ff57fa35aa1a9fd257a40c7ea5f2848ec3d07cdfa5710f79", "sha256_in_prefix": "2aaa0a508b35d2d2ff57fa35aa1a9fd257a40c7ea5f2848ec3d07cdfa5710f79", "size_in_bytes": 69902}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/quant_utils.py", "path_type": "hardlink", "sha256": "b768055fd059f69a3b2b2972eefb541a37704c2655e7f34d3f50b924eb882e17", "sha256_in_prefix": "b768055fd059f69a3b2b2972eefb541a37704c2655e7f34d3f50b924eb882e17", "size_in_bytes": 38899}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/quantize.py", "path_type": "hardlink", "sha256": "3709d571f3a826717e1f4c14043d544ff78bf079743ee24cb8a08e03e9bb7bfc", "sha256_in_prefix": "3709d571f3a826717e1f4c14043d544ff78bf079743ee24cb8a08e03e9bb7bfc", "size_in_bytes": 52421}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/registry.py", "path_type": "hardlink", "sha256": "431cd2dc0d034e6396264daf69b1cc0388c69aae63941786d9906c0c19547b07", "sha256_in_prefix": "431cd2dc0d034e6396264daf69b1cc0388c69aae63941786d9906c0c19547b07", "size_in_bytes": 3686}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/shape_inference.py", "path_type": "hardlink", "sha256": "f362c34d8eb6364496109d8a0064175681bc479ae4ad202fb04b08e058817678", "sha256_in_prefix": "f362c34d8eb6364496109d8a0064175681bc479ae4ad202fb04b08e058817678", "size_in_bytes": 8901}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/static_quantize_runner.py", "path_type": "hardlink", "sha256": "6ff6a23d7c68188327723d360133db338e3998194f91dce4de1904324bbdcdf5", "sha256_in_prefix": "6ff6a23d7c68188327723d360133db338e3998194f91dce4de1904324bbdcdf5", "size_in_bytes": 11059}, {"_path": "lib/python3.11/site-packages/onnxruntime/quantization/tensor_quant_overrides.py", "path_type": "hardlink", "sha256": "6663d764c9f0ff6e0979da69a9a34fa91d15b87e640e11d7d10c13abc0d650ca", "sha256_in_prefix": "6663d764c9f0ff6e0979da69a9a34fa91d15b87e640e11d7d10c13abc0d650ca", "size_in_bytes": 20767}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/__init__.py", "path_type": "hardlink", "sha256": "8b1e1ecad6d6555f1029e66ef8d54f8dbf2820c5ab484d262b37d5867edc8419", "sha256_in_prefix": "8b1e1ecad6d6555f1029e66ef8d54f8dbf2820c5ab484d262b37d5867edc8419", "size_in_bytes": 518}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "cb6037019d21834579ff7435bb557b293ad9568f726dad08086d6c82349c7bfa", "sha256_in_prefix": "cb6037019d21834579ff7435bb557b293ad9568f726dad08086d6c82349c7bfa", "size_in_bytes": 433}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/check_onnx_model_mobile_usability.cpython-311.pyc", "path_type": "hardlink", "sha256": "ad28b1202fc49d3a35206f72630672022c4307e5b9afccbd83cd8a9ca53f2e23", "sha256_in_prefix": "ad28b1202fc49d3a35206f72630672022c4307e5b9afccbd83cd8a9ca53f2e23", "size_in_bytes": 2532}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/convert_onnx_models_to_ort.cpython-311.pyc", "path_type": "hardlink", "sha256": "5dcb7284192c57657be7626ae921418a447cbc2ad5656cf3eaf790af8bf0f279", "sha256_in_prefix": "5dcb7284192c57657be7626ae921418a447cbc2ad5656cf3eaf790af8bf0f279", "size_in_bytes": 15352}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/file_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "955c4cadeb406bb968bf093a357081eae446bbabdfb543cc63f3d1e63abc9336", "sha256_in_prefix": "955c4cadeb406bb968bf093a357081eae446bbabdfb543cc63f3d1e63abc9336", "size_in_bytes": 2554}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/logger.cpython-311.pyc", "path_type": "hardlink", "sha256": "e255b36575431470108c742f353634c144bf2d1b77f9fcbdfca940ec745b90ad", "sha256_in_prefix": "e255b36575431470108c742f353634c144bf2d1b77f9fcbdfca940ec745b90ad", "size_in_bytes": 645}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/make_dynamic_shape_fixed.cpython-311.pyc", "path_type": "hardlink", "sha256": "1f32f547841fc0763e034bd80de7677864ab5cad481f1c6b1b7da4fdb22c324e", "sha256_in_prefix": "1f32f547841fc0763e034bd80de7677864ab5cad481f1c6b1b7da4fdb22c324e", "size_in_bytes": 4396}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/offline_tuning.cpython-311.pyc", "path_type": "hardlink", "sha256": "5caffb12b7b1b5d30791abc2916598f48de3079b42a25a1f0531b0fc08cafbdf", "sha256_in_prefix": "5caffb12b7b1b5d30791abc2916598f48de3079b42a25a1f0531b0fc08cafbdf", "size_in_bytes": 10694}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/onnx_model_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "77aa850a27ac1e080152a7ca4218dab3116b500250b61102e7d34e7c3bdf9e2a", "sha256_in_prefix": "77aa850a27ac1e080152a7ca4218dab3116b500250b61102e7d34e7c3bdf9e2a", "size_in_bytes": 20750}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/onnx_randomizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "5503a4ae41fc595a07b5ca76eca629794027accfb9edb0f6c5f06cae10046f8d", "sha256_in_prefix": "5503a4ae41fc595a07b5ca76eca629794027accfb9edb0f6c5f06cae10046f8d", "size_in_bytes": 4420}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/onnxruntime_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "72ae584156c807e54d072085411ddd74b11747eb9f4947a17670597609368086", "sha256_in_prefix": "72ae584156c807e54d072085411ddd74b11747eb9f4947a17670597609368086", "size_in_bytes": 8280}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/optimize_onnx_model.cpython-311.pyc", "path_type": "hardlink", "sha256": "a940b6fb8f8b87e6ca25e6bc8ac412de1b9932f42b8aa51e3c230565d6fd4613", "sha256_in_prefix": "a940b6fb8f8b87e6ca25e6bc8ac412de1b9932f42b8aa51e3c230565d6fd4613", "size_in_bytes": 2657}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/pytorch_export_contrib_ops.cpython-311.pyc", "path_type": "hardlink", "sha256": "0ce5d821fef4829069bcd09988c64cc03f489abb1ae7701a37c086315ce905c5", "sha256_in_prefix": "0ce5d821fef4829069bcd09988c64cc03f489abb1ae7701a37c086315ce905c5", "size_in_bytes": 6577}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/pytorch_export_helpers.cpython-311.pyc", "path_type": "hardlink", "sha256": "2bf968c13ba26b92b311587bfb7d929547362e5e560f43cc48bc425ce7e0e012", "sha256_in_prefix": "2bf968c13ba26b92b311587bfb7d929547362e5e560f43cc48bc425ce7e0e012", "size_in_bytes": 5794}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/reduced_build_config_parser.cpython-311.pyc", "path_type": "hardlink", "sha256": "ee0c8f5e0731bd8c093f0847360cbfc47f97c4e9b6be60316cfed3c9c8db00fe", "sha256_in_prefix": "ee0c8f5e0731bd8c093f0847360cbfc47f97c4e9b6be60316cfed3c9c8db00fe", "size_in_bytes": 9893}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/symbolic_shape_infer.cpython-311.pyc", "path_type": "hardlink", "sha256": "7981c26d5785a9251092dcc62da432b63eb5a5c2389fe55fc0519efd3f990d85", "sha256_in_prefix": "7981c26d5785a9251092dcc62da432b63eb5a5c2389fe55fc0519efd3f990d85", "size_in_bytes": 195400}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/__pycache__/update_onnx_opset.cpython-311.pyc", "path_type": "hardlink", "sha256": "7f94b79d3ec387b318e2c4c04d885276eee045a2bedf393a1995118fdf8bc156", "sha256_in_prefix": "7f94b79d3ec387b318e2c4c04d885276eee045a2bedf393a1995118fdf8bc156", "size_in_bytes": 1835}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/check_onnx_model_mobile_usability.py", "path_type": "hardlink", "sha256": "f163240311641dad9d34cc033fde11c711b4e314a01bfc11bfe710f88229bb39", "sha256_in_prefix": "f163240311641dad9d34cc033fde11c711b4e314a01bfc11bfe710f88229bb39", "size_in_bytes": 1670}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/convert_onnx_models_to_ort.py", "path_type": "hardlink", "sha256": "0bcc1854bc16e799bb2ff226ff348874504f38065abe4da64b5b2747890c40cc", "sha256_in_prefix": "0bcc1854bc16e799bb2ff226ff348874504f38065abe4da64b5b2747890c40cc", "size_in_bytes": 16433}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/file_utils.py", "path_type": "hardlink", "sha256": "4158f78e261deb4be3ce3b14273c6a79affa3e95ea2f8ce6d160875f894aa16f", "sha256_in_prefix": "4158f78e261deb4be3ce3b14273c6a79affa3e95ea2f8ce6d160875f894aa16f", "size_in_bytes": 1525}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/logger.py", "path_type": "hardlink", "sha256": "e9d41a866f29808aab807d679569c2546048846e898ed95d2aff5f7676892a67", "sha256_in_prefix": "e9d41a866f29808aab807d679569c2546048846e898ed95d2aff5f7676892a67", "size_in_bytes": 322}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/make_dynamic_shape_fixed.py", "path_type": "hardlink", "sha256": "c2467ae5ea357592b330e8c5027d1e16b3be5c68bbad9206f3be7477d9ead161", "sha256_in_prefix": "c2467ae5ea357592b330e8c5027d1e16b3be5c68bbad9206f3be7477d9ead161", "size_in_bytes": 2569}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/mobile_helpers/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/mobile_helpers/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "c5f486d47b4ac8c1eb033410efe77975f1574a169aa75c757457d2bde3a7ec41", "sha256_in_prefix": "c5f486d47b4ac8c1eb033410efe77975f1574a169aa75c757457d2bde3a7ec41", "size_in_bytes": 182}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/mobile_helpers/__pycache__/usability_checker.cpython-311.pyc", "path_type": "hardlink", "sha256": "c7a400ea1ed8abdd444ce3e3acc290a2db5cc61024dd34488e74a21b9b11813e", "sha256_in_prefix": "c7a400ea1ed8abdd444ce3e3acc290a2db5cc61024dd34488e74a21b9b11813e", "size_in_bytes": 33207}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/mobile_helpers/coreml_supported_mlprogram_ops.md", "path_type": "hardlink", "sha256": "a9c211bf540175dbd2f4ef3feb383f8d5fd087def73a211a9ca24221d6311dd0", "sha256_in_prefix": "a9c211bf540175dbd2f4ef3feb383f8d5fd087def73a211a9ca24221d6311dd0", "size_in_bytes": 2353}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/mobile_helpers/coreml_supported_neuralnetwork_ops.md", "path_type": "hardlink", "sha256": "7c160969aa62d276a3a27a663c39090512d7c835db4d878869128c8527acf376", "sha256_in_prefix": "7c160969aa62d276a3a27a663c39090512d7c835db4d878869128c8527acf376", "size_in_bytes": 1915}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/mobile_helpers/nnapi_supported_ops.md", "path_type": "hardlink", "sha256": "e4ca3f87eb530f357ccb5cced6a39b8816e7e00ba40bff4bd181260a11543825", "sha256_in_prefix": "e4ca3f87eb530f357ccb5cced6a39b8816e7e00ba40bff4bd181260a11543825", "size_in_bytes": 2327}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/mobile_helpers/usability_checker.py", "path_type": "hardlink", "sha256": "f1d41e18032b634e761cd336dc7cb966095f7243bd3bc1b35c0e1a16b168bb1b", "sha256_in_prefix": "f1d41e18032b634e761cd336dc7cb966095f7243bd3bc1b35c0e1a16b168bb1b", "size_in_bytes": 31623}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/offline_tuning.py", "path_type": "hardlink", "sha256": "25572a4ab56c7a34c7b9d45c5a5ffca36ec480892a5eb79342b3039bca1d67e9", "sha256_in_prefix": "25572a4ab56c7a34c7b9d45c5a5ffca36ec480892a5eb79342b3039bca1d67e9", "size_in_bytes": 6199}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/onnx_model_utils.py", "path_type": "hardlink", "sha256": "7251f1253da8e8e3c70907e041848e5d57733a8f8edbc5bc15d827aac0f2a409", "sha256_in_prefix": "7251f1253da8e8e3c70907e041848e5d57733a8f8edbc5bc15d827aac0f2a409", "size_in_bytes": 16273}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/onnx_randomizer.py", "path_type": "hardlink", "sha256": "4eb705a57590426a13551884c325b4e1e37d126d879500997dd3af656f71e78a", "sha256_in_prefix": "4eb705a57590426a13551884c325b4e1e37d126d879500997dd3af656f71e78a", "size_in_bytes": 3276}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/onnxruntime_test.py", "path_type": "hardlink", "sha256": "34a6cc232ea2a1ff5516194e75888eccd31a8df700ab4a9707259728238fb9cb", "sha256_in_prefix": "34a6cc232ea2a1ff5516194e75888eccd31a8df700ab4a9707259728238fb9cb", "size_in_bytes": 5606}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/optimize_onnx_model.py", "path_type": "hardlink", "sha256": "f9834b6d94d6bf0123ed92cc222844e7ac7dd605686575a6cfaf5d7821889bd9", "sha256_in_prefix": "f9834b6d94d6bf0123ed92cc222844e7ac7dd605686575a6cfaf5d7821889bd9", "size_in_bytes": 1949}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/__init__.py", "path_type": "hardlink", "sha256": "6ad6e1088a303c660e4a2ad9c18082dfede79ec7a72c787237637cbcc9dc10b8", "sha256_in_prefix": "6ad6e1088a303c660e4a2ad9c18082dfede79ec7a72c787237637cbcc9dc10b8", "size_in_bytes": 1280}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "cbb91f038e217e1b6ce58e15ac1f98d4ab7e93ab157f6c2016744c258db0abfd", "sha256_in_prefix": "cbb91f038e217e1b6ce58e15ac1f98d4ab7e93ab157f6c2016744c258db0abfd", "size_in_bytes": 1399}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/__pycache__/operator_type_usage_processors.cpython-311.pyc", "path_type": "hardlink", "sha256": "1c640e6327e892ccb85910f24b40fea53b05483c4a02bf4810a89a34d3ce43c3", "sha256_in_prefix": "1c640e6327e892ccb85910f24b40fea53b05483c4a02bf4810a89a34d3ce43c3", "size_in_bytes": 33694}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/__pycache__/ort_model_processor.cpython-311.pyc", "path_type": "hardlink", "sha256": "b44d334ce70379457f68d6c740e6d37360cfcd708d85fcf6e2e5d80a92c29a9b", "sha256_in_prefix": "b44d334ce70379457f68d6c740e6d37360cfcd708d85fcf6e2e5d80a92c29a9b", "size_in_bytes": 6284}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/__pycache__/types.cpython-311.pyc", "path_type": "hardlink", "sha256": "eb7ca119d17a0b365d71ca34d453e4e31bd8c16d045f3273b129f510400c51a6", "sha256_in_prefix": "eb7ca119d17a0b365d71ca34d453e4e31bd8c16d045f3273b129f510400c51a6", "size_in_bytes": 4900}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/__pycache__/utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "756491f03b4f10ea7f7a41ebc1d1c36e4b40bfb669a9d284a4fe75cb7452b316", "sha256_in_prefix": "756491f03b4f10ea7f7a41ebc1d1c36e4b40bfb669a9d284a4fe75cb7452b316", "size_in_bytes": 3907}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/operator_type_usage_processors.py", "path_type": "hardlink", "sha256": "9e33e7d1567e9267aad28b4c6599ba85ee878694e9cb95ab2e3435e67ff4ce4e", "sha256_in_prefix": "9e33e7d1567e9267aad28b4c6599ba85ee878694e9cb95ab2e3435e67ff4ce4e", "size_in_bytes": 26379}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "b1d391835c1e83e9ef8471f558c3691200a3ba4c4a6b0602dcba4a2eb2b499fe", "sha256_in_prefix": "b1d391835c1e83e9ef8471f558c3691200a3ba4c4a6b0602dcba4a2eb2b499fe", "size_in_bytes": 203}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ArgType.py", "path_type": "hardlink", "sha256": "a88de1f4ce5b7b545d37dc63b4f65f3c0f1fd6760ff5f4e744e3411d565c9cb7", "sha256_in_prefix": "a88de1f4ce5b7b545d37dc63b4f65f3c0f1fd6760ff5f4e744e3411d565c9cb7", "size_in_bytes": 140}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ArgTypeAndIndex.py", "path_type": "hardlink", "sha256": "be7d76a9d9944741d4d071c4468e5fe542f81d691f5cf5baa317878dcb525319", "sha256_in_prefix": "be7d76a9d9944741d4d071c4468e5fe542f81d691f5cf5baa317878dcb525319", "size_in_bytes": 2026}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Attribute.py", "path_type": "hardlink", "sha256": "01152d7eb462c7a492727187602ae4846677db8f3495d3567f179004e17edf94", "sha256_in_prefix": "01152d7eb462c7a492727187602ae4846677db8f3495d3567f179004e17edf94", "size_in_bytes": 10850}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/AttributeType.py", "path_type": "hardlink", "sha256": "9df4a33bcd3e69b0659d03add0bfaf772b82a88494872ad6fd8eb0d6a71ecd16", "sha256_in_prefix": "9df4a33bcd3e69b0659d03add0bfaf772b82a88494872ad6fd8eb0d6a71ecd16", "size_in_bytes": 328}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Checkpoint.py", "path_type": "hardlink", "sha256": "097abb2972ca172eda88de8cc9e42b91db4099ba4ec5fd6ed3c2d7c2f9ead0ec", "sha256_in_prefix": "097abb2972ca172eda88de8cc9e42b91db4099ba4ec5fd6ed3c2d7c2f9ead0ec", "size_in_bytes": 4217}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedKernelCreateInfos.py", "path_type": "hardlink", "sha256": "1152d036336b214a86996fde4aa8f26235d24cd41dd0c0d12d2f43c5b90b3351", "sha256_in_prefix": "1152d036336b214a86996fde4aa8f26235d24cd41dd0c0d12d2f43c5b90b3351", "size_in_bytes": 4528}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedNodeIndexAndKernelDefHash.py", "path_type": "hardlink", "sha256": "f6f8422df0c69d56830ba601f9b20f6baa4b04cae4701ef172b322b047b3b12e", "sha256_in_prefix": "f6f8422df0c69d56830ba601f9b20f6baa4b04cae4701ef172b322b047b3b12e", "size_in_bytes": 2458}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedSessionState.py", "path_type": "hardlink", "sha256": "e799014012fbe7de1511f6ed99caf2f0c263f2950e8b8836026a8e9ea8366a49", "sha256_in_prefix": "e799014012fbe7de1511f6ed99caf2f0c263f2950e8b8836026a8e9ea8366a49", "size_in_bytes": 3582}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DeprecatedSubGraphSessionState.py", "path_type": "hardlink", "sha256": "cf84d32a8e906d07ab9c125baf2d25ffd1bd2a8d458df677437255c2a672bb17", "sha256_in_prefix": "cf84d32a8e906d07ab9c125baf2d25ffd1bd2a8d458df677437255c2a672bb17", "size_in_bytes": 2610}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Dimension.py", "path_type": "hardlink", "sha256": "7eb48109664528f7e2ac59450b32a71775ecd8c90eeadebfc9bd11e6afa3d9e3", "sha256_in_prefix": "7eb48109664528f7e2ac59450b32a71775ecd8c90eeadebfc9bd11e6afa3d9e3", "size_in_bytes": 2191}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DimensionValue.py", "path_type": "hardlink", "sha256": "c89d085f6c9f37521af1dc9decde67ac21e568a7f56dcba284c0ea097ded6562", "sha256_in_prefix": "c89d085f6c9f37521af1dc9decde67ac21e568a7f56dcba284c0ea097ded6562", "size_in_bytes": 2494}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/DimensionValueType.py", "path_type": "hardlink", "sha256": "300163a0d73be3ad4f94209adddd4de8cc74fae9ec9bf973c64ddbad0970019f", "sha256_in_prefix": "300163a0d73be3ad4f94209adddd4de8cc74fae9ec9bf973c64ddbad0970019f", "size_in_bytes": 166}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/EdgeEnd.py", "path_type": "hardlink", "sha256": "716b8c340f58e956d5990864c88143f4decd34f3ac0db32b64fed5b8a819ecb4", "sha256_in_prefix": "716b8c340f58e956d5990864c88143f4decd34f3ac0db32b64fed5b8a819ecb4", "size_in_bytes": 1105}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/FloatProperty.py", "path_type": "hardlink", "sha256": "3221abf8ef0bc744e840bfe498bbbc768eba6c1f8174d33c20abea77085d827b", "sha256_in_prefix": "3221abf8ef0bc744e840bfe498bbbc768eba6c1f8174d33c20abea77085d827b", "size_in_bytes": 2008}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Graph.py", "path_type": "hardlink", "sha256": "c1305274847455693a90b9445688c403fd70768fe36e0ffac8277d1f2e8ebc74", "sha256_in_prefix": "c1305274847455693a90b9445688c403fd70768fe36e0ffac8277d1f2e8ebc74", "size_in_bytes": 10719}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/InferenceSession.py", "path_type": "hardlink", "sha256": "eab477f6ef5dfe053845d94b7b7f4bd2ece5fb964099e0a1227f3565df864fe3", "sha256_in_prefix": "eab477f6ef5dfe053845d94b7b7f4bd2ece5fb964099e0a1227f3565df864fe3", "size_in_bytes": 3037}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/IntProperty.py", "path_type": "hardlink", "sha256": "91f04d09c4fb1d410fb7d4d97c8711860777c9f47f1ab0e3b0597ae851d4f10a", "sha256_in_prefix": "91f04d09c4fb1d410fb7d4d97c8711860777c9f47f1ab0e3b0597ae851d4f10a", "size_in_bytes": 1970}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/KernelTypeStrArgsEntry.py", "path_type": "hardlink", "sha256": "91ecddf655af14f6d6323cbd2e3e0e316d6f72ba2d646a382c307ad3ef61180a", "sha256_in_prefix": "91ecddf655af14f6d6323cbd2e3e0e316d6f72ba2d646a382c307ad3ef61180a", "size_in_bytes": 3102}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/KernelTypeStrResolver.py", "path_type": "hardlink", "sha256": "841d1183dd99668aef5287b6335b2d6a0bbdb4ab47be201458736c92faaa22d0", "sha256_in_prefix": "841d1183dd99668aef5287b6335b2d6a0bbdb4ab47be201458736c92faaa22d0", "size_in_bytes": 2789}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/MapType.py", "path_type": "hardlink", "sha256": "f919a9b99457326882d85a62576eb480bb506fa261f6a7015f22a5a776c46e88", "sha256_in_prefix": "f919a9b99457326882d85a62576eb480bb506fa261f6a7015f22a5a776c46e88", "size_in_bytes": 2123}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Model.py", "path_type": "hardlink", "sha256": "dc81559f18e8f3fd4c910e8f58eb305bcf1df8221036637ae8d7e40e4adaf8ec", "sha256_in_prefix": "dc81559f18e8f3fd4c910e8f58eb305bcf1df8221036637ae8d7e40e4adaf8ec", "size_in_bytes": 7440}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ModuleState.py", "path_type": "hardlink", "sha256": "5e6eeb48c5be4893c71162e8601ee0fc59cff799e7d62d41eca4bd55d6c5eb4e", "sha256_in_prefix": "5e6eeb48c5be4893c71162e8601ee0fc59cff799e7d62d41eca4bd55d6c5eb4e", "size_in_bytes": 4853}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Node.py", "path_type": "hardlink", "sha256": "20a5ddb00fb84a51f23933837adbdef95fed61ed3d2d107a7176f82b5ffb3c48", "sha256_in_prefix": "20a5ddb00fb84a51f23933837adbdef95fed61ed3d2d107a7176f82b5ffb3c48", "size_in_bytes": 10401}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodeEdge.py", "path_type": "hardlink", "sha256": "d982bdedc50faf7172750d51ba50c0a38a893007a9640df341b5e50f9f64e253", "sha256_in_prefix": "d982bdedc50faf7172750d51ba50c0a38a893007a9640df341b5e50f9f64e253", "size_in_bytes": 4057}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodeType.py", "path_type": "hardlink", "sha256": "f1e340673696bc427d7084bbd63a06f56a3ab9847e1935707d2c3b99f545d79d", "sha256_in_prefix": "f1e340673696bc427d7084bbd63a06f56a3ab9847e1935707d2c3b99f545d79d", "size_in_bytes": 144}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/NodesToOptimizeIndices.py", "path_type": "hardlink", "sha256": "912b540e3b76db7a7f6a40fa6ea436843d09138205247b720dbccc948764982b", "sha256_in_prefix": "912b540e3b76db7a7f6a40fa6ea436843d09138205247b720dbccc948764982b", "size_in_bytes": 5984}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OpIdKernelTypeStrArgsEntry.py", "path_type": "hardlink", "sha256": "44e854d35c62987fb0c30265e72e539058c06f94d1c8d29b5840e4038d663381", "sha256_in_prefix": "44e854d35c62987fb0c30265e72e539058c06f94d1c8d29b5840e4038d663381", "size_in_bytes": 3296}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OperatorSetId.py", "path_type": "hardlink", "sha256": "074775e23b00f821576ead93262c456ad34f61b68c7cc6bdacddfa73688ef125", "sha256_in_prefix": "074775e23b00f821576ead93262c456ad34f61b68c7cc6bdacddfa73688ef125", "size_in_bytes": 2032}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/OptimizerGroup.py", "path_type": "hardlink", "sha256": "f6711ed86d69b37e763041096ea27c543082608f2e0f20166c84192e7adf9c43", "sha256_in_prefix": "f6711ed86d69b37e763041096ea27c543082608f2e0f20166c84192e7adf9c43", "size_in_bytes": 4018}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ParameterOptimizerState.py", "path_type": "hardlink", "sha256": "8dacfbd095bf019addbc9a3d83f4658b3865283d81c06190528ce596339872b2", "sha256_in_prefix": "8dacfbd095bf019addbc9a3d83f4658b3865283d81c06190528ce596339872b2", "size_in_bytes": 3127}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/PropertyBag.py", "path_type": "hardlink", "sha256": "1b08c5faadfab986afd3d85641d261e6ea22edc5eb6e5bb82f549e6f1dcd1cef", "sha256_in_prefix": "1b08c5faadfab986afd3d85641d261e6ea22edc5eb6e5bb82f549e6f1dcd1cef", "size_in_bytes": 4947}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizationRecord.py", "path_type": "hardlink", "sha256": "9383580a2fc1660fcab29d475909c867d14dda8cfc2a7c7c6111998773f79b41", "sha256_in_prefix": "9383580a2fc1660fcab29d475909c867d14dda8cfc2a7c7c6111998773f79b41", "size_in_bytes": 3962}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizationRecordContainerEntry.py", "path_type": "hardlink", "sha256": "d2c0fe10456793f70a19c31c49415464fcd32793703ca08d60cbd02cc1e6d597", "sha256_in_prefix": "d2c0fe10456793f70a19c31c49415464fcd32793703ca08d60cbd02cc1e6d597", "size_in_bytes": 3741}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/RuntimeOptimizations.py", "path_type": "hardlink", "sha256": "18b9673fa41177169dfe3c8a6c4ff79b85157bd380c07dc6cf744ee36ef02858", "sha256_in_prefix": "18b9673fa41177169dfe3c8a6c4ff79b85157bd380c07dc6cf744ee36ef02858", "size_in_bytes": 2721}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/SequenceType.py", "path_type": "hardlink", "sha256": "e02c0cbaa5380d86c349186ba76effad2b964638c30738c3bf47b54bb7f77bb0", "sha256_in_prefix": "e02c0cbaa5380d86c349186ba76effad2b964638c30738c3bf47b54bb7f77bb0", "size_in_bytes": 1771}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Shape.py", "path_type": "hardlink", "sha256": "ea6e0838faf80ceececa1012d616cc570104ac7077442f7981334746174c6f72", "sha256_in_prefix": "ea6e0838faf80ceececa1012d616cc570104ac7077442f7981334746174c6f72", "size_in_bytes": 2274}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/SparseTensor.py", "path_type": "hardlink", "sha256": "78b5695691cfdac8d34497f4dcbe97d3994a273ef92c979d7822205aacf7ba91", "sha256_in_prefix": "78b5695691cfdac8d34497f4dcbe97d3994a273ef92c979d7822205aacf7ba91", "size_in_bytes": 3692}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/StringProperty.py", "path_type": "hardlink", "sha256": "77d80a24826b57b412875dc2b376699b2e53fdd3ed9aaa6682e9654ff23d7ab7", "sha256_in_prefix": "77d80a24826b57b412875dc2b376699b2e53fdd3ed9aaa6682e9654ff23d7ab7", "size_in_bytes": 2043}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/StringStringEntry.py", "path_type": "hardlink", "sha256": "e48415976dcc158bfa4f8da7286bc92a54f1e72ef19b4f5a419d92e8b1e3d38c", "sha256_in_prefix": "e48415976dcc158bfa4f8da7286bc92a54f1e72ef19b4f5a419d92e8b1e3d38c", "size_in_bytes": 2080}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/Tensor.py", "path_type": "hardlink", "sha256": "1b3d0bf9ac30e0e2ad81a2bafadcf1705443ab9fa0cf035f0536036ccf20e5e2", "sha256_in_prefix": "1b3d0bf9ac30e0e2ad81a2bafadcf1705443ab9fa0cf035f0536036ccf20e5e2", "size_in_bytes": 6599}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TensorDataType.py", "path_type": "hardlink", "sha256": "05236aba77882eb2fc32e328023c5c42b2fdbe0f562b8b4ee4dc6e8023863dbf", "sha256_in_prefix": "05236aba77882eb2fc32e328023c5c42b2fdbe0f562b8b4ee4dc6e8023863dbf", "size_in_bytes": 474}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TensorTypeAndShape.py", "path_type": "hardlink", "sha256": "94d423a69770d8b7f93b647789cd634b54ea2b5712342a682d37aaf55b992fd2", "sha256_in_prefix": "94d423a69770d8b7f93b647789cd634b54ea2b5712342a682d37aaf55b992fd2", "size_in_bytes": 2255}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TypeInfo.py", "path_type": "hardlink", "sha256": "234ef8b3bf051579fdd8b7b44ba0e47d2ba54b2fc719fbc1bd4a25fd698e9444", "sha256_in_prefix": "234ef8b3bf051579fdd8b7b44ba0e47d2ba54b2fc719fbc1bd4a25fd698e9444", "size_in_bytes": 2516}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/TypeInfoValue.py", "path_type": "hardlink", "sha256": "7749eeec4b0bd5db475c6556a2c40801da5463396204e90fd62cb3982766e2ad", "sha256_in_prefix": "7749eeec4b0bd5db475c6556a2c40801da5463396204e90fd62cb3982766e2ad", "size_in_bytes": 189}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/ValueInfo.py", "path_type": "hardlink", "sha256": "efec99056d794421bfdaa80a4c67e6154c640d8431d77eb8182248670eb4af8a", "sha256_in_prefix": "efec99056d794421bfdaa80a4c67e6154c640d8431d77eb8182248670eb4af8a", "size_in_bytes": 2571}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__init__.py", "path_type": "hardlink", "sha256": "ea499fd8cfe2c9e5dccd3d6855aa78e1d1ee3b7d6c5214bec13f719d02aa2992", "sha256_in_prefix": "ea499fd8cfe2c9e5dccd3d6855aa78e1d1ee3b7d6c5214bec13f719d02aa2992", "size_in_bytes": 245}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ArgType.cpython-311.pyc", "path_type": "hardlink", "sha256": "60c51ebe32e181d31368cf194a93bdee46d45419d58306053b1a6f9696a927f2", "sha256_in_prefix": "60c51ebe32e181d31368cf194a93bdee46d45419d58306053b1a6f9696a927f2", "size_in_bytes": 477}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ArgTypeAndIndex.cpython-311.pyc", "path_type": "hardlink", "sha256": "c02e6cec666f501ce861884631cf410b754a2a8a0c84644f77adfd3fd04044b0", "sha256_in_prefix": "c02e6cec666f501ce861884631cf410b754a2a8a0c84644f77adfd3fd04044b0", "size_in_bytes": 4765}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Attribute.cpython-311.pyc", "path_type": "hardlink", "sha256": "60513ed1531acf93f2f9b7d5408d2f97c0864d7257b2f04d9f23c5866e6f8bbe", "sha256_in_prefix": "60513ed1531acf93f2f9b7d5408d2f97c0864d7257b2f04d9f23c5866e6f8bbe", "size_in_bytes": 22356}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/AttributeType.cpython-311.pyc", "path_type": "hardlink", "sha256": "3bb5e4d59bce39f4f6c166cec8970503048a6216e58aa04e996bca7f2cb46474", "sha256_in_prefix": "3bb5e4d59bce39f4f6c166cec8970503048a6216e58aa04e996bca7f2cb46474", "size_in_bytes": 745}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Checkpoint.cpython-311.pyc", "path_type": "hardlink", "sha256": "3f8cfb07935845eb727e2b7e353307c8e81c96689d9aba3d8e845ffb08d5bc76", "sha256_in_prefix": "3f8cfb07935845eb727e2b7e353307c8e81c96689d9aba3d8e845ffb08d5bc76", "size_in_bytes": 8904}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedKernelCreateInfos.cpython-311.pyc", "path_type": "hardlink", "sha256": "30adbd7ebf9eecc8ecc5b82a3d647541261fe63a0d76225fcd9c3e1bf2eef568", "sha256_in_prefix": "30adbd7ebf9eecc8ecc5b82a3d647541261fe63a0d76225fcd9c3e1bf2eef568", "size_in_bytes": 8802}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedNodeIndexAndKernelDefHash.cpython-311.pyc", "path_type": "hardlink", "sha256": "9ea6acf356a16bd1d7466e17f3a0731258cd876c638837de402658f9a1d0a272", "sha256_in_prefix": "9ea6acf356a16bd1d7466e17f3a0731258cd876c638837de402658f9a1d0a272", "size_in_bytes": 5051}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedSessionState.cpython-311.pyc", "path_type": "hardlink", "sha256": "a34438f5a6d507683ee84296cea0bba2dcc09f183fd5c47b57a0d687b414dc13", "sha256_in_prefix": "a34438f5a6d507683ee84296cea0bba2dcc09f183fd5c47b57a0d687b414dc13", "size_in_bytes": 7179}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DeprecatedSubGraphSessionState.cpython-311.pyc", "path_type": "hardlink", "sha256": "6e498d53e488ae9ece1feb7eb8e635bf62d1d9b217e352f105511038c1c08e08", "sha256_in_prefix": "6e498d53e488ae9ece1feb7eb8e635bf62d1d9b217e352f105511038c1c08e08", "size_in_bytes": 5428}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Dimension.cpython-311.pyc", "path_type": "hardlink", "sha256": "5eee8c15a69dcc60f65a3285bf3e67cea090ef668f688deae6aa276582264353", "sha256_in_prefix": "5eee8c15a69dcc60f65a3285bf3e67cea090ef668f688deae6aa276582264353", "size_in_bytes": 5050}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DimensionValue.cpython-311.pyc", "path_type": "hardlink", "sha256": "261f62748d4745b94ec136239f7346de13596fd3ff1ca52f8fada6d5d170eb46", "sha256_in_prefix": "261f62748d4745b94ec136239f7346de13596fd3ff1ca52f8fada6d5d170eb46", "size_in_bytes": 5761}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/DimensionValueType.cpython-311.pyc", "path_type": "hardlink", "sha256": "e895b07e01008ac113f4c9e508775281a36350db599d1b2ffcb97d606e3a783b", "sha256_in_prefix": "e895b07e01008ac113f4c9e508775281a36350db599d1b2ffcb97d606e3a783b", "size_in_bytes": 521}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/EdgeEnd.cpython-311.pyc", "path_type": "hardlink", "sha256": "ca9127b2aa59097a736fc6e4bf2400d51f1d6c054432d874dd0e0416f9eba4d9", "sha256_in_prefix": "ca9127b2aa59097a736fc6e4bf2400d51f1d6c054432d874dd0e0416f9eba4d9", "size_in_bytes": 2819}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/FloatProperty.cpython-311.pyc", "path_type": "hardlink", "sha256": "455c8a7dfe09d22d53bcf0590f50a27787a273bbbf0522363bd0aed7ad18ba5d", "sha256_in_prefix": "455c8a7dfe09d22d53bcf0590f50a27787a273bbbf0522363bd0aed7ad18ba5d", "size_in_bytes": 4806}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Graph.cpython-311.pyc", "path_type": "hardlink", "sha256": "72b63b9fa68d7e01fbf95c709cd010041806f4a8ccc71763657643d6ce16fce8", "sha256_in_prefix": "72b63b9fa68d7e01fbf95c709cd010041806f4a8ccc71763657643d6ce16fce8", "size_in_bytes": 21186}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/InferenceSession.cpython-311.pyc", "path_type": "hardlink", "sha256": "de8737657ac0f4d98714832ff10099d8253efbc9074f84d55e58debf93d0be6f", "sha256_in_prefix": "de8737657ac0f4d98714832ff10099d8253efbc9074f84d55e58debf93d0be6f", "size_in_bytes": 6442}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/IntProperty.cpython-311.pyc", "path_type": "hardlink", "sha256": "3f9c4d773e1de6351fd30a82367c9755bf6c939d772a81948cf23b35e3a3df48", "sha256_in_prefix": "3f9c4d773e1de6351fd30a82367c9755bf6c939d772a81948cf23b35e3a3df48", "size_in_bytes": 4761}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/KernelTypeStrArgsEntry.cpython-311.pyc", "path_type": "hardlink", "sha256": "b4e29ac0e5d4e3099ff7930afec644fd7c5ffea437d66811f42cdfbb3ec59bc1", "sha256_in_prefix": "b4e29ac0e5d4e3099ff7930afec644fd7c5ffea437d66811f42cdfbb3ec59bc1", "size_in_bytes": 6710}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/KernelTypeStrResolver.cpython-311.pyc", "path_type": "hardlink", "sha256": "972f5b9ffe8a9966402beb796cbf2f69b1d331f6d70b23e8ac3425be81dd79df", "sha256_in_prefix": "972f5b9ffe8a9966402beb796cbf2f69b1d331f6d70b23e8ac3425be81dd79df", "size_in_bytes": 5859}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/MapType.cpython-311.pyc", "path_type": "hardlink", "sha256": "222b154288f34307f3b2cbe52eab0d489ce5051467ba859326e4691674c9a85b", "sha256_in_prefix": "222b154288f34307f3b2cbe52eab0d489ce5051467ba859326e4691674c9a85b", "size_in_bytes": 4978}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Model.cpython-311.pyc", "path_type": "hardlink", "sha256": "9211c54e20d134732bf7a8868d1982884c460ace3b234c426c8ecc0b7b836c0e", "sha256_in_prefix": "9211c54e20d134732bf7a8868d1982884c460ace3b234c426c8ecc0b7b836c0e", "size_in_bytes": 15383}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ModuleState.cpython-311.pyc", "path_type": "hardlink", "sha256": "e91492ee774319418cefb10e3c35aa2a926e474ac5d100b12d77ab6abb60a845", "sha256_in_prefix": "e91492ee774319418cefb10e3c35aa2a926e474ac5d100b12d77ab6abb60a845", "size_in_bytes": 9696}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Node.cpython-311.pyc", "path_type": "hardlink", "sha256": "32125dc7e05cc7312cf92525eb357d8bc18c9218b591676748a0e82723ea512f", "sha256_in_prefix": "32125dc7e05cc7312cf92525eb357d8bc18c9218b591676748a0e82723ea512f", "size_in_bytes": 20769}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/NodeEdge.cpython-311.pyc", "path_type": "hardlink", "sha256": "71bcab33ee68195edb2dc161137a3b860ad0c90b5d3e04158d0a694028eda6f2", "sha256_in_prefix": "71bcab33ee68195edb2dc161137a3b860ad0c90b5d3e04158d0a694028eda6f2", "size_in_bytes": 8381}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/NodeType.cpython-311.pyc", "path_type": "hardlink", "sha256": "e3996cbeb521847916f62ea7fede012d36f6c45f8e43012fe153a9533e329cac", "sha256_in_prefix": "e3996cbeb521847916f62ea7fede012d36f6c45f8e43012fe153a9533e329cac", "size_in_bytes": 482}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/NodesToOptimizeIndices.cpython-311.pyc", "path_type": "hardlink", "sha256": "60c485ffd9ad21e6a9c4296f2eb1a6b76aa29ded796d230c232e592beabe202c", "sha256_in_prefix": "60c485ffd9ad21e6a9c4296f2eb1a6b76aa29ded796d230c232e592beabe202c", "size_in_bytes": 11556}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/OpIdKernelTypeStrArgsEntry.cpython-311.pyc", "path_type": "hardlink", "sha256": "1a44df94dc844e287dd46827051590e7968cf30aef07fd9170beb7ae4b9b85f9", "sha256_in_prefix": "1a44df94dc844e287dd46827051590e7968cf30aef07fd9170beb7ae4b9b85f9", "size_in_bytes": 6900}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/OperatorSetId.cpython-311.pyc", "path_type": "hardlink", "sha256": "4f3b93b34704c138e58cb912e34bd3eb631e11ddde96643df92f8d93c107ecd1", "sha256_in_prefix": "4f3b93b34704c138e58cb912e34bd3eb631e11ddde96643df92f8d93c107ecd1", "size_in_bytes": 4813}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/OptimizerGroup.cpython-311.pyc", "path_type": "hardlink", "sha256": "916c008a166a3e72c67cd5deaf060c219b0934ffcd3104a960bdf78a00b592e2", "sha256_in_prefix": "916c008a166a3e72c67cd5deaf060c219b0934ffcd3104a960bdf78a00b592e2", "size_in_bytes": 8623}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ParameterOptimizerState.cpython-311.pyc", "path_type": "hardlink", "sha256": "2181e859d9629eece8803d41cf566e04098042062d4f584af4a0cde4bf6927ab", "sha256_in_prefix": "2181e859d9629eece8803d41cf566e04098042062d4f584af4a0cde4bf6927ab", "size_in_bytes": 6663}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/PropertyBag.cpython-311.pyc", "path_type": "hardlink", "sha256": "fada982f28aa09936d3af68df22724b57e43b4300ea5758ae92f33d0d5ccf0b4", "sha256_in_prefix": "fada982f28aa09936d3af68df22724b57e43b4300ea5758ae92f33d0d5ccf0b4", "size_in_bytes": 10206}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/RuntimeOptimizationRecord.cpython-311.pyc", "path_type": "hardlink", "sha256": "78f8c156bec64bb0e15b8e4d5199f76e22924b8bdc0aba9082a1127d5552c590", "sha256_in_prefix": "78f8c156bec64bb0e15b8e4d5199f76e22924b8bdc0aba9082a1127d5552c590", "size_in_bytes": 7910}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/RuntimeOptimizationRecordContainerEntry.cpython-311.pyc", "path_type": "hardlink", "sha256": "fee563575001e7cb4a136a385017ec6da88d215d8d6828aa1def1e372d1a9cb3", "sha256_in_prefix": "fee563575001e7cb4a136a385017ec6da88d215d8d6828aa1def1e372d1a9cb3", "size_in_bytes": 7302}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/RuntimeOptimizations.cpython-311.pyc", "path_type": "hardlink", "sha256": "8ba4662d3a3f988f58154c35d14292b48e1a96e64e3b5aa5c6aefa5c29a1d07f", "sha256_in_prefix": "8ba4662d3a3f988f58154c35d14292b48e1a96e64e3b5aa5c6aefa5c29a1d07f", "size_in_bytes": 5732}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/SequenceType.cpython-311.pyc", "path_type": "hardlink", "sha256": "30bc761ecff8dfd4469f472feef09e4569f6a5bdd6fb3c3c8a4271085c2523a5", "sha256_in_prefix": "30bc761ecff8dfd4469f472feef09e4569f6a5bdd6fb3c3c8a4271085c2523a5", "size_in_bytes": 4109}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Shape.cpython-311.pyc", "path_type": "hardlink", "sha256": "427486c83dee8a92901c56a2ee7c25bde31553df62d5b271782f4946e2e2efc0", "sha256_in_prefix": "427486c83dee8a92901c56a2ee7c25bde31553df62d5b271782f4946e2e2efc0", "size_in_bytes": 5362}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/SparseTensor.cpython-311.pyc", "path_type": "hardlink", "sha256": "9c6f15a81fd9b1c15d978fe38f66606b131310c97a393c700011afb8e2966db4", "sha256_in_prefix": "9c6f15a81fd9b1c15d978fe38f66606b131310c97a393c700011afb8e2966db4", "size_in_bytes": 7949}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/StringProperty.cpython-311.pyc", "path_type": "hardlink", "sha256": "1f55d9b4e6a597e2f021060fcd4f12bb08a37de2e17c0115cce7e35f8a5276c6", "sha256_in_prefix": "1f55d9b4e6a597e2f021060fcd4f12bb08a37de2e17c0115cce7e35f8a5276c6", "size_in_bytes": 4714}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/StringStringEntry.cpython-311.pyc", "path_type": "hardlink", "sha256": "b2411f767bd4ff534239eb9f61fd432d2768ab281f23bc7ddca7a6ab246f016b", "sha256_in_prefix": "b2411f767bd4ff534239eb9f61fd432d2768ab281f23bc7ddca7a6ab246f016b", "size_in_bytes": 4757}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/Tensor.cpython-311.pyc", "path_type": "hardlink", "sha256": "c4b71c374714a833c7f56239df53bff68e8a3e62c46ddf6cfae6ed83fc45f2ee", "sha256_in_prefix": "c4b71c374714a833c7f56239df53bff68e8a3e62c46ddf6cfae6ed83fc45f2ee", "size_in_bytes": 14151}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TensorDataType.cpython-311.pyc", "path_type": "hardlink", "sha256": "5e8aeff066176a4c11401462abb772926650e1724fa37fe3f8c923c3663ea8df", "sha256_in_prefix": "5e8aeff066176a4c11401462abb772926650e1724fa37fe3f8c923c3663ea8df", "size_in_bytes": 940}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TensorTypeAndShape.cpython-311.pyc", "path_type": "hardlink", "sha256": "a5be3343c64789c795e997b1999d9333d5a57718e429b73d9691cd184cdc44e0", "sha256_in_prefix": "a5be3343c64789c795e997b1999d9333d5a57718e429b73d9691cd184cdc44e0", "size_in_bytes": 5133}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TypeInfo.cpython-311.pyc", "path_type": "hardlink", "sha256": "4ff7a898243a44b7416057c0b024b931fd0d4b4fe2e9716206bed9fc2d29ec27", "sha256_in_prefix": "4ff7a898243a44b7416057c0b024b931fd0d4b4fe2e9716206bed9fc2d29ec27", "size_in_bytes": 5843}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/TypeInfoValue.cpython-311.pyc", "path_type": "hardlink", "sha256": "81f29bbfa5ff058fc498416b9b43bd334d69b1143cb287585212b2cd9b1edc05", "sha256_in_prefix": "81f29bbfa5ff058fc498416b9b43bd334d69b1143cb287585212b2cd9b1edc05", "size_in_bytes": 546}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/ValueInfo.cpython-311.pyc", "path_type": "hardlink", "sha256": "3522cdec06b1e193de6338a96522cea82c6b2855e44f1a2b41dc778873f969df", "sha256_in_prefix": "3522cdec06b1e193de6338a96522cea82c6b2855e44f1a2b41dc778873f969df", "size_in_bytes": 5767}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_flatbuffers_py/fbs/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "a9cd85515deacdc1a5b40286d65d0b82c3c020747a4de1f71aeac4cac581fbe7", "sha256_in_prefix": "a9cd85515deacdc1a5b40286d65d0b82c3c020747a4de1f71aeac4cac581fbe7", "size_in_bytes": 941}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/ort_model_processor.py", "path_type": "hardlink", "sha256": "6c9212ac5055e382dbd602d3aacb6032e89420dc737dcf7cb3da3359c11028ff", "sha256_in_prefix": "6c9212ac5055e382dbd602d3aacb6032e89420dc737dcf7cb3da3359c11028ff", "size_in_bytes": 4386}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/types.py", "path_type": "hardlink", "sha256": "65bf1669af8025cf781c95931740f17cdace55559c85b68833090538462d6748", "sha256_in_prefix": "65bf1669af8025cf781c95931740f17cdace55559c85b68833090538462d6748", "size_in_bytes": 4383}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/ort_format_model/utils.py", "path_type": "hardlink", "sha256": "72b4121d9bf772edf181755099f4f1d564bbc4778ed1003f1b11fcdb59b21b18", "sha256_in_prefix": "72b4121d9bf772edf181755099f4f1d564bbc4778ed1003f1b11fcdb59b21b18", "size_in_bytes": 2542}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/pytorch_export_contrib_ops.py", "path_type": "hardlink", "sha256": "a93fe02d2bf36b8783492396a245b4b2b2fb00e8c0d68fe915bcdbd1da4e3c12", "sha256_in_prefix": "a93fe02d2bf36b8783492396a245b4b2b2fb00e8c0d68fe915bcdbd1da4e3c12", "size_in_bytes": 4763}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/pytorch_export_helpers.py", "path_type": "hardlink", "sha256": "2dce1575bb257eb43a52d9c6ed4e99261c4dff4206fe81cc8167e3e052143f75", "sha256_in_prefix": "2dce1575bb257eb43a52d9c6ed4e99261c4dff4206fe81cc8167e3e052143f75", "size_in_bytes": 5840}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/qdq_helpers/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/qdq_helpers/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "35f21148c888879c5848b4bb01f0ca26f0ff7137b69619f0f6b7ecfa85a96530", "sha256_in_prefix": "35f21148c888879c5848b4bb01f0ca26f0ff7137b69619f0f6b7ecfa85a96530", "size_in_bytes": 179}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/qdq_helpers/__pycache__/optimize_qdq_model.cpython-311.pyc", "path_type": "hardlink", "sha256": "75634cebc50d880e8697e1112aae5ffc3c1467b34bdcbaee12da201940e222c9", "sha256_in_prefix": "75634cebc50d880e8697e1112aae5ffc3c1467b34bdcbaee12da201940e222c9", "size_in_bytes": 1788}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/qdq_helpers/optimize_qdq_model.py", "path_type": "hardlink", "sha256": "58bb1989f54d570c245fc5229784de03c99fb2ab69c5432d6f47e544dda08f30", "sha256_in_prefix": "58bb1989f54d570c245fc5229784de03c99fb2ab69c5432d6f47e544dda08f30", "size_in_bytes": 1242}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/reduced_build_config_parser.py", "path_type": "hardlink", "sha256": "7bf7afd69cd4af1958a4c6ea710f632a224850274ea25be2df3c8b9cdf645a58", "sha256_in_prefix": "7bf7afd69cd4af1958a4c6ea710f632a224850274ea25be2df3c8b9cdf645a58", "size_in_bytes": 9958}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/symbolic_shape_infer.py", "path_type": "hardlink", "sha256": "5306c1ba685d285b35acd35e3124c2400bad0b27dd65f312e26bb689dd445f3e", "sha256_in_prefix": "5306c1ba685d285b35acd35e3124c2400bad0b27dd65f312e26bb689dd445f3e", "size_in_bytes": 142549}, {"_path": "lib/python3.11/site-packages/onnxruntime/tools/update_onnx_opset.py", "path_type": "hardlink", "sha256": "9df145f2cae3a53c8ef120a71d932ddf374c581d10e4707506071d6409dced42", "sha256_in_prefix": "9df145f2cae3a53c8ef120a71d932ddf374c581d10e4707506071d6409dced42", "size_in_bytes": 1151}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__init__.py", "path_type": "hardlink", "sha256": "452e41e705a7c05287d0a5d9796c9d863b3d5a5f6c3d1aa053f941d7f75460de", "sha256_in_prefix": "452e41e705a7c05287d0a5d9796c9d863b3d5a5f6c3d1aa053f941d7f75460de", "size_in_bytes": 313}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "4d8bfc21963c72c398367eee838b6e69f6b9cba2cfc7e6ad4e6c1c85b3af34ff", "sha256_in_prefix": "4d8bfc21963c72c398367eee838b6e69f6b9cba2cfc7e6ad4e6c1c85b3af34ff", "size_in_bytes": 384}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/affinity_helper.cpython-311.pyc", "path_type": "hardlink", "sha256": "6847ca3db012888cb5bb001bfecf5e669a9019e353cae5e18ca9fb5b7d6e21d9", "sha256_in_prefix": "6847ca3db012888cb5bb001bfecf5e669a9019e353cae5e18ca9fb5b7d6e21d9", "size_in_bytes": 2342}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/benchmark.cpython-311.pyc", "path_type": "hardlink", "sha256": "d358c8f06a477172b736274c51f8f888e1b6a1cc074e359f59bae78780a72de6", "sha256_in_prefix": "d358c8f06a477172b736274c51f8f888e1b6a1cc074e359f59bae78780a72de6", "size_in_bytes": 32526}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/benchmark_helper.cpython-311.pyc", "path_type": "hardlink", "sha256": "9ce7d4319d47a826d070d05588b8cfd49c35288990dcf6c5191c216f0e051c21", "sha256_in_prefix": "9ce7d4319d47a826d070d05588b8cfd49c35288990dcf6c5191c216f0e051c21", "size_in_bytes": 31826}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/bert_perf_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "57a72981e41f0b50312a7297dc37171a5f908d97298cbd8faf5973c0e3f88265", "sha256_in_prefix": "57a72981e41f0b50312a7297dc37171a5f908d97298cbd8faf5973c0e3f88265", "size_in_bytes": 25560}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/bert_test_data.cpython-311.pyc", "path_type": "hardlink", "sha256": "ee422fa23cc9f1fb1c4c3a578d706d0ba5831c288bf7d6979f668d363f7384f0", "sha256_in_prefix": "ee422fa23cc9f1fb1c4c3a578d706d0ba5831c288bf7d6979f668d363f7384f0", "size_in_bytes": 26371}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/compare_bert_results.cpython-311.pyc", "path_type": "hardlink", "sha256": "064ea8167b5a573aab0fb3361e62c8c0ce6f84025d24be73bc5988af9ac497d7", "sha256_in_prefix": "064ea8167b5a573aab0fb3361e62c8c0ce6f84025d24be73bc5988af9ac497d7", "size_in_bytes": 9488}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/constants.cpython-311.pyc", "path_type": "hardlink", "sha256": "7490b23b46f04a74e9ccd823380cd61ab03a0e915e89b34e28ef3f0aa83313e3", "sha256_in_prefix": "7490b23b46f04a74e9ccd823380cd61ab03a0e915e89b34e28ef3f0aa83313e3", "size_in_bytes": 2085}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/convert_generation.cpython-311.pyc", "path_type": "hardlink", "sha256": "430c2b40947fafad37f515ad5ee7a693f92b3d8c2caa75337dd45d3790b4ec17", "sha256_in_prefix": "430c2b40947fafad37f515ad5ee7a693f92b3d8c2caa75337dd45d3790b4ec17", "size_in_bytes": 142538}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/convert_tf_models_to_pytorch.cpython-311.pyc", "path_type": "hardlink", "sha256": "938ade44a905f12bafbee556d26830289f48f9d86d86eccdb7e421d29173712f", "sha256_in_prefix": "938ade44a905f12bafbee556d26830289f48f9d86d86eccdb7e421d29173712f", "size_in_bytes": 9135}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/convert_to_packing_mode.cpython-311.pyc", "path_type": "hardlink", "sha256": "ea26eeed21b4be2a56901d7e27a5b95767dce65e03f708a3f43440aaf7cb1f4e", "sha256_in_prefix": "ea26eeed21b4be2a56901d7e27a5b95767dce65e03f708a3f43440aaf7cb1f4e", "size_in_bytes": 22603}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/dynamo_onnx_helper.cpython-311.pyc", "path_type": "hardlink", "sha256": "3f9543cb4c28cd2222a9c7e51441a45d72523c190847d9d85319f3ba59b607a6", "sha256_in_prefix": "3f9543cb4c28cd2222a9c7e51441a45d72523c190847d9d85319f3ba59b607a6", "size_in_bytes": 11366}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/float16.cpython-311.pyc", "path_type": "hardlink", "sha256": "fe9cfadabffcb7ff6e32a8c504ded76bf689ab018c86193dc4ed8ef103702cde", "sha256_in_prefix": "fe9cfadabffcb7ff6e32a8c504ded76bf689ab018c86193dc4ed8ef103702cde", "size_in_bytes": 24491}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_attention.cpython-311.pyc", "path_type": "hardlink", "sha256": "3741ff1f6f7ed4fa09125639e00abc56b4452de53d07373d37d4eceeafecfbb1", "sha256_in_prefix": "3741ff1f6f7ed4fa09125639e00abc56b4452de53d07373d37d4eceeafecfbb1", "size_in_bytes": 49991}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_attention_clip.cpython-311.pyc", "path_type": "hardlink", "sha256": "629cabe567fb39362c1fc7d85eb6e23d00e1acd7f6fc0b2eb54a05f0e90dbef7", "sha256_in_prefix": "629cabe567fb39362c1fc7d85eb6e23d00e1acd7f6fc0b2eb54a05f0e90dbef7", "size_in_bytes": 10913}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_attention_sam2.cpython-311.pyc", "path_type": "hardlink", "sha256": "cde45d2e09e16ef33340ac55f2800028f3c22ad5cbd790fc6b88a1b62572523f", "sha256_in_prefix": "cde45d2e09e16ef33340ac55f2800028f3c22ad5cbd790fc6b88a1b62572523f", "size_in_bytes": 20616}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_attention_unet.cpython-311.pyc", "path_type": "hardlink", "sha256": "241f1ce54cd5605202e728d6216be6473bdaf50ab73fa0c11e46aa637a47920b", "sha256_in_prefix": "241f1ce54cd5605202e728d6216be6473bdaf50ab73fa0c11e46aa637a47920b", "size_in_bytes": 44999}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_attention_vae.cpython-311.pyc", "path_type": "hardlink", "sha256": "a87000ff88cdc49430968fa6e8bf5ad73ea98951a1c2acd1e59b90e24e2d55fa", "sha256_in_prefix": "a87000ff88cdc49430968fa6e8bf5ad73ea98951a1c2acd1e59b90e24e2d55fa", "size_in_bytes": 13415}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_bart_attention.cpython-311.pyc", "path_type": "hardlink", "sha256": "ad06633398b74b5645e0cf48e3d6c5e58b25d021fdb14a79d361cdf48f741447", "sha256_in_prefix": "ad06633398b74b5645e0cf48e3d6c5e58b25d021fdb14a79d361cdf48f741447", "size_in_bytes": 22550}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_base.cpython-311.pyc", "path_type": "hardlink", "sha256": "c422f0dd2c7cb08d125017c7af992ee8642f5806e66dd69e3039f1ef63f2bc9e", "sha256_in_prefix": "c422f0dd2c7cb08d125017c7af992ee8642f5806e66dd69e3039f1ef63f2bc9e", "size_in_bytes": 7090}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_bias_add.cpython-311.pyc", "path_type": "hardlink", "sha256": "79e2bc1b61696b0add13d3c82d75e5c8e2bef33c0e658d570c277b18ed4ed606", "sha256_in_prefix": "79e2bc1b61696b0add13d3c82d75e5c8e2bef33c0e658d570c277b18ed4ed606", "size_in_bytes": 2932}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_biasgelu.cpython-311.pyc", "path_type": "hardlink", "sha256": "587b4bc84f0b60443fa6cf1b685d538b0f1285422fe42447c6117a499c5e8945", "sha256_in_prefix": "587b4bc84f0b60443fa6cf1b685d538b0f1285422fe42447c6117a499c5e8945", "size_in_bytes": 3224}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_biassplitgelu.cpython-311.pyc", "path_type": "hardlink", "sha256": "48fd00b422e3c3868559914931dabeb0a3d3c85fd82d0d6a0a9b83e7493971c6", "sha256_in_prefix": "48fd00b422e3c3868559914931dabeb0a3d3c85fd82d0d6a0a9b83e7493971c6", "size_in_bytes": 5055}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_conformer_attention.cpython-311.pyc", "path_type": "hardlink", "sha256": "ec28685b84726c083d0cb557390c8362c2850fdc6f198e9a37b182edfae5736e", "sha256_in_prefix": "ec28685b84726c083d0cb557390c8362c2850fdc6f198e9a37b182edfae5736e", "size_in_bytes": 8038}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_constant_fold.cpython-311.pyc", "path_type": "hardlink", "sha256": "372df4f469feb76c45fd1e6703a01b45ddf799d6dfbd4ee4ff2c4e6be97473e8", "sha256_in_prefix": "372df4f469feb76c45fd1e6703a01b45ddf799d6dfbd4ee4ff2c4e6be97473e8", "size_in_bytes": 7253}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_embedlayer.cpython-311.pyc", "path_type": "hardlink", "sha256": "f130100a1845fa2becb873726b69a71051bf652c3316d49490fccf9eb13a6e32", "sha256_in_prefix": "f130100a1845fa2becb873726b69a71051bf652c3316d49490fccf9eb13a6e32", "size_in_bytes": 34350}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_fastgelu.cpython-311.pyc", "path_type": "hardlink", "sha256": "04e03b91c850cd7e56bb0f2f2fb06737761327f9c063e6494323190bb4fe2caf", "sha256_in_prefix": "04e03b91c850cd7e56bb0f2f2fb06737761327f9c063e6494323190bb4fe2caf", "size_in_bytes": 17957}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_gelu.cpython-311.pyc", "path_type": "hardlink", "sha256": "b005edb4a7a902bac05cb5aba681d1aedbedae2fec243dba74b9f7875ab5216f", "sha256_in_prefix": "b005edb4a7a902bac05cb5aba681d1aedbedae2fec243dba74b9f7875ab5216f", "size_in_bytes": 11814}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_gelu_approximation.cpython-311.pyc", "path_type": "hardlink", "sha256": "5ff444aba8bdbcb31f01ece2971db7b38ca435c4e089ebdf0a81483d8a6efeae", "sha256_in_prefix": "5ff444aba8bdbcb31f01ece2971db7b38ca435c4e089ebdf0a81483d8a6efeae", "size_in_bytes": 1898}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_gemmfastgelu.cpython-311.pyc", "path_type": "hardlink", "sha256": "d74f52d27778c26b0e4b94744bf0850adff05a4a874884ae97a323dc318092b7", "sha256_in_prefix": "d74f52d27778c26b0e4b94744bf0850adff05a4a874884ae97a323dc318092b7", "size_in_bytes": 5413}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_gpt_attention.cpython-311.pyc", "path_type": "hardlink", "sha256": "4eef1cd188960a281f91aff03cb6d745497f4fd997991f922aa7e5b65f3754e2", "sha256_in_prefix": "4eef1cd188960a281f91aff03cb6d745497f4fd997991f922aa7e5b65f3754e2", "size_in_bytes": 19923}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_gpt_attention_megatron.cpython-311.pyc", "path_type": "hardlink", "sha256": "d5ec6fa88185b2403542a87f9f1736a6db47e0eee3b2417e4d09c66b966229ac", "sha256_in_prefix": "d5ec6fa88185b2403542a87f9f1736a6db47e0eee3b2417e4d09c66b966229ac", "size_in_bytes": 14624}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_gpt_attention_no_past.cpython-311.pyc", "path_type": "hardlink", "sha256": "fc9202d1a17db30cb67ced4980496dd7f011a66a33d841a13e0938f57059f866", "sha256_in_prefix": "fc9202d1a17db30cb67ced4980496dd7f011a66a33d841a13e0938f57059f866", "size_in_bytes": 9111}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_group_norm.cpython-311.pyc", "path_type": "hardlink", "sha256": "3b18343e1b5a7f07af1ea01eb0a4fb8927cb2fc30fa5685e105d6411c05b7968", "sha256_in_prefix": "3b18343e1b5a7f07af1ea01eb0a4fb8927cb2fc30fa5685e105d6411c05b7968", "size_in_bytes": 8694}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_layernorm.cpython-311.pyc", "path_type": "hardlink", "sha256": "f7304f0cf370fa2e1bfd7ff560c3a70681d16af0c4d0f4e0a3571a7509a68fd4", "sha256_in_prefix": "f7304f0cf370fa2e1bfd7ff560c3a70681d16af0c4d0f4e0a3571a7509a68fd4", "size_in_bytes": 22457}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_mha_mmdit.cpython-311.pyc", "path_type": "hardlink", "sha256": "8ebf7d1689ad69906acbddc5bc84cce944c2c827365ab936b5a15828ae8f7248", "sha256_in_prefix": "8ebf7d1689ad69906acbddc5bc84cce944c2c827365ab936b5a15828ae8f7248", "size_in_bytes": 26195}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_nhwc_conv.cpython-311.pyc", "path_type": "hardlink", "sha256": "106302fa64a840c295a54701f251b57b492fb25d29add20ba3a9360c6b789465", "sha256_in_prefix": "106302fa64a840c295a54701f251b57b492fb25d29add20ba3a9360c6b789465", "size_in_bytes": 5045}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_options.cpython-311.pyc", "path_type": "hardlink", "sha256": "384e764b9aee455218532ec97a52cf203e89a7f7d82fa0a61904de5e295a1b40", "sha256_in_prefix": "384e764b9aee455218532ec97a52cf203e89a7f7d82fa0a61904de5e295a1b40", "size_in_bytes": 12224}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_qordered_attention.cpython-311.pyc", "path_type": "hardlink", "sha256": "1fc098c2de642ba7a9f8ba73fc881866851cf8ba067b388abca9a6e68209be70", "sha256_in_prefix": "1fc098c2de642ba7a9f8ba73fc881866851cf8ba067b388abca9a6e68209be70", "size_in_bytes": 16353}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_qordered_gelu.cpython-311.pyc", "path_type": "hardlink", "sha256": "67690545c9690b1471f69b91090e2b7b1e5debff21c7811259b53512caeaae84", "sha256_in_prefix": "67690545c9690b1471f69b91090e2b7b1e5debff21c7811259b53512caeaae84", "size_in_bytes": 4583}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_qordered_layernorm.cpython-311.pyc", "path_type": "hardlink", "sha256": "eedbb4f0f853059f357004334119f61859e9d033bc471ad54307160e9b5b6c5f", "sha256_in_prefix": "eedbb4f0f853059f357004334119f61859e9d033bc471ad54307160e9b5b6c5f", "size_in_bytes": 5038}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_qordered_matmul.cpython-311.pyc", "path_type": "hardlink", "sha256": "70124c95b21466b50bc1fa015062bb3b8123d47e50536c52e4bc442086a84a94", "sha256_in_prefix": "70124c95b21466b50bc1fa015062bb3b8123d47e50536c52e4bc442086a84a94", "size_in_bytes": 7315}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_quickgelu.cpython-311.pyc", "path_type": "hardlink", "sha256": "a1c8c3d84ef574ae2945abafd6a5afe59935c6b927c0e3b14183d989a8b85ca1", "sha256_in_prefix": "a1c8c3d84ef574ae2945abafd6a5afe59935c6b927c0e3b14183d989a8b85ca1", "size_in_bytes": 3608}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_reshape.cpython-311.pyc", "path_type": "hardlink", "sha256": "ed57a313a0e2df4d37d1a3dc1f0b44caf2a505071ef7ae78dc9bfd8a8c8ed533", "sha256_in_prefix": "ed57a313a0e2df4d37d1a3dc1f0b44caf2a505071ef7ae78dc9bfd8a8c8ed533", "size_in_bytes": 6992}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_rotary_attention.cpython-311.pyc", "path_type": "hardlink", "sha256": "5a13c6472e32ed410a675d8e00dcf68f18369b1eb68c4d32f5853533ed34bfe3", "sha256_in_prefix": "5a13c6472e32ed410a675d8e00dcf68f18369b1eb68c4d32f5853533ed34bfe3", "size_in_bytes": 54687}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_shape.cpython-311.pyc", "path_type": "hardlink", "sha256": "089a7663827c9151470b4f08f7fce806ea6c525323a9e5df64930b3490a18e70", "sha256_in_prefix": "089a7663827c9151470b4f08f7fce806ea6c525323a9e5df64930b3490a18e70", "size_in_bytes": 4838}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_simplified_layernorm.cpython-311.pyc", "path_type": "hardlink", "sha256": "e86a7ce2978640721085b50225dfdcd1b9e999f2a17c7001b9b7896c3cb880ab", "sha256_in_prefix": "e86a7ce2978640721085b50225dfdcd1b9e999f2a17c7001b9b7896c3cb880ab", "size_in_bytes": 6263}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_skip_group_norm.cpython-311.pyc", "path_type": "hardlink", "sha256": "4dd39b6e13ae9cfdf48e82254f1f1af881d01c32421baa4575638b2976b4e2bc", "sha256_in_prefix": "4dd39b6e13ae9cfdf48e82254f1f1af881d01c32421baa4575638b2976b4e2bc", "size_in_bytes": 10691}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_skiplayernorm.cpython-311.pyc", "path_type": "hardlink", "sha256": "cad3efea37da3b0f876c7f1fa9694b1fb294cda982686cac50c269823ea494cb", "sha256_in_prefix": "cad3efea37da3b0f876c7f1fa9694b1fb294cda982686cac50c269823ea494cb", "size_in_bytes": 9913}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_transpose.cpython-311.pyc", "path_type": "hardlink", "sha256": "a7eb4678263aec01e31991598f9475d3a0880f6ea07d7b9abf54f7505cc6e1aa", "sha256_in_prefix": "a7eb4678263aec01e31991598f9475d3a0880f6ea07d7b9abf54f7505cc6e1aa", "size_in_bytes": 9175}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/fusion_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "54f0a97e6db652953ab419dee3a89d5922871d611aeba50b8d7ba612e9733f34", "sha256_in_prefix": "54f0a97e6db652953ab419dee3a89d5922871d611aeba50b8d7ba612e9733f34", "size_in_bytes": 17140}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/huggingface_models.cpython-311.pyc", "path_type": "hardlink", "sha256": "2834ab794234187de572efe6a7a80422a1dc157c504ceb733cc55a43099cb9ad", "sha256_in_prefix": "2834ab794234187de572efe6a7a80422a1dc157c504ceb733cc55a43099cb9ad", "size_in_bytes": 2919}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/import_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "dddbf0af44d90c2409100988e4fbab97c02f46da8366337d7d5bcf24ca1eb1d9", "sha256_in_prefix": "dddbf0af44d90c2409100988e4fbab97c02f46da8366337d7d5bcf24ca1eb1d9", "size_in_bytes": 933}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/io_binding_helper.cpython-311.pyc", "path_type": "hardlink", "sha256": "857b353c9c1848d3844956bc40ec914326f1b23fc4d42cd815ce10c7ddf6a47e", "sha256_in_prefix": "857b353c9c1848d3844956bc40ec914326f1b23fc4d42cd815ce10c7ddf6a47e", "size_in_bytes": 22822}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/large_model_exporter.cpython-311.pyc", "path_type": "hardlink", "sha256": "53786bd3bfec79caaf1f55b2f437d70506bde3858bc716ca1d0077f5afafddaf", "sha256_in_prefix": "53786bd3bfec79caaf1f55b2f437d70506bde3858bc716ca1d0077f5afafddaf", "size_in_bytes": 22833}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/machine_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "0bd20ee1acafe21e975ae54662da3153084738c7873ee140921b3236e46710ac", "sha256_in_prefix": "0bd20ee1acafe21e975ae54662da3153084738c7873ee140921b3236e46710ac", "size_in_bytes": 11135}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/metrics.cpython-311.pyc", "path_type": "hardlink", "sha256": "1adcd4d7f01b61fd4fc4645fd6c3bffe6306b2f3a1357f8ddc79d8280b53b89b", "sha256_in_prefix": "1adcd4d7f01b61fd4fc4645fd6c3bffe6306b2f3a1357f8ddc79d8280b53b89b", "size_in_bytes": 9884}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_exporter.cpython-311.pyc", "path_type": "hardlink", "sha256": "d2e61e09f5e3e0174497c7e8093aa0e654278bee92ae2c54a3287c8f57bd8c34", "sha256_in_prefix": "d2e61e09f5e3e0174497c7e8093aa0e654278bee92ae2c54a3287c8f57bd8c34", "size_in_bytes": 27080}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model.cpython-311.pyc", "path_type": "hardlink", "sha256": "2845ea854d652655ba2bcd6b9adf738de56d5b4befa9b50514430a4c76610e0b", "sha256_in_prefix": "2845ea854d652655ba2bcd6b9adf738de56d5b4befa9b50514430a4c76610e0b", "size_in_bytes": 81669}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_bart.cpython-311.pyc", "path_type": "hardlink", "sha256": "ff3668b4d6e8d3b6437e0ac10876e55ebd4ca5fabff92344d1a0f836b448ca8a", "sha256_in_prefix": "ff3668b4d6e8d3b6437e0ac10876e55ebd4ca5fabff92344d1a0f836b448ca8a", "size_in_bytes": 7434}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_bert.cpython-311.pyc", "path_type": "hardlink", "sha256": "ca9e9e8981303ef41d28957fb030c25003694470d6fcab3292bb8a1b166c1241", "sha256_in_prefix": "ca9e9e8981303ef41d28957fb030c25003694470d6fcab3292bb8a1b166c1241", "size_in_bytes": 25389}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_bert_keras.cpython-311.pyc", "path_type": "hardlink", "sha256": "64373a473997d4aa15de177490370714445a87fc2033d89f66d4b404a1325674", "sha256_in_prefix": "64373a473997d4aa15de177490370714445a87fc2033d89f66d4b404a1325674", "size_in_bytes": 20266}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_bert_tf.cpython-311.pyc", "path_type": "hardlink", "sha256": "a855855caa944d61b1a2f4ca92637350d00ec5ccb8a3b39522d015061e794512", "sha256_in_prefix": "a855855caa944d61b1a2f4ca92637350d00ec5ccb8a3b39522d015061e794512", "size_in_bytes": 25722}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_clip.cpython-311.pyc", "path_type": "hardlink", "sha256": "20775336d861261400a33d484b00d28316ee71a1a153605e2220ad970e89956b", "sha256_in_prefix": "20775336d861261400a33d484b00d28316ee71a1a153605e2220ad970e89956b", "size_in_bytes": 2235}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_conformer.cpython-311.pyc", "path_type": "hardlink", "sha256": "7e5db9cbc9563650c8c77a5a8e4e477cc4074e9c652222620fedad9965791913", "sha256_in_prefix": "7e5db9cbc9563650c8c77a5a8e4e477cc4074e9c652222620fedad9965791913", "size_in_bytes": 2447}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_gpt2.cpython-311.pyc", "path_type": "hardlink", "sha256": "14be766977c859045d9cddc925ddebb4a06ad1dd54503545a8585476d51b899d", "sha256_in_prefix": "14be766977c859045d9cddc925ddebb4a06ad1dd54503545a8585476d51b899d", "size_in_bytes": 4925}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_mmdit.cpython-311.pyc", "path_type": "hardlink", "sha256": "d75ae6156423dc6150745b1657c9c7cce45299585280b3b00ff62f5a3fc6bfa6", "sha256_in_prefix": "d75ae6156423dc6150745b1657c9c7cce45299585280b3b00ff62f5a3fc6bfa6", "size_in_bytes": 6323}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_phi.cpython-311.pyc", "path_type": "hardlink", "sha256": "45d9d06e6854a7c6f5b56fcee5198b22628039a56d132800eb09afdb6e340f0c", "sha256_in_prefix": "45d9d06e6854a7c6f5b56fcee5198b22628039a56d132800eb09afdb6e340f0c", "size_in_bytes": 47124}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_sam2.cpython-311.pyc", "path_type": "hardlink", "sha256": "c5aa60aea713c7429360dab06db2e65731b6c272741ca74a1f0ae56e95d02fa0", "sha256_in_prefix": "c5aa60aea713c7429360dab06db2e65731b6c272741ca74a1f0ae56e95d02fa0", "size_in_bytes": 7349}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_t5.cpython-311.pyc", "path_type": "hardlink", "sha256": "738637fec6a8cdbfc353c94c021a9554745555dfccbf5869fc5d43b439a213aa", "sha256_in_prefix": "738637fec6a8cdbfc353c94c021a9554745555dfccbf5869fc5d43b439a213aa", "size_in_bytes": 33416}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_tnlr.cpython-311.pyc", "path_type": "hardlink", "sha256": "38ed8b3b0a7e4786d6899fb7491de33760b9a9446fa9e8ce6fdd4abee93931e9", "sha256_in_prefix": "38ed8b3b0a7e4786d6899fb7491de33760b9a9446fa9e8ce6fdd4abee93931e9", "size_in_bytes": 9269}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_unet.cpython-311.pyc", "path_type": "hardlink", "sha256": "c2a0d0251660ee1f6d0e2e205cd9d99303020d109c8aaf9c5ea809792a83e6e4", "sha256_in_prefix": "c2a0d0251660ee1f6d0e2e205cd9d99303020d109c8aaf9c5ea809792a83e6e4", "size_in_bytes": 12954}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_model_vae.cpython-311.pyc", "path_type": "hardlink", "sha256": "ce5e033d6f3881569f04b3f4298682812ab4035ce6edc43c579a69119ed27f81", "sha256_in_prefix": "ce5e033d6f3881569f04b3f4298682812ab4035ce6edc43c579a69119ed27f81", "size_in_bytes": 2417}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/onnx_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "512676cbcffba0e11160d9a2b70f5c4b254d5d9019ca7838030c365eaa29ce1b", "sha256_in_prefix": "512676cbcffba0e11160d9a2b70f5c4b254d5d9019ca7838030c365eaa29ce1b", "size_in_bytes": 2669}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/optimizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "be1055cceed9b938c9ca8e34400b2bcc897069de2bb544418aa7cd055bb29224", "sha256_in_prefix": "be1055cceed9b938c9ca8e34400b2bcc897069de2bb544418aa7cd055bb29224", "size_in_bytes": 25705}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/past_helper.cpython-311.pyc", "path_type": "hardlink", "sha256": "0655e4b37faa6e08abf68b0bdba7b6f125f04b8f2c59097bb55d064c755a47d9", "sha256_in_prefix": "0655e4b37faa6e08abf68b0bdba7b6f125f04b8f2c59097bb55d064c755a47d9", "size_in_bytes": 8836}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/profile_result_processor.cpython-311.pyc", "path_type": "hardlink", "sha256": "75a1c2069a66724ee11036e3e81d9cd39a96c46c6a2d68e2257273ce9372f9cf", "sha256_in_prefix": "75a1c2069a66724ee11036e3e81d9cd39a96c46c6a2d68e2257273ce9372f9cf", "size_in_bytes": 14963}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/profiler.cpython-311.pyc", "path_type": "hardlink", "sha256": "2da48c33f62ebff65cdfaf0951429023c6386c43b4afec4978cb1db2e2426f5f", "sha256_in_prefix": "2da48c33f62ebff65cdfaf0951429023c6386c43b4afec4978cb1db2e2426f5f", "size_in_bytes": 15172}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/quantize_helper.cpython-311.pyc", "path_type": "hardlink", "sha256": "b243d2fff0012104007eac1ce99e6ff343f9dc2ae43a0599d4194be3e9e09474", "sha256_in_prefix": "b243d2fff0012104007eac1ce99e6ff343f9dc2ae43a0599d4194be3e9e09474", "size_in_bytes": 4635}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/shape_infer_helper.cpython-311.pyc", "path_type": "hardlink", "sha256": "115dfdf42455581919dfe360a5b4484e299b1b672f2c4cec6e7b5ebf6d3b49e9", "sha256_in_prefix": "115dfdf42455581919dfe360a5b4484e299b1b672f2c4cec6e7b5ebf6d3b49e9", "size_in_bytes": 6001}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/shape_optimizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "b56642ac10f17d6e83fbdd7ec057f113b9c9842ade89e419c257d17035430174", "sha256_in_prefix": "b56642ac10f17d6e83fbdd7ec057f113b9c9842ade89e419c257d17035430174", "size_in_bytes": 21384}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/__pycache__/torch_onnx_export_helper.cpython-311.pyc", "path_type": "hardlink", "sha256": "8d4e18b4e9dfabe1fc2385da53c2f7d8707222800c13d19a9c90b45363a73f07", "sha256_in_prefix": "8d4e18b4e9dfabe1fc2385da53c2f7d8707222800c13d19a9c90b45363a73f07", "size_in_bytes": 1720}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/affinity_helper.py", "path_type": "hardlink", "sha256": "374411da3974cfabf800cd797322a7cd67fb4fc4b94f7641c1e390e56135971f", "sha256_in_prefix": "374411da3974cfabf800cd797322a7cd67fb4fc4b94f7641c1e390e56135971f", "size_in_bytes": 1402}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/benchmark.py", "path_type": "hardlink", "sha256": "faace39abbe2b8a15dac6fb99542abd4380d5eacce6f77ec5798beca6c5fab06", "sha256_in_prefix": "faace39abbe2b8a15dac6fb99542abd4380d5eacce6f77ec5798beca6c5fab06", "size_in_bytes": 32737}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/benchmark_helper.py", "path_type": "hardlink", "sha256": "642bd98bcb981a9be8bfac2d1171a41f5871ea7c9afe538067a85c3ef9a47e5e", "sha256_in_prefix": "642bd98bcb981a9be8bfac2d1171a41f5871ea7c9afe538067a85c3ef9a47e5e", "size_in_bytes": 22494}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/bert_perf_test.py", "path_type": "hardlink", "sha256": "0f2b156b6941cf7b649f52193626743f6771d386eb21a764bc079332cfdf3b2b", "sha256_in_prefix": "0f2b156b6941cf7b649f52193626743f6771d386eb21a764bc079332cfdf3b2b", "size_in_bytes": 20321}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/bert_test_data.py", "path_type": "hardlink", "sha256": "7b704e5f3eb10cc3866c747b43a627496ebb50c5ab528467bc1b084c0d067334", "sha256_in_prefix": "7b704e5f3eb10cc3866c747b43a627496ebb50c5ab528467bc1b084c0d067334", "size_in_bytes": 22788}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/compare_bert_results.py", "path_type": "hardlink", "sha256": "ea5e3da8842966fc9144b57229c495f757ac52536fbca707c187f7092eea5bcc", "sha256_in_prefix": "ea5e3da8842966fc9144b57229c495f757ac52536fbca707c187f7092eea5bcc", "size_in_bytes": 8175}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/constants.py", "path_type": "hardlink", "sha256": "d0c8f1e1e103f063a38c6fe9e40f2176e34824288fa9c5aa70958a9d932d5b09", "sha256_in_prefix": "d0c8f1e1e103f063a38c6fe9e40f2176e34824288fa9c5aa70958a9d932d5b09", "size_in_bytes": 1080}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/convert_generation.py", "path_type": "hardlink", "sha256": "d7c63159504b8f82cbb22fa6453d9e014c9a0d57e26250f6b36a9679cc7ba99d", "sha256_in_prefix": "d7c63159504b8f82cbb22fa6453d9e014c9a0d57e26250f6b36a9679cc7ba99d", "size_in_bytes": 138982}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/convert_tf_models_to_pytorch.py", "path_type": "hardlink", "sha256": "b5847fc2624005183d0501bd6a3de83b78f40014aee328b8fd2836cf1e95785e", "sha256_in_prefix": "b5847fc2624005183d0501bd6a3de83b78f40014aee328b8fd2836cf1e95785e", "size_in_bytes": 6500}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/convert_to_packing_mode.py", "path_type": "hardlink", "sha256": "c226bdcf0ebf8ddc8c50b66834bf51b4483a2dcedff0ac8b78fa495db778964c", "sha256_in_prefix": "c226bdcf0ebf8ddc8c50b66834bf51b4483a2dcedff0ac8b78fa495db778964c", "size_in_bytes": 16406}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/dynamo_onnx_helper.py", "path_type": "hardlink", "sha256": "37de528f7bff37ec1c74c048d1da6540c09e815d16fb8cfa4a2fd6f1e450ae2e", "sha256_in_prefix": "37de528f7bff37ec1c74c048d1da6540c09e815d16fb8cfa4a2fd6f1e450ae2e", "size_in_bytes": 7580}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/float16.py", "path_type": "hardlink", "sha256": "9dfd1f1ee6c2e2a7ab35e18b617b9396762b52c797e6c0b1f0beff137dbbf7c4", "sha256_in_prefix": "9dfd1f1ee6c2e2a7ab35e18b617b9396762b52c797e6c0b1f0beff137dbbf7c4", "size_in_bytes": 24186}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_attention.py", "path_type": "hardlink", "sha256": "ebfe29684144c7758e0db3141d65d6933292901fbde0ccfd6fd1f1abbec750cb", "sha256_in_prefix": "ebfe29684144c7758e0db3141d65d6933292901fbde0ccfd6fd1f1abbec750cb", "size_in_bytes": 50204}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_attention_clip.py", "path_type": "hardlink", "sha256": "227a5d57e9d2137d6224b7f982917d62630fb1bd4cd46cf38a8adc151e29fc5d", "sha256_in_prefix": "227a5d57e9d2137d6224b7f982917d62630fb1bd4cd46cf38a8adc151e29fc5d", "size_in_bytes": 13430}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_attention_sam2.py", "path_type": "hardlink", "sha256": "24d7a5ea2b7cd969687cbf1691abaca3ab76ac181f894161a6640c3b2678d89e", "sha256_in_prefix": "24d7a5ea2b7cd969687cbf1691abaca3ab76ac181f894161a6640c3b2678d89e", "size_in_bytes": 20774}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_attention_unet.py", "path_type": "hardlink", "sha256": "51cf78870a57685cbda57d38593332cdb65c49859420d82abbc2c66d745b80ea", "sha256_in_prefix": "51cf78870a57685cbda57d38593332cdb65c49859420d82abbc2c66d745b80ea", "size_in_bytes": 55648}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_attention_vae.py", "path_type": "hardlink", "sha256": "de0941ff5f94e96ebb8c7518ccbc916753b83070f82c5810a3c2a7bbe6bf9e28", "sha256_in_prefix": "de0941ff5f94e96ebb8c7518ccbc916753b83070f82c5810a3c2a7bbe6bf9e28", "size_in_bytes": 12079}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_bart_attention.py", "path_type": "hardlink", "sha256": "6a2c9530955238d457be94d71fc0cc0e52ffb284eb10747627cc9dee101c0337", "sha256_in_prefix": "6a2c9530955238d457be94d71fc0cc0e52ffb284eb10747627cc9dee101c0337", "size_in_bytes": 29346}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_base.py", "path_type": "hardlink", "sha256": "15b838aed7b2c3f20f0686f91a0657636b0fc35b3dd83d104b939f4ab7ecd014", "sha256_in_prefix": "15b838aed7b2c3f20f0686f91a0657636b0fc35b3dd83d104b939f4ab7ecd014", "size_in_bytes": 5837}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_bias_add.py", "path_type": "hardlink", "sha256": "dc49a0450f61b0cc738c2dfc70247c61455fc55241ec7304198e142459f8ebce", "sha256_in_prefix": "dc49a0450f61b0cc738c2dfc70247c61455fc55241ec7304198e142459f8ebce", "size_in_bytes": 1984}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_biasgelu.py", "path_type": "hardlink", "sha256": "f99fd3b1014efbd46c58b7b31977b85366898ee5f88558081b03847256e5240a", "sha256_in_prefix": "f99fd3b1014efbd46c58b7b31977b85366898ee5f88558081b03847256e5240a", "size_in_bytes": 2234}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_biassplitgelu.py", "path_type": "hardlink", "sha256": "d856e1ef7d4f333d7a02080855746307fa881726cabe2ac9dac3cea9eae0cd53", "sha256_in_prefix": "d856e1ef7d4f333d7a02080855746307fa881726cabe2ac9dac3cea9eae0cd53", "size_in_bytes": 4381}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_conformer_attention.py", "path_type": "hardlink", "sha256": "a563cf542c8e91e453bd57cdc38db5415afbb084fd9753ca543b3448fb075626", "sha256_in_prefix": "a563cf542c8e91e453bd57cdc38db5415afbb084fd9753ca543b3448fb075626", "size_in_bytes": 8202}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_constant_fold.py", "path_type": "hardlink", "sha256": "dcd3d8804c240745947f21566e01d76d602782d90de721801d879ba433ac0a7c", "sha256_in_prefix": "dcd3d8804c240745947f21566e01d76d602782d90de721801d879ba433ac0a7c", "size_in_bytes": 5870}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_embedlayer.py", "path_type": "hardlink", "sha256": "42d1da6196aa2cca6336e5d49b696afc2e731d9feb009ac10d395b8065452a05", "sha256_in_prefix": "42d1da6196aa2cca6336e5d49b696afc2e731d9feb009ac10d395b8065452a05", "size_in_bytes": 35864}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_fastgelu.py", "path_type": "hardlink", "sha256": "66c84c9135e487e8caf568ca948d598ed552690ca0399b0a7c534acc80ca4618", "sha256_in_prefix": "66c84c9135e487e8caf568ca948d598ed552690ca0399b0a7c534acc80ca4618", "size_in_bytes": 17689}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_gelu.py", "path_type": "hardlink", "sha256": "80a2988228e71698c5b28cc2b07f33e8ccfdc021a4c37a99a69eb95e8af12232", "sha256_in_prefix": "80a2988228e71698c5b28cc2b07f33e8ccfdc021a4c37a99a69eb95e8af12232", "size_in_bytes": 10193}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_gelu_approximation.py", "path_type": "hardlink", "sha256": "8b6c465bef7397a5dbbabaaad05f0323cec0caab209e18cb7855beac188df72b", "sha256_in_prefix": "8b6c465bef7397a5dbbabaaad05f0323cec0caab209e18cb7855beac188df72b", "size_in_bytes": 1004}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_gemmfastgelu.py", "path_type": "hardlink", "sha256": "47d580db9e1cb06c0ed242e2fc89899a1232fb12a4341a9f0db47a34bf07092f", "sha256_in_prefix": "47d580db9e1cb06c0ed242e2fc89899a1232fb12a4341a9f0db47a34bf07092f", "size_in_bytes": 4087}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_gpt_attention.py", "path_type": "hardlink", "sha256": "9a4f942888e8bb3aed90c97703e2dd2ee2dceb094eab101d6c4da0462618c293", "sha256_in_prefix": "9a4f942888e8bb3aed90c97703e2dd2ee2dceb094eab101d6c4da0462618c293", "size_in_bytes": 21962}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_gpt_attention_megatron.py", "path_type": "hardlink", "sha256": "1db0e80d75d886acb8b0415426f69fe4bcb953abbaba90a0f09500d5defc0901", "sha256_in_prefix": "1db0e80d75d886acb8b0415426f69fe4bcb953abbaba90a0f09500d5defc0901", "size_in_bytes": 13284}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_gpt_attention_no_past.py", "path_type": "hardlink", "sha256": "0e4f25e9a4fbae5e2c83fe7cc8c3beb8536a39d0a703a6c195af1df2de2d48a7", "sha256_in_prefix": "0e4f25e9a4fbae5e2c83fe7cc8c3beb8536a39d0a703a6c195af1df2de2d48a7", "size_in_bytes": 10534}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_group_norm.py", "path_type": "hardlink", "sha256": "c84deab405cfc829f34a6c4f50c75844a414b2d8f5f5bc242318ba8773e942a7", "sha256_in_prefix": "c84deab405cfc829f34a6c4f50c75844a414b2d8f5f5bc242318ba8773e942a7", "size_in_bytes": 7465}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_layernorm.py", "path_type": "hardlink", "sha256": "7737ed0ba90bea4c5f772a229dccbf55a05e786cc1d5a0d6ce2c624ac8ead7be", "sha256_in_prefix": "7737ed0ba90bea4c5f772a229dccbf55a05e786cc1d5a0d6ce2c624ac8ead7be", "size_in_bytes": 20356}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_mha_mmdit.py", "path_type": "hardlink", "sha256": "e039b687f3e89de7fe319f72dea3b7082f95e2df488b29aacc8342ca22d559e7", "sha256_in_prefix": "e039b687f3e89de7fe319f72dea3b7082f95e2df488b29aacc8342ca22d559e7", "size_in_bytes": 25149}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_nhwc_conv.py", "path_type": "hardlink", "sha256": "1b5556f90910d8c499502b083c0b6591452fc47ced9e1d4f1dada076b9aa3f6f", "sha256_in_prefix": "1b5556f90910d8c499502b083c0b6591452fc47ced9e1d4f1dada076b9aa3f6f", "size_in_bytes": 3849}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_options.py", "path_type": "hardlink", "sha256": "b3cde1f0a913b7c578fe5010b7ce58a48967c78e3eea045cb95b746fcbee368e", "sha256_in_prefix": "b3cde1f0a913b7c578fe5010b7ce58a48967c78e3eea045cb95b746fcbee368e", "size_in_bytes": 12364}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_qordered_attention.py", "path_type": "hardlink", "sha256": "11441d152d57a4f4fbe6a9946e28ce473641bb64ebf2c90d901c093a4532c795", "sha256_in_prefix": "11441d152d57a4f4fbe6a9946e28ce473641bb64ebf2c90d901c093a4532c795", "size_in_bytes": 16717}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_qordered_gelu.py", "path_type": "hardlink", "sha256": "a41b85369be7ca68d26b08943cd4d0806c5cd630469ab7f11c00bf77e6b84374", "sha256_in_prefix": "a41b85369be7ca68d26b08943cd4d0806c5cd630469ab7f11c00bf77e6b84374", "size_in_bytes": 4292}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_qordered_layernorm.py", "path_type": "hardlink", "sha256": "467d5123c319e18e5c6ad7d5cb98b53aa8a80b128f9b3665e8356e3c98d23cce", "sha256_in_prefix": "467d5123c319e18e5c6ad7d5cb98b53aa8a80b128f9b3665e8356e3c98d23cce", "size_in_bytes": 4810}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_qordered_matmul.py", "path_type": "hardlink", "sha256": "7d97f166b6a65e186beb3c14a3582991b787fd947cde41cc81b59833549471fc", "sha256_in_prefix": "7d97f166b6a65e186beb3c14a3582991b787fd947cde41cc81b59833549471fc", "size_in_bytes": 8325}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_quickgelu.py", "path_type": "hardlink", "sha256": "202faeae88d9167d8b5ede588f3d3e887d22ec1064b68cdbb95e3da66b976828", "sha256_in_prefix": "202faeae88d9167d8b5ede588f3d3e887d22ec1064b68cdbb95e3da66b976828", "size_in_bytes": 2795}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_reshape.py", "path_type": "hardlink", "sha256": "26bc95ae7564eb6dbe31693c11834b973cfc0230354821d6fd397d4ab7270e68", "sha256_in_prefix": "26bc95ae7564eb6dbe31693c11834b973cfc0230354821d6fd397d4ab7270e68", "size_in_bytes": 6230}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_rotary_attention.py", "path_type": "hardlink", "sha256": "0752cc5c74d5110bf6d9d8e69b3eac17eae1a7b2a0507cf452323e88ceebe042", "sha256_in_prefix": "0752cc5c74d5110bf6d9d8e69b3eac17eae1a7b2a0507cf452323e88ceebe042", "size_in_bytes": 66631}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_shape.py", "path_type": "hardlink", "sha256": "b816bf775b55763e5493abafa2b51b37133a5ea6e2f0fc635fdae7794a011092", "sha256_in_prefix": "b816bf775b55763e5493abafa2b51b37133a5ea6e2f0fc635fdae7794a011092", "size_in_bytes": 3654}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_simplified_layernorm.py", "path_type": "hardlink", "sha256": "b3219d7e57b783b724fdb10a84b12850ccd45d7b84a6ed3e940db3495fbd461d", "sha256_in_prefix": "b3219d7e57b783b724fdb10a84b12850ccd45d7b84a6ed3e940db3495fbd461d", "size_in_bytes": 7666}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_skip_group_norm.py", "path_type": "hardlink", "sha256": "223d7991f65beb7da8095dca9b7c436502dffcd915e9790bef1310b4b767107e", "sha256_in_prefix": "223d7991f65beb7da8095dca9b7c436502dffcd915e9790bef1310b4b767107e", "size_in_bytes": 10601}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_skiplayernorm.py", "path_type": "hardlink", "sha256": "181bac5a12722c760e3271cc76501b840691032d9c15da0a2b214f8933d64368", "sha256_in_prefix": "181bac5a12722c760e3271cc76501b840691032d9c15da0a2b214f8933d64368", "size_in_bytes": 8959}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_transpose.py", "path_type": "hardlink", "sha256": "a143990f0a381085d2d07e7723a06f7940cf9fa4f1a32ffdc13b3e8d609428bb", "sha256_in_prefix": "a143990f0a381085d2d07e7723a06f7940cf9fa4f1a32ffdc13b3e8d609428bb", "size_in_bytes": 6837}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/fusion_utils.py", "path_type": "hardlink", "sha256": "7f863c8209c3636bacda505d4345b878cd139303c7d51d2c644c4792143d29c2", "sha256_in_prefix": "7f863c8209c3636bacda505d4345b878cd139303c7d51d2c644c4792143d29c2", "size_in_bytes": 12860}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/huggingface_models.py", "path_type": "hardlink", "sha256": "7f156fea1f63aeae00029521e23df1efecf6a341abec92f5899d813c73f4b022", "sha256_in_prefix": "7f156fea1f63aeae00029521e23df1efecf6a341abec92f5899d813c73f4b022", "size_in_bytes": 3931}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/import_utils.py", "path_type": "hardlink", "sha256": "b08bcfa3c226d2a239dbb909157371a812b8fef8bd5e6a4c3ed2a77dd78f80ad", "sha256_in_prefix": "b08bcfa3c226d2a239dbb909157371a812b8fef8bd5e6a4c3ed2a77dd78f80ad", "size_in_bytes": 631}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/io_binding_helper.py", "path_type": "hardlink", "sha256": "6047ac7dc1fa87bf1b88110f1200916f27b25c4d91c323165ff6341dfae47bbb", "sha256_in_prefix": "6047ac7dc1fa87bf1b88110f1200916f27b25c4d91c323165ff6341dfae47bbb", "size_in_bytes": 17105}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/large_model_exporter.py", "path_type": "hardlink", "sha256": "ea17befa95bbdf47d7f4dfb845db82e4c5f3a41f402b19d5925fdba5ba45ada5", "sha256_in_prefix": "ea17befa95bbdf47d7f4dfb845db82e4c5f3a41f402b19d5925fdba5ba45ada5", "size_in_bytes": 14901}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/machine_info.py", "path_type": "hardlink", "sha256": "610a983ddaa8d04af3429e4561e215fdc2325d3715b36565afdfb0206e77b231", "sha256_in_prefix": "610a983ddaa8d04af3429e4561e215fdc2325d3715b36565afdfb0206e77b231", "size_in_bytes": 7225}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/metrics.py", "path_type": "hardlink", "sha256": "f96c28503271887115f5a693ff86581c2c362ad3223d41a0fa7bffe17adb8a8e", "sha256_in_prefix": "f96c28503271887115f5a693ff86581c2c362ad3223d41a0fa7bffe17adb8a8e", "size_in_bytes": 5060}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/bart/__init__.py", "path_type": "hardlink", "sha256": "07046f69491f612ac655dcf64fa8c28be707f2047d503deadc6aed32d9092fcc", "sha256_in_prefix": "07046f69491f612ac655dcf64fa8c28be707f2047d503deadc6aed32d9092fcc", "size_in_bytes": 483}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/bart/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "93edd83af19894f97021c6e0dd13da8939bbd148693319c1314a826a081cf8b5", "sha256_in_prefix": "93edd83af19894f97021c6e0dd13da8939bbd148693319c1314a826a081cf8b5", "size_in_bytes": 766}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/bart/__pycache__/export.cpython-311.pyc", "path_type": "hardlink", "sha256": "29c10ebf623d7cebe84400423e125e4c631759e208fbcdba571229ef7a8a9ea5", "sha256_in_prefix": "29c10ebf623d7cebe84400423e125e4c631759e208fbcdba571229ef7a8a9ea5", "size_in_bytes": 5832}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/bart/export.py", "path_type": "hardlink", "sha256": "6307027daca8876dc535842b6daa70b294dd2f4a9244d269dbc6c0cab14e984e", "sha256_in_prefix": "6307027daca8876dc535842b6daa70b294dd2f4a9244d269dbc6c0cab14e984e", "size_in_bytes": 4187}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/bert/__init__.py", "path_type": "hardlink", "sha256": "07046f69491f612ac655dcf64fa8c28be707f2047d503deadc6aed32d9092fcc", "sha256_in_prefix": "07046f69491f612ac655dcf64fa8c28be707f2047d503deadc6aed32d9092fcc", "size_in_bytes": 483}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/bert/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "f95c5c96639aaef735f0bad492ca7917aa1f6ba1ce79c2f897420b745e89b2f2", "sha256_in_prefix": "f95c5c96639aaef735f0bad492ca7917aa1f6ba1ce79c2f897420b745e89b2f2", "size_in_bytes": 766}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/bert/__pycache__/eval_squad.cpython-311.pyc", "path_type": "hardlink", "sha256": "4511b0a43862e1c88e3123b82f34057e44e7944625da6f0d624499629a330a55", "sha256_in_prefix": "4511b0a43862e1c88e3123b82f34057e44e7944625da6f0d624499629a330a55", "size_in_bytes": 13962}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/bert/eval_squad.py", "path_type": "hardlink", "sha256": "2738696764077e0e4428bd337a7271a73a9c59a1579dafae06ba0ae66150fd81", "sha256_in_prefix": "2738696764077e0e4428bd337a7271a73a9c59a1579dafae06ba0ae66150fd81", "size_in_bytes": 12024}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/__init__.py", "path_type": "hardlink", "sha256": "07046f69491f612ac655dcf64fa8c28be707f2047d503deadc6aed32d9092fcc", "sha256_in_prefix": "07046f69491f612ac655dcf64fa8c28be707f2047d503deadc6aed32d9092fcc", "size_in_bytes": 483}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "44afd6f631608bc36f83ca3bcf72f09752c47f2b39f39be70ae0ddadda056984", "sha256_in_prefix": "44afd6f631608bc36f83ca3bcf72f09752c47f2b39f39be70ae0ddadda056984", "size_in_bytes": 766}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/__pycache__/benchmark_gpt2.cpython-311.pyc", "path_type": "hardlink", "sha256": "1662d60faaac973bb7cd1c0b7e4cc654987371a2bc145823d730491419be2434", "sha256_in_prefix": "1662d60faaac973bb7cd1c0b7e4cc654987371a2bc145823d730491419be2434", "size_in_bytes": 15665}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/__pycache__/convert_to_onnx.cpython-311.pyc", "path_type": "hardlink", "sha256": "5694974e12cd0c9c6340ae98fa8a2c2c1754d6b355d7d24be578797b683036ec", "sha256_in_prefix": "5694974e12cd0c9c6340ae98fa8a2c2c1754d6b355d7d24be578797b683036ec", "size_in_bytes": 22326}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/__pycache__/gpt2_helper.cpython-311.pyc", "path_type": "hardlink", "sha256": "27cddc86b3bb85891b97ca5d31ad133484c5f29bd01b89f220ea1ad46f81aed1", "sha256_in_prefix": "27cddc86b3bb85891b97ca5d31ad133484c5f29bd01b89f220ea1ad46f81aed1", "size_in_bytes": 48632}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/__pycache__/gpt2_parity.cpython-311.pyc", "path_type": "hardlink", "sha256": "0613b386abf8c19428cc3462ab7b897fb9402beedf0656bfd0f8392ce88187bd", "sha256_in_prefix": "0613b386abf8c19428cc3462ab7b897fb9402beedf0656bfd0f8392ce88187bd", "size_in_bytes": 22424}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/__pycache__/gpt2_tester.cpython-311.pyc", "path_type": "hardlink", "sha256": "59fb44a51f0eef58cb6f5b6574f21dd137fda300e6bdf6b1f8f915b1bf7a445e", "sha256_in_prefix": "59fb44a51f0eef58cb6f5b6574f21dd137fda300e6bdf6b1f8f915b1bf7a445e", "size_in_bytes": 26508}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/__pycache__/parity_check_helper.cpython-311.pyc", "path_type": "hardlink", "sha256": "156ca883d33afb598988f11514cd1824e4165912fecabc26046ba6fc2368d417", "sha256_in_prefix": "156ca883d33afb598988f11514cd1824e4165912fecabc26046ba6fc2368d417", "size_in_bytes": 7700}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/benchmark_gpt2.py", "path_type": "hardlink", "sha256": "2159c8d1969c55b2d009c2c30248db59a86a3e8829300b970b10dcd9489644ab", "sha256_in_prefix": "2159c8d1969c55b2d009c2c30248db59a86a3e8829300b970b10dcd9489644ab", "size_in_bytes": 15517}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/convert_to_onnx.py", "path_type": "hardlink", "sha256": "0e921157448eafe3ce8f53f5d5df382db299a035c209d5be3bcd7a5935670ad1", "sha256_in_prefix": "0e921157448eafe3ce8f53f5d5df382db299a035c209d5be3bcd7a5935670ad1", "size_in_bytes": 20060}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/gpt2_helper.py", "path_type": "hardlink", "sha256": "85b9e214eb772a980b444a0f425ec36c2ba104319cf3e69db91bd44cca90471b", "sha256_in_prefix": "85b9e214eb772a980b444a0f425ec36c2ba104319cf3e69db91bd44cca90471b", "size_in_bytes": 40300}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/gpt2_parity.py", "path_type": "hardlink", "sha256": "d5fc5e1ba1fe4f7760d47af99354c89a810b5dbbdc9cceeba396606f75bb9a40", "sha256_in_prefix": "d5fc5e1ba1fe4f7760d47af99354c89a810b5dbbdc9cceeba396606f75bb9a40", "size_in_bytes": 17718}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/gpt2_tester.py", "path_type": "hardlink", "sha256": "c58c02b13dd7fa932f765afbee53b1538494afe89ff40f31c682d5a7413a9997", "sha256_in_prefix": "c58c02b13dd7fa932f765afbee53b1538494afe89ff40f31c682d5a7413a9997", "size_in_bytes": 19519}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/gpt2/parity_check_helper.py", "path_type": "hardlink", "sha256": "14504c2fbc3437c475603fd0b27faf02c79211d734e8f1ad73d012ca22516ec0", "sha256_in_prefix": "14504c2fbc3437c475603fd0b27faf02c79211d734e8f1ad73d012ca22516ec0", "size_in_bytes": 5660}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__init__.py", "path_type": "hardlink", "sha256": "c74bfebb1533dfd0926b02c6d5107e26c761f63e57db09614b7289bb0be4dcf8", "sha256_in_prefix": "c74bfebb1533dfd0926b02c6d5107e26c761f63e57db09614b7289bb0be4dcf8", "size_in_bytes": 478}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "f418df53f698c4f6b43833d8c2fa76950f2017c8aa65e0cedb27edcfaf278263", "sha256_in_prefix": "f418df53f698c4f6b43833d8c2fa76950f2017c8aa65e0cedb27edcfaf278263", "size_in_bytes": 758}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/benchmark.cpython-311.pyc", "path_type": "hardlink", "sha256": "1a90843347c47c4db46dd205d5938ff1fa9eadcc6e33091320a73f9dc51e6b8d", "sha256_in_prefix": "1a90843347c47c4db46dd205d5938ff1fa9eadcc6e33091320a73f9dc51e6b8d", "size_in_bytes": 29120}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/benchmark_all.cpython-311.pyc", "path_type": "hardlink", "sha256": "edc0bddb14807bbce77de71103469f3b2fbfd184655aeaeadcbf28b56be3fd5d", "sha256_in_prefix": "edc0bddb14807bbce77de71103469f3b2fbfd184655aeaeadcbf28b56be3fd5d", "size_in_bytes": 16304}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/benchmark_e2e.cpython-311.pyc", "path_type": "hardlink", "sha256": "9123c38a608bbd4f00afc0588ecf9b3fa0ca513bc744760f7f7f7334b5052a7b", "sha256_in_prefix": "9123c38a608bbd4f00afc0588ecf9b3fa0ca513bc744760f7f7f7334b5052a7b", "size_in_bytes": 25946}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/convert_to_onnx.cpython-311.pyc", "path_type": "hardlink", "sha256": "8525aa3a418c06beea4588a65774fdf2c21b7215cc02491e506b5cd692bdb509", "sha256_in_prefix": "8525aa3a418c06beea4588a65774fdf2c21b7215cc02491e506b5cd692bdb509", "size_in_bytes": 47072}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/dist_settings.cpython-311.pyc", "path_type": "hardlink", "sha256": "b11cdf97b3652d38af276c23cff05a9ba385ffdcffe6c036bd5696d283254e0b", "sha256_in_prefix": "b11cdf97b3652d38af276c23cff05a9ba385ffdcffe6c036bd5696d283254e0b", "size_in_bytes": 2726}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/llama_inputs.cpython-311.pyc", "path_type": "hardlink", "sha256": "d7b25f8d7e418cc97854d83fa3ab5e801ef2da43a6748c9dcb357f73811e8b04", "sha256_in_prefix": "d7b25f8d7e418cc97854d83fa3ab5e801ef2da43a6748c9dcb357f73811e8b04", "size_in_bytes": 21832}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/llama_parity.cpython-311.pyc", "path_type": "hardlink", "sha256": "b474a6cbd25b3dfc57b772de221be1137a2459072d632024dfe0f84a8db374e0", "sha256_in_prefix": "b474a6cbd25b3dfc57b772de221be1137a2459072d632024dfe0f84a8db374e0", "size_in_bytes": 14329}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/llama_torch.cpython-311.pyc", "path_type": "hardlink", "sha256": "2f7dc216813c00cadf1e8d20dc08b4abe7b3b835a4567b7c14f45736f34f9e89", "sha256_in_prefix": "2f7dc216813c00cadf1e8d20dc08b4abe7b3b835a4567b7c14f45736f34f9e89", "size_in_bytes": 2106}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/__pycache__/quant_kv_dataloader.cpython-311.pyc", "path_type": "hardlink", "sha256": "7e5380c2c23dc50ee74fb82f5b98a82713b746090350d7e3c4996c4972263ca7", "sha256_in_prefix": "7e5380c2c23dc50ee74fb82f5b98a82713b746090350d7e3c4996c4972263ca7", "size_in_bytes": 6926}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/benchmark.py", "path_type": "hardlink", "sha256": "892597f3cbac9151c650a612829f3c72dad1ed76c675ce03289fe26e9bc5db5c", "sha256_in_prefix": "892597f3cbac9151c650a612829f3c72dad1ed76c675ce03289fe26e9bc5db5c", "size_in_bytes": 26785}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/benchmark_all.py", "path_type": "hardlink", "sha256": "a9c3eddeb897f4dc7d97c958824c5423dcd5550ab355033dfc79f7cb6cbdf09f", "sha256_in_prefix": "a9c3eddeb897f4dc7d97c958824c5423dcd5550ab355033dfc79f7cb6cbdf09f", "size_in_bytes": 15279}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/benchmark_e2e.py", "path_type": "hardlink", "sha256": "4da543b764f7a8d7e46d382d9c464b3860e512591468d93a1e38230b45a458dc", "sha256_in_prefix": "4da543b764f7a8d7e46d382d9c464b3860e512591468d93a1e38230b45a458dc", "size_in_bytes": 24869}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/convert_to_onnx.py", "path_type": "hardlink", "sha256": "99b38bb1b184017558c9c597c9e08d1eb85c674b8215788728a0f0c29e397138", "sha256_in_prefix": "99b38bb1b184017558c9c597c9e08d1eb85c674b8215788728a0f0c29e397138", "size_in_bytes": 43718}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/dist_settings.py", "path_type": "hardlink", "sha256": "be18ec4fc177d8f6c256b1263ec58b1186fdedc1f7f1a8d182060d86e2d2e9ca", "sha256_in_prefix": "be18ec4fc177d8f6c256b1263ec58b1186fdedc1f7f1a8d182060d86e2d2e9ca", "size_in_bytes": 1579}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/llama_inputs.py", "path_type": "hardlink", "sha256": "79931e42126f61c3882da3991c3ebc1ab31c105aa8ef9d19e45c3fb74dcb386e", "sha256_in_prefix": "79931e42126f61c3882da3991c3ebc1ab31c105aa8ef9d19e45c3fb74dcb386e", "size_in_bytes": 20222}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/llama_parity.py", "path_type": "hardlink", "sha256": "c8c16a2ba8ad7a25aad0ecf155daa58e41153dcc60f173a526bad29aea3f3767", "sha256_in_prefix": "c8c16a2ba8ad7a25aad0ecf155daa58e41153dcc60f173a526bad29aea3f3767", "size_in_bytes": 11476}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/llama_torch.py", "path_type": "hardlink", "sha256": "7163fcc7518d07631c9cf0d26c0109ba1b8762e5d99cf97c4ab21c732abf6e4e", "sha256_in_prefix": "7163fcc7518d07631c9cf0d26c0109ba1b8762e5d99cf97c4ab21c732abf6e4e", "size_in_bytes": 1685}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/llama/quant_kv_dataloader.py", "path_type": "hardlink", "sha256": "b16219e144292dc19d3642bbb3a4600a797c279c1c74c035eda910c6827e5649", "sha256_in_prefix": "b16219e144292dc19d3642bbb3a4600a797c279c1c74c035eda910c6827e5649", "size_in_bytes": 4851}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/__init__.py", "path_type": "hardlink", "sha256": "07046f69491f612ac655dcf64fa8c28be707f2047d503deadc6aed32d9092fcc", "sha256_in_prefix": "07046f69491f612ac655dcf64fa8c28be707f2047d503deadc6aed32d9092fcc", "size_in_bytes": 483}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "8a9f3c90f90198513edb789a7e45589fcaaf0589359678f9e766379c69327c94", "sha256_in_prefix": "8a9f3c90f90198513edb789a7e45589fcaaf0589359678f9e766379c69327c94", "size_in_bytes": 772}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/__pycache__/benchmark_longformer.cpython-311.pyc", "path_type": "hardlink", "sha256": "d6452a582c20c17ccf3684944ec32736aa11161cfa428785b80a0c85afbab3ed", "sha256_in_prefix": "d6452a582c20c17ccf3684944ec32736aa11161cfa428785b80a0c85afbab3ed", "size_in_bytes": 31521}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/__pycache__/convert_to_onnx.cpython-311.pyc", "path_type": "hardlink", "sha256": "d249cab061d1e65242b6de1a9e085ee7effd8e610134fe9ef523876b43814511", "sha256_in_prefix": "d249cab061d1e65242b6de1a9e085ee7effd8e610134fe9ef523876b43814511", "size_in_bytes": 14227}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/__pycache__/generate_test_data.cpython-311.pyc", "path_type": "hardlink", "sha256": "b869bfd9eef09c742756151ee183b8dc38c0558fbb36b961aac9ceae53377832", "sha256_in_prefix": "b869bfd9eef09c742756151ee183b8dc38c0558fbb36b961aac9ceae53377832", "size_in_bytes": 10335}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/__pycache__/longformer_helper.cpython-311.pyc", "path_type": "hardlink", "sha256": "d1af629727539fea82c944272280a0d3b86f3d87859a186f23ef5b411fa67379", "sha256_in_prefix": "d1af629727539fea82c944272280a0d3b86f3d87859a186f23ef5b411fa67379", "size_in_bytes": 4746}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/benchmark_longformer.py", "path_type": "hardlink", "sha256": "6b961e8dc05ea4bb9e7726fa148acf9e384d9740f1fbd075ae85b113d98c11ee", "sha256_in_prefix": "6b961e8dc05ea4bb9e7726fa148acf9e384d9740f1fbd075ae85b113d98c11ee", "size_in_bytes": 29417}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/convert_to_onnx.py", "path_type": "hardlink", "sha256": "133d207f263bcba1049d40c8cf0e85cf223891061234fee88c58c7f7c6d343af", "sha256_in_prefix": "133d207f263bcba1049d40c8cf0e85cf223891061234fee88c58c7f7c6d343af", "size_in_bytes": 14806}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/generate_test_data.py", "path_type": "hardlink", "sha256": "d2e445029cf94720d42abf702a5004ac54f1bf95c73225eb01aff51ed2687a17", "sha256_in_prefix": "d2e445029cf94720d42abf702a5004ac54f1bf95c73225eb01aff51ed2687a17", "size_in_bytes": 9617}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/longformer/longformer_helper.py", "path_type": "hardlink", "sha256": "efd948098c7b85a1774ea52daba4f0e98fd6e4033d4b5149e7674744ab922e7f", "sha256_in_prefix": "efd948098c7b85a1774ea52daba4f0e98fd6e4033d4b5149e7674744ab922e7f", "size_in_bytes": 3047}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/phi2/__init__.py", "path_type": "hardlink", "sha256": "c74bfebb1533dfd0926b02c6d5107e26c761f63e57db09614b7289bb0be4dcf8", "sha256_in_prefix": "c74bfebb1533dfd0926b02c6d5107e26c761f63e57db09614b7289bb0be4dcf8", "size_in_bytes": 478}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/phi2/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "55518f55ff09670078f7a445204c3b8295aaf1aed6db7fb37796fdbb9006ab22", "sha256_in_prefix": "55518f55ff09670078f7a445204c3b8295aaf1aed6db7fb37796fdbb9006ab22", "size_in_bytes": 757}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/phi2/__pycache__/convert_to_onnx.cpython-311.pyc", "path_type": "hardlink", "sha256": "bd16e4bf6330c4ca730147ea44d6838181d24b9dd407a2f61082995710ccf533", "sha256_in_prefix": "bd16e4bf6330c4ca730147ea44d6838181d24b9dd407a2f61082995710ccf533", "size_in_bytes": 23573}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/phi2/__pycache__/inference_example.cpython-311.pyc", "path_type": "hardlink", "sha256": "3426a8aa7732bb2e2357a8a341a6862fc1792b9fd3b458b1a4de0c1d6495d77e", "sha256_in_prefix": "3426a8aa7732bb2e2357a8a341a6862fc1792b9fd3b458b1a4de0c1d6495d77e", "size_in_bytes": 20522}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/phi2/convert_to_onnx.py", "path_type": "hardlink", "sha256": "2ab6f6d00b9e91390b2b913dfb57c67ee9e4123e454834b31af942a187a1fc37", "sha256_in_prefix": "2ab6f6d00b9e91390b2b913dfb57c67ee9e4123e454834b31af942a187a1fc37", "size_in_bytes": 20058}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/phi2/inference_example.py", "path_type": "hardlink", "sha256": "644567d29fe57b31065d0aa29a91c3fb8f9c2753bf97d0668d3bdef4f46ced5f", "sha256_in_prefix": "644567d29fe57b31065d0aa29a91c3fb8f9c2753bf97d0668d3bdef4f46ced5f", "size_in_bytes": 17286}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__init__.py", "path_type": "hardlink", "sha256": "07046f69491f612ac655dcf64fa8c28be707f2047d503deadc6aed32d9092fcc", "sha256_in_prefix": "07046f69491f612ac655dcf64fa8c28be707f2047d503deadc6aed32d9092fcc", "size_in_bytes": 483}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "62595eb0a97383a7cea196bb8fd9fc4b48b93e9da610304b2866ab73cd12fb00", "sha256_in_prefix": "62595eb0a97383a7cea196bb8fd9fc4b48b93e9da610304b2866ab73cd12fb00", "size_in_bytes": 766}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/benchmark_sam2.cpython-311.pyc", "path_type": "hardlink", "sha256": "b8fe55a9b63fc6373bcbd23e3049f0d6936e84b65c3699173dc5970e60d3b460", "sha256_in_prefix": "b8fe55a9b63fc6373bcbd23e3049f0d6936e84b65c3699173dc5970e60d3b460", "size_in_bytes": 27520}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/convert_to_onnx.cpython-311.pyc", "path_type": "hardlink", "sha256": "50a8bdc7ee7b72a1062a4a5214afddfc16341278d7ee374a8488254d6f29ca5e", "sha256_in_prefix": "50a8bdc7ee7b72a1062a4a5214afddfc16341278d7ee374a8488254d6f29ca5e", "size_in_bytes": 10812}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/image_decoder.cpython-311.pyc", "path_type": "hardlink", "sha256": "1060096208cd8907992179178dc35e14f96dac8e71e8697c308449967f0da821", "sha256_in_prefix": "1060096208cd8907992179178dc35e14f96dac8e71e8697c308449967f0da821", "size_in_bytes": 13775}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/image_encoder.cpython-311.pyc", "path_type": "hardlink", "sha256": "60d065c1329b26fcf75f17fffe16748f1995afe6a8a82db911f9717bb54594df", "sha256_in_prefix": "60d065c1329b26fcf75f17fffe16748f1995afe6a8a82db911f9717bb54594df", "size_in_bytes": 12716}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/mask_decoder.cpython-311.pyc", "path_type": "hardlink", "sha256": "22e40a76382eacc00eecf736eeca8243742ebdaa050f3a46edbe3956255e2877", "sha256_in_prefix": "22e40a76382eacc00eecf736eeca8243742ebdaa050f3a46edbe3956255e2877", "size_in_bytes": 11063}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/nvtx_helper.cpython-311.pyc", "path_type": "hardlink", "sha256": "9b621315aeb22eb4e6844e85e279c39740869d5973fe635ff2b4c4ecdfdda959", "sha256_in_prefix": "9b621315aeb22eb4e6844e85e279c39740869d5973fe635ff2b4c4ecdfdda959", "size_in_bytes": 2422}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/prompt_encoder.cpython-311.pyc", "path_type": "hardlink", "sha256": "91c71b3b99b8aaf607644d6021da621b0d9d5539244c327b76cffd9582318829", "sha256_in_prefix": "91c71b3b99b8aaf607644d6021da621b0d9d5539244c327b76cffd9582318829", "size_in_bytes": 11418}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/sam2_demo.cpython-311.pyc", "path_type": "hardlink", "sha256": "ba99497aab37e89dfc6a8e65575bf10f12f0075c3a5cd8c4dd2d2cc5c6fd3666", "sha256_in_prefix": "ba99497aab37e89dfc6a8e65575bf10f12f0075c3a5cd8c4dd2d2cc5c6fd3666", "size_in_bytes": 13938}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/sam2_image_onnx_predictor.cpython-311.pyc", "path_type": "hardlink", "sha256": "2ead33aa5eb3f8bde101229ced525379efc5edabb62f6e21b4a8cdaae5e92eab", "sha256_in_prefix": "2ead33aa5eb3f8bde101229ced525379efc5edabb62f6e21b4a8cdaae5e92eab", "size_in_bytes": 15222}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/__pycache__/sam2_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "62dd8d54d69ef92770e5172ae3e57133b8477abe82608e88a5ba2ae998d85dba", "sha256_in_prefix": "62dd8d54d69ef92770e5172ae3e57133b8477abe82608e88a5ba2ae998d85dba", "size_in_bytes": 7780}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/benchmark_sam2.py", "path_type": "hardlink", "sha256": "ee0ad5947692b3a99767ba5d758d0b4d20f17d23b2605ae2ac537cf1d7fa45e1", "sha256_in_prefix": "ee0ad5947692b3a99767ba5d758d0b4d20f17d23b2605ae2ac537cf1d7fa45e1", "size_in_bytes": 21728}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/convert_to_onnx.py", "path_type": "hardlink", "sha256": "4a77473b232208d9568c267e1bb002f733c5f3ea45933953438576e5f35f84de", "sha256_in_prefix": "4a77473b232208d9568c267e1bb002f733c5f3ea45933953438576e5f35f84de", "size_in_bytes": 10382}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/image_decoder.py", "path_type": "hardlink", "sha256": "37811921edc67e122c56d7f3e390e9697ca5211429e25ff63641bf3df53819e1", "sha256_in_prefix": "37811921edc67e122c56d7f3e390e9697ca5211429e25ff63641bf3df53819e1", "size_in_bytes": 10787}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/image_encoder.py", "path_type": "hardlink", "sha256": "5249aa77f18bcc1b7e0732070bac567c6cecdea36bad4d906b96b47d2d6d1629", "sha256_in_prefix": "5249aa77f18bcc1b7e0732070bac567c6cecdea36bad4d906b96b47d2d6d1629", "size_in_bytes": 9484}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/mask_decoder.py", "path_type": "hardlink", "sha256": "a4f969c138d63cea4a4f4f8028d957983ff74fac9377ad217c4b6079586db3fe", "sha256_in_prefix": "a4f969c138d63cea4a4f4f8028d957983ff74fac9377ad217c4b6079586db3fe", "size_in_bytes": 8841}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/nvtx_helper.py", "path_type": "hardlink", "sha256": "31d76e7c6599a47f4320c2740deca6b96ee73e623fce9190c434a86234b42a1d", "sha256_in_prefix": "31d76e7c6599a47f4320c2740deca6b96ee73e623fce9190c434a86234b42a1d", "size_in_bytes": 1279}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/prompt_encoder.py", "path_type": "hardlink", "sha256": "abcf97fd7f5abbfc5ce6cf41d044d71eaa4fb43845fb41ef37b3d42f46556671", "sha256_in_prefix": "abcf97fd7f5abbfc5ce6cf41d044d71eaa4fb43845fb41ef37b3d42f46556671", "size_in_bytes": 8324}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/sam2_demo.py", "path_type": "hardlink", "sha256": "357cabb2cd7ba49ac0c4cf7cc2d4fd0f2b297b37dc290c3058b6c2d2b20f9fe3", "sha256_in_prefix": "357cabb2cd7ba49ac0c4cf7cc2d4fd0f2b297b37dc290c3058b6c2d2b20f9fe3", "size_in_bytes": 10484}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/sam2_image_onnx_predictor.py", "path_type": "hardlink", "sha256": "7feb0499bf8375ac3f1144fbce6338cdb7f750c36eb35685835db257e66c9687", "sha256_in_prefix": "7feb0499bf8375ac3f1144fbce6338cdb7f750c36eb35685835db257e66c9687", "size_in_bytes": 12423}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/sam2/sam2_utils.py", "path_type": "hardlink", "sha256": "0fcd9919c85215e79cdaf848c7cd6e7e19fd326ab594ff63202e8979315ae4d0", "sha256_in_prefix": "0fcd9919c85215e79cdaf848c7cd6e7e19fd326ab594ff63202e8979315ae4d0", "size_in_bytes": 5520}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__init__.py", "path_type": "hardlink", "sha256": "07046f69491f612ac655dcf64fa8c28be707f2047d503deadc6aed32d9092fcc", "sha256_in_prefix": "07046f69491f612ac655dcf64fa8c28be707f2047d503deadc6aed32d9092fcc", "size_in_bytes": 483}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "b867335a490b72ab0970c4f4299a5838aca537b4997ab0355844db4f8eea629c", "sha256_in_prefix": "b867335a490b72ab0970c4f4299a5838aca537b4997ab0355844db4f8eea629c", "size_in_bytes": 778}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/benchmark.cpython-311.pyc", "path_type": "hardlink", "sha256": "cbdbcf078aca6c26b67200c92c1289d3245c53a142664cf52cb2f1eedb604099", "sha256_in_prefix": "cbdbcf078aca6c26b67200c92c1289d3245c53a142664cf52cb2f1eedb604099", "size_in_bytes": 48785}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/benchmark_controlnet.cpython-311.pyc", "path_type": "hardlink", "sha256": "c07d0c8d641c9b7c3465d6f7fae7ea9608e784963237e1c1721f3b6a1c31dd31", "sha256_in_prefix": "c07d0c8d641c9b7c3465d6f7fae7ea9608e784963237e1c1721f3b6a1c31dd31", "size_in_bytes": 15075}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/demo_txt2img.cpython-311.pyc", "path_type": "hardlink", "sha256": "8e471517ac33ef33986460ba17be283c6a00e8856d8464581d64d5e90857dc39", "sha256_in_prefix": "8e471517ac33ef33986460ba17be283c6a00e8856d8464581d64d5e90857dc39", "size_in_bytes": 3809}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/demo_txt2img_xl.cpython-311.pyc", "path_type": "hardlink", "sha256": "3b146fd28bd630743aea7601db675cd1ef2c1b8196ef2f07aeeff5ddf6952b3a", "sha256_in_prefix": "3b146fd28bd630743aea7601db675cd1ef2c1b8196ef2f07aeeff5ddf6952b3a", "size_in_bytes": 12272}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/demo_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "969442b87007550bd20090cfe447e5db0fde9c3d1c8f13be17219321716dcd3f", "sha256_in_prefix": "969442b87007550bd20090cfe447e5db0fde9c3d1c8f13be17219321716dcd3f", "size_in_bytes": 32387}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/diffusion_models.cpython-311.pyc", "path_type": "hardlink", "sha256": "c584f8d00f7c81649ff268a3ce779364a622cfe5890a260c8bf79878937d8d66", "sha256_in_prefix": "c584f8d00f7c81649ff268a3ce779364a622cfe5890a260c8bf79878937d8d66", "size_in_bytes": 59112}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/diffusion_schedulers.cpython-311.pyc", "path_type": "hardlink", "sha256": "cfaeb36e49d275af6f724048eb6483ff0a6ecf420203d15ae70f7236e1535778", "sha256_in_prefix": "cfaeb36e49d275af6f724048eb6483ff0a6ecf420203d15ae70f7236e1535778", "size_in_bytes": 52782}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder.cpython-311.pyc", "path_type": "hardlink", "sha256": "287fa5dffd09fcb4fc1bdc5c73d07009ade6392431950b915b28f833fc09a78e", "sha256_in_prefix": "287fa5dffd09fcb4fc1bdc5c73d07009ade6392431950b915b28f833fc09a78e", "size_in_bytes": 14876}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_ort_cuda.cpython-311.pyc", "path_type": "hardlink", "sha256": "ab67536626bd296dd344507bc41fa67a312eb9e010f9a30f7c69d0c1d754dca9", "sha256_in_prefix": "ab67536626bd296dd344507bc41fa67a312eb9e010f9a30f7c69d0c1d754dca9", "size_in_bytes": 17798}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_ort_trt.cpython-311.pyc", "path_type": "hardlink", "sha256": "b168b23849fa3861432d3b4f5ce9507332af63a97f78744be870b68fae821904", "sha256_in_prefix": "b168b23849fa3861432d3b4f5ce9507332af63a97f78744be870b68fae821904", "size_in_bytes": 14036}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_tensorrt.cpython-311.pyc", "path_type": "hardlink", "sha256": "f124a06ad5a2a9bba112da6e44bb04841711e0ead779622514932cd41dc142c8", "sha256_in_prefix": "f124a06ad5a2a9bba112da6e44bb04841711e0ead779622514932cd41dc142c8", "size_in_bytes": 18932}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/engine_builder_torch.cpython-311.pyc", "path_type": "hardlink", "sha256": "de5cd26b975eb739df0affd2e9bdd22b539fc803b58316a0b555c67d6245b3f1", "sha256_in_prefix": "de5cd26b975eb739df0affd2e9bdd22b539fc803b58316a0b555c67d6245b3f1", "size_in_bytes": 5016}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/optimize_pipeline.cpython-311.pyc", "path_type": "hardlink", "sha256": "955b09a3b00086e786cba078a21df6b6f2a9b8d6d10fcc9c99ec1b9838003419", "sha256_in_prefix": "955b09a3b00086e786cba078a21df6b6f2a9b8d6d10fcc9c99ec1b9838003419", "size_in_bytes": 20007}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/ort_optimizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "a6678560a0ba8c02e341f08f9dc4717ad9067d8d5aafdad9ebb8690b68a10303", "sha256_in_prefix": "a6678560a0ba8c02e341f08f9dc4717ad9067d8d5aafdad9ebb8690b68a10303", "size_in_bytes": 6192}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/pipeline_stable_diffusion.cpython-311.pyc", "path_type": "hardlink", "sha256": "92cd64f1cf7e9c354ad17e9e4854e6d9ac44f62ec347a1ac13ad83e58f17b4a8", "sha256_in_prefix": "92cd64f1cf7e9c354ad17e9e4854e6d9ac44f62ec347a1ac13ad83e58f17b4a8", "size_in_bytes": 39423}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/__pycache__/trt_utilities.cpython-311.pyc", "path_type": "hardlink", "sha256": "35fde2e10dfa3efcaa14177ff2188f6ec93b899294b5979c7d57e18ec589cab9", "sha256_in_prefix": "35fde2e10dfa3efcaa14177ff2188f6ec93b899294b5979c7d57e18ec589cab9", "size_in_bytes": 574}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/benchmark.py", "path_type": "hardlink", "sha256": "4a8a4ef7e4b34d1278ff9a63e9310b47434c3a63de9bbc1b6b80ddd199ee8d59", "sha256_in_prefix": "4a8a4ef7e4b34d1278ff9a63e9310b47434c3a63de9bbc1b6b80ddd199ee8d59", "size_in_bytes": 49616}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/benchmark_controlnet.py", "path_type": "hardlink", "sha256": "88aebf9e23d035a79c4ca3635a656f6cc830ec38f5ed9a7d76c3ec8f40c72b12", "sha256_in_prefix": "88aebf9e23d035a79c4ca3635a656f6cc830ec38f5ed9a7d76c3ec8f40c72b12", "size_in_bytes": 12827}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/demo_txt2img.py", "path_type": "hardlink", "sha256": "2a8350dc34399a4789fabdc0d3b00d5208fd1f71e965d10263c1357a9fccc076", "sha256_in_prefix": "2a8350dc34399a4789fabdc0d3b00d5208fd1f71e965d10263c1357a9fccc076", "size_in_bytes": 3292}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/demo_txt2img_xl.py", "path_type": "hardlink", "sha256": "8269489800b6f794433ffd361d46d82db207024748f9879303ae5ec8dd5681c1", "sha256_in_prefix": "8269489800b6f794433ffd361d46d82db207024748f9879303ae5ec8dd5681c1", "size_in_bytes": 9911}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/demo_utils.py", "path_type": "hardlink", "sha256": "e166dc377e4de53f18ea71e2a989b23b753142853a2f0d2220476adff478a270", "sha256_in_prefix": "e166dc377e4de53f18ea71e2a989b23b753142853a2f0d2220476adff478a270", "size_in_bytes": 28564}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/diffusion_models.py", "path_type": "hardlink", "sha256": "4aa5af1ae65f4af43fe91924c90739bc12b73b9ae5e05a3a89d21e38a67bc921", "sha256_in_prefix": "4aa5af1ae65f4af43fe91924c90739bc12b73b9ae5e05a3a89d21e38a67bc921", "size_in_bytes": 50398}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/diffusion_schedulers.py", "path_type": "hardlink", "sha256": "267fac61dfcb3ee2510ad17051b50993eca50ca84124b80a609dc6ddfc273745", "sha256_in_prefix": "267fac61dfcb3ee2510ad17051b50993eca50ca84124b80a609dc6ddfc273745", "size_in_bytes": 48289}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/engine_builder.py", "path_type": "hardlink", "sha256": "7bc007482ba39120d4be09520bbca6e085a3a69753282aae5c77c6ab03985d1d", "sha256_in_prefix": "7bc007482ba39120d4be09520bbca6e085a3a69753282aae5c77c6ab03985d1d", "size_in_bytes": 11653}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/engine_builder_ort_cuda.py", "path_type": "hardlink", "sha256": "31a113189c1ee4cd2f37e1075ba4d38c43bf394c683d467713211057fa31eb9d", "sha256_in_prefix": "31a113189c1ee4cd2f37e1075ba4d38c43bf394c683d467713211057fa31eb9d", "size_in_bytes": 15854}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/engine_builder_ort_trt.py", "path_type": "hardlink", "sha256": "4bd404e9842808a3a491823b6b8d1eb26553c780a4ed8d5224fd16657eba8de3", "sha256_in_prefix": "4bd404e9842808a3a491823b6b8d1eb26553c780a4ed8d5224fd16657eba8de3", "size_in_bytes": 11163}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/engine_builder_tensorrt.py", "path_type": "hardlink", "sha256": "1a07461aa64ad7dd37f9f62d4375fc59b0dcc76f02968e900b2a7bcfffd64703", "sha256_in_prefix": "1a07461aa64ad7dd37f9f62d4375fc59b0dcc76f02968e900b2a7bcfffd64703", "size_in_bytes": 15604}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/engine_builder_torch.py", "path_type": "hardlink", "sha256": "363586bb73e814808c745cf54f6dccde1014cd7faca6299959d4eaf692331349", "sha256_in_prefix": "363586bb73e814808c745cf54f6dccde1014cd7faca6299959d4eaf692331349", "size_in_bytes": 4181}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/optimize_pipeline.py", "path_type": "hardlink", "sha256": "de42b26aa3a5d4f00a449ba9615280f1bc1b9851ce059845b40a565f49f5ce53", "sha256_in_prefix": "de42b26aa3a5d4f00a449ba9615280f1bc1b9851ce059845b40a565f49f5ce53", "size_in_bytes": 21491}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/ort_optimizer.py", "path_type": "hardlink", "sha256": "c1509c784d7e613eac3b504ebff25286ef9c045d7fa878f2f4aeb75329e7dd11", "sha256_in_prefix": "c1509c784d7e613eac3b504ebff25286ef9c045d7fa878f2f4aeb75329e7dd11", "size_in_bytes": 5700}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/pipeline_stable_diffusion.py", "path_type": "hardlink", "sha256": "d311a66c934b85052d5fae92fe8a9af3a201036e2150c70de5504ce680e24cc2", "sha256_in_prefix": "d311a66c934b85052d5fae92fe8a9af3a201036e2150c70de5504ce680e24cc2", "size_in_bytes": 33130}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/stable_diffusion/trt_utilities.py", "path_type": "hardlink", "sha256": "f021e0208c01ae2ab24748142d53c5235b85d6b6c01f8f7f88c3d4750bce9c02", "sha256_in_prefix": "f021e0208c01ae2ab24748142d53c5235b85d6b6c01f8f7f88c3d4750bce9c02", "size_in_bytes": 420}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/__init__.py", "path_type": "hardlink", "sha256": "07046f69491f612ac655dcf64fa8c28be707f2047d503deadc6aed32d9092fcc", "sha256_in_prefix": "07046f69491f612ac655dcf64fa8c28be707f2047d503deadc6aed32d9092fcc", "size_in_bytes": 483}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "12d2654f56f62a77b7a0fe730a61015eb9b9b08f74ff985be3887284a222ff5a", "sha256_in_prefix": "12d2654f56f62a77b7a0fe730a61015eb9b9b08f74ff985be3887284a222ff5a", "size_in_bytes": 764}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/__pycache__/convert_to_onnx.cpython-311.pyc", "path_type": "hardlink", "sha256": "45cc070b7748ec6be877f2054d70cae589ee3c00efee8372d64a5f1ead85a7dd", "sha256_in_prefix": "45cc070b7748ec6be877f2054d70cae589ee3c00efee8372d64a5f1ead85a7dd", "size_in_bytes": 11457}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/__pycache__/t5_decoder.cpython-311.pyc", "path_type": "hardlink", "sha256": "203cfdcfdf2fa77d7bac21a276f661a57a49d46e32c144d99ccf2687c9b74254", "sha256_in_prefix": "203cfdcfdf2fa77d7bac21a276f661a57a49d46e32c144d99ccf2687c9b74254", "size_in_bytes": 18143}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/__pycache__/t5_encoder.cpython-311.pyc", "path_type": "hardlink", "sha256": "0e8eb4fdc1a9d3c6b2cf190a227f46c900a284059ba3af948d0f941bf378dec2", "sha256_in_prefix": "0e8eb4fdc1a9d3c6b2cf190a227f46c900a284059ba3af948d0f941bf378dec2", "size_in_bytes": 3627}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/__pycache__/t5_encoder_decoder_init.cpython-311.pyc", "path_type": "hardlink", "sha256": "7bac772c77d43cd4c75195c0caf9f07f72b4a958473b1990145c4a73a79243db", "sha256_in_prefix": "7bac772c77d43cd4c75195c0caf9f07f72b4a958473b1990145c4a73a79243db", "size_in_bytes": 16360}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/__pycache__/t5_helper.cpython-311.pyc", "path_type": "hardlink", "sha256": "a76a3d744d74a19b0577d14ae9281da20f6ca04fff55d78385bf54af7dc88cc4", "sha256_in_prefix": "a76a3d744d74a19b0577d14ae9281da20f6ca04fff55d78385bf54af7dc88cc4", "size_in_bytes": 12984}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/convert_to_onnx.py", "path_type": "hardlink", "sha256": "5e4137f86b1c1424ea112e7cabff4ef30f59a44067f1ab8bb0ad7920f51b813a", "sha256_in_prefix": "5e4137f86b1c1424ea112e7cabff4ef30f59a44067f1ab8bb0ad7920f51b813a", "size_in_bytes": 10192}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/t5_decoder.py", "path_type": "hardlink", "sha256": "1bd3d552645993ba05b79377c6bd21218d259cb96fa9af959906c3a96da48477", "sha256_in_prefix": "1bd3d552645993ba05b79377c6bd21218d259cb96fa9af959906c3a96da48477", "size_in_bytes": 16751}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/t5_encoder.py", "path_type": "hardlink", "sha256": "aa1b2052dd60f47057c9229cb6ece6c2b7395bb206d65a68cd41c1dc48457a4e", "sha256_in_prefix": "aa1b2052dd60f47057c9229cb6ece6c2b7395bb206d65a68cd41c1dc48457a4e", "size_in_bytes": 2249}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/t5_encoder_decoder_init.py", "path_type": "hardlink", "sha256": "de20ee88d092fc02740aa5aa877a7c02db39939bd086a402e83aa43e5aaed72a", "sha256_in_prefix": "de20ee88d092fc02740aa5aa877a7c02db39939bd086a402e83aa43e5aaed72a", "size_in_bytes": 15060}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/t5/t5_helper.py", "path_type": "hardlink", "sha256": "9a3d9ba9d9d81efa906041f63b9e3d90b293b15d2743ca61949b22ec8ca28301", "sha256_in_prefix": "9a3d9ba9d9d81efa906041f63b9e3d90b293b15d2743ca61949b22ec8ca28301", "size_in_bytes": 12179}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__init__.py", "path_type": "hardlink", "sha256": "c74bfebb1533dfd0926b02c6d5107e26c761f63e57db09614b7289bb0be4dcf8", "sha256_in_prefix": "c74bfebb1533dfd0926b02c6d5107e26c761f63e57db09614b7289bb0be4dcf8", "size_in_bytes": 478}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "b1feb61673f9cb03f99872d3edef44cd31b605a0782402e9c1a6700c6677bbc9", "sha256_in_prefix": "b1feb61673f9cb03f99872d3edef44cd31b605a0782402e9c1a6700c6677bbc9", "size_in_bytes": 760}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/benchmark.cpython-311.pyc", "path_type": "hardlink", "sha256": "aff1f9e3e434c6c55c3e0a98416731d676792f364b315980999985aece4bb022", "sha256_in_prefix": "aff1f9e3e434c6c55c3e0a98416731d676792f364b315980999985aece4bb022", "size_in_bytes": 30718}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/benchmark_all.cpython-311.pyc", "path_type": "hardlink", "sha256": "5feb5eb3557c72e5802afc1c733b4aa5daa81e2102a24f0d8c15be1874c787ff", "sha256_in_prefix": "5feb5eb3557c72e5802afc1c733b4aa5daa81e2102a24f0d8c15be1874c787ff", "size_in_bytes": 19564}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/convert_to_onnx.cpython-311.pyc", "path_type": "hardlink", "sha256": "a87dfe2af501b2360901adba2e0b3579227644d3ad6c648ca95ac347418d2d5b", "sha256_in_prefix": "a87dfe2af501b2360901adba2e0b3579227644d3ad6c648ca95ac347418d2d5b", "size_in_bytes": 18473}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/whisper_chain.cpython-311.pyc", "path_type": "hardlink", "sha256": "514c2b3b33c6a8a7697e98d70a8d9ec22f38daa59d45d95ddf2ea0f42a09039d", "sha256_in_prefix": "514c2b3b33c6a8a7697e98d70a8d9ec22f38daa59d45d95ddf2ea0f42a09039d", "size_in_bytes": 15854}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/whisper_decoder.cpython-311.pyc", "path_type": "hardlink", "sha256": "399b583baaff3464130a4972bcf8fb4322790d0c730807e3aeb10f0cc6dc7e7e", "sha256_in_prefix": "399b583baaff3464130a4972bcf8fb4322790d0c730807e3aeb10f0cc6dc7e7e", "size_in_bytes": 21723}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/whisper_encoder.cpython-311.pyc", "path_type": "hardlink", "sha256": "32320ad8628ca92a0fe4d62da74521019faae4909cc63e92a91166ac5b152f14", "sha256_in_prefix": "32320ad8628ca92a0fe4d62da74521019faae4909cc63e92a91166ac5b152f14", "size_in_bytes": 7356}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/whisper_encoder_decoder_init.cpython-311.pyc", "path_type": "hardlink", "sha256": "23d2be63c6e2e44cd034c29a048ec041198493db3f285054adbdd80dddc741a9", "sha256_in_prefix": "23d2be63c6e2e44cd034c29a048ec041198493db3f285054adbdd80dddc741a9", "size_in_bytes": 18150}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/whisper_helper.cpython-311.pyc", "path_type": "hardlink", "sha256": "a39125ba8be1ca783f43d7e2831f452d755b366a1735bb5e803df327a211fac0", "sha256_in_prefix": "a39125ba8be1ca783f43d7e2831f452d755b366a1735bb5e803df327a211fac0", "size_in_bytes": 23056}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/whisper_inputs.cpython-311.pyc", "path_type": "hardlink", "sha256": "582928b8b691760410766d430516ee0d53902c339ce38cd4326c51f88a07305d", "sha256_in_prefix": "582928b8b691760410766d430516ee0d53902c339ce38cd4326c51f88a07305d", "size_in_bytes": 14875}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/__pycache__/whisper_jump_times.cpython-311.pyc", "path_type": "hardlink", "sha256": "080f865539c6bd968cefbbfa2001669b094eb2a804995912e99149c656a478d7", "sha256_in_prefix": "080f865539c6bd968cefbbfa2001669b094eb2a804995912e99149c656a478d7", "size_in_bytes": 18127}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/benchmark.py", "path_type": "hardlink", "sha256": "0008e582b1b1129a2442bb495e211a5bd8a3b2ae7e8f9923811dc08f0950abbe", "sha256_in_prefix": "0008e582b1b1129a2442bb495e211a5bd8a3b2ae7e8f9923811dc08f0950abbe", "size_in_bytes": 22746}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/benchmark_all.py", "path_type": "hardlink", "sha256": "2b6ca6e72f93afe3a8f31cdc4318ce7bd3d44e6b8216224bd68bf422ab9f5602", "sha256_in_prefix": "2b6ca6e72f93afe3a8f31cdc4318ce7bd3d44e6b8216224bd68bf422ab9f5602", "size_in_bytes": 18808}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/convert_to_onnx.py", "path_type": "hardlink", "sha256": "084d6d0268f0447e3f96e447ae536fe3b94ef6f7fc68e911d5c69dc8908353bc", "sha256_in_prefix": "084d6d0268f0447e3f96e447ae536fe3b94ef6f7fc68e911d5c69dc8908353bc", "size_in_bytes": 18395}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/whisper_chain.py", "path_type": "hardlink", "sha256": "b3ce675d4b6894cebaa35f60c290e10bebca2cd5a887a9cd20ae80ce78c62ddc", "sha256_in_prefix": "b3ce675d4b6894cebaa35f60c290e10bebca2cd5a887a9cd20ae80ce78c62ddc", "size_in_bytes": 14899}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/whisper_decoder.py", "path_type": "hardlink", "sha256": "260ec2b995d0410680e582698f72ba81ef16125814e8524d85648f38faf6137e", "sha256_in_prefix": "260ec2b995d0410680e582698f72ba81ef16125814e8524d85648f38faf6137e", "size_in_bytes": 21433}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/whisper_encoder.py", "path_type": "hardlink", "sha256": "a0795876a49b3fc4077e6856727749f45177616f97503d004446f47cb05dae92", "sha256_in_prefix": "a0795876a49b3fc4077e6856727749f45177616f97503d004446f47cb05dae92", "size_in_bytes": 6178}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/whisper_encoder_decoder_init.py", "path_type": "hardlink", "sha256": "70115dae257ce2d778e3ebf11c4cb737d025206860e80745aedfc5b4ee0e7728", "sha256_in_prefix": "70115dae257ce2d778e3ebf11c4cb737d025206860e80745aedfc5b4ee0e7728", "size_in_bytes": 16437}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/whisper_helper.py", "path_type": "hardlink", "sha256": "c50b2e2e96cfa39687928c716513970e042eb8937e9183948c118cfd5ae68ea1", "sha256_in_prefix": "c50b2e2e96cfa39687928c716513970e042eb8937e9183948c118cfd5ae68ea1", "size_in_bytes": 21376}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/whisper_inputs.py", "path_type": "hardlink", "sha256": "06629c906ad3217567b90042b96871de8b99b1465f6c700803890dda0eac679c", "sha256_in_prefix": "06629c906ad3217567b90042b96871de8b99b1465f6c700803890dda0eac679c", "size_in_bytes": 15670}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/models/whisper/whisper_jump_times.py", "path_type": "hardlink", "sha256": "72a9a2ed368175f8e5a6ca35c6aa4e1c748ae3b876485a5afd5c2eb94927e368", "sha256_in_prefix": "72a9a2ed368175f8e5a6ca35c6aa4e1c748ae3b876485a5afd5c2eb94927e368", "size_in_bytes": 19476}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/onnx_exporter.py", "path_type": "hardlink", "sha256": "abcf5378a98324fa54b057c425690539ffd45e0764035b7718bcb562da142c6c", "sha256_in_prefix": "abcf5378a98324fa54b057c425690539ffd45e0764035b7718bcb562da142c6c", "size_in_bytes": 24451}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model.py", "path_type": "hardlink", "sha256": "c90934078ce41466ae5166620f62c6a2feef006bcc60a6cd76489218413ace28", "sha256_in_prefix": "c90934078ce41466ae5166620f62c6a2feef006bcc60a6cd76489218413ace28", "size_in_bytes": 67817}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_bart.py", "path_type": "hardlink", "sha256": "27a3e965cb9547c24e14429bc88bae29ea6c48b51c355575b3b80bbef45cf1ff", "sha256_in_prefix": "27a3e965cb9547c24e14429bc88bae29ea6c48b51c355575b3b80bbef45cf1ff", "size_in_bytes": 5406}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_bert.py", "path_type": "hardlink", "sha256": "8df6d46d33f736edda0a015e3d87cd99c7a211088dd1140af32e6d2bc295a153", "sha256_in_prefix": "8df6d46d33f736edda0a015e3d87cd99c7a211088dd1140af32e6d2bc295a153", "size_in_bytes": 19914}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_bert_keras.py", "path_type": "hardlink", "sha256": "8d77db7a7110b6246238df74cd3b6aaa2101ecdecc283442a10011017b755b1c", "sha256_in_prefix": "8d77db7a7110b6246238df74cd3b6aaa2101ecdecc283442a10011017b755b1c", "size_in_bytes": 18536}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_bert_tf.py", "path_type": "hardlink", "sha256": "f705bc910c2399d15a84abf8053be718cd4716084bf1cb5c898587d150db5d61", "sha256_in_prefix": "f705bc910c2399d15a84abf8053be718cd4716084bf1cb5c898587d150db5d61", "size_in_bytes": 24915}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_clip.py", "path_type": "hardlink", "sha256": "515d246319365c25806ff26a55f55ad1dda90c86c5b21413db4ef7bbe08822e6", "sha256_in_prefix": "515d246319365c25806ff26a55f55ad1dda90c86c5b21413db4ef7bbe08822e6", "size_in_bytes": 1352}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_conformer.py", "path_type": "hardlink", "sha256": "f46d7e115655579c53d76f04193c87f4351255d8aa490efe5db3223884e0da51", "sha256_in_prefix": "f46d7e115655579c53d76f04193c87f4351255d8aa490efe5db3223884e0da51", "size_in_bytes": 1380}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_gpt2.py", "path_type": "hardlink", "sha256": "5c3187b27ebd767670bcfde48c6dea501e1028350b5867815e0fd8789dacb1a7", "sha256_in_prefix": "5c3187b27ebd767670bcfde48c6dea501e1028350b5867815e0fd8789dacb1a7", "size_in_bytes": 3812}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_mmdit.py", "path_type": "hardlink", "sha256": "51b5b3a8e3afdf19251c4bcca8b8492f7d2af6e373da7664a0ad822a08b39d68", "sha256_in_prefix": "51b5b3a8e3afdf19251c4bcca8b8492f7d2af6e373da7664a0ad822a08b39d68", "size_in_bytes": 4063}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_phi.py", "path_type": "hardlink", "sha256": "71b6a69eca1d56a7ae801baf7e8384c029df66a1c52ae42723804b9953216557", "sha256_in_prefix": "71b6a69eca1d56a7ae801baf7e8384c029df66a1c52ae42723804b9953216557", "size_in_bytes": 35410}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_sam2.py", "path_type": "hardlink", "sha256": "5184357546924129b9ac220fc5a58b1c71ae18cd9479fa9d83a3620e52594fc4", "sha256_in_prefix": "5184357546924129b9ac220fc5a58b1c71ae18cd9479fa9d83a3620e52594fc4", "size_in_bytes": 4810}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_t5.py", "path_type": "hardlink", "sha256": "5874dcab54ed077c8690e2c3b2ec942b1ed33b611dc570b3c36f71c249389a4d", "sha256_in_prefix": "5874dcab54ed077c8690e2c3b2ec942b1ed33b611dc570b3c36f71c249389a4d", "size_in_bytes": 37054}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_tnlr.py", "path_type": "hardlink", "sha256": "85f5a0a560e5aaf1284f6a415d58871f052c4318e8d4180705714e79496bd531", "sha256_in_prefix": "85f5a0a560e5aaf1284f6a415d58871f052c4318e8d4180705714e79496bd531", "size_in_bytes": 8179}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_unet.py", "path_type": "hardlink", "sha256": "a029955a3acb9af4802cdfaafad98a2fedffc9898695cdf000444a3c4c50019f", "sha256_in_prefix": "a029955a3acb9af4802cdfaafad98a2fedffc9898695cdf000444a3c4c50019f", "size_in_bytes": 9221}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/onnx_model_vae.py", "path_type": "hardlink", "sha256": "c4b612ec8c2572e0198ee101fdf5bbe7fffa88e79d5ea55820134bea953da000", "sha256_in_prefix": "c4b612ec8c2572e0198ee101fdf5bbe7fffa88e79d5ea55820134bea953da000", "size_in_bytes": 1471}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/onnx_utils.py", "path_type": "hardlink", "sha256": "a6169b72b28fad9719ce8bed5aa0180fcf3e2bdf16a7b16d6646234de9053f8a", "sha256_in_prefix": "a6169b72b28fad9719ce8bed5aa0180fcf3e2bdf16a7b16d6646234de9053f8a", "size_in_bytes": 2120}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/optimizer.py", "path_type": "hardlink", "sha256": "c2a7a51eb352f961cb19d7e7efeb3b9d2fe559a0e183acc5bd40b2b6eec92c52", "sha256_in_prefix": "c2a7a51eb352f961cb19d7e7efeb3b9d2fe559a0e183acc5bd40b2b6eec92c52", "size_in_bytes": 25062}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/past_helper.py", "path_type": "hardlink", "sha256": "53c32cb13c22b3af8f416ce0836ad14267a2a450b54fe86a3e77da1751b075bc", "sha256_in_prefix": "53c32cb13c22b3af8f416ce0836ad14267a2a450b54fe86a3e77da1751b075bc", "size_in_bytes": 6806}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/profile_result_processor.py", "path_type": "hardlink", "sha256": "c1c37bb949c9cc0e959bee463b2bd3d7e3b8ce8b57f91dc4bcdd98e5e7679fc6", "sha256_in_prefix": "c1c37bb949c9cc0e959bee463b2bd3d7e3b8ce8b57f91dc4bcdd98e5e7679fc6", "size_in_bytes": 12582}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/profiler.py", "path_type": "hardlink", "sha256": "c8255aa2aaa4dfb07481228bf11424a8d3b8619dc74cf44ce6a0f3b51ca9e107", "sha256_in_prefix": "c8255aa2aaa4dfb07481228bf11424a8d3b8619dc74cf44ce6a0f3b51ca9e107", "size_in_bytes": 13234}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/quantize_helper.py", "path_type": "hardlink", "sha256": "dc40fa47327fe7205f2d220261ee80ee8a2136315136ef9a1b7b6e77c171698d", "sha256_in_prefix": "dc40fa47327fe7205f2d220261ee80ee8a2136315136ef9a1b7b6e77c171698d", "size_in_bytes": 2817}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/shape_infer_helper.py", "path_type": "hardlink", "sha256": "19289e11b97704fa06325878f0bbb68ab09fc644f399eff2d574b4129492f6da", "sha256_in_prefix": "19289e11b97704fa06325878f0bbb68ab09fc644f399eff2d574b4129492f6da", "size_in_bytes": 4445}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/shape_optimizer.py", "path_type": "hardlink", "sha256": "036c6165952ddf9bce303818e5250b9662890a6d0d03ae9e09a1eefd87fc062e", "sha256_in_prefix": "036c6165952ddf9bce303818e5250b9662890a6d0d03ae9e09a1eefd87fc062e", "size_in_bytes": 15068}, {"_path": "lib/python3.11/site-packages/onnxruntime/transformers/torch_onnx_export_helper.py", "path_type": "hardlink", "sha256": "5eb72d91edbd5a62007a4fbfae6e00e0bba0903b45682acbd8ff1e2bbd9b5b88", "sha256_in_prefix": "5eb72d91edbd5a62007a4fbfae6e00e0bba0903b45682acbd8ff1e2bbd9b5b88", "size_in_bytes": 2501}], "paths_version": 1}, "requested_spec": "None", "sha256": "055c9603e2cefa336d98f2470f0e129f4b56aa2066739258b28d64972b5cc24c", "size": 12264339, "subdir": "osx-64", "timestamp": 1746976024000, "url": "https://conda.anaconda.org/conda-forge/osx-64/onnxruntime-1.22.0-py311hb982cef_0_cpu.conda", "version": "1.22.0"}