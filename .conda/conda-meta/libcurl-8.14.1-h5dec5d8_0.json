{"build": "h5dec5d8_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13", "krb5 >=1.21.3,<1.22.0a0", "libnghttp2 >=1.64.0,<2.0a0", "libssh2 >=1.11.1,<2.0a0", "libzlib >=1.3.1,<2.0a0", "openssl >=3.5.0,<4.0a0", "zstd >=1.5.7,<1.6.0a0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libcurl-8.14.1-h5dec5d8_0", "files": ["bin/curl-config", "include/curl/curl.h", "include/curl/curlver.h", "include/curl/easy.h", "include/curl/header.h", "include/curl/mprintf.h", "include/curl/multi.h", "include/curl/options.h", "include/curl/stdcheaders.h", "include/curl/system.h", "include/curl/typecheck-gcc.h", "include/curl/urlapi.h", "include/curl/websockets.h", "lib/libcurl.4.dylib", "lib/libcurl.dylib", "lib/pkgconfig/libcurl.pc"], "fn": "libcurl-8.14.1-h5dec5d8_0.conda", "license": "curl", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libcurl-8.14.1-h5dec5d8_0", "type": 1}, "md5": "8738cd19972c3599400404882ddfbc24", "name": "libcurl", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libcurl-8.14.1-h5dec5d8_0.conda", "paths_data": {"paths": [{"_path": "bin/curl-config", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/curl_split_recipe_1749032913818/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_p", "sha256": "d433364c576e2f068314485a2a778f77e094e02a42d3e6f07606516764653110", "sha256_in_prefix": "853211c86b7476b5a1985724dc6ddef2ba7170c12857d81c2efa4e2af2b96ba3", "size_in_bytes": 11663}, {"_path": "include/curl/curl.h", "path_type": "hardlink", "sha256": "f9abac576000aabb21d78f648ccdeeef588ac117df945bc239313c3172990787", "sha256_in_prefix": "f9abac576000aabb21d78f648ccdeeef588ac117df945bc239313c3172990787", "size_in_bytes": 134144}, {"_path": "include/curl/curlver.h", "path_type": "hardlink", "sha256": "842378b147984a36d942522519a78488c0af565bb28ec8c6a6beb3b5f4eb1cfd", "sha256_in_prefix": "842378b147984a36d942522519a78488c0af565bb28ec8c6a6beb3b5f4eb1cfd", "size_in_bytes": 3045}, {"_path": "include/curl/easy.h", "path_type": "hardlink", "sha256": "3a9a663e57fa4104ae479e513a41d99b069f735543d118c90f73c5b5b0f37291", "sha256_in_prefix": "3a9a663e57fa4104ae479e513a41d99b069f735543d118c90f73c5b5b0f37291", "size_in_bytes": 4012}, {"_path": "include/curl/header.h", "path_type": "hardlink", "sha256": "614be48a86f4e5d304c5aa40ef1c85245e25b97732921c3631840146669d992f", "sha256_in_prefix": "614be48a86f4e5d304c5aa40ef1c85245e25b97732921c3631840146669d992f", "size_in_bytes": 2910}, {"_path": "include/curl/mprintf.h", "path_type": "hardlink", "sha256": "5254b33e5e351298cdc25303381edc15889a41e129d41821bbd186dc2ddcbd40", "sha256_in_prefix": "5254b33e5e351298cdc25303381edc15889a41e129d41821bbd186dc2ddcbd40", "size_in_bytes": 3086}, {"_path": "include/curl/multi.h", "path_type": "hardlink", "sha256": "83ae673f7655768bf70b141c9cf845b09695aa801d4d1d56362c3928c38e397c", "sha256_in_prefix": "83ae673f7655768bf70b141c9cf845b09695aa801d4d1d56362c3928c38e397c", "size_in_bytes": 18235}, {"_path": "include/curl/options.h", "path_type": "hardlink", "sha256": "5716018d27e783283825bed2a8a051190487722fdeb64b7aa2d03a997e99b8d1", "sha256_in_prefix": "5716018d27e783283825bed2a8a051190487722fdeb64b7aa2d03a997e99b8d1", "size_in_bytes": 2401}, {"_path": "include/curl/stdcheaders.h", "path_type": "hardlink", "sha256": "d7588b86814a35ffc3766ff6242e6f6705e04401fc9c208a195caff3503af81c", "sha256_in_prefix": "d7588b86814a35ffc3766ff6242e6f6705e04401fc9c208a195caff3503af81c", "size_in_bytes": 1362}, {"_path": "include/curl/system.h", "path_type": "hardlink", "sha256": "ad9637bbe3988e08745b9a6edf53b9651b240cd15b60da60e54ac7c61439cc57", "sha256_in_prefix": "ad9637bbe3988e08745b9a6edf53b9651b240cd15b60da60e54ac7c61439cc57", "size_in_bytes": 17071}, {"_path": "include/curl/typecheck-gcc.h", "path_type": "hardlink", "sha256": "1fa0db62e42e1833028df4d90d1a45efb389c8a91f50dfa411098b8d7b392711", "sha256_in_prefix": "1fa0db62e42e1833028df4d90d1a45efb389c8a91f50dfa411098b8d7b392711", "size_in_bytes": 52355}, {"_path": "include/curl/urlapi.h", "path_type": "hardlink", "sha256": "4366e8eead1d92742c679b14dd3c65b92087226e1cebecc7803d619eded6a868", "sha256_in_prefix": "4366e8eead1d92742c679b14dd3c65b92087226e1cebecc7803d619eded6a868", "size_in_bytes": 5802}, {"_path": "include/curl/websockets.h", "path_type": "hardlink", "sha256": "e5e4883b6de1a62d28cbfad75b45e4a428e160871715ebc34dc696b7c38efb48", "sha256_in_prefix": "e5e4883b6de1a62d28cbfad75b45e4a428e160871715ebc34dc696b7c38efb48", "size_in_bytes": 2776}, {"_path": "lib/libcurl.4.dylib", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/curl_split_recipe_1749032913818/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_p", "sha256": "193f9cef414f38c68dc79e61da12ff13f5f34e8a6075e20c92487c043f2a7e45", "sha256_in_prefix": "1d151526eabfce0616326cd75c6dfcf20a25fd09e492347c8dc01bd922df8b7e", "size_in_bytes": 908944}, {"_path": "lib/libcurl.dylib", "path_type": "softlink", "sha256": "193f9cef414f38c68dc79e61da12ff13f5f34e8a6075e20c92487c043f2a7e45", "size_in_bytes": 908944}, {"_path": "lib/pkgconfig/libcurl.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/curl_split_recipe_1749032913818/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_p", "sha256": "f5de8f022136c78dbbc41f4a9aa90c292fa055eb9d11c29e6fd1e88057de5c07", "sha256_in_prefix": "ea35b73f109921980057ad73b502ed78d1d4938dd11841a23aea29e9894b43b9", "size_in_bytes": 3515}], "paths_version": 1}, "requested_spec": "None", "sha256": "ca0d8d12056227d6b47122cfb6d68fc5a3a0c6ab75a0e908542954fc5f84506c", "size": 424040, "subdir": "osx-64", "timestamp": 1749033558000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libcurl-8.14.1-h5dec5d8_0.conda", "version": "8.14.1"}