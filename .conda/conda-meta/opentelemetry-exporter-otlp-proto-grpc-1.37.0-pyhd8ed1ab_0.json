{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["deprecated >=1.2.6", "googleapis-common-protos ~=1.57", "grpcio <2.0.0,>=1.66.2", "opentelemetry-api ~=1.15", "opentelemetry-exporter-otlp-proto-common 1.37.0", "opentelemetry-proto 1.37.0", "opentelemetry-sdk ~=1.37.0", "python >=3.10"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-exporter-otlp-proto-grpc-1.37.0-pyhd8ed1ab_0", "files": ["lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/__init__.py", "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/_log_exporter/__init__.py", "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/exporter.py", "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/metric_exporter/__init__.py", "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/py.typed", "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/trace_exporter/__init__.py", "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/version/__init__.py", "lib/python3.11/site-packages/opentelemetry_exporter_otlp_proto_grpc-1.37.0.dist-info/INSTALLER", "lib/python3.11/site-packages/opentelemetry_exporter_otlp_proto_grpc-1.37.0.dist-info/METADATA", "lib/python3.11/site-packages/opentelemetry_exporter_otlp_proto_grpc-1.37.0.dist-info/RECORD", "lib/python3.11/site-packages/opentelemetry_exporter_otlp_proto_grpc-1.37.0.dist-info/REQUESTED", "lib/python3.11/site-packages/opentelemetry_exporter_otlp_proto_grpc-1.37.0.dist-info/WHEEL", "lib/python3.11/site-packages/opentelemetry_exporter_otlp_proto_grpc-1.37.0.dist-info/direct_url.json", "lib/python3.11/site-packages/opentelemetry_exporter_otlp_proto_grpc-1.37.0.dist-info/entry_points.txt", "lib/python3.11/site-packages/opentelemetry_exporter_otlp_proto_grpc-1.37.0.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/_log_exporter/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/__pycache__/exporter.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/metric_exporter/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/trace_exporter/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/version/__pycache__/__init__.cpython-311.pyc"], "fn": "opentelemetry-exporter-otlp-proto-grpc-1.37.0-pyhd8ed1ab_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-exporter-otlp-proto-grpc-1.37.0-pyhd8ed1ab_0", "type": 1}, "md5": "46a5a9c6ad82105d173aadc6739ec38c", "name": "opentelemetry-exporter-otlp-proto-grpc", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-exporter-otlp-proto-grpc-1.37.0-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/opentelemetry/exporter/otlp/proto/grpc/__init__.py", "path_type": "hardlink", "sha256": "076d73c7aa85d5560741cae02f897478aac55d8e5f40e68b484fd0ed87f97861", "sha256_in_prefix": "076d73c7aa85d5560741cae02f897478aac55d8e5f40e68b484fd0ed87f97861", "size_in_bytes": 2696}, {"_path": "site-packages/opentelemetry/exporter/otlp/proto/grpc/_log_exporter/__init__.py", "path_type": "hardlink", "sha256": "04b75030f40976e0819d7058e9a229cce5d2c6449e9df500c2a6e7b88b263f56", "sha256_in_prefix": "04b75030f40976e0819d7058e9a229cce5d2c6449e9df500c2a6e7b88b263f56", "size_in_bytes": 4581}, {"_path": "site-packages/opentelemetry/exporter/otlp/proto/grpc/exporter.py", "path_type": "hardlink", "sha256": "387562a02280c3c6b7c901b9830a6818a2f9d8cf8bd0436cafa644486f68a8a7", "sha256_in_prefix": "387562a02280c3c6b7c901b9830a6818a2f9d8cf8bd0436cafa644486f68a8a7", "size_in_bytes": 14050}, {"_path": "site-packages/opentelemetry/exporter/otlp/proto/grpc/metric_exporter/__init__.py", "path_type": "hardlink", "sha256": "c9316ccd6778a06830dd868711820aa6ab14b303091e409e728ec6362635c3ad", "sha256_in_prefix": "c9316ccd6778a06830dd868711820aa6ab14b303091e409e728ec6362635c3ad", "size_in_bytes": 10139}, {"_path": "site-packages/opentelemetry/exporter/otlp/proto/grpc/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/exporter/otlp/proto/grpc/trace_exporter/__init__.py", "path_type": "hardlink", "sha256": "f7d59fc485a1ea8506721b80a32c6ce4e210d89b72a4901275c7bf43af22f3a0", "sha256_in_prefix": "f7d59fc485a1ea8506721b80a32c6ce4e210d89b72a4901275c7bf43af22f3a0", "size_in_bytes": 5366}, {"_path": "site-packages/opentelemetry/exporter/otlp/proto/grpc/version/__init__.py", "path_type": "hardlink", "sha256": "420f83b16cfb888dea77dc3e0d220cfd48ae5a7b8b63a49afd9ea2c88e9d3518", "sha256_in_prefix": "420f83b16cfb888dea77dc3e0d220cfd48ae5a7b8b63a49afd9ea2c88e9d3518", "size_in_bytes": 608}, {"_path": "site-packages/opentelemetry_exporter_otlp_proto_grpc-1.37.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/opentelemetry_exporter_otlp_proto_grpc-1.37.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "1428e9c23c47e9f6fe25d17d35dc64c67fcd869474777529aa80afe5144f45d9", "sha256_in_prefix": "1428e9c23c47e9f6fe25d17d35dc64c67fcd869474777529aa80afe5144f45d9", "size_in_bytes": 2392}, {"_path": "site-packages/opentelemetry_exporter_otlp_proto_grpc-1.37.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "db09c8829587dc5ae0d2adfaf91cc8388e68fa9bf7c50e72fe100d43bdffc635", "sha256_in_prefix": "db09c8829587dc5ae0d2adfaf91cc8388e68fa9bf7c50e72fe100d43bdffc635", "size_in_bytes": 2264}, {"_path": "site-packages/opentelemetry_exporter_otlp_proto_grpc-1.37.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry_exporter_otlp_proto_grpc-1.37.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "sha256_in_prefix": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "size_in_bytes": 87}, {"_path": "site-packages/opentelemetry_exporter_otlp_proto_grpc-1.37.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "f4fefd92b5a1d9502ac99e5ae8f1ae011cde614018439ce3a5001d310df3bc5d", "sha256_in_prefix": "f4fefd92b5a1d9502ac99e5ae8f1ae011cde614018439ce3a5001d310df3bc5d", "size_in_bytes": 134}, {"_path": "site-packages/opentelemetry_exporter_otlp_proto_grpc-1.37.0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "9caf37c6686c7781f43fb406ada53060256d33d72740404bf89411f382482ff9", "sha256_in_prefix": "9caf37c6686c7781f43fb406ada53060256d33d72740404bf89411f382482ff9", "size_in_bytes": 365}, {"_path": "site-packages/opentelemetry_exporter_otlp_proto_grpc-1.37.0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "sha256_in_prefix": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "size_in_bytes": 11357}, {"_path": "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/_log_exporter/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/__pycache__/exporter.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/metric_exporter/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/trace_exporter/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/exporter/otlp/proto/grpc/version/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "f9a7967ca3ac054a647b7f5358b1a58f202c7c74e5dfa3028d625f82a41722ce", "size": 20414, "subdir": "noarch", "timestamp": 1757778800000, "url": "https://conda.anaconda.org/conda-forge/noarch/opentelemetry-exporter-otlp-proto-grpc-1.37.0-pyhd8ed1ab_0.conda", "version": "1.37.0"}