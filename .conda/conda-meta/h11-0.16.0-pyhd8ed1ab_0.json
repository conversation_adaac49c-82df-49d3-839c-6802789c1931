{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.9", "typing_extensions"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/h11-0.16.0-pyhd8ed1ab_0", "files": ["lib/python3.11/site-packages/h11-0.16.0.dist-info/INSTALLER", "lib/python3.11/site-packages/h11-0.16.0.dist-info/METADATA", "lib/python3.11/site-packages/h11-0.16.0.dist-info/RECORD", "lib/python3.11/site-packages/h11-0.16.0.dist-info/REQUESTED", "lib/python3.11/site-packages/h11-0.16.0.dist-info/WHEEL", "lib/python3.11/site-packages/h11-0.16.0.dist-info/direct_url.json", "lib/python3.11/site-packages/h11-0.16.0.dist-info/licenses/LICENSE.txt", "lib/python3.11/site-packages/h11-0.16.0.dist-info/top_level.txt", "lib/python3.11/site-packages/h11/__init__.py", "lib/python3.11/site-packages/h11/_abnf.py", "lib/python3.11/site-packages/h11/_connection.py", "lib/python3.11/site-packages/h11/_events.py", "lib/python3.11/site-packages/h11/_headers.py", "lib/python3.11/site-packages/h11/_readers.py", "lib/python3.11/site-packages/h11/_receivebuffer.py", "lib/python3.11/site-packages/h11/_state.py", "lib/python3.11/site-packages/h11/_util.py", "lib/python3.11/site-packages/h11/_version.py", "lib/python3.11/site-packages/h11/_writers.py", "lib/python3.11/site-packages/h11/py.typed", "lib/python3.11/site-packages/h11/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/h11/__pycache__/_abnf.cpython-311.pyc", "lib/python3.11/site-packages/h11/__pycache__/_connection.cpython-311.pyc", "lib/python3.11/site-packages/h11/__pycache__/_events.cpython-311.pyc", "lib/python3.11/site-packages/h11/__pycache__/_headers.cpython-311.pyc", "lib/python3.11/site-packages/h11/__pycache__/_readers.cpython-311.pyc", "lib/python3.11/site-packages/h11/__pycache__/_receivebuffer.cpython-311.pyc", "lib/python3.11/site-packages/h11/__pycache__/_state.cpython-311.pyc", "lib/python3.11/site-packages/h11/__pycache__/_util.cpython-311.pyc", "lib/python3.11/site-packages/h11/__pycache__/_version.cpython-311.pyc", "lib/python3.11/site-packages/h11/__pycache__/_writers.cpython-311.pyc"], "fn": "h11-0.16.0-pyhd8ed1ab_0.conda", "license": "MIT", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/h11-0.16.0-pyhd8ed1ab_0", "type": 1}, "md5": "4b69232755285701bc86a5afe4d9933a", "name": "h11", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/h11-0.16.0-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/h11-0.16.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/h11-0.16.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "28f326098ac09fcba79b8f180f96087c841fe2456215cb7b872a9c7d5cd19d64", "sha256_in_prefix": "28f326098ac09fcba79b8f180f96087c841fe2456215cb7b872a9c7d5cd19d64", "size_in_bytes": 8348}, {"_path": "site-packages/h11-0.16.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "2d99ca330d08fd2cd72ebb9e9f12b4e9bebb50f4e4a572a5619dbb715d148be2", "sha256_in_prefix": "2d99ca330d08fd2cd72ebb9e9f12b4e9bebb50f4e4a572a5619dbb715d148be2", "size_in_bytes": 1996}, {"_path": "site-packages/h11-0.16.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/h11-0.16.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "4a63b1614ee9ccd281a804af409ec38d7dd719417dda5ac684c6f747afe28aa2", "sha256_in_prefix": "4a63b1614ee9ccd281a804af409ec38d7dd719417dda5ac684c6f747afe28aa2", "size_in_bytes": 91}, {"_path": "site-packages/h11-0.16.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "4e976ceaac8efbe81d7387e36de20682b18dd992feb66a36056e74d55376399a", "sha256_in_prefix": "4e976ceaac8efbe81d7387e36de20682b18dd992feb66a36056e74d55376399a", "size_in_bytes": 99}, {"_path": "site-packages/h11-0.16.0.dist-info/licenses/LICENSE.txt", "path_type": "hardlink", "sha256": "37db5bb85926db28a427a25867f10b1232003aea1be69ccb851138adb8e6f361", "sha256_in_prefix": "37db5bb85926db28a427a25867f10b1232003aea1be69ccb851138adb8e6f361", "size_in_bytes": 1124}, {"_path": "site-packages/h11-0.16.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "17b742e23977cde87c4c61c43da589acc6deba859b4b7efd1b0762f98bdd722b", "sha256_in_prefix": "17b742e23977cde87c4c61c43da589acc6deba859b4b7efd1b0762f98bdd722b", "size_in_bytes": 4}, {"_path": "site-packages/h11/__init__.py", "path_type": "hardlink", "sha256": "88ed4ace448ee36c99e9f7e0f953206f1fd9553586518d349d16045a7facde46", "sha256_in_prefix": "88ed4ace448ee36c99e9f7e0f953206f1fd9553586518d349d16045a7facde46", "size_in_bytes": 1507}, {"_path": "site-packages/h11/_abnf.py", "path_type": "hardlink", "sha256": "c9b8b1af4c6cba99e403a18503232e6ee5c5e9373595bfe117cf743600ac7cd7", "sha256_in_prefix": "c9b8b1af4c6cba99e403a18503232e6ee5c5e9373595bfe117cf743600ac7cd7", "size_in_bytes": 4815}, {"_path": "site-packages/h11/_connection.py", "path_type": "hardlink", "sha256": "93d61155fea4a19a9bb6d056dfac5259a26959d6706bec50554f401c4a3d0ee2", "sha256_in_prefix": "93d61155fea4a19a9bb6d056dfac5259a26959d6706bec50554f401c4a3d0ee2", "size_in_bytes": 26863}, {"_path": "site-packages/h11/_events.py", "path_type": "hardlink", "sha256": "23deda5e86a5d56bbb7642f9e3c04034150290e21b7bec790a2a1803d201635e", "sha256_in_prefix": "23deda5e86a5d56bbb7642f9e3c04034150290e21b7bec790a2a1803d201635e", "size_in_bytes": 11792}, {"_path": "site-packages/h11/_headers.py", "path_type": "hardlink", "sha256": "3fb0fe9413711f074b64f2e29a6630acf1bef59923125bef25925d6408123fee", "sha256_in_prefix": "3fb0fe9413711f074b64f2e29a6630acf1bef59923125bef25925d6408123fee", "size_in_bytes": 10412}, {"_path": "site-packages/h11/_readers.py", "path_type": "hardlink", "sha256": "6b8472a4e454082dddd2afe4c4fb8120cee34c3f222ede57f754c7d05b1db8de", "sha256_in_prefix": "6b8472a4e454082dddd2afe4c4fb8120cee34c3f222ede57f754c7d05b1db8de", "size_in_bytes": 8590}, {"_path": "site-packages/h11/_receivebuffer.py", "path_type": "hardlink", "sha256": "c6bb29b1db0d81617145f41c4d7c51f11add8d15d74cad08a39710616a49d56b", "sha256_in_prefix": "c6bb29b1db0d81617145f41c4d7c51f11add8d15d74cad08a39710616a49d56b", "size_in_bytes": 5252}, {"_path": "site-packages/h11/_state.py", "path_type": "hardlink", "sha256": "ff92c6fc1191f0509c1507813c7f93307826ffe07e1147160a74287fff578c51", "sha256_in_prefix": "ff92c6fc1191f0509c1507813c7f93307826ffe07e1147160a74287fff578c51", "size_in_bytes": 13231}, {"_path": "site-packages/h11/_util.py", "path_type": "hardlink", "sha256": "2d69248d7c89685940cba2eddfdc3bdd44ad925153e68bdcbe8d13918ed162e9", "sha256_in_prefix": "2d69248d7c89685940cba2eddfdc3bdd44ad925153e68bdcbe8d13918ed162e9", "size_in_bytes": 4888}, {"_path": "site-packages/h11/_version.py", "path_type": "hardlink", "sha256": "1954ac6cf48f0dc3ae17aa6d7c88979d52686849b7ca05db327aa5aff1a26a1c", "sha256_in_prefix": "1954ac6cf48f0dc3ae17aa6d7c88979d52686849b7ca05db327aa5aff1a26a1c", "size_in_bytes": 686}, {"_path": "site-packages/h11/_writers.py", "path_type": "hardlink", "sha256": "a052a6e8fb637877db8f844b5fb541eca0dcd60218e778171b7fc747d96d9930", "sha256_in_prefix": "a052a6e8fb637877db8f844b5fb541eca0dcd60218e778171b7fc747d96d9930", "size_in_bytes": 5081}, {"_path": "site-packages/h11/py.typed", "path_type": "hardlink", "sha256": "b28c3db284f03fd4ff80401049587b19bf3ce79874e0dc2686cd967be2518193", "sha256_in_prefix": "b28c3db284f03fd4ff80401049587b19bf3ce79874e0dc2686cd967be2518193", "size_in_bytes": 7}, {"_path": "lib/python3.11/site-packages/h11/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/h11/__pycache__/_abnf.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/h11/__pycache__/_connection.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/h11/__pycache__/_events.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/h11/__pycache__/_headers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/h11/__pycache__/_readers.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/h11/__pycache__/_receivebuffer.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/h11/__pycache__/_state.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/h11/__pycache__/_util.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/h11/__pycache__/_version.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/h11/__pycache__/_writers.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "f64b68148c478c3bfc8f8d519541de7d2616bf59d44485a5271041d40c061887", "size": 37697, "subdir": "noarch", "timestamp": 1745526482000, "url": "https://conda.anaconda.org/conda-forge/noarch/h11-0.16.0-pyhd8ed1ab_0.conda", "version": "0.16.0"}