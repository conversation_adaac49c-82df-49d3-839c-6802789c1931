{"build": "hac325c4_1005", "build_number": 1005, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13", "libcxx >=17"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/gflags-2.2.2-hac325c4_1005", "files": ["bin/gflags_completions.sh", "include/gflags/gflags.h", "include/gflags/gflags_completions.h", "include/gflags/gflags_declare.h", "include/gflags/gflags_gflags.h", "lib/cmake/gflags/gflags-config-version.cmake", "lib/cmake/gflags/gflags-config.cmake", "lib/cmake/gflags/gflags-nonamespace-targets-release.cmake", "lib/cmake/gflags/gflags-nonamespace-targets.cmake", "lib/cmake/gflags/gflags-targets-release.cmake", "lib/cmake/gflags/gflags-targets.cmake", "lib/libgflags.2.2.2.dylib", "lib/libgflags.2.2.dylib", "lib/libgflags.dylib", "lib/libgflags_nothreads.2.2.2.dylib", "lib/libgflags_nothreads.2.2.dylib", "lib/libgflags_nothreads.dylib", "lib/pkgconfig/gflags.pc"], "fn": "gflags-2.2.2-hac325c4_1005.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/gflags-2.2.2-hac325c4_1005", "type": 1}, "md5": "a26de8814083a6971f14f9c8c3cb36c2", "name": "gflags", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/gflags-2.2.2-hac325c4_1005.conda", "paths_data": {"paths": [{"_path": "bin/gflags_completions.sh", "path_type": "hardlink", "sha256": "cc8a0aae240363a0f2561f5e664ee0e96e8f04dee152f9750583ee4344606ace", "sha256_in_prefix": "cc8a0aae240363a0f2561f5e664ee0e96e8f04dee152f9750583ee4344606ace", "size_in_bytes": 5126}, {"_path": "include/gflags/gflags.h", "path_type": "hardlink", "sha256": "5b900a9619a185210dcc6a9c7d74851933bf66a6d11f28f7861d62a683a35b90", "sha256_in_prefix": "5b900a9619a185210dcc6a9c7d74851933bf66a6d11f28f7861d62a683a35b90", "size_in_bytes": 30616}, {"_path": "include/gflags/gflags_completions.h", "path_type": "hardlink", "sha256": "824f96e57d0099e654834786347aa44f5d01ca42820380502cb654dc5e233988", "sha256_in_prefix": "824f96e57d0099e654834786347aa44f5d01ca42820380502cb654dc5e233988", "size_in_bytes": 5761}, {"_path": "include/gflags/gflags_declare.h", "path_type": "hardlink", "sha256": "d101c69d005e42b832b85cb5f66acaee96f5c02e0fb131c6962e5edf8a039a86", "sha256_in_prefix": "d101c69d005e42b832b85cb5f66acaee96f5c02e0fb131c6962e5edf8a039a86", "size_in_bytes": 5366}, {"_path": "include/gflags/gflags_gflags.h", "path_type": "hardlink", "sha256": "6040845c7dd3b491c9f9ea39d7ef0ce38144206558ba95fee21b91205d3599fa", "sha256_in_prefix": "6040845c7dd3b491c9f9ea39d7ef0ce38144206558ba95fee21b91205d3599fa", "size_in_bytes": 4071}, {"_path": "lib/cmake/gflags/gflags-config-version.cmake", "path_type": "hardlink", "sha256": "58c530f798bff1705904c473e8f3da13a6c2668b8768f33e5c480f81258d6106", "sha256_in_prefix": "58c530f798bff1705904c473e8f3da13a6c2668b8768f33e5c480f81258d6106", "size_in_bytes": 668}, {"_path": "lib/cmake/gflags/gflags-config.cmake", "path_type": "hardlink", "sha256": "f333eb064f5c494814ddf87be4a795a85eed1a7046cb1740e3fcbe144707e945", "sha256_in_prefix": "f333eb064f5c494814ddf87be4a795a85eed1a7046cb1740e3fcbe144707e945", "size_in_bytes": 6264}, {"_path": "lib/cmake/gflags/gflags-nonamespace-targets-release.cmake", "path_type": "hardlink", "sha256": "d66234a4bf2fbbd88794cda78a5a459769cdb56b05b040c11e078a6caa85596c", "sha256_in_prefix": "d66234a4bf2fbbd88794cda78a5a459769cdb56b05b040c11e078a6caa85596c", "size_in_bytes": 1428}, {"_path": "lib/cmake/gflags/gflags-nonamespace-targets.cmake", "path_type": "hardlink", "sha256": "2f6f3e0ed29e4b9adce8ce19299dde25645d12a6a005b9a5df06a8bf9371474e", "sha256_in_prefix": "2f6f3e0ed29e4b9adce8ce19299dde25645d12a6a005b9a5df06a8bf9371474e", "size_in_bytes": 4476}, {"_path": "lib/cmake/gflags/gflags-targets-release.cmake", "path_type": "hardlink", "sha256": "d0e10fe3b55a2e917e6c9b233c444689548962429403586089978d1b33d85a22", "sha256_in_prefix": "d0e10fe3b55a2e917e6c9b233c444689548962429403586089978d1b33d85a22", "size_in_bytes": 1508}, {"_path": "lib/cmake/gflags/gflags-targets.cmake", "path_type": "hardlink", "sha256": "3a3d48bca21543041d65cae3edeb87625e715d1e021a278d9c6478fdc3745bbf", "sha256_in_prefix": "3a3d48bca21543041d65cae3edeb87625e715d1e021a278d9c6478fdc3745bbf", "size_in_bytes": 4528}, {"_path": "lib/libgflags.2.2.2.dylib", "path_type": "hardlink", "sha256": "9f21cc5f76cc207b904cd3faef7f59e44bf84a1b1d869ff6fb2c4cc2f03fe391", "sha256_in_prefix": "9f21cc5f76cc207b904cd3faef7f59e44bf84a1b1d869ff6fb2c4cc2f03fe391", "size_in_bytes": 130688}, {"_path": "lib/libgflags.2.2.dylib", "path_type": "softlink", "sha256": "9f21cc5f76cc207b904cd3faef7f59e44bf84a1b1d869ff6fb2c4cc2f03fe391", "size_in_bytes": 130688}, {"_path": "lib/libgflags.dylib", "path_type": "softlink", "sha256": "9f21cc5f76cc207b904cd3faef7f59e44bf84a1b1d869ff6fb2c4cc2f03fe391", "size_in_bytes": 130688}, {"_path": "lib/libgflags_nothreads.2.2.2.dylib", "path_type": "hardlink", "sha256": "3428acb903809ec8d2f7a4f9a3cdbafeea1ceb1aa9d14065b540bd92bf660c7c", "sha256_in_prefix": "3428acb903809ec8d2f7a4f9a3cdbafeea1ceb1aa9d14065b540bd92bf660c7c", "size_in_bytes": 129504}, {"_path": "lib/libgflags_nothreads.2.2.dylib", "path_type": "softlink", "sha256": "3428acb903809ec8d2f7a4f9a3cdbafeea1ceb1aa9d14065b540bd92bf660c7c", "size_in_bytes": 129504}, {"_path": "lib/libgflags_nothreads.dylib", "path_type": "softlink", "sha256": "3428acb903809ec8d2f7a4f9a3cdbafeea1ceb1aa9d14065b540bd92bf660c7c", "size_in_bytes": 129504}, {"_path": "lib/pkgconfig/gflags.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/gflags_1726599791710/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_pl", "sha256": "a3fde7dc2552dd97039b9f4bd57862c79d5d7505065ee60c528b96aec757a6a6", "sha256_in_prefix": "e239f4fa2cd332d8d99e03d3f8136b9923ae5b982afadd781ef0d876e64ba466", "size_in_bytes": 583}], "paths_version": 1}, "requested_spec": "None", "sha256": "c0bea66f71a6f4baa8d4f0248e17f65033d558d9e882c0af571b38bcca3e4b46", "size": 84946, "subdir": "osx-64", "timestamp": 1726600054000, "url": "https://conda.anaconda.org/conda-forge/osx-64/gflags-2.2.2-hac325c4_1005.conda", "version": "2.2.2"}