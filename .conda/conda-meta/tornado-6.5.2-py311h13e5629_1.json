{"build": "py311h13e5629_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13", "python >=3.11,<3.12.0a0", "python_abi 3.11.* *_cp311"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/tornado-6.5.2-py311h13e5629_1", "files": ["lib/python3.11/site-packages/tornado-6.5.2.dist-info/INSTALLER", "lib/python3.11/site-packages/tornado-6.5.2.dist-info/METADATA", "lib/python3.11/site-packages/tornado-6.5.2.dist-info/RECORD", "lib/python3.11/site-packages/tornado-6.5.2.dist-info/REQUESTED", "lib/python3.11/site-packages/tornado-6.5.2.dist-info/WHEEL", "lib/python3.11/site-packages/tornado-6.5.2.dist-info/direct_url.json", "lib/python3.11/site-packages/tornado-6.5.2.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/tornado-6.5.2.dist-info/top_level.txt", "lib/python3.11/site-packages/tornado/__init__.py", "lib/python3.11/site-packages/tornado/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/_locale_data.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/auth.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/autoreload.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/concurrent.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/curl_httpclient.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/escape.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/gen.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/http1connection.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/httpclient.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/httpserver.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/httputil.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/ioloop.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/iostream.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/locale.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/locks.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/log.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/netutil.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/options.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/process.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/queues.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/routing.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/simple_httpclient.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/tcpclient.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/tcpserver.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/template.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/testing.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/util.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/web.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/websocket.cpython-311.pyc", "lib/python3.11/site-packages/tornado/__pycache__/wsgi.cpython-311.pyc", "lib/python3.11/site-packages/tornado/_locale_data.py", "lib/python3.11/site-packages/tornado/auth.py", "lib/python3.11/site-packages/tornado/autoreload.py", "lib/python3.11/site-packages/tornado/concurrent.py", "lib/python3.11/site-packages/tornado/curl_httpclient.py", "lib/python3.11/site-packages/tornado/escape.py", "lib/python3.11/site-packages/tornado/gen.py", "lib/python3.11/site-packages/tornado/http1connection.py", "lib/python3.11/site-packages/tornado/httpclient.py", "lib/python3.11/site-packages/tornado/httpserver.py", "lib/python3.11/site-packages/tornado/httputil.py", "lib/python3.11/site-packages/tornado/ioloop.py", "lib/python3.11/site-packages/tornado/iostream.py", "lib/python3.11/site-packages/tornado/locale.py", "lib/python3.11/site-packages/tornado/locks.py", "lib/python3.11/site-packages/tornado/log.py", "lib/python3.11/site-packages/tornado/netutil.py", "lib/python3.11/site-packages/tornado/options.py", "lib/python3.11/site-packages/tornado/platform/__init__.py", "lib/python3.11/site-packages/tornado/platform/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/tornado/platform/__pycache__/asyncio.cpython-311.pyc", "lib/python3.11/site-packages/tornado/platform/__pycache__/caresresolver.cpython-311.pyc", "lib/python3.11/site-packages/tornado/platform/__pycache__/twisted.cpython-311.pyc", "lib/python3.11/site-packages/tornado/platform/asyncio.py", "lib/python3.11/site-packages/tornado/platform/caresresolver.py", "lib/python3.11/site-packages/tornado/platform/twisted.py", "lib/python3.11/site-packages/tornado/process.py", "lib/python3.11/site-packages/tornado/py.typed", "lib/python3.11/site-packages/tornado/queues.py", "lib/python3.11/site-packages/tornado/routing.py", "lib/python3.11/site-packages/tornado/simple_httpclient.py", "lib/python3.11/site-packages/tornado/speedups.abi3.so", "lib/python3.11/site-packages/tornado/tcpclient.py", "lib/python3.11/site-packages/tornado/tcpserver.py", "lib/python3.11/site-packages/tornado/template.py", "lib/python3.11/site-packages/tornado/test/__init__.py", "lib/python3.11/site-packages/tornado/test/__main__.py", "lib/python3.11/site-packages/tornado/test/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/asyncio_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/auth_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/autoreload_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/circlerefs_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/concurrent_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/curl_httpclient_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/escape_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/gen_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/http1connection_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/httpclient_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/httpserver_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/httputil_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/import_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/ioloop_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/iostream_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/locale_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/locks_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/log_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/netutil_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/options_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/process_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/queues_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/resolve_test_helper.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/routing_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/runtests.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/simple_httpclient_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/tcpclient_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/tcpserver_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/template_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/testing_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/twisted_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/util.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/util_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/web_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/websocket_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/__pycache__/wsgi_test.cpython-311.pyc", "lib/python3.11/site-packages/tornado/test/asyncio_test.py", "lib/python3.11/site-packages/tornado/test/auth_test.py", "lib/python3.11/site-packages/tornado/test/autoreload_test.py", "lib/python3.11/site-packages/tornado/test/circlerefs_test.py", "lib/python3.11/site-packages/tornado/test/concurrent_test.py", "lib/python3.11/site-packages/tornado/test/csv_translations/fr_FR.csv", "lib/python3.11/site-packages/tornado/test/curl_httpclient_test.py", "lib/python3.11/site-packages/tornado/test/escape_test.py", "lib/python3.11/site-packages/tornado/test/gen_test.py", "lib/python3.11/site-packages/tornado/test/gettext_translations/fr_FR/LC_MESSAGES/tornado_test.mo", "lib/python3.11/site-packages/tornado/test/gettext_translations/fr_FR/LC_MESSAGES/tornado_test.po", "lib/python3.11/site-packages/tornado/test/http1connection_test.py", "lib/python3.11/site-packages/tornado/test/httpclient_test.py", "lib/python3.11/site-packages/tornado/test/httpserver_test.py", "lib/python3.11/site-packages/tornado/test/httputil_test.py", "lib/python3.11/site-packages/tornado/test/import_test.py", "lib/python3.11/site-packages/tornado/test/ioloop_test.py", "lib/python3.11/site-packages/tornado/test/iostream_test.py", "lib/python3.11/site-packages/tornado/test/locale_test.py", "lib/python3.11/site-packages/tornado/test/locks_test.py", "lib/python3.11/site-packages/tornado/test/log_test.py", "lib/python3.11/site-packages/tornado/test/netutil_test.py", "lib/python3.11/site-packages/tornado/test/options_test.cfg", "lib/python3.11/site-packages/tornado/test/options_test.py", "lib/python3.11/site-packages/tornado/test/options_test_types.cfg", "lib/python3.11/site-packages/tornado/test/options_test_types_str.cfg", "lib/python3.11/site-packages/tornado/test/process_test.py", "lib/python3.11/site-packages/tornado/test/queues_test.py", "lib/python3.11/site-packages/tornado/test/resolve_test_helper.py", "lib/python3.11/site-packages/tornado/test/routing_test.py", "lib/python3.11/site-packages/tornado/test/runtests.py", "lib/python3.11/site-packages/tornado/test/simple_httpclient_test.py", "lib/python3.11/site-packages/tornado/test/static/dir/index.html", "lib/python3.11/site-packages/tornado/test/static/robots.txt", "lib/python3.11/site-packages/tornado/test/static/sample.xml", "lib/python3.11/site-packages/tornado/test/static/sample.xml.bz2", "lib/python3.11/site-packages/tornado/test/static/sample.xml.gz", "lib/python3.11/site-packages/tornado/test/static_foo.txt", "lib/python3.11/site-packages/tornado/test/tcpclient_test.py", "lib/python3.11/site-packages/tornado/test/tcpserver_test.py", "lib/python3.11/site-packages/tornado/test/template_test.py", "lib/python3.11/site-packages/tornado/test/templates/utf8.html", "lib/python3.11/site-packages/tornado/test/test.crt", "lib/python3.11/site-packages/tornado/test/test.key", "lib/python3.11/site-packages/tornado/test/testing_test.py", "lib/python3.11/site-packages/tornado/test/twisted_test.py", "lib/python3.11/site-packages/tornado/test/util.py", "lib/python3.11/site-packages/tornado/test/util_test.py", "lib/python3.11/site-packages/tornado/test/web_test.py", "lib/python3.11/site-packages/tornado/test/websocket_test.py", "lib/python3.11/site-packages/tornado/test/wsgi_test.py", "lib/python3.11/site-packages/tornado/testing.py", "lib/python3.11/site-packages/tornado/util.py", "lib/python3.11/site-packages/tornado/web.py", "lib/python3.11/site-packages/tornado/websocket.py", "lib/python3.11/site-packages/tornado/wsgi.py"], "fn": "tornado-6.5.2-py311h13e5629_1.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/tornado-6.5.2-py311h13e5629_1", "type": 1}, "md5": "06fd6a712ee16b3e7998e8ee2e7fc8b1", "name": "tornado", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/tornado-6.5.2-py311h13e5629_1.conda", "paths_data": {"paths": [{"_path": "lib/python3.11/site-packages/tornado-6.5.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.11/site-packages/tornado-6.5.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "14b4c80f94800aa19fb2a9768e4ce0f30cb8dcc66a95c26ce3faf7da54290aeb", "sha256_in_prefix": "14b4c80f94800aa19fb2a9768e4ce0f30cb8dcc66a95c26ce3faf7da54290aeb", "size_in_bytes": 2770}, {"_path": "lib/python3.11/site-packages/tornado-6.5.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "b9df1371089636d9f32b84812cc07bd0aab03aa2097833ca0b3de42c7888ecf4", "sha256_in_prefix": "b9df1371089636d9f32b84812cc07bd0aab03aa2097833ca0b3de42c7888ecf4", "size_in_bytes": 12152}, {"_path": "lib/python3.11/site-packages/tornado-6.5.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/tornado-6.5.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1df1f89becc3545bab4610dff61103a298fb9c8c272fd5eac7eecd37d3263ddd", "sha256_in_prefix": "1df1f89becc3545bab4610dff61103a298fb9c8c272fd5eac7eecd37d3263ddd", "size_in_bytes": 109}, {"_path": "lib/python3.11/site-packages/tornado-6.5.2.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "47b91c7f53f69a95d3a5452218f6234b9ca3eb0b2fcbafe0ce2990cd72f04d66", "sha256_in_prefix": "47b91c7f53f69a95d3a5452218f6234b9ca3eb0b2fcbafe0ce2990cd72f04d66", "size_in_bytes": 95}, {"_path": "lib/python3.11/site-packages/tornado-6.5.2.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "sha256_in_prefix": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30", "size_in_bytes": 11358}, {"_path": "lib/python3.11/site-packages/tornado-6.5.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "e5000ad4c78da5681876a5a853c898943b8607c8fa3433e0c7eb925074ded00e", "sha256_in_prefix": "e5000ad4c78da5681876a5a853c898943b8607c8fa3433e0c7eb925074ded00e", "size_in_bytes": 8}, {"_path": "lib/python3.11/site-packages/tornado/__init__.py", "path_type": "hardlink", "sha256": "43307a02e36203b5651324e52e0cabda8a294f50dbefab0ecf953992d99cb4c4", "sha256_in_prefix": "43307a02e36203b5651324e52e0cabda8a294f50dbefab0ecf953992d99cb4c4", "size_in_bytes": 1761}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "5bb3f47293f23a3851fd4475912c5ea3a499a7b78559c724f010d06c4aee6f6b", "sha256_in_prefix": "5bb3f47293f23a3851fd4475912c5ea3a499a7b78559c724f010d06c4aee6f6b", "size_in_bytes": 1112}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/_locale_data.cpython-311.pyc", "path_type": "hardlink", "sha256": "e7dfd7c76b52174fc0179399a37f08450a1e67fd6aae0c84549dee42a5837409", "sha256_in_prefix": "e7dfd7c76b52174fc0179399a37f08450a1e67fd6aae0c84549dee42a5837409", "size_in_bytes": 4224}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/auth.cpython-311.pyc", "path_type": "hardlink", "sha256": "ec4152e9d5597921948d60d121b8c2a78546bdc59ca380d5c1554da543430940", "sha256_in_prefix": "ec4152e9d5597921948d60d121b8c2a78546bdc59ca380d5c1554da543430940", "size_in_bytes": 59997}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/autoreload.cpython-311.pyc", "path_type": "hardlink", "sha256": "65557635374c2ecef9c93af0548f5166e997ac0f74cf738db0dbd7c76f3a7cb4", "sha256_in_prefix": "65557635374c2ecef9c93af0548f5166e997ac0f74cf738db0dbd7c76f3a7cb4", "size_in_bytes": 12487}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/concurrent.cpython-311.pyc", "path_type": "hardlink", "sha256": "229b61cd6ad84dce396052c15cc0cfb35deeb0c1d11bcb5ea72d4b087d01c080", "sha256_in_prefix": "229b61cd6ad84dce396052c15cc0cfb35deeb0c1d11bcb5ea72d4b087d01c080", "size_in_bytes": 11494}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/curl_httpclient.cpython-311.pyc", "path_type": "hardlink", "sha256": "541b3a6851b2b2ad854b103df2f7123874fa49f407df9e535c2a2c2fb0082d94", "sha256_in_prefix": "541b3a6851b2b2ad854b103df2f7123874fa49f407df9e535c2a2c2fb0082d94", "size_in_bytes": 29306}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/escape.cpython-311.pyc", "path_type": "hardlink", "sha256": "56b22c03e601d6aad6d5541503211e272a9cea7141d3b4c618a3b9a0b3e31bce", "sha256_in_prefix": "56b22c03e601d6aad6d5541503211e272a9cea7141d3b4c618a3b9a0b3e31bce", "size_in_bytes": 17071}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/gen.cpython-311.pyc", "path_type": "hardlink", "sha256": "f917480c5ca35bda95b70eab51dc4375a2c3713432b9a7308d349ecdaec530ad", "sha256_in_prefix": "f917480c5ca35bda95b70eab51dc4375a2c3713432b9a7308d349ecdaec530ad", "size_in_bytes": 38553}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/http1connection.cpython-311.pyc", "path_type": "hardlink", "sha256": "8122e3b0cedb1dd77b2ab2c2b58fe74aaaa45f124dee91f50dd562bbc15f5ec9", "sha256_in_prefix": "8122e3b0cedb1dd77b2ab2c2b58fe74aaaa45f124dee91f50dd562bbc15f5ec9", "size_in_bytes": 43851}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/httpclient.cpython-311.pyc", "path_type": "hardlink", "sha256": "3d32275291558f891b20c4683006fe9fdc25a9b6bf776e6470a2cf6ea1f9d29b", "sha256_in_prefix": "3d32275291558f891b20c4683006fe9fdc25a9b6bf776e6470a2cf6ea1f9d29b", "size_in_bytes": 36986}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/httpserver.cpython-311.pyc", "path_type": "hardlink", "sha256": "a2f9b82f8d94cb2268f4afbce1a9221f448cf485bac9332c7a1a9e14031580a5", "sha256_in_prefix": "a2f9b82f8d94cb2268f4afbce1a9221f448cf485bac9332c7a1a9e14031580a5", "size_in_bytes": 19674}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/httputil.cpython-311.pyc", "path_type": "hardlink", "sha256": "c510a04656e871ecf146be8415ad5a232a0d2af9bd268a4a09ce2510640768b9", "sha256_in_prefix": "c510a04656e871ecf146be8415ad5a232a0d2af9bd268a4a09ce2510640768b9", "size_in_bytes": 54378}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/ioloop.cpython-311.pyc", "path_type": "hardlink", "sha256": "7087e760500a8b4582822cb5d591d425461c2f1238639d1cc40e7c8d401c74e3", "sha256_in_prefix": "7087e760500a8b4582822cb5d591d425461c2f1238639d1cc40e7c8d401c74e3", "size_in_bytes": 43179}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/iostream.cpython-311.pyc", "path_type": "hardlink", "sha256": "4feedb24f5ba1a7443f4548e058db1ee4f2a61d922dae82b28695c749bc572e8", "sha256_in_prefix": "4feedb24f5ba1a7443f4548e058db1ee4f2a61d922dae82b28695c749bc572e8", "size_in_bytes": 70137}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/locale.cpython-311.pyc", "path_type": "hardlink", "sha256": "ab06114bc5e2bc9e26c37e0458f00ef97b60a95fa342bc4c74bc7517452dece4", "sha256_in_prefix": "ab06114bc5e2bc9e26c37e0458f00ef97b60a95fa342bc4c74bc7517452dece4", "size_in_bytes": 26278}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/locks.cpython-311.pyc", "path_type": "hardlink", "sha256": "116528453ab1427a3f1caf8d863deda0860fb2d8604174dcf54e3a658da40f73", "sha256_in_prefix": "116528453ab1427a3f1caf8d863deda0860fb2d8604174dcf54e3a658da40f73", "size_in_bytes": 25245}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/log.cpython-311.pyc", "path_type": "hardlink", "sha256": "fe461ee3baeb8c75fbc5356314ec2863cd3fbab3b5efd67e6ccbd9288dc01647", "sha256_in_prefix": "fe461ee3baeb8c75fbc5356314ec2863cd3fbab3b5efd67e6ccbd9288dc01647", "size_in_bytes": 13209}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/netutil.cpython-311.pyc", "path_type": "hardlink", "sha256": "0ecc943cb4f55dc8ffd00cf261313ed63c045a18db1e2e6a1262fce2cbd2c652", "sha256_in_prefix": "0ecc943cb4f55dc8ffd00cf261313ed63c045a18db1e2e6a1262fce2cbd2c652", "size_in_bytes": 28109}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/options.cpython-311.pyc", "path_type": "hardlink", "sha256": "be5bd69c28cf0c29447706d19e2f3cf55ef3e6a21a50cd274842face72090847", "sha256_in_prefix": "be5bd69c28cf0c29447706d19e2f3cf55ef3e6a21a50cd274842face72090847", "size_in_bytes": 34728}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/process.cpython-311.pyc", "path_type": "hardlink", "sha256": "47d39fe3c1b1ce34709e8a52f90cbe4342e5463bd484f0716a7f9810d1ac4920", "sha256_in_prefix": "47d39fe3c1b1ce34709e8a52f90cbe4342e5463bd484f0716a7f9810d1ac4920", "size_in_bytes": 15752}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/queues.cpython-311.pyc", "path_type": "hardlink", "sha256": "aa0ea14cb63121fc2630010df9ea37176f9ce23fe2d692c2c6dd22bd471be911", "sha256_in_prefix": "aa0ea14cb63121fc2630010df9ea37176f9ce23fe2d692c2c6dd22bd471be911", "size_in_bytes": 19719}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/routing.cpython-311.pyc", "path_type": "hardlink", "sha256": "eb56fb32c91943e45cf361dfc14b349c084fa8e3fe074427f195dd91957e5364", "sha256_in_prefix": "eb56fb32c91943e45cf361dfc14b349c084fa8e3fe074427f195dd91957e5364", "size_in_bytes": 33943}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/simple_httpclient.cpython-311.pyc", "path_type": "hardlink", "sha256": "cdb331b71d2667686d2fdc6f04e8b5c375850b92a26dcff3bab3e1b7777d0bd4", "sha256_in_prefix": "cdb331b71d2667686d2fdc6f04e8b5c375850b92a26dcff3bab3e1b7777d0bd4", "size_in_bytes": 33780}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/tcpclient.cpython-311.pyc", "path_type": "hardlink", "sha256": "1f7a9905b515a9e9faa27cc49ed51899560e563ca0f5614fc011451a543a043a", "sha256_in_prefix": "1f7a9905b515a9e9faa27cc49ed51899560e563ca0f5614fc011451a543a043a", "size_in_bytes": 15526}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/tcpserver.cpython-311.pyc", "path_type": "hardlink", "sha256": "8868ada4f39ffc32ac3fe8bab162c24f2b22dc7bb2de29d00d8375f7ee715bf1", "sha256_in_prefix": "8868ada4f39ffc32ac3fe8bab162c24f2b22dc7bb2de29d00d8375f7ee715bf1", "size_in_bytes": 16095}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/template.cpython-311.pyc", "path_type": "hardlink", "sha256": "0de66484647ea0580faedf03bedb949633beee7ec99514bf87b194c6861a6e55", "sha256_in_prefix": "0de66484647ea0580faedf03bedb949633beee7ec99514bf87b194c6861a6e55", "size_in_bytes": 55170}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/testing.cpython-311.pyc", "path_type": "hardlink", "sha256": "a346ea24761f696fc80ec784f1bd1c41a08d2a627bcb0c1f34a5d5b9a8a1ff82", "sha256_in_prefix": "a346ea24761f696fc80ec784f1bd1c41a08d2a627bcb0c1f34a5d5b9a8a1ff82", "size_in_bytes": 39710}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/util.cpython-311.pyc", "path_type": "hardlink", "sha256": "9d97970c542cb66c87c20c37f705727f05f7ee3cfecb50ab77abfa39a9d39c8f", "sha256_in_prefix": "9d97970c542cb66c87c20c37f705727f05f7ee3cfecb50ab77abfa39a9d39c8f", "size_in_bytes": 19488}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/web.cpython-311.pyc", "path_type": "hardlink", "sha256": "868a9edd551e604965b0fc0891edd1b8014e86133bc07906eedd62c4e7261ef5", "sha256_in_prefix": "868a9edd551e604965b0fc0891edd1b8014e86133bc07906eedd62c4e7261ef5", "size_in_bytes": 179764}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/websocket.cpython-311.pyc", "path_type": "hardlink", "sha256": "3f029a0bd1846d5b096fad52f0aa10aaa4a151b5f46d5228c54d8fc8679189fc", "sha256_in_prefix": "3f029a0bd1846d5b096fad52f0aa10aaa4a151b5f46d5228c54d8fc8679189fc", "size_in_bytes": 82102}, {"_path": "lib/python3.11/site-packages/tornado/__pycache__/wsgi.cpython-311.pyc", "path_type": "hardlink", "sha256": "c32316731f44c7ce52db501c4f765db9df72f7d079c2f3af8dabd7ab890d8e50", "sha256_in_prefix": "c32316731f44c7ce52db501c4f765db9df72f7d079c2f3af8dabd7ab890d8e50", "size_in_bytes": 13361}, {"_path": "lib/python3.11/site-packages/tornado/_locale_data.py", "path_type": "hardlink", "sha256": "00ef19b59ef955e66c891a62adb1fe14dbf3dce1434311907179aa4f6f6f696a", "sha256_in_prefix": "00ef19b59ef955e66c891a62adb1fe14dbf3dce1434311907179aa4f6f6f696a", "size_in_bytes": 4503}, {"_path": "lib/python3.11/site-packages/tornado/auth.py", "path_type": "hardlink", "sha256": "3d0d2a2f77d6dd39dab9601a206ee7a6b38a54f32f40a36abc210dd21cce7eab", "sha256_in_prefix": "3d0d2a2f77d6dd39dab9601a206ee7a6b38a54f32f40a36abc210dd21cce7eab", "size_in_bytes": 48955}, {"_path": "lib/python3.11/site-packages/tornado/autoreload.py", "path_type": "hardlink", "sha256": "518dbd41fd20065fe3c2b4b74261a1ccb62f9fe983f4a389e1aed7c4cf4f2b7d", "sha256_in_prefix": "518dbd41fd20065fe3c2b4b74261a1ccb62f9fe983f4a389e1aed7c4cf4f2b7d", "size_in_bytes": 13136}, {"_path": "lib/python3.11/site-packages/tornado/concurrent.py", "path_type": "hardlink", "sha256": "9f468edcb2c46d8ac1e275d4be63507317a786511f2ec1bb237f76d4974cfd68", "sha256_in_prefix": "9f468edcb2c46d8ac1e275d4be63507317a786511f2ec1bb237f76d4974cfd68", "size_in_bytes": 8376}, {"_path": "lib/python3.11/site-packages/tornado/curl_httpclient.py", "path_type": "hardlink", "sha256": "7789805663cc1f936df329732b90f4464332e99d03db292d5f53cfc5ae267b8a", "sha256_in_prefix": "7789805663cc1f936df329732b90f4464332e99d03db292d5f53cfc5ae267b8a", "size_in_bytes": 24904}, {"_path": "lib/python3.11/site-packages/tornado/escape.py", "path_type": "hardlink", "sha256": "d02b7247b091207e62551ab9792ce1ab90bf1b815036937b231a87221b7211bd", "sha256_in_prefix": "d02b7247b091207e62551ab9792ce1ab90bf1b815036937b231a87221b7211bd", "size_in_bytes": 14221}, {"_path": "lib/python3.11/site-packages/tornado/gen.py", "path_type": "hardlink", "sha256": "668a395d29a04ea27ed933ab2afa00e435785b107227f2e148809822aa32691d", "sha256_in_prefix": "668a395d29a04ea27ed933ab2afa00e435785b107227f2e148809822aa32691d", "size_in_bytes": 31763}, {"_path": "lib/python3.11/site-packages/tornado/http1connection.py", "path_type": "hardlink", "sha256": "79ff6f7cf522e08b431062b2e79b28ee71825041d7dec370b71929973cb5dbd7", "sha256_in_prefix": "79ff6f7cf522e08b431062b2e79b28ee71825041d7dec370b71929973cb5dbd7", "size_in_bytes": 37815}, {"_path": "lib/python3.11/site-packages/tornado/httpclient.py", "path_type": "hardlink", "sha256": "ee94ea067ea14c4b718c31eb1b64ccdc0a1ad10f3426a46289278693ae54507b", "sha256_in_prefix": "ee94ea067ea14c4b718c31eb1b64ccdc0a1ad10f3426a46289278693ae54507b", "size_in_bytes": 31843}, {"_path": "lib/python3.11/site-packages/tornado/httpserver.py", "path_type": "hardlink", "sha256": "223c05ed18b0d6788cf9fcea82612c1314cfe5fae4ac6787e0c609c11ddf7f82", "sha256_in_prefix": "223c05ed18b0d6788cf9fcea82612c1314cfe5fae4ac6787e0c609c11ddf7f82", "size_in_bytes": 16131}, {"_path": "lib/python3.11/site-packages/tornado/httputil.py", "path_type": "hardlink", "sha256": "72f8c7397b611e82dd6e50cfbd85893de296ceaeb6759c9f0f7a633cdf243489", "sha256_in_prefix": "72f8c7397b611e82dd6e50cfbd85893de296ceaeb6759c9f0f7a633cdf243489", "size_in_bytes": 43629}, {"_path": "lib/python3.11/site-packages/tornado/ioloop.py", "path_type": "hardlink", "sha256": "b761399b3a6e5a89ec0fa3c562612d1dd173e85ccae9771bd6f948c707c8af5d", "sha256_in_prefix": "b761399b3a6e5a89ec0fa3c562612d1dd173e85ccae9771bd6f948c707c8af5d", "size_in_bytes": 37421}, {"_path": "lib/python3.11/site-packages/tornado/iostream.py", "path_type": "hardlink", "sha256": "d3f4d4035c535be8ab247d1a3fb6be16b210e99ad1eeb07f5bf1f1c288fbef54", "sha256_in_prefix": "d3f4d4035c535be8ab247d1a3fb6be16b210e99ad1eeb07f5bf1f1c288fbef54", "size_in_bytes": 63873}, {"_path": "lib/python3.11/site-packages/tornado/locale.py", "path_type": "hardlink", "sha256": "95a7f71cb3cf7b02ed96fff2099d368a0d44b1642205da98de5cec1b35c3a4f6", "sha256_in_prefix": "95a7f71cb3cf7b02ed96fff2099d368a0d44b1642205da98de5cec1b35c3a4f6", "size_in_bytes": 21122}, {"_path": "lib/python3.11/site-packages/tornado/locks.py", "path_type": "hardlink", "sha256": "3bff7b17678f84864c252e48ecdb5d4176f3657535a0facb2196c4507aeead14", "sha256_in_prefix": "3bff7b17678f84864c252e48ecdb5d4176f3657535a0facb2196c4507aeead14", "size_in_bytes": 17260}, {"_path": "lib/python3.11/site-packages/tornado/log.py", "path_type": "hardlink", "sha256": "4628fbcbf780652f7426ae11021a8a2027bc10096ba6c16d93c9e02b4db09c57", "sha256_in_prefix": "4628fbcbf780652f7426ae11021a8a2027bc10096ba6c16d93c9e02b4db09c57", "size_in_bytes": 12547}, {"_path": "lib/python3.11/site-packages/tornado/netutil.py", "path_type": "hardlink", "sha256": "454b6dc55c4c02b6b4457747a015821ac523773d3a087a7e6a11f5b754d3c88e", "sha256_in_prefix": "454b6dc55c4c02b6b4457747a015821ac523773d3a087a7e6a11f5b754d3c88e", "size_in_bytes": 25077}, {"_path": "lib/python3.11/site-packages/tornado/options.py", "path_type": "hardlink", "sha256": "efcc6111329a331c2b26899495338e8e995d8f41244c22483ec10d42c31f8bce", "sha256_in_prefix": "efcc6111329a331c2b26899495338e8e995d8f41244c22483ec10d42c31f8bce", "size_in_bytes": 25860}, {"_path": "lib/python3.11/site-packages/tornado/platform/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/tornado/platform/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "a441f8212f82f85f5ab60cd48c0d086666b5b4e3cf1fd73fd3bfdc7426ffd386", "sha256_in_prefix": "a441f8212f82f85f5ab60cd48c0d086666b5b4e3cf1fd73fd3bfdc7426ffd386", "size_in_bytes": 166}, {"_path": "lib/python3.11/site-packages/tornado/platform/__pycache__/asyncio.cpython-311.pyc", "path_type": "hardlink", "sha256": "142c07c60753f7d52a1ec072546150b7a3c2b9136afb9d583409f8dfc4cecca8", "sha256_in_prefix": "142c07c60753f7d52a1ec072546150b7a3c2b9136afb9d583409f8dfc4cecca8", "size_in_bytes": 34722}, {"_path": "lib/python3.11/site-packages/tornado/platform/__pycache__/caresresolver.cpython-311.pyc", "path_type": "hardlink", "sha256": "7f30aaf1fd6f0b2af3f3a2c71b2f8bf311f3be693ea58b8d39e28a8ebc0fb732", "sha256_in_prefix": "7f30aaf1fd6f0b2af3f3a2c71b2f8bf311f3be693ea58b8d39e28a8ebc0fb732", "size_in_bytes": 5288}, {"_path": "lib/python3.11/site-packages/tornado/platform/__pycache__/twisted.cpython-311.pyc", "path_type": "hardlink", "sha256": "9c57ad6fdf4b713039d91992d940a0eabce99a210d84e5b5e27b19b4edb76437", "sha256_in_prefix": "9c57ad6fdf4b713039d91992d940a0eabce99a210d84e5b5e27b19b4edb76437", "size_in_bytes": 2504}, {"_path": "lib/python3.11/site-packages/tornado/platform/asyncio.py", "path_type": "hardlink", "sha256": "7aecee672fca4a9d79e740a58bf12860799a566d7ec2a3727244853afa5bd937", "sha256_in_prefix": "7aecee672fca4a9d79e740a58bf12860799a566d7ec2a3727244853afa5bd937", "size_in_bytes": 28136}, {"_path": "lib/python3.11/site-packages/tornado/platform/caresresolver.py", "path_type": "hardlink", "sha256": "d8dab15b5b43bccf4a4afd4e7227f2649b3edd9012ebc0f5101c269516eae361", "sha256_in_prefix": "d8dab15b5b43bccf4a4afd4e7227f2649b3edd9012ebc0f5101c269516eae361", "size_in_bytes": 3500}, {"_path": "lib/python3.11/site-packages/tornado/platform/twisted.py", "path_type": "hardlink", "sha256": "1d963a456760368719f5b75ea3c5ea1a110c2e233b8e1ed7f63872e24c8b388d", "sha256_in_prefix": "1d963a456760368719f5b75ea3c5ea1a110c2e233b8e1ed7f63872e24c8b388d", "size_in_bytes": 2158}, {"_path": "lib/python3.11/site-packages/tornado/process.py", "path_type": "hardlink", "sha256": "322b70e100372b9f051addd383c18b515c3043d23d1d07c1ede381a1cd4442e8", "sha256_in_prefix": "322b70e100372b9f051addd383c18b515c3043d23d1d07c1ede381a1cd4442e8", "size_in_bytes": 12696}, {"_path": "lib/python3.11/site-packages/tornado/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/tornado/queues.py", "path_type": "hardlink", "sha256": "224d6f5d6c7e444d77d106bc64bed7a95194f1c7743670cc8ee0cd170a4fcd57", "sha256_in_prefix": "224d6f5d6c7e444d77d106bc64bed7a95194f1c7743670cc8ee0cd170a4fcd57", "size_in_bytes": 12513}, {"_path": "lib/python3.11/site-packages/tornado/routing.py", "path_type": "hardlink", "sha256": "d3e416dfff923cd84ddb3e0717f2ee730bcfdd35ad27c6833fa1bb255e8b09ce", "sha256_in_prefix": "d3e416dfff923cd84ddb3e0717f2ee730bcfdd35ad27c6833fa1bb255e8b09ce", "size_in_bytes": 25140}, {"_path": "lib/python3.11/site-packages/tornado/simple_httpclient.py", "path_type": "hardlink", "sha256": "153448e9fb407687346c39844d7e3f15eb7d8e82f2c6f96a80230bc58d75a2d2", "sha256_in_prefix": "153448e9fb407687346c39844d7e3f15eb7d8e82f2c6f96a80230bc58d75a2d2", "size_in_bytes": 27747}, {"_path": "lib/python3.11/site-packages/tornado/speedups.abi3.so", "path_type": "hardlink", "sha256": "2087b9fd9852741b1058be4b4194d0307253b4ddb3bfa1cf25aa65ac54441f8b", "sha256_in_prefix": "2087b9fd9852741b1058be4b4194d0307253b4ddb3bfa1cf25aa65ac54441f8b", "size_in_bytes": 8920}, {"_path": "lib/python3.11/site-packages/tornado/tcpclient.py", "path_type": "hardlink", "sha256": "043b36fb74bd8f02c9ca4f6dafa8a3a9d4e603db5ecdbbf6ab2ad8a1b3ed76e8", "sha256_in_prefix": "043b36fb74bd8f02c9ca4f6dafa8a3a9d4e603db5ecdbbf6ab2ad8a1b3ed76e8", "size_in_bytes": 12126}, {"_path": "lib/python3.11/site-packages/tornado/tcpserver.py", "path_type": "hardlink", "sha256": "3fd16787209c7b3a91c67474d0ca6c28dd643d64f93e61930072501b62c12089", "sha256_in_prefix": "3fd16787209c7b3a91c67474d0ca6c28dd643d64f93e61930072501b62c12089", "size_in_bytes": 15006}, {"_path": "lib/python3.11/site-packages/tornado/template.py", "path_type": "hardlink", "sha256": "db920c86bc7d7629045a8592b3ee538d6c3ae39a82c41e8ea1a6ebdb7acb9c3d", "sha256_in_prefix": "db920c86bc7d7629045a8592b3ee538d6c3ae39a82c41e8ea1a6ebdb7acb9c3d", "size_in_bytes": 37670}, {"_path": "lib/python3.11/site-packages/tornado/test/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/tornado/test/__main__.py", "path_type": "hardlink", "sha256": "332da24cb658f116425839bf143f25bf01146104f1bb3c766714a069fa879caf", "sha256_in_prefix": "332da24cb658f116425839bf143f25bf01146104f1bb3c766714a069fa879caf", "size_in_bytes": 303}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "2e6afa6b5f1514cab252939cb5b685564e8ccef78b2c2217666bb586e2281189", "sha256_in_prefix": "2e6afa6b5f1514cab252939cb5b685564e8ccef78b2c2217666bb586e2281189", "size_in_bytes": 162}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "446a9fde30037aa7c1229653880c91bfa79f5081e9ddddef43b0d08961a52524", "sha256_in_prefix": "446a9fde30037aa7c1229653880c91bfa79f5081e9ddddef43b0d08961a52524", "size_in_bytes": 351}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/asyncio_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "f2f89119a94c7eb6ceea9d0f6525f5d5c5898c7ba89cebd525ec02a46e939d78", "sha256_in_prefix": "f2f89119a94c7eb6ceea9d0f6525f5d5c5898c7ba89cebd525ec02a46e939d78", "size_in_bytes": 19748}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/auth_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "48602c4efde0aa030ce9128ab280bb1512a6fd4a56f73ea4cc2ac781e138d321", "sha256_in_prefix": "48602c4efde0aa030ce9128ab280bb1512a6fd4a56f73ea4cc2ac781e138d321", "size_in_bytes": 37734}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/autoreload_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "ef27fb89aeaeb58b0f51b21a9901faddd152478874128e6fbe17a0c3b6672bfd", "sha256_in_prefix": "ef27fb89aeaeb58b0f51b21a9901faddd152478874128e6fbe17a0c3b6672bfd", "size_in_bytes": 10151}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/circlerefs_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "c30f5e81a255f36dfcb263433dcb3e1e9012225b5d6c19eec3451c2407191203", "sha256_in_prefix": "c30f5e81a255f36dfcb263433dcb3e1e9012225b5d6c19eec3451c2407191203", "size_in_bytes": 13719}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/concurrent_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "280e28022a86eade04bf0fabc6517b4c850e68f0db123a2baf166e3c7d075b64", "sha256_in_prefix": "280e28022a86eade04bf0fabc6517b4c850e68f0db123a2baf166e3c7d075b64", "size_in_bytes": 15445}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/curl_httpclient_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "c1a3e843d364882198e2ad59e7d3bad3bc25b0a12dbc183074502e365a6f1d42", "sha256_in_prefix": "c1a3e843d364882198e2ad59e7d3bad3bc25b0a12dbc183074502e365a6f1d42", "size_in_bytes": 8113}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/escape_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "83b0491cd8bf16d51ed7e5d226e8e6aee5d11a031821f5c1d14ae2c15edc6777", "sha256_in_prefix": "83b0491cd8bf16d51ed7e5d226e8e6aee5d11a031821f5c1d14ae2c15edc6777", "size_in_bytes": 13956}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/gen_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "2e8c9e5c7f1d551e2d21d8d2081b531a4834edcdd379cc8b2b10da3cdf673317", "sha256_in_prefix": "2e8c9e5c7f1d551e2d21d8d2081b531a4834edcdd379cc8b2b10da3cdf673317", "size_in_bytes": 73985}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/http1connection_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "0057373bbf10ffd90a8a5c6335a0c899ed5e32dc72e63b82935c8cad5a3f72d1", "sha256_in_prefix": "0057373bbf10ffd90a8a5c6335a0c899ed5e32dc72e63b82935c8cad5a3f72d1", "size_in_bytes": 4897}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/httpclient_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "70b07465b09adebe05854434720bc3e5fdac4958b636505eb7bb283236216150", "sha256_in_prefix": "70b07465b09adebe05854434720bc3e5fdac4958b636505eb7bb283236216150", "size_in_bytes": 62153}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/httpserver_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "366ea004934166834fc80818703d0b7f5785126cf25a1f8a1f1742337cc1a25a", "sha256_in_prefix": "366ea004934166834fc80818703d0b7f5785126cf25a1f8a1f1742337cc1a25a", "size_in_bytes": 111189}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/httputil_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "7ffe872dbf922838881d98bc1557db8af53dd7d1f06adf070daeaeaa42880387", "sha256_in_prefix": "7ffe872dbf922838881d98bc1557db8af53dd7d1f06adf070daeaeaa42880387", "size_in_bytes": 37706}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/import_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "0afcd7b322420f615b807d3f242988b4e7a6a2a5b7fbeee1f2af05f7d04c0c75", "sha256_in_prefix": "0afcd7b322420f615b807d3f242988b4e7a6a2a5b7fbeee1f2af05f7d04c0c75", "size_in_bytes": 3209}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/ioloop_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "4cbdcc25a5aba7f8c699874ca34b8ed578c383771bcc792d17d6789035d7841f", "sha256_in_prefix": "4cbdcc25a5aba7f8c699874ca34b8ed578c383771bcc792d17d6789035d7841f", "size_in_bytes": 58393}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/iostream_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "a6b6503eb93f7e9777e98fa9946e62041374def2504abfd469d59a08244c4a7c", "sha256_in_prefix": "a6b6503eb93f7e9777e98fa9946e62041374def2504abfd469d59a08244c4a7c", "size_in_bytes": 96418}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/locale_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "51b78d3ae6a15700c85cbf8f3527371a8970e3c80fabd8a6350052939a67bbdb", "sha256_in_prefix": "51b78d3ae6a15700c85cbf8f3527371a8970e3c80fabd8a6350052939a67bbdb", "size_in_bytes": 12607}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/locks_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "bcd45fe1cd445018c61ad2dfa0a7e57ea72242133d9dffa77f527fc7cf31739a", "sha256_in_prefix": "bcd45fe1cd445018c61ad2dfa0a7e57ea72242133d9dffa77f527fc7cf31739a", "size_in_bytes": 41802}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/log_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "7a1b0b7b83417b1fed43daae8ab5caa798494dbc37002b71add16947c750ffd7", "sha256_in_prefix": "7a1b0b7b83417b1fed43daae8ab5caa798494dbc37002b71add16947c750ffd7", "size_in_bytes": 16673}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/netutil_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "c0fba596b620c0cde6a080e652da95909c54b10ebd12a76888c3c6eb42e8cfaf", "sha256_in_prefix": "c0fba596b620c0cde6a080e652da95909c54b10ebd12a76888c3c6eb42e8cfaf", "size_in_bytes": 14833}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/options_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "c94eee23e990576e165028eeca307a6a8f52233f8a10b5d9183b3ec39d7fe88e", "sha256_in_prefix": "c94eee23e990576e165028eeca307a6a8f52233f8a10b5d9183b3ec39d7fe88e", "size_in_bytes": 23222}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/process_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "5305abb6db8803b44d5fe45d06339e51aba92ad7f45b9a0b681250b6dc1a453f", "sha256_in_prefix": "5305abb6db8803b44d5fe45d06339e51aba92ad7f45b9a0b681250b6dc1a453f", "size_in_bytes": 16455}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/queues_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "3f1be976216f2ef33123e52390315a83fc611e19b741e3ca8d0d1c8cd3f10930", "sha256_in_prefix": "3f1be976216f2ef33123e52390315a83fc611e19b741e3ca8d0d1c8cd3f10930", "size_in_bytes": 33241}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/resolve_test_helper.cpython-311.pyc", "path_type": "hardlink", "sha256": "5301c3ac97e76a84855f893b82fb409d4d2868a9145c5c40bf9d2c2bc62f6b85", "sha256_in_prefix": "5301c3ac97e76a84855f893b82fb409d4d2868a9145c5c40bf9d2c2bc62f6b85", "size_in_bytes": 656}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/routing_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "b906e559a14255d89cff7f3136274354fa843726004f61085cd2d67dc74f3b17", "sha256_in_prefix": "b906e559a14255d89cff7f3136274354fa843726004f61085cd2d67dc74f3b17", "size_in_bytes": 17583}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/runtests.cpython-311.pyc", "path_type": "hardlink", "sha256": "745cdb966db15efdc28ac5d23d946aff09e1fbbe8bd4b808df40269342eb5cb2", "sha256_in_prefix": "745cdb966db15efdc28ac5d23d946aff09e1fbbe8bd4b808df40269342eb5cb2", "size_in_bytes": 11538}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/simple_httpclient_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "e8ea9d5f54ffd847745275e84c5c306de8c0fb19de1b8a5c88e4ce486b66ba34", "sha256_in_prefix": "e8ea9d5f54ffd847745275e84c5c306de8c0fb19de1b8a5c88e4ce486b66ba34", "size_in_bytes": 71382}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/tcpclient_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "4984523c5fd1cd60b2a4ff5b497ce6e966a6bebb5b973025938706d1fd999a36", "sha256_in_prefix": "4984523c5fd1cd60b2a4ff5b497ce6e966a6bebb5b973025938706d1fd999a36", "size_in_bytes": 33683}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/tcpserver_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "086bd0ba7bd66e63923f55984f4a36d7da06132066ce8e81a8e276315ecc7c96", "sha256_in_prefix": "086bd0ba7bd66e63923f55984f4a36d7da06132066ce8e81a8e276315ecc7c96", "size_in_bytes": 13260}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/template_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "3b0369ee4fdcc06fc7f8ddd211c4385406fd53568965c343ddce8e8981daf58e", "sha256_in_prefix": "3b0369ee4fdcc06fc7f8ddd211c4385406fd53568965c343ddce8e8981daf58e", "size_in_bytes": 33732}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/testing_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "6134bcf8854e6fa78dd15b9d262786728c4063d106dd23ecc15227d7d02d5041", "sha256_in_prefix": "6134bcf8854e6fa78dd15b9d262786728c4063d106dd23ecc15227d7d02d5041", "size_in_bytes": 24668}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/twisted_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "8d9f42ab360be81615efbcdedf2a7f78d9e712880aa60f4d13caa02cf30885aa", "sha256_in_prefix": "8d9f42ab360be81615efbcdedf2a7f78d9e712880aa60f4d13caa02cf30885aa", "size_in_bytes": 2562}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/util.cpython-311.pyc", "path_type": "hardlink", "sha256": "cb48d4f5468883181da9c3bb04e371779b0cc79c145d6dc841e489e634092696", "sha256_in_prefix": "cb48d4f5468883181da9c3bb04e371779b0cc79c145d6dc841e489e634092696", "size_in_bytes": 5948}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/util_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "febef561a2b1672b4cf1ae160f31a2358a94b4b17e725011ce2f48f7e4681a08", "sha256_in_prefix": "febef561a2b1672b4cf1ae160f31a2358a94b4b17e725011ce2f48f7e4681a08", "size_in_bytes": 24417}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/web_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "49ab7aa581e51b22542fbcb96ab912952b3a1af7efc4d9a490b99c0d00c5fec4", "sha256_in_prefix": "49ab7aa581e51b22542fbcb96ab912952b3a1af7efc4d9a490b99c0d00c5fec4", "size_in_bytes": 234970}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/websocket_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "b7dfa8c7598841bcb7d806aa51064a5c02f018e4936e63451fa76f79288006c9", "sha256_in_prefix": "b7dfa8c7598841bcb7d806aa51064a5c02f018e4936e63451fa76f79288006c9", "size_in_bytes": 67865}, {"_path": "lib/python3.11/site-packages/tornado/test/__pycache__/wsgi_test.cpython-311.pyc", "path_type": "hardlink", "sha256": "4f1fa7d8db4f26bb567d451f1dfb60f67d2b7d51c54960d17de0fcc5445646f3", "sha256_in_prefix": "4f1fa7d8db4f26bb567d451f1dfb60f67d2b7d51c54960d17de0fcc5445646f3", "size_in_bytes": 8526}, {"_path": "lib/python3.11/site-packages/tornado/test/asyncio_test.py", "path_type": "hardlink", "sha256": "aeacab9d354e6f106db102ff64b98024b0464f1600edd04a1ff85dd678dbaf0a", "sha256_in_prefix": "aeacab9d354e6f106db102ff64b98024b0464f1600edd04a1ff85dd678dbaf0a", "size_in_bytes": 11891}, {"_path": "lib/python3.11/site-packages/tornado/test/auth_test.py", "path_type": "hardlink", "sha256": "bcb8825509d969b6893d54345baa646db41729fade9c31dcb4850d0e51f9d00e", "sha256_in_prefix": "bcb8825509d969b6893d54345baa646db41729fade9c31dcb4850d0e51f9d00e", "size_in_bytes": 23311}, {"_path": "lib/python3.11/site-packages/tornado/test/autoreload_test.py", "path_type": "hardlink", "sha256": "d88e646b80860f28a09e574dbe57f87718b86447b16eba28a02b9a893b7a4841", "sha256_in_prefix": "d88e646b80860f28a09e574dbe57f87718b86447b16eba28a02b9a893b7a4841", "size_in_bytes": 9176}, {"_path": "lib/python3.11/site-packages/tornado/test/circlerefs_test.py", "path_type": "hardlink", "sha256": "22f47617194e750979a6c126b643d346438cd0788d397e9dd55dbce260d99b48", "sha256_in_prefix": "22f47617194e750979a6c126b643d346438cd0788d397e9dd55dbce260d99b48", "size_in_bytes": 7321}, {"_path": "lib/python3.11/site-packages/tornado/test/concurrent_test.py", "path_type": "hardlink", "sha256": "f9eb8ab3df56b42313a5f6b62cb98db02f9e04f12708d9ead6f88720e5f8a348", "sha256_in_prefix": "f9eb8ab3df56b42313a5f6b62cb98db02f9e04f12708d9ead6f88720e5f8a348", "size_in_bytes": 6667}, {"_path": "lib/python3.11/site-packages/tornado/test/csv_translations/fr_FR.csv", "path_type": "hardlink", "sha256": "d14b0ccdf875730df241d852ee90a645f4280246ea5a9833ce876966aa7bb6d3", "sha256_in_prefix": "d14b0ccdf875730df241d852ee90a645f4280246ea5a9833ce876966aa7bb6d3", "size_in_bytes": 18}, {"_path": "lib/python3.11/site-packages/tornado/test/curl_httpclient_test.py", "path_type": "hardlink", "sha256": "6cd28577d17ac23805c30d9551de3f4901d5119c15f74ac62394081a114ec4e0", "sha256_in_prefix": "6cd28577d17ac23805c30d9551de3f4901d5119c15f74ac62394081a114ec4e0", "size_in_bytes": 4213}, {"_path": "lib/python3.11/site-packages/tornado/test/escape_test.py", "path_type": "hardlink", "sha256": "1380380208b1f5121b4a968e45b62648b08af29a0b170e53d33fbb59c85603ff", "sha256_in_prefix": "1380380208b1f5121b4a968e45b62648b08af29a0b170e53d33fbb59c85603ff", "size_in_bytes": 12330}, {"_path": "lib/python3.11/site-packages/tornado/test/gen_test.py", "path_type": "hardlink", "sha256": "50f104bb49c1ee2294f8957d02561affc2429e14404975b606d3318ed4f2d30f", "sha256_in_prefix": "50f104bb49c1ee2294f8957d02561affc2429e14404975b606d3318ed4f2d30f", "size_in_bytes": 33825}, {"_path": "lib/python3.11/site-packages/tornado/test/gettext_translations/fr_FR/LC_MESSAGES/tornado_test.mo", "path_type": "hardlink", "sha256": "7e5d19559225370c14f653f1dbda606785fe1dfc84558a6126eed45ad965ee3a", "sha256_in_prefix": "7e5d19559225370c14f653f1dbda606785fe1dfc84558a6126eed45ad965ee3a", "size_in_bytes": 665}, {"_path": "lib/python3.11/site-packages/tornado/test/gettext_translations/fr_FR/LC_MESSAGES/tornado_test.po", "path_type": "hardlink", "sha256": "0a5c3a1f2414728a46576e6ac37a6fc37827d59a99458ae8bec8bc3d04d00273", "sha256_in_prefix": "0a5c3a1f2414728a46576e6ac37a6fc37827d59a99458ae8bec8bc3d04d00273", "size_in_bytes": 1049}, {"_path": "lib/python3.11/site-packages/tornado/test/http1connection_test.py", "path_type": "hardlink", "sha256": "d0baf8ef0c94e2c1ac1b848d59a4c85a382400829e2bef0e1394985975a812a7", "sha256_in_prefix": "d0baf8ef0c94e2c1ac1b848d59a4c85a382400829e2bef0e1394985975a812a7", "size_in_bytes": 1964}, {"_path": "lib/python3.11/site-packages/tornado/test/httpclient_test.py", "path_type": "hardlink", "sha256": "67539e9ff839c571127369818d08c9e9b6773216b3d6610d0fc000e52dc599c4", "sha256_in_prefix": "67539e9ff839c571127369818d08c9e9b6773216b3d6610d0fc000e52dc599c4", "size_in_bytes": 36808}, {"_path": "lib/python3.11/site-packages/tornado/test/httpserver_test.py", "path_type": "hardlink", "sha256": "f0fd66cb6f999e083b3a7b6075eef657b8949b12a76633a3a83dd36058aa2ed2", "sha256_in_prefix": "f0fd66cb6f999e083b3a7b6075eef657b8949b12a76633a3a83dd36058aa2ed2", "size_in_bytes": 53616}, {"_path": "lib/python3.11/site-packages/tornado/test/httputil_test.py", "path_type": "hardlink", "sha256": "634722b08a13d3496aa157c09cb55485ad0b84a4d9d1decdbfebed1a40ec2fb0", "sha256_in_prefix": "634722b08a13d3496aa157c09cb55485ad0b84a4d9d1decdbfebed1a40ec2fb0", "size_in_bytes": 24793}, {"_path": "lib/python3.11/site-packages/tornado/test/import_test.py", "path_type": "hardlink", "sha256": "8e20903fb14dd361d7de511c09d633388f3a2e243cb4542543de58dd510c7993", "sha256_in_prefix": "8e20903fb14dd361d7de511c09d633388f3a2e243cb4542543de58dd510c7993", "size_in_bytes": 2339}, {"_path": "lib/python3.11/site-packages/tornado/test/ioloop_test.py", "path_type": "hardlink", "sha256": "ec3a6d4c7f380fb63e0e185c082af020fd39caab6626caaa25443c974fea4fc9", "sha256_in_prefix": "ec3a6d4c7f380fb63e0e185c082af020fd39caab6626caaa25443c974fea4fc9", "size_in_bytes": 27962}, {"_path": "lib/python3.11/site-packages/tornado/test/iostream_test.py", "path_type": "hardlink", "sha256": "8f2a95228f9309ce277f2a0d381b801bcd3ce2687c76a91f2d8e68f173def40f", "sha256_in_prefix": "8f2a95228f9309ce277f2a0d381b801bcd3ce2687c76a91f2d8e68f173def40f", "size_in_bytes": 51795}, {"_path": "lib/python3.11/site-packages/tornado/test/locale_test.py", "path_type": "hardlink", "sha256": "6d6d68a1e833069aaff48cb89bb079b095aa90c6bcd7038327d033b3f25b252f", "sha256_in_prefix": "6d6d68a1e833069aaff48cb89bb079b095aa90c6bcd7038327d033b3f25b252f", "size_in_bytes": 6359}, {"_path": "lib/python3.11/site-packages/tornado/test/locks_test.py", "path_type": "hardlink", "sha256": "5df3f047872646663b93e4657e870846522214cc1a6ea2df221f535a92b6cde7", "sha256_in_prefix": "5df3f047872646663b93e4657e870846522214cc1a6ea2df221f535a92b6cde7", "size_in_bytes": 16998}, {"_path": "lib/python3.11/site-packages/tornado/test/log_test.py", "path_type": "hardlink", "sha256": "09401389e97d2372fa1d7462612b9b1514b719aa6abbba15dec471b2ce8fab96", "sha256_in_prefix": "09401389e97d2372fa1d7462612b9b1514b719aa6abbba15dec471b2ce8fab96", "size_in_bytes": 8912}, {"_path": "lib/python3.11/site-packages/tornado/test/netutil_test.py", "path_type": "hardlink", "sha256": "67a74dcdf97a9dac583dc19fb7f57567c0f241abdb9ce99379047e0307259e19", "sha256_in_prefix": "67a74dcdf97a9dac583dc19fb7f57567c0f241abdb9ce99379047e0307259e19", "size_in_bytes": 7038}, {"_path": "lib/python3.11/site-packages/tornado/test/options_test.cfg", "path_type": "hardlink", "sha256": "4a952ea64f8c7d704c84b10da97983f8ae5ad8ca7cf3c444a9f20242ea5c82a7", "sha256_in_prefix": "4a952ea64f8c7d704c84b10da97983f8ae5ad8ca7cf3c444a9f20242ea5c82a7", "size_in_bytes": 69}, {"_path": "lib/python3.11/site-packages/tornado/test/options_test.py", "path_type": "hardlink", "sha256": "fbfe6edeeec023254361d027908340a3a951d53eff8cdda19c5344a009a04848", "sha256_in_prefix": "fbfe6edeeec023254361d027908340a3a951d53eff8cdda19c5344a009a04848", "size_in_bytes": 11881}, {"_path": "lib/python3.11/site-packages/tornado/test/options_test_types.cfg", "path_type": "hardlink", "sha256": "d7bb00b9b4438a7617fc34617426952745295f501e7d9f68ff0436ca9315c0bd", "sha256_in_prefix": "d7bb00b9b4438a7617fc34617426952745295f501e7d9f68ff0436ca9315c0bd", "size_in_bytes": 296}, {"_path": "lib/python3.11/site-packages/tornado/test/options_test_types_str.cfg", "path_type": "hardlink", "sha256": "ca922876bb69ca6497d87b1ebc6249e00d3ba0b89f6f97df901212a20dfae2aa", "sha256_in_prefix": "ca922876bb69ca6497d87b1ebc6249e00d3ba0b89f6f97df901212a20dfae2aa", "size_in_bytes": 172}, {"_path": "lib/python3.11/site-packages/tornado/test/process_test.py", "path_type": "hardlink", "sha256": "f4b895fc007c79092c526e155f831f48a79f7de530e97f2d95cc3a0fcf655915", "sha256_in_prefix": "f4b895fc007c79092c526e155f831f48a79f7de530e97f2d95cc3a0fcf655915", "size_in_bytes": 10677}, {"_path": "lib/python3.11/site-packages/tornado/test/queues_test.py", "path_type": "hardlink", "sha256": "1064d5d7f04c2b78138e5ac5132a90135144d9633d08f03a0fb363db42768bb7", "sha256_in_prefix": "1064d5d7f04c2b78138e5ac5132a90135144d9633d08f03a0fb363db42768bb7", "size_in_bytes": 13981}, {"_path": "lib/python3.11/site-packages/tornado/test/resolve_test_helper.py", "path_type": "hardlink", "sha256": "ad388e302e0a568ff6d57bec2e1b626428daaae93fcede8354cfe10b208cef21", "sha256_in_prefix": "ad388e302e0a568ff6d57bec2e1b626428daaae93fcede8354cfe10b208cef21", "size_in_bytes": 410}, {"_path": "lib/python3.11/site-packages/tornado/test/routing_test.py", "path_type": "hardlink", "sha256": "616646b58a765158f655ff9ed4a61d857cf7e4c803d33c0d5cbcbb21260ed10d", "sha256_in_prefix": "616646b58a765158f655ff9ed4a61d857cf7e4c803d33c0d5cbcbb21260ed10d", "size_in_bytes": 8827}, {"_path": "lib/python3.11/site-packages/tornado/test/runtests.py", "path_type": "hardlink", "sha256": "3a8171577722b7e2c8cdb0743a2afde9563bb520ac6ce7d73112647d4f2501d0", "sha256_in_prefix": "3a8171577722b7e2c8cdb0743a2afde9563bb520ac6ce7d73112647d4f2501d0", "size_in_bytes": 7459}, {"_path": "lib/python3.11/site-packages/tornado/test/simple_httpclient_test.py", "path_type": "hardlink", "sha256": "25734364d5edab52d5b2a5a4521a8f80ee96ee331c3eb969e05b25b622bcf5dc", "sha256_in_prefix": "25734364d5edab52d5b2a5a4521a8f80ee96ee331c3eb969e05b25b622bcf5dc", "size_in_bytes": 31535}, {"_path": "lib/python3.11/site-packages/tornado/test/static/dir/index.html", "path_type": "hardlink", "sha256": "b41c016a75128c8494cb40556a7fd034a91874b6aef2ab3ffcfd06f680223fbf", "sha256_in_prefix": "b41c016a75128c8494cb40556a7fd034a91874b6aef2ab3ffcfd06f680223fbf", "size_in_bytes": 18}, {"_path": "lib/python3.11/site-packages/tornado/test/static/robots.txt", "path_type": "hardlink", "sha256": "331ea9090db0c9f6f597bd9840fd5b171830f6e0b3ba1cb24dfa91f0c95aedc1", "sha256_in_prefix": "331ea9090db0c9f6f597bd9840fd5b171830f6e0b3ba1cb24dfa91f0c95aedc1", "size_in_bytes": 26}, {"_path": "lib/python3.11/site-packages/tornado/test/static/sample.xml", "path_type": "hardlink", "sha256": "ecb7937f5e81583b9c8a9b1469964aee80f1b4a10c0c7f6c16db0d4751baa4c1", "sha256_in_prefix": "ecb7937f5e81583b9c8a9b1469964aee80f1b4a10c0c7f6c16db0d4751baa4c1", "size_in_bytes": 666}, {"_path": "lib/python3.11/site-packages/tornado/test/static/sample.xml.bz2", "path_type": "hardlink", "sha256": "d9097971c5a7692a43753ca2a229e8ec1c3f75c185906fd140ee4b9b971f4fa0", "sha256_in_prefix": "d9097971c5a7692a43753ca2a229e8ec1c3f75c185906fd140ee4b9b971f4fa0", "size_in_bytes": 285}, {"_path": "lib/python3.11/site-packages/tornado/test/static/sample.xml.gz", "path_type": "hardlink", "sha256": "fc0a69d302a99f7d6565503d3ffaec972b4f9b87a2e46bcd3d52a1e79afb976f", "sha256_in_prefix": "fc0a69d302a99f7d6565503d3ffaec972b4f9b87a2e46bcd3d52a1e79afb976f", "size_in_bytes": 264}, {"_path": "lib/python3.11/site-packages/tornado/test/static_foo.txt", "path_type": "hardlink", "sha256": "0dd00a001cc76fc92e9c6ac0bd4a132e71e581d4cdfbafcf8c7d62ad59a8926f", "sha256_in_prefix": "0dd00a001cc76fc92e9c6ac0bd4a132e71e581d4cdfbafcf8c7d62ad59a8926f", "size_in_bytes": 95}, {"_path": "lib/python3.11/site-packages/tornado/test/tcpclient_test.py", "path_type": "hardlink", "sha256": "e040f0244c4452d3b5b2fe28aaf4e8903f9968ce36da8f9608a8b280fd010f07", "sha256_in_prefix": "e040f0244c4452d3b5b2fe28aaf4e8903f9968ce36da8f9608a8b280fd010f07", "size_in_bytes": 16511}, {"_path": "lib/python3.11/site-packages/tornado/test/tcpserver_test.py", "path_type": "hardlink", "sha256": "e7f6917e67c13b6ac5a78cf9979e87a07a65008d7a3cb9180f203d59ec35b00e", "sha256_in_prefix": "e7f6917e67c13b6ac5a78cf9979e87a07a65008d7a3cb9180f203d59ec35b00e", "size_in_bytes": 7711}, {"_path": "lib/python3.11/site-packages/tornado/test/template_test.py", "path_type": "hardlink", "sha256": "86fa2034bd047c0b44c98354851c6a8a27f3c9da15e1dfd6624608671581098c", "sha256_in_prefix": "86fa2034bd047c0b44c98354851c6a8a27f3c9da15e1dfd6624608671581098c", "size_in_bytes": 18541}, {"_path": "lib/python3.11/site-packages/tornado/test/templates/utf8.html", "path_type": "hardlink", "sha256": "f5dd5e89ac392828d44c26d144897f44b4b2d0b0895da3be6f3545d8bff7d9f3", "sha256_in_prefix": "f5dd5e89ac392828d44c26d144897f44b4b2d0b0895da3be6f3545d8bff7d9f3", "size_in_bytes": 7}, {"_path": "lib/python3.11/site-packages/tornado/test/test.crt", "path_type": "hardlink", "sha256": "6702d3f46ec174de6845c083e3c9be88013d253f12092a243d5cc960cc3dbafa", "sha256_in_prefix": "6702d3f46ec174de6845c083e3c9be88013d253f12092a243d5cc960cc3dbafa", "size_in_bytes": 1042}, {"_path": "lib/python3.11/site-packages/tornado/test/test.key", "path_type": "hardlink", "sha256": "2c95ec86909e3299af80778674140159a44c2b8c8422f31a82e81d40572f0c74", "sha256_in_prefix": "2c95ec86909e3299af80778674140159a44c2b8c8422f31a82e81d40572f0c74", "size_in_bytes": 1708}, {"_path": "lib/python3.11/site-packages/tornado/test/testing_test.py", "path_type": "hardlink", "sha256": "d0acff2297df4648b413719b3f940f7bb5d4923e27969efb8e3339290d29c26e", "sha256_in_prefix": "d0acff2297df4648b413719b3f940f7bb5d4923e27969efb8e3339290d29c26e", "size_in_bytes": 10509}, {"_path": "lib/python3.11/site-packages/tornado/test/twisted_test.py", "path_type": "hardlink", "sha256": "19bb359b952b488a1a2c82e3ea366bd5d9b1c012bf8b3131b642355ee69f5691", "sha256_in_prefix": "19bb359b952b488a1a2c82e3ea366bd5d9b1c012bf8b3131b642355ee69f5691", "size_in_bytes": 2104}, {"_path": "lib/python3.11/site-packages/tornado/test/util.py", "path_type": "hardlink", "sha256": "309965001717fa6997d5bb8d889732f5d43ae595373f1a5b927e3800ccf298ff", "sha256_in_prefix": "309965001717fa6997d5bb8d889732f5d43ae595373f1a5b927e3800ccf298ff", "size_in_bytes": 4323}, {"_path": "lib/python3.11/site-packages/tornado/test/util_test.py", "path_type": "hardlink", "sha256": "233ad55c91754292cf16a3fb07da7e275783dee2ebcd5a54364e85633218cb86", "sha256_in_prefix": "233ad55c91754292cf16a3fb07da7e275783dee2ebcd5a54364e85633218cb86", "size_in_bytes": 13007}, {"_path": "lib/python3.11/site-packages/tornado/test/web_test.py", "path_type": "hardlink", "sha256": "acde4a8a6ccc87183699cf78773be0c1d77bdf5258ff09cda74b080b72dd8485", "sha256_in_prefix": "acde4a8a6ccc87183699cf78773be0c1d77bdf5258ff09cda74b080b72dd8485", "size_in_bytes": 122489}, {"_path": "lib/python3.11/site-packages/tornado/test/websocket_test.py", "path_type": "hardlink", "sha256": "cb773b18914116701fafd717bafdf08d1390f72f4f30df5437c38e1f42cf3539", "sha256_in_prefix": "cb773b18914116701fafd717bafdf08d1390f72f4f30df5437c38e1f42cf3539", "size_in_bytes": 32775}, {"_path": "lib/python3.11/site-packages/tornado/test/wsgi_test.py", "path_type": "hardlink", "sha256": "b5025aee4090e821c2aab63390171b0e38f0977aa2a6c8da8a536b7152608b6a", "sha256_in_prefix": "b5025aee4090e821c2aab63390171b0e38f0977aa2a6c8da8a536b7152608b6a", "size_in_bytes": 3918}, {"_path": "lib/python3.11/site-packages/tornado/testing.py", "path_type": "hardlink", "sha256": "bf6bc9cc6254d49499b430aaad937d9ac84fc68ad20cf40b75cb55470e56fb74", "sha256_in_prefix": "bf6bc9cc6254d49499b430aaad937d9ac84fc68ad20cf40b75cb55470e56fb74", "size_in_bytes": 33153}, {"_path": "lib/python3.11/site-packages/tornado/util.py", "path_type": "hardlink", "sha256": "386a749c39111b82995878a6b92f469bc1a9ebeb5ca97dda54259ab058a88c2b", "sha256_in_prefix": "386a749c39111b82995878a6b92f469bc1a9ebeb5ca97dda54259ab058a88c2b", "size_in_bytes": 15780}, {"_path": "lib/python3.11/site-packages/tornado/web.py", "path_type": "hardlink", "sha256": "74b855253b0c68bedd7fb6cfe397e4e22219f66a02dbb42852fdb2c5b276e49b", "sha256_in_prefix": "74b855253b0c68bedd7fb6cfe397e4e22219f66a02dbb42852fdb2c5b276e49b", "size_in_bytes": 145698}, {"_path": "lib/python3.11/site-packages/tornado/websocket.py", "path_type": "hardlink", "sha256": "edd7b70fc9ae58384f066c3c55dd5983494c8cf3763b7d9cb7ab7518fbaf2de9", "sha256_in_prefix": "edd7b70fc9ae58384f066c3c55dd5983494c8cf3763b7d9cb7ab7518fbaf2de9", "size_in_bytes": 64024}, {"_path": "lib/python3.11/site-packages/tornado/wsgi.py", "path_type": "hardlink", "sha256": "44d99f6ef238cc02a0d089a61d1aed1792c15adef2ca5c6e4cb7acb86353924d", "sha256_in_prefix": "44d99f6ef238cc02a0d089a61d1aed1792c15adef2ca5c6e4cb7acb86353924d", "size_in_bytes": 10799}], "paths_version": 1}, "requested_spec": "None", "sha256": "397226b6314aec2b076294816c941c1e9b7e3bbcf7a2e9dc9ba08f3ac10b0590", "size": 869912, "subdir": "osx-64", "timestamp": 1756855230000, "url": "https://conda.anaconda.org/conda-forge/osx-64/tornado-6.5.2-py311h13e5629_1.conda", "version": "6.5.2"}