{"build": "pyhd8ed1ab_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["ansi2html", "dash", "flask", "ipykernel", "ipython", "nest-asyncio", "python >=3.5", "requests", "retrying"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/jupyter-dash-0.4.2-pyhd8ed1ab_1", "files": ["etc/jupyter/nbconfig/notebook.d/jupyter_dash.json", "share/jupyter/lab/extensions/jupyterlab-dash-v0.4.2.tgz", "share/jupyter/nbextensions/jupyter_dash/main.js", "lib/python3.11/site-packages/jupyter_dash-0.4.2.dist-info/INSTALLER", "lib/python3.11/site-packages/jupyter_dash-0.4.2.dist-info/LICENSE.txt", "lib/python3.11/site-packages/jupyter_dash-0.4.2.dist-info/METADATA", "lib/python3.11/site-packages/jupyter_dash-0.4.2.dist-info/RECORD", "lib/python3.11/site-packages/jupyter_dash-0.4.2.dist-info/REQUESTED", "lib/python3.11/site-packages/jupyter_dash-0.4.2.dist-info/WHEEL", "lib/python3.11/site-packages/jupyter_dash-0.4.2.dist-info/direct_url.json", "lib/python3.11/site-packages/jupyter_dash-0.4.2.dist-info/top_level.txt", "lib/python3.11/site-packages/jupyter_dash/__init__.py", "lib/python3.11/site-packages/jupyter_dash/_stoppable_thread.py", "lib/python3.11/site-packages/jupyter_dash/comms.py", "lib/python3.11/site-packages/jupyter_dash/jupyter_app.py", "lib/python3.11/site-packages/jupyter_dash/labextension/dist/jupyterlab-dash-v0.4.2.tgz", "lib/python3.11/site-packages/jupyter_dash/labextension/package.json", "lib/python3.11/site-packages/jupyter_dash/nbextension/jupyter_dash.json", "lib/python3.11/site-packages/jupyter_dash/nbextension/main.js", "lib/python3.11/site-packages/jupyter_dash/version.py", "lib/python3.11/site-packages/jupyter_dash/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/jupyter_dash/__pycache__/_stoppable_thread.cpython-311.pyc", "lib/python3.11/site-packages/jupyter_dash/__pycache__/comms.cpython-311.pyc", "lib/python3.11/site-packages/jupyter_dash/__pycache__/jupyter_app.cpython-311.pyc", "lib/python3.11/site-packages/jupyter_dash/__pycache__/version.cpython-311.pyc"], "fn": "jupyter-dash-0.4.2-pyhd8ed1ab_1.tar.bz2", "license": "MIT", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/jupyter-dash-0.4.2-pyhd8ed1ab_1", "type": 1}, "md5": "9c77330b235666f244a7b8dcc7c0955a", "name": "jupyter-dash", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/jupyter-dash-0.4.2-pyhd8ed1ab_1.tar.bz2", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "etc/jupyter/nbconfig/notebook.d/jupyter_dash.json", "path_type": "hardlink", "sha256": "8fcc7d35637b0d7ba0a10f19dc30df91c5a3cd9b372aed967a118e14366c9a48", "sha256_in_prefix": "8fcc7d35637b0d7ba0a10f19dc30df91c5a3cd9b372aed967a118e14366c9a48", "size_in_bytes": 61}, {"_path": "share/jupyter/lab/extensions/jupyterlab-dash-v0.4.2.tgz", "path_type": "hardlink", "sha256": "5a857a0af997df2b862c689490aa39091c2855dd2c54a7ce1c10dca4bec8d139", "sha256_in_prefix": "5a857a0af997df2b862c689490aa39091c2855dd2c54a7ce1c10dca4bec8d139", "size_in_bytes": 3417}, {"_path": "share/jupyter/nbextensions/jupyter_dash/main.js", "path_type": "hardlink", "sha256": "d442f17210e021ff207ceaf6d44898449996044e7b6404826810fbe499e391d5", "sha256_in_prefix": "d442f17210e021ff207ceaf6d44898449996044e7b6404826810fbe499e391d5", "size_in_bytes": 1347}, {"_path": "site-packages/jupyter_dash-0.4.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/jupyter_dash-0.4.2.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "d2fc53bed9f2d5f1f9acc5facc090a61791cc476ff3d7e97ca3286907e9bdedc", "sha256_in_prefix": "d2fc53bed9f2d5f1f9acc5facc090a61791cc476ff3d7e97ca3286907e9bdedc", "size_in_bytes": 1078}, {"_path": "site-packages/jupyter_dash-0.4.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "58e9affc842fcda6a416038995f39ffbbadbcf0ee6cecc3c4674b73bb7de7cdc", "sha256_in_prefix": "58e9affc842fcda6a416038995f39ffbbadbcf0ee6cecc3c4674b73bb7de7cdc", "size_in_bytes": 3575}, {"_path": "site-packages/jupyter_dash-0.4.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "102d24946369ae634735c4d4998dcdcdd0bd2c568fc804e808afd3b2c28ef06f", "sha256_in_prefix": "102d24946369ae634735c4d4998dcdcdd0bd2c568fc804e808afd3b2c28ef06f", "size_in_bytes": 2144}, {"_path": "site-packages/jupyter_dash-0.4.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/jupyter_dash-0.4.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "1b5e87e00dc87a84269cead8578b9e6462928e18a95f1f3373c9eef451a5bcc0", "sha256_in_prefix": "1b5e87e00dc87a84269cead8578b9e6462928e18a95f1f3373c9eef451a5bcc0", "size_in_bytes": 92}, {"_path": "site-packages/jupyter_dash-0.4.2.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "278cd3ed213c094f9c64b98df8743cbccd630318831748fbee0b94679f2d79bd", "sha256_in_prefix": "278cd3ed213c094f9c64b98df8743cbccd630318831748fbee0b94679f2d79bd", "size_in_bytes": 108}, {"_path": "site-packages/jupyter_dash-0.4.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "00cfccaa4ea6b4b353636d4ddabfc97ff4928fed1b1d21b30850ad53fccc73e6", "sha256_in_prefix": "00cfccaa4ea6b4b353636d4ddabfc97ff4928fed1b1d21b30850ad53fccc73e6", "size_in_bytes": 13}, {"_path": "site-packages/jupyter_dash/__init__.py", "path_type": "hardlink", "sha256": "6c7f60f96a0861aeeeff849bf3d49ef18b8bef4203d6e7d26237651e6cb8a41c", "sha256_in_prefix": "6c7f60f96a0861aeeeff849bf3d49ef18b8bef4203d6e7d26237651e6cb8a41c", "size_in_bytes": 319}, {"_path": "site-packages/jupyter_dash/_stoppable_thread.py", "path_type": "hardlink", "sha256": "09389b48f948bd5816edff872624dd9c225cc46a7f760772fdb89b2f997414cd", "sha256_in_prefix": "09389b48f948bd5816edff872624dd9c225cc46a7f760772fdb89b2f997414cd", "size_in_bytes": 733}, {"_path": "site-packages/jupyter_dash/comms.py", "path_type": "hardlink", "sha256": "5218cb680d5328ebafe49e51f76870999dbe36c543864760799e3cfda465b9d8", "sha256_in_prefix": "5218cb680d5328ebafe49e51f76870999dbe36c543864760799e3cfda465b9d8", "size_in_bytes": 3155}, {"_path": "site-packages/jupyter_dash/jupyter_app.py", "path_type": "hardlink", "sha256": "c0cce93382f55adaeff3cdfb2c8c3420712c38d85e185e603ee72e028ff2a2fb", "sha256_in_prefix": "c0cce93382f55adaeff3cdfb2c8c3420712c38d85e185e603ee72e028ff2a2fb", "size_in_bytes": 17265}, {"_path": "site-packages/jupyter_dash/labextension/dist/jupyterlab-dash-v0.4.2.tgz", "path_type": "hardlink", "sha256": "5a857a0af997df2b862c689490aa39091c2855dd2c54a7ce1c10dca4bec8d139", "sha256_in_prefix": "5a857a0af997df2b862c689490aa39091c2855dd2c54a7ce1c10dca4bec8d139", "size_in_bytes": 3417}, {"_path": "site-packages/jupyter_dash/labextension/package.json", "path_type": "hardlink", "sha256": "c61e78e9445f46cbd63f092550a5098ae5d7b0cf27c30fcde679f7bb8365a0fc", "sha256_in_prefix": "c61e78e9445f46cbd63f092550a5098ae5d7b0cf27c30fcde679f7bb8365a0fc", "size_in_bytes": 1252}, {"_path": "site-packages/jupyter_dash/nbextension/jupyter_dash.json", "path_type": "hardlink", "sha256": "8fcc7d35637b0d7ba0a10f19dc30df91c5a3cd9b372aed967a118e14366c9a48", "sha256_in_prefix": "8fcc7d35637b0d7ba0a10f19dc30df91c5a3cd9b372aed967a118e14366c9a48", "size_in_bytes": 61}, {"_path": "site-packages/jupyter_dash/nbextension/main.js", "path_type": "hardlink", "sha256": "d442f17210e021ff207ceaf6d44898449996044e7b6404826810fbe499e391d5", "sha256_in_prefix": "d442f17210e021ff207ceaf6d44898449996044e7b6404826810fbe499e391d5", "size_in_bytes": 1347}, {"_path": "site-packages/jupyter_dash/version.py", "path_type": "hardlink", "sha256": "ea17d56b5d90fa75f25045ebe92c8aa6a3c40c95babe5447c8fc65036ecf7d3b", "sha256_in_prefix": "ea17d56b5d90fa75f25045ebe92c8aa6a3c40c95babe5447c8fc65036ecf7d3b", "size_in_bytes": 22}, {"_path": "lib/python3.11/site-packages/jupyter_dash/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jupyter_dash/__pycache__/_stoppable_thread.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jupyter_dash/__pycache__/comms.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jupyter_dash/__pycache__/jupyter_app.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jupyter_dash/__pycache__/version.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "jupyter-dash", "sha256": "1b664f4776f45c897bb783fbfae19c400d58b1e68bdd1942da2c95a4e7f50091", "size": 20932, "subdir": "noarch", "timestamp": 1648919087000, "url": "https://conda.anaconda.org/conda-forge/noarch/jupyter-dash-0.4.2-pyhd8ed1ab_1.tar.bz2", "version": "0.4.2"}