{"build": "pyhff2d567_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.9"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/python-multipart-0.0.20-pyhff2d567_0", "files": ["lib/python3.11/site-packages/multipart/__init__.py", "lib/python3.11/site-packages/multipart/decoders.py", "lib/python3.11/site-packages/multipart/exceptions.py", "lib/python3.11/site-packages/multipart/multipart.py", "lib/python3.11/site-packages/python_multipart-0.0.20.dist-info/INSTALLER", "lib/python3.11/site-packages/python_multipart-0.0.20.dist-info/METADATA", "lib/python3.11/site-packages/python_multipart-0.0.20.dist-info/RECORD", "lib/python3.11/site-packages/python_multipart-0.0.20.dist-info/REQUESTED", "lib/python3.11/site-packages/python_multipart-0.0.20.dist-info/WHEEL", "lib/python3.11/site-packages/python_multipart-0.0.20.dist-info/direct_url.json", "lib/python3.11/site-packages/python_multipart-0.0.20.dist-info/licenses/LICENSE.txt", "lib/python3.11/site-packages/python_multipart/__init__.py", "lib/python3.11/site-packages/python_multipart/decoders.py", "lib/python3.11/site-packages/python_multipart/exceptions.py", "lib/python3.11/site-packages/python_multipart/multipart.py", "lib/python3.11/site-packages/python_multipart/py.typed", "lib/python3.11/site-packages/multipart/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/multipart/__pycache__/decoders.cpython-311.pyc", "lib/python3.11/site-packages/multipart/__pycache__/exceptions.cpython-311.pyc", "lib/python3.11/site-packages/multipart/__pycache__/multipart.cpython-311.pyc", "lib/python3.11/site-packages/python_multipart/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/python_multipart/__pycache__/decoders.cpython-311.pyc", "lib/python3.11/site-packages/python_multipart/__pycache__/exceptions.cpython-311.pyc", "lib/python3.11/site-packages/python_multipart/__pycache__/multipart.cpython-311.pyc"], "fn": "python-multipart-0.0.20-pyhff2d567_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/python-multipart-0.0.20-pyhff2d567_0", "type": 1}, "md5": "a28c984e0429aff3ab7386f7de56de6f", "name": "python-multipart", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/python-multipart-0.0.20-pyhff2d567_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/multipart/__init__.py", "path_type": "hardlink", "sha256": "fedb713801674cde2379a73effc35e5e9697618a343cf108a7c3a0a386053471", "sha256_in_prefix": "fedb713801674cde2379a73effc35e5e9697618a343cf108a7c3a0a386053471", "size_in_bytes": 935}, {"_path": "site-packages/multipart/decoders.py", "path_type": "hardlink", "sha256": "5ef900c1353d5053e25e4734864be8bc77f45ba1f7bcafb689e5a585abf4da14", "sha256_in_prefix": "5ef900c1353d5053e25e4734864be8bc77f45ba1f7bcafb689e5a585abf4da14", "size_in_bytes": 40}, {"_path": "site-packages/multipart/exceptions.py", "path_type": "hardlink", "sha256": "e83fd7fac7a23a63009487a21a53c652cf3ebe972f20625e4327053036f57fb0", "sha256_in_prefix": "e83fd7fac7a23a63009487a21a53c652cf3ebe972f20625e4327053036f57fb0", "size_in_bytes": 42}, {"_path": "site-packages/multipart/multipart.py", "path_type": "hardlink", "sha256": "f1f0c7d788ff54c6eb721ff9f309738badd7340446bfcd2439903237bd9a1bb0", "sha256_in_prefix": "f1f0c7d788ff54c6eb721ff9f309738badd7340446bfcd2439903237bd9a1bb0", "size_in_bytes": 41}, {"_path": "site-packages/python_multipart-0.0.20.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/python_multipart-0.0.20.dist-info/METADATA", "path_type": "hardlink", "sha256": "8761ad3ce55285b564a4152b8e9e4a137b7a7a224985dd3f5826825eb6f94e05", "sha256_in_prefix": "8761ad3ce55285b564a4152b8e9e4a137b7a7a224985dd3f5826825eb6f94e05", "size_in_bytes": 1817}, {"_path": "site-packages/python_multipart-0.0.20.dist-info/RECORD", "path_type": "hardlink", "sha256": "41b9f545969d8d49607673863c9404f25673fc2d5f47864204ec627a9eaeef0f", "sha256_in_prefix": "41b9f545969d8d49607673863c9404f25673fc2d5f47864204ec627a9eaeef0f", "size_in_bytes": 1816}, {"_path": "site-packages/python_multipart-0.0.20.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/python_multipart-0.0.20.dist-info/WHEEL", "path_type": "hardlink", "sha256": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "sha256_in_prefix": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "size_in_bytes": 87}, {"_path": "site-packages/python_multipart-0.0.20.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "cf132ce297cea64d7e01bca87e19f4e60fb0e25b7f9adcbe12316b6a8aae1124", "sha256_in_prefix": "cf132ce297cea64d7e01bca87e19f4e60fb0e25b7f9adcbe12316b6a8aae1124", "size_in_bytes": 112}, {"_path": "site-packages/python_multipart-0.0.20.dist-info/licenses/LICENSE.txt", "path_type": "hardlink", "sha256": "a8e833176cd617daf00b9d6d39fa15ca8edebc6d1643079cd2f4893c0c289be2", "sha256_in_prefix": "a8e833176cd617daf00b9d6d39fa15ca8edebc6d1643079cd2f4893c0c289be2", "size_in_bytes": 556}, {"_path": "site-packages/python_multipart/__init__.py", "path_type": "hardlink", "sha256": "365c3a62b73ffea5e70992e8d7b3b36c9476c369b0892164ebd206e16977e445", "sha256_in_prefix": "365c3a62b73ffea5e70992e8d7b3b36c9476c369b0892164ebd206e16977e445", "size_in_bytes": 512}, {"_path": "site-packages/python_multipart/decoders.py", "path_type": "hardlink", "sha256": "24ce3714c367fc428fd0c236664b991e135ad0c3804a8211d14e53bdd1b9f399", "sha256_in_prefix": "24ce3714c367fc428fd0c236664b991e135ad0c3804a8211d14e53bdd1b9f399", "size_in_bytes": 6669}, {"_path": "site-packages/python_multipart/exceptions.py", "path_type": "hardlink", "sha256": "6bd6ee48ebff7a21d9a2e9042617565fd2c961227ab7b5ce2b7644696a106659", "sha256_in_prefix": "6bd6ee48ebff7a21d9a2e9042617565fd2c961227ab7b5ce2b7644696a106659", "size_in_bytes": 992}, {"_path": "site-packages/python_multipart/multipart.py", "path_type": "hardlink", "sha256": "a64de8dde0772976cdc73381c5b12309dcfed444846483175487e5d7682b1bea", "sha256_in_prefix": "a64de8dde0772976cdc73381c5b12309dcfed444846483175487e5d7682b1bea", "size_in_bytes": 76427}, {"_path": "site-packages/python_multipart/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/multipart/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/multipart/__pycache__/decoders.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/multipart/__pycache__/exceptions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/multipart/__pycache__/multipart.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/python_multipart/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/python_multipart/__pycache__/decoders.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/python_multipart/__pycache__/exceptions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/python_multipart/__pycache__/multipart.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "1b03678d145b1675b757cba165a0d9803885807792f7eb4495e48a38858c3cca", "size": 27913, "subdir": "noarch", "timestamp": 1734420869000, "url": "https://conda.anaconda.org/conda-forge/noarch/python-multipart-0.0.20-pyhff2d567_0.conda", "version": "0.0.20"}