{"build": "pyhd8ed1ab_3", "build_number": 3, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.9"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/webencodings-0.5.1-pyhd8ed1ab_3", "files": ["lib/python3.11/site-packages/webencodings-0.5.1.dist-info/INSTALLER", "lib/python3.11/site-packages/webencodings-0.5.1.dist-info/LICENSE", "lib/python3.11/site-packages/webencodings-0.5.1.dist-info/METADATA", "lib/python3.11/site-packages/webencodings-0.5.1.dist-info/RECORD", "lib/python3.11/site-packages/webencodings-0.5.1.dist-info/REQUESTED", "lib/python3.11/site-packages/webencodings-0.5.1.dist-info/WHEEL", "lib/python3.11/site-packages/webencodings-0.5.1.dist-info/direct_url.json", "lib/python3.11/site-packages/webencodings-0.5.1.dist-info/top_level.txt", "lib/python3.11/site-packages/webencodings/__init__.py", "lib/python3.11/site-packages/webencodings/labels.py", "lib/python3.11/site-packages/webencodings/mklabels.py", "lib/python3.11/site-packages/webencodings/tests.py", "lib/python3.11/site-packages/webencodings/x_user_defined.py", "lib/python3.11/site-packages/webencodings/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/webencodings/__pycache__/labels.cpython-311.pyc", "lib/python3.11/site-packages/webencodings/__pycache__/mklabels.cpython-311.pyc", "lib/python3.11/site-packages/webencodings/__pycache__/tests.cpython-311.pyc", "lib/python3.11/site-packages/webencodings/__pycache__/x_user_defined.cpython-311.pyc"], "fn": "webencodings-0.5.1-pyhd8ed1ab_3.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/webencodings-0.5.1-pyhd8ed1ab_3", "type": 1}, "md5": "2841eb5bfc75ce15e9a0054b98dcd64d", "name": "webencodings", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/webencodings-0.5.1-pyhd8ed1ab_3.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/webencodings-0.5.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/webencodings-0.5.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "f23bae6ada76095610a77137fb92aec7342723900211c5826d54b4c57907ca56", "sha256_in_prefix": "f23bae6ada76095610a77137fb92aec7342723900211c5826d54b4c57907ca56", "size_in_bytes": 1490}, {"_path": "site-packages/webencodings-0.5.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "9205986e77ff1eb9877bc7201300444f6e9e274db91dcc72fc6ab8b6d9916883", "sha256_in_prefix": "9205986e77ff1eb9877bc7201300444f6e9e274db91dcc72fc6ab8b6d9916883", "size_in_bytes": 2128}, {"_path": "site-packages/webencodings-0.5.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "9417fab5feb335574fd7a2f245c43cbfa56108c060da32ce4741cfdcd5dc3217", "sha256_in_prefix": "9417fab5feb335574fd7a2f245c43cbfa56108c060da32ce4741cfdcd5dc3217", "size_in_bytes": 1377}, {"_path": "site-packages/webencodings-0.5.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/webencodings-0.5.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "a7178d5f925db427b9f0f51260ff6ea6673b8dd44f82f4f41a6f646f5487955c", "sha256_in_prefix": "a7178d5f925db427b9f0f51260ff6ea6673b8dd44f82f4f41a6f646f5487955c", "size_in_bytes": 109}, {"_path": "site-packages/webencodings-0.5.1.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "ce12d9fe22929c7365210be0ce024dde81bed0f6c6e25a1f451f914c82e744e5", "sha256_in_prefix": "ce12d9fe22929c7365210be0ce024dde81bed0f6c6e25a1f451f914c82e744e5", "size_in_bytes": 108}, {"_path": "site-packages/webencodings-0.5.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "6d9b3f6991d27ff3cd95f2070f8f81113266462f7d04974a2eb396eeb42781ea", "sha256_in_prefix": "6d9b3f6991d27ff3cd95f2070f8f81113266462f7d04974a2eb396eeb42781ea", "size_in_bytes": 13}, {"_path": "site-packages/webencodings/__init__.py", "path_type": "hardlink", "sha256": "a8e04922e3f2ff8072607e96fdb360245faa610d83a14f9d2ac0eee724560978", "sha256_in_prefix": "a8e04922e3f2ff8072607e96fdb360245faa610d83a14f9d2ac0eee724560978", "size_in_bytes": 10579}, {"_path": "site-packages/webencodings/labels.py", "path_type": "hardlink", "sha256": "e003bf2b14dd76a1adacbf67b3b9003e36f409c37ac6c088c5b2b7ec763daf71", "sha256_in_prefix": "e003bf2b14dd76a1adacbf67b3b9003e36f409c37ac6c088c5b2b7ec763daf71", "size_in_bytes": 8979}, {"_path": "site-packages/webencodings/mklabels.py", "path_type": "hardlink", "sha256": "19821ecb09e968b9cfd064a273c2c55a0774515bcefe5d4d73a62817ef3b47fe", "sha256_in_prefix": "19821ecb09e968b9cfd064a273c2c55a0774515bcefe5d4d73a62817ef3b47fe", "size_in_bytes": 1305}, {"_path": "site-packages/webencodings/tests.py", "path_type": "hardlink", "sha256": "3ad18bca384d6357ef916d46bcb27f155f59a2a0bd027ca3afbab79314dbccdb", "sha256_in_prefix": "3ad18bca384d6357ef916d46bcb27f155f59a2a0bd027ca3afbab79314dbccdb", "size_in_bytes": 6563}, {"_path": "site-packages/webencodings/x_user_defined.py", "path_type": "hardlink", "sha256": "c8ea9649d9a9cad19f52087f67a258803361a1cf81007cb279e4f5e45af8dad3", "sha256_in_prefix": "c8ea9649d9a9cad19f52087f67a258803361a1cf81007cb279e4f5e45af8dad3", "size_in_bytes": 4307}, {"_path": "lib/python3.11/site-packages/webencodings/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/webencodings/__pycache__/labels.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/webencodings/__pycache__/mklabels.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/webencodings/__pycache__/tests.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/webencodings/__pycache__/x_user_defined.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "19ff205e138bb056a46f9e3839935a2e60bd1cf01c8241a5e172a422fed4f9c6", "size": 15496, "subdir": "noarch", "timestamp": 1733236131000, "url": "https://conda.anaconda.org/conda-forge/noarch/webencodings-0.5.1-pyhd8ed1ab_3.conda", "version": "0.5.1"}