{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["markupsafe >=2.0", "python >=3.9"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/jinja2-3.1.6-pyhd8ed1ab_0", "files": ["lib/python3.11/site-packages/jinja2-3.1.6.dist-info/INSTALLER", "lib/python3.11/site-packages/jinja2-3.1.6.dist-info/METADATA", "lib/python3.11/site-packages/jinja2-3.1.6.dist-info/RECORD", "lib/python3.11/site-packages/jinja2-3.1.6.dist-info/REQUESTED", "lib/python3.11/site-packages/jinja2-3.1.6.dist-info/WHEEL", "lib/python3.11/site-packages/jinja2-3.1.6.dist-info/direct_url.json", "lib/python3.11/site-packages/jinja2-3.1.6.dist-info/entry_points.txt", "lib/python3.11/site-packages/jinja2-3.1.6.dist-info/licenses/LICENSE.txt", "lib/python3.11/site-packages/jinja2/__init__.py", "lib/python3.11/site-packages/jinja2/_identifier.py", "lib/python3.11/site-packages/jinja2/async_utils.py", "lib/python3.11/site-packages/jinja2/bccache.py", "lib/python3.11/site-packages/jinja2/compiler.py", "lib/python3.11/site-packages/jinja2/constants.py", "lib/python3.11/site-packages/jinja2/debug.py", "lib/python3.11/site-packages/jinja2/defaults.py", "lib/python3.11/site-packages/jinja2/environment.py", "lib/python3.11/site-packages/jinja2/exceptions.py", "lib/python3.11/site-packages/jinja2/ext.py", "lib/python3.11/site-packages/jinja2/filters.py", "lib/python3.11/site-packages/jinja2/idtracking.py", "lib/python3.11/site-packages/jinja2/lexer.py", "lib/python3.11/site-packages/jinja2/loaders.py", "lib/python3.11/site-packages/jinja2/meta.py", "lib/python3.11/site-packages/jinja2/nativetypes.py", "lib/python3.11/site-packages/jinja2/nodes.py", "lib/python3.11/site-packages/jinja2/optimizer.py", "lib/python3.11/site-packages/jinja2/parser.py", "lib/python3.11/site-packages/jinja2/py.typed", "lib/python3.11/site-packages/jinja2/runtime.py", "lib/python3.11/site-packages/jinja2/sandbox.py", "lib/python3.11/site-packages/jinja2/tests.py", "lib/python3.11/site-packages/jinja2/utils.py", "lib/python3.11/site-packages/jinja2/visitor.py", "lib/python3.11/site-packages/jinja2/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/_identifier.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/async_utils.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/bccache.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/compiler.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/constants.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/debug.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/defaults.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/environment.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/exceptions.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/ext.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/filters.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/idtracking.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/lexer.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/loaders.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/meta.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/nativetypes.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/nodes.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/optimizer.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/parser.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/runtime.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/sandbox.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/tests.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/jinja2/__pycache__/visitor.cpython-311.pyc"], "fn": "jinja2-3.1.6-pyhd8ed1ab_0.conda", "license": "BSD-3-<PERSON><PERSON>", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/jinja2-3.1.6-pyhd8ed1ab_0", "type": 1}, "md5": "446bd6c8cb26050d528881df495ce646", "name": "jinja2", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/jinja2-3.1.6-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/jinja2-3.1.6.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/jinja2-3.1.6.dist-info/METADATA", "path_type": "hardlink", "sha256": "68c5548fb67c4132a13898d9b31ec50c6bea2abdd915d921f214355c3a6499c8", "sha256_in_prefix": "68c5548fb67c4132a13898d9b31ec50c6bea2abdd915d921f214355c3a6499c8", "size_in_bytes": 2871}, {"_path": "site-packages/jinja2-3.1.6.dist-info/RECORD", "path_type": "hardlink", "sha256": "ff6b04d37d918c978b193d0b423bec7ea53ba69202db88335307e82af193caa8", "sha256_in_prefix": "ff6b04d37d918c978b193d0b423bec7ea53ba69202db88335307e82af193caa8", "size_in_bytes": 3776}, {"_path": "site-packages/jinja2-3.1.6.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/jinja2-3.1.6.dist-info/WHEEL", "path_type": "hardlink", "sha256": "ff6a3334508b59cf776cae1628708ef9e0d410d0e5a3e76073d714deaa2460ee", "sha256_in_prefix": "ff6a3334508b59cf776cae1628708ef9e0d410d0e5a3e76073d714deaa2460ee", "size_in_bytes": 82}, {"_path": "site-packages/jinja2-3.1.6.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "c85711fbb99220cf23dd54cc29f2da3f23fa7d62a7cd80c41f147185b31fca77", "sha256_in_prefix": "c85711fbb99220cf23dd54cc29f2da3f23fa7d62a7cd80c41f147185b31fca77", "size_in_bytes": 102}, {"_path": "site-packages/jinja2-3.1.6.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "38bf39818535783f1cb8f9629227c59e05e97818dac65eab209f0a902ff7afe2", "sha256_in_prefix": "38bf39818535783f1cb8f9629227c59e05e97818dac65eab209f0a902ff7afe2", "size_in_bytes": 58}, {"_path": "site-packages/jinja2-3.1.6.dist-info/licenses/LICENSE.txt", "path_type": "hardlink", "sha256": "3b49dcee4105eb37bac10faf1be260408fe85d252b8e9df2e0979fc1e094437b", "sha256_in_prefix": "3b49dcee4105eb37bac10faf1be260408fe85d252b8e9df2e0979fc1e094437b", "size_in_bytes": 1475}, {"_path": "site-packages/jinja2/__init__.py", "path_type": "hardlink", "sha256": "c717a93bd8bb0c7b2a910ae004476e2ed7e8cf6402b93ebf81b2f85d237585b5", "sha256_in_prefix": "c717a93bd8bb0c7b2a910ae004476e2ed7e8cf6402b93ebf81b2f85d237585b5", "size_in_bytes": 1928}, {"_path": "site-packages/jinja2/_identifier.py", "path_type": "hardlink", "sha256": "ff361cb4d2b346a964fe6bab4cd973ae3bb514524bed56bf223aaa77b8a2da55", "sha256_in_prefix": "ff361cb4d2b346a964fe6bab4cd973ae3bb514524bed56bf223aaa77b8a2da55", "size_in_bytes": 1958}, {"_path": "site-packages/jinja2/async_utils.py", "path_type": "hardlink", "sha256": "bcaf8f76cba8ace3275929c4913de25094489139d880ed93e8c9c6c43807239a", "sha256_in_prefix": "bcaf8f76cba8ace3275929c4913de25094489139d880ed93e8c9c6c43807239a", "size_in_bytes": 2834}, {"_path": "site-packages/jinja2/bccache.py", "path_type": "hardlink", "sha256": "821d2ab3daee9675e8d0f857e634c9cb6507cc8f3016743ade8fefc3b9e1cd18", "sha256_in_prefix": "821d2ab3daee9675e8d0f857e634c9cb6507cc8f3016743ade8fefc3b9e1cd18", "size_in_bytes": 14061}, {"_path": "site-packages/jinja2/compiler.py", "path_type": "hardlink", "sha256": "f51a42425e57f3c0479652623ec1cf876f791e1d2e029bf0149350baeb542de3", "sha256_in_prefix": "f51a42425e57f3c0479652623ec1cf876f791e1d2e029bf0149350baeb542de3", "size_in_bytes": 74131}, {"_path": "site-packages/jinja2/constants.py", "path_type": "hardlink", "sha256": "18ca05c9d045fe476969128fa0ce5c97932f8aab9544b57266d7e9e7ed7a8e0d", "sha256_in_prefix": "18ca05c9d045fe476969128fa0ce5c97932f8aab9544b57266d7e9e7ed7a8e0d", "size_in_bytes": 1433}, {"_path": "site-packages/jinja2/debug.py", "path_type": "hardlink", "sha256": "0a71ea0831ddf81546bed8bff19b13a259d7361037102b18fba9ffda9c14f07c", "sha256_in_prefix": "0a71ea0831ddf81546bed8bff19b13a259d7361037102b18fba9ffda9c14f07c", "size_in_bytes": 6297}, {"_path": "site-packages/jinja2/defaults.py", "path_type": "hardlink", "sha256": "6e805c4b0efc87e969db461b697489b2a900236b8dfe60ff4ed0b27697aae705", "sha256_in_prefix": "6e805c4b0efc87e969db461b697489b2a900236b8dfe60ff4ed0b27697aae705", "size_in_bytes": 1267}, {"_path": "site-packages/jinja2/environment.py", "path_type": "hardlink", "sha256": "f6786b3fb0a1f8d6c65f4d30bf2af8cb2fae84d1ead8e09ceb48201ab9e2fdf9", "sha256_in_prefix": "f6786b3fb0a1f8d6c65f4d30bf2af8cb2fae84d1ead8e09ceb48201ab9e2fdf9", "size_in_bytes": 61513}, {"_path": "site-packages/jinja2/exceptions.py", "path_type": "hardlink", "sha256": "8a81de1eb5b009635a5d7d629c798755b96f73885a3bb017b230a9dc67d6bf1d", "sha256_in_prefix": "8a81de1eb5b009635a5d7d629c798755b96f73885a3bb017b230a9dc67d6bf1d", "size_in_bytes": 5071}, {"_path": "site-packages/jinja2/ext.py", "path_type": "hardlink", "sha256": "e4f1797877e1f265c02315c71d1076c576d7a218bca44de71d23b16baeae4bb1", "sha256_in_prefix": "e4f1797877e1f265c02315c71d1076c576d7a218bca44de71d23b16baeae4bb1", "size_in_bytes": 31875}, {"_path": "site-packages/jinja2/filters.py", "path_type": "hardlink", "sha256": "3d0fc481df67f634a0b671906321782b98f69d8c21508ba584f9f28a691dafe9", "sha256_in_prefix": "3d0fc481df67f634a0b671906321782b98f69d8c21508ba584f9f28a691dafe9", "size_in_bytes": 55212}, {"_path": "site-packages/jinja2/idtracking.py", "path_type": "hardlink", "sha256": "fa5979948a7bde930bdc4ad46222098fbb5d996c5c1ff2250efdf203f862658a", "sha256_in_prefix": "fa5979948a7bde930bdc4ad46222098fbb5d996c5c1ff2250efdf203f862658a", "size_in_bytes": 10555}, {"_path": "site-packages/jinja2/lexer.py", "path_type": "hardlink", "sha256": "2d88988a8e9bafe4dea7d9cf72ea565ec3c4b6396ec37a75994fa534155151f9", "sha256_in_prefix": "2d88988a8e9bafe4dea7d9cf72ea565ec3c4b6396ec37a75994fa534155151f9", "size_in_bytes": 29786}, {"_path": "site-packages/jinja2/loaders.py", "path_type": "hardlink", "sha256": "c08ae7c63bdc6ea8795705b6f0d4a47e8b620eaf2a342c4839215b1948922c1e", "sha256_in_prefix": "c08ae7c63bdc6ea8795705b6f0d4a47e8b620eaf2a342c4839215b1948922c1e", "size_in_bytes": 24055}, {"_path": "site-packages/jinja2/meta.py", "path_type": "hardlink", "sha256": "3930cf91a16f5361e0bf1fba6a4cfbd79e05f012166919af25c045bf0a291f0c", "sha256_in_prefix": "3930cf91a16f5361e0bf1fba6a4cfbd79e05f012166919af25c045bf0a291f0c", "size_in_bytes": 4397}, {"_path": "site-packages/jinja2/nativetypes.py", "path_type": "hardlink", "sha256": "ec620600b54981dc8bf34a1925d4146947f04ade6ada549265b5edd1d35ffcce", "sha256_in_prefix": "ec620600b54981dc8bf34a1925d4146947f04ade6ada549265b5edd1d35ffcce", "size_in_bytes": 4210}, {"_path": "site-packages/jinja2/nodes.py", "path_type": "hardlink", "sha256": "9b50eecdcafaaa1648f0943a57226050d8a78c07f96d0ce28d29839ccb2f531f", "sha256_in_prefix": "9b50eecdcafaaa1648f0943a57226050d8a78c07f96d0ce28d29839ccb2f531f", "size_in_bytes": 34579}, {"_path": "site-packages/jinja2/optimizer.py", "path_type": "hardlink", "sha256": "ac99c246543ba59b04126321b100e00bfa4ac831f13f94cf4bacd53c6b2072ef", "sha256_in_prefix": "ac99c246543ba59b04126321b100e00bfa4ac831f13f94cf4bacd53c6b2072ef", "size_in_bytes": 1651}, {"_path": "site-packages/jinja2/parser.py", "path_type": "hardlink", "sha256": "94b385cb7b0498773921a1074621f5b1056721dda6a14433872789653b5d637d", "sha256_in_prefix": "94b385cb7b0498773921a1074621f5b1056721dda6a14433872789653b5d637d", "size_in_bytes": 40383}, {"_path": "site-packages/jinja2/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/jinja2/runtime.py", "path_type": "hardlink", "sha256": "80393e1af76b8895ea82c19b1e0adc2933f4629eb33d7ce1ceb2290851f78c05", "sha256_in_prefix": "80393e1af76b8895ea82c19b1e0adc2933f4629eb33d7ce1ceb2290851f78c05", "size_in_bytes": 34249}, {"_path": "site-packages/jinja2/sandbox.py", "path_type": "hardlink", "sha256": "330d9a8ad958d88f256bb15885c5f6606f41b5461c2e70f41a1ddddbd7035ab6", "sha256_in_prefix": "330d9a8ad958d88f256bb15885c5f6606f41b5461c2e70f41a1ddddbd7035ab6", "size_in_bytes": 15009}, {"_path": "site-packages/jinja2/tests.py", "path_type": "hardlink", "sha256": "54bb018551675a0f8fc52073d4c8519cd5a03f5a2f5e4de778ed452d031e0bd4", "sha256_in_prefix": "54bb018551675a0f8fc52073d4c8519cd5a03f5a2f5e4de778ed452d031e0bd4", "size_in_bytes": 5926}, {"_path": "site-packages/jinja2/utils.py", "path_type": "hardlink", "sha256": "ad1a77a3d7bb64a4b87f2ad645b10bc8b729b8655315c9e8a1a39ad6ac7f1489", "sha256_in_prefix": "ad1a77a3d7bb64a4b87f2ad645b10bc8b729b8655315c9e8a1a39ad6ac7f1489", "size_in_bytes": 24129}, {"_path": "site-packages/jinja2/visitor.py", "path_type": "hardlink", "sha256": "11c9cbd4f2307ffe1154238cc6c44db91f005c76d2d6a7c074c384da780a273e", "sha256_in_prefix": "11c9cbd4f2307ffe1154238cc6c44db91f005c76d2d6a7c074c384da780a273e", "size_in_bytes": 3557}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/_identifier.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/async_utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/bccache.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/compiler.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/constants.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/debug.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/defaults.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/environment.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/exceptions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/ext.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/filters.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/idtracking.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/lexer.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/loaders.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/meta.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/nativetypes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/nodes.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/optimizer.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/parser.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/runtime.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/sandbox.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/tests.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jinja2/__pycache__/visitor.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "f1ac18b11637ddadc05642e8185a851c7fab5998c6f5470d716812fae943b2af", "size": 112714, "subdir": "noarch", "timestamp": 1741263433000, "url": "https://conda.anaconda.org/conda-forge/noarch/jinja2-3.1.6-pyhd8ed1ab_0.conda", "version": "3.1.6"}