{"build": "py311h1854d6b_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["python", "libcxx >=19", "__osx >=10.13", "python_abi 3.11.* *_cp311"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/debugpy-1.8.17-py311h1854d6b_0", "files": ["bin/debugpy", "bin/debugpy-adapter", "lib/python3.11/site-packages/debugpy/ThirdPartyNotices.txt", "lib/python3.11/site-packages/debugpy/__init__.py", "lib/python3.11/site-packages/debugpy/__main__.py", "lib/python3.11/site-packages/debugpy/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/__pycache__/_version.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/__pycache__/public_api.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/__init__.py", "lib/python3.11/site-packages/debugpy/_vendored/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/__pycache__/_pydevd_packaging.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/__pycache__/_util.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/__pycache__/force_pydevd.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/_pydevd_packaging.py", "lib/python3.11/site-packages/debugpy/_vendored/_util.py", "lib/python3.11/site-packages/debugpy/_vendored/force_pydevd.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/pydev_app_engine_debug_startup.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/pydev_coverage.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/pydev_pysrc.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/pydev_run_in_console.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/pydevconsole.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/pydevd.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/pydevd_file_utils.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/pydevd_tracing.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/setup_pydevd_cython.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__init__.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_calltip_util.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_completer.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_execfile.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_filesystem_encoding.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_getopt.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_imports_tipper.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_jy_imports_tipper.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_log.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_saved_modules.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_sys_patch.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_tipper_common.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_console_utils.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_import_hook.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_imports.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_ipython_console.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_ipython_console_011.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_is_thread_alive.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_localhost.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_log.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_monkey.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_monkey_qt.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_override.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_umd.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_versioncheck.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_calltip_util.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_completer.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_execfile.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_filesystem_encoding.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_getopt.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_imports_tipper.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_jy_imports_tipper.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_log.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_saved_modules.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_sys_patch.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_tipper_common.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/fsnotify/__init__.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/fsnotify/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_console_utils.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_import_hook.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_imports.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_ipython_console.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_ipython_console_011.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_is_thread_alive.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_localhost.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_log.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_monkey.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_monkey_qt.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_override.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_umd.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_versioncheck.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__init__.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/pydev_runfiles.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/pydev_runfiles_coverage.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/pydev_runfiles_nose.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/pydev_runfiles_parallel.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/pydev_runfiles_parallel_client.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/pydev_runfiles_pytest2.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/pydev_runfiles_unittest.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/pydev_runfiles_xml_rpc.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/pydev_runfiles.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/pydev_runfiles_coverage.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/pydev_runfiles_nose.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/pydev_runfiles_parallel.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/pydev_runfiles_parallel_client.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/pydev_runfiles_pytest2.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/pydev_runfiles_unittest.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/pydev_runfiles_xml_rpc.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__init__.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevconsole_code.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_additional_thread_info.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_additional_thread_info_regular.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_api.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_breakpoints.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_bytecode_utils.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_bytecode_utils_py311.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_code_to_source.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_collect_bytecode_info.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_comm.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_comm_constants.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_command_line_handling.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_console.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_constants.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_custom_frames.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_cython_wrapper.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_daemon_thread.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_defaults.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_dont_trace.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_dont_trace_files.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_exec2.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_extension_api.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_extension_utils.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_filtering.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_frame.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_frame_utils.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_gevent_integration.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_import_class.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_io.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_json_debug_options.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_net_command.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_net_command_factory_json.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_net_command_factory_xml.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_plugin_utils.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_process_net_command.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_process_net_command_json.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_referrers.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_reload.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_resolver.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_runpy.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_safe_repr.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_save_locals.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_signature.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_source_mapping.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_stackless.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_suspended_frames.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_thread_lifecycle.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_timeout.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_trace_dispatch.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_trace_dispatch_regular.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_traceproperty.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_utils.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_vars.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_vm_type.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_xml.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/__init__.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/__main__pydevd_gen_debug_adapter_protocol.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/__pycache__/__main__pydevd_gen_debug_adapter_protocol.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/__pycache__/pydevd_base_schema.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/__pycache__/pydevd_schema.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/__pycache__/pydevd_schema_log.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/debugProtocol.json", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/debugProtocolCustom.json", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/pydevd_base_schema.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/pydevd_schema.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/pydevd_schema_log.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevconsole_code.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_additional_thread_info.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_additional_thread_info_regular.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_api.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_breakpoints.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_bytecode_utils.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_bytecode_utils_py311.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_code_to_source.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_collect_bytecode_info.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_comm.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_comm_constants.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_command_line_handling.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_concurrency_analyser/__init__.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_concurrency_analyser/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_concurrency_analyser/__pycache__/pydevd_concurrency_logger.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_concurrency_analyser/__pycache__/pydevd_thread_wrappers.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_concurrency_analyser/pydevd_concurrency_logger.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_concurrency_analyser/pydevd_thread_wrappers.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_console.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_constants.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_custom_frames.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_cython.c", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_cython.cpython-311-darwin.so", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_cython.pxd", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_cython.pyx", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_cython_wrapper.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_daemon_thread.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_defaults.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_dont_trace.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_dont_trace_files.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_exec2.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_extension_api.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_extension_utils.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_filtering.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_frame.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_frame_utils.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_gevent_integration.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_import_class.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_io.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_json_debug_options.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_net_command.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_net_command_factory_json.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_net_command_factory_xml.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_plugin_utils.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_process_net_command.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_process_net_command_json.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_referrers.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_reload.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_resolver.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_runpy.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_safe_repr.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_save_locals.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_signature.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_source_mapping.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_stackless.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_suspended_frames.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_thread_lifecycle.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_timeout.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_trace_dispatch.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_trace_dispatch_regular.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_traceproperty.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_utils.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_vars.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_vm_type.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_xml.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/__init__.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/__pycache__/pydevd_frame_eval_cython_wrapper.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/__pycache__/pydevd_frame_eval_main.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/__pycache__/pydevd_frame_tracing.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/__pycache__/pydevd_modify_bytecode.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/pydevd_frame_eval_cython_wrapper.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/pydevd_frame_eval_main.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/pydevd_frame_evaluator.c", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/pydevd_frame_evaluator.pxd", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/pydevd_frame_evaluator.pyx", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/pydevd_frame_evaluator.template.pyx", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/pydevd_frame_tracing.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/pydevd_modify_bytecode.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/release_mem.h", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/README.txt", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/__init__.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/__pycache__/pydevd_fix_code.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/__init__.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/__pycache__/bytecode.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/__pycache__/cfg.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/__pycache__/concrete.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/__pycache__/flags.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/__pycache__/instr.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/__pycache__/peephole_opt.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/bytecode.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/cfg.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/concrete.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/flags.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/instr.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/peephole_opt.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__init__.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/test_bytecode.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/test_cfg.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/test_code.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/test_concrete.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/test_flags.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/test_instr.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/test_misc.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/test_peephole_opt.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/util_annotation.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/test_bytecode.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/test_cfg.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/test_code.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/test_concrete.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/test_flags.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/test_instr.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/test_misc.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/test_peephole_opt.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/util_annotation.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/pydevd_fix_code.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_sys_monitoring/__pycache__/_pydevd_sys_monitoring.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_sys_monitoring/__pycache__/pydevd_sys_monitoring.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_sys_monitoring/_pydevd_sys_monitoring.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_sys_monitoring/_pydevd_sys_monitoring_cython.c", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_sys_monitoring/_pydevd_sys_monitoring_cython.pxd", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_sys_monitoring/_pydevd_sys_monitoring_cython.pyx", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_sys_monitoring/pydevd_sys_monitoring.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_app_engine_debug_startup.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_coverage.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/README", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__init__.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhook.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhookglut.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhookgtk.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhookgtk3.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhookpyglet.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhookqt4.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhookqt5.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhookqt6.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhooktk.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhookwx.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/matplotlibtools.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/qt.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/qt_for_kernel.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/qt_loaders.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/version.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhook.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhookglut.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhookgtk.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhookgtk3.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhookpyglet.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhookqt4.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhookqt5.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhookqt6.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhooktk.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhookwx.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/matplotlibtools.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/qt.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/qt_for_kernel.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/qt_loaders.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/version.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_pysrc.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_run_in_console.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_sitecustomize/__not_in_default_pythonpath.txt", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_sitecustomize/__pycache__/sitecustomize.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_sitecustomize/sitecustomize.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevconsole.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/README.txt", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/__pycache__/_always_live_program.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/__pycache__/_check.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/__pycache__/_test_attach_to_process.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/__pycache__/_test_attach_to_process_linux.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/__pycache__/add_code_to_python_process.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/__pycache__/attach_pydevd.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/__pycache__/attach_script.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/_always_live_program.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/_check.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/_test_attach_to_process.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/_test_attach_to_process_linux.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/add_code_to_python_process.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/attach.dylib", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/attach_pydevd.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/attach_script.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/py_custom_pyeval_settrace.hpp", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/py_custom_pyeval_settrace_310.hpp", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/py_custom_pyeval_settrace_311.hpp", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/py_custom_pyeval_settrace_common.hpp", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/py_settrace.hpp", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/py_utils.hpp", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/py_version.hpp", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/python.h", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/ref_utils.hpp", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/linux_and_mac/__pycache__/lldb_prepare.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/linux_and_mac/attach.cpp", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/linux_and_mac/compile_linux.sh", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/linux_and_mac/compile_mac.sh", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/linux_and_mac/compile_manylinux.cmd", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/linux_and_mac/lldb_prepare.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__init__.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/breakpoint.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/compat.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/crash.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/debug.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/disasm.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/event.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/interactive.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/module.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/process.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/registry.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/search.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/sql.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/system.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/textio.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/thread.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/util.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/window.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/breakpoint.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/compat.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/crash.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/debug.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/disasm.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/event.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/interactive.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/module.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/process.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/registry.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/search.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/sql.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/system.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/textio.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/thread.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/util.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__init__.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/advapi32.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/context_amd64.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/context_i386.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/dbghelp.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/defines.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/gdi32.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/kernel32.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/ntdll.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/peb_teb.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/psapi.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/shell32.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/shlwapi.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/user32.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/version.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/wtsapi32.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/advapi32.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/context_amd64.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/context_i386.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/dbghelp.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/defines.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/gdi32.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/kernel32.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/ntdll.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/peb_teb.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/psapi.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/shell32.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/shlwapi.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/user32.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/version.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/wtsapi32.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/window.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/attach.cpp", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/attach.h", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/compile_windows.bat", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/inject_dll.cpp", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/py_win_helpers.hpp", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/run_code_in_memory.hpp", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/run_code_on_dllmain.cpp", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/stdafx.cpp", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/stdafx.h", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/targetver.h", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_file_utils.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/__init__.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/__pycache__/django_debug.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/__pycache__/jinja2_debug.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/__pycache__/pydevd_line_validation.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/django_debug.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/README.md", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/__init__.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/__init__.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/__pycache__/pydevd_helpers.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/__pycache__/pydevd_plugin_numpy_types.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/__pycache__/pydevd_plugin_pandas_types.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/__pycache__/pydevd_plugins_django_form_str.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/pydevd_helpers.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/pydevd_plugin_numpy_types.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/pydevd_plugin_pandas_types.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/pydevd_plugins_django_form_str.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/jinja2_debug.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/pydevd_line_validation.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_tracing.py", "lib/python3.11/site-packages/debugpy/_vendored/pydevd/setup_pydevd_cython.py", "lib/python3.11/site-packages/debugpy/_version.py", "lib/python3.11/site-packages/debugpy/adapter/__init__.py", "lib/python3.11/site-packages/debugpy/adapter/__main__.py", "lib/python3.11/site-packages/debugpy/adapter/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/adapter/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/adapter/__pycache__/clients.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/adapter/__pycache__/components.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/adapter/__pycache__/launchers.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/adapter/__pycache__/servers.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/adapter/__pycache__/sessions.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/adapter/clients.py", "lib/python3.11/site-packages/debugpy/adapter/components.py", "lib/python3.11/site-packages/debugpy/adapter/launchers.py", "lib/python3.11/site-packages/debugpy/adapter/servers.py", "lib/python3.11/site-packages/debugpy/adapter/sessions.py", "lib/python3.11/site-packages/debugpy/common/__init__.py", "lib/python3.11/site-packages/debugpy/common/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/common/__pycache__/json.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/common/__pycache__/log.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/common/__pycache__/messaging.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/common/__pycache__/singleton.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/common/__pycache__/sockets.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/common/__pycache__/stacks.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/common/__pycache__/timestamp.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/common/__pycache__/util.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/common/json.py", "lib/python3.11/site-packages/debugpy/common/log.py", "lib/python3.11/site-packages/debugpy/common/messaging.py", "lib/python3.11/site-packages/debugpy/common/singleton.py", "lib/python3.11/site-packages/debugpy/common/sockets.py", "lib/python3.11/site-packages/debugpy/common/stacks.py", "lib/python3.11/site-packages/debugpy/common/timestamp.py", "lib/python3.11/site-packages/debugpy/common/util.py", "lib/python3.11/site-packages/debugpy/launcher/__init__.py", "lib/python3.11/site-packages/debugpy/launcher/__main__.py", "lib/python3.11/site-packages/debugpy/launcher/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/launcher/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/launcher/__pycache__/debuggee.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/launcher/__pycache__/handlers.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/launcher/__pycache__/output.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/launcher/__pycache__/winapi.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/launcher/debuggee.py", "lib/python3.11/site-packages/debugpy/launcher/handlers.py", "lib/python3.11/site-packages/debugpy/launcher/output.py", "lib/python3.11/site-packages/debugpy/launcher/winapi.py", "lib/python3.11/site-packages/debugpy/public_api.py", "lib/python3.11/site-packages/debugpy/server/__init__.py", "lib/python3.11/site-packages/debugpy/server/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/server/__pycache__/api.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/server/__pycache__/attach_pid_injected.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/server/__pycache__/cli.cpython-311.pyc", "lib/python3.11/site-packages/debugpy/server/api.py", "lib/python3.11/site-packages/debugpy/server/attach_pid_injected.py", "lib/python3.11/site-packages/debugpy/server/cli.py", "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/INSTALLER", "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/METADATA", "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/RECORD", "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/REQUESTED", "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/WHEEL", "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/direct_url.json", "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/entry_points.txt", "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/top_level.txt"], "fn": "debugpy-1.8.17-py311h1854d6b_0.conda", "license": "MIT", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/debugpy-1.8.17-py311h1854d6b_0", "type": 1}, "md5": "ebc7f723cde7539c66ec925ec761f792", "name": "debugpy", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/debugpy-1.8.17-py311h1854d6b_0.conda", "paths_data": {"paths": [{"_path": "bin/debugpy", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/bld/rattler-build_debugpy_1758162088/host_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_plac", "sha256": "dbe19493d90bb4f3daf4d095955d16332e3080bf23f73e33a5191bc3eba06aa5", "sha256_in_prefix": "7c948f2bd8ff050e08c3c4a6989a0ace26586c81448571d7a4ebbc8676e36f6f", "size_in_bytes": 440}, {"_path": "bin/debugpy-adapter", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/bld/rattler-build_debugpy_1758162088/host_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_plac", "sha256": "f3abfb87d138e28b60d1097a9c4155f5661a228ba5ad0b3aae476a0be4ac4a15", "sha256_in_prefix": "da81cba8a1e3f8c3cec5ad65ddcc7d69ca6b58bf126167c3a1f331efd78b6b03", "size_in_bytes": 446}, {"_path": "lib/python3.11/site-packages/debugpy/ThirdPartyNotices.txt", "path_type": "hardlink", "sha256": "e3baaa532b9e2303b187b18e05dfed48961d6d16c82d0ae47978b2fd2ce47084", "sha256_in_prefix": "e3baaa532b9e2303b187b18e05dfed48961d6d16c82d0ae47978b2fd2ce47084", "size_in_bytes": 34844}, {"_path": "lib/python3.11/site-packages/debugpy/__init__.py", "path_type": "hardlink", "sha256": "8189afc9d321b8951a770174606f63e384b44a12048234c2de3020ae6673424e", "sha256_in_prefix": "8189afc9d321b8951a770174606f63e384b44a12048234c2de3020ae6673424e", "size_in_bytes": 1080}, {"_path": "lib/python3.11/site-packages/debugpy/__main__.py", "path_type": "hardlink", "sha256": "f5be25012018fa4dbc0b442be2d08d2b970b12df7ee7653236b0ceb8a471b334", "sha256_in_prefix": "f5be25012018fa4dbc0b442be2d08d2b970b12df7ee7653236b0ceb8a471b334", "size_in_bytes": 3258}, {"_path": "lib/python3.11/site-packages/debugpy/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "f38a68ff4f46d43bad800ed7237fe88d79fb2d7cac3f670f6501c892d38832e0", "sha256_in_prefix": "f38a68ff4f46d43bad800ed7237fe88d79fb2d7cac3f670f6501c892d38832e0", "size_in_bytes": 1062}, {"_path": "lib/python3.11/site-packages/debugpy/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "9bdb3907ad7ecded461e2ae5d1e5272020bb62b4c97df7a6cec9148040ce6850", "sha256_in_prefix": "9bdb3907ad7ecded461e2ae5d1e5272020bb62b4c97df7a6cec9148040ce6850", "size_in_bytes": 782}, {"_path": "lib/python3.11/site-packages/debugpy/__pycache__/_version.cpython-311.pyc", "path_type": "hardlink", "sha256": "348a4912150a56d5499b07ec2a04c17e5a32c2a400c857ca870e2a97567e97d1", "sha256_in_prefix": "348a4912150a56d5499b07ec2a04c17e5a32c2a400c857ca870e2a97567e97d1", "size_in_bytes": 822}, {"_path": "lib/python3.11/site-packages/debugpy/__pycache__/public_api.cpython-311.pyc", "path_type": "hardlink", "sha256": "b12bd3761104a22d3b6a3feff393bfcf5b39a4e62490c2af414adc2f087f7f69", "sha256_in_prefix": "b12bd3761104a22d3b6a3feff393bfcf5b39a4e62490c2af414adc2f087f7f69", "size_in_bytes": 10636}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/__init__.py", "path_type": "hardlink", "sha256": "5f62cc11bf547d8cda606938f4c7f5aac98aca416b8613b4d5b31cfa02f27e1d", "sha256_in_prefix": "5f62cc11bf547d8cda606938f4c7f5aac98aca416b8613b4d5b31cfa02f27e1d", "size_in_bytes": 4004}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "a56b22475f1f8fb76e8773bf1ba9067d71238a776e9d8fe469c905e7284f5773", "sha256_in_prefix": "a56b22475f1f8fb76e8773bf1ba9067d71238a776e9d8fe469c905e7284f5773", "size_in_bytes": 6078}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/__pycache__/_pydevd_packaging.cpython-311.pyc", "path_type": "hardlink", "sha256": "2eb25c6699d3df8f987ede966424d504fabeec8c119f037b2ad79e20bd1e1075", "sha256_in_prefix": "2eb25c6699d3df8f987ede966424d504fabeec8c119f037b2ad79e20bd1e1075", "size_in_bytes": 1872}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/__pycache__/_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "25201eabef3429abaa74316276c8466ef7b02373ce34c9438b4158cba9116230", "sha256_in_prefix": "25201eabef3429abaa74316276c8466ef7b02373ce34c9438b4158cba9116230", "size_in_bytes": 2948}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/__pycache__/force_pydevd.cpython-311.pyc", "path_type": "hardlink", "sha256": "eb8640399ab85d119a826dea1562ab61fa2e4a034b66607a5342ca2131ce88fe", "sha256_in_prefix": "eb8640399ab85d119a826dea1562ab61fa2e4a034b66607a5342ca2131ce88fe", "size_in_bytes": 3703}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/_pydevd_packaging.py", "path_type": "hardlink", "sha256": "65e40179e0ed1b78bbe5709052cc985f709f2387c034193d9c23b0a4b275c871", "sha256_in_prefix": "65e40179e0ed1b78bbe5709052cc985f709f2387c034193d9c23b0a4b275c871", "size_in_bytes": 1293}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/_util.py", "path_type": "hardlink", "sha256": "9e47c5bb238ac0f518d4c9e4bbedfe4982100de51e89d10f9256890b2210813c", "sha256_in_prefix": "9e47c5bb238ac0f518d4c9e4bbedfe4982100de51e89d10f9256890b2210813c", "size_in_bytes": 1899}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/force_pydevd.py", "path_type": "hardlink", "sha256": "ab41dbdcd058bb4e77fefd4baaac1f3273a527b56d3d6b049c14d2a5700bb348", "sha256_in_prefix": "ab41dbdcd058bb4e77fefd4baaac1f3273a527b56d3d6b049c14d2a5700bb348", "size_in_bytes": 3253}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/pydev_app_engine_debug_startup.cpython-311.pyc", "path_type": "hardlink", "sha256": "214ab434f531a3d2974477ce6f15d9daa7161f0b25271e7e0db70f3c05b1e6c3", "sha256_in_prefix": "214ab434f531a3d2974477ce6f15d9daa7161f0b25271e7e0db70f3c05b1e6c3", "size_in_bytes": 1262}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/pydev_coverage.cpython-311.pyc", "path_type": "hardlink", "sha256": "afd757c4d4a3d5174830808267de05417c72ccc32765e270d9843967d5eaf61a", "sha256_in_prefix": "afd757c4d4a3d5174830808267de05417c72ccc32765e270d9843967d5eaf61a", "size_in_bytes": 4696}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/pydev_pysrc.cpython-311.pyc", "path_type": "hardlink", "sha256": "32a7e4630dcd28215f2caa456b910bdc5c40c0f390581e6b274eceb45a3ac61b", "sha256_in_prefix": "32a7e4630dcd28215f2caa456b910bdc5c40c0f390581e6b274eceb45a3ac61b", "size_in_bytes": 544}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/pydev_run_in_console.cpython-311.pyc", "path_type": "hardlink", "sha256": "94b54b9e898109ab877c93d3c6824f3609beb1a3eb3a76567f3f7b2de0beb522", "sha256_in_prefix": "94b54b9e898109ab877c93d3c6824f3609beb1a3eb3a76567f3f7b2de0beb522", "size_in_bytes": 6181}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/pydevconsole.cpython-311.pyc", "path_type": "hardlink", "sha256": "c3c63b0cdce25484675c43a0ba07d3036381379e16b5ba98a48f9721e9320cf1", "sha256_in_prefix": "c3c63b0cdce25484675c43a0ba07d3036381379e16b5ba98a48f9721e9320cf1", "size_in_bytes": 23986}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/pydevd.cpython-311.pyc", "path_type": "hardlink", "sha256": "a6ce7ef5d0e5856f9de24b63a6a3b54a0864bdc1c88af98e07a538fe5f8c5a45", "sha256_in_prefix": "a6ce7ef5d0e5856f9de24b63a6a3b54a0864bdc1c88af98e07a538fe5f8c5a45", "size_in_bytes": 165425}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/pydevd_file_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "77096db59a3704f1c469c3e275f09a829ea5d26a1ece343811295434843a9d88", "sha256_in_prefix": "77096db59a3704f1c469c3e275f09a829ea5d26a1ece343811295434843a9d88", "size_in_bytes": 37521}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/pydevd_tracing.cpython-311.pyc", "path_type": "hardlink", "sha256": "e0fd03abec5f779653d160dd556d02e160ce3173133fa9b613c907bfba145d16", "sha256_in_prefix": "e0fd03abec5f779653d160dd556d02e160ce3173133fa9b613c907bfba145d16", "size_in_bytes": 17100}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/__pycache__/setup_pydevd_cython.cpython-311.pyc", "path_type": "hardlink", "sha256": "1e40bbeb14848896938615a0e37114e0e24b5b7d9fe647ef7d3a6863789ba36c", "sha256_in_prefix": "1e40bbeb14848896938615a0e37114e0e24b5b7d9fe647ef7d3a6863789ba36c", "size_in_bytes": 13033}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "bb8ffde580d90ea6545d1197aa7f02ce0dba6bd4a6d39038425e86d9737fddb8", "sha256_in_prefix": "bb8ffde580d90ea6545d1197aa7f02ce0dba6bd4a6d39038425e86d9737fddb8", "size_in_bytes": 447}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_calltip_util.cpython-311.pyc", "path_type": "hardlink", "sha256": "0b1f0cdfbedea310410527c6c7b72e83bea4259cb129ee52ccd75f8318963a8a", "sha256_in_prefix": "0b1f0cdfbedea310410527c6c7b72e83bea4259cb129ee52ccd75f8318963a8a", "size_in_bytes": 6892}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_completer.cpython-311.pyc", "path_type": "hardlink", "sha256": "1b76b2c086fe5040181fff250d9dbdd9d68f36e9656334a813d95ad889ec85ec", "sha256_in_prefix": "1b76b2c086fe5040181fff250d9dbdd9d68f36e9656334a813d95ad889ec85ec", "size_in_bytes": 10736}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_execfile.cpython-311.pyc", "path_type": "hardlink", "sha256": "f6dafa7f819274dc2a3cc238ba52dcff9d0384eb02f3cadc8310a8572bde94ab", "sha256_in_prefix": "f6dafa7f819274dc2a3cc238ba52dcff9d0384eb02f3cadc8310a8572bde94ab", "size_in_bytes": 1218}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_filesystem_encoding.cpython-311.pyc", "path_type": "hardlink", "sha256": "284c1755c4809787f55849700a1be1637daa1788d2ffff7d1cf529e780f7ee79", "sha256_in_prefix": "284c1755c4809787f55849700a1be1637daa1788d2ffff7d1cf529e780f7ee79", "size_in_bytes": 1857}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_getopt.cpython-311.pyc", "path_type": "hardlink", "sha256": "e0d5952b174a23d1d3b7f56ccb327bf251bf1f30e908076baf5a248461c666b6", "sha256_in_prefix": "e0d5952b174a23d1d3b7f56ccb327bf251bf1f30e908076baf5a248461c666b6", "size_in_bytes": 6016}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_imports_tipper.cpython-311.pyc", "path_type": "hardlink", "sha256": "c26cf0a7d725476d2a81a8e94f3f3b66085eac341314fcfab475006178355b1e", "sha256_in_prefix": "c26cf0a7d725476d2a81a8e94f3f3b66085eac341314fcfab475006178355b1e", "size_in_bytes": 13806}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_jy_imports_tipper.cpython-311.pyc", "path_type": "hardlink", "sha256": "46fb6f83a4936882b4edfddff7cbd8347f46ff20014b0f332dd4ba409dac9e34", "sha256_in_prefix": "46fb6f83a4936882b4edfddff7cbd8347f46ff20014b0f332dd4ba409dac9e34", "size_in_bytes": 19118}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_log.cpython-311.pyc", "path_type": "hardlink", "sha256": "7ca62257d24a687b484e803cb3ec0e6a87394534ee734b2163c969aeba63aa77", "sha256_in_prefix": "7ca62257d24a687b484e803cb3ec0e6a87394534ee734b2163c969aeba63aa77", "size_in_bytes": 2167}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_saved_modules.cpython-311.pyc", "path_type": "hardlink", "sha256": "6a485fc593e1bb4d25d3bd4c4460245453b72ca9e8b793eee7f193b94d7dc7d9", "sha256_in_prefix": "6a485fc593e1bb4d25d3bd4c4460245453b72ca9e8b793eee7f193b94d7dc7d9", "size_in_bytes": 7399}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_sys_patch.cpython-311.pyc", "path_type": "hardlink", "sha256": "ab7f28aa9c3f00878f74f7f233089464770e27b3c325ac15c24b718651bf7666", "sha256_in_prefix": "ab7f28aa9c3f00878f74f7f233089464770e27b3c325ac15c24b718651bf7666", "size_in_bytes": 3600}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/_pydev_tipper_common.cpython-311.pyc", "path_type": "hardlink", "sha256": "bc4bf44c11c312f2056b468168c1b8c6fe8e8cac5c6c2c255fede673cde60472", "sha256_in_prefix": "bc4bf44c11c312f2056b468168c1b8c6fe8e8cac5c6c2c255fede673cde60472", "size_in_bytes": 2581}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_console_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "694d6f62200014b1a11380ba9f4d953f23d761d53286e9f4866371dd9a603ebf", "sha256_in_prefix": "694d6f62200014b1a11380ba9f4d953f23d761d53286e9f4866371dd9a603ebf", "size_in_bytes": 31720}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_import_hook.cpython-311.pyc", "path_type": "hardlink", "sha256": "c2b4b55ba5897ca24639e2206d0b9e69035253a72e482291521f2635748bcb31", "sha256_in_prefix": "c2b4b55ba5897ca24639e2206d0b9e69035253a72e482291521f2635748bcb31", "size_in_bytes": 2342}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_imports.cpython-311.pyc", "path_type": "hardlink", "sha256": "4246bf422ebbed22d69ef7734fc55f4b08d8377b2b546b4c45193e085f45a5a4", "sha256_in_prefix": "4246bf422ebbed22d69ef7734fc55f4b08d8377b2b546b4c45193e085f45a5a4", "size_in_bytes": 937}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_ipython_console.cpython-311.pyc", "path_type": "hardlink", "sha256": "862b8e8781ba226aa025660f6ca68a1116845c6ff88772fad6d658d587c0eba1", "sha256_in_prefix": "862b8e8781ba226aa025660f6ca68a1116845c6ff88772fad6d658d587c0eba1", "size_in_bytes": 6307}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_ipython_console_011.cpython-311.pyc", "path_type": "hardlink", "sha256": "2a7c3057a743e6b59b24c63a06a4811934a8985afcfb2d8fddb59eb26b26ddd6", "sha256_in_prefix": "2a7c3057a743e6b59b24c63a06a4811934a8985afcfb2d8fddb59eb26b26ddd6", "size_in_bytes": 19609}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_is_thread_alive.cpython-311.pyc", "path_type": "hardlink", "sha256": "35b57083e3f2c32d9c41909d56da71bdded0b3f302d985ec915668666c8eda0b", "sha256_in_prefix": "35b57083e3f2c32d9c41909d56da71bdded0b3f302d985ec915668666c8eda0b", "size_in_bytes": 1745}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_localhost.cpython-311.pyc", "path_type": "hardlink", "sha256": "c67375ef46066eff5cdeed7607cd5632fc3dc2f7208984fa90e26c3daa657f38", "sha256_in_prefix": "c67375ef46066eff5cdeed7607cd5632fc3dc2f7208984fa90e26c3daa657f38", "size_in_bytes": 3095}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_log.cpython-311.pyc", "path_type": "hardlink", "sha256": "f086155c515daf77bdca5a9c2f2b0c47efdfc3ab14b59089cf9e485ad85eb7bd", "sha256_in_prefix": "f086155c515daf77bdca5a9c2f2b0c47efdfc3ab14b59089cf9e485ad85eb7bd", "size_in_bytes": 12495}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_monkey.cpython-311.pyc", "path_type": "hardlink", "sha256": "2022d013ba9ba8b69ce5e9f3a7d6d35a9ca912e1f85ff2ba427449d915bc872f", "sha256_in_prefix": "2022d013ba9ba8b69ce5e9f3a7d6d35a9ca912e1f85ff2ba427449d915bc872f", "size_in_bytes": 46814}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_monkey_qt.cpython-311.pyc", "path_type": "hardlink", "sha256": "7935cfb78ca603f3bef56abd65bc1d5d2bc4827c9063fe344231140c013cd786", "sha256_in_prefix": "7935cfb78ca603f3bef56abd65bc1d5d2bc4827c9063fe344231140c013cd786", "size_in_bytes": 10286}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_override.cpython-311.pyc", "path_type": "hardlink", "sha256": "c2daacfe547d14d3f3580c580b47958bc1cb75a676da3b9ede60414c81ddcf2e", "sha256_in_prefix": "c2daacfe547d14d3f3580c580b47958bc1cb75a676da3b9ede60414c81ddcf2e", "size_in_bytes": 1690}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_umd.cpython-311.pyc", "path_type": "hardlink", "sha256": "f3753a0c2693cff708c2292817f0572454fd76cc7610da6e014bc9cecd765a77", "sha256_in_prefix": "f3753a0c2693cff708c2292817f0572454fd76cc7610da6e014bc9cecd765a77", "size_in_bytes": 8230}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/__pycache__/pydev_versioncheck.cpython-311.pyc", "path_type": "hardlink", "sha256": "aae70c19d6025d982ee924a72b652cdcf57a109ec80b69c0b233903453e000b3", "sha256_in_prefix": "aae70c19d6025d982ee924a72b652cdcf57a109ec80b69c0b233903453e000b3", "size_in_bytes": 1104}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_calltip_util.py", "path_type": "hardlink", "sha256": "98a0aaf4eb3d0b75dc1a9f023678f70cc74ea0684e388114e1a3e41c1606e7c1", "sha256_in_prefix": "98a0aaf4eb3d0b75dc1a9f023678f70cc74ea0684e388114e1a3e41c1606e7c1", "size_in_bytes": 4837}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_completer.py", "path_type": "hardlink", "sha256": "7e6847c85dc2b75384c3a1a8fe792beaede572e30d0340c3c1f4551cbd240d7c", "sha256_in_prefix": "7e6847c85dc2b75384c3a1a8fe792beaede572e30d0340c3c1f4551cbd240d7c", "size_in_bytes": 8802}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_execfile.py", "path_type": "hardlink", "sha256": "afcf1aadf528f97450d20f003aa5469cb5ca576db02edbd1d3eb8cb4a05c6759", "sha256_in_prefix": "afcf1aadf528f97450d20f003aa5469cb5ca576db02edbd1d3eb8cb4a05c6759", "size_in_bytes": 501}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_filesystem_encoding.py", "path_type": "hardlink", "sha256": "e637fbd62fc2ada3d9aa0db896d384e5afce341cadc62f19f7c318ab2888b03f", "sha256_in_prefix": "e637fbd62fc2ada3d9aa0db896d384e5afce341cadc62f19f7c318ab2888b03f", "size_in_bytes": 1144}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_getopt.py", "path_type": "hardlink", "sha256": "a86184be76dbee318698e8aa47c051bf952e3fd83852b1ea8940c0a73a3cb8fc", "sha256_in_prefix": "a86184be76dbee318698e8aa47c051bf952e3fd83852b1ea8940c0a73a3cb8fc", "size_in_bytes": 4562}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_imports_tipper.py", "path_type": "hardlink", "sha256": "f13f86781baa9fb6238553b3904a2cdcef929dcdb2c8f02d21c04ae4d31a1f30", "sha256_in_prefix": "f13f86781baa9fb6238553b3904a2cdcef929dcdb2c8f02d21c04ae4d31a1f30", "size_in_bytes": 12684}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_jy_imports_tipper.py", "path_type": "hardlink", "sha256": "3fa85c317a7c8b6aad2a4f402ae80134febcc5e5be2a012d5c2f997cad2c3cde", "sha256_in_prefix": "3fa85c317a7c8b6aad2a4f402ae80134febcc5e5be2a012d5c2f997cad2c3cde", "size_in_bytes": 17479}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_log.py", "path_type": "hardlink", "sha256": "6deac3029983d693df2db4aed4ce36948f70fff4a0c8b1a66f89173d02c79ac0", "sha256_in_prefix": "6deac3029983d693df2db4aed4ce36948f70fff4a0c8b1a66f89173d02c79ac0", "size_in_bytes": 577}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_saved_modules.py", "path_type": "hardlink", "sha256": "7a17ebd0fa6e91f6f6540c5570060d4619b96c9e9e72ee996fb55756865f9cc4", "sha256_in_prefix": "7a17ebd0fa6e91f6f6540c5570060d4619b96c9e9e72ee996fb55756865f9cc4", "size_in_bytes": 4874}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_sys_patch.py", "path_type": "hardlink", "sha256": "c87a9894be6af43726b83af3afed0006dfdc9ea8b65f493d888743d3f124f41d", "sha256_in_prefix": "c87a9894be6af43726b83af3afed0006dfdc9ea8b65f493d888743d3f124f41d", "size_in_bytes": 2314}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/_pydev_tipper_common.py", "path_type": "hardlink", "sha256": "cc9472d96ffae0629d759be49b8d15fe7efb6d9be7e7622d4ef822b0bc24a5ae", "sha256_in_prefix": "cc9472d96ffae0629d759be49b8d15fe7efb6d9be7e7622d4ef822b0bc24a5ae", "size_in_bytes": 1281}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/fsnotify/__init__.py", "path_type": "hardlink", "sha256": "c974b91b96e8949f56df9a10bf0fd58d8812e9d230d538c2af83e8dd050f6616", "sha256_in_prefix": "c974b91b96e8949f56df9a10bf0fd58d8812e9d230d538c2af83e8dd050f6616", "size_in_bytes": 13055}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/fsnotify/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "1361c511f559568eb2767e661ab66b7d1c8effc41828fd5fc100e7cab32fabfc", "sha256_in_prefix": "1361c511f559568eb2767e661ab66b7d1c8effc41828fd5fc100e7cab32fabfc", "size_in_bytes": 14833}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_console_utils.py", "path_type": "hardlink", "sha256": "ac01e9f9eb6fcc13861c8854b19682e02cb04ff02b774d3f89523bd0d18354c8", "sha256_in_prefix": "ac01e9f9eb6fcc13861c8854b19682e02cb04ff02b774d3f89523bd0d18354c8", "size_in_bytes": 24391}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_import_hook.py", "path_type": "hardlink", "sha256": "68bff8131c653ee9d67ee18c57d51b57aec6e5c4a43ae6a4c0ac9ecebe40b218", "sha256_in_prefix": "68bff8131c653ee9d67ee18c57d51b57aec6e5c4a43ae6a4c0ac9ecebe40b218", "size_in_bytes": 1358}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_imports.py", "path_type": "hardlink", "sha256": "3bc7be48346cc6588ca3ba938a8996d6530702d29a03502d8f5a515405d24dcd", "sha256_in_prefix": "3bc7be48346cc6588ca3ba938a8996d6530702d29a03502d8f5a515405d24dcd", "size_in_bytes": 415}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_ipython_console.py", "path_type": "hardlink", "sha256": "9e73dd0b3dce2a43e8cfa1c91abb4084ca7b7d8aa68bf7fa23db8f3416e9f6ce", "sha256_in_prefix": "9e73dd0b3dce2a43e8cfa1c91abb4084ca7b7d8aa68bf7fa23db8f3416e9f6ce", "size_in_bytes": 3868}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_ipython_console_011.py", "path_type": "hardlink", "sha256": "cb894c43822829fbef81f1ede508b8fc3a2b9fc301445e9b712040f5b9315b8e", "sha256_in_prefix": "cb894c43822829fbef81f1ede508b8fc3a2b9fc301445e9b712040f5b9315b8e", "size_in_bytes": 21267}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_is_thread_alive.py", "path_type": "hardlink", "sha256": "471bec4d8275fef6c0ffd6d502183dcbbe711ac0aec739cce6cbedb8fdd1596f", "sha256_in_prefix": "471bec4d8275fef6c0ffd6d502183dcbbe711ac0aec739cce6cbedb8fdd1596f", "size_in_bytes": 1062}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_localhost.py", "path_type": "hardlink", "sha256": "a5516d45eabb9bc012ab30c2a109fa50d26bf56754204ff62cf071602eefdc15", "sha256_in_prefix": "a5516d45eabb9bc012ab30c2a109fa50d26bf56754204ff62cf071602eefdc15", "size_in_bytes": 2139}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_log.py", "path_type": "hardlink", "sha256": "5d37a881ae8d7c9d88b9b21e375c2d37a4a7d23ee5a66a056e9474ea3a529ec8", "sha256_in_prefix": "5d37a881ae8d7c9d88b9b21e375c2d37a4a7d23ee5a66a056e9474ea3a529ec8", "size_in_bytes": 9605}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_monkey.py", "path_type": "hardlink", "sha256": "8f9f0c5eed60093da12773a6cff853ce16532c1b14ccdfbbc84a9fd69e3a5111", "sha256_in_prefix": "8f9f0c5eed60093da12773a6cff853ce16532c1b14ccdfbbc84a9fd69e3a5111", "size_in_bytes": 45443}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_monkey_qt.py", "path_type": "hardlink", "sha256": "e1e77a5dd27bf54135bf36b3d3d067c7672551d9535693c3af86919ef698c8cf", "sha256_in_prefix": "e1e77a5dd27bf54135bf36b3d3d067c7672551d9535693c3af86919ef698c8cf", "size_in_bytes": 7530}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_override.py", "path_type": "hardlink", "sha256": "64652305bcd5da0b1ac62e0b5ee63338f669f967dbfeed158b11eec1f701000d", "sha256_in_prefix": "64652305bcd5da0b1ac62e0b5ee63338f669f967dbfeed158b11eec1f701000d", "size_in_bytes": 908}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_umd.py", "path_type": "hardlink", "sha256": "ce80ae9e4f947a72ba9a1296154324ee771d3ae02459e1611c3536271a3a941e", "sha256_in_prefix": "ce80ae9e4f947a72ba9a1296154324ee771d3ae02459e1611c3536271a3a941e", "size_in_bytes": 6409}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_bundle/pydev_versioncheck.py", "path_type": "hardlink", "sha256": "1f4e3889e3a0106deb7c56376afe1c6d7a3f7c951f806ed61d7f1718d2aaa13b", "sha256_in_prefix": "1f4e3889e3a0106deb7c56376afe1c6d7a3f7c951f806ed61d7f1718d2aaa13b", "size_in_bytes": 524}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "365d08ca78a01ed54e016d863d6781bd81d2088ce0c0e333719ae991f7559999", "sha256_in_prefix": "365d08ca78a01ed54e016d863d6781bd81d2088ce0c0e333719ae991f7559999", "size_in_bytes": 449}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/pydev_runfiles.cpython-311.pyc", "path_type": "hardlink", "sha256": "27c41cb95e4cf5b0a7702a6344170bec176f48eb58b32fdf544dd9cef7a9656b", "sha256_in_prefix": "27c41cb95e4cf5b0a7702a6344170bec176f48eb58b32fdf544dd9cef7a9656b", "size_in_bytes": 39009}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/pydev_runfiles_coverage.cpython-311.pyc", "path_type": "hardlink", "sha256": "c24d8efcaa50dfb8b28989cdcbbe48d35c49012b410004f0a4f56f9b7eeb953b", "sha256_in_prefix": "c24d8efcaa50dfb8b28989cdcbbe48d35c49012b410004f0a4f56f9b7eeb953b", "size_in_bytes": 3685}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/pydev_runfiles_nose.cpython-311.pyc", "path_type": "hardlink", "sha256": "09bc24a6e0909ec98bb3f3a7832ff87f9918846e0b333e1c10e50439f434bf2c", "sha256_in_prefix": "09bc24a6e0909ec98bb3f3a7832ff87f9918846e0b333e1c10e50439f434bf2c", "size_in_bytes": 9645}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/pydev_runfiles_parallel.cpython-311.pyc", "path_type": "hardlink", "sha256": "f349f9c96322ec15976cb2b02c73d78d587c500c0b1cd6b5e4f1289deb421b2e", "sha256_in_prefix": "f349f9c96322ec15976cb2b02c73d78d587c500c0b1cd6b5e4f1289deb421b2e", "size_in_bytes": 11368}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/pydev_runfiles_parallel_client.cpython-311.pyc", "path_type": "hardlink", "sha256": "9f0742792ba11adee2d16358576c55ff83caa82a7d7b1f521278c6e8e47d49e7", "sha256_in_prefix": "9f0742792ba11adee2d16358576c55ff83caa82a7d7b1f521278c6e8e47d49e7", "size_in_bytes": 9022}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/pydev_runfiles_pytest2.cpython-311.pyc", "path_type": "hardlink", "sha256": "ae91016850e7a2c03166e3a673513c29c7e244f550fb2989365f214c92e70404", "sha256_in_prefix": "ae91016850e7a2c03166e3a673513c29c7e244f550fb2989365f214c92e70404", "size_in_bytes": 11428}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/pydev_runfiles_unittest.cpython-311.pyc", "path_type": "hardlink", "sha256": "06b61c9aa46dee8054eebc687ebab7ca3e19f1ac59defb2b4196fabb025e06b9", "sha256_in_prefix": "06b61c9aa46dee8054eebc687ebab7ca3e19f1ac59defb2b4196fabb025e06b9", "size_in_bytes": 9037}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/__pycache__/pydev_runfiles_xml_rpc.cpython-311.pyc", "path_type": "hardlink", "sha256": "3d81f769cd623cf037e65cf3b69e752d7997ca96cde7f77f3407578c274461f3", "sha256_in_prefix": "3d81f769cd623cf037e65cf3b69e752d7997ca96cde7f77f3407578c274461f3", "size_in_bytes": 10824}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/pydev_runfiles.py", "path_type": "hardlink", "sha256": "a16abf05d1d9f9faae0fa64f29bd93b265ffc848afe6f2e214f8e806582e9c01", "sha256_in_prefix": "a16abf05d1d9f9faae0fa64f29bd93b265ffc848afe6f2e214f8e806582e9c01", "size_in_bytes": 33562}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/pydev_runfiles_coverage.py", "path_type": "hardlink", "sha256": "cf386f511f7db82370ae67565207386bebbc92a33405a5a253691f627721ef89", "sha256_in_prefix": "cf386f511f7db82370ae67565207386bebbc92a33405a5a253691f627721ef89", "size_in_bytes": 3485}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/pydev_runfiles_nose.py", "path_type": "hardlink", "sha256": "65f86d97254b3a63c76ee9e2af6094b05b0772c7ce3192da98bd728abc0c4a52", "sha256_in_prefix": "65f86d97254b3a63c76ee9e2af6094b05b0772c7ce3192da98bd728abc0c4a52", "size_in_bytes": 7762}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/pydev_runfiles_parallel.py", "path_type": "hardlink", "sha256": "5af18c9bb38d7aba44cdf7d8b47e6d2afe71613970ec788e229d55248b71215f", "sha256_in_prefix": "5af18c9bb38d7aba44cdf7d8b47e6d2afe71613970ec788e229d55248b71215f", "size_in_bytes": 9746}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/pydev_runfiles_parallel_client.py", "path_type": "hardlink", "sha256": "fc2368d20d4a55c8cd02d1274f2b41fdd07b9224dc185cbe160bfebf1dde8f41", "sha256_in_prefix": "fc2368d20d4a55c8cd02d1274f2b41fdd07b9224dc185cbe160bfebf1dde8f41", "size_in_bytes": 7906}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/pydev_runfiles_pytest2.py", "path_type": "hardlink", "sha256": "7541b66229c6f0c5e6cb5bafa59c8ac85a97879e99346146f536e16c7636a12b", "sha256_in_prefix": "7541b66229c6f0c5e6cb5bafa59c8ac85a97879e99346146f536e16c7636a12b", "size_in_bytes": 10153}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/pydev_runfiles_unittest.py", "path_type": "hardlink", "sha256": "1f24a6ddc44fc15576c426c3f7864d9ba234d92729a55e9306069a5a9ced2704", "sha256_in_prefix": "1f24a6ddc44fc15576c426c3f7864d9ba234d92729a55e9306069a5a9ced2704", "size_in_bytes": 6707}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydev_runfiles/pydev_runfiles_xml_rpc.py", "path_type": "hardlink", "sha256": "28aae84b2f1ccd5ed7edfec3c732a796de71ddddd8555f325d541fb2d2cee03c", "sha256_in_prefix": "28aae84b2f1ccd5ed7edfec3c732a796de71ddddd8555f325d541fb2d2cee03c", "size_in_bytes": 11093}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "01fee8e845507f960a2ed82e9710b012cebb5626a80e61f4a1e15faea2650a33", "sha256_in_prefix": "01fee8e845507f960a2ed82e9710b012cebb5626a80e61f4a1e15faea2650a33", "size_in_bytes": 448}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevconsole_code.cpython-311.pyc", "path_type": "hardlink", "sha256": "52f6e3d4f9d813b66168f3ef1ba0f594a671bf03b4b6dbe69bf47c57e7463868", "sha256_in_prefix": "52f6e3d4f9d813b66168f3ef1ba0f594a671bf03b4b6dbe69bf47c57e7463868", "size_in_bytes": 22175}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_additional_thread_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "c2ec44aa7af237c9828d6be4714f98bdb82cf39e20ea05359e9cfe5d7c419cf2", "sha256_in_prefix": "c2ec44aa7af237c9828d6be4714f98bdb82cf39e20ea05359e9cfe5d7c419cf2", "size_in_bytes": 1454}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_additional_thread_info_regular.cpython-311.pyc", "path_type": "hardlink", "sha256": "b865bdb9ca1442fffb7f3394d0ec71bc1444167638da55f5f2e03077e7ada57e", "sha256_in_prefix": "b865bdb9ca1442fffb7f3394d0ec71bc1444167638da55f5f2e03077e7ada57e", "size_in_bytes": 9088}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_api.cpython-311.pyc", "path_type": "hardlink", "sha256": "70cdb98eaf6a80734e24899f76f237d1fc9b5e0918274ec0c68f4e4e2b3bdd8a", "sha256_in_prefix": "70cdb98eaf6a80734e24899f76f237d1fc9b5e0918274ec0c68f4e4e2b3bdd8a", "size_in_bytes": 60120}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_breakpoints.cpython-311.pyc", "path_type": "hardlink", "sha256": "90194cb2276bd9676cfa27550bb80e02044aedf119820bafd0e19773bac32497", "sha256_in_prefix": "90194cb2276bd9676cfa27550bb80e02044aedf119820bafd0e19773bac32497", "size_in_bytes": 8734}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_bytecode_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "6ac0b7fc9bc1459a3f1cd69f79a08442a3437c2d732a9cacc21bebf3fdf83ca4", "sha256_in_prefix": "6ac0b7fc9bc1459a3f1cd69f79a08442a3437c2d732a9cacc21bebf3fdf83ca4", "size_in_bytes": 40718}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_bytecode_utils_py311.cpython-311.pyc", "path_type": "hardlink", "sha256": "8b4cd3a97d3389271c01516270e33622d2f5629a49a67723eba904c2f87cfb64", "sha256_in_prefix": "8b4cd3a97d3389271c01516270e33622d2f5629a49a67723eba904c2f87cfb64", "size_in_bytes": 4959}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_code_to_source.cpython-311.pyc", "path_type": "hardlink", "sha256": "ad815649f71439b7e5b562df78ba7fda30a2528ddc88d8a0e15f9ac3241c1cdd", "sha256_in_prefix": "ad815649f71439b7e5b562df78ba7fda30a2528ddc88d8a0e15f9ac3241c1cdd", "size_in_bytes": 30293}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_collect_bytecode_info.cpython-311.pyc", "path_type": "hardlink", "sha256": "92354a12271b6fe7b93960757f6dcf38169d1a491c3503acf01b281293336dc8", "sha256_in_prefix": "92354a12271b6fe7b93960757f6dcf38169d1a491c3503acf01b281293336dc8", "size_in_bytes": 38230}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_comm.cpython-311.pyc", "path_type": "hardlink", "sha256": "12b9b9e4e96e3e4c608d603ea4758287b6c0cd0c04cc4d908c072438f7ad0eb9", "sha256_in_prefix": "12b9b9e4e96e3e4c608d603ea4758287b6c0cd0c04cc4d908c072438f7ad0eb9", "size_in_bytes": 96625}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_comm_constants.cpython-311.pyc", "path_type": "hardlink", "sha256": "9e3c117743bbdebe1f0e60857f8602437e005bac10cd7cd43d188e8c56dfadc3", "sha256_in_prefix": "9e3c117743bbdebe1f0e60857f8602437e005bac10cd7cd43d188e8c56dfadc3", "size_in_bytes": 6298}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_command_line_handling.cpython-311.pyc", "path_type": "hardlink", "sha256": "0be9619ca68fff330cccbf79387701b3c7baa33a4bcd164f1d187d8ab3f5eeac", "sha256_in_prefix": "0be9619ca68fff330cccbf79387701b3c7baa33a4bcd164f1d187d8ab3f5eeac", "size_in_bytes": 8062}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_console.cpython-311.pyc", "path_type": "hardlink", "sha256": "af0a1e370c262f475b4a9285ce757a2a7281d6bdf89120d2cb9e86e51480dffb", "sha256_in_prefix": "af0a1e370c262f475b4a9285ce757a2a7281d6bdf89120d2cb9e86e51480dffb", "size_in_bytes": 12767}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_constants.cpython-311.pyc", "path_type": "hardlink", "sha256": "bb576b6a33f8e1e33404da787c222221111f9f576d247a176698c7c5ef1988b9", "sha256_in_prefix": "bb576b6a33f8e1e33404da787c222221111f9f576d247a176698c7c5ef1988b9", "size_in_bytes": 32237}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_custom_frames.cpython-311.pyc", "path_type": "hardlink", "sha256": "8e9b037e642921e337abd1a88cab06b9803cfa3e15e27abe425d037341b1f93b", "sha256_in_prefix": "8e9b037e642921e337abd1a88cab06b9803cfa3e15e27abe425d037341b1f93b", "size_in_bytes": 5803}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_cython_wrapper.cpython-311.pyc", "path_type": "hardlink", "sha256": "e3841e6343e6ae10ff661c34550b92536e5e670f2b018bd4d702795b1c6831b9", "sha256_in_prefix": "e3841e6343e6ae10ff661c34550b92536e5e670f2b018bd4d702795b1c6831b9", "size_in_bytes": 2053}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_daemon_thread.cpython-311.pyc", "path_type": "hardlink", "sha256": "046d1ee280349fc6d85cac66dec0ddacf09abac0cd2ce12e8035b8a9390e6570", "sha256_in_prefix": "046d1ee280349fc6d85cac66dec0ddacf09abac0cd2ce12e8035b8a9390e6570", "size_in_bytes": 11401}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_defaults.cpython-311.pyc", "path_type": "hardlink", "sha256": "be47be88f510864ecd1982e61b3ba55f49daf3f895637026d5309573829b4553", "sha256_in_prefix": "be47be88f510864ecd1982e61b3ba55f49daf3f895637026d5309573829b4553", "size_in_bytes": 3113}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_dont_trace.cpython-311.pyc", "path_type": "hardlink", "sha256": "b7f5d1b43d5e6e40ca569041cc3f6ce3d710b1d4f50cd72ac10b4253da903d60", "sha256_in_prefix": "b7f5d1b43d5e6e40ca569041cc3f6ce3d710b1d4f50cd72ac10b4253da903d60", "size_in_bytes": 3122}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_dont_trace_files.cpython-311.pyc", "path_type": "hardlink", "sha256": "f684931d6f60f1f8e071736e3475ce4054e0666434895f0e8205b9733e55b4e3", "sha256_in_prefix": "f684931d6f60f1f8e071736e3475ce4054e0666434895f0e8205b9733e55b4e3", "size_in_bytes": 6706}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_exec2.cpython-311.pyc", "path_type": "hardlink", "sha256": "1c1dea56e2ffa13e46f338f48088a1c5ea2769f2c679a4ac7fcf8aa01851eef6", "sha256_in_prefix": "1c1dea56e2ffa13e46f338f48088a1c5ea2769f2c679a4ac7fcf8aa01851eef6", "size_in_bytes": 728}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_extension_api.cpython-311.pyc", "path_type": "hardlink", "sha256": "9bdc017c09fa9b1375a79345ca9a88b5bfe64149c6de3f28550fe2a1644f1e81", "sha256_in_prefix": "9bdc017c09fa9b1375a79345ca9a88b5bfe64149c6de3f28550fe2a1644f1e81", "size_in_bytes": 6241}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_extension_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "29f6788b7766c0d85d6addcdd5b08a312ff4ff05d063a92e0aa02b06cc6fa88f", "sha256_in_prefix": "29f6788b7766c0d85d6addcdd5b08a312ff4ff05d063a92e0aa02b06cc6fa88f", "size_in_bytes": 4411}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_filtering.cpython-311.pyc", "path_type": "hardlink", "sha256": "fd438f5b07b3683f46e55c29bb037ac82ef1e58c4b4c0e932c616ae86729b1c6", "sha256_in_prefix": "fd438f5b07b3683f46e55c29bb037ac82ef1e58c4b4c0e932c616ae86729b1c6", "size_in_bytes": 17206}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_frame.cpython-311.pyc", "path_type": "hardlink", "sha256": "25052b8544b2c80d876572b52d74c22d14f7b846ac1f2ed47c4979c89b44b57d", "sha256_in_prefix": "25052b8544b2c80d876572b52d74c22d14f7b846ac1f2ed47c4979c89b44b57d", "size_in_bytes": 36891}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_frame_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "e1cebbc07ab9bd3d60b7ee5e515317cefa84e08a016a5e107943c93361acc407", "sha256_in_prefix": "e1cebbc07ab9bd3d60b7ee5e515317cefa84e08a016a5e107943c93361acc407", "size_in_bytes": 19539}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_gevent_integration.cpython-311.pyc", "path_type": "hardlink", "sha256": "bdb8b78f9d9201146210cacfee93fbeab99bcd14930fc832b2730d140b964b6a", "sha256_in_prefix": "bdb8b78f9d9201146210cacfee93fbeab99bcd14930fc832b2730d140b964b6a", "size_in_bytes": 4934}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_import_class.cpython-311.pyc", "path_type": "hardlink", "sha256": "ad7fefd4af97d1df42bb140c2e733c6a19f5da97e2120f80ac63336761870262", "sha256_in_prefix": "ad7fefd4af97d1df42bb140c2e733c6a19f5da97e2120f80ac63336761870262", "size_in_bytes": 2674}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_io.cpython-311.pyc", "path_type": "hardlink", "sha256": "b691ab5fca3ca489a62b5c420b460b74417ef9b07c86747f0e75e05dd42ec6a5", "sha256_in_prefix": "b691ab5fca3ca489a62b5c420b460b74417ef9b07c86747f0e75e05dd42ec6a5", "size_in_bytes": 13510}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_json_debug_options.cpython-311.pyc", "path_type": "hardlink", "sha256": "d95a03e3c54bb9b9fa25ab331601557ed76dcbc723eb62449c10d075a09bd1bd", "sha256_in_prefix": "d95a03e3c54bb9b9fa25ab331601557ed76dcbc723eb62449c10d075a09bd1bd", "size_in_bytes": 8027}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_net_command.cpython-311.pyc", "path_type": "hardlink", "sha256": "8270138aab6c9f2955dc0eac560d26cf71106f628f2034e019d98a0341003730", "sha256_in_prefix": "8270138aab6c9f2955dc0eac560d26cf71106f628f2034e019d98a0341003730", "size_in_bytes": 7076}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_net_command_factory_json.cpython-311.pyc", "path_type": "hardlink", "sha256": "2b6631f28a3c63007d7b208b360cb0e287c944ebce048666def6a422b4889cab", "sha256_in_prefix": "2b6631f28a3c63007d7b208b360cb0e287c944ebce048666def6a422b4889cab", "size_in_bytes": 30507}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_net_command_factory_xml.cpython-311.pyc", "path_type": "hardlink", "sha256": "51180dbabdf45989516a94fd907e96c46d8fd0e00dcfe86c0c351b3180ea4d12", "sha256_in_prefix": "51180dbabdf45989516a94fd907e96c46d8fd0e00dcfe86c0c351b3180ea4d12", "size_in_bytes": 32931}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_plugin_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "400274752a9c78a6e3529a6aa31e6f7f9c936568e89a5b44a0ff2deecf653a5e", "sha256_in_prefix": "400274752a9c78a6e3529a6aa31e6f7f9c936568e89a5b44a0ff2deecf653a5e", "size_in_bytes": 9948}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_process_net_command.cpython-311.pyc", "path_type": "hardlink", "sha256": "56d02707f9c5e84053a5c2adf19bc7d4b09026823f8107212a01d2e51660d03e", "sha256_in_prefix": "56d02707f9c5e84053a5c2adf19bc7d4b09026823f8107212a01d2e51660d03e", "size_in_bytes": 39708}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_process_net_command_json.cpython-311.pyc", "path_type": "hardlink", "sha256": "8d2d460b94c43fb281048c086762bb143d894e0403d825c0d4d341640541fb92", "sha256_in_prefix": "8d2d460b94c43fb281048c086762bb143d894e0403d825c0d4d341640541fb92", "size_in_bytes": 65130}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_referrers.cpython-311.pyc", "path_type": "hardlink", "sha256": "026ab4097bbe910836efc99c032fadd8ab788b482eb20dae14432eed67a4ab6d", "sha256_in_prefix": "026ab4097bbe910836efc99c032fadd8ab788b482eb20dae14432eed67a4ab6d", "size_in_bytes": 10742}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_reload.cpython-311.pyc", "path_type": "hardlink", "sha256": "5780362902136b1e062e910f167a9e2d87e76d30d65671b12cdd02ed6cbb7fa5", "sha256_in_prefix": "5780362902136b1e062e910f167a9e2d87e76d30d65671b12cdd02ed6cbb7fa5", "size_in_bytes": 15890}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_resolver.cpython-311.pyc", "path_type": "hardlink", "sha256": "b17b76f66bb851111723b02fe03766a98dac34fed985d8e9069efb173d5aebc9", "sha256_in_prefix": "b17b76f66bb851111723b02fe03766a98dac34fed985d8e9069efb173d5aebc9", "size_in_bytes": 37630}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_runpy.cpython-311.pyc", "path_type": "hardlink", "sha256": "f1069932681d380c15f0d1ae0e9ae1f09efdfd2009b780dff65a627e0f075937", "sha256_in_prefix": "f1069932681d380c15f0d1ae0e9ae1f09efdfd2009b780dff65a627e0f075937", "size_in_bytes": 16626}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_safe_repr.cpython-311.pyc", "path_type": "hardlink", "sha256": "73b8b82009c8776e02779ab659ab18d62bd2a0d0a5d40d420ff80cd9470832db", "sha256_in_prefix": "73b8b82009c8776e02779ab659ab18d62bd2a0d0a5d40d420ff80cd9470832db", "size_in_bytes": 15166}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_save_locals.cpython-311.pyc", "path_type": "hardlink", "sha256": "7e65522d08836c3b08e79359d47bf46dc21c3ea301cdd64dece51a12c79f7f0f", "sha256_in_prefix": "7e65522d08836c3b08e79359d47bf46dc21c3ea301cdd64dece51a12c79f7f0f", "size_in_bytes": 4495}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_signature.cpython-311.pyc", "path_type": "hardlink", "sha256": "2bd667ba41b07baac2df3e3cbd0d8d9af4dad4addf426e298437719825163e3f", "sha256_in_prefix": "2bd667ba41b07baac2df3e3cbd0d8d9af4dad4addf426e298437719825163e3f", "size_in_bytes": 11253}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_source_mapping.cpython-311.pyc", "path_type": "hardlink", "sha256": "f8d135276202616dfcf392009d7a99be97a14aa544478eb0bf50c62bbb2fcb6c", "sha256_in_prefix": "f8d135276202616dfcf392009d7a99be97a14aa544478eb0bf50c62bbb2fcb6c", "size_in_bytes": 8267}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_stackless.cpython-311.pyc", "path_type": "hardlink", "sha256": "9410bb4285d522d6bcaf3cf430da661047486eed9faa6f9c32ff18d560d878ae", "sha256_in_prefix": "9410bb4285d522d6bcaf3cf430da661047486eed9faa6f9c32ff18d560d878ae", "size_in_bytes": 14306}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_suspended_frames.cpython-311.pyc", "path_type": "hardlink", "sha256": "fa447b0e10247a67914f98e1897ff7fe26c50b58223e60338ced9433ddafb33c", "sha256_in_prefix": "fa447b0e10247a67914f98e1897ff7fe26c50b58223e60338ced9433ddafb33c", "size_in_bytes": 26520}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_thread_lifecycle.cpython-311.pyc", "path_type": "hardlink", "sha256": "627a94af64a8e4ed90e032933818fa85eba3a07e34b1ef2c8af195c81b4fadad", "sha256_in_prefix": "627a94af64a8e4ed90e032933818fa85eba3a07e34b1ef2c8af195c81b4fadad", "size_in_bytes": 5305}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_timeout.cpython-311.pyc", "path_type": "hardlink", "sha256": "6d54c0e227ca41b2b2b4e51bea0f93440cf62d7ef252ca5894f14441f8f3237f", "sha256_in_prefix": "6d54c0e227ca41b2b2b4e51bea0f93440cf62d7ef252ca5894f14441f8f3237f", "size_in_bytes": 12074}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_trace_dispatch.cpython-311.pyc", "path_type": "hardlink", "sha256": "042b32c8cba6d0da4b0148a94304714806a667fd4613a796e4f444fcfc7cf4ce", "sha256_in_prefix": "042b32c8cba6d0da4b0148a94304714806a667fd4613a796e4f444fcfc7cf4ce", "size_in_bytes": 4400}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_trace_dispatch_regular.cpython-311.pyc", "path_type": "hardlink", "sha256": "2c56d77b927d82737795c0e067200897d4f618720b8c33d1f08e3cba0642a3f0", "sha256_in_prefix": "2c56d77b927d82737795c0e067200897d4f618720b8c33d1f08e3cba0642a3f0", "size_in_bytes": 15687}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_traceproperty.cpython-311.pyc", "path_type": "hardlink", "sha256": "88277817b1408f6fd4f169a7efa01e07ad7dc8f65fb977fecfed368edb73ba9d", "sha256_in_prefix": "88277817b1408f6fd4f169a7efa01e07ad7dc8f65fb977fecfed368edb73ba9d", "size_in_bytes": 4534}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_utils.cpython-311.pyc", "path_type": "hardlink", "sha256": "933b0da735b6e3f151ceef33afaadff804dd73a391ffee8e392d5995c4c36424", "sha256_in_prefix": "933b0da735b6e3f151ceef33afaadff804dd73a391ffee8e392d5995c4c36424", "size_in_bytes": 23141}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_vars.cpython-311.pyc", "path_type": "hardlink", "sha256": "20d01fe05f778ce56284d2ecc8f1a309fa1f90ee361476f386cd77379747921b", "sha256_in_prefix": "20d01fe05f778ce56284d2ecc8f1a309fa1f90ee361476f386cd77379747921b", "size_in_bytes": 36629}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_vm_type.cpython-311.pyc", "path_type": "hardlink", "sha256": "c136b7743994409e0831106aa69fd92e7b66a582be5719e0f708e037792a9a53", "sha256_in_prefix": "c136b7743994409e0831106aa69fd92e7b66a582be5719e0f708e037792a9a53", "size_in_bytes": 1602}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/__pycache__/pydevd_xml.cpython-311.pyc", "path_type": "hardlink", "sha256": "6bf5aae6b91c285deaba4c1d43ca8f2a3a60869aa7e0ca43711e36a30038d2ac", "sha256_in_prefix": "6bf5aae6b91c285deaba4c1d43ca8f2a3a60869aa7e0ca43711e36a30038d2ac", "size_in_bytes": 18538}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/__main__pydevd_gen_debug_adapter_protocol.py", "path_type": "hardlink", "sha256": "5f7a06d2d6037ef665b3cc3c51e06c1a393eda10f308fa644077a418c4b52c73", "sha256_in_prefix": "5f7a06d2d6037ef665b3cc3c51e06c1a393eda10f308fa644077a418c4b52c73", "size_in_bytes": 23959}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "f2554dc6a0bb44858cd5453cfb31e6333de8d8ae4b78767f1239ec970f37a2a9", "sha256_in_prefix": "f2554dc6a0bb44858cd5453cfb31e6333de8d8ae4b78767f1239ec970f37a2a9", "size_in_bytes": 463}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/__pycache__/__main__pydevd_gen_debug_adapter_protocol.cpython-311.pyc", "path_type": "hardlink", "sha256": "e5f298406ab8473561450a4c07733cd3bc912162640a38e40e80095969a8052a", "sha256_in_prefix": "e5f298406ab8473561450a4c07733cd3bc912162640a38e40e80095969a8052a", "size_in_bytes": 32482}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/__pycache__/pydevd_base_schema.cpython-311.pyc", "path_type": "hardlink", "sha256": "02c0a6a5017ee0e4f1287bbb1cdb5458d739bc6c22675535767dc2b5a1012bb6", "sha256_in_prefix": "02c0a6a5017ee0e4f1287bbb1cdb5458d739bc6c22675535767dc2b5a1012bb6", "size_in_bytes": 7099}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/__pycache__/pydevd_schema.cpython-311.pyc", "path_type": "hardlink", "sha256": "1cc8cea2a40faab42e45afbdf081fe318c25133b3b76d0f1093288204bf7493f", "sha256_in_prefix": "1cc8cea2a40faab42e45afbdf081fe318c25133b3b76d0f1093288204bf7493f", "size_in_bytes": 669975}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/__pycache__/pydevd_schema_log.cpython-311.pyc", "path_type": "hardlink", "sha256": "ba20db4fee201a2890f31fb1151089e0c2db7b9d1403bff98a741c9b939acf3c", "sha256_in_prefix": "ba20db4fee201a2890f31fb1151089e0c2db7b9d1403bff98a741c9b939acf3c", "size_in_bytes": 3213}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/debugProtocol.json", "path_type": "hardlink", "sha256": "183c21b21c8edd5c7163e5edddf106cf0a8e11cf62d37ac17d47a3a847006c99", "sha256_in_prefix": "183c21b21c8edd5c7163e5edddf106cf0a8e11cf62d37ac17d47a3a847006c99", "size_in_bytes": 174580}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/debugProtocolCustom.json", "path_type": "hardlink", "sha256": "abec6679af3cd9c9c75948b25fe9ebe4aacf7b6248b663c9b2e6e2ab4f954c2f", "sha256_in_prefix": "abec6679af3cd9c9c75948b25fe9ebe4aacf7b6248b663c9b2e6e2ab4f954c2f", "size_in_bytes": 10940}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/pydevd_base_schema.py", "path_type": "hardlink", "sha256": "c64ab548549d57e7c04da6d315fa2d04b9480827011863c73212d673b84d331d", "sha256_in_prefix": "c64ab548549d57e7c04da6d315fa2d04b9480827011863c73212d673b84d331d", "size_in_bytes": 4143}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/pydevd_schema.py", "path_type": "hardlink", "sha256": "b27ae99b2249e8774aa629420ad1ba3d8f8c33f4c6db22afd2a441e45387d6d0", "sha256_in_prefix": "b27ae99b2249e8774aa629420ad1ba3d8f8c33f4c6db22afd2a441e45387d6d0", "size_in_bytes": 860496}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/_debug_adapter/pydevd_schema_log.py", "path_type": "hardlink", "sha256": "f3419ebeeca1eeb9075ad39154b781dca168cb7d1623ef437052ebd2f6ab1fac", "sha256_in_prefix": "f3419ebeeca1eeb9075ad39154b781dca168cb7d1623ef437052ebd2f6ab1fac", "size_in_bytes": 1299}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevconsole_code.py", "path_type": "hardlink", "sha256": "c5d15707a330b70a9566023a6ca480fe6e961557ac03feb3546adea5dc6d941b", "sha256_in_prefix": "c5d15707a330b70a9566023a6ca480fe6e961557ac03feb3546adea5dc6d941b", "size_in_bytes": 19489}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_additional_thread_info.py", "path_type": "hardlink", "sha256": "e454cb6284b9e749e7288bf6a2510f2425e0fe51eb993fef7820588360196567", "sha256_in_prefix": "e454cb6284b9e749e7288bf6a2510f2425e0fe51eb993fef7820588360196567", "size_in_bytes": 1685}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_additional_thread_info_regular.py", "path_type": "hardlink", "sha256": "976266e384f7d45a9a4b587abc7610209002b01ce962fe7e4102eeeb5fab0fd8", "sha256_in_prefix": "976266e384f7d45a9a4b587abc7610209002b01ce962fe7e4102eeeb5fab0fd8", "size_in_bytes": 11139}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_api.py", "path_type": "hardlink", "sha256": "edcdb0c75a7dd5e2320394d480bec32334336eed8204a7ca158a16e13ca41056", "sha256_in_prefix": "edcdb0c75a7dd5e2320394d480bec32334336eed8204a7ca158a16e13ca41056", "size_in_bytes": 52496}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_breakpoints.py", "path_type": "hardlink", "sha256": "f4d7bc2edecc4afb5a8ebf55eadc19779f34b82607fe369004da3c74135edcb8", "sha256_in_prefix": "f4d7bc2edecc4afb5a8ebf55eadc19779f34b82607fe369004da3c74135edcb8", "size_in_bytes": 6185}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_bytecode_utils.py", "path_type": "hardlink", "sha256": "ad8c2970f607ab936bce173b14ee9482e2d41dd7b23aa8795e919a7d442060b2", "sha256_in_prefix": "ad8c2970f607ab936bce173b14ee9482e2d41dd7b23aa8795e919a7d442060b2", "size_in_bytes": 29963}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_bytecode_utils_py311.py", "path_type": "hardlink", "sha256": "8094a2481fff7e8663d7ec64e9d6c55aeb5b78affa1a1a39def116c75f72dec4", "sha256_in_prefix": "8094a2481fff7e8663d7ec64e9d6c55aeb5b78affa1a1a39def116c75f72dec4", "size_in_bytes": 3705}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_code_to_source.py", "path_type": "hardlink", "sha256": "1df58cf99a9482c3cf595043c1eb3d22e29a48b1bc641de58437c512eaba872c", "sha256_in_prefix": "1df58cf99a9482c3cf595043c1eb3d22e29a48b1bc641de58437c512eaba872c", "size_in_bytes": 18165}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_collect_bytecode_info.py", "path_type": "hardlink", "sha256": "901b2afa9905e6b7116ea54a61b344aa66ed8c1696359bacf089f83e17d06266", "sha256_in_prefix": "901b2afa9905e6b7116ea54a61b344aa66ed8c1696359bacf089f83e17d06266", "size_in_bytes": 36003}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_comm.py", "path_type": "hardlink", "sha256": "cf7355487003ec8518fa0258c5a7f51e3c00e1145d6b3252b785bb730bf18a49", "sha256_in_prefix": "cf7355487003ec8518fa0258c5a7f51e3c00e1145d6b3252b785bb730bf18a49", "size_in_bytes": 81335}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_comm_constants.py", "path_type": "hardlink", "sha256": "0f324ad02ce20ffcbeba4b4011332c18bfae26daafd36212133e54ddff993717", "sha256_in_prefix": "0f324ad02ce20ffcbeba4b4011332c18bfae26daafd36212133e54ddff993717", "size_in_bytes": 6276}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_command_line_handling.py", "path_type": "hardlink", "sha256": "bfd68c2352a9200a0397b225e09fc4abec4bbbdcb05d4deea33bb272fa66e853", "sha256_in_prefix": "bfd68c2352a9200a0397b225e09fc4abec4bbbdcb05d4deea33bb272fa66e853", "size_in_bytes": 6283}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_concurrency_analyser/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_concurrency_analyser/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "8935dde46de948aaf61f23f4f6da74010f671f46fc80e82618e12eb180a05f88", "sha256_in_prefix": "8935dde46de948aaf61f23f4f6da74010f671f46fc80e82618e12eb180a05f88", "size_in_bytes": 476}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_concurrency_analyser/__pycache__/pydevd_concurrency_logger.cpython-311.pyc", "path_type": "hardlink", "sha256": "84f29a5e83104a6fed02679524314790ddf538820587ec24dd3f2b6bd13e087e", "sha256_in_prefix": "84f29a5e83104a6fed02679524314790ddf538820587ec24dd3f2b6bd13e087e", "size_in_bytes": 18125}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_concurrency_analyser/__pycache__/pydevd_thread_wrappers.cpython-311.pyc", "path_type": "hardlink", "sha256": "9200822fa019d7b5ae524cc0a3ee99b6e7a0d4ecabca124263a55e04acc6ddfa", "sha256_in_prefix": "9200822fa019d7b5ae524cc0a3ee99b6e7a0d4ecabca124263a55e04acc6ddfa", "size_in_bytes": 4599}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_concurrency_analyser/pydevd_concurrency_logger.py", "path_type": "hardlink", "sha256": "b500f2f4beeb1d8a69427c4da303bea81f89096cdbf1a4febbe3f0845512dc78", "sha256_in_prefix": "b500f2f4beeb1d8a69427c4da303bea81f89096cdbf1a4febbe3f0845512dc78", "size_in_bytes": 21190}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_concurrency_analyser/pydevd_thread_wrappers.py", "path_type": "hardlink", "sha256": "453b963cd138a3b23ad22709ffdd90fb0dc3af4f66e69be68d94d5e563e75551", "sha256_in_prefix": "453b963cd138a3b23ad22709ffdd90fb0dc3af4f66e69be68d94d5e563e75551", "size_in_bytes": 2120}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_console.py", "path_type": "hardlink", "sha256": "ce92ab9adeedf0c0c93900124b721657eb74c75ab428a9f58d4abd1a6a2ed1ca", "sha256_in_prefix": "ce92ab9adeedf0c0c93900124b721657eb74c75ab428a9f58d4abd1a6a2ed1ca", "size_in_bytes": 10434}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_constants.py", "path_type": "hardlink", "sha256": "9e7586e11a2d522f883ec360168c394a68ac6253474e4e5b3a1aad97c005cd0e", "sha256_in_prefix": "9e7586e11a2d522f883ec360168c394a68ac6253474e4e5b3a1aad97c005cd0e", "size_in_bytes": 29340}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_custom_frames.py", "path_type": "hardlink", "sha256": "1ef3fa090b77d963e22e99d01732496ec570164451bd8a435e8a8b5a7650de5d", "sha256_in_prefix": "1ef3fa090b77d963e22e99d01732496ec570164451bd8a435e8a8b5a7650de5d", "size_in_bytes": 4538}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_cython.c", "path_type": "hardlink", "sha256": "c5eeeb9128f1e226702014cba0aaad4b9ee81a268245a6ca0467c8feeb06a36e", "sha256_in_prefix": "c5eeeb9128f1e226702014cba0aaad4b9ee81a268245a6ca0467c8feeb06a36e", "size_in_bytes": 2676744}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_cython.cpython-311-darwin.so", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/bld/rattler-build_debugpy_1758162088/host_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_plac", "sha256": "231324c9cc09f7b960a278143672cf76d27a9742a142bfbccdc43746107c70ba", "sha256_in_prefix": "5444753a2198b2132b4f44bf8de458c2e3ddf44c6baa1b37bb9501953a473db4", "size_in_bytes": 565392}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_cython.pxd", "path_type": "hardlink", "sha256": "3fc73b011c8088bfd99d5ad94d6ace37380fae231e97c078fe380887ff09ab6e", "sha256_in_prefix": "3fc73b011c8088bfd99d5ad94d6ace37380fae231e97c078fe380887ff09ab6e", "size_in_bytes": 1732}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_cython.pyx", "path_type": "hardlink", "sha256": "3ab759decb78f73a0ec9148f8a38700f3b9e2cfc91bad3bc9f3834ba0ab92722", "sha256_in_prefix": "3ab759decb78f73a0ec9148f8a38700f3b9e2cfc91bad3bc9f3834ba0ab92722", "size_in_bytes": 101046}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_cython_wrapper.py", "path_type": "hardlink", "sha256": "13f6addba03411fc786d7198721da0cea1690f1eca3ddbb85cd50430e5290781", "sha256_in_prefix": "13f6addba03411fc786d7198721da0cea1690f1eca3ddbb85cd50430e5290781", "size_in_bytes": 1913}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_daemon_thread.py", "path_type": "hardlink", "sha256": "e7a7858d8fbe1d7440d3478f3141bdfe640df02140a509ab8152e8ac8cc81373", "sha256_in_prefix": "e7a7858d8fbe1d7440d3478f3141bdfe640df02140a509ab8152e8ac8cc81373", "size_in_bytes": 8579}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_defaults.py", "path_type": "hardlink", "sha256": "5762c7f4bc48fb51b96409b393119692ca280f6b122e3ab09b779f9df69f7e67", "sha256_in_prefix": "5762c7f4bc48fb51b96409b393119692ca280f6b122e3ab09b779f9df69f7e67", "size_in_bytes": 2358}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_dont_trace.py", "path_type": "hardlink", "sha256": "546a03a77811b4c9a1ab0314a06486e17c03e53ca7ca7278e6871ba53400ec2b", "sha256_in_prefix": "546a03a77811b4c9a1ab0314a06486e17c03e53ca7ca7278e6871ba53400ec2b", "size_in_bytes": 3685}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_dont_trace_files.py", "path_type": "hardlink", "sha256": "41e4d2ba0eecda4c6b78d4a0d393fe4bd0e45172c8d7a1787c5f47aa0c1bfd65", "sha256_in_prefix": "41e4d2ba0eecda4c6b78d4a0d393fe4bd0e45172c8d7a1787c5f47aa0c1bfd65", "size_in_bytes": 6541}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_exec2.py", "path_type": "hardlink", "sha256": "b981fd20225b5628a5c4d3b679611ab669e5cda28618fb2eccf78388257dc6c4", "sha256_in_prefix": "b981fd20225b5628a5c4d3b679611ab669e5cda28618fb2eccf78388257dc6c4", "size_in_bytes": 165}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_extension_api.py", "path_type": "hardlink", "sha256": "9178931633f3c7e724dc1440b28f8f7d55f0996b28c236456a71dbde4f36ae5e", "sha256_in_prefix": "9178931633f3c7e724dc1440b28f8f7d55f0996b28c236456a71dbde4f36ae5e", "size_in_bytes": 4013}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_extension_utils.py", "path_type": "hardlink", "sha256": "b7c65b86e0f99b0d549390c3a4fdd74e6cebd194ec3b936efbcb4c9ac85a5956", "sha256_in_prefix": "b7c65b86e0f99b0d549390c3a4fdd74e6cebd194ec3b936efbcb4c9ac85a5956", "size_in_bytes": 2365}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_filtering.py", "path_type": "hardlink", "sha256": "b12724c59699c9c8e4abc7f5e44154cb7415ad0bfa73b06854c80ed6584334a4", "sha256_in_prefix": "b12724c59699c9c8e4abc7f5e44154cb7415ad0bfa73b06854c80ed6584334a4", "size_in_bytes": 13372}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_frame.py", "path_type": "hardlink", "sha256": "5ceb19255c24ae91f34bf5340e7b87f32f00b9c807496d766203ef67caf5c411", "sha256_in_prefix": "5ceb19255c24ae91f34bf5340e7b87f32f00b9c807496d766203ef67caf5c411", "size_in_bytes": 65836}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_frame_utils.py", "path_type": "hardlink", "sha256": "9463c7784b5cb46b37535c3a43e2e68d59a47703f2a93b3a949da1ee0b50e562", "sha256_in_prefix": "9463c7784b5cb46b37535c3a43e2e68d59a47703f2a93b3a949da1ee0b50e562", "size_in_bytes": 14529}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_gevent_integration.py", "path_type": "hardlink", "sha256": "b4ae57285766f67ae783a89a868297a3ea94e9c1a9c7da9f1274c9ff54502982", "sha256_in_prefix": "b4ae57285766f67ae783a89a868297a3ea94e9c1a9c7da9f1274c9ff54502982", "size_in_bytes": 3957}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_import_class.py", "path_type": "hardlink", "sha256": "2efbc07398c5ff9c6b8e58fe840fc043183333294c4969882af489189cda9697", "sha256_in_prefix": "2efbc07398c5ff9c6b8e58fe840fc043183333294c4969882af489189cda9697", "size_in_bytes": 1849}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_io.py", "path_type": "hardlink", "sha256": "591c89974e576096c046fe553b841eb40e0118ae5dbbcf758f5ed66aa124bb00", "sha256_in_prefix": "591c89974e576096c046fe553b841eb40e0118ae5dbbcf758f5ed66aa124bb00", "size_in_bytes": 8371}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_json_debug_options.py", "path_type": "hardlink", "sha256": "986a4f39bee299e1b42ca70f92cdd78d6d6da23e82b80cf04e48937dd425d976", "sha256_in_prefix": "986a4f39bee299e1b42ca70f92cdd78d6d6da23e82b80cf04e48937dd425d976", "size_in_bytes": 6353}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_net_command.py", "path_type": "hardlink", "sha256": "9e7c563497edc651b0dcd473c69fbe94fb8e74e396b04ca112c041ef3eaacd9a", "sha256_in_prefix": "9e7c563497edc651b0dcd473c69fbe94fb8e74e396b04ca112c041ef3eaacd9a", "size_in_bytes": 4852}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_net_command_factory_json.py", "path_type": "hardlink", "sha256": "8d952d7c10936b1861b97f9362b38a8f88a23866d0834ec6a10988dac0efd4d8", "sha256_in_prefix": "8d952d7c10936b1861b97f9362b38a8f88a23866d0834ec6a10988dac0efd4d8", "size_in_bytes": 25782}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_net_command_factory_xml.py", "path_type": "hardlink", "sha256": "d929e343b877e11cd6b1e08f4d5ac7e30a73378cbb2fac01a52452333dcb87b7", "sha256_in_prefix": "d929e343b877e11cd6b1e08f4d5ac7e30a73378cbb2fac01a52452333dcb87b7", "size_in_bytes": 24342}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_plugin_utils.py", "path_type": "hardlink", "sha256": "04565977432838410f280e6b623ae9afe94743c3625996ea95551aad002c844b", "sha256_in_prefix": "04565977432838410f280e6b623ae9afe94743c3625996ea95551aad002c844b", "size_in_bytes": 7427}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_process_net_command.py", "path_type": "hardlink", "sha256": "c59f5e24a6a5e182a87e05b87766ead05cda3154869deb7b1251543b284562c7", "sha256_in_prefix": "c59f5e24a6a5e182a87e05b87766ead05cda3154869deb7b1251543b284562c7", "size_in_bytes": 36814}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_process_net_command_json.py", "path_type": "hardlink", "sha256": "ffa58710efe799c4ec9c165f1e6b315f57329e5c0ebf694086367c291149cc87", "sha256_in_prefix": "ffa58710efe799c4ec9c165f1e6b315f57329e5c0ebf694086367c291149cc87", "size_in_bytes": 58893}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_referrers.py", "path_type": "hardlink", "sha256": "5d9caf036b5732f074e82914f6d22b85660452a518384584d6f9bfb3d280867e", "sha256_in_prefix": "5d9caf036b5732f074e82914f6d22b85660452a518384584d6f9bfb3d280867e", "size_in_bytes": 9930}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_reload.py", "path_type": "hardlink", "sha256": "0505af829b879fc3a7a2ee86ab9d85687fdba2fa5616297a331e84eeb28f6cd4", "sha256_in_prefix": "0505af829b879fc3a7a2ee86ab9d85687fdba2fa5616297a331e84eeb28f6cd4", "size_in_bytes": 16212}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_resolver.py", "path_type": "hardlink", "sha256": "2f3a971aa42f6d953db56a53ced758ee430468bb839211d2395e1485c12344f0", "sha256_in_prefix": "2f3a971aa42f6d953db56a53ced758ee430468bb839211d2395e1485c12344f0", "size_in_bytes": 30451}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_runpy.py", "path_type": "hardlink", "sha256": "e589bbaaca72d0be57e6007ae3853921abdef3cf1e7f2226a6be9858effd4f75", "sha256_in_prefix": "e589bbaaca72d0be57e6007ae3853921abdef3cf1e7f2226a6be9858effd4f75", "size_in_bytes": 13346}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_safe_repr.py", "path_type": "hardlink", "sha256": "a5e34189d6997763ad191cebc7af929ffb82f5794753badd8d5e270a4a6e19a9", "sha256_in_prefix": "a5e34189d6997763ad191cebc7af929ffb82f5794753badd8d5e270a4a6e19a9", "size_in_bytes": 14787}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_save_locals.py", "path_type": "hardlink", "sha256": "f8687b2f46d095dfa88dc675b20e92b13166ddd26a7ffd5ac13f8bdeb259d069", "sha256_in_prefix": "f8687b2f46d095dfa88dc675b20e92b13166ddd26a7ffd5ac13f8bdeb259d069", "size_in_bytes": 4290}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_signature.py", "path_type": "hardlink", "sha256": "681745bc716ccf133e0f3edd0c0e02a98aaecb871e2d12b2055191894204de96", "sha256_in_prefix": "681745bc716ccf133e0f3edd0c0e02a98aaecb871e2d12b2055191894204de96", "size_in_bytes": 7078}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_source_mapping.py", "path_type": "hardlink", "sha256": "e35d771fd3c263f32222ab66721d6ff037f33954df32f7d72989c90fe119afdb", "sha256_in_prefix": "e35d771fd3c263f32222ab66721d6ff037f33954df32f7d72989c90fe119afdb", "size_in_bytes": 6643}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_stackless.py", "path_type": "hardlink", "sha256": "8c0cfafd38d0bc9c52b610defd1a5843832537d7b4862c5454b6730bbe6bdbc2", "sha256_in_prefix": "8c0cfafd38d0bc9c52b610defd1a5843832537d7b4862c5454b6730bbe6bdbc2", "size_in_bytes": 17333}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_suspended_frames.py", "path_type": "hardlink", "sha256": "f09b1a9e257350ec92d685e4782edc30099ba46cbef0fa0c965f7e4c8e752be5", "sha256_in_prefix": "f09b1a9e257350ec92d685e4782edc30099ba46cbef0fa0c965f7e4c8e752be5", "size_in_bytes": 21720}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_thread_lifecycle.py", "path_type": "hardlink", "sha256": "3add4ea73b477f604a2e16d71b46e5876f5ce79803481aa61998ba9531785248", "sha256_in_prefix": "3add4ea73b477f604a2e16d71b46e5876f5ce79803481aa61998ba9531785248", "size_in_bytes": 3960}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_timeout.py", "path_type": "hardlink", "sha256": "03263b5a2c549e28c46961d1da24252aebcc5ab0e84cb8da889d2327c076ffd2", "sha256_in_prefix": "03263b5a2c549e28c46961d1da24252aebcc5ab0e84cb8da889d2327c076ffd2", "size_in_bytes": 8644}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_trace_dispatch.py", "path_type": "hardlink", "sha256": "b41a5169075253d39a13e135f756d86023011c93fc104c3576bc6ed26325ed1d", "sha256_in_prefix": "b41a5169075253d39a13e135f756d86023011c93fc104c3576bc6ed26325ed1d", "size_in_bytes": 4009}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_trace_dispatch_regular.py", "path_type": "hardlink", "sha256": "28fd8eccb36c38e653ed101b97d8949718f6ccc6c7c2c1b4971fe0af7d29af08", "sha256_in_prefix": "28fd8eccb36c38e653ed101b97d8949718f6ccc6c7c2c1b4971fe0af7d29af08", "size_in_bytes": 23908}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_traceproperty.py", "path_type": "hardlink", "sha256": "2bd4d7020cfd91976181cf870f4ac9f695e3d4525082bad69cbd10a1dbd66a27", "sha256_in_prefix": "2bd4d7020cfd91976181cf870f4ac9f695e3d4525082bad69cbd10a1dbd66a27", "size_in_bytes": 3345}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_utils.py", "path_type": "hardlink", "sha256": "2a9504691cfe5447cfab6d6281d587f3d2f57abc3d5d2e135c6be97768649813", "sha256_in_prefix": "2a9504691cfe5447cfab6d6281d587f3d2f57abc3d5d2e135c6be97768649813", "size_in_bytes": 18336}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_vars.py", "path_type": "hardlink", "sha256": "ee4a231f58753ff106c848eeac99cb6065ce7a43d850135c6cc6db8780dc9c27", "sha256_in_prefix": "ee4a231f58753ff106c848eeac99cb6065ce7a43d850135c6cc6db8780dc9c27", "size_in_bytes": 32343}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_vm_type.py", "path_type": "hardlink", "sha256": "7272878ad719d54f848301a9f30e6e4d29ec9fef4d451930a093f9e9282ccf7d", "sha256_in_prefix": "7272878ad719d54f848301a9f30e6e4d29ec9fef4d451930a093f9e9282ccf7d", "size_in_bytes": 1597}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_bundle/pydevd_xml.py", "path_type": "hardlink", "sha256": "74325bf94ff560a009ce7dbe66e3ae3b792bc4bbf016c5da06d2091c1b70d264", "sha256_in_prefix": "74325bf94ff560a009ce7dbe66e3ae3b792bc4bbf016c5da06d2091c1b70d264", "size_in_bytes": 15933}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "f225794b0660141a1430657723f9a116491977378e78449669405ac476f4cf67", "sha256_in_prefix": "f225794b0660141a1430657723f9a116491977378e78449669405ac476f4cf67", "size_in_bytes": 452}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/__pycache__/pydevd_frame_eval_cython_wrapper.cpython-311.pyc", "path_type": "hardlink", "sha256": "00a4eb1ae68e46cff3f454cf3d057a9f6a86e96c6f192df977ed27883af565d7", "sha256_in_prefix": "00a4eb1ae68e46cff3f454cf3d057a9f6a86e96c6f192df977ed27883af565d7", "size_in_bytes": 1648}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/__pycache__/pydevd_frame_eval_main.cpython-311.pyc", "path_type": "hardlink", "sha256": "ee813289558cd195581d97754887d3715976d7b7511bbb48d1260e1fcb41c624", "sha256_in_prefix": "ee813289558cd195581d97754887d3715976d7b7511bbb48d1260e1fcb41c624", "size_in_bytes": 1951}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/__pycache__/pydevd_frame_tracing.cpython-311.pyc", "path_type": "hardlink", "sha256": "576a1ac7d48aee6c4acd6f7a2e61dc5585c6bc1bc53729669459489d148564f8", "sha256_in_prefix": "576a1ac7d48aee6c4acd6f7a2e61dc5585c6bc1bc53729669459489d148564f8", "size_in_bytes": 5328}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/__pycache__/pydevd_modify_bytecode.cpython-311.pyc", "path_type": "hardlink", "sha256": "176a289b67b694aac7883774ed1d940ed698120072585027b8cc52caba841c4c", "sha256_in_prefix": "176a289b67b694aac7883774ed1d940ed698120072585027b8cc52caba841c4c", "size_in_bytes": 12616}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/pydevd_frame_eval_cython_wrapper.py", "path_type": "hardlink", "sha256": "2508916791464fe04eecff56e8528df06e2c94826fb8e73528c56a5caa6ae761", "sha256_in_prefix": "2508916791464fe04eecff56e8528df06e2c94826fb8e73528c56a5caa6ae761", "size_in_bytes": 1384}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/pydevd_frame_eval_main.py", "path_type": "hardlink", "sha256": "1c776b00f6029cded71c14bbf0733c6ab3ee5b0b549af279ee7580f988bf8016", "sha256_in_prefix": "1c776b00f6029cded71c14bbf0733c6ab3ee5b0b549af279ee7580f988bf8016", "size_in_bytes": 2515}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/pydevd_frame_evaluator.c", "path_type": "hardlink", "sha256": "c933a1187a4b056d808f0dc1e6ebae5d0231ec1b8d512596d75d5736c9c2e041", "sha256_in_prefix": "c933a1187a4b056d808f0dc1e6ebae5d0231ec1b8d512596d75d5736c9c2e041", "size_in_bytes": 1207712}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/pydevd_frame_evaluator.pxd", "path_type": "hardlink", "sha256": "3751bca6e71723e06edb73ed62014f68fe21074118c3a131b8c4192715f4600c", "sha256_in_prefix": "3751bca6e71723e06edb73ed62014f68fe21074118c3a131b8c4192715f4600c", "size_in_bytes": 5455}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/pydevd_frame_evaluator.pyx", "path_type": "hardlink", "sha256": "76b2e53d223f856cdfaab71ebcf1cd1de0e507addd35ff0ec5801c3a4430cc08", "sha256_in_prefix": "76b2e53d223f856cdfaab71ebcf1cd1de0e507addd35ff0ec5801c3a4430cc08", "size_in_bytes": 32109}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/pydevd_frame_evaluator.template.pyx", "path_type": "hardlink", "sha256": "9f8ed007cb499819b800458c37889d54b19cde9e12bb15da7f59cc72b6b5023f", "sha256_in_prefix": "9f8ed007cb499819b800458c37889d54b19cde9e12bb15da7f59cc72b6b5023f", "size_in_bytes": 25163}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/pydevd_frame_tracing.py", "path_type": "hardlink", "sha256": "74781b6d3d31c2848fa0aa6d054c5c411f4a3cd2bd727fa90461f31d16fbcd2e", "sha256_in_prefix": "74781b6d3d31c2848fa0aa6d054c5c411f4a3cd2bd727fa90461f31d16fbcd2e", "size_in_bytes": 4339}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/pydevd_modify_bytecode.py", "path_type": "hardlink", "sha256": "a128873ec68bf5c0ecd666ef73a58f1843c75f31a612878ee59934e569c4dc14", "sha256_in_prefix": "a128873ec68bf5c0ecd666ef73a58f1843c75f31a612878ee59934e569c4dc14", "size_in_bytes": 13908}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/release_mem.h", "path_type": "hardlink", "sha256": "7b8139527301488e03f8470b143394fbc91a290cb8bbd5a0cd467691967103de", "sha256_in_prefix": "7b8139527301488e03f8470b143394fbc91a290cb8bbd5a0cd467691967103de", "size_in_bytes": 84}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/README.txt", "path_type": "hardlink", "sha256": "d5d8f3f7570673c7d6cbebc5b3ea4981fb549ecac7fc38bf05fcbbd9ed75166b", "sha256_in_prefix": "d5d8f3f7570673c7d6cbebc5b3ea4981fb549ecac7fc38bf05fcbbd9ed75166b", "size_in_bytes": 717}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "d6abc1a06e756f718ec7dbf49c3bf221d53edcc94708bb9f936530d826c461cd", "sha256_in_prefix": "d6abc1a06e756f718ec7dbf49c3bf221d53edcc94708bb9f936530d826c461cd", "size_in_bytes": 461}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/__pycache__/pydevd_fix_code.cpython-311.pyc", "path_type": "hardlink", "sha256": "73f03c09b44d541dab505e72d4d8e58208df70adefad7af0e1417e73d790bed3", "sha256_in_prefix": "73f03c09b44d541dab505e72d4d8e58208df70adefad7af0e1417e73d790bed3", "size_in_bytes": 3158}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/__init__.py", "path_type": "hardlink", "sha256": "05081a005e097a99ebca8572c599e2f4e9fb5b15f272572e1cc396a72114b92c", "sha256_in_prefix": "05081a005e097a99ebca8572c599e2f4e9fb5b15f272572e1cc396a72114b92c", "size_in_bytes": 4284}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "76cc0c32c600bfe679dde85545b1e9da9c79a0bf829a575a95d22773f949c5e0", "sha256_in_prefix": "76cc0c32c600bfe679dde85545b1e9da9c79a0bf829a575a95d22773f949c5e0", "size_in_bytes": 5626}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/__pycache__/bytecode.cpython-311.pyc", "path_type": "hardlink", "sha256": "994d5979b45d3502c8d287e55a8305c27ecf956de45f4c1b908b9e9d0fa3459f", "sha256_in_prefix": "994d5979b45d3502c8d287e55a8305c27ecf956de45f4c1b908b9e9d0fa3459f", "size_in_bytes": 12078}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/__pycache__/cfg.cpython-311.pyc", "path_type": "hardlink", "sha256": "eb23e8b2e77cbcd16131e117356278343980fc74090d9ae66f8393beef07ebe5", "sha256_in_prefix": "eb23e8b2e77cbcd16131e117356278343980fc74090d9ae66f8393beef07ebe5", "size_in_bytes": 19754}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/__pycache__/concrete.cpython-311.pyc", "path_type": "hardlink", "sha256": "d6f5f5fd868874f7da6884ddb3308be23d92409c109ea88dbb7ef638bd4eb12a", "sha256_in_prefix": "d6f5f5fd868874f7da6884ddb3308be23d92409c109ea88dbb7ef638bd4eb12a", "size_in_bytes": 28813}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/__pycache__/flags.cpython-311.pyc", "path_type": "hardlink", "sha256": "1782bc0565756bff835d47b81c1d04241b5a791c70f9a754ffe24c9f3ce92877", "sha256_in_prefix": "1782bc0565756bff835d47b81c1d04241b5a791c70f9a754ffe24c9f3ce92877", "size_in_bytes": 5496}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/__pycache__/instr.cpython-311.pyc", "path_type": "hardlink", "sha256": "e6c2308adb7bb124e50ac8a742f938fce667158a385cd7cb8c7e62cdc55c0458", "sha256_in_prefix": "e6c2308adb7bb124e50ac8a742f938fce667158a385cd7cb8c7e62cdc55c0458", "size_in_bytes": 17896}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/__pycache__/peephole_opt.cpython-311.pyc", "path_type": "hardlink", "sha256": "18d431493921e8acfc8f369b12d3da7ce66ec90de81fa22100c5147ebd59d4c2", "sha256_in_prefix": "18d431493921e8acfc8f369b12d3da7ce66ec90de81fa22100c5147ebd59d4c2", "size_in_bytes": 22530}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/bytecode.py", "path_type": "hardlink", "sha256": "f19dd347894e1c969409cb4d505e91a6350b4d67c8f9c56e6ffe4bff58b3c320", "sha256_in_prefix": "f19dd347894e1c969409cb4d505e91a6350b4d67c8f9c56e6ffe4bff58b3c320", "size_in_bytes": 7297}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/cfg.py", "path_type": "hardlink", "sha256": "68b2329f09f556f79d5cfb9289db83f762d776ee128e5ec1a4edb55e112fe582", "sha256_in_prefix": "68b2329f09f556f79d5cfb9289db83f762d776ee128e5ec1a4edb55e112fe582", "size_in_bytes": 15549}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/concrete.py", "path_type": "hardlink", "sha256": "9904e2639f99cd02603d848ac0b9360f4da4679a8f71dd063765f52f234eb774", "sha256_in_prefix": "9904e2639f99cd02603d848ac0b9360f4da4679a8f71dd063765f52f234eb774", "size_in_bytes": 22811}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/flags.py", "path_type": "hardlink", "sha256": "c4c37208387f6ac96c44cfa4d1506a5ba74f0088efb8f782f7c8b007663e0f83", "sha256_in_prefix": "c4c37208387f6ac96c44cfa4d1506a5ba74f0088efb8f782f7c8b007663e0f83", "size_in_bytes": 6013}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/instr.py", "path_type": "hardlink", "sha256": "37794fbd8b33bfe149f861ba241d376238c876b58a4825a802eb0414413be1eb", "sha256_in_prefix": "37794fbd8b33bfe149f861ba241d376238c876b58a4825a802eb0414413be1eb", "size_in_bytes": 11691}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/peephole_opt.py", "path_type": "hardlink", "sha256": "62f6f7d8611413300faeed382d4f72dee31ade56933bb9a4481f1c2dc5949936", "sha256_in_prefix": "62f6f7d8611413300faeed382d4f72dee31ade56933bb9a4481f1c2dc5949936", "size_in_bytes": 16182}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__init__.py", "path_type": "hardlink", "sha256": "bf4ddbdfc2e23df13a6d7953fd0c16d98810d099e70358548df640ab1b2b21df", "sha256_in_prefix": "bf4ddbdfc2e23df13a6d7953fd0c16d98810d099e70358548df640ab1b2b21df", "size_in_bytes": 5094}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "7aba3b88b82ace8427e46c1f4ccd584d6148097def9f3790705c9d8aaa18a1fd", "sha256_in_prefix": "7aba3b88b82ace8427e46c1f4ccd584d6148097def9f3790705c9d8aaa18a1fd", "size_in_bytes": 7710}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/test_bytecode.cpython-311.pyc", "path_type": "hardlink", "sha256": "93ae2b8dee65f6a5b9a3fdd0f029199261baf8217d407c71a5da54918920df53", "sha256_in_prefix": "93ae2b8dee65f6a5b9a3fdd0f029199261baf8217d407c71a5da54918920df53", "size_in_bytes": 26850}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/test_cfg.cpython-311.pyc", "path_type": "hardlink", "sha256": "010d280a85b0e3b59330970d10d5bac3e24b4df230335760a332386d37bfe942", "sha256_in_prefix": "010d280a85b0e3b59330970d10d5bac3e24b4df230335760a332386d37bfe942", "size_in_bytes": 47402}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/test_code.cpython-311.pyc", "path_type": "hardlink", "sha256": "610e0872697acd03deb3616fcad6e61eace2feea2d421d0d3fdf3beda05b90fb", "sha256_in_prefix": "610e0872697acd03deb3616fcad6e61eace2feea2d421d0d3fdf3beda05b90fb", "size_in_bytes": 4348}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/test_concrete.cpython-311.pyc", "path_type": "hardlink", "sha256": "6d32f902f92bd516b8f4e38f245b61d448ac4ff50b4e7d08a155844db088e37e", "sha256_in_prefix": "6d32f902f92bd516b8f4e38f245b61d448ac4ff50b4e7d08a155844db088e37e", "size_in_bytes": 64107}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/test_flags.cpython-311.pyc", "path_type": "hardlink", "sha256": "1956efdc2cbec86f4abe2a0d6fa992cc2abc15978e13289451611b00f9f78881", "sha256_in_prefix": "1956efdc2cbec86f4abe2a0d6fa992cc2abc15978e13289451611b00f9f78881", "size_in_bytes": 10010}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/test_instr.cpython-311.pyc", "path_type": "hardlink", "sha256": "ad45cf6f1edce4fe5896ed5f5f246204de32f5a433ca408e28fff39da8340b67", "sha256_in_prefix": "ad45cf6f1edce4fe5896ed5f5f246204de32f5a433ca408e28fff39da8340b67", "size_in_bytes": 21362}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/test_misc.cpython-311.pyc", "path_type": "hardlink", "sha256": "104ce34319df1ea0eb58a639542e2e1a98f5515f3ad30b0768cf1dbf1a3821e6", "sha256_in_prefix": "104ce34319df1ea0eb58a639542e2e1a98f5515f3ad30b0768cf1dbf1a3821e6", "size_in_bytes": 10648}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/test_peephole_opt.cpython-311.pyc", "path_type": "hardlink", "sha256": "1e157bbde1ee6a3a5881a4456405355b470efd3d682598a201c44f0e208deb81", "sha256_in_prefix": "1e157bbde1ee6a3a5881a4456405355b470efd3d682598a201c44f0e208deb81", "size_in_bytes": 38555}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/__pycache__/util_annotation.cpython-311.pyc", "path_type": "hardlink", "sha256": "f4d4ba30c5ff239eaf00de6063f94a921ba6818ef4c2f8d0a28a81b1c06b64e6", "sha256_in_prefix": "f4d4ba30c5ff239eaf00de6063f94a921ba6818ef4c2f8d0a28a81b1c06b64e6", "size_in_bytes": 1507}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/test_bytecode.py", "path_type": "hardlink", "sha256": "200ce8550c0d475c8a603276bb701f6e799a6078c70e5afec226c9011ad1bf93", "sha256_in_prefix": "200ce8550c0d475c8a603276bb701f6e799a6078c70e5afec226c9011ad1bf93", "size_in_bytes": 16365}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/test_cfg.py", "path_type": "hardlink", "sha256": "6ae6b86f6812a1ccdc0ba93b1132cdbcb9021c585d46cb29d86c3c51c30ed7f5", "sha256_in_prefix": "6ae6b86f6812a1ccdc0ba93b1132cdbcb9021c585d46cb29d86c3c51c30ed7f5", "size_in_bytes": 29127}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/test_code.py", "path_type": "hardlink", "sha256": "25d108833f3cebe83187e24fd714be52dd53815eb0d2fcdc7bae56d25457734b", "sha256_in_prefix": "25d108833f3cebe83187e24fd714be52dd53815eb0d2fcdc7bae56d25457734b", "size_in_bytes": 2518}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/test_concrete.py", "path_type": "hardlink", "sha256": "338074b6a4fb31a1c2f30c74d6f1abf6071ea74888ddff854961ceab0f3901f1", "sha256_in_prefix": "338074b6a4fb31a1c2f30c74d6f1abf6071ea74888ddff854961ceab0f3901f1", "size_in_bytes": 50807}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/test_flags.py", "path_type": "hardlink", "sha256": "a33958b8e31f872aeaab70b4ce6096c83032717ef4388e67feaec2eaef4a9562", "sha256_in_prefix": "a33958b8e31f872aeaab70b4ce6096c83032717ef4388e67feaec2eaef4a9562", "size_in_bytes": 6164}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/test_instr.py", "path_type": "hardlink", "sha256": "af4d0455a8c5e0e11bb987d3e24921ed7630daae80382c290c0d43bff4e283ab", "sha256_in_prefix": "af4d0455a8c5e0e11bb987d3e24921ed7630daae80382c290c0d43bff4e283ab", "size_in_bytes": 11888}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/test_misc.py", "path_type": "hardlink", "sha256": "d1eec2699214298caab3a21936ad557158e6550aa798dfcc8dec9f67a91c86e4", "sha256_in_prefix": "d1eec2699214298caab3a21936ad557158e6550aa798dfcc8dec9f67a91c86e4", "size_in_bytes": 7275}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/test_peephole_opt.py", "path_type": "hardlink", "sha256": "5458293bf45640966d36f09500cd4dada394e012528df906bf0b54c9f9f756d0", "sha256_in_prefix": "5458293bf45640966d36f09500cd4dada394e012528df906bf0b54c9f9f756d0", "size_in_bytes": 33840}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/bytecode/tests/util_annotation.py", "path_type": "hardlink", "sha256": "c738a1143e5d866736c02d5f12534c9f05caf469e922a43af57de2183a378bb2", "sha256_in_prefix": "c738a1143e5d866736c02d5f12534c9f05caf469e922a43af57de2183a378bb2", "size_in_bytes": 478}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_frame_eval/vendored/pydevd_fix_code.py", "path_type": "hardlink", "sha256": "c238b736c5db2081517298bc4b093141062958ebdbd8214d4bb79877119ecb2d", "sha256_in_prefix": "c238b736c5db2081517298bc4b093141062958ebdbd8214d4bb79877119ecb2d", "size_in_bytes": 1806}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_sys_monitoring/__pycache__/_pydevd_sys_monitoring.cpython-311.pyc", "path_type": "hardlink", "sha256": "bb0a97709f0b0ae9133fc0a17cbb378914ac8db1b0475677c7cccd7d6da96420", "sha256_in_prefix": "bb0a97709f0b0ae9133fc0a17cbb378914ac8db1b0475677c7cccd7d6da96420", "size_in_bytes": 55550}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_sys_monitoring/__pycache__/pydevd_sys_monitoring.cpython-311.pyc", "path_type": "hardlink", "sha256": "251c0ad10856723b9956bb26fbcb1f81609316b53209c3dad99aa269ea4522f5", "sha256_in_prefix": "251c0ad10856723b9956bb26fbcb1f81609316b53209c3dad99aa269ea4522f5", "size_in_bytes": 963}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_sys_monitoring/_pydevd_sys_monitoring.py", "path_type": "hardlink", "sha256": "026d46f5681083e911f74d762c45ccf2dea4f39d87e1f0ba0a8998e73b03efe6", "sha256_in_prefix": "026d46f5681083e911f74d762c45ccf2dea4f39d87e1f0ba0a8998e73b03efe6", "size_in_bytes": 75973}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_sys_monitoring/_pydevd_sys_monitoring_cython.c", "path_type": "hardlink", "sha256": "3a1c72debf2bbaf1c238282706586b5e0d55db5f3f5faf9d94b59082ab6369a7", "sha256_in_prefix": "3a1c72debf2bbaf1c238282706586b5e0d55db5f3f5faf9d94b59082ab6369a7", "size_in_bytes": 2081245}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_sys_monitoring/_pydevd_sys_monitoring_cython.pxd", "path_type": "hardlink", "sha256": "f23a523f5678c2242ce2916354219e37bf6088cb7255eb27eee39c5d85d17d4a", "sha256_in_prefix": "f23a523f5678c2242ce2916354219e37bf6088cb7255eb27eee39c5d85d17d4a", "size_in_bytes": 2385}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_sys_monitoring/_pydevd_sys_monitoring_cython.pyx", "path_type": "hardlink", "sha256": "c6dfdc6d10330e4d77170bedf6745b40f46ea7fae11e3089f7b05952473473e8", "sha256_in_prefix": "c6dfdc6d10330e4d77170bedf6745b40f46ea7fae11e3089f7b05952473473e8", "size_in_bytes": 78394}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/_pydevd_sys_monitoring/pydevd_sys_monitoring.py", "path_type": "hardlink", "sha256": "176bc327a3c5ce1ca2bad2120e30eac4a8eb79a308a944d0e215949e661ee5d0", "sha256_in_prefix": "176bc327a3c5ce1ca2bad2120e30eac4a8eb79a308a944d0e215949e661ee5d0", "size_in_bytes": 565}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_app_engine_debug_startup.py", "path_type": "hardlink", "sha256": "181d1cb46ea7dc14aa48ccb98cfde09ea52646670841ae9edd8effd4c3890c26", "sha256_in_prefix": "181d1cb46ea7dc14aa48ccb98cfde09ea52646670841ae9edd8effd4c3890c26", "size_in_bytes": 691}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_coverage.py", "path_type": "hardlink", "sha256": "0b2864f9b74e82c46fb40f00d57a034e7b548255bba967177a396202ccfcd132", "sha256_in_prefix": "0b2864f9b74e82c46fb40f00d57a034e7b548255bba967177a396202ccfcd132", "size_in_bytes": 3204}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/README", "path_type": "hardlink", "sha256": "765fc9b0f9233354f5e194476534ef4de685a32a3dd42d088dfb5f6b618e0e77", "sha256_in_prefix": "765fc9b0f9233354f5e194476534ef4de685a32a3dd42d088dfb5f6b618e0e77", "size_in_bytes": 546}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "c2e10951a90e493f21cb354729b2630a720d82c98adca3de358b40b41d713ca7", "sha256_in_prefix": "c2e10951a90e493f21cb354729b2630a720d82c98adca3de358b40b41d713ca7", "size_in_bytes": 447}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhook.cpython-311.pyc", "path_type": "hardlink", "sha256": "c665b7332ed25392338f5eb8dfd05a17a04c2577974564a13bc209dcfcbc9cb0", "sha256_in_prefix": "c665b7332ed25392338f5eb8dfd05a17a04c2577974564a13bc209dcfcbc9cb0", "size_in_bytes": 23910}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhookglut.cpython-311.pyc", "path_type": "hardlink", "sha256": "d06a1f0a5468e35b7a5e4e48669cc52666e192302a8cd4422e1f40912f45b523", "sha256_in_prefix": "d06a1f0a5468e35b7a5e4e48669cc52666e192302a8cd4422e1f40912f45b523", "size_in_bytes": 4060}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhookgtk.cpython-311.pyc", "path_type": "hardlink", "sha256": "acdd982ae38c9d0e4831997bfe8d804495ef2135c75f46cffdffb7dc0ac76f97", "sha256_in_prefix": "acdd982ae38c9d0e4831997bfe8d804495ef2135c75f46cffdffb7dc0ac76f97", "size_in_bytes": 1334}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhookgtk3.cpython-311.pyc", "path_type": "hardlink", "sha256": "43fa3a1cdb8be4adc6e881532c5eb4a0641df580e994721f6c13ca68f43f1bb9", "sha256_in_prefix": "43fa3a1cdb8be4adc6e881532c5eb4a0641df580e994721f6c13ca68f43f1bb9", "size_in_bytes": 1348}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhookpyglet.cpython-311.pyc", "path_type": "hardlink", "sha256": "7a5b91e8c595a3e02b13ae4b6e5e2d4973c81c54dbdebd5ecc78f4992479937d", "sha256_in_prefix": "7a5b91e8c595a3e02b13ae4b6e5e2d4973c81c54dbdebd5ecc78f4992479937d", "size_in_bytes": 2935}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhookqt4.cpython-311.pyc", "path_type": "hardlink", "sha256": "67dff6d95203281e25a734c73989a80467e4ce0ca326ca30ee877aa9fe839229", "sha256_in_prefix": "67dff6d95203281e25a734c73989a80467e4ce0ca326ca30ee877aa9fe839229", "size_in_bytes": 6449}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhookqt5.cpython-311.pyc", "path_type": "hardlink", "sha256": "a9f6f1aac2070237dc95bdc607e01c8126d9a084f9adae2a32a9b08725acb3b4", "sha256_in_prefix": "a9f6f1aac2070237dc95bdc607e01c8126d9a084f9adae2a32a9b08725acb3b4", "size_in_bytes": 6515}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhookqt6.cpython-311.pyc", "path_type": "hardlink", "sha256": "6d6932ed6e35f062d972928ec5f6afb399cb815eb49ccb3ed9e186a2a3fada17", "sha256_in_prefix": "6d6932ed6e35f062d972928ec5f6afb399cb815eb49ccb3ed9e186a2a3fada17", "size_in_bytes": 6562}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhooktk.cpython-311.pyc", "path_type": "hardlink", "sha256": "7231fc23ed6b4f4289f48f2470994e8475af02e3f3609fa9ab26894e4bc737a4", "sha256_in_prefix": "7231fc23ed6b4f4289f48f2470994e8475af02e3f3609fa9ab26894e4bc737a4", "size_in_bytes": 1121}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/inputhookwx.cpython-311.pyc", "path_type": "hardlink", "sha256": "47bbf58c02d5848bf734cf7d63d2e20a3d27d06ec68e60635dd7a4fbf496a442", "sha256_in_prefix": "47bbf58c02d5848bf734cf7d63d2e20a3d27d06ec68e60635dd7a4fbf496a442", "size_in_bytes": 6926}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/matplotlibtools.cpython-311.pyc", "path_type": "hardlink", "sha256": "8bc91cf8edcb276d6c8563f4e015944416a1615acbac7ae9d56d0c8fa12d6ea2", "sha256_in_prefix": "8bc91cf8edcb276d6c8563f4e015944416a1615acbac7ae9d56d0c8fa12d6ea2", "size_in_bytes": 8304}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/qt.cpython-311.pyc", "path_type": "hardlink", "sha256": "354df58e47647ff345e2a33e486b16cbf81ac5b9302333f4079e88326a0b26b1", "sha256_in_prefix": "354df58e47647ff345e2a33e486b16cbf81ac5b9302333f4079e88326a0b26b1", "size_in_bytes": 1399}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/qt_for_kernel.cpython-311.pyc", "path_type": "hardlink", "sha256": "f8d6aae3cb37da545d4e7a05e211e61028bcaf007db57e72f0c8266a576bab8a", "sha256_in_prefix": "f8d6aae3cb37da545d4e7a05e211e61028bcaf007db57e72f0c8266a576bab8a", "size_in_bytes": 4984}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/qt_loaders.cpython-311.pyc", "path_type": "hardlink", "sha256": "0dd473319ef026712d397a5c9a63b5e0767289acaa06b5b29f0afb8f6fa851a1", "sha256_in_prefix": "0dd473319ef026712d397a5c9a63b5e0767289acaa06b5b29f0afb8f6fa851a1", "size_in_bytes": 13200}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "00c32d50b21af4e8a36e1d7a13dd90b34f1244b7f71db4a471d2d78fec8843ad", "sha256_in_prefix": "00c32d50b21af4e8a36e1d7a13dd90b34f1244b7f71db4a471d2d78fec8843ad", "size_in_bytes": 3005}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhook.py", "path_type": "hardlink", "sha256": "3a1cd2b48f7a1fc919801a49765046fe98520bb2203fb542e1e63f1cc9ede46c", "sha256_in_prefix": "3a1cd2b48f7a1fc919801a49765046fe98520bb2203fb542e1e63f1cc9ede46c", "size_in_bytes": 20592}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhookglut.py", "path_type": "hardlink", "sha256": "275d7b9d68f0da56e833959965fc5faa59d2ccdd7f665036a2cf9a173c82fae9", "sha256_in_prefix": "275d7b9d68f0da56e833959965fc5faa59d2ccdd7f665036a2cf9a173c82fae9", "size_in_bytes": 5769}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhookgtk.py", "path_type": "hardlink", "sha256": "a5c47315751ee53db7a43cab5c7ead7a6f85d505d7c625b931e4d35e72dcaa3b", "sha256_in_prefix": "a5c47315751ee53db7a43cab5c7ead7a6f85d505d7c625b931e4d35e72dcaa3b", "size_in_bytes": 1151}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhookgtk3.py", "path_type": "hardlink", "sha256": "75fa949362d4777e458d744b53608262f6c0a01164138d26d7a6c86a08dd9159", "sha256_in_prefix": "75fa949362d4777e458d744b53608262f6c0a01164138d26d7a6c86a08dd9159", "size_in_bytes": 1149}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhookpyglet.py", "path_type": "hardlink", "sha256": "d31d5222baa3504ecc96ffbdd71cd5dab83cd6025676684f4833efdeb61656d9", "sha256_in_prefix": "d31d5222baa3504ecc96ffbdd71cd5dab83cd6025676684f4833efdeb61656d9", "size_in_bytes": 3359}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhookqt4.py", "path_type": "hardlink", "sha256": "9a012abbbd527b1f731abad8565b2b252acabd39f4b092aae3a7bec18f820bfb", "sha256_in_prefix": "9a012abbbd527b1f731abad8565b2b252acabd39f4b092aae3a7bec18f820bfb", "size_in_bytes": 7449}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhookqt5.py", "path_type": "hardlink", "sha256": "607ea13526c05c6e80b60ad75cfa25ed31dc8ed637e424263ea726e66cd38dbf", "sha256_in_prefix": "607ea13526c05c6e80b60ad75cfa25ed31dc8ed637e424263ea726e66cd38dbf", "size_in_bytes": 7498}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhookqt6.py", "path_type": "hardlink", "sha256": "c1876d1e405cc1981d84a261419942ad5dba0caa0455f02079e63cf8804a6cb2", "sha256_in_prefix": "c1876d1e405cc1981d84a261419942ad5dba0caa0455f02079e63cf8804a6cb2", "size_in_bytes": 7533}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhooktk.py", "path_type": "hardlink", "sha256": "555bdc63980ccefcbc13ba44523aa88f8ef93769fd857afcf7141e8dca1f880c", "sha256_in_prefix": "555bdc63980ccefcbc13ba44523aa88f8ef93769fd857afcf7141e8dca1f880c", "size_in_bytes": 779}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/inputhookwx.py", "path_type": "hardlink", "sha256": "b0070e1f4f83ee2bdb4d35c9ce3db0d73d5fe5f4daa2063420c2f16f41dcbe1b", "sha256_in_prefix": "b0070e1f4f83ee2bdb4d35c9ce3db0d73d5fe5f4daa2063420c2f16f41dcbe1b", "size_in_bytes": 6700}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/matplotlibtools.py", "path_type": "hardlink", "sha256": "afb712f6b8adcc4717f988cf78208ea112d3f91f431bd18a7a523032b75ac771", "sha256_in_prefix": "afb712f6b8adcc4717f988cf78208ea112d3f91f431bd18a7a523032b75ac771", "size_in_bytes": 6700}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/qt.py", "path_type": "hardlink", "sha256": "cf634eda7c02728e979c8a125340496495f8f154feec3b509c43ff9d566efa84", "sha256_in_prefix": "cf634eda7c02728e979c8a125340496495f8f154feec3b509c43ff9d566efa84", "size_in_bytes": 896}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/qt_for_kernel.py", "path_type": "hardlink", "sha256": "6b128dfc338683e3f6a4c468e83039425240488a4a57407ef83b4dc545cc0b45", "sha256_in_prefix": "6b128dfc338683e3f6a4c468e83039425240488a4a57407ef83b4dc545cc0b45", "size_in_bytes": 4089}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/qt_loaders.py", "path_type": "hardlink", "sha256": "93dc6b9771ca0bb2410998f015134b59c3b1382e73bc7e24559693aef0bcba21", "sha256_in_prefix": "93dc6b9771ca0bb2410998f015134b59c3b1382e73bc7e24559693aef0bcba21", "size_in_bytes": 10922}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_ipython/version.py", "path_type": "hardlink", "sha256": "a9677a319666fbf7050e3a876324914d24a60807b0c421ca862fb86a8c9a001f", "sha256_in_prefix": "a9677a319666fbf7050e3a876324914d24a60807b0c421ca862fb86a8c9a001f", "size_in_bytes": 1546}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_pysrc.py", "path_type": "hardlink", "sha256": "ba5b09996cbe95401c522e4f3793072f3fa28056e2116449de186535b6331ba1", "sha256_in_prefix": "ba5b09996cbe95401c522e4f3793072f3fa28056e2116449de186535b6331ba1", "size_in_bytes": 102}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_run_in_console.py", "path_type": "hardlink", "sha256": "107e5aa2b9df5529ca69e2db9ef9c89f8e5d6ebecd19288145b550bf13d25af1", "sha256_in_prefix": "107e5aa2b9df5529ca69e2db9ef9c89f8e5d6ebecd19288145b550bf13d25af1", "size_in_bytes": 4789}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_sitecustomize/__not_in_default_pythonpath.txt", "path_type": "hardlink", "sha256": "86791302ec52156fd38a5830d01b754552eb7c6444de16230264cf9b593eb1cf", "sha256_in_prefix": "86791302ec52156fd38a5830d01b754552eb7c6444de16230264cf9b593eb1cf", "size_in_bytes": 21}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_sitecustomize/__pycache__/sitecustomize.cpython-311.pyc", "path_type": "hardlink", "sha256": "8323c998b59d3c4f89ad8469994f89e1c52b5aba9a1814aa379e59c46117f4e9", "sha256_in_prefix": "8323c998b59d3c4f89ad8469994f89e1c52b5aba9a1814aa379e59c46117f4e9", "size_in_bytes": 7674}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydev_sitecustomize/sitecustomize.py", "path_type": "hardlink", "sha256": "0107a0ee35802400153e2bf6e4907038a3dba244205010ddedcd9608d7744694", "sha256_in_prefix": "0107a0ee35802400153e2bf6e4907038a3dba244205010ddedcd9608d7744694", "size_in_bytes": 9939}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevconsole.py", "path_type": "hardlink", "sha256": "db6cddaa5f29681a427a4fcafe7c7c7e711fecaa620911b1b644c35b7c7bd0cc", "sha256_in_prefix": "db6cddaa5f29681a427a4fcafe7c7c7e711fecaa620911b1b644c35b7c7bd0cc", "size_in_bytes": 21730}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd.py", "path_type": "hardlink", "sha256": "a5a9fc4d257eace3ebfe37869b53527ca1ad483ee5b6b8c5ea6699ec96b46076", "sha256_in_prefix": "a5a9fc4d257eace3ebfe37869b53527ca1ad483ee5b6b8c5ea6699ec96b46076", "size_in_bytes": 159371}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/README.txt", "path_type": "hardlink", "sha256": "3424f34186d417d6e1c284f3f895b39f02dcc5c503bbb3e670fba4ee9f7f9c98", "sha256_in_prefix": "3424f34186d417d6e1c284f3f895b39f02dcc5c503bbb3e670fba4ee9f7f9c98", "size_in_bytes": 987}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/__pycache__/_always_live_program.cpython-311.pyc", "path_type": "hardlink", "sha256": "946af1f920402b848dc2137e5bbd21efcc3a8d53122295f566d998cbe3508659", "sha256_in_prefix": "946af1f920402b848dc2137e5bbd21efcc3a8d53122295f566d998cbe3508659", "size_in_bytes": 2118}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/__pycache__/_check.cpython-311.pyc", "path_type": "hardlink", "sha256": "e83dd46bc578ac8ad935666a9d37e6289739e05abc835d4aa8d193d769268529", "sha256_in_prefix": "e83dd46bc578ac8ad935666a9d37e6289739e05abc835d4aa8d193d769268529", "size_in_bytes": 666}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/__pycache__/_test_attach_to_process.cpython-311.pyc", "path_type": "hardlink", "sha256": "a034c9ccf84caae7d3474d92dcc7b2ae95739c25cc9f022a4676252b1fa9f699", "sha256_in_prefix": "a034c9ccf84caae7d3474d92dcc7b2ae95739c25cc9f022a4676252b1fa9f699", "size_in_bytes": 1069}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/__pycache__/_test_attach_to_process_linux.cpython-311.pyc", "path_type": "hardlink", "sha256": "1e827508d33eda00858da67aeeecd3541c1db7f26acb46887cafbf2976910f12", "sha256_in_prefix": "1e827508d33eda00858da67aeeecd3541c1db7f26acb46887cafbf2976910f12", "size_in_bytes": 3717}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/__pycache__/add_code_to_python_process.cpython-311.pyc", "path_type": "hardlink", "sha256": "1b22d2eb8ef7918b47f48fc9fbc300fe76a4cca755ae59c0cc571f110befb3d3", "sha256_in_prefix": "1b22d2eb8ef7918b47f48fc9fbc300fe76a4cca755ae59c0cc571f110befb3d3", "size_in_bytes": 24814}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/__pycache__/attach_pydevd.cpython-311.pyc", "path_type": "hardlink", "sha256": "215ee01af8f7d488cddeb3bc153e9b95d798816397ba52d7edb2270f6a50232a", "sha256_in_prefix": "215ee01af8f7d488cddeb3bc153e9b95d798816397ba52d7edb2270f6a50232a", "size_in_bytes": 4075}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/__pycache__/attach_script.cpython-311.pyc", "path_type": "hardlink", "sha256": "6813b51f4bd50f0919a7098fcf92fb839a2710037277d4642e6fede70e01f122", "sha256_in_prefix": "6813b51f4bd50f0919a7098fcf92fb839a2710037277d4642e6fede70e01f122", "size_in_bytes": 7775}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/_always_live_program.py", "path_type": "hardlink", "sha256": "20b7a86418aa27384e193edf54ee581392cf7301b678c6b294bde7ceaa173f13", "sha256_in_prefix": "20b7a86418aa27384e193edf54ee581392cf7301b678c6b294bde7ceaa173f13", "size_in_bytes": 727}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/_check.py", "path_type": "hardlink", "sha256": "fd67e79d18208988d064058440aa508095752cceca5cb3fdd6989d02be21bf22", "sha256_in_prefix": "fd67e79d18208988d064058440aa508095752cceca5cb3fdd6989d02be21bf22", "size_in_bytes": 139}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/_test_attach_to_process.py", "path_type": "hardlink", "sha256": "d848ebbed36d12a5b486210f7ca6caf98067907e244cd7e606e8c17a58e8c117", "sha256_in_prefix": "d848ebbed36d12a5b486210f7ca6caf98067907e244cd7e606e8c17a58e8c117", "size_in_bytes": 310}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/_test_attach_to_process_linux.py", "path_type": "hardlink", "sha256": "6749c331e59b19deb8b28ad8a8a8a6f810fc67cf4f5451637dd28c02de751f18", "sha256_in_prefix": "6749c331e59b19deb8b28ad8a8a8a6f810fc67cf4f5451637dd28c02de751f18", "size_in_bytes": 2638}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/add_code_to_python_process.py", "path_type": "hardlink", "sha256": "d4e9e29a22585f692c573db6c5dc9d36115bc561425a04253b1ef7366beb3cff", "sha256_in_prefix": "d4e9e29a22585f692c573db6c5dc9d36115bc561425a04253b1ef7366beb3cff", "size_in_bytes": 23283}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/attach.dylib", "path_type": "hardlink", "sha256": "f6cc378fb7600c9feaf1e3d4a8c35bbe26b982f1b16f8d3b59db2280a75de1b3", "sha256_in_prefix": "f6cc378fb7600c9feaf1e3d4a8c35bbe26b982f1b16f8d3b59db2280a75de1b3", "size_in_bytes": 24576}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/attach_pydevd.py", "path_type": "hardlink", "sha256": "03b13134374af4dc70e8332fdad6440c3f639505a18a978e4eb3b71b8135a00a", "sha256_in_prefix": "03b13134374af4dc70e8332fdad6440c3f639505a18a978e4eb3b71b8135a00a", "size_in_bytes": 2674}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/attach_script.py", "path_type": "hardlink", "sha256": "8600d5bd4a0b95298ae339135f072a517b027680e7757c885f9e76be164bab88", "sha256_in_prefix": "8600d5bd4a0b95298ae339135f072a517b027680e7757c885f9e76be164bab88", "size_in_bytes": 8228}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/py_custom_pyeval_settrace.hpp", "path_type": "hardlink", "sha256": "79e98be9acefda26458c94d6a19c071b49b83ab9c1c70bad21a5628527904b79", "sha256_in_prefix": "79e98be9acefda26458c94d6a19c071b49b83ab9c1c70bad21a5628527904b79", "size_in_bytes": 8591}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/py_custom_pyeval_settrace_310.hpp", "path_type": "hardlink", "sha256": "808f08a5f0b3948b5ef98660d72851208418c2873777dff33ddbbb47a55c4cbd", "sha256_in_prefix": "808f08a5f0b3948b5ef98660d72851208418c2873777dff33ddbbb47a55c4cbd", "size_in_bytes": 4174}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/py_custom_pyeval_settrace_311.hpp", "path_type": "hardlink", "sha256": "491cccd653b5b76684c5b4de6ad89e7b6af75864d7d6f14c18c335ebdf70aecf", "sha256_in_prefix": "491cccd653b5b76684c5b4de6ad89e7b6af75864d7d6f14c18c335ebdf70aecf", "size_in_bytes": 4388}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/py_custom_pyeval_settrace_common.hpp", "path_type": "hardlink", "sha256": "56bfbc154078e3b257a3353b0e08666ea70bc54801d5c791bba447c755c59c3c", "sha256_in_prefix": "56bfbc154078e3b257a3353b0e08666ea70bc54801d5c791bba447c755c59c3c", "size_in_bytes": 1931}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/py_settrace.hpp", "path_type": "hardlink", "sha256": "9625d555b60601bd533758be13d727582f7c7ee754c6228571dac934c694889b", "sha256_in_prefix": "9625d555b60601bd533758be13d727582f7c7ee754c6228571dac934c694889b", "size_in_bytes": 7875}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/py_utils.hpp", "path_type": "hardlink", "sha256": "96a51b8a35e1aff26f1d616e80d18e01246c5a3d3ddb41188069b2fce9d933da", "sha256_in_prefix": "96a51b8a35e1aff26f1d616e80d18e01246c5a3d3ddb41188069b2fce9d933da", "size_in_bytes": 3949}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/py_version.hpp", "path_type": "hardlink", "sha256": "09ee0d30a94f732057ee36b850bfe608ce25cee42ccf25eaa048340dd69ba7eb", "sha256_in_prefix": "09ee0d30a94f732057ee36b850bfe608ce25cee42ccf25eaa048340dd69ba7eb", "size_in_bytes": 3160}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/python.h", "path_type": "hardlink", "sha256": "d6f71bd096cddd23d9e43e174bcc9281f166b8505ee228fc71d76921906135b7", "sha256_in_prefix": "d6f71bd096cddd23d9e43e174bcc9281f166b8505ee228fc71d76921906135b7", "size_in_bytes": 22335}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/common/ref_utils.hpp", "path_type": "hardlink", "sha256": "40dd174ef884cd4d5e562c2f893b04ca0ea3f31ea052a6c96f3b86b2b1f5a9af", "sha256_in_prefix": "40dd174ef884cd4d5e562c2f893b04ca0ea3f31ea052a6c96f3b86b2b1f5a9af", "size_in_bytes": 1537}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/linux_and_mac/__pycache__/lldb_prepare.cpython-311.pyc", "path_type": "hardlink", "sha256": "e8cbfd7301a81963c79f5b8e782536782dfb020d505ddb61f5a3dd569460c4e6", "sha256_in_prefix": "e8cbfd7301a81963c79f5b8e782536782dfb020d505ddb61f5a3dd569460c4e6", "size_in_bytes": 2929}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/linux_and_mac/attach.cpp", "path_type": "hardlink", "sha256": "032865d44079845c6fea1a1c93b03221b6d4f0251ec703006290b71ce8c0ec3e", "sha256_in_prefix": "032865d44079845c6fea1a1c93b03221b6d4f0251ec703006290b71ce8c0ec3e", "size_in_bytes": 3814}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/linux_and_mac/compile_linux.sh", "path_type": "hardlink", "sha256": "e32338a5aec5f973cb2d053a7e913ff2628cbfd92169395a7f039d24852b2104", "sha256_in_prefix": "e32338a5aec5f973cb2d053a7e913ff2628cbfd92169395a7f039d24852b2104", "size_in_bytes": 335}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/linux_and_mac/compile_mac.sh", "path_type": "hardlink", "sha256": "2b16d58dedceac80ff2c5e2b9f5ed0a80063e8ff27a24c996a9723652e0f3b6f", "sha256_in_prefix": "2b16d58dedceac80ff2c5e2b9f5ed0a80063e8ff27a24c996a9723652e0f3b6f", "size_in_bytes": 713}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/linux_and_mac/compile_manylinux.cmd", "path_type": "hardlink", "sha256": "057bcb8a2f735dc4dbf49fc1bb0cc5b6d57165cbbf1cc1e9e7476dd8009a122f", "sha256_in_prefix": "057bcb8a2f735dc4dbf49fc1bb0cc5b6d57165cbbf1cc1e9e7476dd8009a122f", "size_in_bytes": 683}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/linux_and_mac/lldb_prepare.py", "path_type": "hardlink", "sha256": "2fbeb431d244897dd52c58519971c130f8851f840eb0837c9a5403b5f7cb8805", "sha256_in_prefix": "2fbeb431d244897dd52c58519971c130f8851f840eb0837c9a5403b5f7cb8805", "size_in_bytes": 1734}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__init__.py", "path_type": "hardlink", "sha256": "3b53cb23a2d19aa2f8323a4a5d9750d88d1223429a357b504a24a1c6dd7720ad", "sha256_in_prefix": "3b53cb23a2d19aa2f8323a4a5d9750d88d1223429a357b504a24a1c6dd7720ad", "size_in_bytes": 7129}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "66a0a4c9216c42fbdf5d3f2903c14c4ca87daed7a732a39209cb05134239926f", "sha256_in_prefix": "66a0a4c9216c42fbdf5d3f2903c14c4ca87daed7a732a39209cb05134239926f", "size_in_bytes": 4681}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/breakpoint.cpython-311.pyc", "path_type": "hardlink", "sha256": "951a1ddb3b4de0b3801e8143cf359748648f7b45105bdac8ff8dfd16fdb838dd", "sha256_in_prefix": "951a1ddb3b4de0b3801e8143cf359748648f7b45105bdac8ff8dfd16fdb838dd", "size_in_bytes": 189518}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/compat.cpython-311.pyc", "path_type": "hardlink", "sha256": "46cf69ed0e86e5996b1e2691e7ac580a9f11f91bc22cba7f7b10e2d91b5d8304", "sha256_in_prefix": "46cf69ed0e86e5996b1e2691e7ac580a9f11f91bc22cba7f7b10e2d91b5d8304", "size_in_bytes": 7393}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/crash.cpython-311.pyc", "path_type": "hardlink", "sha256": "dcb03e4a7a6f30871034b3c5addfae2439fa20efdb87edc8a5c1a3259b720e5f", "sha256_in_prefix": "dcb03e4a7a6f30871034b3c5addfae2439fa20efdb87edc8a5c1a3259b720e5f", "size_in_bytes": 71018}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/debug.cpython-311.pyc", "path_type": "hardlink", "sha256": "9206d47be0280910296ab7a0990d5c67cc22e085d042aa2e972fab1880bdefa6", "sha256_in_prefix": "9206d47be0280910296ab7a0990d5c67cc22e085d042aa2e972fab1880bdefa6", "size_in_bytes": 58248}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/disasm.cpython-311.pyc", "path_type": "hardlink", "sha256": "8270f87be6ed52f4d5bac0bc1dac3f4a1980925028af8c8bd2b86ec9fc027c77", "sha256_in_prefix": "8270f87be6ed52f4d5bac0bc1dac3f4a1980925028af8c8bd2b86ec9fc027c77", "size_in_bytes": 21235}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/event.cpython-311.pyc", "path_type": "hardlink", "sha256": "bef3d5c6e72c2ce1ec5ba5a2748b48c0a1549c2b57f55e8f0064d3c0e01a9b76", "sha256_in_prefix": "bef3d5c6e72c2ce1ec5ba5a2748b48c0a1549c2b57f55e8f0064d3c0e01a9b76", "size_in_bytes": 70807}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/interactive.cpython-311.pyc", "path_type": "hardlink", "sha256": "14f0326c6bf1b99850b937d2fb1a38cb664c14ee8832058879ea39249ea96398", "sha256_in_prefix": "14f0326c6bf1b99850b937d2fb1a38cb664c14ee8832058879ea39249ea96398", "size_in_bytes": 104833}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/module.cpython-311.pyc", "path_type": "hardlink", "sha256": "f8394f5840a24d4fcc3f04fdfe22def5c0a745ddde0654980d344d1169363580", "sha256_in_prefix": "f8394f5840a24d4fcc3f04fdfe22def5c0a745ddde0654980d344d1169363580", "size_in_bytes": 70479}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/process.cpython-311.pyc", "path_type": "hardlink", "sha256": "8c20788c792b7a97400eb6f03dcacd1b76a3b3f17c5cfad45750a9969a018050", "sha256_in_prefix": "8c20788c792b7a97400eb6f03dcacd1b76a3b3f17c5cfad45750a9969a018050", "size_in_bytes": 190366}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/registry.cpython-311.pyc", "path_type": "hardlink", "sha256": "436f4d5388663bdce8ac05fe712ea73c68233e7ba230977c56b722a952998e69", "sha256_in_prefix": "436f4d5388663bdce8ac05fe712ea73c68233e7ba230977c56b722a952998e69", "size_in_bytes": 26815}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/search.cpython-311.pyc", "path_type": "hardlink", "sha256": "ba501207b7e0fade9401f1bf2bc49ad333d89186f68b23c2bd8f1addb67687d5", "sha256_in_prefix": "ba501207b7e0fade9401f1bf2bc49ad333d89186f68b23c2bd8f1addb67687d5", "size_in_bytes": 23086}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/sql.cpython-311.pyc", "path_type": "hardlink", "sha256": "86f2b53184d840eb1581635e4d90b01b10e7d94439800d8ee7e8ad592891579d", "sha256_in_prefix": "86f2b53184d840eb1581635e4d90b01b10e7d94439800d8ee7e8ad592891579d", "size_in_bytes": 37072}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/system.cpython-311.pyc", "path_type": "hardlink", "sha256": "c03bd1418dfc5f283bd9ac2dda7d7bc58a4945127156966fa170cc90ba7d3b4b", "sha256_in_prefix": "c03bd1418dfc5f283bd9ac2dda7d7bc58a4945127156966fa170cc90ba7d3b4b", "size_in_bytes": 50536}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/textio.cpython-311.pyc", "path_type": "hardlink", "sha256": "65df5267fd93f7cd0730cd2c2f2a318cb11bafb5c7bc7d7e2b5e5857e5ce564c", "sha256_in_prefix": "65df5267fd93f7cd0730cd2c2f2a318cb11bafb5c7bc7d7e2b5e5857e5ce564c", "size_in_bytes": 74376}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/thread.cpython-311.pyc", "path_type": "hardlink", "sha256": "4658a31762b5203735532ab554276c00c704d6f50ef27d02a1de015145a5de7d", "sha256_in_prefix": "4658a31762b5203735532ab554276c00c704d6f50ef27d02a1de015145a5de7d", "size_in_bytes": 86995}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/util.cpython-311.pyc", "path_type": "hardlink", "sha256": "38b676726faa909319113304e2db4692adf67fbcd7bf99b28f70ce1cc9bfd827", "sha256_in_prefix": "38b676726faa909319113304e2db4692adf67fbcd7bf99b28f70ce1cc9bfd827", "size_in_bytes": 32849}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/__pycache__/window.cpython-311.pyc", "path_type": "hardlink", "sha256": "9588a2c8d1504025e22a546dea43f777513b730a9b71bd0984c81276a66e7915", "sha256_in_prefix": "9588a2c8d1504025e22a546dea43f777513b730a9b71bd0984c81276a66e7915", "size_in_bytes": 29786}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/breakpoint.py", "path_type": "hardlink", "sha256": "4e9bd49cabbfceecf7ffa45d5f524eae3a558d95eca93e1f782540f5a5a7fc87", "sha256_in_prefix": "4e9bd49cabbfceecf7ffa45d5f524eae3a558d95eca93e1f782540f5a5a7fc87", "size_in_bytes": 169903}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/compat.py", "path_type": "hardlink", "sha256": "4a918ee2673bb1d283baa86611bfd6473e4ddd74c1b738b0cda95b1b21e4a9d3", "sha256_in_prefix": "4a918ee2673bb1d283baa86611bfd6473e4ddd74c1b738b0cda95b1b21e4a9d3", "size_in_bytes": 5470}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/crash.py", "path_type": "hardlink", "sha256": "b31f76553068ac8511dce67ca63410000f357f29e15182c45c0fff0ab5c514ef", "sha256_in_prefix": "b31f76553068ac8511dce67ca63410000f357f29e15182c45c0fff0ab5c514ef", "size_in_bytes": 66661}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/debug.py", "path_type": "hardlink", "sha256": "8f0d4433c0f62c789f1d4dca473be38f235835e9f526c4c6684c91fb16805bf1", "sha256_in_prefix": "8f0d4433c0f62c789f1d4dca473be38f235835e9f526c4c6684c91fb16805bf1", "size_in_bytes": 59178}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/disasm.py", "path_type": "hardlink", "sha256": "a3cfb0c02372c0c168aa1bd2b734d663c5d03a06c39bda051796c3644bb83dcb", "sha256_in_prefix": "a3cfb0c02372c0c168aa1bd2b734d663c5d03a06c39bda051796c3644bb83dcb", "size_in_bytes": 25106}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/event.py", "path_type": "hardlink", "sha256": "fbd169e772ae404e87bac64e8dd990651371adc00a217b596658acba4948e257", "sha256_in_prefix": "fbd169e772ae404e87bac64e8dd990651371adc00a217b596658acba4948e257", "size_in_bytes": 67016}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/interactive.py", "path_type": "hardlink", "sha256": "74bddb2df6fdd596db3409ba2725eb36edbe1e7b6ef0b343af19f33354514e08", "sha256_in_prefix": "74bddb2df6fdd596db3409ba2725eb36edbe1e7b6ef0b343af19f33354514e08", "size_in_bytes": 85946}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/module.py", "path_type": "hardlink", "sha256": "9175fa15558260ab7ee94abb5dbc3f7282f6d62796ad24e7ef520ab305d35816", "sha256_in_prefix": "9175fa15558260ab7ee94abb5dbc3f7282f6d62796ad24e7ef520ab305d35816", "size_in_bytes": 71737}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/process.py", "path_type": "hardlink", "sha256": "5eb64d6e9598d6bd7eccd63b554163531167f78b91a8d2472f80d4f52b9f4607", "sha256_in_prefix": "5eb64d6e9598d6bd7eccd63b554163531167f78b91a8d2472f80d4f52b9f4607", "size_in_bytes": 184598}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/registry.py", "path_type": "hardlink", "sha256": "5fe860b6d9f362349dd64b314ffe572151302eb75051d72d1abbaf166e480a9e", "sha256_in_prefix": "5fe860b6d9f362349dd64b314ffe572151302eb75051d72d1abbaf166e480a9e", "size_in_bytes": 22135}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/search.py", "path_type": "hardlink", "sha256": "3efee229c5389f79db1d3770840689e383fef11bcaa0d27cbdce96d60942421b", "sha256_in_prefix": "3efee229c5389f79db1d3770840689e383fef11bcaa0d27cbdce96d60942421b", "size_in_bytes": 23902}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/sql.py", "path_type": "hardlink", "sha256": "584e18da5e1cf69ab3b3ec0ee7abbea0121fd4731871e1b64287ee98364d00dc", "sha256_in_prefix": "584e18da5e1cf69ab3b3ec0ee7abbea0121fd4731871e1b64287ee98364d00dc", "size_in_bytes": 34677}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/system.py", "path_type": "hardlink", "sha256": "956730590dbbffe9fa4dd4452334980a61c5f7979fbfb0bf5165d222d79c9c68", "sha256_in_prefix": "956730590dbbffe9fa4dd4452334980a61c5f7979fbfb0bf5165d222d79c9c68", "size_in_bytes": 45624}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/textio.py", "path_type": "hardlink", "sha256": "89b56ef3ad93422cbed0d43c2d9980f0429b0952a39facaa40ddae0b60d86bfc", "sha256_in_prefix": "89b56ef3ad93422cbed0d43c2d9980f0429b0952a39facaa40ddae0b60d86bfc", "size_in_bytes": 61387}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/thread.py", "path_type": "hardlink", "sha256": "9ab2d4bb4a092f361b54a8f0aeb62a469ed91920d29fd313cf7a1c8cd67176a2", "sha256_in_prefix": "9ab2d4bb4a092f361b54a8f0aeb62a469ed91920d29fd313cf7a1c8cd67176a2", "size_in_bytes": 76790}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/util.py", "path_type": "hardlink", "sha256": "7c2ce9a23cb0082b0681056d0b70a2bceb80f2729923f84a8a6fdb4272ca235f", "sha256_in_prefix": "7c2ce9a23cb0082b0681056d0b70a2bceb80f2729923f84a8a6fdb4272ca235f", "size_in_bytes": 36978}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__init__.py", "path_type": "hardlink", "sha256": "b0d6a6e9934e69fabc57e07197c3f801a48b27c9979e1d10207f2527eeeec0c2", "sha256_in_prefix": "b0d6a6e9934e69fabc57e07197c3f801a48b27c9979e1d10207f2527eeeec0c2", "size_in_bytes": 2885}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "3419bf57cc10538ee1b822c9dc8d6cfd8e5586e5e96fc7cd7251136bca5bb20e", "sha256_in_prefix": "3419bf57cc10538ee1b822c9dc8d6cfd8e5586e5e96fc7cd7251136bca5bb20e", "size_in_bytes": 2571}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/advapi32.cpython-311.pyc", "path_type": "hardlink", "sha256": "be70317401115c5856418e58e93df5a3677920da4a0867bd79968a49bce36d26", "sha256_in_prefix": "be70317401115c5856418e58e93df5a3677920da4a0867bd79968a49bce36d26", "size_in_bytes": 118986}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/context_amd64.cpython-311.pyc", "path_type": "hardlink", "sha256": "d04449910f3ac5f9fc7cb08b39ac844093ffcfd01489c1c567998727d536b93b", "sha256_in_prefix": "d04449910f3ac5f9fc7cb08b39ac844093ffcfd01489c1c567998727d536b93b", "size_in_bytes": 22189}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/context_i386.cpython-311.pyc", "path_type": "hardlink", "sha256": "c48b673a3eb8794b2bb192c59822227c0d079975355cacaa384911b5a233c586", "sha256_in_prefix": "c48b673a3eb8794b2bb192c59822227c0d079975355cacaa384911b5a233c586", "size_in_bytes": 13474}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/dbghelp.cpython-311.pyc", "path_type": "hardlink", "sha256": "cb6527b209fa835e61dc99ab6c20d0b242b5034a1b9faf858d2047d9863a335f", "sha256_in_prefix": "cb6527b209fa835e61dc99ab6c20d0b242b5034a1b9faf858d2047d9863a335f", "size_in_bytes": 38565}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/defines.cpython-311.pyc", "path_type": "hardlink", "sha256": "856111f65089d2820c353a51425dfa088eb3a8085dddfee0bc04ea2ba2e07180", "sha256_in_prefix": "856111f65089d2820c353a51425dfa088eb3a8085dddfee0bc04ea2ba2e07180", "size_in_bytes": 24279}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/gdi32.cpython-311.pyc", "path_type": "hardlink", "sha256": "9656250be620902a54cd9d9f892a29631a36776e020788f397d1ed71cb549832", "sha256_in_prefix": "9656250be620902a54cd9d9f892a29631a36776e020788f397d1ed71cb549832", "size_in_bytes": 14607}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/kernel32.cpython-311.pyc", "path_type": "hardlink", "sha256": "c0632289afecabf99abcbff8c87c29047409f633158fe4ead91e4a44023432e1", "sha256_in_prefix": "c0632289afecabf99abcbff8c87c29047409f633158fe4ead91e4a44023432e1", "size_in_bytes": 164873}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/ntdll.cpython-311.pyc", "path_type": "hardlink", "sha256": "419488f0242a8d762c49f9b545573d81f578ce67cd1a572f06d46238e4abef6e", "sha256_in_prefix": "419488f0242a8d762c49f9b545573d81f578ce67cd1a572f06d46238e4abef6e", "size_in_bytes": 16969}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/peb_teb.cpython-311.pyc", "path_type": "hardlink", "sha256": "50c238f237efd438e8e6b7fed6ae9d1beb61d7e2ee80f0858cd88e80c9509c94", "sha256_in_prefix": "50c238f237efd438e8e6b7fed6ae9d1beb61d7e2ee80f0858cd88e80c9509c94", "size_in_bytes": 62076}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/psapi.cpython-311.pyc", "path_type": "hardlink", "sha256": "c1f0993c8a3a89fef7db7255e6ae6ba63c442ecd6143a5c4793ceb96fe641df4", "sha256_in_prefix": "c1f0993c8a3a89fef7db7255e6ae6ba63c442ecd6143a5c4793ceb96fe641df4", "size_in_bytes": 14810}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/shell32.cpython-311.pyc", "path_type": "hardlink", "sha256": "5b5112120b7cd65d4928b90fc32974eaa56d66c52ceed3bb59bb517762f56bee", "sha256_in_prefix": "5b5112120b7cd65d4928b90fc32974eaa56d66c52ceed3bb59bb517762f56bee", "size_in_bytes": 14115}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/shlwapi.cpython-311.pyc", "path_type": "hardlink", "sha256": "dd645dd9a5eb8ad06a8206a7bc37163c1192cf0ac19edbf171619994be0793a6", "sha256_in_prefix": "dd645dd9a5eb8ad06a8206a7bc37163c1192cf0ac19edbf171619994be0793a6", "size_in_bytes": 30124}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/user32.cpython-311.pyc", "path_type": "hardlink", "sha256": "d800d4a0bc9f7502b2ce1d450da6c92f5b97d8e2fe298eab398b0cb57404e7ff", "sha256_in_prefix": "d800d4a0bc9f7502b2ce1d450da6c92f5b97d8e2fe298eab398b0cb57404e7ff", "size_in_bytes": 63973}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/version.cpython-311.pyc", "path_type": "hardlink", "sha256": "b74e422077eb6f9f3879385c53c02f474d7c9d9266a1ad2b21c0941a87112016", "sha256_in_prefix": "b74e422077eb6f9f3879385c53c02f474d7c9d9266a1ad2b21c0941a87112016", "size_in_bytes": 33688}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/__pycache__/wtsapi32.cpython-311.pyc", "path_type": "hardlink", "sha256": "a6d4c8ac2c074c5c9a831a3e8c4d6816e123e83f3b296121b137d24ecdf785b5", "sha256_in_prefix": "a6d4c8ac2c074c5c9a831a3e8c4d6816e123e83f3b296121b137d24ecdf785b5", "size_in_bytes": 6984}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/advapi32.py", "path_type": "hardlink", "sha256": "c6af52dc066f1bd9b213c5fbcea1f1c5eb49637426f6287fc2428982feb04988", "sha256_in_prefix": "c6af52dc066f1bd9b213c5fbcea1f1c5eb49637426f6287fc2428982feb04988", "size_in_bytes": 122129}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/context_amd64.py", "path_type": "hardlink", "sha256": "3580afe67ccd0a4c4c1975782ecc3f492816e1c51bc6c4b093a900e3bbc51561", "sha256_in_prefix": "3580afe67ccd0a4c4c1975782ecc3f492816e1c51bc6c4b093a900e3bbc51561", "size_in_bytes": 24465}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/context_i386.py", "path_type": "hardlink", "sha256": "213c579240dcbd26bfaeb4ce3d24652bd8193e827eb167e3fdf1c441baef0c17", "sha256_in_prefix": "213c579240dcbd26bfaeb4ce3d24652bd8193e827eb167e3fdf1c441baef0c17", "size_in_bytes": 15869}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/dbghelp.py", "path_type": "hardlink", "sha256": "4622b119ff18e66769231812302b15a97cc562fef6b31fe7b5e28d10d5b83338", "sha256_in_prefix": "4622b119ff18e66769231812302b15a97cc562fef6b31fe7b5e28d10d5b83338", "size_in_bytes": 46365}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/defines.py", "path_type": "hardlink", "sha256": "99b1442b14c33aab4a4a5842074b2bf241ccf6a0c675dfbe5576156860c0c5e6", "sha256_in_prefix": "99b1442b14c33aab4a4a5842074b2bf241ccf6a0c675dfbe5576156860c0c5e6", "size_in_bytes": 21637}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/gdi32.py", "path_type": "hardlink", "sha256": "ef8a889b767fa03cf762c95028c1e014a6616868021dd88e51206fbb5ff2b169", "sha256_in_prefix": "ef8a889b767fa03cf762c95028c1e014a6616868021dd88e51206fbb5ff2b169", "size_in_bytes": 14681}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/kernel32.py", "path_type": "hardlink", "sha256": "319c3a4a9448626b75a26989b6917e162b7a66dd56ed6f9cd5648268d666af8b", "sha256_in_prefix": "319c3a4a9448626b75a26989b6917e162b7a66dd56ed6f9cd5648268d666af8b", "size_in_bytes": 163857}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/ntdll.py", "path_type": "hardlink", "sha256": "30d9668120f1e359662deba81700c282015dfc33411d1a4033faa498f34cd70f", "sha256_in_prefix": "30d9668120f1e359662deba81700c282015dfc33411d1a4033faa498f34cd70f", "size_in_bytes": 21135}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/peb_teb.py", "path_type": "hardlink", "sha256": "b1877f2f745faec8683502de97ec4a19208ac3c645c505420d3209862bc42711", "sha256_in_prefix": "b1877f2f745faec8683502de97ec4a19208ac3c645c505420d3209862bc42711", "size_in_bytes": 139565}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/psapi.py", "path_type": "hardlink", "sha256": "032626d61ba5876db31b5223ec7e0a776288fc384e7715a38a5e131797607c2d", "sha256_in_prefix": "032626d61ba5876db31b5223ec7e0a776288fc384e7715a38a5e131797607c2d", "size_in_bytes": 14115}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/shell32.py", "path_type": "hardlink", "sha256": "80a089ff7653f7a98dcd6100b1625bacbae6e0a9747053fc7b057d468e98e3bf", "sha256_in_prefix": "80a089ff7653f7a98dcd6100b1625bacbae6e0a9747053fc7b057d468e98e3bf", "size_in_bytes": 13176}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/shlwapi.py", "path_type": "hardlink", "sha256": "c0eeb399c357c6d77e76cbd0969bbe3b36561a770b41e92ed83fa89b53d6df16", "sha256_in_prefix": "c0eeb399c357c6d77e76cbd0969bbe3b36561a770b41e92ed83fa89b53d6df16", "size_in_bytes": 26209}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/user32.py", "path_type": "hardlink", "sha256": "ed3e2ca2fcb493bd0d44b4ef6e336744705befb91ac54681a6d3e29655a71b6e", "sha256_in_prefix": "ed3e2ca2fcb493bd0d44b4ef6e336744705befb91ac54681a6d3e29655a71b6e", "size_in_bytes": 53270}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/version.py", "path_type": "hardlink", "sha256": "a5eaacbf302de37fd1906d9d2eeb5ed073a1cc41128d878123d4024bc1fd9d5b", "sha256_in_prefix": "a5eaacbf302de37fd1906d9d2eeb5ed073a1cc41128d878123d4024bc1fd9d5b", "size_in_bytes": 34919}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/win32/wtsapi32.py", "path_type": "hardlink", "sha256": "939dd218fed1c044df89c1d2511b289f0f2ce9462b7a54ccc150da336e42f274", "sha256_in_prefix": "939dd218fed1c044df89c1d2511b289f0f2ce9462b7a54ccc150da336e42f274", "size_in_bytes": 11136}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/winappdbg/window.py", "path_type": "hardlink", "sha256": "40c802fcbdb22b2844af24523972e6b43c17c3d8a1d56f97743d1626f686932b", "sha256_in_prefix": "40c802fcbdb22b2844af24523972e6b43c17c3d8a1d56f97743d1626f686932b", "size_in_bytes": 24770}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/attach.cpp", "path_type": "hardlink", "sha256": "a7fdd07bc0ea9ac49f0cc20893b1f0b08ae4115f6ee5af5293cc4fe5c5ab0081", "sha256_in_prefix": "a7fdd07bc0ea9ac49f0cc20893b1f0b08ae4115f6ee5af5293cc4fe5c5ab0081", "size_in_bytes": 28624}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/attach.h", "path_type": "hardlink", "sha256": "0d36d8197c3f70d585e0b550faf5c251d36c5239ee440e5966a8a59644a25263", "sha256_in_prefix": "0d36d8197c3f70d585e0b550faf5c251d36c5239ee440e5966a8a59644a25263", "size_in_bytes": 1902}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/compile_windows.bat", "path_type": "hardlink", "sha256": "3d4f91795dff4aa7d69a79990af2d116f0b332bfbb5f91a50b8ecfe849c3f169", "sha256_in_prefix": "3d4f91795dff4aa7d69a79990af2d116f0b332bfbb5f91a50b8ecfe849c3f169", "size_in_bytes": 2203}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/inject_dll.cpp", "path_type": "hardlink", "sha256": "327b346c8641324ed40966b91baefda6333662c7f47085edcd9438a7f2d7252d", "sha256_in_prefix": "327b346c8641324ed40966b91baefda6333662c7f47085edcd9438a7f2d7252d", "size_in_bytes": 4925}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/py_win_helpers.hpp", "path_type": "hardlink", "sha256": "d3c3e4871d8964761ec3c409d0a53a3edc540a07bd8687a69a70f4f41cf21b47", "sha256_in_prefix": "d3c3e4871d8964761ec3c409d0a53a3edc540a07bd8687a69a70f4f41cf21b47", "size_in_bytes": 2555}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/run_code_in_memory.hpp", "path_type": "hardlink", "sha256": "c00ae74c2ec8f5d84c5dff99be4f3da4bde94ba4c73f14deabd63a24bfa2f00c", "sha256_in_prefix": "c00ae74c2ec8f5d84c5dff99be4f3da4bde94ba4c73f14deabd63a24bfa2f00c", "size_in_bytes": 3470}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/run_code_on_dllmain.cpp", "path_type": "hardlink", "sha256": "27c94fe8bc8035512ed63f676cf5a835912512dadeacf0f08407f9b3454d9833", "sha256_in_prefix": "27c94fe8bc8035512ed63f676cf5a835912512dadeacf0f08407f9b3454d9833", "size_in_bytes": 2594}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/stdafx.cpp", "path_type": "hardlink", "sha256": "57ba748116dfdaf6c5139e9aac66431041842c0c582efa1509509ba6ec9cd6e2", "sha256_in_prefix": "57ba748116dfdaf6c5139e9aac66431041842c0c582efa1509509ba6ec9cd6e2", "size_in_bytes": 1021}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/stdafx.h", "path_type": "hardlink", "sha256": "2a94293ded391850bf57fa18bed08ebe9ffe1487b60307605e353529c3abedda", "sha256_in_prefix": "2a94293ded391850bf57fa18bed08ebe9ffe1487b60307605e353529c3abedda", "size_in_bytes": 1198}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_attach_to_process/windows/targetver.h", "path_type": "hardlink", "sha256": "8c5919d5736ffa584c15b1a798f39cf5cd09818811c6555969ea069efa1f82fb", "sha256_in_prefix": "8c5919d5736ffa584c15b1a798f39cf5cd09818811c6555969ea069efa1f82fb", "size_in_bytes": 1035}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_file_utils.py", "path_type": "hardlink", "sha256": "9433bb2af3ff7b39c3b75ecc2639ea9862b3b286423f370843043df872988001", "sha256_in_prefix": "9433bb2af3ff7b39c3b75ecc2639ea9862b3b286423f370843043df872988001", "size_in_bytes": 38281}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/__init__.py", "path_type": "hardlink", "sha256": "63d7753d1fd95c6e949bf7db76baeb63151dd7a34a1fb762958ac928ce138ec9", "sha256_in_prefix": "63d7753d1fd95c6e949bf7db76baeb63151dd7a34a1fb762958ac928ce138ec9", "size_in_bytes": 70}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "0ef9f6a243ad3a55abd477937d5759153e84c53112ed3632f7ba8b36b310f679", "sha256_in_prefix": "0ef9f6a243ad3a55abd477937d5759153e84c53112ed3632f7ba8b36b310f679", "size_in_bytes": 557}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/__pycache__/django_debug.cpython-311.pyc", "path_type": "hardlink", "sha256": "9339091e1072409468c5e05eb95a7b673218148eed4c3fa01237a3c187a1aedb", "sha256_in_prefix": "9339091e1072409468c5e05eb95a7b673218148eed4c3fa01237a3c187a1aedb", "size_in_bytes": 25631}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/__pycache__/jinja2_debug.cpython-311.pyc", "path_type": "hardlink", "sha256": "ee5cf2a8d10ec0faae81f65251dd99ae98e82337308a52b37d08b41563b2b7dd", "sha256_in_prefix": "ee5cf2a8d10ec0faae81f65251dd99ae98e82337308a52b37d08b41563b2b7dd", "size_in_bytes": 21125}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/__pycache__/pydevd_line_validation.cpython-311.pyc", "path_type": "hardlink", "sha256": "f6208f11b73d70fc6ab60bbd6bee992996e78a3118a94b37a6ecd166ddb87f5d", "sha256_in_prefix": "f6208f11b73d70fc6ab60bbd6bee992996e78a3118a94b37a6ecd166ddb87f5d", "size_in_bytes": 6122}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/django_debug.py", "path_type": "hardlink", "sha256": "f6a7e139e53873ad65cbe952f6c31cc1d12c15eebcab2220cc7a8abf8068d544", "sha256_in_prefix": "f6a7e139e53873ad65cbe952f6c31cc1d12c15eebcab2220cc7a8abf8068d544", "size_in_bytes": 22911}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/README.md", "path_type": "hardlink", "sha256": "088e645de96ceb8a87ae1b808ffbabe6733f55649ce6a55da8a2bfcb17e110fc", "sha256_in_prefix": "088e645de96ceb8a87ae1b808ffbabe6733f55649ce6a55da8a2bfcb17e110fc", "size_in_bytes": 1212}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/__init__.py", "path_type": "hardlink", "sha256": "63d7753d1fd95c6e949bf7db76baeb63151dd7a34a1fb762958ac928ce138ec9", "sha256_in_prefix": "63d7753d1fd95c6e949bf7db76baeb63151dd7a34a1fb762958ac928ce138ec9", "size_in_bytes": 70}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "34663c22031a74b3a60d965cde6f2d3073a931c78fa41a9c80f0e06116a14a53", "sha256_in_prefix": "34663c22031a74b3a60d965cde6f2d3073a931c78fa41a9c80f0e06116a14a53", "size_in_bytes": 568}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/__init__.py", "path_type": "hardlink", "sha256": "63d7753d1fd95c6e949bf7db76baeb63151dd7a34a1fb762958ac928ce138ec9", "sha256_in_prefix": "63d7753d1fd95c6e949bf7db76baeb63151dd7a34a1fb762958ac928ce138ec9", "size_in_bytes": 70}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "e9dcff3e26413c37194da9ebbf72faa81a89f416362aa0f2ee82484950638d8f", "sha256_in_prefix": "e9dcff3e26413c37194da9ebbf72faa81a89f416362aa0f2ee82484950638d8f", "size_in_bytes": 574}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/__pycache__/pydevd_helpers.cpython-311.pyc", "path_type": "hardlink", "sha256": "cfdcf3e292ff989a21c78696d6fcd2db33f46ad3bb49e861a2f59f2e530da6a0", "sha256_in_prefix": "cfdcf3e292ff989a21c78696d6fcd2db33f46ad3bb49e861a2f59f2e530da6a0", "size_in_bytes": 1666}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/__pycache__/pydevd_plugin_numpy_types.cpython-311.pyc", "path_type": "hardlink", "sha256": "99aae0ad4faf6ee952c10b9390363fcd8a88badface5044e077f96eb13bb25bd", "sha256_in_prefix": "99aae0ad4faf6ee952c10b9390363fcd8a88badface5044e077f96eb13bb25bd", "size_in_bytes": 5217}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/__pycache__/pydevd_plugin_pandas_types.cpython-311.pyc", "path_type": "hardlink", "sha256": "fc1fcb09cfc5363360248e2ec36624e19123aa838e9d67636639a5a3b077e768", "sha256_in_prefix": "fc1fcb09cfc5363360248e2ec36624e19123aa838e9d67636639a5a3b077e768", "size_in_bytes": 8969}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/__pycache__/pydevd_plugins_django_form_str.cpython-311.pyc", "path_type": "hardlink", "sha256": "c6a2d2e5ea04f0b1de6f839f28c91a999d7fd3db572c76b481159e2987d4ab3e", "sha256_in_prefix": "c6a2d2e5ea04f0b1de6f839f28c91a999d7fd3db572c76b481159e2987d4ab3e", "size_in_bytes": 1669}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/pydevd_helpers.py", "path_type": "hardlink", "sha256": "b5548b3e4933ef65cffe8641eb977c836412c96c3db3fc42e8f25601fe26e3ac", "sha256_in_prefix": "b5548b3e4933ef65cffe8641eb977c836412c96c3db3fc42e8f25601fe26e3ac", "size_in_bytes": 668}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/pydevd_plugin_numpy_types.py", "path_type": "hardlink", "sha256": "7f1aa0ee653addb9929ee7c872bd494f22f3a59f36f8ba6309f584f36686edb4", "sha256_in_prefix": "7f1aa0ee653addb9929ee7c872bd494f22f3a59f36f8ba6309f584f36686edb4", "size_in_bytes": 3340}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/pydevd_plugin_pandas_types.py", "path_type": "hardlink", "sha256": "537f175d60085801ea0b606daf72ab74355f6cb9ce38033348ce48490edde5f4", "sha256_in_prefix": "537f175d60085801ea0b606daf72ab74355f6cb9ce38033348ce48490edde5f4", "size_in_bytes": 6753}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/extensions/types/pydevd_plugins_django_form_str.py", "path_type": "hardlink", "sha256": "be669e45bcdcec46ad6109127fb4e5aa7b015fba067758405c8847e4f53af89e", "sha256_in_prefix": "be669e45bcdcec46ad6109127fb4e5aa7b015fba067758405c8847e4f53af89e", "size_in_bytes": 556}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/jinja2_debug.py", "path_type": "hardlink", "sha256": "95c76fc837e11e7333982df57afa2eb0c6a33d63d8ac56af6d9fd5cb48f6c3f7", "sha256_in_prefix": "95c76fc837e11e7333982df57afa2eb0c6a33d63d8ac56af6d9fd5cb48f6c3f7", "size_in_bytes": 19571}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_plugins/pydevd_line_validation.py", "path_type": "hardlink", "sha256": "fea7ecb4060e6afa129c82eaa1d51a405b8cf2fd334af224e890284ca520fd1f", "sha256_in_prefix": "fea7ecb4060e6afa129c82eaa1d51a405b8cf2fd334af224e890284ca520fd1f", "size_in_bytes": 6774}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/pydevd_tracing.py", "path_type": "hardlink", "sha256": "1f1f3bda155a876f1e7dd1c4bd92c5879390317a13d80f80ac806bc08226b870", "sha256_in_prefix": "1f1f3bda155a876f1e7dd1c4bd92c5879390317a13d80f80ac806bc08226b870", "size_in_bytes": 16263}, {"_path": "lib/python3.11/site-packages/debugpy/_vendored/pydevd/setup_pydevd_cython.py", "path_type": "hardlink", "sha256": "dde2e20513c361494c08fd4893d922759b04fc72dce32de7a88f6916e67c204f", "sha256_in_prefix": "dde2e20513c361494c08fd4893d922759b04fc72dce32de7a88f6916e67c204f", "size_in_bytes": 11842}, {"_path": "lib/python3.11/site-packages/debugpy/_version.py", "path_type": "hardlink", "sha256": "c25c1ec22517be12d019c380395b55485acd0b5253248a00d974b0ec8b55a03b", "sha256_in_prefix": "c25c1ec22517be12d019c380395b55485acd0b5253248a00d974b0ec8b55a03b", "size_in_bytes": 498}, {"_path": "lib/python3.11/site-packages/debugpy/adapter/__init__.py", "path_type": "hardlink", "sha256": "f552d8ba52c660727ca0525e8e9b4db399af594daaa806e9848a9719f57cee0c", "sha256_in_prefix": "f552d8ba52c660727ca0525e8e9b4db399af594daaa806e9848a9719f57cee0c", "size_in_bytes": 360}, {"_path": "lib/python3.11/site-packages/debugpy/adapter/__main__.py", "path_type": "hardlink", "sha256": "ce6d475e0a1e5dce3cc79b51c254561a8031041b31f1d11904a53dad4e5a095f", "sha256_in_prefix": "ce6d475e0a1e5dce3cc79b51c254561a8031041b31f1d11904a53dad4e5a095f", "size_in_bytes": 8872}, {"_path": "lib/python3.11/site-packages/debugpy/adapter/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "bcc10f51de7390b77efe4d0db691d27ac173e4a2bd4454513b2a6a22b6ed304e", "sha256_in_prefix": "bcc10f51de7390b77efe4d0db691d27ac173e4a2bd4454513b2a6a22b6ed304e", "size_in_bytes": 652}, {"_path": "lib/python3.11/site-packages/debugpy/adapter/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "0b8774b0ef0c863f95283bb1c86559cfa050acea0d5af56b83fc5856304a1aa6", "sha256_in_prefix": "0b8774b0ef0c863f95283bb1c86559cfa050acea0d5af56b83fc5856304a1aa6", "size_in_bytes": 10129}, {"_path": "lib/python3.11/site-packages/debugpy/adapter/__pycache__/clients.cpython-311.pyc", "path_type": "hardlink", "sha256": "057f076fb4b041190e2fe30a35b4ff4f5db7de4f6fbc692f3aff0928e444dd8a", "sha256_in_prefix": "057f076fb4b041190e2fe30a35b4ff4f5db7de4f6fbc692f3aff0928e444dd8a", "size_in_bytes": 33976}, {"_path": "lib/python3.11/site-packages/debugpy/adapter/__pycache__/components.cpython-311.pyc", "path_type": "hardlink", "sha256": "cc4e08dbf385a84b315c4d4a8efdff86b2ebdfcdce112e5b9d4a55cf33549d4d", "sha256_in_prefix": "cc4e08dbf385a84b315c4d4a8efdff86b2ebdfcdce112e5b9d4a55cf33549d4d", "size_in_bytes": 11030}, {"_path": "lib/python3.11/site-packages/debugpy/adapter/__pycache__/launchers.cpython-311.pyc", "path_type": "hardlink", "sha256": "355a831463dec916f3675ee18ea1328c79cd3742b356681b0fb93468232b3f7d", "sha256_in_prefix": "355a831463dec916f3675ee18ea1328c79cd3742b356681b0fb93468232b3f7d", "size_in_bytes": 10107}, {"_path": "lib/python3.11/site-packages/debugpy/adapter/__pycache__/servers.cpython-311.pyc", "path_type": "hardlink", "sha256": "d5cae8eb484665804904f4b3e77da1227d80637053cbd11b301d4e54cbf34253", "sha256_in_prefix": "d5cae8eb484665804904f4b3e77da1227d80637053cbd11b301d4e54cbf34253", "size_in_bytes": 28480}, {"_path": "lib/python3.11/site-packages/debugpy/adapter/__pycache__/sessions.cpython-311.pyc", "path_type": "hardlink", "sha256": "07faea14e9d3cf0ebd6078efa24a8977f16f529188b873740406a87bacf24189", "sha256_in_prefix": "07faea14e9d3cf0ebd6078efa24a8977f16f529188b873740406a87bacf24189", "size_in_bytes": 15231}, {"_path": "lib/python3.11/site-packages/debugpy/adapter/clients.py", "path_type": "hardlink", "sha256": "92df10a72a3c013f55b856e56236649b2f9dfbc5d056735ce2e525fbaea2db7c", "sha256_in_prefix": "92df10a72a3c013f55b856e56236649b2f9dfbc5d056735ce2e525fbaea2db7c", "size_in_bytes": 32547}, {"_path": "lib/python3.11/site-packages/debugpy/adapter/components.py", "path_type": "hardlink", "sha256": "de2b85a8ea073666ee8248cb4c969728c1ceeff51e9bb08142290a35ead6fb0d", "sha256_in_prefix": "de2b85a8ea073666ee8248cb4c969728c1ceeff51e9bb08142290a35ead6fb0d", "size_in_bytes": 6264}, {"_path": "lib/python3.11/site-packages/debugpy/adapter/launchers.py", "path_type": "hardlink", "sha256": "e922c38e3f8496ea319aace5adb6e34f2562f2cdd46384981234ec6b6776c311", "sha256_in_prefix": "e922c38e3f8496ea319aace5adb6e34f2562f2cdd46384981234ec6b6776c311", "size_in_bytes": 7263}, {"_path": "lib/python3.11/site-packages/debugpy/adapter/servers.py", "path_type": "hardlink", "sha256": "fcda211deb9c2fada184ee4d4cfa4373970c743d2a8866bd5f8c9ca99af3885b", "sha256_in_prefix": "fcda211deb9c2fada184ee4d4cfa4373970c743d2a8866bd5f8c9ca99af3885b", "size_in_bytes": 24052}, {"_path": "lib/python3.11/site-packages/debugpy/adapter/sessions.py", "path_type": "hardlink", "sha256": "851de6b54aac59a397a2848ce147b62bb4609fba66eddda6e00ee0d9a7551180", "sha256_in_prefix": "851de6b54aac59a397a2848ce147b62bb4609fba66eddda6e00ee0d9a7551180", "size_in_bytes": 11522}, {"_path": "lib/python3.11/site-packages/debugpy/common/__init__.py", "path_type": "hardlink", "sha256": "2893ca9934bfeac05671e8418adb29c7831bcbd0d4ceadc539cefb1b472557a1", "sha256_in_prefix": "2893ca9934bfeac05671e8418adb29c7831bcbd0d4ceadc539cefb1b472557a1", "size_in_bytes": 627}, {"_path": "lib/python3.11/site-packages/debugpy/common/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "97c9015815e1a0050d41da194b9960949501a79fef4cdbf615ae265bbacfe2de", "sha256_in_prefix": "97c9015815e1a0050d41da194b9960949501a79fef4cdbf615ae265bbacfe2de", "size_in_bytes": 969}, {"_path": "lib/python3.11/site-packages/debugpy/common/__pycache__/json.cpython-311.pyc", "path_type": "hardlink", "sha256": "d6788072842f86a96d2abb3cc94d76db0676a7c7539af967ae1812f7c3490af0", "sha256_in_prefix": "d6788072842f86a96d2abb3cc94d76db0676a7c7539af967ae1812f7c3490af0", "size_in_bytes": 14359}, {"_path": "lib/python3.11/site-packages/debugpy/common/__pycache__/log.cpython-311.pyc", "path_type": "hardlink", "sha256": "c0c94b3084c3dd6169ba853a2d5fd40a90824f1048915612bf591e7979a1a91b", "sha256_in_prefix": "c0c94b3084c3dd6169ba853a2d5fd40a90824f1048915612bf591e7979a1a91b", "size_in_bytes": 20700}, {"_path": "lib/python3.11/site-packages/debugpy/common/__pycache__/messaging.cpython-311.pyc", "path_type": "hardlink", "sha256": "a8623af036348c9912c128e0260dfa9fffb0b215032a146e100b5b6dcd8b2333", "sha256_in_prefix": "a8623af036348c9912c128e0260dfa9fffb0b215032a146e100b5b6dcd8b2333", "size_in_bytes": 72682}, {"_path": "lib/python3.11/site-packages/debugpy/common/__pycache__/singleton.cpython-311.pyc", "path_type": "hardlink", "sha256": "88e2dcc571bf1de0728517ef40ddfd3b47caccb274614eb888dea370d9efd85a", "sha256_in_prefix": "88e2dcc571bf1de0728517ef40ddfd3b47caccb274614eb888dea370d9efd85a", "size_in_bytes": 9126}, {"_path": "lib/python3.11/site-packages/debugpy/common/__pycache__/sockets.cpython-311.pyc", "path_type": "hardlink", "sha256": "f163087a11df467f977acaddb00ffb2126aa8468bae975eacf80bd8bbdb2b523", "sha256_in_prefix": "f163087a11df467f977acaddb00ffb2126aa8468bae975eacf80bd8bbdb2b523", "size_in_bytes": 8573}, {"_path": "lib/python3.11/site-packages/debugpy/common/__pycache__/stacks.cpython-311.pyc", "path_type": "hardlink", "sha256": "003e8f11bd3aa93f2af2d53eaeeac198cdc291bad2908f0f9f31ee957d1e7c3a", "sha256_in_prefix": "003e8f11bd3aa93f2af2d53eaeeac198cdc291bad2908f0f9f31ee957d1e7c3a", "size_in_bytes": 2772}, {"_path": "lib/python3.11/site-packages/debugpy/common/__pycache__/timestamp.cpython-311.pyc", "path_type": "hardlink", "sha256": "787fe09acfa92b8cbb6dfedacbcd4ad485a718ae8861e28e423d84553014a729", "sha256_in_prefix": "787fe09acfa92b8cbb6dfedacbcd4ad485a718ae8861e28e423d84553014a729", "size_in_bytes": 968}, {"_path": "lib/python3.11/site-packages/debugpy/common/__pycache__/util.cpython-311.pyc", "path_type": "hardlink", "sha256": "30d58760b65c5a425d792bebae818a5ee872fadddc55c8620f6bdb624b934e99", "sha256_in_prefix": "30d58760b65c5a425d792bebae818a5ee872fadddc55c8620f6bdb624b934e99", "size_in_bytes": 7319}, {"_path": "lib/python3.11/site-packages/debugpy/common/json.py", "path_type": "hardlink", "sha256": "52726ee0f7b4e142a785fdb3f644a2f16d3a4d73f5152ffca6a4053385539dd7", "sha256_in_prefix": "52726ee0f7b4e142a785fdb3f644a2f16d3a4d73f5152ffca6a4053385539dd7", "size_in_bytes": 9966}, {"_path": "lib/python3.11/site-packages/debugpy/common/log.py", "path_type": "hardlink", "sha256": "3026dc27471ace1c1779d9024de793dd06e80c645ddfa40a0fd810cbb5ed4a99", "sha256_in_prefix": "3026dc27471ace1c1779d9024de793dd06e80c645ddfa40a0fd810cbb5ed4a99", "size_in_bytes": 12117}, {"_path": "lib/python3.11/site-packages/debugpy/common/messaging.py", "path_type": "hardlink", "sha256": "41c65427a2acd68aeaecbe311df2ddac93e48ae95d9c97981a99e1988ea456fb", "sha256_in_prefix": "41c65427a2acd68aeaecbe311df2ddac93e48ae95d9c97981a99e1988ea456fb", "size_in_bytes": 58126}, {"_path": "lib/python3.11/site-packages/debugpy/common/singleton.py", "path_type": "hardlink", "sha256": "cb13c8dcc64f140186dac47e0f415ab325bbaae39881f1d93c7045989ee4f263", "sha256_in_prefix": "cb13c8dcc64f140186dac47e0f415ab325bbaae39881f1d93c7045989ee4f263", "size_in_bytes": 7851}, {"_path": "lib/python3.11/site-packages/debugpy/common/sockets.py", "path_type": "hardlink", "sha256": "e3e5f47c3805e9b628b8f536c079bb9eb9d4bba81a8f421544b7aa8540e1f23a", "sha256_in_prefix": "e3e5f47c3805e9b628b8f536c079bb9eb9d4bba81a8f421544b7aa8540e1f23a", "size_in_bytes": 6277}, {"_path": "lib/python3.11/site-packages/debugpy/common/stacks.py", "path_type": "hardlink", "sha256": "1af4dc2776494cfeefcf3c69c7a265ac70261f0ab2e68cf583eefa6d4dfce90c", "sha256_in_prefix": "1af4dc2776494cfeefcf3c69c7a265ac70261f0ab2e68cf583eefa6d4dfce90c", "size_in_bytes": 1588}, {"_path": "lib/python3.11/site-packages/debugpy/common/timestamp.py", "path_type": "hardlink", "sha256": "d8b5ac61f650706f16e8a67ca9243e1ca3cc7d4d617fd35cf87d696004464602", "sha256_in_prefix": "d8b5ac61f650706f16e8a67ca9243e1ca3cc7d4d617fd35cf87d696004464602", "size_in_bytes": 432}, {"_path": "lib/python3.11/site-packages/debugpy/common/util.py", "path_type": "hardlink", "sha256": "ca78841ba23adf5ecf798b55b64a09c3105bb230a2162ebbf1549bf9882ffb24", "sha256_in_prefix": "ca78841ba23adf5ecf798b55b64a09c3105bb230a2162ebbf1549bf9882ffb24", "size_in_bytes": 4810}, {"_path": "lib/python3.11/site-packages/debugpy/launcher/__init__.py", "path_type": "hardlink", "sha256": "8d2e0c8fa239a8957d0e49c728dfd6ef2cfbed1d7b869b096d12f9dad137497b", "sha256_in_prefix": "8d2e0c8fa239a8957d0e49c728dfd6ef2cfbed1d7b869b096d12f9dad137497b", "size_in_bytes": 958}, {"_path": "lib/python3.11/site-packages/debugpy/launcher/__main__.py", "path_type": "hardlink", "sha256": "5a046f24b72d86e90ab2207007d61e57de865453466ba538d37b4e72d0d90cca", "sha256_in_prefix": "5a046f24b72d86e90ab2207007d61e57de865453466ba538d37b4e72d0d90cca", "size_in_bytes": 3955}, {"_path": "lib/python3.11/site-packages/debugpy/launcher/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "7b7e0f1295c85795c6d7e526118aab14310aece81eb627e9b9d4438d211a4077", "sha256_in_prefix": "7b7e0f1295c85795c6d7e526118aab14310aece81eb627e9b9d4438d211a4077", "size_in_bytes": 1542}, {"_path": "lib/python3.11/site-packages/debugpy/launcher/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "3457f04b7de1c1c734e13b2195f0b3d421bbecc9e4e827ff192e6b79d9e8c43d", "sha256_in_prefix": "3457f04b7de1c1c734e13b2195f0b3d421bbecc9e4e827ff192e6b79d9e8c43d", "size_in_bytes": 3013}, {"_path": "lib/python3.11/site-packages/debugpy/launcher/__pycache__/debuggee.cpython-311.pyc", "path_type": "hardlink", "sha256": "8979717370b7dc6b17f5b8df06cbbb4761f850de05403ecbc693c14796847f14", "sha256_in_prefix": "8979717370b7dc6b17f5b8df06cbbb4761f850de05403ecbc693c14796847f14", "size_in_bytes": 11111}, {"_path": "lib/python3.11/site-packages/debugpy/launcher/__pycache__/handlers.cpython-311.pyc", "path_type": "hardlink", "sha256": "703cebb526bf7d0f14effe379fe8746455cf74d145436809afe3df17957c3083", "sha256_in_prefix": "703cebb526bf7d0f14effe379fe8746455cf74d145436809afe3df17957c3083", "size_in_bytes": 7201}, {"_path": "lib/python3.11/site-packages/debugpy/launcher/__pycache__/output.cpython-311.pyc", "path_type": "hardlink", "sha256": "3e85fd994e14d82b517293d0c864f7714f4de663f09ed3317da7edd25ccccb1f", "sha256_in_prefix": "3e85fd994e14d82b517293d0c864f7714f4de663f09ed3317da7edd25ccccb1f", "size_in_bytes": 5587}, {"_path": "lib/python3.11/site-packages/debugpy/launcher/__pycache__/winapi.cpython-311.pyc", "path_type": "hardlink", "sha256": "48b40636e0cc49ae83f4c238f97a0fd05d5170ed544e7dc1a0e5563c61828082", "sha256_in_prefix": "48b40636e0cc49ae83f4c238f97a0fd05d5170ed544e7dc1a0e5563c61828082", "size_in_bytes": 4615}, {"_path": "lib/python3.11/site-packages/debugpy/launcher/debuggee.py", "path_type": "hardlink", "sha256": "5442f9a63a4f8dd07ccf15a07456d730ee6b086334be9617e91658374463114f", "sha256_in_prefix": "5442f9a63a4f8dd07ccf15a07456d730ee6b086334be9617e91658374463114f", "size_in_bytes": 8939}, {"_path": "lib/python3.11/site-packages/debugpy/launcher/handlers.py", "path_type": "hardlink", "sha256": "276281efbd0ce642a64d5a649c7432c94c135681b65ba09907d2296a6d0bb3b6", "sha256_in_prefix": "276281efbd0ce642a64d5a649c7432c94c135681b65ba09907d2296a6d0bb3b6", "size_in_bytes": 5880}, {"_path": "lib/python3.11/site-packages/debugpy/launcher/output.py", "path_type": "hardlink", "sha256": "d72d3d9f39cfb6452aaa2748ea0d1fae5e5dd6e89fd2841facfba73020ee8d4e", "sha256_in_prefix": "d72d3d9f39cfb6452aaa2748ea0d1fae5e5dd6e89fd2841facfba73020ee8d4e", "size_in_bytes": 3861}, {"_path": "lib/python3.11/site-packages/debugpy/launcher/winapi.py", "path_type": "hardlink", "sha256": "4eff7541e0717b6d03207beae279b9c6f56c02a5e7516ba73f8a28a2790bf026", "sha256_in_prefix": "4eff7541e0717b6d03207beae279b9c6f56c02a5e7516ba73f8a28a2790bf026", "size_in_bytes": 3233}, {"_path": "lib/python3.11/site-packages/debugpy/public_api.py", "path_type": "hardlink", "sha256": "d7dea62a7340aee8e4985cf0efd1593ba49c00630f9595f8670630b708cc45fb", "sha256_in_prefix": "d7dea62a7340aee8e4985cf0efd1593ba49c00630f9595f8670630b708cc45fb", "size_in_bytes": 8632}, {"_path": "lib/python3.11/site-packages/debugpy/server/__init__.py", "path_type": "hardlink", "sha256": "b2fa2ecbd2525bd0fe4f2ab6ac00a48efbf38558be40e1c1b0939b721a9e321e", "sha256_in_prefix": "b2fa2ecbd2525bd0fe4f2ab6ac00a48efbf38558be40e1c1b0939b721a9e321e", "size_in_bytes": 330}, {"_path": "lib/python3.11/site-packages/debugpy/server/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "27df5ed52040c4c577f96ec2b641d2c81040fe8aa2a57e504612cf66b2ebf144", "sha256_in_prefix": "27df5ed52040c4c577f96ec2b641d2c81040fe8aa2a57e504612cf66b2ebf144", "size_in_bytes": 484}, {"_path": "lib/python3.11/site-packages/debugpy/server/__pycache__/api.cpython-311.pyc", "path_type": "hardlink", "sha256": "72bfb7e5788f6560ad4f5dc88c5a6433450cc84361e1b3066d9b445ac341f352", "sha256_in_prefix": "72bfb7e5788f6560ad4f5dc88c5a6433450cc84361e1b3066d9b445ac341f352", "size_in_bytes": 16692}, {"_path": "lib/python3.11/site-packages/debugpy/server/__pycache__/attach_pid_injected.cpython-311.pyc", "path_type": "hardlink", "sha256": "4bb09594cdf25f8e5881bafd18da661027e0f6720e60fde512ae3a54a30cd66b", "sha256_in_prefix": "4bb09594cdf25f8e5881bafd18da661027e0f6720e60fde512ae3a54a30cd66b", "size_in_bytes": 4331}, {"_path": "lib/python3.11/site-packages/debugpy/server/__pycache__/cli.cpython-311.pyc", "path_type": "hardlink", "sha256": "68f29053f90328e9bbc7b1de11b7b8b49c2ae3f2c9b33459b22b31c65eeaf99a", "sha256_in_prefix": "68f29053f90328e9bbc7b1de11b7b8b49c2ae3f2c9b33459b22b31c65eeaf99a", "size_in_bytes": 23911}, {"_path": "lib/python3.11/site-packages/debugpy/server/api.py", "path_type": "hardlink", "sha256": "a7e4d2cea77542ea01426efa2a1a2d2efaeaf7064abc2fc158a97461140232eb", "sha256_in_prefix": "a7e4d2cea77542ea01426efa2a1a2d2efaeaf7064abc2fc158a97461140232eb", "size_in_bytes": 12316}, {"_path": "lib/python3.11/site-packages/debugpy/server/attach_pid_injected.py", "path_type": "hardlink", "sha256": "b9a7820272841447a1671c855a0312affdc233810b1b81fd18289fea8e17caff", "sha256_in_prefix": "b9a7820272841447a1671c855a0312affdc233810b1b81fd18289fea8e17caff", "size_in_bytes": 2826}, {"_path": "lib/python3.11/site-packages/debugpy/server/cli.py", "path_type": "hardlink", "sha256": "762cfce33a69c2b4461992d90594d251136f6c354959f084931d67d107d35760", "sha256_in_prefix": "762cfce33a69c2b4461992d90594d251136f6c354959f084931d67d107d35760", "size_in_bytes": 17492}, {"_path": "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "bc33022edcb7639ff53355b4e91dade50a0bbf0299efeb6171d1ec0ba5029cfc", "sha256_in_prefix": "bc33022edcb7639ff53355b4e91dade50a0bbf0299efeb6171d1ec0ba5029cfc", "size_in_bytes": 6}, {"_path": "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/METADATA", "path_type": "hardlink", "sha256": "07efad6aef667779401e295b4c9b067f3d0c5412ef61d45f96f2f3a99a713547", "sha256_in_prefix": "07efad6aef667779401e295b4c9b067f3d0c5412ef61d45f96f2f3a99a713547", "size_in_bytes": 1430}, {"_path": "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/RECORD", "path_type": "hardlink", "sha256": "c37526bf79c6e50c9de0dc6ae6bad69420a21890bb8db76a4d026b0fc7c232b5", "sha256_in_prefix": "c37526bf79c6e50c9de0dc6ae6bad69420a21890bb8db76a4d026b0fc7c232b5", "size_in_bytes": 55773}, {"_path": "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/WHEEL", "path_type": "hardlink", "sha256": "9a99a83f4370ced80864e3ca8f3c6d5e12203e9375332d371b67792389f5359e", "sha256_in_prefix": "9a99a83f4370ced80864e3ca8f3c6d5e12203e9375332d371b67792389f5359e", "size_in_bytes": 111}, {"_path": "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "1a1d01f23dbb8aaa2e2a6b7c5f3fedbeb2c1da3060be90465a5ab311751af9cc", "sha256_in_prefix": "1a1d01f23dbb8aaa2e2a6b7c5f3fedbeb2c1da3060be90465a5ab311751af9cc", "size_in_bytes": 110}, {"_path": "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "fd33ab3714fce7006bb3c021dbd8e8e217f3e3268d7c1e6728b69b081e4f568b", "sha256_in_prefix": "fd33ab3714fce7006bb3c021dbd8e8e217f3e3268d7c1e6728b69b081e4f568b", "size_in_bytes": 100}, {"_path": "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "649844f60da138e54b9bcc13338c62c5f97be8612cb6249ac10f84bbb97ffc88", "sha256_in_prefix": "649844f60da138e54b9bcc13338c62c5f97be8612cb6249ac10f84bbb97ffc88", "size_in_bytes": 1200}, {"_path": "lib/python3.11/site-packages/debugpy-1.8.17.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "e8e9ba25311aaa45a78fef7eee424e26bf7cf2c4ceea24ae88ae0df57e912e98", "sha256_in_prefix": "e8e9ba25311aaa45a78fef7eee424e26bf7cf2c4ceea24ae88ae0df57e912e98", "size_in_bytes": 8}], "paths_version": 1}, "requested_spec": "None", "sha256": "0b07c46d15fc32e9eb35a74c56de4fa7bac5c36cf4a05f326e73baa3273a5520", "size": 2666646, "subdir": "osx-64", "timestamp": 1758162088000, "url": "https://conda.anaconda.org/conda-forge/osx-64/debugpy-1.8.17-py311h1854d6b_0.conda", "version": "1.8.17"}