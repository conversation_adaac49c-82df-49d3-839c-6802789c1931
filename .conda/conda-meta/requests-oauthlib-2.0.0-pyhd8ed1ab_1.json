{"build": "pyhd8ed1ab_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["oauthlib >=3.0.0", "python >=3.9", "requests >=2.0.0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/requests-oauthlib-2.0.0-pyhd8ed1ab_1", "files": ["lib/python3.11/site-packages/requests_oauthlib-2.0.0.dist-info/INSTALLER", "lib/python3.11/site-packages/requests_oauthlib-2.0.0.dist-info/LICENSE", "lib/python3.11/site-packages/requests_oauthlib-2.0.0.dist-info/METADATA", "lib/python3.11/site-packages/requests_oauthlib-2.0.0.dist-info/RECORD", "lib/python3.11/site-packages/requests_oauthlib-2.0.0.dist-info/REQUESTED", "lib/python3.11/site-packages/requests_oauthlib-2.0.0.dist-info/WHEEL", "lib/python3.11/site-packages/requests_oauthlib-2.0.0.dist-info/direct_url.json", "lib/python3.11/site-packages/requests_oauthlib-2.0.0.dist-info/top_level.txt", "lib/python3.11/site-packages/requests_oauthlib/__init__.py", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__init__.py", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/douban.py", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/ebay.py", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/facebook.py", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/fitbit.py", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/instagram.py", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/mailchimp.py", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/plentymarkets.py", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/slack.py", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/weibo.py", "lib/python3.11/site-packages/requests_oauthlib/oauth1_auth.py", "lib/python3.11/site-packages/requests_oauthlib/oauth1_session.py", "lib/python3.11/site-packages/requests_oauthlib/oauth2_auth.py", "lib/python3.11/site-packages/requests_oauthlib/oauth2_session.py", "lib/python3.11/site-packages/requests_oauthlib/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/douban.cpython-311.pyc", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/ebay.cpython-311.pyc", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/facebook.cpython-311.pyc", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/fitbit.cpython-311.pyc", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/instagram.cpython-311.pyc", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/mailchimp.cpython-311.pyc", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/plentymarkets.cpython-311.pyc", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/slack.cpython-311.pyc", "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/weibo.cpython-311.pyc", "lib/python3.11/site-packages/requests_oauthlib/__pycache__/oauth1_auth.cpython-311.pyc", "lib/python3.11/site-packages/requests_oauthlib/__pycache__/oauth1_session.cpython-311.pyc", "lib/python3.11/site-packages/requests_oauthlib/__pycache__/oauth2_auth.cpython-311.pyc", "lib/python3.11/site-packages/requests_oauthlib/__pycache__/oauth2_session.cpython-311.pyc"], "fn": "requests-oauthlib-2.0.0-pyhd8ed1ab_1.conda", "license": "ISC", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/requests-oauthlib-2.0.0-pyhd8ed1ab_1", "type": 1}, "md5": "a283b764d8b155f81e904675ef5e1f4b", "name": "requests-o<PERSON><PERSON><PERSON>", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/requests-oauthlib-2.0.0-pyhd8ed1ab_1.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/requests_oauthlib-2.0.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/requests_oauthlib-2.0.0.dist-info/LICENSE", "path_type": "hardlink", "sha256": "ae01846afad8a8291fe6a08966f3015af9a8ff675d84b981f9793c122f7876e8", "sha256_in_prefix": "ae01846afad8a8291fe6a08966f3015af9a8ff675d84b981f9793c122f7876e8", "size_in_bytes": 745}, {"_path": "site-packages/requests_oauthlib-2.0.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "b416e92f3a18feca9d0dd7f627549c0134f23ecc3bc81acc6b8b9042c0fd79f6", "sha256_in_prefix": "b416e92f3a18feca9d0dd7f627549c0134f23ecc3bc81acc6b8b9042c0fd79f6", "size_in_bytes": 11129}, {"_path": "site-packages/requests_oauthlib-2.0.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "511634e93aa9ed5892fa8744ff54a53be93b4caee72754be99a7f42b21059cde", "sha256_in_prefix": "511634e93aa9ed5892fa8744ff54a53be93b4caee72754be99a7f42b21059cde", "size_in_bytes": 3258}, {"_path": "site-packages/requests_oauthlib-2.0.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/requests_oauthlib-2.0.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "a7178d5f925db427b9f0f51260ff6ea6673b8dd44f82f4f41a6f646f5487955c", "sha256_in_prefix": "a7178d5f925db427b9f0f51260ff6ea6673b8dd44f82f4f41a6f646f5487955c", "size_in_bytes": 109}, {"_path": "site-packages/requests_oauthlib-2.0.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "89f997a777e5955d1d7f7f3584b10e9c1e7ec2d1897e35e1e86c2c0f6e2eb945", "sha256_in_prefix": "89f997a777e5955d1d7f7f3584b10e9c1e7ec2d1897e35e1e86c2c0f6e2eb945", "size_in_bytes": 113}, {"_path": "site-packages/requests_oauthlib-2.0.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "bf1d51e360d604eeb887a8aef06728d05d354d54cf597491581695d46c154534", "sha256_in_prefix": "bf1d51e360d604eeb887a8aef06728d05d354d54cf597491581695d46c154534", "size_in_bytes": 18}, {"_path": "site-packages/requests_oauthlib/__init__.py", "path_type": "hardlink", "sha256": "721bd888809d722786ad173f7b95ad171697aba4ff7ac9a270bfc12d60de9dde", "sha256_in_prefix": "721bd888809d722786ad173f7b95ad171697aba4ff7ac9a270bfc12d60de9dde", "size_in_bytes": 548}, {"_path": "site-packages/requests_oauthlib/compliance_fixes/__init__.py", "path_type": "hardlink", "sha256": "7b4a9794bd454db7bb9314cb1664d837d8a914d664b3eb201a262a86bc81a1bc", "sha256_in_prefix": "7b4a9794bd454db7bb9314cb1664d837d8a914d664b3eb201a262a86bc81a1bc", "size_in_bytes": 377}, {"_path": "site-packages/requests_oauthlib/compliance_fixes/douban.py", "path_type": "hardlink", "sha256": "f514da7d1bb49d6ca2027f6bf39d3608463acafa3371a9d7576a709ca3d424dc", "sha256_in_prefix": "f514da7d1bb49d6ca2027f6bf39d3608463acafa3371a9d7576a709ca3d424dc", "size_in_bytes": 413}, {"_path": "site-packages/requests_oauthlib/compliance_fixes/ebay.py", "path_type": "hardlink", "sha256": "35153120c29530fd1050bbabf5112eb6cc9922a20ffc0c0e00d8e58e2f598001", "sha256_in_prefix": "35153120c29530fd1050bbabf5112eb6cc9922a20ffc0c0e00d8e58e2f598001", "size_in_bytes": 832}, {"_path": "site-packages/requests_oauthlib/compliance_fixes/facebook.py", "path_type": "hardlink", "sha256": "af26e21fcdfd630cf2635dd288603aefa8870814e1de6a8575a9b9b6ac74e352", "sha256_in_prefix": "af26e21fcdfd630cf2635dd288603aefa8870814e1de6a8575a9b9b6ac74e352", "size_in_bytes": 995}, {"_path": "site-packages/requests_oauthlib/compliance_fixes/fitbit.py", "path_type": "hardlink", "sha256": "708e0f3e94e75c9a59c35498f8df2872c855da956f7a9ebbf5dfe944824f2268", "sha256_in_prefix": "708e0f3e94e75c9a59c35498f8df2872c855da956f7a9ebbf5dfe944824f2268", "size_in_bytes": 846}, {"_path": "site-packages/requests_oauthlib/compliance_fixes/instagram.py", "path_type": "hardlink", "sha256": "604bd758a9a133ac98e24dfbf15da636597854ac8e2646411f47d1bd4e9f0ccd", "sha256_in_prefix": "604bd758a9a133ac98e24dfbf15da636597854ac8e2646411f47d1bd4e9f0ccd", "size_in_bytes": 875}, {"_path": "site-packages/requests_oauthlib/compliance_fixes/mailchimp.py", "path_type": "hardlink", "sha256": "4951509676ee5d908b78abfa626860f5e48149c1c25d5f773d72c75a150f00dd", "sha256_in_prefix": "4951509676ee5d908b78abfa626860f5e48149c1c25d5f773d72c75a150f00dd", "size_in_bytes": 679}, {"_path": "site-packages/requests_oauthlib/compliance_fixes/plentymarkets.py", "path_type": "hardlink", "sha256": "b3b96b4d984835a9222a395b3884937874d25ec364706f307ca704565414328b", "sha256_in_prefix": "b3b96b4d984835a9222a395b3884937874d25ec364706f307ca704565414328b", "size_in_bytes": 737}, {"_path": "site-packages/requests_oauthlib/compliance_fixes/slack.py", "path_type": "hardlink", "sha256": "e19420d67cb4bd4fa17fc3498cac206448201f6381ecddda27d1addafedc55ce", "sha256_in_prefix": "e19420d67cb4bd4fa17fc3498cac206448201f6381ecddda27d1addafedc55ce", "size_in_bytes": 1380}, {"_path": "site-packages/requests_oauthlib/compliance_fixes/weibo.py", "path_type": "hardlink", "sha256": "4cbf2ebb85cc0ff321f6d5c828c340f134f580e22b6818a94caa17912a51dac3", "sha256_in_prefix": "4cbf2ebb85cc0ff321f6d5c828c340f134f580e22b6818a94caa17912a51dac3", "size_in_bytes": 385}, {"_path": "site-packages/requests_oauthlib/oauth1_auth.py", "path_type": "hardlink", "sha256": "1775df85538357cd279c210f0c7b9e535826ab18618c64e69c31181ad458c097", "sha256_in_prefix": "1775df85538357cd279c210f0c7b9e535826ab18618c64e69c31181ad458c097", "size_in_bytes": 3604}, {"_path": "site-packages/requests_oauthlib/oauth1_session.py", "path_type": "hardlink", "sha256": "03a8fb0a043c9f622be009fef14d09f65c9435e63c1f959f7f92393d61f39477", "sha256_in_prefix": "03a8fb0a043c9f622be009fef14d09f65c9435e63c1f959f7f92393d61f39477", "size_in_bytes": 16942}, {"_path": "site-packages/requests_oauthlib/oauth2_auth.py", "path_type": "hardlink", "sha256": "317b3cbc86c13a15e3f4d2e2c3ff89db8939e8bfd7449d2d147fc4329b216149", "sha256_in_prefix": "317b3cbc86c13a15e3f4d2e2c3ff89db8939e8bfd7449d2d147fc4329b216149", "size_in_bytes": 1508}, {"_path": "site-packages/requests_oauthlib/oauth2_session.py", "path_type": "hardlink", "sha256": "2b85ed769353894e83abc26f12c8fc694a4c75abd6482640b9339cff1859701c", "sha256_in_prefix": "2b85ed769353894e83abc26f12c8fc694a4c75abd6482640b9339cff1859701c", "size_in_bytes": 23932}, {"_path": "lib/python3.11/site-packages/requests_oauthlib/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/douban.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/ebay.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/facebook.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/fitbit.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/instagram.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/mailchimp.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/plentymarkets.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/slack.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests_oauthlib/compliance_fixes/__pycache__/weibo.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests_oauthlib/__pycache__/oauth1_auth.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests_oauthlib/__pycache__/oauth1_session.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests_oauthlib/__pycache__/oauth2_auth.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/requests_oauthlib/__pycache__/oauth2_session.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "75ef0072ae6691f5ca9709fe6a2570b98177b49d0231a6749ac4e610da934cab", "size": 25875, "subdir": "noarch", "timestamp": 1733772348000, "url": "https://conda.anaconda.org/conda-forge/noarch/requests-oauthlib-2.0.0-pyhd8ed1ab_1.conda", "version": "2.0.0"}