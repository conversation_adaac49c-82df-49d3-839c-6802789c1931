{"build": "py311h98b24dd_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": ["__osx >=10.13"], "depends": ["__osx >=10.13", "huggingface_hub >=0.16.4,<1.0", "libcxx >=19", "python >=3.11,<3.12.0a0", "python_abi 3.11.* *_cp311"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/tokenizers-0.22.1-py311h98b24dd_0", "files": ["lib/python3.11/site-packages/tokenizers-0.22.1.dist-info/INSTALLER", "lib/python3.11/site-packages/tokenizers-0.22.1.dist-info/METADATA", "lib/python3.11/site-packages/tokenizers-0.22.1.dist-info/RECORD", "lib/python3.11/site-packages/tokenizers-0.22.1.dist-info/REQUESTED", "lib/python3.11/site-packages/tokenizers-0.22.1.dist-info/WHEEL", "lib/python3.11/site-packages/tokenizers-0.22.1.dist-info/direct_url.json", "lib/python3.11/site-packages/tokenizers/__init__.py", "lib/python3.11/site-packages/tokenizers/__init__.pyi", "lib/python3.11/site-packages/tokenizers/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/tokenizers/decoders/__init__.py", "lib/python3.11/site-packages/tokenizers/decoders/__init__.pyi", "lib/python3.11/site-packages/tokenizers/decoders/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/tokenizers/implementations/__init__.py", "lib/python3.11/site-packages/tokenizers/implementations/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/tokenizers/implementations/__pycache__/base_tokenizer.cpython-311.pyc", "lib/python3.11/site-packages/tokenizers/implementations/__pycache__/bert_wordpiece.cpython-311.pyc", "lib/python3.11/site-packages/tokenizers/implementations/__pycache__/byte_level_bpe.cpython-311.pyc", "lib/python3.11/site-packages/tokenizers/implementations/__pycache__/char_level_bpe.cpython-311.pyc", "lib/python3.11/site-packages/tokenizers/implementations/__pycache__/sentencepiece_bpe.cpython-311.pyc", "lib/python3.11/site-packages/tokenizers/implementations/__pycache__/sentencepiece_unigram.cpython-311.pyc", "lib/python3.11/site-packages/tokenizers/implementations/base_tokenizer.py", "lib/python3.11/site-packages/tokenizers/implementations/bert_wordpiece.py", "lib/python3.11/site-packages/tokenizers/implementations/byte_level_bpe.py", "lib/python3.11/site-packages/tokenizers/implementations/char_level_bpe.py", "lib/python3.11/site-packages/tokenizers/implementations/sentencepiece_bpe.py", "lib/python3.11/site-packages/tokenizers/implementations/sentencepiece_unigram.py", "lib/python3.11/site-packages/tokenizers/models/__init__.py", "lib/python3.11/site-packages/tokenizers/models/__init__.pyi", "lib/python3.11/site-packages/tokenizers/models/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/tokenizers/normalizers/__init__.py", "lib/python3.11/site-packages/tokenizers/normalizers/__init__.pyi", "lib/python3.11/site-packages/tokenizers/normalizers/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/tokenizers/pre_tokenizers/__init__.py", "lib/python3.11/site-packages/tokenizers/pre_tokenizers/__init__.pyi", "lib/python3.11/site-packages/tokenizers/pre_tokenizers/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/tokenizers/processors/__init__.py", "lib/python3.11/site-packages/tokenizers/processors/__init__.pyi", "lib/python3.11/site-packages/tokenizers/processors/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/tokenizers/tokenizers.abi3.so", "lib/python3.11/site-packages/tokenizers/tools/__init__.py", "lib/python3.11/site-packages/tokenizers/tools/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/tokenizers/tools/__pycache__/visualizer.cpython-311.pyc", "lib/python3.11/site-packages/tokenizers/tools/visualizer-styles.css", "lib/python3.11/site-packages/tokenizers/tools/visualizer.py", "lib/python3.11/site-packages/tokenizers/trainers/__init__.py", "lib/python3.11/site-packages/tokenizers/trainers/__init__.pyi", "lib/python3.11/site-packages/tokenizers/trainers/__pycache__/__init__.cpython-311.pyc"], "fn": "tokenizers-0.22.1-py311h98b24dd_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/tokenizers-0.22.1-py311h98b24dd_0", "type": 1}, "md5": "007627ff69316bdade73490272776a71", "name": "tokenizers", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/tokenizers-0.22.1-py311h98b24dd_0.conda", "paths_data": {"paths": [{"_path": "lib/python3.11/site-packages/tokenizers-0.22.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.11/site-packages/tokenizers-0.22.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "ddc92f061ff422fb05d19da58e9161c5098da143c38aa23779bf8a5b48ce76ca", "sha256_in_prefix": "ddc92f061ff422fb05d19da58e9161c5098da143c38aa23779bf8a5b48ce76ca", "size_in_bytes": 6779}, {"_path": "lib/python3.11/site-packages/tokenizers-0.22.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "6044e64657a78394257453dc2ce22f52fa01470a06f43b22fd4d8d3b5e919e7c", "sha256_in_prefix": "6044e64657a78394257453dc2ce22f52fa01470a06f43b22fd4d8d3b5e919e7c", "size_in_bytes": 3886}, {"_path": "lib/python3.11/site-packages/tokenizers-0.22.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/tokenizers-0.22.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "68ff6134f1bf8943e87d9fcebed104979273c5b138e051b74e8cd6b27e100cf8", "sha256_in_prefix": "68ff6134f1bf8943e87d9fcebed104979273c5b138e051b74e8cd6b27e100cf8", "size_in_bytes": 104}, {"_path": "lib/python3.11/site-packages/tokenizers-0.22.1.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "d742c441ebf2866e79b08916f484f10225609ea1a5641a694cce8d611778bc25", "sha256_in_prefix": "d742c441ebf2866e79b08916f484f10225609ea1a5641a694cce8d611778bc25", "size_in_bytes": 114}, {"_path": "lib/python3.11/site-packages/tokenizers/__init__.py", "path_type": "hardlink", "sha256": "644e596a052fa1b05272b1c141d1286e1c78c2a3346ecabd17d68b62404d8d84", "sha256_in_prefix": "644e596a052fa1b05272b1c141d1286e1c78c2a3346ecabd17d68b62404d8d84", "size_in_bytes": 2615}, {"_path": "lib/python3.11/site-packages/tokenizers/__init__.pyi", "path_type": "hardlink", "sha256": "e13a3591f6d3f361c4dad4accc927048a526cbb9b7639770e8eab3cbffb421aa", "sha256_in_prefix": "e13a3591f6d3f361c4dad4accc927048a526cbb9b7639770e8eab3cbffb421aa", "size_in_bytes": 45154}, {"_path": "lib/python3.11/site-packages/tokenizers/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "f9f8f4fdf319792ba638c57cd7fbc37bdc40d052ab13437ac4fef99ba15bb5a7", "sha256_in_prefix": "f9f8f4fdf319792ba638c57cd7fbc37bdc40d052ab13437ac4fef99ba15bb5a7", "size_in_bytes": 2553}, {"_path": "lib/python3.11/site-packages/tokenizers/decoders/__init__.py", "path_type": "hardlink", "sha256": "85fc0ce821540ef94c1862f8fb1b1a698cfcd4af4fe6b408e592f950758af18e", "sha256_in_prefix": "85fc0ce821540ef94c1862f8fb1b1a698cfcd4af4fe6b408e592f950758af18e", "size_in_bytes": 372}, {"_path": "lib/python3.11/site-packages/tokenizers/decoders/__init__.pyi", "path_type": "hardlink", "sha256": "ef68627710885a057e75b27115ef4a45e1acd9839701d8eafe4261248fe680b6", "sha256_in_prefix": "ef68627710885a057e75b27115ef4a45e1acd9839701d8eafe4261248fe680b6", "size_in_bytes": 7395}, {"_path": "lib/python3.11/site-packages/tokenizers/decoders/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "2551157e553dc57e9504ffef9c3485fe389d1c728255d5d4525ff4326cdc5151", "sha256_in_prefix": "2551157e553dc57e9504ffef9c3485fe389d1c728255d5d4525ff4326cdc5151", "size_in_bytes": 596}, {"_path": "lib/python3.11/site-packages/tokenizers/implementations/__init__.py", "path_type": "hardlink", "sha256": "57302ca65688a3bae5e0014ef0c8aeee283b31f663be89f055b959c34d7347a3", "sha256_in_prefix": "57302ca65688a3bae5e0014ef0c8aeee283b31f663be89f055b959c34d7347a3", "size_in_bytes": 310}, {"_path": "lib/python3.11/site-packages/tokenizers/implementations/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "ac82f51ed2bfee265e39843776f7a71f56783e5cda608a7b89b053970f558fb1", "sha256_in_prefix": "ac82f51ed2bfee265e39843776f7a71f56783e5cda608a7b89b053970f558fb1", "size_in_bytes": 640}, {"_path": "lib/python3.11/site-packages/tokenizers/implementations/__pycache__/base_tokenizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "979ca2911fb7bd58a2a3386d1b0f278a73e6ca0645348e22b69566ca1fd253d8", "sha256_in_prefix": "979ca2911fb7bd58a2a3386d1b0f278a73e6ca0645348e22b69566ca1fd253d8", "size_in_bytes": 21900}, {"_path": "lib/python3.11/site-packages/tokenizers/implementations/__pycache__/bert_wordpiece.cpython-311.pyc", "path_type": "hardlink", "sha256": "c1e1f89d95ffbf55c49bd9c80aebcef627d7386ee68762d50cccfd2eb38b0fe4", "sha256_in_prefix": "c1e1f89d95ffbf55c49bd9c80aebcef627d7386ee68762d50cccfd2eb38b0fe4", "size_in_bytes": 6818}, {"_path": "lib/python3.11/site-packages/tokenizers/implementations/__pycache__/byte_level_bpe.cpython-311.pyc", "path_type": "hardlink", "sha256": "450fe29700b703f677bf855bda46fed499e33e5afb51e38e1026b90c1188d0e5", "sha256_in_prefix": "450fe29700b703f677bf855bda46fed499e33e5afb51e38e1026b90c1188d0e5", "size_in_bytes": 5467}, {"_path": "lib/python3.11/site-packages/tokenizers/implementations/__pycache__/char_level_bpe.cpython-311.pyc", "path_type": "hardlink", "sha256": "bfb6d6eb663a8fa83672e7442f01d96e0bc982922f75a6228b530159420a231b", "sha256_in_prefix": "bfb6d6eb663a8fa83672e7442f01d96e0bc982922f75a6228b530159420a231b", "size_in_bytes": 6581}, {"_path": "lib/python3.11/site-packages/tokenizers/implementations/__pycache__/sentencepiece_bpe.cpython-311.pyc", "path_type": "hardlink", "sha256": "6bd56c2a7f5a9f43207b7f88e713f45ce11fa478fe03b1b59b93b5da218abf20", "sha256_in_prefix": "6bd56c2a7f5a9f43207b7f88e713f45ce11fa478fe03b1b59b93b5da218abf20", "size_in_bytes": 5128}, {"_path": "lib/python3.11/site-packages/tokenizers/implementations/__pycache__/sentencepiece_unigram.cpython-311.pyc", "path_type": "hardlink", "sha256": "fcd8a55f92c0212eb8c37e8a11b9f463c8002990dcf69c21b3fa2254bb08ad29", "sha256_in_prefix": "fcd8a55f92c0212eb8c37e8a11b9f463c8002990dcf69c21b3fa2254bb08ad29", "size_in_bytes": 9423}, {"_path": "lib/python3.11/site-packages/tokenizers/implementations/base_tokenizer.py", "path_type": "hardlink", "sha256": "1f32ba366dfa2f125a29366b20cd42c7f2ddf60363789741f44dbd3346981812", "sha256_in_prefix": "1f32ba366dfa2f125a29366b20cd42c7f2ddf60363789741f44dbd3346981812", "size_in_bytes": 15791}, {"_path": "lib/python3.11/site-packages/tokenizers/implementations/bert_wordpiece.py", "path_type": "hardlink", "sha256": "b0a0ae9b414a3d875280914937c2c37ab541a130d14aacaa49dadc9be96f42a2", "sha256_in_prefix": "b0a0ae9b414a3d875280914937c2c37ab541a130d14aacaa49dadc9be96f42a2", "size_in_bytes": 5520}, {"_path": "lib/python3.11/site-packages/tokenizers/implementations/byte_level_bpe.py", "path_type": "hardlink", "sha256": "8817a933fcf5b392b2ef314356b60b7372f96f262b228ba4efe934246b85d74b", "sha256_in_prefix": "8817a933fcf5b392b2ef314356b60b7372f96f262b228ba4efe934246b85d74b", "size_in_bytes": 4272}, {"_path": "lib/python3.11/site-packages/tokenizers/implementations/char_level_bpe.py", "path_type": "hardlink", "sha256": "35a83f1c5abc46f72e72a8bc321575fb4c6da11d02ec58c739e70555444beecb", "sha256_in_prefix": "35a83f1c5abc46f72e72a8bc321575fb4c6da11d02ec58c739e70555444beecb", "size_in_bytes": 5449}, {"_path": "lib/python3.11/site-packages/tokenizers/implementations/sentencepiece_bpe.py", "path_type": "hardlink", "sha256": "734f1f29fea2f7613646c2a0b31cbb2f365f617f3e3000874911bc53f239cad6", "sha256_in_prefix": "734f1f29fea2f7613646c2a0b31cbb2f365f617f3e3000874911bc53f239cad6", "size_in_bytes": 3721}, {"_path": "lib/python3.11/site-packages/tokenizers/implementations/sentencepiece_unigram.py", "path_type": "hardlink", "sha256": "4988955cbf19b6a2d72a9baac27c26adfc601a8b6ef320243aeedd2f3b445c8a", "sha256_in_prefix": "4988955cbf19b6a2d72a9baac27c26adfc601a8b6ef320243aeedd2f3b445c8a", "size_in_bytes": 7580}, {"_path": "lib/python3.11/site-packages/tokenizers/models/__init__.py", "path_type": "hardlink", "sha256": "7896781d3010669c672882cdca559a4c5ab15f2f816ba38ab3058de3b7de795f", "sha256_in_prefix": "7896781d3010669c672882cdca559a4c5ab15f2f816ba38ab3058de3b7de795f", "size_in_bytes": 176}, {"_path": "lib/python3.11/site-packages/tokenizers/models/__init__.pyi", "path_type": "hardlink", "sha256": "7253d3c22ca3cfb16555d12ec28ff759afd3990adb6615b44869a737295ea676", "sha256_in_prefix": "7253d3c22ca3cfb16555d12ec28ff759afd3990adb6615b44869a737295ea676", "size_in_bytes": 16929}, {"_path": "lib/python3.11/site-packages/tokenizers/models/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "2467762744cf3cfc2ab5d8471c47e5beabfa907b59b67b18bf0ff6e657a49385", "sha256_in_prefix": "2467762744cf3cfc2ab5d8471c47e5beabfa907b59b67b18bf0ff6e657a49385", "size_in_bytes": 360}, {"_path": "lib/python3.11/site-packages/tokenizers/normalizers/__init__.py", "path_type": "hardlink", "sha256": "ff4eb0e1ca9122dbde12021d75868b3127209123a4200308cd809eb1be400385", "sha256_in_prefix": "ff4eb0e1ca9122dbde12021d75868b3127209123a4200308cd809eb1be400385", "size_in_bytes": 841}, {"_path": "lib/python3.11/site-packages/tokenizers/normalizers/__init__.pyi", "path_type": "hardlink", "sha256": "95216a0dbfe53d905f471106f7d11c144694d4796722121052eef3648c8fe006", "sha256_in_prefix": "95216a0dbfe53d905f471106f7d11c144694d4796722121052eef3648c8fe006", "size_in_bytes": 20898}, {"_path": "lib/python3.11/site-packages/tokenizers/normalizers/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "00e556f541b42437e13b2609d91f264f1b23e31a2b9a9c46797240f4f33a6a01", "sha256_in_prefix": "00e556f541b42437e13b2609d91f264f1b23e31a2b9a9c46797240f4f33a6a01", "size_in_bytes": 1284}, {"_path": "lib/python3.11/site-packages/tokenizers/pre_tokenizers/__init__.py", "path_type": "hardlink", "sha256": "295f7e12c03290610d514ce419609bbf89fa60cea161ad617e763e83306930d1", "sha256_in_prefix": "295f7e12c03290610d514ce419609bbf89fa60cea161ad617e763e83306930d1", "size_in_bytes": 598}, {"_path": "lib/python3.11/site-packages/tokenizers/pre_tokenizers/__init__.pyi", "path_type": "hardlink", "sha256": "9fa0450a58719bccbb9a2082d2525dee855ea3ca23de43e0dad53d39b5783c65", "sha256_in_prefix": "9fa0450a58719bccbb9a2082d2525dee855ea3ca23de43e0dad53d39b5783c65", "size_in_bytes": 26556}, {"_path": "lib/python3.11/site-packages/tokenizers/pre_tokenizers/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "83d787674c123dedea2ff08c0b6363f96ef8cab4fbf267e5475f0c24677b5c8f", "sha256_in_prefix": "83d787674c123dedea2ff08c0b6363f96ef8cab4fbf267e5475f0c24677b5c8f", "size_in_bytes": 684}, {"_path": "lib/python3.11/site-packages/tokenizers/processors/__init__.py", "path_type": "hardlink", "sha256": "c4cd8310ac0ab4722e987b2ccccf00324abe0256aabc16455d680b53c48d84e6", "sha256_in_prefix": "c4cd8310ac0ab4722e987b2ccccf00324abe0256aabc16455d680b53c48d84e6", "size_in_bytes": 307}, {"_path": "lib/python3.11/site-packages/tokenizers/processors/__init__.pyi", "path_type": "hardlink", "sha256": "871efaed963c4878716ff8625cf4719be7ff29ca11e170f1eef7cad9cd2547e4", "sha256_in_prefix": "871efaed963c4878716ff8625cf4719be7ff29ca11e170f1eef7cad9cd2547e4", "size_in_bytes": 11357}, {"_path": "lib/python3.11/site-packages/tokenizers/processors/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "54f53758f8726c10f6e918c33b777f92e3ce81917d60c674b2b4d4f66c1d011e", "sha256_in_prefix": "54f53758f8726c10f6e918c33b777f92e3ce81917d60c674b2b4d4f66c1d011e", "size_in_bytes": 445}, {"_path": "lib/python3.11/site-packages/tokenizers/tokenizers.abi3.so", "path_type": "hardlink", "sha256": "86c4c57c6d8205f9754e72b1b268cb9dc55a26a80ca62535cb45a4c8d861bbdd", "sha256_in_prefix": "86c4c57c6d8205f9754e72b1b268cb9dc55a26a80ca62535cb45a4c8d861bbdd", "size_in_bytes": 8827336}, {"_path": "lib/python3.11/site-packages/tokenizers/tools/__init__.py", "path_type": "hardlink", "sha256": "c46f1c681f4e1c2f1c6c1d354b9bd8575e0767184ee9e59b2de86c6fbd29a62a", "sha256_in_prefix": "c46f1c681f4e1c2f1c6c1d354b9bd8575e0767184ee9e59b2de86c6fbd29a62a", "size_in_bytes": 55}, {"_path": "lib/python3.11/site-packages/tokenizers/tools/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "718edd45e77ce62c4b10390c3ea28ac5888ae3f720f81b3e843db62a9e6c7315", "sha256_in_prefix": "718edd45e77ce62c4b10390c3ea28ac5888ae3f720f81b3e843db62a9e6c7315", "size_in_bytes": 260}, {"_path": "lib/python3.11/site-packages/tokenizers/tools/__pycache__/visualizer.cpython-311.pyc", "path_type": "hardlink", "sha256": "1ea02a4df957a0985728dfc8ce1b0020689f169b53e70ccf6123b110122cda25", "sha256_in_prefix": "1ea02a4df957a0985728dfc8ce1b0020689f169b53e70ccf6123b110122cda25", "size_in_bytes": 17266}, {"_path": "lib/python3.11/site-packages/tokenizers/tools/visualizer-styles.css", "path_type": "hardlink", "sha256": "cc0c9dab5a06583f10125978f9ec8bf0b970d01d6cb72fe1a48137b58c4bd369", "sha256_in_prefix": "cc0c9dab5a06583f10125978f9ec8bf0b970d01d6cb72fe1a48137b58c4bd369", "size_in_bytes": 4850}, {"_path": "lib/python3.11/site-packages/tokenizers/tools/visualizer.py", "path_type": "hardlink", "sha256": "cc45c42c22d2c572ff4604200938d542b7371bfa6db83c2bedd79a53de5bddd0", "sha256_in_prefix": "cc45c42c22d2c572ff4604200938d542b7371bfa6db83c2bedd79a53de5bddd0", "size_in_bytes": 14625}, {"_path": "lib/python3.11/site-packages/tokenizers/trainers/__init__.py", "path_type": "hardlink", "sha256": "513bb6d8019ca7be88be95b8e712d16c95844f4e0dc4f5ba35f09bd98633d043", "sha256_in_prefix": "513bb6d8019ca7be88be95b8e712d16c95844f4e0dc4f5ba35f09bd98633d043", "size_in_bytes": 248}, {"_path": "lib/python3.11/site-packages/tokenizers/trainers/__init__.pyi", "path_type": "hardlink", "sha256": "3b076254e94c5e153984eabb6b94d8606d6fc377e4f274ea1fcf2d56bd39359d", "sha256_in_prefix": "3b076254e94c5e153984eabb6b94d8606d6fc377e4f274ea1fcf2d56bd39359d", "size_in_bytes": 5860}, {"_path": "lib/python3.11/site-packages/tokenizers/trainers/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "32d9bdd5b2f8e2825ff8bb9fde4c641ce00d9c44717f302fc10ad91b19e6ba49", "sha256_in_prefix": "32d9bdd5b2f8e2825ff8bb9fde4c641ce00d9c44717f302fc10ad91b19e6ba49", "size_in_bytes": 401}], "paths_version": 1}, "requested_spec": "None", "sha256": "d68a7fe3468d531b7f45663a9340b206a0fbec9895e17a9ad025fc28830b8e9b", "size": 2340185, "subdir": "osx-64", "timestamp": 1758299166000, "url": "https://conda.anaconda.org/conda-forge/osx-64/tokenizers-0.22.1-py311h98b24dd_0.conda", "version": "0.22.1"}