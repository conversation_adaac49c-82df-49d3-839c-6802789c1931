{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["opentelemetry-api ~=1.12", "opentelemetry-instrumentation 0.58b0", "opentelemetry-instrumentation-asgi 0.58b0", "opentelemetry-semantic-conventions 0.58b0", "opentelemetry-util-http 0.58b0", "python >=3.10"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-instrumentation-fastapi-0.58b0-pyhd8ed1ab_0", "files": ["lib/python3.11/site-packages/opentelemetry/instrumentation/fastapi/__init__.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/fastapi/package.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/fastapi/version.py", "lib/python3.11/site-packages/opentelemetry_instrumentation_fastapi-0.58b0.dist-info/INSTALLER", "lib/python3.11/site-packages/opentelemetry_instrumentation_fastapi-0.58b0.dist-info/METADATA", "lib/python3.11/site-packages/opentelemetry_instrumentation_fastapi-0.58b0.dist-info/RECORD", "lib/python3.11/site-packages/opentelemetry_instrumentation_fastapi-0.58b0.dist-info/REQUESTED", "lib/python3.11/site-packages/opentelemetry_instrumentation_fastapi-0.58b0.dist-info/WHEEL", "lib/python3.11/site-packages/opentelemetry_instrumentation_fastapi-0.58b0.dist-info/direct_url.json", "lib/python3.11/site-packages/opentelemetry_instrumentation_fastapi-0.58b0.dist-info/entry_points.txt", "lib/python3.11/site-packages/opentelemetry_instrumentation_fastapi-0.58b0.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/opentelemetry/instrumentation/fastapi/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/fastapi/__pycache__/package.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/fastapi/__pycache__/version.cpython-311.pyc"], "fn": "opentelemetry-instrumentation-fastapi-0.58b0-pyhd8ed1ab_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-instrumentation-fastapi-0.58b0-pyhd8ed1ab_0", "type": 1}, "md5": "5ca96fa188c9cc8aca376653b3609f91", "name": "opentelemetry-instrumentation-fastapi", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-instrumentation-fastapi-0.58b0-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/opentelemetry/instrumentation/fastapi/__init__.py", "path_type": "hardlink", "sha256": "0e35ec2371aa1f2d8abcba10faf508ac8bfa15161a47fbd4a48a5bb29e67efec", "sha256_in_prefix": "0e35ec2371aa1f2d8abcba10faf508ac8bfa15161a47fbd4a48a5bb29e67efec", "size_in_bytes": 22266}, {"_path": "site-packages/opentelemetry/instrumentation/fastapi/package.py", "path_type": "hardlink", "sha256": "913b8369e1e5976d8cb2d147cb7e5db145b5905e8f14b9e385004e0f5c8f041e", "sha256_in_prefix": "913b8369e1e5976d8cb2d147cb7e5db145b5905e8f14b9e385004e0f5c8f041e", "size_in_bytes": 679}, {"_path": "site-packages/opentelemetry/instrumentation/fastapi/version.py", "path_type": "hardlink", "sha256": "7df3fea041072f2c50a9ace13e0a7b0ae35b56da7fb236f5328dbfccd65bf905", "sha256_in_prefix": "7df3fea041072f2c50a9ace13e0a7b0ae35b56da7fb236f5328dbfccd65bf905", "size_in_bytes": 608}, {"_path": "site-packages/opentelemetry_instrumentation_fastapi-0.58b0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/opentelemetry_instrumentation_fastapi-0.58b0.dist-info/METADATA", "path_type": "hardlink", "sha256": "7a72f5180d4980c54de659c86e3dacdf8c1e24ff3f6db96a2613e17573887613", "sha256_in_prefix": "7a72f5180d4980c54de659c86e3dacdf8c1e24ff3f6db96a2613e17573887613", "size_in_bytes": 2178}, {"_path": "site-packages/opentelemetry_instrumentation_fastapi-0.58b0.dist-info/RECORD", "path_type": "hardlink", "sha256": "acf788182448477fc4d067cbbdd3c3770421badc1668c2a72917590befa81f7d", "sha256_in_prefix": "acf788182448477fc4d067cbbdd3c3770421badc1668c2a72917590befa81f7d", "size_in_bytes": 1474}, {"_path": "site-packages/opentelemetry_instrumentation_fastapi-0.58b0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry_instrumentation_fastapi-0.58b0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "sha256_in_prefix": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "size_in_bytes": 87}, {"_path": "site-packages/opentelemetry_instrumentation_fastapi-0.58b0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "b5b42313b2bb52b2df33a6a9d4fc7baa5400e7ab21cd1f3afa399da3e8a6ea29", "sha256_in_prefix": "b5b42313b2bb52b2df33a6a9d4fc7baa5400e7ab21cd1f3afa399da3e8a6ea29", "size_in_bytes": 133}, {"_path": "site-packages/opentelemetry_instrumentation_fastapi-0.58b0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "3a723fdba31a8c4be41b3bd836e3caf98a8a4b8740faf61e3fda8c62dd84b53c", "sha256_in_prefix": "3a723fdba31a8c4be41b3bd836e3caf98a8a4b8740faf61e3fda8c62dd84b53c", "size_in_bytes": 97}, {"_path": "site-packages/opentelemetry_instrumentation_fastapi-0.58b0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "sha256_in_prefix": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "size_in_bytes": 11357}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/fastapi/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/fastapi/__pycache__/package.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/fastapi/__pycache__/version.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "d414c72c2d23b81e74b4966158ba606f7f512237c479bdd4575bc0c4f09ff4a2", "size": 22078, "subdir": "noarch", "timestamp": 1757779207000, "url": "https://conda.anaconda.org/conda-forge/noarch/opentelemetry-instrumentation-fastapi-0.58b0-pyhd8ed1ab_0.conda", "version": "0.58b0"}