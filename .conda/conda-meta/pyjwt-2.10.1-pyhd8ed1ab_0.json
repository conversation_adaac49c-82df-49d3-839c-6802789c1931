{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": ["cryptography >=3.4.0"], "depends": ["python >=3.9"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/pyjwt-2.10.1-pyhd8ed1ab_0", "files": ["lib/python3.11/site-packages/PyJWT-2.10.1.dist-info/AUTHORS.rst", "lib/python3.11/site-packages/PyJWT-2.10.1.dist-info/INSTALLER", "lib/python3.11/site-packages/PyJWT-2.10.1.dist-info/LICENSE", "lib/python3.11/site-packages/PyJWT-2.10.1.dist-info/METADATA", "lib/python3.11/site-packages/PyJWT-2.10.1.dist-info/RECORD", "lib/python3.11/site-packages/PyJWT-2.10.1.dist-info/REQUESTED", "lib/python3.11/site-packages/PyJWT-2.10.1.dist-info/WHEEL", "lib/python3.11/site-packages/PyJWT-2.10.1.dist-info/direct_url.json", "lib/python3.11/site-packages/PyJWT-2.10.1.dist-info/top_level.txt", "lib/python3.11/site-packages/jwt/__init__.py", "lib/python3.11/site-packages/jwt/algorithms.py", "lib/python3.11/site-packages/jwt/api_jwk.py", "lib/python3.11/site-packages/jwt/api_jws.py", "lib/python3.11/site-packages/jwt/api_jwt.py", "lib/python3.11/site-packages/jwt/exceptions.py", "lib/python3.11/site-packages/jwt/help.py", "lib/python3.11/site-packages/jwt/jwk_set_cache.py", "lib/python3.11/site-packages/jwt/jwks_client.py", "lib/python3.11/site-packages/jwt/py.typed", "lib/python3.11/site-packages/jwt/types.py", "lib/python3.11/site-packages/jwt/utils.py", "lib/python3.11/site-packages/jwt/warnings.py", "lib/python3.11/site-packages/jwt/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/jwt/__pycache__/algorithms.cpython-311.pyc", "lib/python3.11/site-packages/jwt/__pycache__/api_jwk.cpython-311.pyc", "lib/python3.11/site-packages/jwt/__pycache__/api_jws.cpython-311.pyc", "lib/python3.11/site-packages/jwt/__pycache__/api_jwt.cpython-311.pyc", "lib/python3.11/site-packages/jwt/__pycache__/exceptions.cpython-311.pyc", "lib/python3.11/site-packages/jwt/__pycache__/help.cpython-311.pyc", "lib/python3.11/site-packages/jwt/__pycache__/jwk_set_cache.cpython-311.pyc", "lib/python3.11/site-packages/jwt/__pycache__/jwks_client.cpython-311.pyc", "lib/python3.11/site-packages/jwt/__pycache__/types.cpython-311.pyc", "lib/python3.11/site-packages/jwt/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/jwt/__pycache__/warnings.cpython-311.pyc"], "fn": "pyjwt-2.10.1-pyhd8ed1ab_0.conda", "license": "MIT", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/pyjwt-2.10.1-pyhd8ed1ab_0", "type": 1}, "md5": "84c5c40ea7c5bbc6243556e5daed20e7", "name": "pyjwt", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/pyjwt-2.10.1-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/PyJWT-2.10.1.dist-info/AUTHORS.rst", "path_type": "hardlink", "sha256": "925ce43461029eedbf558ec0b7cf7fc4b045def5120ebb97937c70814071cf07", "sha256_in_prefix": "925ce43461029eedbf558ec0b7cf7fc4b045def5120ebb97937c70814071cf07", "size_in_bytes": 322}, {"_path": "site-packages/PyJWT-2.10.1.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/PyJWT-2.10.1.dist-info/LICENSE", "path_type": "hardlink", "sha256": "797a7a20231d4c433e9f1911db1731d06b5828b98f499819a034f7c0f56f5ce5", "sha256_in_prefix": "797a7a20231d4c433e9f1911db1731d06b5828b98f499819a034f7c0f56f5ce5", "size_in_bytes": 1085}, {"_path": "site-packages/PyJWT-2.10.1.dist-info/METADATA", "path_type": "hardlink", "sha256": "1247b017a0fa294f1219a690cd561fc54514d4ffe0b3f769d6961392862f031f", "sha256_in_prefix": "1247b017a0fa294f1219a690cd561fc54514d4ffe0b3f769d6961392862f031f", "size_in_bytes": 3990}, {"_path": "site-packages/PyJWT-2.10.1.dist-info/RECORD", "path_type": "hardlink", "sha256": "94d38ef6f0e1d1145d17d6f6424f9d647b970a8a6ac33c4de889cd0d5df44739", "sha256_in_prefix": "94d38ef6f0e1d1145d17d6f6424f9d647b970a8a6ac33c4de889cd0d5df44739", "size_in_bytes": 2200}, {"_path": "site-packages/PyJWT-2.10.1.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/PyJWT-2.10.1.dist-info/WHEEL", "path_type": "hardlink", "sha256": "3d9504c5d7fbd548bfb28ebb417a724ae1ed0a2dfe277c2f1783912ba93f4bc5", "sha256_in_prefix": "3d9504c5d7fbd548bfb28ebb417a724ae1ed0a2dfe277c2f1783912ba93f4bc5", "size_in_bytes": 91}, {"_path": "site-packages/PyJWT-2.10.1.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "481a3ddabf67d49e1cb2831c621679acbcd3d968956b67e6fdcf6b8b05158ecb", "sha256_in_prefix": "481a3ddabf67d49e1cb2831c621679acbcd3d968956b67e6fdcf6b8b05158ecb", "size_in_bytes": 101}, {"_path": "site-packages/PyJWT-2.10.1.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "44fe431cdc896ccab691ad0599f4e0a12690ce1ededebe57b825823bc6b4d24f", "sha256_in_prefix": "44fe431cdc896ccab691ad0599f4e0a12690ce1ededebe57b825823bc6b4d24f", "size_in_bytes": 4}, {"_path": "site-packages/jwt/__init__.py", "path_type": "hardlink", "sha256": "541daf14ab9ba138dc0c6799f2bf94a8afddcf736c412404ab24922026f2f178", "sha256_in_prefix": "541daf14ab9ba138dc0c6799f2bf94a8afddcf736c412404ab24922026f2f178", "size_in_bytes": 1711}, {"_path": "site-packages/jwt/algorithms.py", "path_type": "hardlink", "sha256": "70aafe5c48a87b4981b6a24c09a1c4b30a95380d59f0fbabb7949bdc18bee411", "sha256_in_prefix": "70aafe5c48a87b4981b6a24c09a1c4b30a95380d59f0fbabb7949bdc18bee411", "size_in_bytes": 30409}, {"_path": "site-packages/jwt/api_jwk.py", "path_type": "hardlink", "sha256": "e85d6beeb9a6f15e6a12704a03fc4c8d2f51ed5a0035ed7f04bd680f616b0231", "sha256_in_prefix": "e85d6beeb9a6f15e6a12704a03fc4c8d2f51ed5a0035ed7f04bd680f616b0231", "size_in_bytes": 4451}, {"_path": "site-packages/jwt/api_jws.py", "path_type": "hardlink", "sha256": "68cf2fcea41fea646b030edb472f8ca23fe98d6b0a49543260af3de807cc8c95", "sha256_in_prefix": "68cf2fcea41fea646b030edb472f8ca23fe98d6b0a49543260af3de807cc8c95", "size_in_bytes": 11762}, {"_path": "site-packages/jwt/api_jwt.py", "path_type": "hardlink", "sha256": "3864f886893597903a147fca75cad4e60eaee8443c07b7a6d2bf6418cf526200", "sha256_in_prefix": "3864f886893597903a147fca75cad4e60eaee8443c07b7a6d2bf6418cf526200", "size_in_bytes": 14512}, {"_path": "site-packages/jwt/exceptions.py", "path_type": "hardlink", "sha256": "6d420e27ebfdb63a294cb4be158393737905c7958fe4866dee4b0dfc71351bd4", "sha256_in_prefix": "6d420e27ebfdb63a294cb4be158393737905c7958fe4866dee4b0dfc71351bd4", "size_in_bytes": 1211}, {"_path": "site-packages/jwt/help.py", "path_type": "hardlink", "sha256": "bc574dce342801c874e1708c6290a4c81d9b95aa8700602a42bb5ff6748f91d9", "sha256_in_prefix": "bc574dce342801c874e1708c6290a4c81d9b95aa8700602a42bb5ff6748f91d9", "size_in_bytes": 1808}, {"_path": "site-packages/jwt/jwk_set_cache.py", "path_type": "hardlink", "sha256": "8412a637e82253bf86dfb2ff5ca81cfce66ed9a878c1d6e3d59346fc6928484f", "sha256_in_prefix": "8412a637e82253bf86dfb2ff5ca81cfce66ed9a878c1d6e3d59346fc6928484f", "size_in_bytes": 959}, {"_path": "site-packages/jwt/jwks_client.py", "path_type": "hardlink", "sha256": "a7d6fe21b42aa36b4481ef598adde848f04535e3eac21a3de952db9d4ac7516b", "sha256_in_prefix": "a7d6fe21b42aa36b4481ef598adde848f04535e3eac21a3de952db9d4ac7516b", "size_in_bytes": 4259}, {"_path": "site-packages/jwt/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/jwt/types.py", "path_type": "hardlink", "sha256": "567846bff545bb96bbfe6acfa12081ec768d2eb25d84cf12ab5b127c483480b5", "sha256_in_prefix": "567846bff545bb96bbfe6acfa12081ec768d2eb25d84cf12ab5b127c483480b5", "size_in_bytes": 99}, {"_path": "site-packages/jwt/utils.py", "path_type": "hardlink", "sha256": "8713a3bc3061781621cfe4483e2133ed0f3c7525124cccc474a13f5b0d95749c", "sha256_in_prefix": "8713a3bc3061781621cfe4483e2133ed0f3c7525124cccc474a13f5b0d95749c", "size_in_bytes": 3640}, {"_path": "site-packages/jwt/warnings.py", "path_type": "hardlink", "sha256": "e745d63a7c8db086aacd425393a5c7362203ca488befadc6600f763234ca9a89", "sha256_in_prefix": "e745d63a7c8db086aacd425393a5c7362203ca488befadc6600f763234ca9a89", "size_in_bytes": 59}, {"_path": "lib/python3.11/site-packages/jwt/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jwt/__pycache__/algorithms.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jwt/__pycache__/api_jwk.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jwt/__pycache__/api_jws.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jwt/__pycache__/api_jwt.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jwt/__pycache__/exceptions.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jwt/__pycache__/help.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jwt/__pycache__/jwk_set_cache.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jwt/__pycache__/jwks_client.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jwt/__pycache__/types.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jwt/__pycache__/utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/jwt/__pycache__/warnings.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "158d8911e873e2a339c27768933747bf9c2aec1caa038f1b7b38a011734a956f", "size": 25093, "subdir": "noarch", "timestamp": 1732782523000, "url": "https://conda.anaconda.org/conda-forge/noarch/pyjwt-2.10.1-pyhd8ed1ab_0.conda", "version": "2.10.1"}