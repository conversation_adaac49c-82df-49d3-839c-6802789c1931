{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["opentelemetry-api ~=1.4", "opentelemetry-semantic-conventions 0.58b0", "packaging >=18.0", "python >=3.10", "setuptools >=16.0", "wrapt <2.0.0,>=1.0.0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-instrumentation-0.58b0-pyhd8ed1ab_0", "files": ["lib/python3.11/site-packages/opentelemetry/instrumentation/_semconv.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/auto_instrumentation/__init__.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/auto_instrumentation/_load.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/auto_instrumentation/sitecustomize.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/bootstrap.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/bootstrap_gen.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/dependencies.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/distro.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/environment_variables.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/instrumentor.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/propagators.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/py.typed", "lib/python3.11/site-packages/opentelemetry/instrumentation/sqlcommenter_utils.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/utils.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/version.py", "lib/python3.11/site-packages/opentelemetry_instrumentation-0.58b0.dist-info/INSTALLER", "lib/python3.11/site-packages/opentelemetry_instrumentation-0.58b0.dist-info/METADATA", "lib/python3.11/site-packages/opentelemetry_instrumentation-0.58b0.dist-info/RECORD", "lib/python3.11/site-packages/opentelemetry_instrumentation-0.58b0.dist-info/REQUESTED", "lib/python3.11/site-packages/opentelemetry_instrumentation-0.58b0.dist-info/WHEEL", "lib/python3.11/site-packages/opentelemetry_instrumentation-0.58b0.dist-info/direct_url.json", "lib/python3.11/site-packages/opentelemetry_instrumentation-0.58b0.dist-info/entry_points.txt", "lib/python3.11/site-packages/opentelemetry_instrumentation-0.58b0.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/_semconv.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/auto_instrumentation/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/auto_instrumentation/__pycache__/_load.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/auto_instrumentation/__pycache__/sitecustomize.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/bootstrap.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/bootstrap_gen.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/dependencies.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/distro.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/environment_variables.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/instrumentor.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/propagators.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/sqlcommenter_utils.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/version.cpython-311.pyc", "bin/opentelemetry-instrument", "bin/opentelemetry-bootstrap"], "fn": "opentelemetry-instrumentation-0.58b0-pyhd8ed1ab_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-instrumentation-0.58b0-pyhd8ed1ab_0", "type": 1}, "md5": "737df721803ceada1000d2f737735cb6", "name": "opentelemetry-instrumentation", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-instrumentation-0.58b0-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/opentelemetry/instrumentation/_semconv.py", "path_type": "hardlink", "sha256": "6156220eb25818f6fc317c244b976965926e07a87fc1b1df4c11d55ed10d5f9c", "sha256_in_prefix": "6156220eb25818f6fc317c244b976965926e07a87fc1b1df4c11d55ed10d5f9c", "size_in_bytes": 15629}, {"_path": "site-packages/opentelemetry/instrumentation/auto_instrumentation/__init__.py", "path_type": "hardlink", "sha256": "2eddb0c471a5b398787301bf15ff4a85c5cf8c4c8c2e67e8d17e7185ff0a59d0", "sha256_in_prefix": "2eddb0c471a5b398787301bf15ff4a85c5cf8c4c8c2e67e8d17e7185ff0a59d0", "size_in_bytes": 5991}, {"_path": "site-packages/opentelemetry/instrumentation/auto_instrumentation/_load.py", "path_type": "hardlink", "sha256": "b19b582223f4bb82ea1776083b36221302b2f820decd30ce2a6a2c9d43ed4e40", "sha256_in_prefix": "b19b582223f4bb82ea1776083b36221302b2f820decd30ce2a6a2c9d43ed4e40", "size_in_bytes": 7333}, {"_path": "site-packages/opentelemetry/instrumentation/auto_instrumentation/sitecustomize.py", "path_type": "hardlink", "sha256": "ddcfb83130a15563be3e97502e92073e9d0cf69643aa73c410dfa3721eafe265", "sha256_in_prefix": "ddcfb83130a15563be3e97502e92073e9d0cf69643aa73c410dfa3721eafe265", "size_in_bytes": 673}, {"_path": "site-packages/opentelemetry/instrumentation/bootstrap.py", "path_type": "hardlink", "sha256": "43ed63d46ed02974d3bc7e711861915f78c2a537ff36e841a32d97fccbccf6c8", "sha256_in_prefix": "43ed63d46ed02974d3bc7e711861915f78c2a537ff36e841a32d97fccbccf6c8", "size_in_bytes": 5428}, {"_path": "site-packages/opentelemetry/instrumentation/bootstrap_gen.py", "path_type": "hardlink", "sha256": "2e766fb01eb5e26461f228010764865f5fe2416f4edd500a8d12c86dfd4baa23", "sha256_in_prefix": "2e766fb01eb5e26461f228010764865f5fe2416f4edd500a8d12c86dfd4baa23", "size_in_bytes": 7630}, {"_path": "site-packages/opentelemetry/instrumentation/dependencies.py", "path_type": "hardlink", "sha256": "e0a0491f6bbf20728bbf2047c8910d26036309aa7337e8b7d9131bda26d327cf", "sha256_in_prefix": "e0a0491f6bbf20728bbf2047c8910d26036309aa7337e8b7d9131bda26d327cf", "size_in_bytes": 6608}, {"_path": "site-packages/opentelemetry/instrumentation/distro.py", "path_type": "hardlink", "sha256": "97bc2333d791e385fe064eb0f9bdff916dff4205bcd8e8884d14ce638f2c8599", "sha256_in_prefix": "97bc2333d791e385fe064eb0f9bdff916dff4205bcd8e8884d14ce638f2c8599", "size_in_bytes": 2168}, {"_path": "site-packages/opentelemetry/instrumentation/environment_variables.py", "path_type": "hardlink", "sha256": "879330af286e7edc995025bd5eb5177a00bd0877cb4afa5c07f9a62a73195437", "sha256_in_prefix": "879330af286e7edc995025bd5eb5177a00bd0877cb4afa5c07f9a62a73195437", "size_in_bytes": 1115}, {"_path": "site-packages/opentelemetry/instrumentation/instrumentor.py", "path_type": "hardlink", "sha256": "e6728de721991ce88da223016db07f40e7636b2c81031dcf3f50bc2a95164626", "sha256_in_prefix": "e6728de721991ce88da223016db07f40e7636b2c81031dcf3f50bc2a95164626", "size_in_bytes": 5022}, {"_path": "site-packages/opentelemetry/instrumentation/propagators.py", "path_type": "hardlink", "sha256": "841906ef42a53148938f13de8b23a191bfde13de83455cd1c98e1f108ccca83e", "sha256_in_prefix": "841906ef42a53148938f13de8b23a191bfde13de83455cd1c98e1f108ccca83e", "size_in_bytes": 4070}, {"_path": "site-packages/opentelemetry/instrumentation/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/instrumentation/sqlcommenter_utils.py", "path_type": "hardlink", "sha256": "a21f7bc035ec5ef53c19dc46469586c5c9de033e7f7704c16b553e395a74ccc5", "sha256_in_prefix": "a21f7bc035ec5ef53c19dc46469586c5c9de033e7f7704c16b553e395a74ccc5", "size_in_bytes": 1963}, {"_path": "site-packages/opentelemetry/instrumentation/utils.py", "path_type": "hardlink", "sha256": "e86c47fb300a6b4a23ed5da522ec1df29d1398c0364a20bce2457aff015f7fd6", "sha256_in_prefix": "e86c47fb300a6b4a23ed5da522ec1df29d1398c0364a20bce2457aff015f7fd6", "size_in_bytes": 7139}, {"_path": "site-packages/opentelemetry/instrumentation/version.py", "path_type": "hardlink", "sha256": "7df3fea041072f2c50a9ace13e0a7b0ae35b56da7fb236f5328dbfccd65bf905", "sha256_in_prefix": "7df3fea041072f2c50a9ace13e0a7b0ae35b56da7fb236f5328dbfccd65bf905", "size_in_bytes": 608}, {"_path": "site-packages/opentelemetry_instrumentation-0.58b0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/opentelemetry_instrumentation-0.58b0.dist-info/METADATA", "path_type": "hardlink", "sha256": "72f2315425ea51acb69b20788d53b5041f585acd6eb6833ca068f337a7550413", "sha256_in_prefix": "72f2315425ea51acb69b20788d53b5041f585acd6eb6833ca068f337a7550413", "size_in_bytes": 7090}, {"_path": "site-packages/opentelemetry_instrumentation-0.58b0.dist-info/RECORD", "path_type": "hardlink", "sha256": "c77f09c11db58a3fcd637e2556e7ddcd21c407dc45daecf3c83bffe8ed2cbbfa", "sha256_in_prefix": "c77f09c11db58a3fcd637e2556e7ddcd21c407dc45daecf3c83bffe8ed2cbbfa", "size_in_bytes": 3704}, {"_path": "site-packages/opentelemetry_instrumentation-0.58b0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry_instrumentation-0.58b0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "sha256_in_prefix": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "size_in_bytes": 87}, {"_path": "site-packages/opentelemetry_instrumentation-0.58b0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "e86c18d75ca00bbfd12c516575b1abd8dce8e04f259423e374cbcad70685b367", "sha256_in_prefix": "e86c18d75ca00bbfd12c516575b1abd8dce8e04f259423e374cbcad70685b367", "size_in_bytes": 125}, {"_path": "site-packages/opentelemetry_instrumentation-0.58b0.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "895bf7b794440743b9f2d15411041760bad309ad5554e1545dfadbbd493afda5", "sha256_in_prefix": "895bf7b794440743b9f2d15411041760bad309ad5554e1545dfadbbd493afda5", "size_in_bytes": 279}, {"_path": "site-packages/opentelemetry_instrumentation-0.58b0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "sha256_in_prefix": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "size_in_bytes": 11357}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/_semconv.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/auto_instrumentation/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/auto_instrumentation/__pycache__/_load.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/auto_instrumentation/__pycache__/sitecustomize.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/bootstrap.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/bootstrap_gen.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/dependencies.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/distro.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/environment_variables.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/instrumentor.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/propagators.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/sqlcommenter_utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/__pycache__/version.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "bin/opentelemetry-instrument", "path_type": "unix_python_entry_point"}, {"_path": "bin/opentelemetry-bootstrap", "path_type": "unix_python_entry_point"}], "paths_version": 1}, "requested_spec": "None", "sha256": "5feaee817cadb5757e78a5d509586a9e0fcf979a910c28562cffb11466869ecd", "size": 34174, "subdir": "noarch", "timestamp": 1757659136000, "url": "https://conda.anaconda.org/conda-forge/noarch/opentelemetry-instrumentation-0.58b0-pyhd8ed1ab_0.conda", "version": "0.58b0"}