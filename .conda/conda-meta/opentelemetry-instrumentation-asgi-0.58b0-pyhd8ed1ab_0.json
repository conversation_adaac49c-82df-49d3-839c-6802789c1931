{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["asgiref ~=3.0", "opentelemetry-api ~=1.12", "opentelemetry-instrumentation 0.58b0", "opentelemetry-semantic-conventions 0.58b0", "opentelemetry-util-http 0.58b0", "python >=3.10"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-instrumentation-asgi-0.58b0-pyhd8ed1ab_0", "files": ["lib/python3.11/site-packages/opentelemetry/instrumentation/asgi/__init__.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/asgi/package.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/asgi/types.py", "lib/python3.11/site-packages/opentelemetry/instrumentation/asgi/version.py", "lib/python3.11/site-packages/opentelemetry_instrumentation_asgi-0.58b0.dist-info/INSTALLER", "lib/python3.11/site-packages/opentelemetry_instrumentation_asgi-0.58b0.dist-info/METADATA", "lib/python3.11/site-packages/opentelemetry_instrumentation_asgi-0.58b0.dist-info/RECORD", "lib/python3.11/site-packages/opentelemetry_instrumentation_asgi-0.58b0.dist-info/REQUESTED", "lib/python3.11/site-packages/opentelemetry_instrumentation_asgi-0.58b0.dist-info/WHEEL", "lib/python3.11/site-packages/opentelemetry_instrumentation_asgi-0.58b0.dist-info/direct_url.json", "lib/python3.11/site-packages/opentelemetry_instrumentation_asgi-0.58b0.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/opentelemetry/instrumentation/asgi/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/asgi/__pycache__/package.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/asgi/__pycache__/types.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/instrumentation/asgi/__pycache__/version.cpython-311.pyc"], "fn": "opentelemetry-instrumentation-asgi-0.58b0-pyhd8ed1ab_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-instrumentation-asgi-0.58b0-pyhd8ed1ab_0", "type": 1}, "md5": "71fb8b88d71254299b192940bebe69a9", "name": "opentelemetry-instrumentation-asgi", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-instrumentation-asgi-0.58b0-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/opentelemetry/instrumentation/asgi/__init__.py", "path_type": "hardlink", "sha256": "6d2e3153ca01b999e156f1ad2d1f776494698fe64dba5ce2b25ddcfce767845a", "sha256_in_prefix": "6d2e3153ca01b999e156f1ad2d1f776494698fe64dba5ce2b25ddcfce767845a", "size_in_bytes": 38469}, {"_path": "site-packages/opentelemetry/instrumentation/asgi/package.py", "path_type": "hardlink", "sha256": "d1cac5d6ef53dd5b4b184da45f0d0fb3212bc61080f8748081a78047c4387920", "sha256_in_prefix": "d1cac5d6ef53dd5b4b184da45f0d0fb3212bc61080f8748081a78047c4387920", "size_in_bytes": 678}, {"_path": "site-packages/opentelemetry/instrumentation/asgi/types.py", "path_type": "hardlink", "sha256": "0097746e0c76a2fc5329a909673d366344ff8c334caddf9174b58cdbdd802eda", "sha256_in_prefix": "0097746e0c76a2fc5329a909673d366344ff8c334caddf9174b58cdbdd802eda", "size_in_bytes": 1258}, {"_path": "site-packages/opentelemetry/instrumentation/asgi/version.py", "path_type": "hardlink", "sha256": "7df3fea041072f2c50a9ace13e0a7b0ae35b56da7fb236f5328dbfccd65bf905", "sha256_in_prefix": "7df3fea041072f2c50a9ace13e0a7b0ae35b56da7fb236f5328dbfccd65bf905", "size_in_bytes": 608}, {"_path": "site-packages/opentelemetry_instrumentation_asgi-0.58b0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/opentelemetry_instrumentation_asgi-0.58b0.dist-info/METADATA", "path_type": "hardlink", "sha256": "8975a0e2835fabb4e7c2d28e17332cbdd32f756daa5fc39203cd6ce787fd028d", "sha256_in_prefix": "8975a0e2835fabb4e7c2d28e17332cbdd32f756daa5fc39203cd6ce787fd028d", "size_in_bytes": 2047}, {"_path": "site-packages/opentelemetry_instrumentation_asgi-0.58b0.dist-info/RECORD", "path_type": "hardlink", "sha256": "8c053e2259e7e06d3f6fcdae6514ddada6cb877ba363b94fc52e1f1320cee599", "sha256_in_prefix": "8c053e2259e7e06d3f6fcdae6514ddada6cb877ba363b94fc52e1f1320cee599", "size_in_bytes": 1481}, {"_path": "site-packages/opentelemetry_instrumentation_asgi-0.58b0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry_instrumentation_asgi-0.58b0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "sha256_in_prefix": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "size_in_bytes": 87}, {"_path": "site-packages/opentelemetry_instrumentation_asgi-0.58b0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "7b63e67707058acae09685938ba51424753fe1afa29083d91723943a29a5186e", "sha256_in_prefix": "7b63e67707058acae09685938ba51424753fe1afa29083d91723943a29a5186e", "size_in_bytes": 130}, {"_path": "site-packages/opentelemetry_instrumentation_asgi-0.58b0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "sha256_in_prefix": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "size_in_bytes": 11357}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/asgi/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/asgi/__pycache__/package.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/asgi/__pycache__/types.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/instrumentation/asgi/__pycache__/version.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "902f4be9d270fe2dd2d45d5098e9393f66d2f358bcc2262906c5df02067ac5bc", "size": 24850, "subdir": "noarch", "timestamp": 1757748284000, "url": "https://conda.anaconda.org/conda-forge/noarch/opentelemetry-instrumentation-asgi-0.58b0-pyhd8ed1ab_0.conda", "version": "0.58b0"}