{"build": "pyhd8ed1ab_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["python >=3.9"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/pure_eval-0.2.3-pyhd8ed1ab_1", "files": ["lib/python3.11/site-packages/pure_eval-0.2.3.dist-info/INSTALLER", "lib/python3.11/site-packages/pure_eval-0.2.3.dist-info/LICENSE.txt", "lib/python3.11/site-packages/pure_eval-0.2.3.dist-info/METADATA", "lib/python3.11/site-packages/pure_eval-0.2.3.dist-info/RECORD", "lib/python3.11/site-packages/pure_eval-0.2.3.dist-info/REQUESTED", "lib/python3.11/site-packages/pure_eval-0.2.3.dist-info/WHEEL", "lib/python3.11/site-packages/pure_eval-0.2.3.dist-info/direct_url.json", "lib/python3.11/site-packages/pure_eval-0.2.3.dist-info/top_level.txt", "lib/python3.11/site-packages/pure_eval/__init__.py", "lib/python3.11/site-packages/pure_eval/core.py", "lib/python3.11/site-packages/pure_eval/my_getattr_static.py", "lib/python3.11/site-packages/pure_eval/py.typed", "lib/python3.11/site-packages/pure_eval/utils.py", "lib/python3.11/site-packages/pure_eval/version.py", "lib/python3.11/site-packages/pure_eval/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/pure_eval/__pycache__/core.cpython-311.pyc", "lib/python3.11/site-packages/pure_eval/__pycache__/my_getattr_static.cpython-311.pyc", "lib/python3.11/site-packages/pure_eval/__pycache__/utils.cpython-311.pyc", "lib/python3.11/site-packages/pure_eval/__pycache__/version.cpython-311.pyc"], "fn": "pure_eval-0.2.3-pyhd8ed1ab_1.conda", "license": "MIT", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/pure_eval-0.2.3-pyhd8ed1ab_1", "type": 1}, "md5": "3bfdfb8dbcdc4af1ae3f9a8eb3948f04", "name": "pure_eval", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/pure_eval-0.2.3-pyhd8ed1ab_1.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/pure_eval-0.2.3.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/pure_eval-0.2.3.dist-info/LICENSE.txt", "path_type": "hardlink", "sha256": "a476a2cb0ef4c41450340a577a28b91ac4c7f669136b2ee148047fabd5fc4181", "sha256_in_prefix": "a476a2cb0ef4c41450340a577a28b91ac4c7f669136b2ee148047fabd5fc4181", "size_in_bytes": 1066}, {"_path": "site-packages/pure_eval-0.2.3.dist-info/METADATA", "path_type": "hardlink", "sha256": "6b93e91bf03b476f3e3dfde8a082fe7c1056abc0d7d62174ec924e7c156468c5", "sha256_in_prefix": "6b93e91bf03b476f3e3dfde8a082fe7c1056abc0d7d62174ec924e7c156468c5", "size_in_bytes": 6277}, {"_path": "site-packages/pure_eval-0.2.3.dist-info/RECORD", "path_type": "hardlink", "sha256": "3145aba02e746228fe4fc9f880aceac0099fffd622529e18858d6c93e47a1d1c", "sha256_in_prefix": "3145aba02e746228fe4fc9f880aceac0099fffd622529e18858d6c93e47a1d1c", "size_in_bytes": 1397}, {"_path": "site-packages/pure_eval-0.2.3.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/pure_eval-0.2.3.dist-info/WHEEL", "path_type": "hardlink", "sha256": "3d9504c5d7fbd548bfb28ebb417a724ae1ed0a2dfe277c2f1783912ba93f4bc5", "sha256_in_prefix": "3d9504c5d7fbd548bfb28ebb417a724ae1ed0a2dfe277c2f1783912ba93f4bc5", "size_in_bytes": 91}, {"_path": "site-packages/pure_eval-0.2.3.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "08389a12e33ca7a8fe11cea1df8ec8a3d8291abc57aa2307007213d7364d2512", "sha256_in_prefix": "08389a12e33ca7a8fe11cea1df8ec8a3d8291abc57aa2307007213d7364d2512", "size_in_bytes": 105}, {"_path": "site-packages/pure_eval-0.2.3.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "6a3db8bf3c37c1cdd7b0bdd0d2c1e27485fa37b4da562103af547f47841b823a", "sha256_in_prefix": "6a3db8bf3c37c1cdd7b0bdd0d2c1e27485fa37b4da562103af547f47841b823a", "size_in_bytes": 10}, {"_path": "site-packages/pure_eval/__init__.py", "path_type": "hardlink", "sha256": "0e91487e2c28901bb3e7e2fc3fe83f8fb86b91b85b160fcf47a17ded41600391", "sha256_in_prefix": "0e91487e2c28901bb3e7e2fc3fe83f8fb86b91b85b160fcf47a17ded41600391", "size_in_bytes": 434}, {"_path": "site-packages/pure_eval/core.py", "path_type": "hardlink", "sha256": "6f1b215a0855c026b07557eb35d5cba878f950ea5a3f3d5a0240744f7c61ff74", "sha256_in_prefix": "6f1b215a0855c026b07557eb35d5cba878f950ea5a3f3d5a0240744f7c61ff74", "size_in_bytes": 15309}, {"_path": "site-packages/pure_eval/my_getattr_static.py", "path_type": "hardlink", "sha256": "e85c0a21b83784b5e063ca2768afcbd65c9124a8cb54d1a082ce52cfaba158c7", "sha256_in_prefix": "e85c0a21b83784b5e063ca2768afcbd65c9124a8cb54d1a082ce52cfaba158c7", "size_in_bytes": 4161}, {"_path": "site-packages/pure_eval/py.typed", "path_type": "hardlink", "sha256": "c37c58268ef5cc0ee90e717ae71afabbc14bf20bc9ec674028c2522eebf620f5", "sha256_in_prefix": "c37c58268ef5cc0ee90e717ae71afabbc14bf20bc9ec674028c2522eebf620f5", "size_in_bytes": 68}, {"_path": "site-packages/pure_eval/utils.py", "path_type": "hardlink", "sha256": "b0f667d0f022476e3f6e28ec695b67bfb199293d9ac6ff3aac56f7706841ff96", "sha256_in_prefix": "b0f667d0f022476e3f6e28ec695b67bfb199293d9ac6ff3aac56f7706841ff96", "size_in_bytes": 4612}, {"_path": "site-packages/pure_eval/version.py", "path_type": "hardlink", "sha256": "ce86d4cd0f1d9a2eeffdfadb42d8c00b5a5a70d2556f003fddb31a78ba2d04c5", "sha256_in_prefix": "ce86d4cd0f1d9a2eeffdfadb42d8c00b5a5a70d2556f003fddb31a78ba2d04c5", "size_in_bytes": 21}, {"_path": "lib/python3.11/site-packages/pure_eval/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/pure_eval/__pycache__/core.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/pure_eval/__pycache__/my_getattr_static.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/pure_eval/__pycache__/utils.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/pure_eval/__pycache__/version.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "71bd24600d14bb171a6321d523486f6a06f855e75e547fa0cb2a0953b02047f0", "size": 16668, "subdir": "noarch", "timestamp": 1733569518000, "url": "https://conda.anaconda.org/conda-forge/noarch/pure_eval-0.2.3-pyhd8ed1ab_1.conda", "version": "0.2.3"}