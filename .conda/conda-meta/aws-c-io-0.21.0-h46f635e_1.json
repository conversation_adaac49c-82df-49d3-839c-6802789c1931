{"build": "h46f635e_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.15", "aws-c-common >=0.12.4,<0.12.5.0a0", "aws-c-cal >=0.9.2,<0.9.3.0a0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/aws-c-io-0.21.0-h46f635e_1", "files": ["include/aws/io/async_stream.h", "include/aws/io/channel.h", "include/aws/io/channel_bootstrap.h", "include/aws/io/event_loop.h", "include/aws/io/exports.h", "include/aws/io/file_utils.h", "include/aws/io/future.h", "include/aws/io/host_resolver.h", "include/aws/io/io.h", "include/aws/io/logging.h", "include/aws/io/message_pool.h", "include/aws/io/pem.h", "include/aws/io/pipe.h", "include/aws/io/pkcs11.h", "include/aws/io/retry_strategy.h", "include/aws/io/shared_library.h", "include/aws/io/socket.h", "include/aws/io/socket_channel_handler.h", "include/aws/io/statistics.h", "include/aws/io/stream.h", "include/aws/io/tls_channel_handler.h", "include/aws/io/uri.h", "include/aws/testing/async_stream_tester.h", "include/aws/testing/io_testing_channel.h", "include/aws/testing/stream_tester.h", "lib/cmake/aws-c-io/aws-c-io-config.cmake", "lib/cmake/aws-c-io/shared/aws-c-io-targets-release.cmake", "lib/cmake/aws-c-io/shared/aws-c-io-targets.cmake", "lib/libaws-c-io.1.0.0.dylib", "lib/libaws-c-io.dylib"], "fn": "aws-c-io-0.21.0-h46f635e_1.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/aws-c-io-0.21.0-h46f635e_1", "type": 1}, "md5": "1e8e30da68cdcf5c38e3975b0535656d", "name": "aws-c-io", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/aws-c-io-0.21.0-h46f635e_1.conda", "paths_data": {"paths": [{"_path": "include/aws/io/async_stream.h", "path_type": "hardlink", "sha256": "3fd1581c07f747abf9123767c64677bee2935aee6e9d58bd5515a4fd2568a491", "sha256_in_prefix": "3fd1581c07f747abf9123767c64677bee2935aee6e9d58bd5515a4fd2568a491", "size_in_bytes": 3993}, {"_path": "include/aws/io/channel.h", "path_type": "hardlink", "sha256": "170dcf21aa2381bfb6e12905463f207bebf30247ccb679277d7cabf8b6ede7b1", "sha256_in_prefix": "170dcf21aa2381bfb6e12905463f207bebf30247ccb679277d7cabf8b6ede7b1", "size_in_bytes": 20171}, {"_path": "include/aws/io/channel_bootstrap.h", "path_type": "hardlink", "sha256": "2524d207a41f4573b476d55f9170b6bf140e61547686d4cf717947e9937b0bc6", "sha256_in_prefix": "2524d207a41f4573b476d55f9170b6bf140e61547686d4cf717947e9937b0bc6", "size_in_bytes": 14386}, {"_path": "include/aws/io/event_loop.h", "path_type": "hardlink", "sha256": "1f0725dc1f2d03e9f9cb6de2ec2a7b9027d991e0cb3c4c2bc4067d4877dfaebb", "sha256_in_prefix": "1f0725dc1f2d03e9f9cb6de2ec2a7b9027d991e0cb3c4c2bc4067d4877dfaebb", "size_in_bytes": 10126}, {"_path": "include/aws/io/exports.h", "path_type": "hardlink", "sha256": "6e48ef70044ef298fe16622384d4aea4de21e516b0385563f944aa1b1a225e98", "sha256_in_prefix": "6e48ef70044ef298fe16622384d4aea4de21e516b0385563f944aa1b1a225e98", "size_in_bytes": 827}, {"_path": "include/aws/io/file_utils.h", "path_type": "hardlink", "sha256": "0e6f709437f216c35584e55c9eef63b29e09404e81bb65bf7ca405cbf99011de", "sha256_in_prefix": "0e6f709437f216c35584e55c9eef63b29e09404e81bb65bf7ca405cbf99011de", "size_in_bytes": 315}, {"_path": "include/aws/io/future.h", "path_type": "hardlink", "sha256": "046e2a9ae551f5f8adcc4c50365dbd917d6e80a21a1ea1c7b29b4843f8088aa3", "sha256_in_prefix": "046e2a9ae551f5f8adcc4c50365dbd917d6e80a21a1ea1c7b29b4843f8088aa3", "size_in_bytes": 42256}, {"_path": "include/aws/io/host_resolver.h", "path_type": "hardlink", "sha256": "d19e8994ee9b1660ed6795e15c2d20572d12a8932cd3f9d7c941d6ed34ccf089", "sha256_in_prefix": "d19e8994ee9b1660ed6795e15c2d20572d12a8932cd3f9d7c941d6ed34ccf089", "size_in_bytes": 10410}, {"_path": "include/aws/io/io.h", "path_type": "hardlink", "sha256": "c0c868877de59789305edfe5f7797a99d3e7ceab1f9e046b48d8142c51f0f6cc", "sha256_in_prefix": "c0c868877de59789305edfe5f7797a99d3e7ceab1f9e046b48d8142c51f0f6cc", "size_in_bytes": 10444}, {"_path": "include/aws/io/logging.h", "path_type": "hardlink", "sha256": "7588cb40f56f1c7a8930bcf06020e7961c5d68a99d4d9f6e09e6a8cc96b1c819", "sha256_in_prefix": "7588cb40f56f1c7a8930bcf06020e7961c5d68a99d4d9f6e09e6a8cc96b1c819", "size_in_bytes": 956}, {"_path": "include/aws/io/message_pool.h", "path_type": "hardlink", "sha256": "35a967926d74e260ae24a825dfe4c16806dde40c344b62fe5f20b75b8c0bd8d1", "sha256_in_prefix": "35a967926d74e260ae24a825dfe4c16806dde40c344b62fe5f20b75b8c0bd8d1", "size_in_bytes": 2457}, {"_path": "include/aws/io/pem.h", "path_type": "hardlink", "sha256": "d97eec9654288414c736dd9f12caf61468110ae4f0a8b7f8924fd310d450cd7e", "sha256_in_prefix": "d97eec9654288414c736dd9f12caf61468110ae4f0a8b7f8924fd310d450cd7e", "size_in_bytes": 4219}, {"_path": "include/aws/io/pipe.h", "path_type": "hardlink", "sha256": "0a1ae4c33684e212c39471cbcab562991373bc64246f6992ae32a8a506558964", "sha256_in_prefix": "0a1ae4c33684e212c39471cbcab562991373bc64246f6992ae32a8a506558964", "size_in_bytes": 5722}, {"_path": "include/aws/io/pkcs11.h", "path_type": "hardlink", "sha256": "236c0d2932f206058c5dd2d144418eb506e9fa90a7af86f2135db37967557e04", "sha256_in_prefix": "236c0d2932f206058c5dd2d144418eb506e9fa90a7af86f2135db37967557e04", "size_in_bytes": 3002}, {"_path": "include/aws/io/retry_strategy.h", "path_type": "hardlink", "sha256": "34086fca74f3a619e2d41a0a5a0347ce56b3173d8f0cea7164611512b1f77492", "sha256_in_prefix": "34086fca74f3a619e2d41a0a5a0347ce56b3173d8f0cea7164611512b1f77492", "size_in_bytes": 11349}, {"_path": "include/aws/io/shared_library.h", "path_type": "hardlink", "sha256": "27c9494733e75da4ca45087899a95db893ce33b1cc837b87aa9ab63e212772de", "sha256_in_prefix": "27c9494733e75da4ca45087899a95db893ce33b1cc837b87aa9ab63e212772de", "size_in_bytes": 1202}, {"_path": "include/aws/io/socket.h", "path_type": "hardlink", "sha256": "542e973474d685107c3d2fb191caced6152ee27ef87e373327370c2fa2d7d117", "sha256_in_prefix": "542e973474d685107c3d2fb191caced6152ee27ef87e373327370c2fa2d7d117", "size_in_bytes": 18510}, {"_path": "include/aws/io/socket_channel_handler.h", "path_type": "hardlink", "sha256": "21bf5c0ee4fd63730ebf25a6d785a44be9f05c27d5adc6db38611ffc6c2b755d", "sha256_in_prefix": "21bf5c0ee4fd63730ebf25a6d785a44be9f05c27d5adc6db38611ffc6c2b755d", "size_in_bytes": 1115}, {"_path": "include/aws/io/statistics.h", "path_type": "hardlink", "sha256": "6ff0428f11e957728f55a9e19cda01864e85bdcf4ded3ecdbf6706758a53b48a", "sha256_in_prefix": "6ff0428f11e957728f55a9e19cda01864e85bdcf4ded3ecdbf6706758a53b48a", "size_in_bytes": 1983}, {"_path": "include/aws/io/stream.h", "path_type": "hardlink", "sha256": "07e0dd2257b22a96905678be2defa7d5095281d061c2ca1a7470659ee1ba2308", "sha256_in_prefix": "07e0dd2257b22a96905678be2defa7d5095281d061c2ca1a7470659ee1ba2308", "size_in_bytes": 4670}, {"_path": "include/aws/io/tls_channel_handler.h", "path_type": "hardlink", "sha256": "d3e1aa365ad67a928cf3a516bdb656dea964e860d49db47ca487a388531251b0", "sha256_in_prefix": "d3e1aa365ad67a928cf3a516bdb656dea964e860d49db47ca487a388531251b0", "size_in_bytes": 35827}, {"_path": "include/aws/io/uri.h", "path_type": "hardlink", "sha256": "55cac9d5078e9bf2f08fa2a6a420993954ec456f0e8d2db0b1d6f2f135b89d48", "sha256_in_prefix": "55cac9d5078e9bf2f08fa2a6a420993954ec456f0e8d2db0b1d6f2f135b89d48", "size_in_bytes": 239}, {"_path": "include/aws/testing/async_stream_tester.h", "path_type": "hardlink", "sha256": "a977816ceeca13a40b1b259805a97ab1854b3ea094ba1f9763a546a00661dbcf", "sha256_in_prefix": "a977816ceeca13a40b1b259805a97ab1854b3ea094ba1f9763a546a00661dbcf", "size_in_bytes": 10154}, {"_path": "include/aws/testing/io_testing_channel.h", "path_type": "hardlink", "sha256": "5794c2f8011a017f8971f69357e8ca53240dd0723783893e62611ebe3f0c9048", "sha256_in_prefix": "5794c2f8011a017f8971f69357e8ca53240dd0723783893e62611ebe3f0c9048", "size_in_bytes": 27217}, {"_path": "include/aws/testing/stream_tester.h", "path_type": "hardlink", "sha256": "cb4430d0c6e0459dac9f53a3ec7643694daac8be62c0789221f5367e24849bfd", "sha256_in_prefix": "cb4430d0c6e0459dac9f53a3ec7643694daac8be62c0789221f5367e24849bfd", "size_in_bytes": 8873}, {"_path": "lib/cmake/aws-c-io/aws-c-io-config.cmake", "path_type": "hardlink", "sha256": "1e293358c8918a46dcede7b688058324dba9e58c117b7a5a0f0438c7a165d0d4", "sha256_in_prefix": "1e293358c8918a46dcede7b688058324dba9e58c117b7a5a0f0438c7a165d0d4", "size_in_bytes": 671}, {"_path": "lib/cmake/aws-c-io/shared/aws-c-io-targets-release.cmake", "path_type": "hardlink", "sha256": "07431d94b6f67ce8aac13a31f6c8e8244c74db8b21dd52f657647b3390b4711d", "sha256_in_prefix": "07431d94b6f67ce8aac13a31f6c8e8244c74db8b21dd52f657647b3390b4711d", "size_in_bytes": 871}, {"_path": "lib/cmake/aws-c-io/shared/aws-c-io-targets.cmake", "path_type": "hardlink", "sha256": "abff636ede6af54cfb15b0e9dc2f89e9f40b6be0fd3eec3b21b4d1686f8294be", "sha256_in_prefix": "abff636ede6af54cfb15b0e9dc2f89e9f40b6be0fd3eec3b21b4d1686f8294be", "size_in_bytes": 4348}, {"_path": "lib/libaws-c-io.1.0.0.dylib", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/bld/rattler-build_aws-c-io_1752246166/host_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_pla", "sha256": "733cdbbc40f4894f72a9bd09fa98c406dd58f84eab58c1a1d9eec1c208e5407a", "sha256_in_prefix": "974738f9f7a9d9d03c3ff66674dfd3fe91c5ea93b1f5dc1d176c05bc573ade83", "size_in_bytes": 357504}, {"_path": "lib/libaws-c-io.dylib", "path_type": "softlink", "sha256": "733cdbbc40f4894f72a9bd09fa98c406dd58f84eab58c1a1d9eec1c208e5407a", "size_in_bytes": 23}], "paths_version": 1}, "requested_spec": "None", "sha256": "2d78068eed46304e6b27f261273da18f9a4a48501c5e035ea56a3d7fef5a49b2", "size": 181279, "subdir": "osx-64", "timestamp": 1752246166000, "url": "https://conda.anaconda.org/conda-forge/osx-64/aws-c-io-0.21.0-h46f635e_1.conda", "version": "0.21.0"}