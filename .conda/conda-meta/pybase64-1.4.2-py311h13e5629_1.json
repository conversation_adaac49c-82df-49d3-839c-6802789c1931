{"build": "py311h13e5629_1", "build_number": 1, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13", "python >=3.11,<3.12.0a0", "python_abi 3.11.* *_cp311"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/pybase64-1.4.2-py311h13e5629_1", "files": ["bin/pybase64", "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/INSTALLER", "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/METADATA", "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/RECORD", "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/REQUESTED", "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/WHEEL", "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/direct_url.json", "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/entry_points.txt", "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/top_level.txt", "lib/python3.11/site-packages/pybase64/__init__.py", "lib/python3.11/site-packages/pybase64/__main__.py", "lib/python3.11/site-packages/pybase64/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/pybase64/__pycache__/__main__.cpython-311.pyc", "lib/python3.11/site-packages/pybase64/__pycache__/_fallback.cpython-311.pyc", "lib/python3.11/site-packages/pybase64/__pycache__/_license.cpython-311.pyc", "lib/python3.11/site-packages/pybase64/__pycache__/_typing.cpython-311.pyc", "lib/python3.11/site-packages/pybase64/__pycache__/_version.cpython-311.pyc", "lib/python3.11/site-packages/pybase64/_fallback.py", "lib/python3.11/site-packages/pybase64/_license.py", "lib/python3.11/site-packages/pybase64/_license.pyi", "lib/python3.11/site-packages/pybase64/_pybase64.cpython-311-darwin.so", "lib/python3.11/site-packages/pybase64/_pybase64.pyi", "lib/python3.11/site-packages/pybase64/_typing.py", "lib/python3.11/site-packages/pybase64/_version.py", "lib/python3.11/site-packages/pybase64/py.typed"], "fn": "pybase64-1.4.2-py311h13e5629_1.conda", "license": "BSD-2-<PERSON><PERSON>", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/pybase64-1.4.2-py311h13e5629_1", "type": 1}, "md5": "ee5b2ef160f7cf7e07475e38769d8ca3", "name": "pybase64", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/pybase64-1.4.2-py311h13e5629_1.conda", "paths_data": {"paths": [{"_path": "bin/pybase64", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/pybase64_1756309040335/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_", "sha256": "119bb7764d964804a18e804367b9f1af3f9173a96f8ccaef2dc90eb226a81904", "sha256_in_prefix": "62f468af3c9d5c6ac0287b5f1e2011b188474ccb19bda9268a221e9b6f6aceb9", "size_in_bytes": 470}, {"_path": "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/METADATA", "path_type": "hardlink", "sha256": "249a8b75c7c0d35b82ae7c1a81fe099f8e1afd678972cf5f4d96a82ff2235dad", "sha256_in_prefix": "249a8b75c7c0d35b82ae7c1a81fe099f8e1afd678972cf5f4d96a82ff2235dad", "size_in_bytes": 8663}, {"_path": "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/RECORD", "path_type": "hardlink", "sha256": "fa8dcb7a14919409cf403eab79114233888abecfc4f1106073531a3b1cf40fa2", "sha256_in_prefix": "fa8dcb7a14919409cf403eab79114233888abecfc4f1106073531a3b1cf40fa2", "size_in_bytes": 1940}, {"_path": "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/WHEEL", "path_type": "hardlink", "sha256": "9a99a83f4370ced80864e3ca8f3c6d5e12203e9375332d371b67792389f5359e", "sha256_in_prefix": "9a99a83f4370ced80864e3ca8f3c6d5e12203e9375332d371b67792389f5359e", "size_in_bytes": 111}, {"_path": "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "f0005eb7e06ebdd925a06f2a3e657e1ca3ff490a8e8eb7e9668b679002f199c6", "sha256_in_prefix": "f0005eb7e06ebdd925a06f2a3e657e1ca3ff490a8e8eb7e9668b679002f199c6", "size_in_bytes": 96}, {"_path": "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/entry_points.txt", "path_type": "hardlink", "sha256": "a8b84b7ae3d19e3ea3bbbe92a9e5583aec5abe60cac7d6568d71cb96da4b6b9a", "sha256_in_prefix": "a8b84b7ae3d19e3ea3bbbe92a9e5583aec5abe60cac7d6568d71cb96da4b6b9a", "size_in_bytes": 52}, {"_path": "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "5e523b54c1581d44f458bfdd2cd3944d622bb1a32aa41f0773ee4da390fb45b4", "sha256_in_prefix": "5e523b54c1581d44f458bfdd2cd3944d622bb1a32aa41f0773ee4da390fb45b4", "size_in_bytes": 1326}, {"_path": "lib/python3.11/site-packages/pybase64-1.4.2.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "4e6ded986a4c9cb580be3af13914b613923a2c045a2dea4e1148c6693985b998", "sha256_in_prefix": "4e6ded986a4c9cb580be3af13914b613923a2c045a2dea4e1148c6693985b998", "size_in_bytes": 9}, {"_path": "lib/python3.11/site-packages/pybase64/__init__.py", "path_type": "hardlink", "sha256": "c1bfd81b4f66d3eac2ea53e60ca17ad97b04715846902c5c3f0d86672c44f4be", "sha256_in_prefix": "c1bfd81b4f66d3eac2ea53e60ca17ad97b04715846902c5c3f0d86672c44f4be", "size_in_bytes": 3230}, {"_path": "lib/python3.11/site-packages/pybase64/__main__.py", "path_type": "hardlink", "sha256": "76366a43a40d9a02008ef60741afae200a59871c94663d94cef0de4237cb9f4c", "sha256_in_prefix": "76366a43a40d9a02008ef60741afae200a59871c94663d94cef0de4237cb9f4c", "size_in_bytes": 8530}, {"_path": "lib/python3.11/site-packages/pybase64/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "d80c0b1d1f4e9b1c38a495df8e46eeb9a1b0324668fc80d6420ef0b9924f6da3", "sha256_in_prefix": "d80c0b1d1f4e9b1c38a495df8e46eeb9a1b0324668fc80d6420ef0b9924f6da3", "size_in_bytes": 4151}, {"_path": "lib/python3.11/site-packages/pybase64/__pycache__/__main__.cpython-311.pyc", "path_type": "hardlink", "sha256": "c85e547e7916d2a0689332f95272f26b72f18f3f3a5c32e03a17c7ba49b7a508", "sha256_in_prefix": "c85e547e7916d2a0689332f95272f26b72f18f3f3a5c32e03a17c7ba49b7a508", "size_in_bytes": 12944}, {"_path": "lib/python3.11/site-packages/pybase64/__pycache__/_fallback.cpython-311.pyc", "path_type": "hardlink", "sha256": "886b0536a85309de11effc1afc79b7bbac27910e835e3c8de4fa78cb31139533", "sha256_in_prefix": "886b0536a85309de11effc1afc79b7bbac27910e835e3c8de4fa78cb31139533", "size_in_bytes": 7469}, {"_path": "lib/python3.11/site-packages/pybase64/__pycache__/_license.cpython-311.pyc", "path_type": "hardlink", "sha256": "dbf5db484d28a13b53c878114f6d146c98a1b0983b5f76ca030fba356e114b69", "sha256_in_prefix": "dbf5db484d28a13b53c878114f6d146c98a1b0983b5f76ca030fba356e114b69", "size_in_bytes": 3262}, {"_path": "lib/python3.11/site-packages/pybase64/__pycache__/_typing.cpython-311.pyc", "path_type": "hardlink", "sha256": "27df3f68878d71de1b10364d77c9d13bf574febcb890839085898db000398982", "sha256_in_prefix": "27df3f68878d71de1b10364d77c9d13bf574febcb890839085898db000398982", "size_in_bytes": 2008}, {"_path": "lib/python3.11/site-packages/pybase64/__pycache__/_version.cpython-311.pyc", "path_type": "hardlink", "sha256": "69006e9240fb63be1e9b4d62100cf21505d20bd0bec12c0a9a04a18a3b3f9150", "sha256_in_prefix": "69006e9240fb63be1e9b4d62100cf21505d20bd0bec12c0a9a04a18a3b3f9150", "size_in_bytes": 242}, {"_path": "lib/python3.11/site-packages/pybase64/_fallback.py", "path_type": "hardlink", "sha256": "2fe8b92a746d2d4d8f7c16ca20bda13621e636b5acd4b796621df1183f26ae23", "sha256_in_prefix": "2fe8b92a746d2d4d8f7c16ca20bda13621e636b5acd4b796621df1183f26ae23", "size_in_bytes": 5800}, {"_path": "lib/python3.11/site-packages/pybase64/_license.py", "path_type": "hardlink", "sha256": "2eedefd4eddbb8470c3e0282e5fe2be76de51af3468284d78479c787462cd64c", "sha256_in_prefix": "2eedefd4eddbb8470c3e0282e5fe2be76de51af3468284d78479c787462cd64c", "size_in_bytes": 3116}, {"_path": "lib/python3.11/site-packages/pybase64/_license.pyi", "path_type": "hardlink", "sha256": "693a7066a50987cd8db320701e1428fdc25166c0584804422b119db5bd5060f9", "sha256_in_prefix": "693a7066a50987cd8db320701e1428fdc25166c0584804422b119db5bd5060f9", "size_in_bytes": 14}, {"_path": "lib/python3.11/site-packages/pybase64/_pybase64.cpython-311-darwin.so", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/pybase64_1756309040335/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_", "sha256": "bd8239cd39e4314297cc74c472f79faeb95aac33559f866081745a7b921a3384", "sha256_in_prefix": "8b1ca7dde58c5c35bb03bc460326ccc860f8217a26cb99fbe733ee33d7f1e653", "size_in_bytes": 92056}, {"_path": "lib/python3.11/site-packages/pybase64/_pybase64.pyi", "path_type": "hardlink", "sha256": "c3193a7a85a669fc84f4d395af68fd4f4136c369520be22620be44f873f55dc1", "sha256_in_prefix": "c3193a7a85a669fc84f4d395af68fd4f4136c369520be22620be44f873f55dc1", "size_in_bytes": 679}, {"_path": "lib/python3.11/site-packages/pybase64/_typing.py", "path_type": "hardlink", "sha256": "42fcb1152ce4b6b000393da88650796a1d560dba8be9b9d90d1be07a60d96359", "sha256_in_prefix": "42fcb1152ce4b6b000393da88650796a1d560dba8be9b9d90d1be07a60d96359", "size_in_bytes": 709}, {"_path": "lib/python3.11/site-packages/pybase64/_version.py", "path_type": "hardlink", "sha256": "fd5485c497d7c0a5bd8ac5ac41811e285e7daf2b6f9ff2ceef2276bae57e2cbd", "sha256_in_prefix": "fd5485c497d7c0a5bd8ac5ac41811e285e7daf2b6f9ff2ceef2276bae57e2cbd", "size_in_bytes": 55}, {"_path": "lib/python3.11/site-packages/pybase64/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}], "paths_version": 1}, "requested_spec": "None", "sha256": "c0976e9255ad59fffe3fd7921d5750dabd7c1c8387a38639320bd0575eeeeaff", "size": 48720, "subdir": "osx-64", "timestamp": 1756309235000, "url": "https://conda.anaconda.org/conda-forge/osx-64/pybase64-1.4.2-py311h13e5629_1.conda", "version": "1.4.2"}