{"build": "hde974f2_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["libcxx >=18", "__osx >=10.14", "openssl >=3.5.1,<4.0a0", "zstd >=1.5.7,<1.6.0a0", "snappy >=1.2.1,<1.3.0a0", "libcurl >=8.14.1,<9.0a0", "libzlib >=1.3.1,<2.0a0", "libprotobuf >=5.29.3,<5.29.4.0a0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libpulsar-3.7.2-hde974f2_0", "files": ["include/pulsar/Authentication.h", "include/pulsar/BatchReceivePolicy.h", "include/pulsar/BrokerConsumerStats.h", "include/pulsar/Client.h", "include/pulsar/ClientConfiguration.h", "include/pulsar/CompressionType.h", "include/pulsar/ConsoleLoggerFactory.h", "include/pulsar/Consumer.h", "include/pulsar/ConsumerConfiguration.h", "include/pulsar/ConsumerCryptoFailureAction.h", "include/pulsar/ConsumerEventListener.h", "include/pulsar/ConsumerInterceptor.h", "include/pulsar/ConsumerType.h", "include/pulsar/CryptoKeyReader.h", "include/pulsar/DeadLetterPolicy.h", "include/pulsar/DeadLetterPolicyBuilder.h", "include/pulsar/DeprecatedException.h", "include/pulsar/EncryptionKeyInfo.h", "include/pulsar/FileLoggerFactory.h", "include/pulsar/InitialPosition.h", "include/pulsar/KeySharedPolicy.h", "include/pulsar/KeyValue.h", "include/pulsar/Logger.h", "include/pulsar/Message.h", "include/pulsar/MessageBatch.h", "include/pulsar/MessageBuilder.h", "include/pulsar/MessageId.h", "include/pulsar/MessageIdBuilder.h", "include/pulsar/MessageRoutingPolicy.h", "include/pulsar/Producer.h", "include/pulsar/ProducerConfiguration.h", "include/pulsar/ProducerCryptoFailureAction.h", "include/pulsar/ProducerInterceptor.h", "include/pulsar/ProtobufNativeSchema.h", "include/pulsar/Reader.h", "include/pulsar/ReaderConfiguration.h", "include/pulsar/RegexSubscriptionMode.h", "include/pulsar/Result.h", "include/pulsar/Schema.h", "include/pulsar/TableView.h", "include/pulsar/TableViewConfiguration.h", "include/pulsar/TopicMetadata.h", "include/pulsar/TypedMessage.h", "include/pulsar/TypedMessageBuilder.h", "include/pulsar/Version.h", "include/pulsar/c/authentication.h", "include/pulsar/c/client.h", "include/pulsar/c/client_configuration.h", "include/pulsar/c/consumer.h", "include/pulsar/c/consumer_configuration.h", "include/pulsar/c/message.h", "include/pulsar/c/message_id.h", "include/pulsar/c/message_router.h", "include/pulsar/c/messages.h", "include/pulsar/c/producer.h", "include/pulsar/c/producer_configuration.h", "include/pulsar/c/reader.h", "include/pulsar/c/reader_configuration.h", "include/pulsar/c/result.h", "include/pulsar/c/string_list.h", "include/pulsar/c/string_map.h", "include/pulsar/c/table_view.h", "include/pulsar/c/table_view_configuration.h", "include/pulsar/c/version.h", "include/pulsar/defines.h", "lib/libpulsar.dylib"], "fn": "libpulsar-3.7.2-hde974f2_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libpulsar-3.7.2-hde974f2_0", "type": 1}, "md5": "f1120f059fad3fcbc72a392f67bfb42e", "name": "libpulsar", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libpulsar-3.7.2-hde974f2_0.conda", "paths_data": {"paths": [{"_path": "include/pulsar/Authentication.h", "path_type": "hardlink", "sha256": "d7d37e3d8f6e5ccc4ed1e9f80e13f83d9f05f87ed6e46cc86fed783f64fff4b5", "sha256_in_prefix": "d7d37e3d8f6e5ccc4ed1e9f80e13f83d9f05f87ed6e46cc86fed783f64fff4b5", "size_in_bytes": 17134}, {"_path": "include/pulsar/BatchReceivePolicy.h", "path_type": "hardlink", "sha256": "50faf67cf3353550b4ebc1ac3021926b8beea919b30e0f903fedaf251000d366", "sha256_in_prefix": "50faf67cf3353550b4ebc1ac3021926b8beea919b30e0f903fedaf251000d366", "size_in_bytes": 3035}, {"_path": "include/pulsar/BrokerConsumerStats.h", "path_type": "hardlink", "sha256": "8154876c1080b9313db4d4e7caf0d4c2be7408169b9551fde56e979841b7cb60", "sha256_in_prefix": "8154876c1080b9313db4d4e7caf0d4c2be7408169b9551fde56e979841b7cb60", "size_in_bytes": 3413}, {"_path": "include/pulsar/Client.h", "path_type": "hardlink", "sha256": "956922b81cc9cda2e8d16b1292f99376c862a28d2254747aef205b09a515cffb", "sha256_in_prefix": "956922b81cc9cda2e8d16b1292f99376c862a28d2254747aef205b09a515cffb", "size_in_bytes": 18310}, {"_path": "include/pulsar/ClientConfiguration.h", "path_type": "hardlink", "sha256": "324786a9cba87840942f14f8ff15729f15ab435b47693599069735e8113f1d8a", "sha256_in_prefix": "324786a9cba87840942f14f8ff15729f15ab435b47693599069735e8113f1d8a", "size_in_bytes": 13780}, {"_path": "include/pulsar/CompressionType.h", "path_type": "hardlink", "sha256": "0aed403678aab75e507883b284340d458858238443cee54a3c20e87897904484", "sha256_in_prefix": "0aed403678aab75e507883b284340d458858238443cee54a3c20e87897904484", "size_in_bytes": 1090}, {"_path": "include/pulsar/ConsoleLoggerFactory.h", "path_type": "hardlink", "sha256": "54791077532e06afeff9374c6b66519a5ab6b054e83e513fcd9a1d0b5c14f8c9", "sha256_in_prefix": "54791077532e06afeff9374c6b66519a5ab6b054e83e513fcd9a1d0b5c14f8c9", "size_in_bytes": 1911}, {"_path": "include/pulsar/Consumer.h", "path_type": "hardlink", "sha256": "1ded58ca245df964bc5dfccc026f0a04d37aafc3ca2cf87fedee7e7d6d5fa058", "sha256_in_prefix": "1ded58ca245df964bc5dfccc026f0a04d37aafc3ca2cf87fedee7e7d6d5fa058", "size_in_bytes": 17885}, {"_path": "include/pulsar/ConsumerConfiguration.h", "path_type": "hardlink", "sha256": "1caa8cb410ff5be79b03414e850c9bf9d69eb4522db890230d7b800b5da25d3e", "sha256_in_prefix": "1caa8cb410ff5be79b03414e850c9bf9d69eb4522db890230d7b800b5da25d3e", "size_in_bytes": 24958}, {"_path": "include/pulsar/ConsumerCryptoFailureAction.h", "path_type": "hardlink", "sha256": "7714f501bd7c0acf271ae5063206d5517b0ee6cc2cbdef15bf3d18a0502764e7", "sha256_in_prefix": "7714f501bd7c0acf271ae5063206d5517b0ee6cc2cbdef15bf3d18a0502764e7", "size_in_bytes": 1532}, {"_path": "include/pulsar/ConsumerEventListener.h", "path_type": "hardlink", "sha256": "d6aa8bab58891b0b3ead5986c952dad6a9af3a85e38670cecb59951713dcd3c4", "sha256_in_prefix": "d6aa8bab58891b0b3ead5986c952dad6a9af3a85e38670cecb59951713dcd3c4", "size_in_bytes": 1817}, {"_path": "include/pulsar/ConsumerInterceptor.h", "path_type": "hardlink", "sha256": "a68d5be4afaf604ff20ba7a4bcda18aeb2fc971bd44a94ddda4b89d8bb01a547", "sha256_in_prefix": "a68d5be4afaf604ff20ba7a4bcda18aeb2fc971bd44a94ddda4b89d8bb01a547", "size_in_bytes": 4871}, {"_path": "include/pulsar/ConsumerType.h", "path_type": "hardlink", "sha256": "45bf7f298e47f084f05b58ff909ab8ca717ad2cece93d854b208e9dab2b77688", "sha256_in_prefix": "45bf7f298e47f084f05b58ff909ab8ca717ad2cece93d854b208e9dab2b77688", "size_in_bytes": 1705}, {"_path": "include/pulsar/CryptoKeyReader.h", "path_type": "hardlink", "sha256": "102fd11a36a731f1da8763add5be6c0ba7e218e28b707ab146b910f798f7cda8", "sha256_in_prefix": "102fd11a36a731f1da8763add5be6c0ba7e218e28b707ab146b910f798f7cda8", "size_in_bytes": 4826}, {"_path": "include/pulsar/DeadLetterPolicy.h", "path_type": "hardlink", "sha256": "705e0b2ceccccbe02f6e89711edf044542ef8edf8e279d0df9387a1d769c02d1", "sha256_in_prefix": "705e0b2ceccccbe02f6e89711edf044542ef8edf8e279d0df9387a1d769c02d1", "size_in_bytes": 1834}, {"_path": "include/pulsar/DeadLetterPolicyBuilder.h", "path_type": "hardlink", "sha256": "7d6794c3c182a11e11c12e04f212ee921752dd45e2982e70e1a01822c66d1778", "sha256_in_prefix": "7d6794c3c182a11e11c12e04f212ee921752dd45e2982e70e1a01822c66d1778", "size_in_bytes": 2973}, {"_path": "include/pulsar/DeprecatedException.h", "path_type": "hardlink", "sha256": "e2712b3a7b09a5389fd29f97d7dbd073b3be7e168ff7804cc5a4ded68a8297a9", "sha256_in_prefix": "e2712b3a7b09a5389fd29f97d7dbd073b3be7e168ff7804cc5a4ded68a8297a9", "size_in_bytes": 1229}, {"_path": "include/pulsar/EncryptionKeyInfo.h", "path_type": "hardlink", "sha256": "487004eaa0d5c4d350a23f07c07c8e606b4edc9770d1e600b6c93fc19a36917c", "sha256_in_prefix": "487004eaa0d5c4d350a23f07c07c8e606b4edc9770d1e600b6c93fc19a36917c", "size_in_bytes": 2356}, {"_path": "include/pulsar/FileLoggerFactory.h", "path_type": "hardlink", "sha256": "383bdd32fd9622990996e88bb81efedc4af3971456cb4fc6b5a69eebec922f7d", "sha256_in_prefix": "383bdd32fd9622990996e88bb81efedc4af3971456cb4fc6b5a69eebec922f7d", "size_in_bytes": 1927}, {"_path": "include/pulsar/InitialPosition.h", "path_type": "hardlink", "sha256": "9a06e6ae6771e77d432410264f78fc316ff458046daf47d0363954536fc4053f", "sha256_in_prefix": "9a06e6ae6771e77d432410264f78fc316ff458046daf47d0363954536fc4053f", "size_in_bytes": 1030}, {"_path": "include/pulsar/KeySharedPolicy.h", "path_type": "hardlink", "sha256": "22b20a576823117c242b8fd4552d4466850ab828348e41bfe01ece066575051e", "sha256_in_prefix": "22b20a576823117c242b8fd4552d4466850ab828348e41bfe01ece066575051e", "size_in_bytes": 3324}, {"_path": "include/pulsar/KeyValue.h", "path_type": "hardlink", "sha256": "23dd87b2c323ae4f572d01045e126bfa5b27d91310502f7e4f3fc742e3949dfe", "sha256_in_prefix": "23dd87b2c323ae4f572d01045e126bfa5b27d91310502f7e4f3fc742e3949dfe", "size_in_bytes": 2256}, {"_path": "include/pulsar/Logger.h", "path_type": "hardlink", "sha256": "5bc7aaaa225dba980f7c5fd1f532df20bbb9bdf3a43ea1017307e26a9993ad30", "sha256_in_prefix": "5bc7aaaa225dba980f7c5fd1f532df20bbb9bdf3a43ea1017307e26a9993ad30", "size_in_bytes": 2020}, {"_path": "include/pulsar/Message.h", "path_type": "hardlink", "sha256": "08af791e0250a748dcc95199c992cdf99afd408b85bb00e33ff39140db5c3c6c", "sha256_in_prefix": "08af791e0250a748dcc95199c992cdf99afd408b85bb00e33ff39140db5c3c6c", "size_in_bytes": 6645}, {"_path": "include/pulsar/MessageBatch.h", "path_type": "hardlink", "sha256": "008df93bdf002114424f65e88b12883a75beee1e3059d1c30ccbb6e9032fbb5d", "sha256_in_prefix": "008df93bdf002114424f65e88b12883a75beee1e3059d1c30ccbb6e9032fbb5d", "size_in_bytes": 1506}, {"_path": "include/pulsar/MessageBuilder.h", "path_type": "hardlink", "sha256": "beecebaad215c6e01b157a044314fc5e61b2f8ad0a92e68d4ea1e43181a278ab", "sha256_in_prefix": "beecebaad215c6e01b157a044314fc5e61b2f8ad0a92e68d4ea1e43181a278ab", "size_in_bytes": 5522}, {"_path": "include/pulsar/MessageId.h", "path_type": "hardlink", "sha256": "56c727fa81f07ca4d5999d9f97556f6307889ed174b96ef032daec1839e753dc", "sha256_in_prefix": "56c727fa81f07ca4d5999d9f97556f6307889ed174b96ef032daec1839e753dc", "size_in_bytes": 3971}, {"_path": "include/pulsar/MessageIdBuilder.h", "path_type": "hardlink", "sha256": "fc81444b565eb9d9a293aec090584e38d8f7f7aa78a3226e08d5e6adf6ed9f63", "sha256_in_prefix": "fc81444b565eb9d9a293aec090584e38d8f7f7aa78a3226e08d5e6adf6ed9f63", "size_in_bytes": 2845}, {"_path": "include/pulsar/MessageRoutingPolicy.h", "path_type": "hardlink", "sha256": "bf3120a27015ed41737acb0472f1eacdd8189993715a331b196a0a3f1a80a395", "sha256_in_prefix": "bf3120a27015ed41737acb0472f1eacdd8189993715a331b196a0a3f1a80a395", "size_in_bytes": 2090}, {"_path": "include/pulsar/Producer.h", "path_type": "hardlink", "sha256": "7ab6045693be34e5c929e4ffc676fd2f27a6928a5b57a5f0d0e59e035c2edf98", "sha256_in_prefix": "7ab6045693be34e5c929e4ffc676fd2f27a6928a5b57a5f0d0e59e035c2edf98", "size_in_bytes": 6649}, {"_path": "include/pulsar/ProducerConfiguration.h", "path_type": "hardlink", "sha256": "bdf6d667d41949390b6cfec85c886fca3c2710c4cf0782460abb8e08d607fddb", "sha256_in_prefix": "bdf6d667d41949390b6cfec85c886fca3c2710c4cf0782460abb8e08d607fddb", "size_in_bytes": 19531}, {"_path": "include/pulsar/ProducerCryptoFailureAction.h", "path_type": "hardlink", "sha256": "e91ac293ea4fff8619681e2e6481316b2564f4c0920289e3bfab2650df95dc1e", "sha256_in_prefix": "e91ac293ea4fff8619681e2e6481316b2564f4c0920289e3bfab2650df95dc1e", "size_in_bytes": 1185}, {"_path": "include/pulsar/ProducerInterceptor.h", "path_type": "hardlink", "sha256": "78a8685d2f7c75bb213b4997e2db5fd61906b6ff54038b1b93a828a185c9478d", "sha256_in_prefix": "78a8685d2f7c75bb213b4997e2db5fd61906b6ff54038b1b93a828a185c9478d", "size_in_bytes": 4752}, {"_path": "include/pulsar/ProtobufNativeSchema.h", "path_type": "hardlink", "sha256": "6fc2e5ab26583f94bfab7277b014b9a995a8dada17828b6633f824c7f7af861e", "sha256_in_prefix": "6fc2e5ab26583f94bfab7277b014b9a995a8dada17828b6633f824c7f7af861e", "size_in_bytes": 1260}, {"_path": "include/pulsar/Reader.h", "path_type": "hardlink", "sha256": "a5a305d8770af208f9d698603753b0bfd920d6d68b4a426033b77ba4c4ee8a04", "sha256_in_prefix": "a5a305d8770af208f9d698603753b0bfd920d6d68b4a426033b77ba4c4ee8a04", "size_in_bytes": 5705}, {"_path": "include/pulsar/ReaderConfiguration.h", "path_type": "hardlink", "sha256": "4cd840ce66f9bf9de63c0f5ddedfa9570f3c24496dd564a56cded32cdfe15a7a", "sha256_in_prefix": "4cd840ce66f9bf9de63c0f5ddedfa9570f3c24496dd564a56cded32cdfe15a7a", "size_in_bytes": 10659}, {"_path": "include/pulsar/RegexSubscriptionMode.h", "path_type": "hardlink", "sha256": "c1062f7a868c99790bc96b705d21c61e5b9725f5bf678d3935979959219fa5b2", "sha256_in_prefix": "c1062f7a868c99790bc96b705d21c61e5b9725f5bf678d3935979959219fa5b2", "size_in_bytes": 1249}, {"_path": "include/pulsar/Result.h", "path_type": "hardlink", "sha256": "fc87b7f12cc7810c910b2be9387cfc5d56c7eb49d7bbbd71baeca714b30cb8b6", "sha256_in_prefix": "fc87b7f12cc7810c910b2be9387cfc5d56c7eb49d7bbbd71baeca714b30cb8b6", "size_in_bytes": 5101}, {"_path": "include/pulsar/Schema.h", "path_type": "hardlink", "sha256": "614eb0bb15350889841a34c77c771e340038297aeb30ddbb8de90948ef98415f", "sha256_in_prefix": "614eb0bb15350889841a34c77c771e340038297aeb30ddbb8de90948ef98415f", "size_in_bytes": 4797}, {"_path": "include/pulsar/TableView.h", "path_type": "hardlink", "sha256": "cfde401855615f4abd86874ae5906a0a4e000b428ddf4eff520fbe2f7befca33", "sha256_in_prefix": "cfde401855615f4abd86874ae5906a0a4e000b428ddf4eff520fbe2f7befca33", "size_in_bytes": 3848}, {"_path": "include/pulsar/TableViewConfiguration.h", "path_type": "hardlink", "sha256": "6afae0108d6a782a9c6a06319df1e1c76094957740ce5983316f79120e9dda27", "sha256_in_prefix": "6afae0108d6a782a9c6a06319df1e1c76094957740ce5983316f79120e9dda27", "size_in_bytes": 1441}, {"_path": "include/pulsar/TopicMetadata.h", "path_type": "hardlink", "sha256": "a4186b437e3d7619090fabdc9a4717405f66167a7c4f74772a7861af6e83a68b", "sha256_in_prefix": "a4186b437e3d7619090fabdc9a4717405f66167a7c4f74772a7861af6e83a68b", "size_in_bytes": 1225}, {"_path": "include/pulsar/TypedMessage.h", "path_type": "hardlink", "sha256": "95efd7272fb66b4d5fd59e7099a5a21fa7846e7bd8a1189042d55372b8c76a91", "sha256_in_prefix": "95efd7272fb66b4d5fd59e7099a5a21fa7846e7bd8a1189042d55372b8c76a91", "size_in_bytes": 1487}, {"_path": "include/pulsar/TypedMessageBuilder.h", "path_type": "hardlink", "sha256": "2d35bf780138daa73c7f0d04f4dc0012997814cd6770b0f4017cf9c30237e2c1", "sha256_in_prefix": "2d35bf780138daa73c7f0d04f4dc0012997814cd6770b0f4017cf9c30237e2c1", "size_in_bytes": 2416}, {"_path": "include/pulsar/Version.h", "path_type": "hardlink", "sha256": "fd606a1ef6e5cf677b0b076d4c24a014cc50731c774aa207f3c5c85a312e56c5", "sha256_in_prefix": "fd606a1ef6e5cf677b0b076d4c24a014cc50731c774aa207f3c5c85a312e56c5", "size_in_bytes": 969}, {"_path": "include/pulsar/c/authentication.h", "path_type": "hardlink", "sha256": "1715599bbb1bd64b61d381935784fc2966b980a4746746df43bc1c47cceb6a0c", "sha256_in_prefix": "1715599bbb1bd64b61d381935784fc2966b980a4746746df43bc1c47cceb6a0c", "size_in_bytes": 2152}, {"_path": "include/pulsar/c/client.h", "path_type": "hardlink", "sha256": "3fe851bd1061f90c644efa8d2116775067ec130f22d4020d5942a684d1333656", "sha256_in_prefix": "3fe851bd1061f90c644efa8d2116775067ec130f22d4020d5942a684d1333656", "size_in_bytes": 12471}, {"_path": "include/pulsar/c/client_configuration.h", "path_type": "hardlink", "sha256": "cf996c71bd3ae7449e68dabcaa8e9567f410d1176fe06b4194dbda96bb723725", "sha256_in_prefix": "cf996c71bd3ae7449e68dabcaa8e9567f410d1176fe06b4194dbda96bb723725", "size_in_bytes": 8522}, {"_path": "include/pulsar/c/consumer.h", "path_type": "hardlink", "sha256": "f71a53a5cf609b7020bbfd80172a97138239b1f16decd7176ef7dd9911f89c3d", "sha256_in_prefix": "f71a53a5cf609b7020bbfd80172a97138239b1f16decd7176ef7dd9911f89c3d", "size_in_bytes": 13825}, {"_path": "include/pulsar/c/consumer_configuration.h", "path_type": "hardlink", "sha256": "8148ac95cb95daf5b36a6c0edae056582512fd8f0071409bee5c4e5e48eb4f42", "sha256_in_prefix": "8148ac95cb95daf5b36a6c0edae056582512fd8f0071409bee5c4e5e48eb4f42", "size_in_bytes": 18263}, {"_path": "include/pulsar/c/message.h", "path_type": "hardlink", "sha256": "5202e3170145344859c2f6d3dc998e904973905a32e3133d5961ba67a63bae48", "sha256_in_prefix": "5202e3170145344859c2f6d3dc998e904973905a32e3133d5961ba67a63bae48", "size_in_bytes": 7945}, {"_path": "include/pulsar/c/message_id.h", "path_type": "hardlink", "sha256": "c783595f6bdf1ac9649bba5cc5ea0500750f5e2727e847fc595d1276d06622d2", "sha256_in_prefix": "c783595f6bdf1ac9649bba5cc5ea0500750f5e2727e847fc595d1276d06622d2", "size_in_bytes": 1816}, {"_path": "include/pulsar/c/message_router.h", "path_type": "hardlink", "sha256": "82a8b26273a96eefbd8e5ee50063df4c5f5ed1e7bf3022542504231c728da6e6", "sha256_in_prefix": "82a8b26273a96eefbd8e5ee50063df4c5f5ed1e7bf3022542504231c728da6e6", "size_in_bytes": 1265}, {"_path": "include/pulsar/c/messages.h", "path_type": "hardlink", "sha256": "afd9e3c044ec1ef167b548f831415b08ff276be8338b9e38f7084372a9ba30a6", "sha256_in_prefix": "afd9e3c044ec1ef167b548f831415b08ff276be8338b9e38f7084372a9ba30a6", "size_in_bytes": 1645}, {"_path": "include/pulsar/c/producer.h", "path_type": "hardlink", "sha256": "96d8235c9cf91186e50c1f95356f42785014cd61c5c47780678c3da3920fcd97", "sha256_in_prefix": "96d8235c9cf91186e50c1f95356f42785014cd61c5c47780678c3da3920fcd97", "size_in_bytes": 5117}, {"_path": "include/pulsar/c/producer_configuration.h", "path_type": "hardlink", "sha256": "247d549f22f0444bf7587b5d32542d6aaf20ba00cc7e1b121acaa67a33137cac", "sha256_in_prefix": "247d549f22f0444bf7587b5d32542d6aaf20ba00cc7e1b121acaa67a33137cac", "size_in_bytes": 10418}, {"_path": "include/pulsar/c/reader.h", "path_type": "hardlink", "sha256": "3f683bda1e19d39e134f7033ae837b684a4f46ae0b91fdc909dd7ca440de3dc3", "sha256_in_prefix": "3f683bda1e19d39e134f7033ae837b684a4f46ae0b91fdc909dd7ca440de3dc3", "size_in_bytes": 4670}, {"_path": "include/pulsar/c/reader_configuration.h", "path_type": "hardlink", "sha256": "c90f6db50a2f7dffe493d20bef053896fe3d0f4a127930c839200d8b847e7ad4", "sha256_in_prefix": "c90f6db50a2f7dffe493d20bef053896fe3d0f4a127930c839200d8b847e7ad4", "size_in_bytes": 4422}, {"_path": "include/pulsar/c/result.h", "path_type": "hardlink", "sha256": "f15fe52ace9645e515d39fa50c493b5b47a7a49280f06474b4a6df7b9fe76b9f", "sha256_in_prefix": "f15fe52ace9645e515d39fa50c493b5b47a7a49280f06474b4a6df7b9fe76b9f", "size_in_bytes": 5306}, {"_path": "include/pulsar/c/string_list.h", "path_type": "hardlink", "sha256": "c1ffb381f7544f09220c05f11b1160ab0aa5779ca347ba206cf2d866bf11e864", "sha256_in_prefix": "c1ffb381f7544f09220c05f11b1160ab0aa5779ca347ba206cf2d866bf11e864", "size_in_bytes": 1370}, {"_path": "include/pulsar/c/string_map.h", "path_type": "hardlink", "sha256": "2ca113ec49560f6dc20447dbf333d58c2ad336e666de0177f6a00dd2f7dc535a", "sha256_in_prefix": "2ca113ec49560f6dc20447dbf333d58c2ad336e666de0177f6a00dd2f7dc535a", "size_in_bytes": 1555}, {"_path": "include/pulsar/c/table_view.h", "path_type": "hardlink", "sha256": "b0d42728d07cba0c8628d422f98bb2a9936a5bea75fee35415a3ecc8f0da77ec", "sha256_in_prefix": "b0d42728d07cba0c8628d422f98bb2a9936a5bea75fee35415a3ecc8f0da77ec", "size_in_bytes": 4860}, {"_path": "include/pulsar/c/table_view_configuration.h", "path_type": "hardlink", "sha256": "987612ed95824fda70c35af4bb88e22190d1219b07ea06eb52c007e838952f34", "sha256_in_prefix": "987612ed95824fda70c35af4bb88e22190d1219b07ea06eb52c007e838952f34", "size_in_bytes": 1800}, {"_path": "include/pulsar/c/version.h", "path_type": "hardlink", "sha256": "7c360e1c748029c331391e7ddf1bd90b379340d597af15769a751e5df486b40a", "sha256_in_prefix": "7c360e1c748029c331391e7ddf1bd90b379340d597af15769a751e5df486b40a", "size_in_bytes": 852}, {"_path": "include/pulsar/defines.h", "path_type": "hardlink", "sha256": "c7335f36073b593bc5a0b2928ba6f9c5e8ff9eafbc2bc7f73cd1c016cf80514f", "sha256_in_prefix": "c7335f36073b593bc5a0b2928ba6f9c5e8ff9eafbc2bc7f73cd1c016cf80514f", "size_in_bytes": 1219}, {"_path": "lib/libpulsar.dylib", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/bld/rattler-build_libpulsar_1751877254/host_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_pl", "sha256": "a28cbdbde47535c4a7fd3b2497c8822d26c393ad6ff76a5272e3a72edfbb8770", "sha256_in_prefix": "6cfc77cc4d963f4b7d8bcc1e060e45c888dbb2d8f0d2b8e6b3a7645864044ae4", "size_in_bytes": 4686080}], "paths_version": 1}, "requested_spec": "None", "sha256": "e743f68e5b14ea6d2cb63580229b20ccd036b869bcb2dca1ae92033b3b56e5be", "size": 1110195, "subdir": "osx-64", "timestamp": 1751877254000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libpulsar-3.7.2-hde974f2_0.conda", "version": "3.7.2"}