==> 2025-09-21 19:04:49 <==
# cmd: /usr/local/Caskroom/miniconda/base/lib/python3.12/site-packages/conda/__main__.py create --yes --prefix .conda python=3.11
# conda version: 24.11.1
+defaults/noarch::pip-25.2-pyhc872135_0
+defaults/noarch::tzdata-2025b-h04d1e81_0
+defaults/osx-64::bzip2-1.0.8-h6c40b1e_6
+defaults/osx-64::ca-certificates-2025.7.15-hecd8cb5_0
+defaults/osx-64::expat-2.7.1-h6d0c2b6_0
+defaults/osx-64::libcxx-19.1.7-haebbb44_3
+defaults/osx-64::libffi-3.4.4-hecd8cb5_1
+defaults/osx-64::ncurses-6.5-h923df54_0
+defaults/osx-64::openssl-3.0.17-hee2dfae_0
+defaults/osx-64::python-3.11.13-hbff2529_0
+defaults/osx-64::readline-8.3-h49f2429_0
+defaults/osx-64::setuptools-78.1.1-py311hecd8cb5_0
+defaults/osx-64::sqlite-3.50.2-hc8b0dd6_1
+defaults/osx-64::tk-8.6.15-h3a5a201_0
+defaults/osx-64::wheel-0.45.1-py311hecd8cb5_0
+defaults/osx-64::xz-5.6.4-h46256e1_1
+defaults/osx-64::zlib-1.2.13-h4b97444_1
# update specs: ['python=3.11']
==> 2025-09-21 19:10:17 <==
# cmd: /usr/local/Caskroom/miniconda/base/lib/python3.12/site-packages/conda/__main__.py env update --prefix /Users/<USER>/Documents/projects/llm_engineering/.conda --file /Users/<USER>/Documents/projects/llm_engineering/environment.yml
# conda version: 24.11.1
-defaults/noarch::pip-25.2-pyhc872135_0
-defaults/osx-64::ca-certificates-2025.7.15-hecd8cb5_0
-defaults/osx-64::libffi-3.4.4-hecd8cb5_1
-defaults/osx-64::openssl-3.0.17-hee2dfae_0
-defaults/osx-64::python-3.11.13-hbff2529_0
-defaults/osx-64::sqlite-3.50.2-hc8b0dd6_1
-defaults/osx-64::tk-8.6.15-h3a5a201_0
-defaults/osx-64::xz-5.6.4-h46256e1_1
-defaults/osx-64::zlib-1.2.13-h4b97444_1
+conda-forge/noarch::_python_abi3_support-1.0-hd8ed1ab_2
+conda-forge/noarch::aiohappyeyeballs-2.6.1-pyhd8ed1ab_0
+conda-forge/noarch::aiosignal-1.4.0-pyhd8ed1ab_0
+conda-forge/noarch::annotated-types-0.7.0-pyhd8ed1ab_1
+conda-forge/noarch::ansi2html-1.9.2-pyhcf101f3_3
+conda-forge/noarch::anyio-4.10.0-pyhe01879c_0
+conda-forge/noarch::appnope-0.1.4-pyhd8ed1ab_1
+conda-forge/noarch::argon2-cffi-25.1.0-pyhd8ed1ab_0
+conda-forge/noarch::arrow-1.3.0-pyhd8ed1ab_1
+conda-forge/noarch::asgiref-3.9.1-pyhd8ed1ab_0
+conda-forge/noarch::asttokens-3.0.0-pyhd8ed1ab_1
+conda-forge/noarch::async-lru-2.0.5-pyh29332c3_0
+conda-forge/noarch::attrs-25.3.0-pyh71513ae_0
+conda-forge/noarch::babel-2.17.0-pyhd8ed1ab_0
+conda-forge/noarch::backoff-2.2.1-pyhd8ed1ab_1
+conda-forge/noarch::beautifulsoup4-4.13.5-pyha770c72_0
+conda-forge/noarch::bleach-6.2.0-pyh29332c3_4
+conda-forge/noarch::bleach-with-css-6.2.0-h82add2a_4
+conda-forge/noarch::blinker-1.9.0-pyhff2d567_0
+conda-forge/noarch::ca-certificates-2025.8.3-hbd8a1cb_0
+conda-forge/noarch::cached-property-1.5.2-hd8ed1ab_1
+conda-forge/noarch::cached_property-1.5.2-pyha770c72_1
+conda-forge/noarch::cachetools-5.5.2-pyhd8ed1ab_0
+conda-forge/noarch::certifi-2025.8.3-pyhd8ed1ab_0
+conda-forge/noarch::charset-normalizer-3.4.3-pyhd8ed1ab_0
+conda-forge/noarch::click-8.3.0-pyh707e725_0
+conda-forge/noarch::colorama-0.4.6-pyhd8ed1ab_1
+conda-forge/noarch::coloredlogs-15.0.1-pyhd8ed1ab_4
+conda-forge/noarch::comm-0.2.3-pyhe01879c_0
+conda-forge/noarch::cpython-3.11.13-py311hd8ed1ab_0
+conda-forge/noarch::cycler-0.12.1-pyhd8ed1ab_1
+conda-forge/noarch::dash-3.2.0-pyhd8ed1ab_0
+conda-forge/noarch::decorator-5.2.1-pyhd8ed1ab_0
+conda-forge/noarch::defusedxml-0.7.1-pyhd8ed1ab_0
+conda-forge/noarch::deprecated-1.2.18-pyhd8ed1ab_0
+conda-forge/noarch::distro-1.9.0-pyhd8ed1ab_1
+conda-forge/noarch::dnspython-2.8.0-pyhcf101f3_0
+conda-forge/noarch::durationpy-0.10-pyhd8ed1ab_0
+conda-forge/noarch::email-validator-2.3.0-pyhd8ed1ab_0
+conda-forge/noarch::email_validator-2.3.0-hd8ed1ab_0
+conda-forge/noarch::exceptiongroup-1.3.0-pyhd8ed1ab_0
+conda-forge/noarch::executing-2.2.1-pyhd8ed1ab_0
+conda-forge/noarch::fastapi-0.115.9-pyh29332c3_0
+conda-forge/noarch::fastapi-cli-0.0.13-pyhcf101f3_0
+conda-forge/noarch::filelock-3.19.1-pyhd8ed1ab_0
+conda-forge/noarch::flask-3.1.2-pyhd8ed1ab_0
+conda-forge/noarch::fqdn-1.5.1-pyhd8ed1ab_1
+conda-forge/noarch::fsspec-2025.9.0-pyhd8ed1ab_0
+conda-forge/noarch::google-auth-2.40.3-pyhd8ed1ab_0
+conda-forge/noarch::googleapis-common-protos-1.70.0-pyhd8ed1ab_0
+conda-forge/noarch::h11-0.16.0-pyhd8ed1ab_0
+conda-forge/noarch::h2-4.3.0-pyhcf101f3_0
+conda-forge/noarch::hpack-4.1.0-pyhd8ed1ab_0
+conda-forge/noarch::httpcore-1.0.9-pyh29332c3_0
+conda-forge/noarch::httpx-0.28.1-pyhd8ed1ab_0
+conda-forge/noarch::huggingface_hub-0.35.0-pyhd8ed1ab_0
+conda-forge/noarch::humanfriendly-10.0-pyh707e725_8
+conda-forge/noarch::hyperframe-6.1.0-pyhd8ed1ab_0
+conda-forge/noarch::idna-3.10-pyhd8ed1ab_1
+conda-forge/noarch::importlib-metadata-8.7.0-pyhe01879c_1
+conda-forge/noarch::importlib-resources-6.5.2-pyhd8ed1ab_0
+conda-forge/noarch::importlib_resources-6.5.2-pyhd8ed1ab_0
+conda-forge/noarch::ipykernel-6.30.1-pyh92f572d_0
+conda-forge/noarch::ipython-9.5.0-pyhfa0c392_0
+conda-forge/noarch::ipython_pygments_lexers-1.1.1-pyhd8ed1ab_0
+conda-forge/noarch::ipywidgets-8.1.7-pyhd8ed1ab_0
+conda-forge/noarch::isoduration-20.11.0-pyhd8ed1ab_1
+conda-forge/noarch::itsdangerous-2.2.0-pyhd8ed1ab_1
+conda-forge/noarch::jedi-0.19.2-pyhd8ed1ab_1
+conda-forge/noarch::jinja2-3.1.6-pyhd8ed1ab_0
+conda-forge/noarch::joblib-1.5.2-pyhd8ed1ab_0
+conda-forge/noarch::json5-0.12.1-pyhd8ed1ab_0
+conda-forge/noarch::jsonschema-4.25.1-pyhe01879c_0
+conda-forge/noarch::jsonschema-specifications-2025.9.1-pyhcf101f3_0
+conda-forge/noarch::jsonschema-with-format-nongpl-4.25.1-he01879c_0
+conda-forge/noarch::jupyter-dash-0.4.2-pyhd8ed1ab_1
+conda-forge/noarch::jupyter-lsp-2.3.0-pyhcf101f3_0
+conda-forge/noarch::jupyter_client-8.6.3-pyhd8ed1ab_1
+conda-forge/noarch::jupyter_core-5.8.1-pyh31011fe_0
+conda-forge/noarch::jupyter_events-0.12.0-pyh29332c3_0
+conda-forge/noarch::jupyter_server-2.17.0-pyhcf101f3_0
+conda-forge/noarch::jupyter_server_terminals-0.5.3-pyhd8ed1ab_1
+conda-forge/noarch::jupyterlab-4.4.7-pyhd8ed1ab_0
+conda-forge/noarch::jupyterlab_pygments-0.3.0-pyhd8ed1ab_2
+conda-forge/noarch::jupyterlab_server-2.27.3-pyhd8ed1ab_1
+conda-forge/noarch::jupyterlab_widgets-3.0.15-pyhd8ed1ab_0
+conda-forge/noarch::lark-1.2.2-pyhd8ed1ab_1
+conda-forge/noarch::markdown-it-py-4.0.0-pyhd8ed1ab_0
+conda-forge/noarch::matplotlib-inline-0.1.7-pyhd8ed1ab_1
+conda-forge/noarch::mdurl-0.1.2-pyhd8ed1ab_1
+conda-forge/noarch::mistune-3.1.4-pyhcf101f3_0
+conda-forge/noarch::monotonic-1.6-pyhd8ed1ab_0
+conda-forge/noarch::mpmath-1.3.0-pyhd8ed1ab_1
+conda-forge/noarch::munkres-1.1.4-pyhd8ed1ab_1
+conda-forge/noarch::narwhals-2.5.0-pyhcf101f3_0
+conda-forge/noarch::nbclient-0.10.2-pyhd8ed1ab_0
+conda-forge/noarch::nbconvert-core-7.16.6-pyh29332c3_0
+conda-forge/noarch::nbformat-5.10.4-pyhd8ed1ab_1
+conda-forge/noarch::nest-asyncio-1.6.0-pyhd8ed1ab_1
+conda-forge/noarch::networkx-3.5-pyhe01879c_0
+conda-forge/noarch::notebook-shim-0.2.4-pyhd8ed1ab_1
+conda-forge/noarch::oauthlib-3.3.1-pyhd8ed1ab_0
+conda-forge/noarch::opentelemetry-api-1.37.0-pyhd8ed1ab_0
+conda-forge/noarch::opentelemetry-exporter-otlp-proto-common-1.37.0-pyhd8ed1ab_0
+conda-forge/noarch::opentelemetry-exporter-otlp-proto-grpc-1.37.0-pyhd8ed1ab_0
+conda-forge/noarch::opentelemetry-instrumentation-0.58b0-pyhd8ed1ab_0
+conda-forge/noarch::opentelemetry-instrumentation-asgi-0.58b0-pyhd8ed1ab_0
+conda-forge/noarch::opentelemetry-instrumentation-fastapi-0.58b0-pyhd8ed1ab_0
+conda-forge/noarch::opentelemetry-proto-1.37.0-pyhd8ed1ab_0
+conda-forge/noarch::opentelemetry-sdk-1.37.0-pyhd8ed1ab_0
+conda-forge/noarch::opentelemetry-semantic-conventions-0.58b0-pyh3cfb1c2_0
+conda-forge/noarch::opentelemetry-util-http-0.58b0-pyhd8ed1ab_0
+conda-forge/noarch::overrides-7.7.0-pyhd8ed1ab_1
+conda-forge/noarch::packaging-25.0-pyh29332c3_1
+conda-forge/noarch::pandocfilters-1.5.0-pyhd8ed1ab_0
+conda-forge/noarch::parso-0.8.5-pyhcf101f3_0
+conda-forge/noarch::pexpect-4.9.0-pyhd8ed1ab_1
+conda-forge/noarch::pickleshare-0.7.5-pyhd8ed1ab_1004
+conda-forge/noarch::pip-25.2-pyh8b19718_0
+conda-forge/noarch::platformdirs-4.4.0-pyhcf101f3_0
+conda-forge/noarch::plotly-6.3.0-pyhd8ed1ab_0
+conda-forge/noarch::posthog-5.4.0-pyhd8ed1ab_0
+conda-forge/noarch::prometheus_client-0.23.1-pyhd8ed1ab_0
+conda-forge/noarch::prompt-toolkit-3.0.52-pyha770c72_0
+conda-forge/noarch::ptyprocess-0.7.0-pyhd8ed1ab_1
+conda-forge/noarch::pure_eval-0.2.3-pyhd8ed1ab_1
+conda-forge/noarch::pyasn1-0.6.1-pyhd8ed1ab_2
+conda-forge/noarch::pyasn1-modules-0.4.2-pyhd8ed1ab_0
+conda-forge/noarch::pybind11-3.0.1-pyh7a1b43c_0
+conda-forge/noarch::pybind11-global-3.0.1-pyhc7ab6ef_0
+conda-forge/noarch::pycparser-2.22-pyh29332c3_1
+conda-forge/noarch::pydantic-2.11.9-pyh3cfb1c2_0
+conda-forge/noarch::pygments-2.19.2-pyhd8ed1ab_0
+conda-forge/noarch::pyjwt-2.10.1-pyhd8ed1ab_0
+conda-forge/noarch::pyparsing-3.2.5-pyhcf101f3_0
+conda-forge/noarch::pypika-0.48.9-pyhd8ed1ab_1
+conda-forge/noarch::pyproject_hooks-1.2.0-pyhd8ed1ab_1
+conda-forge/noarch::pysocks-1.7.1-pyha55dd90_7
+conda-forge/noarch::python-build-1.3.0-pyhff2d567_0
+conda-forge/noarch::python-dateutil-2.9.0.post0-pyhe01879c_2
+conda-forge/noarch::python-dotenv-1.1.1-pyhe01879c_0
+conda-forge/noarch::python-fastjsonschema-2.21.2-pyhe01879c_0
+conda-forge/noarch::python-flatbuffers-25.2.10-pyhbc23db3_0
+conda-forge/noarch::python-gil-3.11.13-hd8ed1ab_0
+conda-forge/noarch::python-json-logger-2.0.7-pyhd8ed1ab_0
+conda-forge/noarch::python-kubernetes-33.1.0-pyhd8ed1ab_0
+conda-forge/noarch::python-multipart-0.0.20-pyhff2d567_0
+conda-forge/noarch::python-tzdata-2025.2-pyhd8ed1ab_0
+conda-forge/noarch::python_abi-3.11-8_cp311
+conda-forge/noarch::pytz-2025.2-pyhd8ed1ab_0
+conda-forge/noarch::pyu2f-0.1.5-pyhd8ed1ab_1
+conda-forge/noarch::referencing-0.36.2-pyh29332c3_0
+conda-forge/noarch::requests-2.32.5-pyhd8ed1ab_0
+conda-forge/noarch::requests-oauthlib-2.0.0-pyhd8ed1ab_1
+conda-forge/noarch::retrying-1.4.2-pyhe01879c_0
+conda-forge/noarch::rfc3339-validator-0.1.4-pyhd8ed1ab_1
+conda-forge/noarch::rfc3986-validator-0.1.1-pyh9f0ad1d_0
+conda-forge/noarch::rfc3987-syntax-1.1.0-pyhe01879c_1
+conda-forge/noarch::rich-14.1.0-pyhe01879c_0
+conda-forge/noarch::rich-toolkit-0.15.1-pyhcf101f3_0
+conda-forge/noarch::rsa-4.9.1-pyhd8ed1ab_0
+conda-forge/noarch::send2trash-1.8.3-pyh31c8845_1
+conda-forge/noarch::shellingham-1.5.4-pyhd8ed1ab_1
+conda-forge/noarch::six-1.17.0-pyhe01879c_1
+conda-forge/noarch::sniffio-1.3.1-pyhd8ed1ab_1
+conda-forge/noarch::soupsieve-2.8-pyhd8ed1ab_0
+conda-forge/noarch::stack_data-0.6.3-pyhd8ed1ab_1
+conda-forge/noarch::starlette-0.45.3-pyha770c72_0
+conda-forge/noarch::sympy-1.14.0-pyh2585a3b_105
+conda-forge/noarch::tenacity-9.1.2-pyhd8ed1ab_0
+conda-forge/noarch::terminado-0.18.1-pyh31c8845_0
+conda-forge/noarch::threadpoolctl-3.6.0-pyhecae5ae_0
+conda-forge/noarch::tinycss2-1.4.0-pyhd8ed1ab_0
+conda-forge/noarch::tomli-2.2.1-pyhe01879c_2
+conda-forge/noarch::tqdm-4.67.1-pyhd8ed1ab_1
+conda-forge/noarch::traitlets-5.14.3-pyhd8ed1ab_1
+conda-forge/noarch::typer-0.17.4-pyh66367de_0
+conda-forge/noarch::typer-slim-0.17.4-pyhcf101f3_0
+conda-forge/noarch::typer-slim-standard-0.17.4-h5a5fed6_0
+conda-forge/noarch::types-python-dateutil-2.9.0.20250822-pyhd8ed1ab_0
+conda-forge/noarch::typing-extensions-4.15.0-h396c80c_0
+conda-forge/noarch::typing-inspection-0.4.1-pyhd8ed1ab_0
+conda-forge/noarch::typing_extensions-4.15.0-pyhcf101f3_0
+conda-forge/noarch::typing_utils-0.1.0-pyhd8ed1ab_1
+conda-forge/noarch::uri-template-1.3.0-pyhd8ed1ab_1
+conda-forge/noarch::urllib3-2.5.0-pyhd8ed1ab_0
+conda-forge/noarch::uvicorn-0.36.0-pyh31011fe_0
+conda-forge/noarch::uvicorn-standard-0.36.0-h31011fe_0
+conda-forge/noarch::wcwidth-0.2.13-pyhd8ed1ab_1
+conda-forge/noarch::webcolors-24.11.1-pyhd8ed1ab_0
+conda-forge/noarch::webencodings-0.5.1-pyhd8ed1ab_3
+conda-forge/noarch::websocket-client-1.8.0-pyhd8ed1ab_1
+conda-forge/noarch::werkzeug-3.1.3-pyhd8ed1ab_1
+conda-forge/noarch::widgetsnbextension-4.0.14-pyhd8ed1ab_0
+conda-forge/noarch::zipp-3.23.0-pyhd8ed1ab_0
+conda-forge/osx-64::_openmp_mutex-4.5-4_kmp_llvm
+conda-forge/osx-64::aiohttp-3.12.15-py311hfbe4617_0
+conda-forge/osx-64::argon2-cffi-bindings-25.1.0-py311h13e5629_0
+conda-forge/osx-64::aws-c-auth-0.9.0-h7b7b1db_16
+conda-forge/osx-64::aws-c-cal-0.9.2-h6f29d6d_1
+conda-forge/osx-64::aws-c-common-0.12.4-h1c43f85_0
+conda-forge/osx-64::aws-c-compression-0.3.1-h7a4e982_6
+conda-forge/osx-64::aws-c-event-stream-0.5.5-he80c2a0_1
+conda-forge/osx-64::aws-c-http-0.10.2-h5393f03_3
+conda-forge/osx-64::aws-c-io-0.21.0-h46f635e_1
+conda-forge/osx-64::aws-c-mqtt-0.13.1-h14d32e2_4
+conda-forge/osx-64::aws-c-s3-0.8.3-hee7c3f6_1
+conda-forge/osx-64::aws-c-sdkutils-0.2.4-h7a4e982_1
+conda-forge/osx-64::aws-checksums-0.2.7-h7a4e982_2
+conda-forge/osx-64::aws-crt-cpp-0.32.10-hdb1ac85_3
+conda-forge/osx-64::aws-sdk-cpp-1.11.510-h406418f_14
+conda-forge/osx-64::azure-core-cpp-1.14.0-h9a36307_0
+conda-forge/osx-64::azure-identity-cpp-1.10.0-ha4e2ba9_0
+conda-forge/osx-64::azure-storage-blobs-cpp-12.13.0-h3d2f5f1_1
+conda-forge/osx-64::azure-storage-common-cpp-12.8.0-h1ccc5ac_1
+conda-forge/osx-64::azure-storage-files-datalake-cpp-12.12.0-h86941f0_1
+conda-forge/osx-64::bcrypt-4.3.0-py311hd3d88a1_2
+conda-forge/osx-64::brotli-1.1.0-h1c43f85_4
+conda-forge/osx-64::brotli-bin-1.1.0-h1c43f85_4
+conda-forge/osx-64::brotli-python-1.1.0-py311h7b20566_4
+conda-forge/osx-64::c-ares-1.34.5-hf13058a_0
+conda-forge/osx-64::cffi-1.17.1-py311he66fa18_1
+conda-forge/osx-64::chromadb-1.0.20-py311h4cf515e_0
+conda-forge/osx-64::contourpy-1.3.3-py311hd4d69bb_2
+conda-forge/osx-64::cryptography-46.0.1-py311h3e2dd55_2
+conda-forge/osx-64::debugpy-1.8.17-py311h1854d6b_0
+conda-forge/osx-64::fonttools-4.60.0-py311he13f9b5_0
+conda-forge/osx-64::freetype-2.14.1-h694c41f_0
+conda-forge/osx-64::frozenlist-1.7.0-py311h7a2b322_0
+conda-forge/osx-64::gflags-2.2.2-hac325c4_1005
+conda-forge/osx-64::glog-0.7.1-h2790a97_0
+conda-forge/osx-64::gmp-6.3.0-hf036a51_2
+conda-forge/osx-64::gmpy2-2.2.1-py311h2f44f96_1
+conda-forge/osx-64::grpcio-1.71.0-py311hf4db095_1
+conda-forge/osx-64::hf-xet-1.1.9-py310h75747b3_1
+conda-forge/osx-64::httptools-0.6.4-py311h13e5629_1
+conda-forge/osx-64::icu-75.1-h120a0e1_0
+conda-forge/osx-64::jsonpointer-3.0.0-py311h6eed73b_2
+conda-forge/osx-64::kiwisolver-1.4.9-py311ha94bed4_1
+conda-forge/osx-64::krb5-1.21.3-h37d8d59_0
+conda-forge/osx-64::lcms2-2.17-h72f5680_0
+conda-forge/osx-64::lerc-4.0.0-hcca01a6_1
+conda-forge/osx-64::libabseil-20250127.1-cxx17_h0e468a2_0
+conda-forge/osx-64::libarrow-20.0.0-h7601d43_8_cpu
+conda-forge/osx-64::libarrow-acero-20.0.0-hdc53af8_8_cpu
+conda-forge/osx-64::libarrow-dataset-20.0.0-hdc53af8_8_cpu
+conda-forge/osx-64::libarrow-substrait-20.0.0-ha37b807_8_cpu
+conda-forge/osx-64::libblas-3.9.0-20_osx64_mkl
+conda-forge/osx-64::libbrotlicommon-1.1.0-h1c43f85_4
+conda-forge/osx-64::libbrotlidec-1.1.0-h1c43f85_4
+conda-forge/osx-64::libbrotlienc-1.1.0-h1c43f85_4
+conda-forge/osx-64::libcblas-3.9.0-20_osx64_mkl
+conda-forge/osx-64::libcrc32c-1.1.2-he49afe7_0
+conda-forge/osx-64::libcurl-8.14.1-h5dec5d8_0
+conda-forge/osx-64::libdeflate-1.24-hcc1b750_0
+conda-forge/osx-64::libedit-3.1.20250104-pl5321ha958ccf_0
+conda-forge/osx-64::libev-4.33-h10d778d_2
+conda-forge/osx-64::libevent-2.1.12-ha90c15b_1
+conda-forge/osx-64::libexpat-2.7.1-h21dd04a_0
+conda-forge/osx-64::libffi-3.4.6-h281671d_1
+conda-forge/osx-64::libfreetype-2.14.1-h694c41f_0
+conda-forge/osx-64::libfreetype6-2.14.1-h6912278_0
+conda-forge/osx-64::libgfortran-15.1.0-h5f6db21_1
+conda-forge/osx-64::libgfortran5-15.1.0-hfa3c126_1
+conda-forge/osx-64::libgoogle-cloud-2.36.0-h777fda5_1
+conda-forge/osx-64::libgoogle-cloud-storage-2.36.0-h3397294_1
+conda-forge/osx-64::libgrpc-1.71.0-h7d722e6_1
+conda-forge/osx-64::libhwloc-2.12.1-default_h8c32e24_1000
+conda-forge/osx-64::libiconv-1.18-h57a12c2_2
+conda-forge/osx-64::libjpeg-turbo-3.1.0-h6e16a3a_0
+conda-forge/osx-64::liblapack-3.9.0-20_osx64_mkl
+conda-forge/osx-64::liblzma-5.8.1-hd471939_2
+conda-forge/osx-64::liblzma-devel-5.8.1-hd471939_2
+conda-forge/osx-64::libnghttp2-1.67.0-h3338091_0
+conda-forge/osx-64::libopentelemetry-cpp-1.21.0-h30c661f_0
+conda-forge/osx-64::libopentelemetry-cpp-headers-1.21.0-h694c41f_0
+conda-forge/osx-64::libparquet-20.0.0-h283e888_8_cpu
+conda-forge/osx-64::libpng-1.6.50-h84aeda2_1
+conda-forge/osx-64::libprotobuf-5.29.3-h14f6895_2
+conda-forge/osx-64::libpulsar-3.7.2-hde974f2_0
+conda-forge/osx-64::libre2-11-2025.06.26-hfc00f1c_0
+conda-forge/osx-64::libsodium-1.0.20-hfdf4475_0
+conda-forge/osx-64::libsqlite-3.50.4-h39a8b3b_0
+conda-forge/osx-64::libssh2-1.11.1-hed3591d_0
+conda-forge/osx-64::libthrift-0.21.0-h75589b3_0
+conda-forge/osx-64::libtiff-4.7.1-haa3b502_0
+conda-forge/osx-64::libtorch-2.7.1-cpu_mkl_hd81324f_102
+conda-forge/osx-64::libutf8proc-2.10.0-h5b79583_0
+conda-forge/osx-64::libuv-1.51.0-h58003a5_1
+conda-forge/osx-64::libwebp-base-1.6.0-hb807250_0
+conda-forge/osx-64::libxcb-1.17.0-hf1f96e2_0
+conda-forge/osx-64::libxml2-2.13.8-he1bc88e_1
+conda-forge/osx-64::libzlib-1.3.1-hd23fc13_2
+conda-forge/osx-64::llvm-openmp-19.1.7-ha54dae1_1
+conda-forge/osx-64::lz4-c-1.10.0-h240833e_1
+conda-forge/osx-64::markupsafe-3.0.2-py311ha3cf9ac_1
+conda-forge/osx-64::matplotlib-3.10.6-py311h6eed73b_1
+conda-forge/osx-64::matplotlib-base-3.10.6-py311h48d7e91_1
+conda-forge/osx-64::mkl-2023.2.0-h694c41f_50502
+conda-forge/osx-64::mmh3-5.2.0-py311h7b20566_0
+conda-forge/osx-64::mpc-1.3.1-h9d8efa1_1
+conda-forge/osx-64::mpfr-4.2.1-haed47dc_3
+conda-forge/osx-64::multidict-6.6.3-py311h1cc1194_0
+conda-forge/osx-64::nlohmann_json-3.12.0-h53ec75d_1
+conda-forge/osx-64::numpy-2.3.3-py311hf157cb9_0
+conda-forge/osx-64::onnxruntime-1.22.0-py311hb982cef_0_cpu
+conda-forge/osx-64::openjpeg-2.5.4-h87e8dc5_0
+conda-forge/osx-64::openssl-3.5.3-h230baf5_0
+conda-forge/osx-64::optree-0.17.0-py311hd4d69bb_1
+conda-forge/osx-64::orc-2.1.2-h82caab2_0
+conda-forge/osx-64::orjson-3.11.3-py311hd3d88a1_1
+conda-forge/osx-64::pandas-2.3.2-py311hf4bc098_0
+conda-forge/osx-64::pillow-11.3.0-py311ha88f94d_3
+conda-forge/osx-64::prometheus-cpp-1.3.0-h7802330_0
+conda-forge/osx-64::propcache-0.3.1-py311ha3cf9ac_0
+conda-forge/osx-64::protobuf-5.29.3-py311hdcc76d6_0
+conda-forge/osx-64::psutil-7.1.0-py311hf197a57_0
+conda-forge/osx-64::pthread-stubs-0.4-h00291cd_1002
+conda-forge/osx-64::pulsar-client-3.8.0-py311h5433a66_1
+conda-forge/osx-64::pyarrow-20.0.0-py311h6eed73b_0
+conda-forge/osx-64::pyarrow-core-20.0.0-py311he02522f_0_cpu
+conda-forge/osx-64::pybase64-1.4.2-py311h13e5629_1
+conda-forge/osx-64::pydantic-core-2.33.2-py311hd1a56c6_0
+conda-forge/osx-64::pyobjc-core-11.1-py311h2f44256_1
+conda-forge/osx-64::pyobjc-framework-cocoa-11.1-py311hbc8e8a3_1
+conda-forge/osx-64::python-3.11.13-h9ccd52b_0_cpython
+conda-forge/osx-64::pytorch-2.7.1-cpu_mkl_py311_hec6b2c5_102
+conda-forge/osx-64::pyyaml-6.0.2-py311ha3cf9ac_2
+conda-forge/osx-64::pyzmq-27.1.0-py311h0ab6910_0
+conda-forge/osx-64::qhull-2020.2-h3c5361c_5
+conda-forge/osx-64::re2-2025.06.26-ha5e900a_0
+conda-forge/osx-64::rpds-py-0.27.1-py311hd3d88a1_1
+conda-forge/osx-64::scikit-learn-1.7.2-py311had5a2ce_0
+conda-forge/osx-64::scipy-1.16.2-py311h32c7e5c_0
+conda-forge/osx-64::sleef-3.9.0-h289094c_0
+conda-forge/osx-64::snappy-1.2.2-h25c286d_0
+conda-forge/osx-64::sqlite-3.50.4-h64b5abc_0
+conda-forge/osx-64::tbb-2021.13.0-hc025b3e_3
+conda-forge/osx-64::tk-8.6.13-hf689a15_2
+conda-forge/osx-64::tokenizers-0.22.1-py311h98b24dd_0
+conda-forge/osx-64::tornado-6.5.2-py311h13e5629_1
+conda-forge/osx-64::unicodedata2-16.0.0-py311h13e5629_1
+conda-forge/osx-64::uvloop-0.21.0-py311h1314207_1
+conda-forge/osx-64::watchfiles-1.1.0-py311h052f894_1
+conda-forge/osx-64::websockets-15.0.1-py311h179db11_2
+conda-forge/osx-64::wrapt-1.17.3-py311h13e5629_1
+conda-forge/osx-64::xorg-libxau-1.0.12-h6e16a3a_0
+conda-forge/osx-64::xorg-libxdmcp-1.1.5-h00291cd_0
+conda-forge/osx-64::xz-5.8.1-h357f2ed_2
+conda-forge/osx-64::xz-gpl-tools-5.8.1-h357f2ed_2
+conda-forge/osx-64::xz-tools-5.8.1-hd471939_2
+conda-forge/osx-64::yaml-0.2.5-h4132b18_3
+conda-forge/osx-64::yarl-1.20.1-py311ha3cf9ac_0
+conda-forge/osx-64::zeromq-4.3.5-h6c33b1e_9
+conda-forge/osx-64::zlib-1.3.1-hd23fc13_2
+conda-forge/osx-64::zstandard-0.25.0-py311h62e9434_0
+conda-forge/osx-64::zstd-1.5.7-h8210216_2
+defaults/noarch::pyopenssl-22.0.0-pyhd3eb1b0_0
# update specs: ['chromadb', 'hf-xet==1.1.9', 'ipywidgets', 'jupyter-dash', 'jupyterlab', 'matplotlib', 'numpy', 'pandas', 'pip', 'pyarrow', 'python-dotenv', 'python=3.11', 'pytorch', 'requests', 'scikit-learn', 'scipy']
