{"build": "h694c41f_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": [], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libopentelemetry-cpp-headers-1.21.0-h694c41f_0", "files": ["include/opentelemetry/baggage/baggage.h", "include/opentelemetry/baggage/baggage_context.h", "include/opentelemetry/baggage/propagation/baggage_propagator.h", "include/opentelemetry/common/attribute_value.h", "include/opentelemetry/common/key_value_iterable.h", "include/opentelemetry/common/key_value_iterable_view.h", "include/opentelemetry/common/kv_properties.h", "include/opentelemetry/common/macros.h", "include/opentelemetry/common/spin_lock_mutex.h", "include/opentelemetry/common/string_util.h", "include/opentelemetry/common/timestamp.h", "include/opentelemetry/config.h", "include/opentelemetry/context/context.h", "include/opentelemetry/context/context_value.h", "include/opentelemetry/context/propagation/composite_propagator.h", "include/opentelemetry/context/propagation/global_propagator.h", "include/opentelemetry/context/propagation/noop_propagator.h", "include/opentelemetry/context/propagation/text_map_propagator.h", "include/opentelemetry/context/runtime_context.h", "include/opentelemetry/detail/preprocessor.h", "include/opentelemetry/exporters/memory/in_memory_data.h", "include/opentelemetry/exporters/memory/in_memory_metric_data.h", "include/opentelemetry/exporters/memory/in_memory_metric_exporter_factory.h", "include/opentelemetry/exporters/memory/in_memory_span_data.h", "include/opentelemetry/exporters/memory/in_memory_span_exporter.h", "include/opentelemetry/exporters/memory/in_memory_span_exporter_factory.h", "include/opentelemetry/exporters/ostream/common_utils.h", "include/opentelemetry/exporters/ostream/log_record_exporter.h", "include/opentelemetry/exporters/ostream/log_record_exporter_factory.h", "include/opentelemetry/exporters/ostream/metric_exporter.h", "include/opentelemetry/exporters/ostream/metric_exporter_factory.h", "include/opentelemetry/exporters/ostream/span_exporter.h", "include/opentelemetry/exporters/ostream/span_exporter_factory.h", "include/opentelemetry/exporters/otlp/otlp_environment.h", "include/opentelemetry/exporters/otlp/otlp_grpc_client.h", "include/opentelemetry/exporters/otlp/otlp_grpc_client_factory.h", "include/opentelemetry/exporters/otlp/otlp_grpc_client_options.h", "include/opentelemetry/exporters/otlp/otlp_grpc_exporter.h", "include/opentelemetry/exporters/otlp/otlp_grpc_exporter_factory.h", "include/opentelemetry/exporters/otlp/otlp_grpc_exporter_options.h", "include/opentelemetry/exporters/otlp/otlp_grpc_log_record_exporter.h", "include/opentelemetry/exporters/otlp/otlp_grpc_log_record_exporter_factory.h", "include/opentelemetry/exporters/otlp/otlp_grpc_log_record_exporter_options.h", "include/opentelemetry/exporters/otlp/otlp_grpc_metric_exporter.h", "include/opentelemetry/exporters/otlp/otlp_grpc_metric_exporter_factory.h", "include/opentelemetry/exporters/otlp/otlp_grpc_metric_exporter_options.h", "include/opentelemetry/exporters/otlp/otlp_grpc_utils.h", "include/opentelemetry/exporters/otlp/otlp_http.h", "include/opentelemetry/exporters/otlp/otlp_http_client.h", "include/opentelemetry/exporters/otlp/otlp_http_exporter.h", "include/opentelemetry/exporters/otlp/otlp_http_exporter_factory.h", "include/opentelemetry/exporters/otlp/otlp_http_exporter_options.h", "include/opentelemetry/exporters/otlp/otlp_http_exporter_runtime_options.h", "include/opentelemetry/exporters/otlp/otlp_http_log_record_exporter.h", "include/opentelemetry/exporters/otlp/otlp_http_log_record_exporter_factory.h", "include/opentelemetry/exporters/otlp/otlp_http_log_record_exporter_options.h", "include/opentelemetry/exporters/otlp/otlp_http_log_record_exporter_runtime_options.h", "include/opentelemetry/exporters/otlp/otlp_http_metric_exporter.h", "include/opentelemetry/exporters/otlp/otlp_http_metric_exporter_factory.h", "include/opentelemetry/exporters/otlp/otlp_http_metric_exporter_options.h", "include/opentelemetry/exporters/otlp/otlp_http_metric_exporter_runtime_options.h", "include/opentelemetry/exporters/otlp/otlp_log_recordable.h", "include/opentelemetry/exporters/otlp/otlp_metric_utils.h", "include/opentelemetry/exporters/otlp/otlp_populate_attribute_utils.h", "include/opentelemetry/exporters/otlp/otlp_preferred_temporality.h", "include/opentelemetry/exporters/otlp/otlp_recordable.h", "include/opentelemetry/exporters/otlp/otlp_recordable_utils.h", "include/opentelemetry/exporters/otlp/protobuf_include_prefix.h", "include/opentelemetry/exporters/otlp/protobuf_include_suffix.h", "include/opentelemetry/exporters/prometheus/collector.h", "include/opentelemetry/exporters/prometheus/exporter.h", "include/opentelemetry/exporters/prometheus/exporter_factory.h", "include/opentelemetry/exporters/prometheus/exporter_options.h", "include/opentelemetry/exporters/prometheus/exporter_utils.h", "include/opentelemetry/exporters/zipkin/zipkin_exporter.h", "include/opentelemetry/exporters/zipkin/zipkin_exporter_factory.h", "include/opentelemetry/exporters/zipkin/zipkin_exporter_options.h", "include/opentelemetry/ext/http/client/curl/http_client_curl.h", "include/opentelemetry/ext/http/client/curl/http_operation_curl.h", "include/opentelemetry/ext/http/client/http_client.h", "include/opentelemetry/ext/http/client/http_client_factory.h", "include/opentelemetry/ext/http/common/url_parser.h", "include/opentelemetry/ext/http/server/file_http_server.h", "include/opentelemetry/ext/http/server/http_server.h", "include/opentelemetry/ext/http/server/socket_tools.h", "include/opentelemetry/logs/event_id.h", "include/opentelemetry/logs/event_logger.h", "include/opentelemetry/logs/event_logger_provider.h", "include/opentelemetry/logs/log_record.h", "include/opentelemetry/logs/logger.h", "include/opentelemetry/logs/logger_provider.h", "include/opentelemetry/logs/logger_type_traits.h", "include/opentelemetry/logs/noop.h", "include/opentelemetry/logs/provider.h", "include/opentelemetry/logs/severity.h", "include/opentelemetry/metrics/async_instruments.h", "include/opentelemetry/metrics/meter.h", "include/opentelemetry/metrics/meter_provider.h", "include/opentelemetry/metrics/noop.h", "include/opentelemetry/metrics/observer_result.h", "include/opentelemetry/metrics/provider.h", "include/opentelemetry/metrics/sync_instruments.h", "include/opentelemetry/nostd/detail/all.h", "include/opentelemetry/nostd/detail/decay.h", "include/opentelemetry/nostd/detail/dependent_type.h", "include/opentelemetry/nostd/detail/functional.h", "include/opentelemetry/nostd/detail/invoke.h", "include/opentelemetry/nostd/detail/trait.h", "include/opentelemetry/nostd/detail/type_pack_element.h", "include/opentelemetry/nostd/detail/valueless.h", "include/opentelemetry/nostd/detail/variant_alternative.h", "include/opentelemetry/nostd/detail/variant_fwd.h", "include/opentelemetry/nostd/detail/variant_size.h", "include/opentelemetry/nostd/detail/void.h", "include/opentelemetry/nostd/function_ref.h", "include/opentelemetry/nostd/internal/absl/base/attributes.h", "include/opentelemetry/nostd/internal/absl/base/config.h", "include/opentelemetry/nostd/internal/absl/base/internal/identity.h", "include/opentelemetry/nostd/internal/absl/base/internal/inline_variable.h", "include/opentelemetry/nostd/internal/absl/base/internal/invoke.h", "include/opentelemetry/nostd/internal/absl/base/macros.h", "include/opentelemetry/nostd/internal/absl/base/optimization.h", "include/opentelemetry/nostd/internal/absl/base/options.h", "include/opentelemetry/nostd/internal/absl/base/policy_checks.h", "include/opentelemetry/nostd/internal/absl/base/port.h", "include/opentelemetry/nostd/internal/absl/meta/type_traits.h", "include/opentelemetry/nostd/internal/absl/types/bad_variant_access.h", "include/opentelemetry/nostd/internal/absl/types/internal/variant.h", "include/opentelemetry/nostd/internal/absl/types/variant.h", "include/opentelemetry/nostd/internal/absl/utility/utility.h", "include/opentelemetry/nostd/shared_ptr.h", "include/opentelemetry/nostd/span.h", "include/opentelemetry/nostd/string_view.h", "include/opentelemetry/nostd/type_traits.h", "include/opentelemetry/nostd/unique_ptr.h", "include/opentelemetry/nostd/utility.h", "include/opentelemetry/nostd/variant.h", "include/opentelemetry/plugin/detail/dynamic_library_handle.h", "include/opentelemetry/plugin/detail/dynamic_load_unix.h", "include/opentelemetry/plugin/detail/dynamic_load_windows.h", "include/opentelemetry/plugin/detail/loader_info.h", "include/opentelemetry/plugin/detail/tracer_handle.h", "include/opentelemetry/plugin/detail/utility.h", "include/opentelemetry/plugin/dynamic_load.h", "include/opentelemetry/plugin/factory.h", "include/opentelemetry/plugin/hook.h", "include/opentelemetry/plugin/tracer.h", "include/opentelemetry/proto/collector/logs/v1/logs_service.grpc.pb.h", "include/opentelemetry/proto/collector/logs/v1/logs_service.pb.h", "include/opentelemetry/proto/collector/logs/v1/logs_service_mock.grpc.pb.h", "include/opentelemetry/proto/collector/metrics/v1/metrics_service.grpc.pb.h", "include/opentelemetry/proto/collector/metrics/v1/metrics_service.pb.h", "include/opentelemetry/proto/collector/metrics/v1/metrics_service_mock.grpc.pb.h", "include/opentelemetry/proto/collector/profiles/v1development/profiles_service.grpc.pb.h", "include/opentelemetry/proto/collector/profiles/v1development/profiles_service.pb.h", "include/opentelemetry/proto/collector/profiles/v1development/profiles_service_mock.grpc.pb.h", "include/opentelemetry/proto/collector/trace/v1/trace_service.grpc.pb.h", "include/opentelemetry/proto/collector/trace/v1/trace_service.pb.h", "include/opentelemetry/proto/collector/trace/v1/trace_service_mock.grpc.pb.h", "include/opentelemetry/proto/common/v1/common.grpc.pb.h", "include/opentelemetry/proto/common/v1/common.pb.h", "include/opentelemetry/proto/common/v1/common_mock.grpc.pb.h", "include/opentelemetry/proto/logs/v1/logs.grpc.pb.h", "include/opentelemetry/proto/logs/v1/logs.pb.h", "include/opentelemetry/proto/logs/v1/logs_mock.grpc.pb.h", "include/opentelemetry/proto/metrics/v1/metrics.grpc.pb.h", "include/opentelemetry/proto/metrics/v1/metrics.pb.h", "include/opentelemetry/proto/metrics/v1/metrics_mock.grpc.pb.h", "include/opentelemetry/proto/profiles/v1development/profiles.grpc.pb.h", "include/opentelemetry/proto/profiles/v1development/profiles.pb.h", "include/opentelemetry/proto/profiles/v1development/profiles_mock.grpc.pb.h", "include/opentelemetry/proto/resource/v1/resource.grpc.pb.h", "include/opentelemetry/proto/resource/v1/resource.pb.h", "include/opentelemetry/proto/resource/v1/resource_mock.grpc.pb.h", "include/opentelemetry/proto/trace/v1/trace.grpc.pb.h", "include/opentelemetry/proto/trace/v1/trace.pb.h", "include/opentelemetry/proto/trace/v1/trace_mock.grpc.pb.h", "include/opentelemetry/sdk/common/atomic_shared_ptr.h", "include/opentelemetry/sdk/common/atomic_unique_ptr.h", "include/opentelemetry/sdk/common/attribute_utils.h", "include/opentelemetry/sdk/common/attributemap_hash.h", "include/opentelemetry/sdk/common/base64.h", "include/opentelemetry/sdk/common/circular_buffer.h", "include/opentelemetry/sdk/common/circular_buffer_range.h", "include/opentelemetry/sdk/common/disabled.h", "include/opentelemetry/sdk/common/empty_attributes.h", "include/opentelemetry/sdk/common/env_variables.h", "include/opentelemetry/sdk/common/exporter_utils.h", "include/opentelemetry/sdk/common/global_log_handler.h", "include/opentelemetry/sdk/common/thread_instrumentation.h", "include/opentelemetry/sdk/instrumentationlibrary/instrumentation_library.h", "include/opentelemetry/sdk/instrumentationscope/instrumentation_scope.h", "include/opentelemetry/sdk/instrumentationscope/scope_configurator.h", "include/opentelemetry/sdk/logs/batch_log_record_processor.h", "include/opentelemetry/sdk/logs/batch_log_record_processor_factory.h", "include/opentelemetry/sdk/logs/batch_log_record_processor_options.h", "include/opentelemetry/sdk/logs/batch_log_record_processor_runtime_options.h", "include/opentelemetry/sdk/logs/event_logger.h", "include/opentelemetry/sdk/logs/event_logger_provider.h", "include/opentelemetry/sdk/logs/event_logger_provider_factory.h", "include/opentelemetry/sdk/logs/exporter.h", "include/opentelemetry/sdk/logs/logger.h", "include/opentelemetry/sdk/logs/logger_config.h", "include/opentelemetry/sdk/logs/logger_context.h", "include/opentelemetry/sdk/logs/logger_context_factory.h", "include/opentelemetry/sdk/logs/logger_provider.h", "include/opentelemetry/sdk/logs/logger_provider_factory.h", "include/opentelemetry/sdk/logs/multi_log_record_processor.h", "include/opentelemetry/sdk/logs/multi_log_record_processor_factory.h", "include/opentelemetry/sdk/logs/multi_recordable.h", "include/opentelemetry/sdk/logs/processor.h", "include/opentelemetry/sdk/logs/provider.h", "include/opentelemetry/sdk/logs/read_write_log_record.h", "include/opentelemetry/sdk/logs/readable_log_record.h", "include/opentelemetry/sdk/logs/recordable.h", "include/opentelemetry/sdk/logs/simple_log_record_processor.h", "include/opentelemetry/sdk/logs/simple_log_record_processor_factory.h", "include/opentelemetry/sdk/metrics/aggregation/aggregation.h", "include/opentelemetry/sdk/metrics/aggregation/aggregation_config.h", "include/opentelemetry/sdk/metrics/aggregation/base2_exponential_histogram_aggregation.h", "include/opentelemetry/sdk/metrics/aggregation/base2_exponential_histogram_indexer.h", "include/opentelemetry/sdk/metrics/aggregation/default_aggregation.h", "include/opentelemetry/sdk/metrics/aggregation/drop_aggregation.h", "include/opentelemetry/sdk/metrics/aggregation/histogram_aggregation.h", "include/opentelemetry/sdk/metrics/aggregation/lastvalue_aggregation.h", "include/opentelemetry/sdk/metrics/aggregation/sum_aggregation.h", "include/opentelemetry/sdk/metrics/async_instruments.h", "include/opentelemetry/sdk/metrics/data/circular_buffer.h", "include/opentelemetry/sdk/metrics/data/exemplar_data.h", "include/opentelemetry/sdk/metrics/data/metric_data.h", "include/opentelemetry/sdk/metrics/data/point_data.h", "include/opentelemetry/sdk/metrics/exemplar/aligned_histogram_bucket_exemplar_reservoir.h", "include/opentelemetry/sdk/metrics/exemplar/filter_type.h", "include/opentelemetry/sdk/metrics/exemplar/fixed_size_exemplar_reservoir.h", "include/opentelemetry/sdk/metrics/exemplar/no_exemplar_reservoir.h", "include/opentelemetry/sdk/metrics/exemplar/reservoir.h", "include/opentelemetry/sdk/metrics/exemplar/reservoir_cell.h", "include/opentelemetry/sdk/metrics/exemplar/reservoir_cell_selector.h", "include/opentelemetry/sdk/metrics/exemplar/reservoir_utils.h", "include/opentelemetry/sdk/metrics/exemplar/simple_fixed_size_exemplar_reservoir.h", "include/opentelemetry/sdk/metrics/export/metric_filter.h", "include/opentelemetry/sdk/metrics/export/metric_producer.h", "include/opentelemetry/sdk/metrics/export/periodic_exporting_metric_reader.h", "include/opentelemetry/sdk/metrics/export/periodic_exporting_metric_reader_factory.h", "include/opentelemetry/sdk/metrics/export/periodic_exporting_metric_reader_options.h", "include/opentelemetry/sdk/metrics/export/periodic_exporting_metric_reader_runtime_options.h", "include/opentelemetry/sdk/metrics/instrument_metadata_validator.h", "include/opentelemetry/sdk/metrics/instruments.h", "include/opentelemetry/sdk/metrics/meter.h", "include/opentelemetry/sdk/metrics/meter_config.h", "include/opentelemetry/sdk/metrics/meter_context.h", "include/opentelemetry/sdk/metrics/meter_context_factory.h", "include/opentelemetry/sdk/metrics/meter_provider.h", "include/opentelemetry/sdk/metrics/meter_provider_factory.h", "include/opentelemetry/sdk/metrics/metric_reader.h", "include/opentelemetry/sdk/metrics/observer_result.h", "include/opentelemetry/sdk/metrics/provider.h", "include/opentelemetry/sdk/metrics/push_metric_exporter.h", "include/opentelemetry/sdk/metrics/state/async_metric_storage.h", "include/opentelemetry/sdk/metrics/state/attributes_hashmap.h", "include/opentelemetry/sdk/metrics/state/filtered_ordered_attribute_map.h", "include/opentelemetry/sdk/metrics/state/metric_collector.h", "include/opentelemetry/sdk/metrics/state/metric_storage.h", "include/opentelemetry/sdk/metrics/state/multi_metric_storage.h", "include/opentelemetry/sdk/metrics/state/observable_registry.h", "include/opentelemetry/sdk/metrics/state/sync_metric_storage.h", "include/opentelemetry/sdk/metrics/state/temporal_metric_storage.h", "include/opentelemetry/sdk/metrics/sync_instruments.h", "include/opentelemetry/sdk/metrics/view/attributes_processor.h", "include/opentelemetry/sdk/metrics/view/instrument_selector.h", "include/opentelemetry/sdk/metrics/view/instrument_selector_factory.h", "include/opentelemetry/sdk/metrics/view/meter_selector.h", "include/opentelemetry/sdk/metrics/view/meter_selector_factory.h", "include/opentelemetry/sdk/metrics/view/predicate.h", "include/opentelemetry/sdk/metrics/view/predicate_factory.h", "include/opentelemetry/sdk/metrics/view/view.h", "include/opentelemetry/sdk/metrics/view/view_factory.h", "include/opentelemetry/sdk/metrics/view/view_registry.h", "include/opentelemetry/sdk/metrics/view/view_registry_factory.h", "include/opentelemetry/sdk/resource/resource.h", "include/opentelemetry/sdk/resource/resource_detector.h", "include/opentelemetry/sdk/resource/semantic_conventions.h", "include/opentelemetry/sdk/trace/batch_span_processor.h", "include/opentelemetry/sdk/trace/batch_span_processor_factory.h", "include/opentelemetry/sdk/trace/batch_span_processor_options.h", "include/opentelemetry/sdk/trace/batch_span_processor_runtime_options.h", "include/opentelemetry/sdk/trace/exporter.h", "include/opentelemetry/sdk/trace/id_generator.h", "include/opentelemetry/sdk/trace/multi_recordable.h", "include/opentelemetry/sdk/trace/multi_span_processor.h", "include/opentelemetry/sdk/trace/processor.h", "include/opentelemetry/sdk/trace/provider.h", "include/opentelemetry/sdk/trace/random_id_generator.h", "include/opentelemetry/sdk/trace/random_id_generator_factory.h", "include/opentelemetry/sdk/trace/recordable.h", "include/opentelemetry/sdk/trace/sampler.h", "include/opentelemetry/sdk/trace/samplers/always_off.h", "include/opentelemetry/sdk/trace/samplers/always_off_factory.h", "include/opentelemetry/sdk/trace/samplers/always_on.h", "include/opentelemetry/sdk/trace/samplers/always_on_factory.h", "include/opentelemetry/sdk/trace/samplers/parent.h", "include/opentelemetry/sdk/trace/samplers/parent_factory.h", "include/opentelemetry/sdk/trace/samplers/trace_id_ratio.h", "include/opentelemetry/sdk/trace/samplers/trace_id_ratio_factory.h", "include/opentelemetry/sdk/trace/simple_processor.h", "include/opentelemetry/sdk/trace/simple_processor_factory.h", "include/opentelemetry/sdk/trace/span_data.h", "include/opentelemetry/sdk/trace/tracer.h", "include/opentelemetry/sdk/trace/tracer_config.h", "include/opentelemetry/sdk/trace/tracer_context.h", "include/opentelemetry/sdk/trace/tracer_context_factory.h", "include/opentelemetry/sdk/trace/tracer_provider.h", "include/opentelemetry/sdk/trace/tracer_provider_factory.h", "include/opentelemetry/sdk/version/version.h", "include/opentelemetry/sdk_config.h", "include/opentelemetry/semconv/azure_metrics.h", "include/opentelemetry/semconv/cicd_metrics.h", "include/opentelemetry/semconv/client_attributes.h", "include/opentelemetry/semconv/code_attributes.h", "include/opentelemetry/semconv/container_metrics.h", "include/opentelemetry/semconv/cpu_metrics.h", "include/opentelemetry/semconv/db_attributes.h", "include/opentelemetry/semconv/db_metrics.h", "include/opentelemetry/semconv/dns_metrics.h", "include/opentelemetry/semconv/error_attributes.h", "include/opentelemetry/semconv/exception_attributes.h", "include/opentelemetry/semconv/faas_metrics.h", "include/opentelemetry/semconv/gen_ai_metrics.h", "include/opentelemetry/semconv/http_attributes.h", "include/opentelemetry/semconv/http_metrics.h", "include/opentelemetry/semconv/hw_metrics.h", "include/opentelemetry/semconv/incubating/app_attributes.h", "include/opentelemetry/semconv/incubating/artifact_attributes.h", "include/opentelemetry/semconv/incubating/aws_attributes.h", "include/opentelemetry/semconv/incubating/az_attributes.h", "include/opentelemetry/semconv/incubating/azure_attributes.h", "include/opentelemetry/semconv/incubating/azure_metrics.h", "include/opentelemetry/semconv/incubating/browser_attributes.h", "include/opentelemetry/semconv/incubating/cassandra_attributes.h", "include/opentelemetry/semconv/incubating/cicd_attributes.h", "include/opentelemetry/semconv/incubating/cicd_metrics.h", "include/opentelemetry/semconv/incubating/client_attributes.h", "include/opentelemetry/semconv/incubating/cloud_attributes.h", "include/opentelemetry/semconv/incubating/cloudevents_attributes.h", "include/opentelemetry/semconv/incubating/cloudfoundry_attributes.h", "include/opentelemetry/semconv/incubating/code_attributes.h", "include/opentelemetry/semconv/incubating/container_attributes.h", "include/opentelemetry/semconv/incubating/container_metrics.h", "include/opentelemetry/semconv/incubating/cpu_attributes.h", "include/opentelemetry/semconv/incubating/cpu_metrics.h", "include/opentelemetry/semconv/incubating/cpython_attributes.h", "include/opentelemetry/semconv/incubating/cpython_metrics.h", "include/opentelemetry/semconv/incubating/db_attributes.h", "include/opentelemetry/semconv/incubating/db_metrics.h", "include/opentelemetry/semconv/incubating/deployment_attributes.h", "include/opentelemetry/semconv/incubating/destination_attributes.h", "include/opentelemetry/semconv/incubating/device_attributes.h", "include/opentelemetry/semconv/incubating/disk_attributes.h", "include/opentelemetry/semconv/incubating/dns_attributes.h", "include/opentelemetry/semconv/incubating/dns_metrics.h", "include/opentelemetry/semconv/incubating/elasticsearch_attributes.h", "include/opentelemetry/semconv/incubating/enduser_attributes.h", "include/opentelemetry/semconv/incubating/error_attributes.h", "include/opentelemetry/semconv/incubating/event_attributes.h", "include/opentelemetry/semconv/incubating/exception_attributes.h", "include/opentelemetry/semconv/incubating/faas_attributes.h", "include/opentelemetry/semconv/incubating/faas_metrics.h", "include/opentelemetry/semconv/incubating/feature_flag_attributes.h", "include/opentelemetry/semconv/incubating/file_attributes.h", "include/opentelemetry/semconv/incubating/gcp_attributes.h", "include/opentelemetry/semconv/incubating/gen_ai_attributes.h", "include/opentelemetry/semconv/incubating/gen_ai_metrics.h", "include/opentelemetry/semconv/incubating/geo_attributes.h", "include/opentelemetry/semconv/incubating/graphql_attributes.h", "include/opentelemetry/semconv/incubating/heroku_attributes.h", "include/opentelemetry/semconv/incubating/host_attributes.h", "include/opentelemetry/semconv/incubating/http_attributes.h", "include/opentelemetry/semconv/incubating/http_metrics.h", "include/opentelemetry/semconv/incubating/hw_attributes.h", "include/opentelemetry/semconv/incubating/hw_metrics.h", "include/opentelemetry/semconv/incubating/k8s_attributes.h", "include/opentelemetry/semconv/incubating/k8s_metrics.h", "include/opentelemetry/semconv/incubating/linux_attributes.h", "include/opentelemetry/semconv/incubating/log_attributes.h", "include/opentelemetry/semconv/incubating/message_attributes.h", "include/opentelemetry/semconv/incubating/messaging_attributes.h", "include/opentelemetry/semconv/incubating/messaging_metrics.h", "include/opentelemetry/semconv/incubating/net_attributes.h", "include/opentelemetry/semconv/incubating/network_attributes.h", "include/opentelemetry/semconv/incubating/oci_attributes.h", "include/opentelemetry/semconv/incubating/opentracing_attributes.h", "include/opentelemetry/semconv/incubating/os_attributes.h", "include/opentelemetry/semconv/incubating/otel_attributes.h", "include/opentelemetry/semconv/incubating/otel_metrics.h", "include/opentelemetry/semconv/incubating/other_attributes.h", "include/opentelemetry/semconv/incubating/peer_attributes.h", "include/opentelemetry/semconv/incubating/pool_attributes.h", "include/opentelemetry/semconv/incubating/process_attributes.h", "include/opentelemetry/semconv/incubating/process_metrics.h", "include/opentelemetry/semconv/incubating/profile_attributes.h", "include/opentelemetry/semconv/incubating/rpc_attributes.h", "include/opentelemetry/semconv/incubating/rpc_metrics.h", "include/opentelemetry/semconv/incubating/security_rule_attributes.h", "include/opentelemetry/semconv/incubating/server_attributes.h", "include/opentelemetry/semconv/incubating/service_attributes.h", "include/opentelemetry/semconv/incubating/session_attributes.h", "include/opentelemetry/semconv/incubating/source_attributes.h", "include/opentelemetry/semconv/incubating/system_attributes.h", "include/opentelemetry/semconv/incubating/system_metrics.h", "include/opentelemetry/semconv/incubating/telemetry_attributes.h", "include/opentelemetry/semconv/incubating/test_attributes.h", "include/opentelemetry/semconv/incubating/thread_attributes.h", "include/opentelemetry/semconv/incubating/tls_attributes.h", "include/opentelemetry/semconv/incubating/url_attributes.h", "include/opentelemetry/semconv/incubating/user_agent_attributes.h", "include/opentelemetry/semconv/incubating/user_attributes.h", "include/opentelemetry/semconv/incubating/vcs_attributes.h", "include/opentelemetry/semconv/incubating/vcs_metrics.h", "include/opentelemetry/semconv/incubating/webengine_attributes.h", "include/opentelemetry/semconv/k8s_metrics.h", "include/opentelemetry/semconv/messaging_metrics.h", "include/opentelemetry/semconv/network_attributes.h", "include/opentelemetry/semconv/otel_attributes.h", "include/opentelemetry/semconv/otel_metrics.h", "include/opentelemetry/semconv/process_metrics.h", "include/opentelemetry/semconv/rpc_metrics.h", "include/opentelemetry/semconv/schema_url.h", "include/opentelemetry/semconv/server_attributes.h", "include/opentelemetry/semconv/service_attributes.h", "include/opentelemetry/semconv/system_metrics.h", "include/opentelemetry/semconv/telemetry_attributes.h", "include/opentelemetry/semconv/url_attributes.h", "include/opentelemetry/semconv/user_agent_attributes.h", "include/opentelemetry/semconv/vcs_metrics.h", "include/opentelemetry/std/shared_ptr.h", "include/opentelemetry/std/span.h", "include/opentelemetry/std/string_view.h", "include/opentelemetry/std/type_traits.h", "include/opentelemetry/std/unique_ptr.h", "include/opentelemetry/std/utility.h", "include/opentelemetry/std/variant.h", "include/opentelemetry/trace/context.h", "include/opentelemetry/trace/default_span.h", "include/opentelemetry/trace/noop.h", "include/opentelemetry/trace/propagation/b3_propagator.h", "include/opentelemetry/trace/propagation/detail/hex.h", "include/opentelemetry/trace/propagation/detail/string.h", "include/opentelemetry/trace/propagation/http_trace_context.h", "include/opentelemetry/trace/propagation/jaeger.h", "include/opentelemetry/trace/provider.h", "include/opentelemetry/trace/scope.h", "include/opentelemetry/trace/semantic_conventions.h", "include/opentelemetry/trace/span.h", "include/opentelemetry/trace/span_context.h", "include/opentelemetry/trace/span_context_kv_iterable.h", "include/opentelemetry/trace/span_context_kv_iterable_view.h", "include/opentelemetry/trace/span_id.h", "include/opentelemetry/trace/span_metadata.h", "include/opentelemetry/trace/span_startoptions.h", "include/opentelemetry/trace/trace_flags.h", "include/opentelemetry/trace/trace_id.h", "include/opentelemetry/trace/trace_state.h", "include/opentelemetry/trace/tracer.h", "include/opentelemetry/trace/tracer_provider.h", "include/opentelemetry/version.h", "lib/pkgconfig/opentelemetry_api.pc", "lib/pkgconfig/opentelemetry_common.pc", "lib/pkgconfig/opentelemetry_logs.pc", "lib/pkgconfig/opentelemetry_metrics.pc", "lib/pkgconfig/opentelemetry_resources.pc", "lib/pkgconfig/opentelemetry_trace.pc", "lib/pkgconfig/opentelemetry_version.pc"], "fn": "libopentelemetry-cpp-headers-1.21.0-h694c41f_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libopentelemetry-cpp-headers-1.21.0-h694c41f_0", "type": 1}, "md5": "55d26993919e0075f536dc3843b8d2a4", "name": "libopentelemetry-cpp-headers", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libopentelemetry-cpp-headers-1.21.0-h694c41f_0.conda", "paths_data": {"paths": [{"_path": "include/opentelemetry/baggage/baggage.h", "path_type": "hardlink", "sha256": "04c8a2917e75acddc384215f871c226bc199569d15cc6bb1e75d8a429310037f", "sha256_in_prefix": "04c8a2917e75acddc384215f871c226bc199569d15cc6bb1e75d8a429310037f", "size_in_bytes": 8796}, {"_path": "include/opentelemetry/baggage/baggage_context.h", "path_type": "hardlink", "sha256": "376f5503a5107fcec95017c8022123e2ca0f0d26b52963b08cd957f55d87ab2a", "sha256_in_prefix": "376f5503a5107fcec95017c8022123e2ca0f0d26b52963b08cd957f55d87ab2a", "size_in_bytes": 1028}, {"_path": "include/opentelemetry/baggage/propagation/baggage_propagator.h", "path_type": "hardlink", "sha256": "75f8931e943a56f8e325f58e2ad74fd0aacbe0949eb3bb6490c0125afba562d8", "sha256_in_prefix": "75f8931e943a56f8e325f58e2ad74fd0aacbe0949eb3bb6490c0125afba562d8", "size_in_bytes": 1603}, {"_path": "include/opentelemetry/common/attribute_value.h", "path_type": "hardlink", "sha256": "d9c578c590ecd55ad7d31bb9d93a7b83b3fd1cd449770619e9f16ab82cccbd2e", "sha256_in_prefix": "d9c578c590ecd55ad7d31bb9d93a7b83b3fd1cd449770619e9f16ab82cccbd2e", "size_in_bytes": 2737}, {"_path": "include/opentelemetry/common/key_value_iterable.h", "path_type": "hardlink", "sha256": "f35699ff9acef9bf15bba57388220f91a955937f0628d7731ad9fd4e915916d4", "sha256_in_prefix": "f35699ff9acef9bf15bba57388220f91a955937f0628d7731ad9fd4e915916d4", "size_in_bytes": 1703}, {"_path": "include/opentelemetry/common/key_value_iterable_view.h", "path_type": "hardlink", "sha256": "a452665866118abfa2d3c7d6d052075456b6974f0672b530f88c29a4dd3085d4", "sha256_in_prefix": "a452665866118abfa2d3c7d6d052075456b6974f0672b530f88c29a4dd3085d4", "size_in_bytes": 4727}, {"_path": "include/opentelemetry/common/kv_properties.h", "path_type": "hardlink", "sha256": "40599c411a542800403cdfc0384db72668f34330dd0f41e0d9f3beb227cf8c53", "sha256_in_prefix": "40599c411a542800403cdfc0384db72668f34330dd0f41e0d9f3beb227cf8c53", "size_in_bytes": 7394}, {"_path": "include/opentelemetry/common/macros.h", "path_type": "hardlink", "sha256": "c96249c972319c856ea83da6c197349f3336822b55c67a466326d878a0ca90ed", "sha256_in_prefix": "c96249c972319c856ea83da6c197349f3336822b55c67a466326d878a0ca90ed", "size_in_bytes": 17220}, {"_path": "include/opentelemetry/common/spin_lock_mutex.h", "path_type": "hardlink", "sha256": "43ca417fc67eb02b1c39a8934f386bb9dd18c2e65ac5ac228db02523bd392168", "sha256_in_prefix": "43ca417fc67eb02b1c39a8934f386bb9dd18c2e65ac5ac228db02523bd392168", "size_in_bytes": 3969}, {"_path": "include/opentelemetry/common/string_util.h", "path_type": "hardlink", "sha256": "cbc2a285da72a07bf9200d759752991287af5cef2361401288b47850bfa5cc6d", "sha256_in_prefix": "cbc2a285da72a07bf9200d759752991287af5cef2361401288b47850bfa5cc6d", "size_in_bytes": 773}, {"_path": "include/opentelemetry/common/timestamp.h", "path_type": "hardlink", "sha256": "127c900bafd0228f1aeaa301b215f99d963e1a2612dad78c0310aa44428ddeed", "sha256_in_prefix": "127c900bafd0228f1aeaa301b215f99d963e1a2612dad78c0310aa44428ddeed", "size_in_bytes": 5995}, {"_path": "include/opentelemetry/config.h", "path_type": "hardlink", "sha256": "bec2931520558e1985e9829221ba883013c2cf2a45014bc7cd8d60448a013907", "sha256_in_prefix": "bec2931520558e1985e9829221ba883013c2cf2a45014bc7cd8d60448a013907", "size_in_bytes": 682}, {"_path": "include/opentelemetry/context/context.h", "path_type": "hardlink", "sha256": "9d327e21eac45dfc96e8049e83f15353eec623887bb2a576147201c5d92a6500", "sha256_in_prefix": "9d327e21eac45dfc96e8049e83f15353eec623887bb2a576147201c5d92a6500", "size_in_bytes": 4819}, {"_path": "include/opentelemetry/context/context_value.h", "path_type": "hardlink", "sha256": "7fddffa6b3b1bc4393b9dd1b6dd884caaaf9c63c2a0cdaa5dfb0981f2bb61277", "sha256_in_prefix": "7fddffa6b3b1bc4393b9dd1b6dd884caaaf9c63c2a0cdaa5dfb0981f2bb61277", "size_in_bytes": 916}, {"_path": "include/opentelemetry/context/propagation/composite_propagator.h", "path_type": "hardlink", "sha256": "468626319b5ffaf278d612411c48151ae697f304177fee21830ecd3ea8265fff", "sha256_in_prefix": "468626319b5ffaf278d612411c48151ae697f304177fee21830ecd3ea8265fff", "size_in_bytes": 2482}, {"_path": "include/opentelemetry/context/propagation/global_propagator.h", "path_type": "hardlink", "sha256": "2abda1b316aec7fecdafacb0481545a47defad92befbf716aa8a80f1a4ef5b43", "sha256_in_prefix": "2abda1b316aec7fecdafacb0481545a47defad92befbf716aa8a80f1a4ef5b43", "size_in_bytes": 1409}, {"_path": "include/opentelemetry/context/propagation/noop_propagator.h", "path_type": "hardlink", "sha256": "05e64bd0cc209433a9ee480e2e526748009147da59586d0e08cd04044c837c58", "sha256_in_prefix": "05e64bd0cc209433a9ee480e2e526748009147da59586d0e08cd04044c837c58", "size_in_bytes": 990}, {"_path": "include/opentelemetry/context/propagation/text_map_propagator.h", "path_type": "hardlink", "sha256": "105c8b07287d127b04ed3d557f8d00bffeec65bbfc332e8256cb144cc33bc157", "sha256_in_prefix": "105c8b07287d127b04ed3d557f8d00bffeec65bbfc332e8256cb144cc33bc157", "size_in_bytes": 2040}, {"_path": "include/opentelemetry/context/runtime_context.h", "path_type": "hardlink", "sha256": "6ca71899ca54a83f5402ed58782d8be847d0acd48e9b31cffbbe88374df85d63", "sha256_in_prefix": "6ca71899ca54a83f5402ed58782d8be847d0acd48e9b31cffbbe88374df85d63", "size_in_bytes": 9621}, {"_path": "include/opentelemetry/detail/preprocessor.h", "path_type": "hardlink", "sha256": "8fd66d130b6d9bb3999484a6c3c3980c7b178455eb68c618dbe6c1f5649c7154", "sha256_in_prefix": "8fd66d130b6d9bb3999484a6c3c3980c7b178455eb68c618dbe6c1f5649c7154", "size_in_bytes": 755}, {"_path": "include/opentelemetry/exporters/memory/in_memory_data.h", "path_type": "hardlink", "sha256": "7986a5b6b4849f79898f1f7f68e0482461ddc57147e1d3ed2eaabfc39ad4e319", "sha256_in_prefix": "7986a5b6b4849f79898f1f7f68e0482461ddc57147e1d3ed2eaabfc39ad4e319", "size_in_bytes": 1714}, {"_path": "include/opentelemetry/exporters/memory/in_memory_metric_data.h", "path_type": "hardlink", "sha256": "7369774c3e1b3fb3a25ec4893bffcc7ecda642da6d93e7925530e041e16f68a2", "sha256_in_prefix": "7369774c3e1b3fb3a25ec4893bffcc7ecda642da6d93e7925530e041e16f68a2", "size_in_bytes": 2414}, {"_path": "include/opentelemetry/exporters/memory/in_memory_metric_exporter_factory.h", "path_type": "hardlink", "sha256": "a0a2b2033bf65afd18a2451b037234a755f393e83e91befc9aade194f4048211", "sha256_in_prefix": "a0a2b2033bf65afd18a2451b037234a755f393e83e91befc9aade194f4048211", "size_in_bytes": 1305}, {"_path": "include/opentelemetry/exporters/memory/in_memory_span_data.h", "path_type": "hardlink", "sha256": "f6fe2238b9b159912b5173d15ee2eb147160ea9600697d4b792096058b20ad5b", "sha256_in_prefix": "f6fe2238b9b159912b5173d15ee2eb147160ea9600697d4b792096058b20ad5b", "size_in_bytes": 855}, {"_path": "include/opentelemetry/exporters/memory/in_memory_span_exporter.h", "path_type": "hardlink", "sha256": "750538cbdf822dedfd56d7da12e39af77959e052897e0ee3aef20f8790790f49", "sha256_in_prefix": "750538cbdf822dedfd56d7da12e39af77959e052897e0ee3aef20f8790790f49", "size_in_bytes": 3124}, {"_path": "include/opentelemetry/exporters/memory/in_memory_span_exporter_factory.h", "path_type": "hardlink", "sha256": "d1c67555227f965ca6aade84a84f3c98c5664a9277a5f06da9530360b6da8071", "sha256_in_prefix": "d1c67555227f965ca6aade84a84f3c98c5664a9277a5f06da9530360b6da8071", "size_in_bytes": 1256}, {"_path": "include/opentelemetry/exporters/ostream/common_utils.h", "path_type": "hardlink", "sha256": "b5bc31fefc9ba90f3ddb51946ef9ce136b9fda1ea25a0f355b5b9831dbba1d05", "sha256_in_prefix": "b5bc31fefc9ba90f3ddb51946ef9ce136b9fda1ea25a0f355b5b9831dbba1d05", "size_in_bytes": 2950}, {"_path": "include/opentelemetry/exporters/ostream/log_record_exporter.h", "path_type": "hardlink", "sha256": "f386ab845e817d63aa685adba427ea0c5eca15c9a3f2daaadf37ee9b0f2c901d", "sha256_in_prefix": "f386ab845e817d63aa685adba427ea0c5eca15c9a3f2daaadf37ee9b0f2c901d", "size_in_bytes": 2446}, {"_path": "include/opentelemetry/exporters/ostream/log_record_exporter_factory.h", "path_type": "hardlink", "sha256": "93c45ae88fc9fe09e41c3231c4fe78ce92a21055762d72f605232b2018047177", "sha256_in_prefix": "93c45ae88fc9fe09e41c3231c4fe78ce92a21055762d72f605232b2018047177", "size_in_bytes": 833}, {"_path": "include/opentelemetry/exporters/ostream/metric_exporter.h", "path_type": "hardlink", "sha256": "29ca31eeb33b652a28490d388f3d8fad57ffc2a15e90d55c59f88db0eae0bc32", "sha256_in_prefix": "29ca31eeb33b652a28490d388f3d8fad57ffc2a15e90d55c59f88db0eae0bc32", "size_in_bytes": 3009}, {"_path": "include/opentelemetry/exporters/ostream/metric_exporter_factory.h", "path_type": "hardlink", "sha256": "b875c66d26667ff2751066bf3225aaaeb19f14b301c816e47fb06d99a3d6084c", "sha256_in_prefix": "b875c66d26667ff2751066bf3225aaaeb19f14b301c816e47fb06d99a3d6084c", "size_in_bytes": 1086}, {"_path": "include/opentelemetry/exporters/ostream/span_exporter.h", "path_type": "hardlink", "sha256": "169e347f9a01eee35f28b20ff9f63075cf413412a15c1e88ff75cf219cd4acb1", "sha256_in_prefix": "169e347f9a01eee35f28b20ff9f63075cf413412a15c1e88ff75cf219cd4acb1", "size_in_bytes": 2813}, {"_path": "include/opentelemetry/exporters/ostream/span_exporter_factory.h", "path_type": "hardlink", "sha256": "e9f9a8e9e0d3562fd28b8fad135d0945867d7e6310887c8398f1ff0829bc4197", "sha256_in_prefix": "e9f9a8e9e0d3562fd28b8fad135d0945867d7e6310887c8398f1ff0829bc4197", "size_in_bytes": 808}, {"_path": "include/opentelemetry/exporters/otlp/otlp_environment.h", "path_type": "hardlink", "sha256": "2c614548a3047bcb248d8b3a12ad806578bcf1cc52cf37b32c6d5a70bf75db5d", "sha256_in_prefix": "2c614548a3047bcb248d8b3a12ad806578bcf1cc52cf37b32c6d5a70bf75db5d", "size_in_bytes": 5390}, {"_path": "include/opentelemetry/exporters/otlp/otlp_grpc_client.h", "path_type": "hardlink", "sha256": "bcc7678b64d4023fe9b73b550dec686e17438d0a8f0d0440721d8784bf0eb905", "sha256_in_prefix": "bcc7678b64d4023fe9b73b550dec686e17438d0a8f0d0440721d8784bf0eb905", "size_in_bytes": 8250}, {"_path": "include/opentelemetry/exporters/otlp/otlp_grpc_client_factory.h", "path_type": "hardlink", "sha256": "6a416b602eca19810088e0eb6c441e62eaf57abd46c366ccf7967b84371f6c5e", "sha256_in_prefix": "6a416b602eca19810088e0eb6c441e62eaf57abd46c366ccf7967b84371f6c5e", "size_in_bytes": 896}, {"_path": "include/opentelemetry/exporters/otlp/otlp_grpc_client_options.h", "path_type": "hardlink", "sha256": "f62dbc1c960fc2b7ac69d087c02dd9cbbdb8db36d632c3ecf3aae55a3cfe63c0", "sha256_in_prefix": "f62dbc1c960fc2b7ac69d087c02dd9cbbdb8db36d632c3ecf3aae55a3cfe63c0", "size_in_bytes": 1985}, {"_path": "include/opentelemetry/exporters/otlp/otlp_grpc_exporter.h", "path_type": "hardlink", "sha256": "bc3884626535f9f2cd88240c1906379e9d622b73fd59076199cc334dcc39b63f", "sha256_in_prefix": "bc3884626535f9f2cd88240c1906379e9d622b73fd59076199cc334dcc39b63f", "size_in_bytes": 4045}, {"_path": "include/opentelemetry/exporters/otlp/otlp_grpc_exporter_factory.h", "path_type": "hardlink", "sha256": "398c2a964d3c0e8ea984912ce2ee190c57ed32f20b4b71bd2163999dcdedefeb", "sha256_in_prefix": "398c2a964d3c0e8ea984912ce2ee190c57ed32f20b4b71bd2163999dcdedefeb", "size_in_bytes": 1148}, {"_path": "include/opentelemetry/exporters/otlp/otlp_grpc_exporter_options.h", "path_type": "hardlink", "sha256": "792d9f0163fd3c9dee9005bf91074e84612c66d1b393c90b57b5dafa078963b7", "sha256_in_prefix": "792d9f0163fd3c9dee9005bf91074e84612c66d1b393c90b57b5dafa078963b7", "size_in_bytes": 776}, {"_path": "include/opentelemetry/exporters/otlp/otlp_grpc_log_record_exporter.h", "path_type": "hardlink", "sha256": "356de9d09eb871b570a2261a772058b6181e7ff87d4667bf9c52a3dd223b1ae9", "sha256_in_prefix": "356de9d09eb871b570a2261a772058b6181e7ff87d4667bf9c52a3dd223b1ae9", "size_in_bytes": 4288}, {"_path": "include/opentelemetry/exporters/otlp/otlp_grpc_log_record_exporter_factory.h", "path_type": "hardlink", "sha256": "f2766cefddb72b592b22f8945383e120a4218c0b3cd772706cd774fa59c732e4", "sha256_in_prefix": "f2766cefddb72b592b22f8945383e120a4218c0b3cd772706cd774fa59c732e4", "size_in_bytes": 1185}, {"_path": "include/opentelemetry/exporters/otlp/otlp_grpc_log_record_exporter_options.h", "path_type": "hardlink", "sha256": "7dceedacce20fcc7d32085dd7703d9335fbbe7b427477e54c4c1a5bd1b01d738", "sha256_in_prefix": "7dceedacce20fcc7d32085dd7703d9335fbbe7b427477e54c4c1a5bd1b01d738", "size_in_bytes": 772}, {"_path": "include/opentelemetry/exporters/otlp/otlp_grpc_metric_exporter.h", "path_type": "hardlink", "sha256": "747e6ae4ca61d0d36754e82f9690dadcf01d4de0ee452ae6272a882edb2a8870", "sha256_in_prefix": "747e6ae4ca61d0d36754e82f9690dadcf01d4de0ee452ae6272a882edb2a8870", "size_in_bytes": 3841}, {"_path": "include/opentelemetry/exporters/otlp/otlp_grpc_metric_exporter_factory.h", "path_type": "hardlink", "sha256": "313ce5cf2cc93ae25dd0844c0c5da47d6e8ddc765951d3dc4db508581390c266", "sha256_in_prefix": "313ce5cf2cc93ae25dd0844c0c5da47d6e8ddc765951d3dc4db508581390c266", "size_in_bytes": 1206}, {"_path": "include/opentelemetry/exporters/otlp/otlp_grpc_metric_exporter_options.h", "path_type": "hardlink", "sha256": "7c8437958499fa9f6317d98c6e1ed4cfc232d6bf76015576e01e7bc7cef52ec7", "sha256_in_prefix": "7c8437958499fa9f6317d98c6e1ed4cfc232d6bf76015576e01e7bc7cef52ec7", "size_in_bytes": 968}, {"_path": "include/opentelemetry/exporters/otlp/otlp_grpc_utils.h", "path_type": "hardlink", "sha256": "e048442e1071b7c448ae3945956e9a43a2b8edc53f21f8a61ebe09991719d5fa", "sha256_in_prefix": "e048442e1071b7c448ae3945956e9a43a2b8edc53f21f8a61ebe09991719d5fa", "size_in_bytes": 433}, {"_path": "include/opentelemetry/exporters/otlp/otlp_http.h", "path_type": "hardlink", "sha256": "95c3af685ea94d0c78b7729a8f4772941316a8f3ba5563af2bc5c4b3adb7b91f", "sha256_in_prefix": "95c3af685ea94d0c78b7729a8f4772941316a8f3ba5563af2bc5c4b3adb7b91f", "size_in_bytes": 554}, {"_path": "include/opentelemetry/exporters/otlp/otlp_http_client.h", "path_type": "hardlink", "sha256": "3fd9c37fb25323654f7ff73e9eb4f1eb8b2e424b312d0169c1f21e3f706288b8", "sha256_in_prefix": "3fd9c37fb25323654f7ff73e9eb4f1eb8b2e424b312d0169c1f21e3f706288b8", "size_in_bytes": 11167}, {"_path": "include/opentelemetry/exporters/otlp/otlp_http_exporter.h", "path_type": "hardlink", "sha256": "333efc730de3a0bc6a37e9d468d3f1cca28f3da4ef0201726139718d51e4e8d9", "sha256_in_prefix": "333efc730de3a0bc6a37e9d468d3f1cca28f3da4ef0201726139718d51e4e8d9", "size_in_bytes": 3023}, {"_path": "include/opentelemetry/exporters/otlp/otlp_http_exporter_factory.h", "path_type": "hardlink", "sha256": "3e640bf7dc16dec3e46304b7221e02813725128d2ee7f450b38d47ea9ff2005d", "sha256_in_prefix": "3e640bf7dc16dec3e46304b7221e02813725128d2ee7f450b38d47ea9ff2005d", "size_in_bytes": 1185}, {"_path": "include/opentelemetry/exporters/otlp/otlp_http_exporter_options.h", "path_type": "hardlink", "sha256": "460e0ad77be386fbe7b040b20daa83357586559843686e4ad2a7bfa1378308c3", "sha256_in_prefix": "460e0ad77be386fbe7b040b20daa83357586559843686e4ad2a7bfa1378308c3", "size_in_bytes": 2965}, {"_path": "include/opentelemetry/exporters/otlp/otlp_http_exporter_runtime_options.h", "path_type": "hardlink", "sha256": "bb3112f34bfe1ee99177cade3602e59b2c4279ac6e75f0ce599b1c7cfcbe00e3", "sha256_in_prefix": "bb3112f34bfe1ee99177cade3602e59b2c4279ac6e75f0ce599b1c7cfcbe00e3", "size_in_bytes": 728}, {"_path": "include/opentelemetry/exporters/otlp/otlp_http_log_record_exporter.h", "path_type": "hardlink", "sha256": "e2620bd0f949b4332b3ab7d046ce801e0404522ef5a6d56b9d30eabf91919614", "sha256_in_prefix": "e2620bd0f949b4332b3ab7d046ce801e0404522ef5a6d56b9d30eabf91919614", "size_in_bytes": 3428}, {"_path": "include/opentelemetry/exporters/otlp/otlp_http_log_record_exporter_factory.h", "path_type": "hardlink", "sha256": "a5aadfee019bcda4f1773cc85124da800f4cd9a096a944b33f041ebaf1540261", "sha256_in_prefix": "a5aadfee019bcda4f1773cc85124da800f4cd9a096a944b33f041ebaf1540261", "size_in_bytes": 1213}, {"_path": "include/opentelemetry/exporters/otlp/otlp_http_log_record_exporter_options.h", "path_type": "hardlink", "sha256": "003365fb7f0aa631fe78742f192a2170172ae0b30e53f322242796a2e9d49218", "sha256_in_prefix": "003365fb7f0aa631fe78742f192a2170172ae0b30e53f322242796a2e9d49218", "size_in_bytes": 2996}, {"_path": "include/opentelemetry/exporters/otlp/otlp_http_log_record_exporter_runtime_options.h", "path_type": "hardlink", "sha256": "791bc784db2589aaa584509463243667ba76185d0d178fe7dd2b3e74c25214a9", "sha256_in_prefix": "791bc784db2589aaa584509463243667ba76185d0d178fe7dd2b3e74c25214a9", "size_in_bytes": 759}, {"_path": "include/opentelemetry/exporters/otlp/otlp_http_metric_exporter.h", "path_type": "hardlink", "sha256": "8a9ced5c4949506e427eddf90c1af6b7b4528ba41021f16fb76163b6ac412ac0", "sha256_in_prefix": "8a9ced5c4949506e427eddf90c1af6b7b4528ba41021f16fb76163b6ac412ac0", "size_in_bytes": 3107}, {"_path": "include/opentelemetry/exporters/otlp/otlp_http_metric_exporter_factory.h", "path_type": "hardlink", "sha256": "574864b8e15db896fcca9ecb28145b4b1787c65ae7c8596eb5b61ceb5c61cf4c", "sha256_in_prefix": "574864b8e15db896fcca9ecb28145b4b1787c65ae7c8596eb5b61ceb5c61cf4c", "size_in_bytes": 1208}, {"_path": "include/opentelemetry/exporters/otlp/otlp_http_metric_exporter_options.h", "path_type": "hardlink", "sha256": "d9d729a97cd600239b7581fcb4f91ed39306ef36af9ac50ba0794a7879f9f99b", "sha256_in_prefix": "d9d729a97cd600239b7581fcb4f91ed39306ef36af9ac50ba0794a7879f9f99b", "size_in_bytes": 3113}, {"_path": "include/opentelemetry/exporters/otlp/otlp_http_metric_exporter_runtime_options.h", "path_type": "hardlink", "sha256": "278b83a5d4056b707ef4a22d9bbb203a759df524bb4335b242d4087ea9f46bdb", "sha256_in_prefix": "278b83a5d4056b707ef4a22d9bbb203a759df524bb4335b242d4087ea9f46bdb", "size_in_bytes": 747}, {"_path": "include/opentelemetry/exporters/otlp/otlp_log_recordable.h", "path_type": "hardlink", "sha256": "d6e6d769d89c874b0c667b94815d144db3d8cec0de378ed853bd9cb880c44127", "sha256_in_prefix": "d6e6d769d89c874b0c667b94815d144db3d8cec0de378ed853bd9cb880c44127", "size_in_bytes": 4138}, {"_path": "include/opentelemetry/exporters/otlp/otlp_metric_utils.h", "path_type": "hardlink", "sha256": "deb1760be9ac06c18b151ebb5da821a2c0ebf9770f1e70a01133b6e2170818d5", "sha256_in_prefix": "deb1760be9ac06c18b151ebb5da821a2c0ebf9770f1e70a01133b6e2170818d5", "size_in_bytes": 3297}, {"_path": "include/opentelemetry/exporters/otlp/otlp_populate_attribute_utils.h", "path_type": "hardlink", "sha256": "f6d8c216ca49f4eabeea15aad60d13c2d9269d6a534de0d9ea1eaa1757f0bed9", "sha256_in_prefix": "f6d8c216ca49f4eabeea15aad60d13c2d9269d6a534de0d9ea1eaa1757f0bed9", "size_in_bytes": 2235}, {"_path": "include/opentelemetry/exporters/otlp/otlp_preferred_temporality.h", "path_type": "hardlink", "sha256": "c632446673eb77532eca6e883cfcdbdc972a7b2c0998f7f46506d70886fe9de4", "sha256_in_prefix": "c632446673eb77532eca6e883cfcdbdc972a7b2c0998f7f46506d70886fe9de4", "size_in_bytes": 356}, {"_path": "include/opentelemetry/exporters/otlp/otlp_recordable.h", "path_type": "hardlink", "sha256": "0909890c23996ccde59c56c0ae46a0150913415a763cfb0883ca4f2b5ba81dbd", "sha256_in_prefix": "0909890c23996ccde59c56c0ae46a0150913415a763cfb0883ca4f2b5ba81dbd", "size_in_bytes": 3699}, {"_path": "include/opentelemetry/exporters/otlp/otlp_recordable_utils.h", "path_type": "hardlink", "sha256": "2a5dc356cec7cc5359c9003467fab9d05ff5c6ea067a5667c403c6da30be113d", "sha256_in_prefix": "2a5dc356cec7cc5359c9003467fab9d05ff5c6ea067a5667c403c6da30be113d", "size_in_bytes": 1265}, {"_path": "include/opentelemetry/exporters/otlp/protobuf_include_prefix.h", "path_type": "hardlink", "sha256": "14feb1b9b09ae990cf802a3729d0fc81ece933266ef4639e10b9930512d28d96", "sha256_in_prefix": "14feb1b9b09ae990cf802a3729d0fc81ece933266ef4639e10b9930512d28d96", "size_in_bytes": 2467}, {"_path": "include/opentelemetry/exporters/otlp/protobuf_include_suffix.h", "path_type": "hardlink", "sha256": "e15309724d83aa59b82dd176df1d2ac29b0de96e7055efd25bb2f59a24a2c905", "sha256_in_prefix": "e15309724d83aa59b82dd176df1d2ac29b0de96e7055efd25bb2f59a24a2c905", "size_in_bytes": 479}, {"_path": "include/opentelemetry/exporters/prometheus/collector.h", "path_type": "hardlink", "sha256": "96ee642cfe053c1e94ffe0c5ca5f1e5fa5dc5941cf6e39b6fbf8a11e394b111f", "sha256_in_prefix": "96ee642cfe053c1e94ffe0c5ca5f1e5fa5dc5941cf6e39b6fbf8a11e394b111f", "size_in_bytes": 1427}, {"_path": "include/opentelemetry/exporters/prometheus/exporter.h", "path_type": "hardlink", "sha256": "e22230b70650fe9860c46c91d990b2baf912b6049227b2e8e486fb9c8b820e47", "sha256_in_prefix": "e22230b70650fe9860c46c91d990b2baf912b6049227b2e8e486fb9c8b820e47", "size_in_bytes": 1775}, {"_path": "include/opentelemetry/exporters/prometheus/exporter_factory.h", "path_type": "hardlink", "sha256": "6f5b8b3de1ea4bbc183c9979eeeb44f9c21c2230d2fbc4d310dab0af5482a928", "sha256_in_prefix": "6f5b8b3de1ea4bbc183c9979eeeb44f9c21c2230d2fbc4d310dab0af5482a928", "size_in_bytes": 660}, {"_path": "include/opentelemetry/exporters/prometheus/exporter_options.h", "path_type": "hardlink", "sha256": "e50889686d384ce2ad225464efff084e5418e50304031d8e8365455cf7b1a30f", "sha256_in_prefix": "e50889686d384ce2ad225464efff084e5418e50304031d8e8365455cf7b1a30f", "size_in_bytes": 846}, {"_path": "include/opentelemetry/exporters/prometheus/exporter_utils.h", "path_type": "hardlink", "sha256": "a930f034af61c983af3dfcfa324c81d9d5668622de29cd0cf85fb4645b7e197e", "sha256_in_prefix": "a930f034af61c983af3dfcfa324c81d9d5668622de29cd0cf85fb4645b7e197e", "size_in_bytes": 9454}, {"_path": "include/opentelemetry/exporters/zipkin/zipkin_exporter.h", "path_type": "hardlink", "sha256": "4f01be327179ef2ca20c5c3b0aadfc15a9a91c3e0d59eeb6f51548a8e1bc4b33", "sha256_in_prefix": "4f01be327179ef2ca20c5c3b0aadfc15a9a91c3e0d59eeb6f51548a8e1bc4b33", "size_in_bytes": 2842}, {"_path": "include/opentelemetry/exporters/zipkin/zipkin_exporter_factory.h", "path_type": "hardlink", "sha256": "e9fb2d467735b5cc17682c354eaa3522d57a4e4224f4864a67cccddceb7a28da", "sha256_in_prefix": "e9fb2d467735b5cc17682c354eaa3522d57a4e4224f4864a67cccddceb7a28da", "size_in_bytes": 826}, {"_path": "include/opentelemetry/exporters/zipkin/zipkin_exporter_options.h", "path_type": "hardlink", "sha256": "7b3adc3f83871d398acf67b9c0d64a9b74216b8fd26897c9a85e64ed2120f3fc", "sha256_in_prefix": "7b3adc3f83871d398acf67b9c0d64a9b74216b8fd26897c9a85e64ed2120f3fc", "size_in_bytes": 1352}, {"_path": "include/opentelemetry/ext/http/client/curl/http_client_curl.h", "path_type": "hardlink", "sha256": "7f09ca5acc520608ff45fd965f8dbcd639e86d40349aa3687818cb1f4c42c0a4", "sha256_in_prefix": "7f09ca5acc520608ff45fd965f8dbcd639e86d40349aa3687818cb1f4c42c0a4", "size_in_bytes": 12221}, {"_path": "include/opentelemetry/ext/http/client/curl/http_operation_curl.h", "path_type": "hardlink", "sha256": "2e38b1927b1592994cf5082b01674473cf41bdaa219f7054997ff36b6855f915", "sha256_in_prefix": "2e38b1927b1592994cf5082b01674473cf41bdaa219f7054997ff36b6855f915", "size_in_bytes": 10347}, {"_path": "include/opentelemetry/ext/http/client/http_client.h", "path_type": "hardlink", "sha256": "f42bec6e62c3c6d29bca4ace9e844fbbc6ab70830d9da1c4c2eb1aa4de57fd74", "sha256_in_prefix": "f42bec6e62c3c6d29bca4ace9e844fbbc6ab70830d9da1c4c2eb1aa4de57fd74", "size_in_bytes": 11313}, {"_path": "include/opentelemetry/ext/http/client/http_client_factory.h", "path_type": "hardlink", "sha256": "0a132de4fe381dfdfdb6ab9b7c6e783fb5b18112efbfed7f70a509c39610c174", "sha256_in_prefix": "0a132de4fe381dfdfdb6ab9b7c6e783fb5b18112efbfed7f70a509c39610c174", "size_in_bytes": 694}, {"_path": "include/opentelemetry/ext/http/common/url_parser.h", "path_type": "hardlink", "sha256": "05203a4b2e5e1f95c407879a12cca3cc4d5be0c1769f23f646ac08d685e14e14", "sha256_in_prefix": "05203a4b2e5e1f95c407879a12cca3cc4d5be0c1769f23f646ac08d685e14e14", "size_in_bytes": 4924}, {"_path": "include/opentelemetry/ext/http/server/file_http_server.h", "path_type": "hardlink", "sha256": "e7fd48444b6884b622123d0b39be4fd1bcaa116281a7ce13966fd9949f391147", "sha256_in_prefix": "e7fd48444b6884b622123d0b39be4fd1bcaa116281a7ce13966fd9949f391147", "size_in_bytes": 5167}, {"_path": "include/opentelemetry/ext/http/server/http_server.h", "path_type": "hardlink", "sha256": "f141ce9e87dc414ecc25856214eafa0ad346171f48cc15a46a8570cac17f90d9", "sha256_in_prefix": "f141ce9e87dc414ecc25856214eafa0ad346171f48cc15a46a8570cac17f90d9", "size_in_bytes": 23302}, {"_path": "include/opentelemetry/ext/http/server/socket_tools.h", "path_type": "hardlink", "sha256": "6eaa6fd40a09ca04e38bf74688fe3b66be14a437c30f7a77523bab988173aef6", "sha256_in_prefix": "6eaa6fd40a09ca04e38bf74688fe3b66be14a437c30f7a77523bab988173aef6", "size_in_bytes": 20608}, {"_path": "include/opentelemetry/logs/event_id.h", "path_type": "hardlink", "sha256": "5086065abaee86f37cd82a57b728ca678f2785f763ff0ff9fcac9b2f0b5850e5", "sha256_in_prefix": "5086065abaee86f37cd82a57b728ca678f2785f763ff0ff9fcac9b2f0b5850e5", "size_in_bytes": 797}, {"_path": "include/opentelemetry/logs/event_logger.h", "path_type": "hardlink", "sha256": "71b53c6de43d8af8b4127c3e5a1ee029be0e908d87d84610bd1b5162943813b9", "sha256_in_prefix": "71b53c6de43d8af8b4127c3e5a1ee029be0e908d87d84610bd1b5162943813b9", "size_in_bytes": 2733}, {"_path": "include/opentelemetry/logs/event_logger_provider.h", "path_type": "hardlink", "sha256": "345f4a8d8b19e474c9bcbf9d14501981a3aa54c56df9bd2e3e17b1a271531e9c", "sha256_in_prefix": "345f4a8d8b19e474c9bcbf9d14501981a3aa54c56df9bd2e3e17b1a271531e9c", "size_in_bytes": 771}, {"_path": "include/opentelemetry/logs/log_record.h", "path_type": "hardlink", "sha256": "4908c5bc8c3b5a4ff0c294eb48183c15e91ff66a7676478d1876d7a56242ab82", "sha256_in_prefix": "4908c5bc8c3b5a4ff0c294eb48183c15e91ff66a7676478d1876d7a56242ab82", "size_in_bytes": 2340}, {"_path": "include/opentelemetry/logs/logger.h", "path_type": "hardlink", "sha256": "3b28797aaaaecd322c2623d4c5339411a021c65c62dc74d5175d32fad555d87c", "sha256_in_prefix": "3b28797aaaaecd322c2623d4c5339411a021c65c62dc74d5175d32fad555d87c", "size_in_bytes": 18485}, {"_path": "include/opentelemetry/logs/logger_provider.h", "path_type": "hardlink", "sha256": "d84a728be8550f62c706320b412e76078150529a96702ae81b3bbaaf1476c33b", "sha256_in_prefix": "d84a728be8550f62c706320b412e76078150529a96702ae81b3bbaaf1476c33b", "size_in_bytes": 2424}, {"_path": "include/opentelemetry/logs/logger_type_traits.h", "path_type": "hardlink", "sha256": "a5e47dbf13b30fec812963b0f35a2a1838f62e4d74d5cda78e271f721dd672f4", "sha256_in_prefix": "a5e47dbf13b30fec812963b0f35a2a1838f62e4d74d5cda78e271f721dd672f4", "size_in_bytes": 5698}, {"_path": "include/opentelemetry/logs/noop.h", "path_type": "hardlink", "sha256": "179043262d4c78b8e390d9e78b5021ec5ff395c25c90d2503db4eab905e97a45", "sha256_in_prefix": "179043262d4c78b8e390d9e78b5021ec5ff395c25c90d2503db4eab905e97a45", "size_in_bytes": 4253}, {"_path": "include/opentelemetry/logs/provider.h", "path_type": "hardlink", "sha256": "22406d5e7117e8665928d04e1123224d5e4e7361e353390e9b1429735680e2bc", "sha256_in_prefix": "22406d5e7117e8665928d04e1123224d5e4e7361e353390e9b1429735680e2bc", "size_in_bytes": 2637}, {"_path": "include/opentelemetry/logs/severity.h", "path_type": "hardlink", "sha256": "52e6aee6495e605f7d15d53a6860a00aa0d5c40e7dca92cad940f81d3da81481", "sha256_in_prefix": "52e6aee6495e605f7d15d53a6860a00aa0d5c40e7dca92cad940f81d3da81481", "size_in_bytes": 1696}, {"_path": "include/opentelemetry/metrics/async_instruments.h", "path_type": "hardlink", "sha256": "bfb7966e7e2346234fd4b016035bdbf33e8aca7dcc5de22491c9ea3d7f5bedf8", "sha256_in_prefix": "bfb7966e7e2346234fd4b016035bdbf33e8aca7dcc5de22491c9ea3d7f5bedf8", "size_in_bytes": 848}, {"_path": "include/opentelemetry/metrics/meter.h", "path_type": "hardlink", "sha256": "566da9618cff4ed4f9a338d2b6d261984c97dde97aa29a373cd4b49ddf1d8152", "sha256_in_prefix": "566da9618cff4ed4f9a338d2b6d261984c97dde97aa29a373cd4b49ddf1d8152", "size_in_bytes": 6570}, {"_path": "include/opentelemetry/metrics/meter_provider.h", "path_type": "hardlink", "sha256": "ef94c84bd2a17cf7f5e33b1fc172ac1313c57533a213ea1d454527053428ef3a", "sha256_in_prefix": "ef94c84bd2a17cf7f5e33b1fc172ac1313c57533a213ea1d454527053428ef3a", "size_in_bytes": 4948}, {"_path": "include/opentelemetry/metrics/noop.h", "path_type": "hardlink", "sha256": "3c5d5c3b3ff8291810b1cfdff26f2280b892ea9a5351087eb746b77e36de67c8", "sha256_in_prefix": "3c5d5c3b3ff8291810b1cfdff26f2280b892ea9a5351087eb746b77e36de67c8", "size_in_bytes": 9478}, {"_path": "include/opentelemetry/metrics/observer_result.h", "path_type": "hardlink", "sha256": "36dc05224a8a9616972bc2b486677bb059d0532b60d42e2e915b8d52a0a7dd9d", "sha256_in_prefix": "36dc05224a8a9616972bc2b486677bb059d0532b60d42e2e915b8d52a0a7dd9d", "size_in_bytes": 1640}, {"_path": "include/opentelemetry/metrics/provider.h", "path_type": "hardlink", "sha256": "90fbce524d49566e2aa7427765196cd0d90547acbb2946f1f4781aa7580a924e", "sha256_in_prefix": "90fbce524d49566e2aa7427765196cd0d90547acbb2946f1f4781aa7580a924e", "size_in_bytes": 1466}, {"_path": "include/opentelemetry/metrics/sync_instruments.h", "path_type": "hardlink", "sha256": "1ac44ca458b5b5231dd94000c1c5031cdb91892c3901ef249c7a8f6c67fc46d9", "sha256_in_prefix": "1ac44ca458b5b5231dd94000c1c5031cdb91892c3901ef249c7a8f6c67fc46d9", "size_in_bytes": 10815}, {"_path": "include/opentelemetry/nostd/detail/all.h", "path_type": "hardlink", "sha256": "2febc12636c6ae3f1db1b83e64d3f494ff5cc87b964ddc44d7fa3a95f3843870", "sha256_in_prefix": "2febc12636c6ae3f1db1b83e64d3f494ff5cc87b964ddc44d7fa3a95f3843870", "size_in_bytes": 457}, {"_path": "include/opentelemetry/nostd/detail/decay.h", "path_type": "hardlink", "sha256": "aa79d555b46d159fbc627780bcf4a2a5c3d66319c3092637189ef155b43d8893", "sha256_in_prefix": "aa79d555b46d159fbc627780bcf4a2a5c3d66319c3092637189ef155b43d8893", "size_in_bytes": 316}, {"_path": "include/opentelemetry/nostd/detail/dependent_type.h", "path_type": "hardlink", "sha256": "87d30479f964c89993c08b54d4dfded796b5d5e218c1befa844c946a8fc4c8b5", "sha256_in_prefix": "87d30479f964c89993c08b54d4dfded796b5d5e218c1befa844c946a8fc4c8b5", "size_in_bytes": 351}, {"_path": "include/opentelemetry/nostd/detail/functional.h", "path_type": "hardlink", "sha256": "e9431d68fe8a8544657d7a3608f3626ed703aa211940a167f1b290019cc0ef61", "sha256_in_prefix": "e9431d68fe8a8544657d7a3608f3626ed703aa211940a167f1b290019cc0ef61", "size_in_bytes": 1774}, {"_path": "include/opentelemetry/nostd/detail/invoke.h", "path_type": "hardlink", "sha256": "1b91c0cbfb2944f48d2b97bdf73f5fef326dc075d5783ca181869dbfe8957df1", "sha256_in_prefix": "1b91c0cbfb2944f48d2b97bdf73f5fef326dc075d5783ca181869dbfe8957df1", "size_in_bytes": 5039}, {"_path": "include/opentelemetry/nostd/detail/trait.h", "path_type": "hardlink", "sha256": "d98d739978f8f9117fefcc714265547eb03d223a25ae2685eb58f1c27a1732e2", "sha256_in_prefix": "d98d739978f8f9117fefcc714265547eb03d223a25ae2685eb58f1c27a1732e2", "size_in_bytes": 2152}, {"_path": "include/opentelemetry/nostd/detail/type_pack_element.h", "path_type": "hardlink", "sha256": "5451dbae39e6cc8f663d4b67311b61c74364592e29e5c83b2c184152d92db046", "sha256_in_prefix": "5451dbae39e6cc8f663d4b67311b61c74364592e29e5c83b2c184152d92db046", "size_in_bytes": 1208}, {"_path": "include/opentelemetry/nostd/detail/valueless.h", "path_type": "hardlink", "sha256": "b89cadab0a5b0299adb8f7269664338aaf505414a6b3a7784c08f1761b797f9b", "sha256_in_prefix": "b89cadab0a5b0299adb8f7269664338aaf505414a6b3a7784c08f1761b797f9b", "size_in_bytes": 250}, {"_path": "include/opentelemetry/nostd/detail/variant_alternative.h", "path_type": "hardlink", "sha256": "0cf06c4e2439dccba842d8113738862e7d7d91610e3255ee708c4af9d9e35451", "sha256_in_prefix": "0cf06c4e2439dccba842d8113738862e7d7d91610e3255ee708c4af9d9e35451", "size_in_bytes": 1167}, {"_path": "include/opentelemetry/nostd/detail/variant_fwd.h", "path_type": "hardlink", "sha256": "294f16f4adc23d8ceace4e72be365b7966e39139a426d68bdbd28e703dea66b6", "sha256_in_prefix": "294f16f4adc23d8ceace4e72be365b7966e39139a426d68bdbd28e703dea66b6", "size_in_bytes": 268}, {"_path": "include/opentelemetry/nostd/detail/variant_size.h", "path_type": "hardlink", "sha256": "08210ec3d0b52e7f9041add86331039f480170874e20f8b503fd10926d242574", "sha256_in_prefix": "08210ec3d0b52e7f9041add86331039f480170874e20f8b503fd10926d242574", "size_in_bytes": 695}, {"_path": "include/opentelemetry/nostd/detail/void.h", "path_type": "hardlink", "sha256": "aff15271fa5f527b8262271e9d0017888c59b5337d18c56abe4029354e3bf1c2", "sha256_in_prefix": "aff15271fa5f527b8262271e9d0017888c59b5337d18c56abe4029354e3bf1c2", "size_in_bytes": 516}, {"_path": "include/opentelemetry/nostd/function_ref.h", "path_type": "hardlink", "sha256": "d3623d63e1d8858c2be26fb7149ed7664b5fa0f5896e540886ab59f15da2fd13", "sha256_in_prefix": "d3623d63e1d8858c2be26fb7149ed7664b5fa0f5896e540886ab59f15da2fd13", "size_in_bytes": 2627}, {"_path": "include/opentelemetry/nostd/internal/absl/base/attributes.h", "path_type": "hardlink", "sha256": "cf8ab888548572d4fa8cb98b56b03d6c9653e9b89ffd38cb310209fd7eea05ad", "sha256_in_prefix": "cf8ab888548572d4fa8cb98b56b03d6c9653e9b89ffd38cb310209fd7eea05ad", "size_in_bytes": 24884}, {"_path": "include/opentelemetry/nostd/internal/absl/base/config.h", "path_type": "hardlink", "sha256": "c80f3e6c721b85a7616967daa9da690f0ebe2a888e9c9b613559287ca314a017", "sha256_in_prefix": "c80f3e6c721b85a7616967daa9da690f0ebe2a888e9c9b613559287ca314a017", "size_in_bytes": 25914}, {"_path": "include/opentelemetry/nostd/internal/absl/base/internal/identity.h", "path_type": "hardlink", "sha256": "5a575af60c67ac888f6e9dba65f0bde9faf65c2c0dd978700245fd902917f4d6", "sha256_in_prefix": "5a575af60c67ac888f6e9dba65f0bde9faf65c2c0dd978700245fd902917f4d6", "size_in_bytes": 1015}, {"_path": "include/opentelemetry/nostd/internal/absl/base/internal/inline_variable.h", "path_type": "hardlink", "sha256": "9ee2727c05e0def4bb4ed0f6b3d400b611f6747d01d1e68bda280e8be25a1269", "sha256_in_prefix": "9ee2727c05e0def4bb4ed0f6b3d400b611f6747d01d1e68bda280e8be25a1269", "size_in_bytes": 4860}, {"_path": "include/opentelemetry/nostd/internal/absl/base/internal/invoke.h", "path_type": "hardlink", "sha256": "f4138f74b10cc1bd96c0a5d2efa2da52896184857b24d4cf6160a4192ebe3c17", "sha256_in_prefix": "f4138f74b10cc1bd96c0a5d2efa2da52896184857b24d4cf6160a4192ebe3c17", "size_in_bytes": 7846}, {"_path": "include/opentelemetry/nostd/internal/absl/base/macros.h", "path_type": "hardlink", "sha256": "65b6312c896db2c209a117276690f805575be15e49d79efbb580abe997791eca", "sha256_in_prefix": "65b6312c896db2c209a117276690f805575be15e49d79efbb580abe997791eca", "size_in_bytes": 8089}, {"_path": "include/opentelemetry/nostd/internal/absl/base/optimization.h", "path_type": "hardlink", "sha256": "a0ef3891475a22c1103def334a2d59c45215893f375f6a6270a3da08e2fc5b1e", "sha256_in_prefix": "a0ef3891475a22c1103def334a2d59c45215893f375f6a6270a3da08e2fc5b1e", "size_in_bytes": 7049}, {"_path": "include/opentelemetry/nostd/internal/absl/base/options.h", "path_type": "hardlink", "sha256": "68ed34b3e4a26adf9ace9f775add644e817b2afe3dd0afe505795e4947bcf80e", "sha256_in_prefix": "68ed34b3e4a26adf9ace9f775add644e817b2afe3dd0afe505795e4947bcf80e", "size_in_bytes": 10615}, {"_path": "include/opentelemetry/nostd/internal/absl/base/policy_checks.h", "path_type": "hardlink", "sha256": "b99f825175920daeb6c618daa758ff280d0b5a321869cb8baf8e4fe46bfcbe2a", "sha256_in_prefix": "b99f825175920daeb6c618daa758ff280d0b5a321869cb8baf8e4fe46bfcbe2a", "size_in_bytes": 4361}, {"_path": "include/opentelemetry/nostd/internal/absl/base/port.h", "path_type": "hardlink", "sha256": "d7ffad881437c5fc01b0e65ad56aab0b49470b9313c7807035181957b9122593", "sha256_in_prefix": "d7ffad881437c5fc01b0e65ad56aab0b49470b9313c7807035181957b9122593", "size_in_bytes": 913}, {"_path": "include/opentelemetry/nostd/internal/absl/meta/type_traits.h", "path_type": "hardlink", "sha256": "8b8c0932ce5afb140a5b3866bba9d3dba4fc9a58ec7ac9b59a805674e2ae7d47", "sha256_in_prefix": "8b8c0932ce5afb140a5b3866bba9d3dba4fc9a58ec7ac9b59a805674e2ae7d47", "size_in_bytes": 30984}, {"_path": "include/opentelemetry/nostd/internal/absl/types/bad_variant_access.h", "path_type": "hardlink", "sha256": "b514fa2da93718805e4efabb5146951cd624dde8ab365175ffc76503a08554a3", "sha256_in_prefix": "b514fa2da93718805e4efabb5146951cd624dde8ab365175ffc76503a08554a3", "size_in_bytes": 3116}, {"_path": "include/opentelemetry/nostd/internal/absl/types/internal/variant.h", "path_type": "hardlink", "sha256": "9c88d153cb6195ddd7a315f78c4b27928ccc84e0723bc5fdc9f49c4e47e902b8", "sha256_in_prefix": "9c88d153cb6195ddd7a315f78c4b27928ccc84e0723bc5fdc9f49c4e47e902b8", "size_in_bytes": 61087}, {"_path": "include/opentelemetry/nostd/internal/absl/types/variant.h", "path_type": "hardlink", "sha256": "b444cbd2cd47b7ea8bff85235b428193675a65e31b2f796057890d0444c782fb", "sha256_in_prefix": "b444cbd2cd47b7ea8bff85235b428193675a65e31b2f796057890d0444c782fb", "size_in_bytes": 37667}, {"_path": "include/opentelemetry/nostd/internal/absl/utility/utility.h", "path_type": "hardlink", "sha256": "f24ab1dbd750a8edbd334e1ce86bf5b096d14c84dcd3d82e41b798de7afe55e8", "sha256_in_prefix": "f24ab1dbd750a8edbd334e1ce86bf5b096d14c84dcd3d82e41b798de7afe55e8", "size_in_bytes": 12745}, {"_path": "include/opentelemetry/nostd/shared_ptr.h", "path_type": "hardlink", "sha256": "ecb7921a403e6997d17ac025b2174688dca26320060ffc0988ebcd8cca7c2791", "sha256_in_prefix": "ecb7921a403e6997d17ac025b2174688dca26320060ffc0988ebcd8cca7c2791", "size_in_bytes": 5288}, {"_path": "include/opentelemetry/nostd/span.h", "path_type": "hardlink", "sha256": "73c57105261052e2658614b32103ad8d8afcf288e933736293ed1fa5b50fbdb3", "sha256_in_prefix": "73c57105261052e2658614b32103ad8d8afcf288e933736293ed1fa5b50fbdb3", "size_in_bytes": 8448}, {"_path": "include/opentelemetry/nostd/string_view.h", "path_type": "hardlink", "sha256": "2d77449e455883910113e32c219e853b447619b45fd50ea1c1c65e11e933d838", "sha256_in_prefix": "2d77449e455883910113e32c219e853b447619b45fd50ea1c1c65e11e933d838", "size_in_bytes": 5993}, {"_path": "include/opentelemetry/nostd/type_traits.h", "path_type": "hardlink", "sha256": "8f6e2b5bd19b4f0589e5a1fbf2ca4e7015abed9e6230cc3ca305b3de885e70d3", "sha256_in_prefix": "8f6e2b5bd19b4f0589e5a1fbf2ca4e7015abed9e6230cc3ca305b3de885e70d3", "size_in_bytes": 3959}, {"_path": "include/opentelemetry/nostd/unique_ptr.h", "path_type": "hardlink", "sha256": "1087a28701b614ffa43765572829194b071bc6c90832a54b1bb795db35aaf47c", "sha256_in_prefix": "1087a28701b614ffa43765572829194b071bc6c90832a54b1bb795db35aaf47c", "size_in_bytes": 4002}, {"_path": "include/opentelemetry/nostd/utility.h", "path_type": "hardlink", "sha256": "ef3a988bc439d58d385bdd1af8f3094ce5d224e18b710d2605fe65684a83df30", "sha256_in_prefix": "ef3a988bc439d58d385bdd1af8f3094ce5d224e18b710d2605fe65684a83df30", "size_in_bytes": 3250}, {"_path": "include/opentelemetry/nostd/variant.h", "path_type": "hardlink", "sha256": "65c0de52f686dae0e100d711cc51666d63a87f238b77a0c0b455b22d62fb3e3c", "sha256_in_prefix": "65c0de52f686dae0e100d711cc51666d63a87f238b77a0c0b455b22d62fb3e3c", "size_in_bytes": 2483}, {"_path": "include/opentelemetry/plugin/detail/dynamic_library_handle.h", "path_type": "hardlink", "sha256": "76e16e3e0dd56a4fcdf5a0debd3d7e7aa0a1ec43f410abef1a4082f86aba5c76", "sha256_in_prefix": "76e16e3e0dd56a4fcdf5a0debd3d7e7aa0a1ec43f410abef1a4082f86aba5c76", "size_in_bytes": 379}, {"_path": "include/opentelemetry/plugin/detail/dynamic_load_unix.h", "path_type": "hardlink", "sha256": "49d6b6a559e254d65139da0650fbd3e7120b1dc7490444b09cb1d60218c6ed99", "sha256_in_prefix": "49d6b6a559e254d65139da0650fbd3e7120b1dc7490444b09cb1d60218c6ed99", "size_in_bytes": 2148}, {"_path": "include/opentelemetry/plugin/detail/dynamic_load_windows.h", "path_type": "hardlink", "sha256": "72bb4521336424b5d4512a0142b57442134abeb72a2362d6e46d56e5fa126ef7", "sha256_in_prefix": "72bb4521336424b5d4512a0142b57442134abeb72a2362d6e46d56e5fa126ef7", "size_in_bytes": 2809}, {"_path": "include/opentelemetry/plugin/detail/loader_info.h", "path_type": "hardlink", "sha256": "081d32f34d64606cfc155c10aa58b337c4bd8d689e915dacc99988c27c675cc4", "sha256_in_prefix": "081d32f34d64606cfc155c10aa58b337c4bd8d689e915dacc99988c27c675cc4", "size_in_bytes": 644}, {"_path": "include/opentelemetry/plugin/detail/tracer_handle.h", "path_type": "hardlink", "sha256": "e3b5ead1d52bd3776ddeda04cea64b2bcae51e5187b5acf619e8c07637c02d55", "sha256_in_prefix": "e3b5ead1d52bd3776ddeda04cea64b2bcae51e5187b5acf619e8c07637c02d55", "size_in_bytes": 472}, {"_path": "include/opentelemetry/plugin/detail/utility.h", "path_type": "hardlink", "sha256": "70ffb3f5b1021c48852744dfdd81751d19f54107685d065a573c28756c90dbe0", "sha256_in_prefix": "70ffb3f5b1021c48852744dfdd81751d19f54107685d065a573c28756c90dbe0", "size_in_bytes": 598}, {"_path": "include/opentelemetry/plugin/dynamic_load.h", "path_type": "hardlink", "sha256": "a638e13ff8eba02a5c7f4e18379239559bc8da62e7b92c63a8e71d0230f29aeb", "sha256_in_prefix": "a638e13ff8eba02a5c7f4e18379239559bc8da62e7b92c63a8e71d0230f29aeb", "size_in_bytes": 874}, {"_path": "include/opentelemetry/plugin/factory.h", "path_type": "hardlink", "sha256": "7101646cb68d64b2badc0cbe2adeac111f93c2c6a8777e1e41744248d91b1fef", "sha256_in_prefix": "7101646cb68d64b2badc0cbe2adeac111f93c2c6a8777e1e41744248d91b1fef", "size_in_bytes": 2107}, {"_path": "include/opentelemetry/plugin/hook.h", "path_type": "hardlink", "sha256": "610b8128b2bb295cab02c0bc6b273a48b37c56fba45f176d24e97078dcc1b631", "sha256_in_prefix": "610b8128b2bb295cab02c0bc6b273a48b37c56fba45f176d24e97078dcc1b631", "size_in_bytes": 2144}, {"_path": "include/opentelemetry/plugin/tracer.h", "path_type": "hardlink", "sha256": "811c49e1a52913ecc879d2d301643c1d050358db7c0fec2e5d277d9a285b8435", "sha256_in_prefix": "811c49e1a52913ecc879d2d301643c1d050358db7c0fec2e5d277d9a285b8435", "size_in_bytes": 3988}, {"_path": "include/opentelemetry/proto/collector/logs/v1/logs_service.grpc.pb.h", "path_type": "hardlink", "sha256": "387ed7c73d74f93ec6b16a6071226df88c2775fe36bded662aba68d51d02ac9e", "sha256_in_prefix": "387ed7c73d74f93ec6b16a6071226df88c2775fe36bded662aba68d51d02ac9e", "size_in_bytes": 17224}, {"_path": "include/opentelemetry/proto/collector/logs/v1/logs_service.pb.h", "path_type": "hardlink", "sha256": "c3f25a6cc1b037340e18ea43f1ea55e24abceea9cb9ee56736f19fc79fed0966", "sha256_in_prefix": "c3f25a6cc1b037340e18ea43f1ea55e24abceea9cb9ee56736f19fc79fed0966", "size_in_bytes": 41394}, {"_path": "include/opentelemetry/proto/collector/logs/v1/logs_service_mock.grpc.pb.h", "path_type": "hardlink", "sha256": "13e51d2aa1599c59c902b8ba216bd0c94ff5f80bf1f894dd9685f40d936c68b7", "sha256_in_prefix": "13e51d2aa1599c59c902b8ba216bd0c94ff5f80bf1f894dd9685f40d936c68b7", "size_in_bytes": 1842}, {"_path": "include/opentelemetry/proto/collector/metrics/v1/metrics_service.grpc.pb.h", "path_type": "hardlink", "sha256": "5dd25149e0d68596940641d99825bbaae2c9208ff69b025fa3ee7a39885c97ef", "sha256_in_prefix": "5dd25149e0d68596940641d99825bbaae2c9208ff69b025fa3ee7a39885c97ef", "size_in_bytes": 17586}, {"_path": "include/opentelemetry/proto/collector/metrics/v1/metrics_service.pb.h", "path_type": "hardlink", "sha256": "67c67ba837a3d20ff7d70e6f637532dfb790938edee50761ee8f19b59285e3e7", "sha256_in_prefix": "67c67ba837a3d20ff7d70e6f637532dfb790938edee50761ee8f19b59285e3e7", "size_in_bytes": 42507}, {"_path": "include/opentelemetry/proto/collector/metrics/v1/metrics_service_mock.grpc.pb.h", "path_type": "hardlink", "sha256": "277c5a43475383ff214572122f0815b752cf322810950b43f38fb932ca3f6ec3", "sha256_in_prefix": "277c5a43475383ff214572122f0815b752cf322810950b43f38fb932ca3f6ec3", "size_in_bytes": 1926}, {"_path": "include/opentelemetry/proto/collector/profiles/v1development/profiles_service.grpc.pb.h", "path_type": "hardlink", "sha256": "daff578f65a8e8405e3885b6358153065555a26ee37b73a185e1d92d78ae4a54", "sha256_in_prefix": "daff578f65a8e8405e3885b6358153065555a26ee37b73a185e1d92d78ae4a54", "size_in_bytes": 18519}, {"_path": "include/opentelemetry/proto/collector/profiles/v1development/profiles_service.pb.h", "path_type": "hardlink", "sha256": "e70ebcd217b0943bb67e542ecf886f4479fa71661b43600a8ab503abb7ad25b9", "sha256_in_prefix": "e70ebcd217b0943bb67e542ecf886f4479fa71661b43600a8ab503abb7ad25b9", "size_in_bytes": 43785}, {"_path": "include/opentelemetry/proto/collector/profiles/v1development/profiles_service_mock.grpc.pb.h", "path_type": "hardlink", "sha256": "a4998c5ee608be2a60f3d16c06749e49c5e04e44ca85ea723b1a39f287ccf91a", "sha256_in_prefix": "a4998c5ee608be2a60f3d16c06749e49c5e04e44ca85ea723b1a39f287ccf91a", "size_in_bytes": 2108}, {"_path": "include/opentelemetry/proto/collector/trace/v1/trace_service.grpc.pb.h", "path_type": "hardlink", "sha256": "656f78edcd9a16478a3f9be96617feea942456df9da1687a947549482e8526a3", "sha256_in_prefix": "656f78edcd9a16478a3f9be96617feea942456df9da1687a947549482e8526a3", "size_in_bytes": 17367}, {"_path": "include/opentelemetry/proto/collector/trace/v1/trace_service.pb.h", "path_type": "hardlink", "sha256": "bbf719fafacc5f0bb603eee8faa3be6e2d300420788064a57e6f7278b1685b73", "sha256_in_prefix": "bbf719fafacc5f0bb603eee8faa3be6e2d300420788064a57e6f7278b1685b73", "size_in_bytes": 41640}, {"_path": "include/opentelemetry/proto/collector/trace/v1/trace_service_mock.grpc.pb.h", "path_type": "hardlink", "sha256": "5aa1be5ebeafe60150cf02961790ea601927a3e479986bd6cd50eb129173998f", "sha256_in_prefix": "5aa1be5ebeafe60150cf02961790ea601927a3e479986bd6cd50eb129173998f", "size_in_bytes": 1870}, {"_path": "include/opentelemetry/proto/common/v1/common.grpc.pb.h", "path_type": "hardlink", "sha256": "5ecf00528c74eec9b8b1cd35a67c80b41382ad229da440a849d74bd1f60f396f", "sha256_in_prefix": "5ecf00528c74eec9b8b1cd35a67c80b41382ad229da440a849d74bd1f60f396f", "size_in_bytes": 2019}, {"_path": "include/opentelemetry/proto/common/v1/common.pb.h", "path_type": "hardlink", "sha256": "6803fa3d7701ffaf8d514718d035251b5c0fdc2cf08c1a33db75573a07d36745", "sha256_in_prefix": "6803fa3d7701ffaf8d514718d035251b5c0fdc2cf08c1a33db75573a07d36745", "size_in_bytes": 105404}, {"_path": "include/opentelemetry/proto/common/v1/common_mock.grpc.pb.h", "path_type": "hardlink", "sha256": "b97e05c261fbbbd261b0163c815f92a38eebae24800a6330b6b39a31c5bae882", "sha256_in_prefix": "b97e05c261fbbbd261b0163c815f92a38eebae24800a6330b6b39a31c5bae882", "size_in_bytes": 784}, {"_path": "include/opentelemetry/proto/logs/v1/logs.grpc.pb.h", "path_type": "hardlink", "sha256": "8343b08b894e00a3f386775bc21c5fa3e376023d2f1794518027b4e772154b1b", "sha256_in_prefix": "8343b08b894e00a3f386775bc21c5fa3e376023d2f1794518027b4e772154b1b", "size_in_bytes": 1995}, {"_path": "include/opentelemetry/proto/logs/v1/logs.pb.h", "path_type": "hardlink", "sha256": "21cb9c95bc69b7fa08324af95e70e81108d245332745d1d525b1d9b121acbc4a", "sha256_in_prefix": "21cb9c95bc69b7fa08324af95e70e81108d245332745d1d525b1d9b121acbc4a", "size_in_bytes": 88440}, {"_path": "include/opentelemetry/proto/logs/v1/logs_mock.grpc.pb.h", "path_type": "hardlink", "sha256": "6e7c159a87f21f8dc716537b119179c349b811ba11080262d2e6d9c1ffaa4e9c", "sha256_in_prefix": "6e7c159a87f21f8dc716537b119179c349b811ba11080262d2e6d9c1ffaa4e9c", "size_in_bytes": 756}, {"_path": "include/opentelemetry/proto/metrics/v1/metrics.grpc.pb.h", "path_type": "hardlink", "sha256": "80fd3cadbe3658fb8651e7d2476d99e62eb4cf9f9777a8354e0944b5d28149a1", "sha256_in_prefix": "80fd3cadbe3658fb8651e7d2476d99e62eb4cf9f9777a8354e0944b5d28149a1", "size_in_bytes": 2031}, {"_path": "include/opentelemetry/proto/metrics/v1/metrics.pb.h", "path_type": "hardlink", "sha256": "ee0fc20374d648c57205f1af9a90829c75bb61024fe494a87007ecbbc1f97f5c", "sha256_in_prefix": "ee0fc20374d648c57205f1af9a90829c75bb61024fe494a87007ecbbc1f97f5c", "size_in_bytes": 311896}, {"_path": "include/opentelemetry/proto/metrics/v1/metrics_mock.grpc.pb.h", "path_type": "hardlink", "sha256": "8bf2b15c262553bf217dbcb8332243af7c325491984859946a295563d2fe6650", "sha256_in_prefix": "8bf2b15c262553bf217dbcb8332243af7c325491984859946a295563d2fe6650", "size_in_bytes": 798}, {"_path": "include/opentelemetry/proto/profiles/v1development/profiles.grpc.pb.h", "path_type": "hardlink", "sha256": "40c694d8667c4be6be9678c8f195f980ad95608e338822abff2e33e515ae4e42", "sha256_in_prefix": "40c694d8667c4be6be9678c8f195f980ad95608e338822abff2e33e515ae4e42", "size_in_bytes": 2821}, {"_path": "include/opentelemetry/proto/profiles/v1development/profiles.pb.h", "path_type": "hardlink", "sha256": "dc238411dca0df2740ee235cc6c70117e7294478c67a192770de554a656b4e02", "sha256_in_prefix": "dc238411dca0df2740ee235cc6c70117e7294478c67a192770de554a656b4e02", "size_in_bytes": 239903}, {"_path": "include/opentelemetry/proto/profiles/v1development/profiles_mock.grpc.pb.h", "path_type": "hardlink", "sha256": "c29dce5a75b86a5c12de22e910d3514223253ca13e4660153fdf02e8743e210b", "sha256_in_prefix": "c29dce5a75b86a5c12de22e910d3514223253ca13e4660153fdf02e8743e210b", "size_in_bytes": 900}, {"_path": "include/opentelemetry/proto/resource/v1/resource.grpc.pb.h", "path_type": "hardlink", "sha256": "31ebd2e5bfc92143f5b2b6c42eb2b3612ff10194daa34884d6afc86bd65a4823", "sha256_in_prefix": "31ebd2e5bfc92143f5b2b6c42eb2b3612ff10194daa34884d6afc86bd65a4823", "size_in_bytes": 2043}, {"_path": "include/opentelemetry/proto/resource/v1/resource.pb.h", "path_type": "hardlink", "sha256": "a66ca8d2cef16fba59751623a5c351b0a256e71043bba50ed41bf8332b7b0857", "sha256_in_prefix": "a66ca8d2cef16fba59751623a5c351b0a256e71043bba50ed41bf8332b7b0857", "size_in_bytes": 18064}, {"_path": "include/opentelemetry/proto/resource/v1/resource_mock.grpc.pb.h", "path_type": "hardlink", "sha256": "bf049880eef13fca2a174bea73118fff7d8128398ea5d14b09d44785daaea218", "sha256_in_prefix": "bf049880eef13fca2a174bea73118fff7d8128398ea5d14b09d44785daaea218", "size_in_bytes": 812}, {"_path": "include/opentelemetry/proto/trace/v1/trace.grpc.pb.h", "path_type": "hardlink", "sha256": "264f2cb2d95c229a840d77d562d26ced3845471bc152c454b0f8fcabb00bc490", "sha256_in_prefix": "264f2cb2d95c229a840d77d562d26ced3845471bc152c454b0f8fcabb00bc490", "size_in_bytes": 2007}, {"_path": "include/opentelemetry/proto/trace/v1/trace.pb.h", "path_type": "hardlink", "sha256": "9c747d7063bc82ad51291db452813f272f3568c2f80acbd7b10d1704c3d9d6bf", "sha256_in_prefix": "9c747d7063bc82ad51291db452813f272f3568c2f80acbd7b10d1704c3d9d6bf", "size_in_bytes": 150760}, {"_path": "include/opentelemetry/proto/trace/v1/trace_mock.grpc.pb.h", "path_type": "hardlink", "sha256": "08a9b1d5bf99b3f81ca39dc65062ef4f836419c7fd8abda7bf02f11d7e4c66b4", "sha256_in_prefix": "08a9b1d5bf99b3f81ca39dc65062ef4f836419c7fd8abda7bf02f11d7e4c66b4", "size_in_bytes": 770}, {"_path": "include/opentelemetry/sdk/common/atomic_shared_ptr.h", "path_type": "hardlink", "sha256": "6410757017c804f30e9069b8348b49b6d73bc88b410c1f329c4dd3260d8f042c", "sha256_in_prefix": "6410757017c804f30e9069b8348b49b6d73bc88b410c1f329c4dd3260d8f042c", "size_in_bytes": 1375}, {"_path": "include/opentelemetry/sdk/common/atomic_unique_ptr.h", "path_type": "hardlink", "sha256": "69588484154f9622f1a0a7af8619efa0c60dacb20444ae2b082deb4850ba5673", "sha256_in_prefix": "69588484154f9622f1a0a7af8619efa0c60dacb20444ae2b082deb4850ba5673", "size_in_bytes": 2015}, {"_path": "include/opentelemetry/sdk/common/attribute_utils.h", "path_type": "hardlink", "sha256": "5da2afbba24ec4238834ca71f215cbb71cf57a3f30d543886e8a7b1f9d0b18fa", "sha256_in_prefix": "5da2afbba24ec4238834ca71f215cbb71cf57a3f30d543886e8a7b1f9d0b18fa", "size_in_bytes": 10322}, {"_path": "include/opentelemetry/sdk/common/attributemap_hash.h", "path_type": "hardlink", "sha256": "e12ab272b5ea7266d67c023b35f1c1ea57ab4d3dd735ad8cde31c628f36397c4", "sha256_in_prefix": "e12ab272b5ea7266d67c023b35f1c1ea57ab4d3dd735ad8cde31c628f36397c4", "size_in_bytes": 1770}, {"_path": "include/opentelemetry/sdk/common/base64.h", "path_type": "hardlink", "sha256": "9ddd76ee615211fe59311b3d7ccff1e77581d62e03828a0bc85efab6c2462f32", "sha256_in_prefix": "9ddd76ee615211fe59311b3d7ccff1e77581d62e03828a0bc85efab6c2462f32", "size_in_bytes": 1197}, {"_path": "include/opentelemetry/sdk/common/circular_buffer.h", "path_type": "hardlink", "sha256": "0350dcd906ee5d5c9b5d95c32c8ad6f14b8b655f0ab80099b7fed1fc867a7e3c", "sha256_in_prefix": "0350dcd906ee5d5c9b5d95c32c8ad6f14b8b655f0ab80099b7fed1fc867a7e3c", "size_in_bytes": 5430}, {"_path": "include/opentelemetry/sdk/common/circular_buffer_range.h", "path_type": "hardlink", "sha256": "d59b8cce433ff8f74b22d34e89692ea12cce94b5094623f90ae3514e10979395", "sha256_in_prefix": "d59b8cce433ff8f74b22d34e89692ea12cce94b5094623f90ae3514e10979395", "size_in_bytes": 2153}, {"_path": "include/opentelemetry/sdk/common/disabled.h", "path_type": "hardlink", "sha256": "1701bf65958c78252f2de0e52502b5c4e784cad79480d237cb63620051b5efb2", "sha256_in_prefix": "1701bf65958c78252f2de0e52502b5c4e784cad79480d237cb63620051b5efb2", "size_in_bytes": 290}, {"_path": "include/opentelemetry/sdk/common/empty_attributes.h", "path_type": "hardlink", "sha256": "6e850963b330da96fcbba043cf16f8411d8752e156e6971942162de2a5383124", "sha256_in_prefix": "6e850963b330da96fcbba043cf16f8411d8752e156e6971942162de2a5383124", "size_in_bytes": 975}, {"_path": "include/opentelemetry/sdk/common/env_variables.h", "path_type": "hardlink", "sha256": "280c5ddff4d3edb4425ba35d9232e872b65aa6174ede42462ef628da266e2879", "sha256_in_prefix": "280c5ddff4d3edb4425ba35d9232e872b65aa6174ede42462ef628da266e2879", "size_in_bytes": 1887}, {"_path": "include/opentelemetry/sdk/common/exporter_utils.h", "path_type": "hardlink", "sha256": "9fb58cf44c988ae3ff1adb225471d150107314c857aa3ce9251c9c17dd7ac396", "sha256_in_prefix": "9fb58cf44c988ae3ff1adb225471d150107314c857aa3ce9251c9c17dd7ac396", "size_in_bytes": 744}, {"_path": "include/opentelemetry/sdk/common/global_log_handler.h", "path_type": "hardlink", "sha256": "37c950a9209ea77821ed148cc83b43cc9c9622b8cc25449e45bcc9316a1fc588", "sha256_in_prefix": "37c950a9209ea77821ed148cc83b43cc9c9622b8cc25449e45bcc9316a1fc588", "size_in_bytes": 8844}, {"_path": "include/opentelemetry/sdk/common/thread_instrumentation.h", "path_type": "hardlink", "sha256": "a4d6067830f1d520322b62f08594915cec0bb001eb3c5a59b89891f60f89ec2a", "sha256_in_prefix": "a4d6067830f1d520322b62f08594915cec0bb001eb3c5a59b89891f60f89ec2a", "size_in_bytes": 3448}, {"_path": "include/opentelemetry/sdk/instrumentationlibrary/instrumentation_library.h", "path_type": "hardlink", "sha256": "658a209367f433832cc6c46dc6b66ca98d37a903c342b78652fab0301c8d51ee", "sha256_in_prefix": "658a209367f433832cc6c46dc6b66ca98d37a903c342b78652fab0301c8d51ee", "size_in_bytes": 450}, {"_path": "include/opentelemetry/sdk/instrumentationscope/instrumentation_scope.h", "path_type": "hardlink", "sha256": "9873472154b1b2d5abf3047249fc9e1c0b1620c6ce738e87df440a5ccc28cb38", "sha256_in_prefix": "9873472154b1b2d5abf3047249fc9e1c0b1620c6ce738e87df440a5ccc28cb38", "size_in_bytes": 6710}, {"_path": "include/opentelemetry/sdk/instrumentationscope/scope_configurator.h", "path_type": "hardlink", "sha256": "97945cecd22f968fa8b2a469a9eed3dc24745c1b0d75ec5e780e46fb3e6ad880", "sha256_in_prefix": "97945cecd22f968fa8b2a469a9eed3dc24745c1b0d75ec5e780e46fb3e6ad880", "size_in_bytes": 4996}, {"_path": "include/opentelemetry/sdk/logs/batch_log_record_processor.h", "path_type": "hardlink", "sha256": "ec10de941d2eabb1c98da50a42f4dc9fb7feb24be31665fcca0372bb59d36040", "sha256_in_prefix": "ec10de941d2eabb1c98da50a42f4dc9fb7feb24be31665fcca0372bb59d36040", "size_in_bytes": 6667}, {"_path": "include/opentelemetry/sdk/logs/batch_log_record_processor_factory.h", "path_type": "hardlink", "sha256": "c17a45a7697701f686ff98354d83619d939c536cbc8071271a6eb83075305f98", "sha256_in_prefix": "c17a45a7697701f686ff98354d83619d939c536cbc8071271a6eb83075305f98", "size_in_bytes": 1179}, {"_path": "include/opentelemetry/sdk/logs/batch_log_record_processor_options.h", "path_type": "hardlink", "sha256": "32b009d2a373fc36cc981a75d1aad474e9ce71fab2a4467ad2961ac227fb2bff", "sha256_in_prefix": "32b009d2a373fc36cc981a75d1aad474e9ce71fab2a4467ad2961ac227fb2bff", "size_in_bytes": 824}, {"_path": "include/opentelemetry/sdk/logs/batch_log_record_processor_runtime_options.h", "path_type": "hardlink", "sha256": "8b7d9351c1d93438c2c4731f51c7aa131450b9e03dc9cdcde49df866ad91e849", "sha256_in_prefix": "8b7d9351c1d93438c2c4731f51c7aa131450b9e03dc9cdcde49df866ad91e849", "size_in_bytes": 623}, {"_path": "include/opentelemetry/sdk/logs/event_logger.h", "path_type": "hardlink", "sha256": "ddd1b8f0a08d93785f30d9995ada8f9353ee191cfdb35bc55f4710f7e06cb3a8", "sha256_in_prefix": "ddd1b8f0a08d93785f30d9995ada8f9353ee191cfdb35bc55f4710f7e06cb3a8", "size_in_bytes": 1503}, {"_path": "include/opentelemetry/sdk/logs/event_logger_provider.h", "path_type": "hardlink", "sha256": "1b2d0ada0e1642c001f50b124fbcc51c4b7f0f3b5c7821d167b810753fe048cf", "sha256_in_prefix": "1b2d0ada0e1642c001f50b124fbcc51c4b7f0f3b5c7821d167b810753fe048cf", "size_in_bytes": 1136}, {"_path": "include/opentelemetry/sdk/logs/event_logger_provider_factory.h", "path_type": "hardlink", "sha256": "58244001680abed47d0827c2a93596e54ca748d4fa1c0c944f4504432d3b793c", "sha256_in_prefix": "58244001680abed47d0827c2a93596e54ca748d4fa1c0c944f4504432d3b793c", "size_in_bytes": 663}, {"_path": "include/opentelemetry/sdk/logs/exporter.h", "path_type": "hardlink", "sha256": "8d91588fd8fd2e15e8836a377e927b7c1b374058bd55165ac3b17e6e8a5687c9", "sha256_in_prefix": "8d91588fd8fd2e15e8836a377e927b7c1b374058bd55165ac3b17e6e8a5687c9", "size_in_bytes": 2299}, {"_path": "include/opentelemetry/sdk/logs/logger.h", "path_type": "hardlink", "sha256": "a0b5926491d129ba025bf5c358a24774c3f672b8fab21e3e84e9a940245cce7a", "sha256_in_prefix": "a0b5926491d129ba025bf5c358a24774c3f672b8fab21e3e84e9a940245cce7a", "size_in_bytes": 2354}, {"_path": "include/opentelemetry/sdk/logs/logger_config.h", "path_type": "hardlink", "sha256": "18a2660cbd806ca13fca59438ea934baf671d96836d3d4f77ae08b8980733b83", "sha256_in_prefix": "18a2660cbd806ca13fca59438ea934baf671d96836d3d4f77ae08b8980733b83", "size_in_bytes": 1729}, {"_path": "include/opentelemetry/sdk/logs/logger_context.h", "path_type": "hardlink", "sha256": "096bc281d7eb12259ee42e89f0317ae391a51a29243753e09447bb0ac7d93a8e", "sha256_in_prefix": "096bc281d7eb12259ee42e89f0317ae391a51a29243753e09447bb0ac7d93a8e", "size_in_bytes": 3596}, {"_path": "include/opentelemetry/sdk/logs/logger_context_factory.h", "path_type": "hardlink", "sha256": "a4af4bd8398cbff8041269f409a9c256096ba5f14f41d57b2642f0036b7a327e", "sha256_in_prefix": "a4af4bd8398cbff8041269f409a9c256096ba5f14f41d57b2642f0036b7a327e", "size_in_bytes": 2153}, {"_path": "include/opentelemetry/sdk/logs/logger_provider.h", "path_type": "hardlink", "sha256": "e45bfd26557c54e1d454f9b03b8ccb3d69e4080cf84085e2bb62965026b46d55", "sha256_in_prefix": "e45bfd26557c54e1d454f9b03b8ccb3d69e4080cf84085e2bb62965026b46d55", "size_in_bytes": 5042}, {"_path": "include/opentelemetry/sdk/logs/logger_provider_factory.h", "path_type": "hardlink", "sha256": "c57a3783123cea844c027ed238461b03a7b604018a3a4bc8f291b546cbb9bde0", "sha256_in_prefix": "c57a3783123cea844c027ed238461b03a7b604018a3a4bc8f291b546cbb9bde0", "size_in_bytes": 2300}, {"_path": "include/opentelemetry/sdk/logs/multi_log_record_processor.h", "path_type": "hardlink", "sha256": "5f98bad9d4f433094c50cec65a56a62ef02a91ece6b6df43b370d918b5a56952", "sha256_in_prefix": "5f98bad9d4f433094c50cec65a56a62ef02a91ece6b6df43b370d918b5a56952", "size_in_bytes": 2034}, {"_path": "include/opentelemetry/sdk/logs/multi_log_record_processor_factory.h", "path_type": "hardlink", "sha256": "ae9354c0b68f963cad13f6062a3fc4ef2a0bf3f08001d48a44d47cdec71dd125", "sha256_in_prefix": "ae9354c0b68f963cad13f6062a3fc4ef2a0bf3f08001d48a44d47cdec71dd125", "size_in_bytes": 595}, {"_path": "include/opentelemetry/sdk/logs/multi_recordable.h", "path_type": "hardlink", "sha256": "c1a2d00b5be24dbce49e4ffe0e552950f1ba968bfcb0ab42f9b3a04715cc71a1", "sha256_in_prefix": "c1a2d00b5be24dbce49e4ffe0e552950f1ba968bfcb0ab42f9b3a04715cc71a1", "size_in_bytes": 3379}, {"_path": "include/opentelemetry/sdk/logs/processor.h", "path_type": "hardlink", "sha256": "76f2e4ed8e01d47a5e5ce30fe17f32fd1fa811d07adb90e9fe714a74eeb36812", "sha256_in_prefix": "76f2e4ed8e01d47a5e5ce30fe17f32fd1fa811d07adb90e9fe714a74eeb36812", "size_in_bytes": 1811}, {"_path": "include/opentelemetry/sdk/logs/provider.h", "path_type": "hardlink", "sha256": "edffcb7434a037904fb86dc01d87df3d135ad59cfb4df82e0ed1cd6c16642ce6", "sha256_in_prefix": "edffcb7434a037904fb86dc01d87df3d135ad59cfb4df82e0ed1cd6c16642ce6", "size_in_bytes": 580}, {"_path": "include/opentelemetry/sdk/logs/read_write_log_record.h", "path_type": "hardlink", "sha256": "8dde5179b0900dadc27d3e1d5b9ad3e68b0b3eb7b07d48335e17d5a649722e78", "sha256_in_prefix": "8dde5179b0900dadc27d3e1d5b9ad3e68b0b3eb7b07d48335e17d5a649722e78", "size_in_bytes": 6368}, {"_path": "include/opentelemetry/sdk/logs/readable_log_record.h", "path_type": "hardlink", "sha256": "c3d61925dc2b3f31c7c477de79f44350ec40c7afbb4c0302130f4f02f35f5379", "sha256_in_prefix": "c3d61925dc2b3f31c7c477de79f44350ec40c7afbb4c0302130f4f02f35f5379", "size_in_bytes": 3827}, {"_path": "include/opentelemetry/sdk/logs/recordable.h", "path_type": "hardlink", "sha256": "1daac555b7666c849e94338d25749e6c05dbd0084bcf237017e707a8af427181", "sha256_in_prefix": "1daac555b7666c849e94338d25749e6c05dbd0084bcf237017e707a8af427181", "size_in_bytes": 1141}, {"_path": "include/opentelemetry/sdk/logs/simple_log_record_processor.h", "path_type": "hardlink", "sha256": "e0e52bd17bd9984ebcdba6fd36ed73d87d5234fef26471276f6d9f0d086bf8f4", "sha256_in_prefix": "e0e52bd17bd9984ebcdba6fd36ed73d87d5234fef26471276f6d9f0d086bf8f4", "size_in_bytes": 1657}, {"_path": "include/opentelemetry/sdk/logs/simple_log_record_processor_factory.h", "path_type": "hardlink", "sha256": "543da8e5ff8370e3f1e91bde58653a165103d68f51f25106e974f2736a3ccf0e", "sha256_in_prefix": "543da8e5ff8370e3f1e91bde58653a165103d68f51f25106e974f2736a3ccf0e", "size_in_bytes": 632}, {"_path": "include/opentelemetry/sdk/metrics/aggregation/aggregation.h", "path_type": "hardlink", "sha256": "4eac8454ebda2c58b4d1a70bdb015f647990648599b238ffcbc6b2cf4a3c763f", "sha256_in_prefix": "4eac8454ebda2c58b4d1a70bdb015f647990648599b238ffcbc6b2cf4a3c763f", "size_in_bytes": 1485}, {"_path": "include/opentelemetry/sdk/metrics/aggregation/aggregation_config.h", "path_type": "hardlink", "sha256": "7a036aaaebda75680e7fe0d89b220b2432bde55319ae90afb6674688926d7468", "sha256_in_prefix": "7a036aaaebda75680e7fe0d89b220b2432bde55319ae90afb6674688926d7468", "size_in_bytes": 686}, {"_path": "include/opentelemetry/sdk/metrics/aggregation/base2_exponential_histogram_aggregation.h", "path_type": "hardlink", "sha256": "9c009bdc904d3ee3b0ca6d15fba709a604cb7acf839ed58067024af04e78500e", "sha256_in_prefix": "9c009bdc904d3ee3b0ca6d15fba709a604cb7acf839ed58067024af04e78500e", "size_in_bytes": 2310}, {"_path": "include/opentelemetry/sdk/metrics/aggregation/base2_exponential_histogram_indexer.h", "path_type": "hardlink", "sha256": "be1ee4688b54a70fd4b43e050f0bdcdb82377b599de9bfe8921cc1d0b525d449", "sha256_in_prefix": "be1ee4688b54a70fd4b43e050f0bdcdb82377b599de9bfe8921cc1d0b525d449", "size_in_bytes": 1058}, {"_path": "include/opentelemetry/sdk/metrics/aggregation/default_aggregation.h", "path_type": "hardlink", "sha256": "426ac9f5438d565f3c6229e8d9c2ecddbbd329e67860983eb5949f46382030de", "sha256_in_prefix": "426ac9f5438d565f3c6229e8d9c2ecddbbd329e67860983eb5949f46382030de", "size_in_bytes": 7451}, {"_path": "include/opentelemetry/sdk/metrics/aggregation/drop_aggregation.h", "path_type": "hardlink", "sha256": "66fb0f79359ae9e306562105945eeeccd5a95d34c4ef919cce49889ffd086869", "sha256_in_prefix": "66fb0f79359ae9e306562105945eeeccd5a95d34c4ef919cce49889ffd086869", "size_in_bytes": 1238}, {"_path": "include/opentelemetry/sdk/metrics/aggregation/histogram_aggregation.h", "path_type": "hardlink", "sha256": "681ecbc7a778998b799eec8c5e3490a2ad4c89fd26b8966a38c7b8ef7da947d8", "sha256_in_prefix": "681ecbc7a778998b799eec8c5e3490a2ad4c89fd26b8966a38c7b8ef7da947d8", "size_in_bytes": 4559}, {"_path": "include/opentelemetry/sdk/metrics/aggregation/lastvalue_aggregation.h", "path_type": "hardlink", "sha256": "05aaadde429b4c22307753142ecdea0598d7d21b0a47564cbe6921b8b935dade", "sha256_in_prefix": "05aaadde429b4c22307753142ecdea0598d7d21b0a47564cbe6921b8b935dade", "size_in_bytes": 1998}, {"_path": "include/opentelemetry/sdk/metrics/aggregation/sum_aggregation.h", "path_type": "hardlink", "sha256": "9cfb708edd172ae63420fea70fffe023e5e65fedcd39f982fa75150271bd4904", "sha256_in_prefix": "9cfb708edd172ae63420fea70fffe023e5e65fedcd39f982fa75150271bd4904", "size_in_bytes": 1925}, {"_path": "include/opentelemetry/sdk/metrics/async_instruments.h", "path_type": "hardlink", "sha256": "e19fc29d15d3f9fb7a4440043c482be4765c63105dad423a41788b1b458dd6d4", "sha256_in_prefix": "e19fc29d15d3f9fb7a4440043c482be4765c63105dad423a41788b1b458dd6d4", "size_in_bytes": 1412}, {"_path": "include/opentelemetry/sdk/metrics/data/circular_buffer.h", "path_type": "hardlink", "sha256": "44c0c969eaa3f1e4b21603276e9af10d24a65441ef621320ef6169d1a81a13ea", "sha256_in_prefix": "44c0c969eaa3f1e4b21603276e9af10d24a65441ef621320ef6169d1a81a13ea", "size_in_bytes": 5137}, {"_path": "include/opentelemetry/sdk/metrics/data/exemplar_data.h", "path_type": "hardlink", "sha256": "96ab3116aca278dae5f138b2de19d5590b42644d3a4bbdfddeea6d990808336a", "sha256_in_prefix": "96ab3116aca278dae5f138b2de19d5590b42644d3a4bbdfddeea6d990808336a", "size_in_bytes": 2905}, {"_path": "include/opentelemetry/sdk/metrics/data/metric_data.h", "path_type": "hardlink", "sha256": "09628e7246b5c1108769bdc9ab53f9a6ae364b170947173ca523f6de7763c1b1", "sha256_in_prefix": "09628e7246b5c1108769bdc9ab53f9a6ae364b170947173ca523f6de7763c1b1", "size_in_bytes": 1305}, {"_path": "include/opentelemetry/sdk/metrics/data/point_data.h", "path_type": "hardlink", "sha256": "caa11e7705415b7f8fd75c6db3081e329dfa38d348c12eddfa01213037cba9fe", "sha256_in_prefix": "caa11e7705415b7f8fd75c6db3081e329dfa38d348c12eddfa01213037cba9fe", "size_in_bytes": 5168}, {"_path": "include/opentelemetry/sdk/metrics/exemplar/aligned_histogram_bucket_exemplar_reservoir.h", "path_type": "hardlink", "sha256": "a25591df9cbfdd30b518d39dc948a02b6e908040974ddc601bbe40262542bdba", "sha256_in_prefix": "a25591df9cbfdd30b518d39dc948a02b6e908040974ddc601bbe40262542bdba", "size_in_bytes": 2831}, {"_path": "include/opentelemetry/sdk/metrics/exemplar/filter_type.h", "path_type": "hardlink", "sha256": "28abb911d55b5213003fb7de9c175c9a974a175546b5c25ce5104099e09f5d25", "sha256_in_prefix": "28abb911d55b5213003fb7de9c175c9a974a175546b5c25ce5104099e09f5d25", "size_in_bytes": 990}, {"_path": "include/opentelemetry/sdk/metrics/exemplar/fixed_size_exemplar_reservoir.h", "path_type": "hardlink", "sha256": "a58bdaee31a5f2f4384b6ff6874435bc9c02a80aae46347a3ee66d093cfa2ae1", "sha256_in_prefix": "a58bdaee31a5f2f4384b6ff6874435bc9c02a80aae46347a3ee66d093cfa2ae1", "size_in_bytes": 3033}, {"_path": "include/opentelemetry/sdk/metrics/exemplar/no_exemplar_reservoir.h", "path_type": "hardlink", "sha256": "286bb1dbd0e491307cdb186190cee23a55a389632d31c6f2035531cf9bd4d2fd", "sha256_in_prefix": "286bb1dbd0e491307cdb186190cee23a55a389632d31c6f2035531cf9bd4d2fd", "size_in_bytes": 1543}, {"_path": "include/opentelemetry/sdk/metrics/exemplar/reservoir.h", "path_type": "hardlink", "sha256": "dea4d4d92b104f3e5cbe8ada0467cbcb3ae54865c533ab8d8174ffbebfebd0ed", "sha256_in_prefix": "dea4d4d92b104f3e5cbe8ada0467cbcb3ae54865c533ab8d8174ffbebfebd0ed", "size_in_bytes": 2761}, {"_path": "include/opentelemetry/sdk/metrics/exemplar/reservoir_cell.h", "path_type": "hardlink", "sha256": "0c7a83066642262de3832c04b3c68af8383fc309f9867e3e6005b28c76da759c", "sha256_in_prefix": "0c7a83066642262de3832c04b3c68af8383fc309f9867e3e6005b28c76da759c", "size_in_bytes": 4610}, {"_path": "include/opentelemetry/sdk/metrics/exemplar/reservoir_cell_selector.h", "path_type": "hardlink", "sha256": "3116e3b7801afd692906c59ac49e20fb2383cf0527037dbd87f3f506069ef2cf", "sha256_in_prefix": "3116e3b7801afd692906c59ac49e20fb2383cf0527037dbd87f3f506069ef2cf", "size_in_bytes": 1524}, {"_path": "include/opentelemetry/sdk/metrics/exemplar/reservoir_utils.h", "path_type": "hardlink", "sha256": "6e01e40fda84b6037499a9ba4bdc5528b748439d1e0851d05fbb3f4fb23f98d7", "sha256_in_prefix": "6e01e40fda84b6037499a9ba4bdc5528b748439d1e0851d05fbb3f4fb23f98d7", "size_in_bytes": 2288}, {"_path": "include/opentelemetry/sdk/metrics/exemplar/simple_fixed_size_exemplar_reservoir.h", "path_type": "hardlink", "sha256": "a891ba59653072b0c9ce99c9e559ed7c366d9e3efaa0955f7c7c78e5a915f8ef", "sha256_in_prefix": "a891ba59653072b0c9ce99c9e559ed7c366d9e3efaa0955f7c7c78e5a915f8ef", "size_in_bytes": 3264}, {"_path": "include/opentelemetry/sdk/metrics/export/metric_filter.h", "path_type": "hardlink", "sha256": "03720034dcbf45fd1a8778e1b08e435c0359cb8cfdea58d13716babbd6150a23", "sha256_in_prefix": "03720034dcbf45fd1a8778e1b08e435c0359cb8cfdea58d13716babbd6150a23", "size_in_bytes": 3714}, {"_path": "include/opentelemetry/sdk/metrics/export/metric_producer.h", "path_type": "hardlink", "sha256": "7353ac8d90b579ad377dca1e45752ef86fa0e763b43daaa758aeca93db47e993", "sha256_in_prefix": "7353ac8d90b579ad377dca1e45752ef86fa0e763b43daaa758aeca93db47e993", "size_in_bytes": 3121}, {"_path": "include/opentelemetry/sdk/metrics/export/periodic_exporting_metric_reader.h", "path_type": "hardlink", "sha256": "adfe4e63f76ecfc1dfcf8c73059ed245676f94e048bbba20d7b19b86c18446ef", "sha256_in_prefix": "adfe4e63f76ecfc1dfcf8c73059ed245676f94e048bbba20d7b19b86c18446ef", "size_in_bytes": 2387}, {"_path": "include/opentelemetry/sdk/metrics/export/periodic_exporting_metric_reader_factory.h", "path_type": "hardlink", "sha256": "7678bdbf1a437af2b2145a37d10298e83b3e988d7d000813fa17f1947c020fb0", "sha256_in_prefix": "7678bdbf1a437af2b2145a37d10298e83b3e988d7d000813fa17f1947c020fb0", "size_in_bytes": 1086}, {"_path": "include/opentelemetry/sdk/metrics/export/periodic_exporting_metric_reader_options.h", "path_type": "hardlink", "sha256": "72c87a72ada2130c1d0c1ab028e2d4f12f0255e0c3597fe9b6236b1c4b9b6ea6", "sha256_in_prefix": "72c87a72ada2130c1d0c1ab028e2d4f12f0255e0c3597fe9b6236b1c4b9b6ea6", "size_in_bytes": 922}, {"_path": "include/opentelemetry/sdk/metrics/export/periodic_exporting_metric_reader_runtime_options.h", "path_type": "hardlink", "sha256": "6931ec1662a999ae39a8494cbb888828282063e6c7f80b2fce83129a4ee57cd5", "sha256_in_prefix": "6931ec1662a999ae39a8494cbb888828282063e6c7f80b2fce83129a4ee57cd5", "size_in_bytes": 788}, {"_path": "include/opentelemetry/sdk/metrics/instrument_metadata_validator.h", "path_type": "hardlink", "sha256": "ce9d65dc434f006becdfc5441362d0c0a07ce2fcc701bd65a0a1ae8d8f7bbc2d", "sha256_in_prefix": "ce9d65dc434f006becdfc5441362d0c0a07ce2fcc701bd65a0a1ae8d8f7bbc2d", "size_in_bytes": 749}, {"_path": "include/opentelemetry/sdk/metrics/instruments.h", "path_type": "hardlink", "sha256": "5a51948f1d1d660e684510e752128d18786b9a8bdbb97e9d9a680b6453edec61", "sha256_in_prefix": "5a51948f1d1d660e684510e752128d18786b9a8bdbb97e9d9a680b6453edec61", "size_in_bytes": 6253}, {"_path": "include/opentelemetry/sdk/metrics/meter.h", "path_type": "hardlink", "sha256": "6e467b83dc46f1e3141c511da0d4e6f1d802f85722efc520e66d90f45aa7417a", "sha256_in_prefix": "6e467b83dc46f1e3141c511da0d4e6f1d802f85722efc520e66d90f45aa7417a", "size_in_bytes": 8201}, {"_path": "include/opentelemetry/sdk/metrics/meter_config.h", "path_type": "hardlink", "sha256": "9a3d579292b05a55d132477aa212f17d5185ee76adf87753fe9f3ad88dcf92f8", "sha256_in_prefix": "9a3d579292b05a55d132477aa212f17d5185ee76adf87753fe9f3ad88dcf92f8", "size_in_bytes": 1706}, {"_path": "include/opentelemetry/sdk/metrics/meter_context.h", "path_type": "hardlink", "sha256": "a8cb442bc1bfc2710c9d47cc9ed80ef57f1d15bdb3fb020ebb0972726b2b0893", "sha256_in_prefix": "a8cb442bc1bfc2710c9d47cc9ed80ef57f1d15bdb3fb020ebb0972726b2b0893", "size_in_bytes": 6644}, {"_path": "include/opentelemetry/sdk/metrics/meter_context_factory.h", "path_type": "hardlink", "sha256": "08d9e1c9a5616b64a2e0668d999c66ab6b06eab834234aef34b78c70e2def6a4", "sha256_in_prefix": "08d9e1c9a5616b64a2e0668d999c66ab6b06eab834234aef34b78c70e2def6a4", "size_in_bytes": 2170}, {"_path": "include/opentelemetry/sdk/metrics/meter_provider.h", "path_type": "hardlink", "sha256": "06f3b3f110dfa9782951a4a8c37ca639115ed530d0fb63260ba9763f7ae1976f", "sha256_in_prefix": "06f3b3f110dfa9782951a4a8c37ca639115ed530d0fb63260ba9763f7ae1976f", "size_in_bytes": 5245}, {"_path": "include/opentelemetry/sdk/metrics/meter_provider_factory.h", "path_type": "hardlink", "sha256": "91f156c9c7ae08f30d3e66af1f636481a438246095a64ed00f105a77bdb468f0", "sha256_in_prefix": "91f156c9c7ae08f30d3e66af1f636481a438246095a64ed00f105a77bdb468f0", "size_in_bytes": 1416}, {"_path": "include/opentelemetry/sdk/metrics/metric_reader.h", "path_type": "hardlink", "sha256": "6e1c79c253b00d511099a708a3e81c827bbd58bfa3b467256bd6decd7b6c7e38", "sha256_in_prefix": "6e1c79c253b00d511099a708a3e81c827bbd58bfa3b467256bd6decd7b6c7e38", "size_in_bytes": 1822}, {"_path": "include/opentelemetry/sdk/metrics/observer_result.h", "path_type": "hardlink", "sha256": "ee32e0124ef878527a2e6ef3d72a293a80f8e0815c6154e1a30a40cf8fc46d03", "sha256_in_prefix": "ee32e0124ef878527a2e6ef3d72a293a80f8e0815c6154e1a30a40cf8fc46d03", "size_in_bytes": 1522}, {"_path": "include/opentelemetry/sdk/metrics/provider.h", "path_type": "hardlink", "sha256": "dfcda776a5563dc2e82db79aa55d2eeed362362ba254eea14e5f8caddacf0b44", "sha256_in_prefix": "dfcda776a5563dc2e82db79aa55d2eeed362362ba254eea14e5f8caddacf0b44", "size_in_bytes": 585}, {"_path": "include/opentelemetry/sdk/metrics/push_metric_exporter.h", "path_type": "hardlink", "sha256": "a0aae8af60b94a0352946dd54b29d54ba3bb5e951bd9f7222fbd1d4c4879fc68", "sha256_in_prefix": "a0aae8af60b94a0352946dd54b29d54ba3bb5e951bd9f7222fbd1d4c4879fc68", "size_in_bytes": 1590}, {"_path": "include/opentelemetry/sdk/metrics/state/async_metric_storage.h", "path_type": "hardlink", "sha256": "720cbbf01c2e192099257ce86f5e307e67eccc7a05d6a60c54b0cbbea0800f99", "sha256_in_prefix": "720cbbf01c2e192099257ce86f5e307e67eccc7a05d6a60c54b0cbbea0800f99", "size_in_bytes": 5754}, {"_path": "include/opentelemetry/sdk/metrics/state/attributes_hashmap.h", "path_type": "hardlink", "sha256": "a68fbb08581c3527c1942ed6e75d7f3f390569090e7e57a15a19ff75e7c07d3e", "sha256_in_prefix": "a68fbb08581c3527c1942ed6e75d7f3f390569090e7e57a15a19ff75e7c07d3e", "size_in_bytes": 6525}, {"_path": "include/opentelemetry/sdk/metrics/state/filtered_ordered_attribute_map.h", "path_type": "hardlink", "sha256": "4369e0cd939078eb0665c9a8a28da2e6fd9756f2ea4cf0027aaa93dbcfbbf0fb", "sha256_in_prefix": "4369e0cd939078eb0665c9a8a28da2e6fd9756f2ea4cf0027aaa93dbcfbbf0fb", "size_in_bytes": 2944}, {"_path": "include/opentelemetry/sdk/metrics/state/metric_collector.h", "path_type": "hardlink", "sha256": "c63f814e5c29aa414e4e2361caedce6f5f3b94a33c0e044fa20f2f0e19104e1f", "sha256_in_prefix": "c63f814e5c29aa414e4e2361caedce6f5f3b94a33c0e044fa20f2f0e19104e1f", "size_in_bytes": 2046}, {"_path": "include/opentelemetry/sdk/metrics/state/metric_storage.h", "path_type": "hardlink", "sha256": "36d471fdefa12317b335b768dc9c6a5e26a4ac5263466aaf7e72225225601339", "sha256_in_prefix": "36d471fdefa12317b335b768dc9c6a5e26a4ac5263466aaf7e72225225601339", "size_in_bytes": 4672}, {"_path": "include/opentelemetry/sdk/metrics/state/multi_metric_storage.h", "path_type": "hardlink", "sha256": "893f281524499978a3f203c5423e5fa6bbd7602b9f9140d454315ecc806d075f", "sha256_in_prefix": "893f281524499978a3f203c5423e5fa6bbd7602b9f9140d454315ecc806d075f", "size_in_bytes": 2916}, {"_path": "include/opentelemetry/sdk/metrics/state/observable_registry.h", "path_type": "hardlink", "sha256": "bbff68c1442fec544ce49c76e05c9cc1cd1bb826e0c3051ecdae8eeec4f6f376", "sha256_in_prefix": "bbff68c1442fec544ce49c76e05c9cc1cd1bb826e0c3051ecdae8eeec4f6f376", "size_in_bytes": 1269}, {"_path": "include/opentelemetry/sdk/metrics/state/sync_metric_storage.h", "path_type": "hardlink", "sha256": "f9af56fc2a1d1d556a3ad6f1977a487970147de776d0815d68db7d0cf6739027", "sha256_in_prefix": "f9af56fc2a1d1d556a3ad6f1977a487970147de776d0815d68db7d0cf6739027", "size_in_bytes": 7564}, {"_path": "include/opentelemetry/sdk/metrics/state/temporal_metric_storage.h", "path_type": "hardlink", "sha256": "a28252b538d12c4fee5ef0b1b7e8df38f3c1a8c71c3cf0624fbe1550ef8213a8", "sha256_in_prefix": "a28252b538d12c4fee5ef0b1b7e8df38f3c1a8c71c3cf0624fbe1550ef8213a8", "size_in_bytes": 2087}, {"_path": "include/opentelemetry/sdk/metrics/sync_instruments.h", "path_type": "hardlink", "sha256": "96a3b9d606250104edb2ed473bb79336cb19d7961d6a8bf34ae45d35f9dd5f8f", "sha256_in_prefix": "96a3b9d606250104edb2ed473bb79336cb19d7961d6a8bf34ae45d35f9dd5f8f", "size_in_bytes": 6536}, {"_path": "include/opentelemetry/sdk/metrics/view/attributes_processor.h", "path_type": "hardlink", "sha256": "919483682eb7118cd43492813494e4e0c08abc9b4c9dc5fef468bb64980efb13", "sha256_in_prefix": "919483682eb7118cd43492813494e4e0c08abc9b4c9dc5fef468bb64980efb13", "size_in_bytes": 2965}, {"_path": "include/opentelemetry/sdk/metrics/view/instrument_selector.h", "path_type": "hardlink", "sha256": "51c798d08989ed03756426ea2b559d163d633c9cddfcca115604198f1384b87a", "sha256_in_prefix": "51c798d08989ed03756426ea2b559d163d633c9cddfcca115604198f1384b87a", "size_in_bytes": 1489}, {"_path": "include/opentelemetry/sdk/metrics/view/instrument_selector_factory.h", "path_type": "hardlink", "sha256": "19995c3044a50fdce86290d8642dc57f7066a9cec05005e99e5e89f5381e36e4", "sha256_in_prefix": "19995c3044a50fdce86290d8642dc57f7066a9cec05005e99e5e89f5381e36e4", "size_in_bytes": 670}, {"_path": "include/opentelemetry/sdk/metrics/view/meter_selector.h", "path_type": "hardlink", "sha256": "f67456cdbba7eed50ebf2d3fb59ac3d7767fef0a67dbfa7d6da9df2808828bb9", "sha256_in_prefix": "f67456cdbba7eed50ebf2d3fb59ac3d7767fef0a67dbfa7d6da9df2808828bb9", "size_in_bytes": 1512}, {"_path": "include/opentelemetry/sdk/metrics/view/meter_selector_factory.h", "path_type": "hardlink", "sha256": "667ae21a5e5d810fa630478ee98ffaf3cea5fa7f671f97daa8f3d3b2cf4af0dc", "sha256_in_prefix": "667ae21a5e5d810fa630478ee98ffaf3cea5fa7f671f97daa8f3d3b2cf4af0dc", "size_in_bytes": 648}, {"_path": "include/opentelemetry/sdk/metrics/view/predicate.h", "path_type": "hardlink", "sha256": "0fc7f0b30d2cd28b7ce5f37130c398c109254e8e2cc6f8895baff1983518616d", "sha256_in_prefix": "0fc7f0b30d2cd28b7ce5f37130c398c109254e8e2cc6f8895baff1983518616d", "size_in_bytes": 1936}, {"_path": "include/opentelemetry/sdk/metrics/view/predicate_factory.h", "path_type": "hardlink", "sha256": "dca0874c2db8c0bcaf519049073d25ae78b0a05819658cf48380cec770e61b03", "sha256_in_prefix": "dca0874c2db8c0bcaf519049073d25ae78b0a05819658cf48380cec770e61b03", "size_in_bytes": 1169}, {"_path": "include/opentelemetry/sdk/metrics/view/view.h", "path_type": "hardlink", "sha256": "a9a87c52a6a163d6ec4d64c63f751ccd390ed76aeb2540e71423e61219883639", "sha256_in_prefix": "a9a87c52a6a163d6ec4d64c63f751ccd390ed76aeb2540e71423e61219883639", "size_in_bytes": 2247}, {"_path": "include/opentelemetry/sdk/metrics/view/view_factory.h", "path_type": "hardlink", "sha256": "0b558e0442611de953c06d6bb86ecf7d8699b0538c4ce550ed405ec5a64baf42", "sha256_in_prefix": "0b558e0442611de953c06d6bb86ecf7d8699b0538c4ce550ed405ec5a64baf42", "size_in_bytes": 2080}, {"_path": "include/opentelemetry/sdk/metrics/view/view_registry.h", "path_type": "hardlink", "sha256": "2f4f2f9445a1cb65df17393b8956085c6ed3461a4acaf36803422bf2a73d51ff", "sha256_in_prefix": "2f4f2f9445a1cb65df17393b8956085c6ed3461a4acaf36803422bf2a73d51ff", "size_in_bytes": 3979}, {"_path": "include/opentelemetry/sdk/metrics/view/view_registry_factory.h", "path_type": "hardlink", "sha256": "08f701496c3c0c4bef150f7dfa294c2c7c184d02facfd75566fba5714c554d40", "sha256_in_prefix": "08f701496c3c0c4bef150f7dfa294c2c7c184d02facfd75566fba5714c554d40", "size_in_bytes": 432}, {"_path": "include/opentelemetry/sdk/resource/resource.h", "path_type": "hardlink", "sha256": "d9b9393e22564c02a85495c61a1bdd1fff887cef70557397e0510e3964f895c9", "sha256_in_prefix": "d9b9393e22564c02a85495c61a1bdd1fff887cef70557397e0510e3964f895c9", "size_in_bytes": 2171}, {"_path": "include/opentelemetry/sdk/resource/resource_detector.h", "path_type": "hardlink", "sha256": "e2451b74714cb722acdffd0482b46c6e946235a7b43b3595a2efbda9d82caacc", "sha256_in_prefix": "e2451b74714cb722acdffd0482b46c6e946235a7b43b3595a2efbda9d82caacc", "size_in_bytes": 930}, {"_path": "include/opentelemetry/sdk/resource/semantic_conventions.h", "path_type": "hardlink", "sha256": "88e12dbc2635903a2955e62c3fda34f053cbe2fbb16d0ae7688cebf2c8d93fcf", "sha256_in_prefix": "88e12dbc2635903a2955e62c3fda34f053cbe2fbb16d0ae7688cebf2c8d93fcf", "size_in_bytes": 186279}, {"_path": "include/opentelemetry/sdk/trace/batch_span_processor.h", "path_type": "hardlink", "sha256": "95156d4af41beed2ae338f88f22a97a45e338752b6e7858211a165bcf3e8eaf1", "sha256_in_prefix": "95156d4af41beed2ae338f88f22a97a45e338752b6e7858211a165bcf3e8eaf1", "size_in_bytes": 6440}, {"_path": "include/opentelemetry/sdk/trace/batch_span_processor_factory.h", "path_type": "hardlink", "sha256": "ed34d0f2a446c185d1b4eec27a4ee94aa3112b4f5531ae90b53de61409161ab4", "sha256_in_prefix": "ed34d0f2a446c185d1b4eec27a4ee94aa3112b4f5531ae90b53de61409161ab4", "size_in_bytes": 1112}, {"_path": "include/opentelemetry/sdk/trace/batch_span_processor_options.h", "path_type": "hardlink", "sha256": "35073958bc8542ba44ffcf6f1cd6d2f4ce0eb4c7d8563632e73eb51e88a1678d", "sha256_in_prefix": "35073958bc8542ba44ffcf6f1cd6d2f4ce0eb4c7d8563632e73eb51e88a1678d", "size_in_bytes": 802}, {"_path": "include/opentelemetry/sdk/trace/batch_span_processor_runtime_options.h", "path_type": "hardlink", "sha256": "009e96a959d13da644ee5a73566bddba4e719929dbccc93d699b3276bda44561", "sha256_in_prefix": "009e96a959d13da644ee5a73566bddba4e719929dbccc93d699b3276bda44561", "size_in_bytes": 601}, {"_path": "include/opentelemetry/sdk/trace/exporter.h", "path_type": "hardlink", "sha256": "aedecc0334b240275df1fe1d2f0b2c989afaa6e5193556c2049d97c65352b21b", "sha256_in_prefix": "aedecc0334b240275df1fe1d2f0b2c989afaa6e5193556c2049d97c65352b21b", "size_in_bytes": 2014}, {"_path": "include/opentelemetry/sdk/trace/id_generator.h", "path_type": "hardlink", "sha256": "f75b04ab49531ff6dcb55a2400ee948902e7b5d8efa9d0ff0302c34968e6bcca", "sha256_in_prefix": "f75b04ab49531ff6dcb55a2400ee948902e7b5d8efa9d0ff0302c34968e6bcca", "size_in_bytes": 1035}, {"_path": "include/opentelemetry/sdk/trace/multi_recordable.h", "path_type": "hardlink", "sha256": "093edaa8c4690207f82471ecffa038d3fd1f3343f5248b5e1f726e86e6767f9e", "sha256_in_prefix": "093edaa8c4690207f82471ecffa038d3fd1f3343f5248b5e1f726e86e6767f9e", "size_in_bytes": 4363}, {"_path": "include/opentelemetry/sdk/trace/multi_span_processor.h", "path_type": "hardlink", "sha256": "8d9963fa40b23699fa1b7a6c9c86e5c0366958bb599a1ca842d4b84d1bd10581", "sha256_in_prefix": "8d9963fa40b23699fa1b7a6c9c86e5c0366958bb599a1ca842d4b84d1bd10581", "size_in_bytes": 4611}, {"_path": "include/opentelemetry/sdk/trace/processor.h", "path_type": "hardlink", "sha256": "203f2fe827548325e1c0ddad29c135f43ec597f3b5ed8a2446f31d4a0be795ab", "sha256_in_prefix": "203f2fe827548325e1c0ddad29c135f43ec597f3b5ed8a2446f31d4a0be795ab", "size_in_bytes": 2288}, {"_path": "include/opentelemetry/sdk/trace/provider.h", "path_type": "hardlink", "sha256": "42668e2512453cec9b351621e2ea310037591ac65aac2ca2c7f97d5d5dc26291", "sha256_in_prefix": "42668e2512453cec9b351621e2ea310037591ac65aac2ca2c7f97d5d5dc26291", "size_in_bytes": 634}, {"_path": "include/opentelemetry/sdk/trace/random_id_generator.h", "path_type": "hardlink", "sha256": "a3e9170d847660ea6a76ac6499e692d88eab44bb9b6b5e6a58ac6298873a2b60", "sha256_in_prefix": "a3e9170d847660ea6a76ac6499e692d88eab44bb9b6b5e6a58ac6298873a2b60", "size_in_bytes": 639}, {"_path": "include/opentelemetry/sdk/trace/random_id_generator_factory.h", "path_type": "hardlink", "sha256": "e278a75066d630327ea38ee915c0102a4de769e8ca5468f3969cd2c4a1b26cf9", "sha256_in_prefix": "e278a75066d630327ea38ee915c0102a4de769e8ca5468f3969cd2c4a1b26cf9", "size_in_bytes": 519}, {"_path": "include/opentelemetry/sdk/trace/recordable.h", "path_type": "hardlink", "sha256": "45b2699668f8c823c66e5cecd8c372a7cc2add8a14096227f0c995453f1f0565", "sha256_in_prefix": "45b2699668f8c823c66e5cecd8c372a7cc2add8a14096227f0c995453f1f0565", "size_in_bytes": 5773}, {"_path": "include/opentelemetry/sdk/trace/sampler.h", "path_type": "hardlink", "sha256": "8e1d08a62418d1a70970d4928f32a4642bb7574033a2859d406cd425f0f9690b", "sha256_in_prefix": "8e1d08a62418d1a70970d4928f32a4642bb7574033a2859d406cd425f0f9690b", "size_in_bytes": 3328}, {"_path": "include/opentelemetry/sdk/trace/samplers/always_off.h", "path_type": "hardlink", "sha256": "4e3a952514b847603730e99a5d8bf6cf1b812bb4a1cc92e84fb6cf0fd71f4a90", "sha256_in_prefix": "4e3a952514b847603730e99a5d8bf6cf1b812bb4a1cc92e84fb6cf0fd71f4a90", "size_in_bytes": 1473}, {"_path": "include/opentelemetry/sdk/trace/samplers/always_off_factory.h", "path_type": "hardlink", "sha256": "2effe0b8f34773d216be14e622014f2ae75678992aea1ee14bee292fa5cb1490", "sha256_in_prefix": "2effe0b8f34773d216be14e622014f2ae75678992aea1ee14bee292fa5cb1490", "size_in_bytes": 508}, {"_path": "include/opentelemetry/sdk/trace/samplers/always_on.h", "path_type": "hardlink", "sha256": "faeaf602e0ca35db739fafeddfeac896f4583c8bda9785b34852caa82ddcf16a", "sha256_in_prefix": "faeaf602e0ca35db739fafeddfeac896f4583c8bda9785b34852caa82ddcf16a", "size_in_bytes": 1673}, {"_path": "include/opentelemetry/sdk/trace/samplers/always_on_factory.h", "path_type": "hardlink", "sha256": "201f22130b064ec676cf789af79f6daf8e1586db9bbdc4b7d9f2e0173b3a21d8", "sha256_in_prefix": "201f22130b064ec676cf789af79f6daf8e1586db9bbdc4b7d9f2e0173b3a21d8", "size_in_bytes": 505}, {"_path": "include/opentelemetry/sdk/trace/samplers/parent.h", "path_type": "hardlink", "sha256": "0bc0fb1d65654ffaf0a37f0cd6fd91347844f2256c06facc942669bed3cf6e47", "sha256_in_prefix": "0bc0fb1d65654ffaf0a37f0cd6fd91347844f2256c06facc942669bed3cf6e47", "size_in_bytes": 1703}, {"_path": "include/opentelemetry/sdk/trace/samplers/parent_factory.h", "path_type": "hardlink", "sha256": "d15d0dfab49c645cbb1c82c097ff74f0e6c3a1fd03257e635b68438aa66131f5", "sha256_in_prefix": "d15d0dfab49c645cbb1c82c097ff74f0e6c3a1fd03257e635b68438aa66131f5", "size_in_bytes": 561}, {"_path": "include/opentelemetry/sdk/trace/samplers/trace_id_ratio.h", "path_type": "hardlink", "sha256": "f2f8b767caf8c49b003ba2d0d41c7f68f8602029753d5a40f2e9dea2d868616c", "sha256_in_prefix": "f2f8b767caf8c49b003ba2d0d41c7f68f8602029753d5a40f2e9dea2d868616c", "size_in_bytes": 1889}, {"_path": "include/opentelemetry/sdk/trace/samplers/trace_id_ratio_factory.h", "path_type": "hardlink", "sha256": "ed13a72b4de50d0975a290f591da50c832130c4bcbbfba7b700e7779e3dbd237", "sha256_in_prefix": "ed13a72b4de50d0975a290f591da50c832130c4bcbbfba7b700e7779e3dbd237", "size_in_bytes": 543}, {"_path": "include/opentelemetry/sdk/trace/simple_processor.h", "path_type": "hardlink", "sha256": "d602982fdaa5e8acc2c138713470328251c7b4efa04364d5c152c74a5a04646a", "sha256_in_prefix": "d602982fdaa5e8acc2c138713470328251c7b4efa04364d5c152c74a5a04646a", "size_in_bytes": 2835}, {"_path": "include/opentelemetry/sdk/trace/simple_processor_factory.h", "path_type": "hardlink", "sha256": "28bb0449e3260df809a78fb0fdf60186507a3d0de6b47de7950130edca66ac56", "sha256_in_prefix": "28bb0449e3260df809a78fb0fdf60186507a3d0de6b47de7950130edca66ac56", "size_in_bytes": 631}, {"_path": "include/opentelemetry/sdk/trace/span_data.h", "path_type": "hardlink", "sha256": "5bd80545a775543d2c661f5638e7646f12186a1f025dc60c93a80770e519a8dd", "sha256_in_prefix": "5bd80545a775543d2c661f5638e7646f12186a1f025dc60c93a80770e519a8dd", "size_in_bytes": 10345}, {"_path": "include/opentelemetry/sdk/trace/tracer.h", "path_type": "hardlink", "sha256": "8c7538e3d0febde4f14babd810387aed55b5a736167c9fcdc1c86ce0668f3c42", "sha256_in_prefix": "8c7538e3d0febde4f14babd810387aed55b5a736167c9fcdc1c86ce0668f3c42", "size_in_bytes": 4096}, {"_path": "include/opentelemetry/sdk/trace/tracer_config.h", "path_type": "hardlink", "sha256": "83c807ebc96011e68c1828abd1543f2953baa7d437febccccbce480d3ceab428", "sha256_in_prefix": "83c807ebc96011e68c1828abd1543f2953baa7d437febccccbce480d3ceab428", "size_in_bytes": 1730}, {"_path": "include/opentelemetry/sdk/trace/tracer_context.h", "path_type": "hardlink", "sha256": "352710f68aa88ff25bdc032c1fdce5badaa28be142ca7fcbb0b79e3ef2793671", "sha256_in_prefix": "352710f68aa88ff25bdc032c1fdce5badaa28be142ca7fcbb0b79e3ef2793671", "size_in_bytes": 4506}, {"_path": "include/opentelemetry/sdk/trace/tracer_context_factory.h", "path_type": "hardlink", "sha256": "b45fed3539c12ba906b1a12e34b3c4d848ad76dbb2dbd844dca914b4f5201aeb", "sha256_in_prefix": "b45fed3539c12ba906b1a12e34b3c4d848ad76dbb2dbd844dca914b4f5201aeb", "size_in_bytes": 1988}, {"_path": "include/opentelemetry/sdk/trace/tracer_provider.h", "path_type": "hardlink", "sha256": "16598816c1b8fa79f8ba1ee23419088d6b28edafe43131e623001da41cd94628", "sha256_in_prefix": "16598816c1b8fa79f8ba1ee23419088d6b28edafe43131e623001da41cd94628", "size_in_bytes": 5293}, {"_path": "include/opentelemetry/sdk/trace/tracer_provider_factory.h", "path_type": "hardlink", "sha256": "2c1ad4fcb00034342970a48bb6bd3aa2ff25f5f5a8a92ce2ecbd105eed0b93d4", "sha256_in_prefix": "2c1ad4fcb00034342970a48bb6bd3aa2ff25f5f5a8a92ce2ecbd105eed0b93d4", "size_in_bytes": 3536}, {"_path": "include/opentelemetry/sdk/version/version.h", "path_type": "hardlink", "sha256": "0713421dbdb4a57bdbf115843967e4cb5ffb3d3e42f232b291e9b56b6da7ea53", "sha256_in_prefix": "0713421dbdb4a57bdbf115843967e4cb5ffb3d3e42f232b291e9b56b6da7ea53", "size_in_bytes": 572}, {"_path": "include/opentelemetry/sdk_config.h", "path_type": "hardlink", "sha256": "bbabc9d4c545e3c0cdc3374cecf04692c7cead1961b0bd073ef6df6dcf37595c", "sha256_in_prefix": "bbabc9d4c545e3c0cdc3374cecf04692c7cead1961b0bd073ef6df6dcf37595c", "size_in_bytes": 150}, {"_path": "include/opentelemetry/semconv/azure_metrics.h", "path_type": "hardlink", "sha256": "a45e12fb5ce3530bbcb812a9a9edde9c91c397ad96945c03c5c8682f80b4ec56", "sha256_in_prefix": "a45e12fb5ce3530bbcb812a9a9edde9c91c397ad96945c03c5c8682f80b4ec56", "size_in_bytes": 3934}, {"_path": "include/opentelemetry/semconv/cicd_metrics.h", "path_type": "hardlink", "sha256": "995d71cb927737c5a01eaf657e39ec678a8900ead8e55c119cb50619edea1940", "sha256_in_prefix": "995d71cb927737c5a01eaf657e39ec678a8900ead8e55c119cb50619edea1940", "size_in_bytes": 8772}, {"_path": "include/opentelemetry/semconv/client_attributes.h", "path_type": "hardlink", "sha256": "bbc5c15c8e6267cc13f16732d2804edd6354e76bea828fcbf5291fae4f239515", "sha256_in_prefix": "bbc5c15c8e6267cc13f16732d2804edd6354e76bea828fcbf5291fae4f239515", "size_in_bytes": 1199}, {"_path": "include/opentelemetry/semconv/code_attributes.h", "path_type": "hardlink", "sha256": "8080318aef560311d2604b8d11cb1a392dbf41104add411943b79042f7cb9609", "sha256_in_prefix": "8080318aef560311d2604b8d11cb1a392dbf41104add411943b79042f7cb9609", "size_in_bytes": 3696}, {"_path": "include/opentelemetry/semconv/container_metrics.h", "path_type": "hardlink", "sha256": "0a231bc146eb5afa7b385597c06eaf0427a8dffed1fae14a7fb62ad27b3071ce", "sha256_in_prefix": "0a231bc146eb5afa7b385597c06eaf0427a8dffed1fae14a7fb62ad27b3071ce", "size_in_bytes": 10218}, {"_path": "include/opentelemetry/semconv/cpu_metrics.h", "path_type": "hardlink", "sha256": "f4578c0b428aca8a74c20c0d884c4883b0e7e189a5b932a43b42ed3d488abd6f", "sha256_in_prefix": "f4578c0b428aca8a74c20c0d884c4883b0e7e189a5b932a43b42ed3d488abd6f", "size_in_bytes": 4968}, {"_path": "include/opentelemetry/semconv/db_attributes.h", "path_type": "hardlink", "sha256": "7433a7101ba400fb31a1a3d1cb7e262c84ef028eb183d1ae7dbc0be1250f69e2", "sha256_in_prefix": "7433a7101ba400fb31a1a3d1cb7e262c84ef028eb183d1ae7dbc0be1250f69e2", "size_in_bytes": 10985}, {"_path": "include/opentelemetry/semconv/db_metrics.h", "path_type": "hardlink", "sha256": "4ce0e2802c9c5034c5e3e73308d09402db9197a1ab81b3e5477467a713e662b8", "sha256_in_prefix": "4ce0e2802c9c5034c5e3e73308d09402db9197a1ab81b3e5477467a713e662b8", "size_in_bytes": 1633}, {"_path": "include/opentelemetry/semconv/dns_metrics.h", "path_type": "hardlink", "sha256": "20aa2b5751d90b71ba407df087c7e49bddeb0fc861a5a2dfcaf1963cb20987ae", "sha256_in_prefix": "20aa2b5751d90b71ba407df087c7e49bddeb0fc861a5a2dfcaf1963cb20987ae", "size_in_bytes": 1421}, {"_path": "include/opentelemetry/semconv/error_attributes.h", "path_type": "hardlink", "sha256": "9a136eb465a496d9cce89b9d1674d430044c4175a2777a5f39ecaa1380ed257d", "sha256_in_prefix": "9a136eb465a496d9cce89b9d1674d430044c4175a2777a5f39ecaa1380ed257d", "size_in_bytes": 1897}, {"_path": "include/opentelemetry/semconv/exception_attributes.h", "path_type": "hardlink", "sha256": "c493e741fce506459fc205b894dfc72c0a6932e4e17ca51735896e4389e6145f", "sha256_in_prefix": "c493e741fce506459fc205b894dfc72c0a6932e4e17ca51735896e4389e6145f", "size_in_bytes": 1406}, {"_path": "include/opentelemetry/semconv/faas_metrics.h", "path_type": "hardlink", "sha256": "f5a9495a7b166192759d5630c3fd809de5749a0b8b6924bd8fcb196a7ccda591", "sha256_in_prefix": "f5a9495a7b166192759d5630c3fd809de5749a0b8b6924bd8fcb196a7ccda591", "size_in_bytes": 10538}, {"_path": "include/opentelemetry/semconv/gen_ai_metrics.h", "path_type": "hardlink", "sha256": "810fec1d96212697e4d4854f15518faa84fa12c5c51dc82e890c6e83baa73b6f", "sha256_in_prefix": "810fec1d96212697e4d4854f15518faa84fa12c5c51dc82e890c6e83baa73b6f", "size_in_bytes": 6159}, {"_path": "include/opentelemetry/semconv/http_attributes.h", "path_type": "hardlink", "sha256": "28953cf091b80bfea00e2d9f3b67c5cf0c48d7026d36ef729014fa88d4f8117d", "sha256_in_prefix": "28953cf091b80bfea00e2d9f3b67c5cf0c48d7026d36ef729014fa88d4f8117d", "size_in_bytes": 6753}, {"_path": "include/opentelemetry/semconv/http_metrics.h", "path_type": "hardlink", "sha256": "755ee1b4b4f59da403fb054af666682f2cd533f575c73aef8d5132261fe9ef96", "sha256_in_prefix": "755ee1b4b4f59da403fb054af666682f2cd533f575c73aef8d5132261fe9ef96", "size_in_bytes": 2619}, {"_path": "include/opentelemetry/semconv/hw_metrics.h", "path_type": "hardlink", "sha256": "6ba9295815f06ad216e5503d8d6087960a92996328311d36be26281eab38f168", "sha256_in_prefix": "6ba9295815f06ad216e5503d8d6087960a92996328311d36be26281eab38f168", "size_in_bytes": 13723}, {"_path": "include/opentelemetry/semconv/incubating/app_attributes.h", "path_type": "hardlink", "sha256": "7167d635e36309a91b0b214de5d3d6a559e16ce87ba31a5917cf27e148b38643", "sha256_in_prefix": "7167d635e36309a91b0b214de5d3d6a559e16ce87ba31a5917cf27e148b38643", "size_in_bytes": 3068}, {"_path": "include/opentelemetry/semconv/incubating/artifact_attributes.h", "path_type": "hardlink", "sha256": "4dfae3f151ef6e49b7772afe6cf67d9bd4c57ce6e90f1c80abc86019365b1f98", "sha256_in_prefix": "4dfae3f151ef6e49b7772afe6cf67d9bd4c57ce6e90f1c80abc86019365b1f98", "size_in_bytes": 3285}, {"_path": "include/opentelemetry/semconv/incubating/aws_attributes.h", "path_type": "hardlink", "sha256": "c5684b9fde7a73ca772c63cd3511a86ff5f9004db9f26bacf9395037d8130059", "sha256_in_prefix": "c5684b9fde7a73ca772c63cd3511a86ff5f9004db9f26bacf9395037d8130059", "size_in_bytes": 16714}, {"_path": "include/opentelemetry/semconv/incubating/az_attributes.h", "path_type": "hardlink", "sha256": "c52c5eca13905bbada0b1dd33a08778285682c0b4a66da6c309f5c4dccb2a87e", "sha256_in_prefix": "c52c5eca13905bbada0b1dd33a08778285682c0b4a66da6c309f5c4dccb2a87e", "size_in_bytes": 912}, {"_path": "include/opentelemetry/semconv/incubating/azure_attributes.h", "path_type": "hardlink", "sha256": "0d6c948fc2074103cd077556c391cd47d43534b916b0808bb81753cedcecc7a2", "sha256_in_prefix": "0d6c948fc2074103cd077556c391cd47d43534b916b0808bb81753cedcecc7a2", "size_in_bytes": 2742}, {"_path": "include/opentelemetry/semconv/incubating/azure_metrics.h", "path_type": "hardlink", "sha256": "6b9083461a99cc0a5872ce6b8337423066340303e43d56a70a038745e0d98c33", "sha256_in_prefix": "6b9083461a99cc0a5872ce6b8337423066340303e43d56a70a038745e0d98c33", "size_in_bytes": 3929}, {"_path": "include/opentelemetry/semconv/incubating/browser_attributes.h", "path_type": "hardlink", "sha256": "3d6fddda96b7377cf497c7de905049a2746651727043c19cd059531e37c0ec6d", "sha256_in_prefix": "3d6fddda96b7377cf497c7de905049a2746651727043c19cd059531e37c0ec6d", "size_in_bytes": 2368}, {"_path": "include/opentelemetry/semconv/incubating/cassandra_attributes.h", "path_type": "hardlink", "sha256": "a318c804ed78ceca6130fe315f095e97b098b5e8925d7445339b05099f01a4cf", "sha256_in_prefix": "a318c804ed78ceca6130fe315f095e97b098b5e8925d7445339b05099f01a4cf", "size_in_bytes": 2404}, {"_path": "include/opentelemetry/semconv/incubating/cicd_attributes.h", "path_type": "hardlink", "sha256": "09e6d6a0fd52c707c645f022be03d9d4b590313e51ff4db16c5cd154453b0942", "sha256_in_prefix": "09e6d6a0fd52c707c645f022be03d9d4b590313e51ff4db16c5cd154453b0942", "size_in_bytes": 6921}, {"_path": "include/opentelemetry/semconv/incubating/cicd_metrics.h", "path_type": "hardlink", "sha256": "256fc914ac4749300110bf2c897feb3b126bb427e68477d7d72c522ed482887f", "sha256_in_prefix": "256fc914ac4749300110bf2c897feb3b126bb427e68477d7d72c522ed482887f", "size_in_bytes": 8753}, {"_path": "include/opentelemetry/semconv/incubating/client_attributes.h", "path_type": "hardlink", "sha256": "bbc5c15c8e6267cc13f16732d2804edd6354e76bea828fcbf5291fae4f239515", "sha256_in_prefix": "bbc5c15c8e6267cc13f16732d2804edd6354e76bea828fcbf5291fae4f239515", "size_in_bytes": 1199}, {"_path": "include/opentelemetry/semconv/incubating/cloud_attributes.h", "path_type": "hardlink", "sha256": "0296e083f1317351a0212648b67191e9ec871929be2faf4ae1450966aa199311", "sha256_in_prefix": "0296e083f1317351a0212648b67191e9ec871929be2faf4ae1450966aa199311", "size_in_bytes": 8104}, {"_path": "include/opentelemetry/semconv/incubating/cloudevents_attributes.h", "path_type": "hardlink", "sha256": "f3e94e06d3730827497324de96f94a7a32905ed56e66a57fb268e23dfa783e55", "sha256_in_prefix": "f3e94e06d3730827497324de96f94a7a32905ed56e66a57fb268e23dfa783e55", "size_in_bytes": 1768}, {"_path": "include/opentelemetry/semconv/incubating/cloudfoundry_attributes.h", "path_type": "hardlink", "sha256": "5d406759ef0c96860b452a8e1ce7984773f858ab720ddb839536d502eb7e27b3", "sha256_in_prefix": "5d406759ef0c96860b452a8e1ce7984773f858ab720ddb839536d502eb7e27b3", "size_in_bytes": 5294}, {"_path": "include/opentelemetry/semconv/incubating/code_attributes.h", "path_type": "hardlink", "sha256": "a34b9186ba7656afa25c8a9a076cfb3297a6f2357c00d0b0ad87ab156429c756", "sha256_in_prefix": "a34b9186ba7656afa25c8a9a076cfb3297a6f2357c00d0b0ad87ab156429c756", "size_in_bytes": 5208}, {"_path": "include/opentelemetry/semconv/incubating/container_attributes.h", "path_type": "hardlink", "sha256": "73961362b64b810a481823f0d578ddd792813552ff5d122c3e4c29d87cbdcb54", "sha256_in_prefix": "73961362b64b810a481823f0d578ddd792813552ff5d122c3e4c29d87cbdcb54", "size_in_bytes": 5767}, {"_path": "include/opentelemetry/semconv/incubating/container_metrics.h", "path_type": "hardlink", "sha256": "9651d9ac603f2766584526d7e3c23c93191c24dd5a8686c579efbcb19072836e", "sha256_in_prefix": "9651d9ac603f2766584526d7e3c23c93191c24dd5a8686c579efbcb19072836e", "size_in_bytes": 10188}, {"_path": "include/opentelemetry/semconv/incubating/cpu_attributes.h", "path_type": "hardlink", "sha256": "0b28e800ab0cafa1f2636c005ffdff960088424ebcf2b317fe46c94436b21ad7", "sha256_in_prefix": "0b28e800ab0cafa1f2636c005ffdff960088424ebcf2b317fe46c94436b21ad7", "size_in_bytes": 1223}, {"_path": "include/opentelemetry/semconv/incubating/cpu_metrics.h", "path_type": "hardlink", "sha256": "70a538c27d942bf631a564fe1acd35a118f71be3816c35c3cab389ef2da8b9ec", "sha256_in_prefix": "70a538c27d942bf631a564fe1acd35a118f71be3816c35c3cab389ef2da8b9ec", "size_in_bytes": 4960}, {"_path": "include/opentelemetry/semconv/incubating/cpython_attributes.h", "path_type": "hardlink", "sha256": "e675554cf77de7188a8737a83e88a2dfc9f17990e017600602837661e2583a34", "sha256_in_prefix": "e675554cf77de7188a8737a83e88a2dfc9f17990e017600602837661e2583a34", "size_in_bytes": 877}, {"_path": "include/opentelemetry/semconv/incubating/cpython_metrics.h", "path_type": "hardlink", "sha256": "9e461916a5b63c483f245b829a805df7fcee187df466e2455d0934625ff3fe0c", "sha256_in_prefix": "9e461916a5b63c483f245b829a805df7fcee187df466e2455d0934625ff3fe0c", "size_in_bytes": 6449}, {"_path": "include/opentelemetry/semconv/incubating/db_attributes.h", "path_type": "hardlink", "sha256": "78080c7ed8f49b1cfa3732d54860d71fd98cdb2e96deafdcf45d2ae4a18599f8", "sha256_in_prefix": "78080c7ed8f49b1cfa3732d54860d71fd98cdb2e96deafdcf45d2ae4a18599f8", "size_in_bytes": 32066}, {"_path": "include/opentelemetry/semconv/incubating/db_metrics.h", "path_type": "hardlink", "sha256": "e629c4fba0fbad83271033fa2d43cd1a72146133dc4a8a85eeaf3de0426abe58", "sha256_in_prefix": "e629c4fba0fbad83271033fa2d43cd1a72146133dc4a8a85eeaf3de0426abe58", "size_in_bytes": 40852}, {"_path": "include/opentelemetry/semconv/incubating/deployment_attributes.h", "path_type": "hardlink", "sha256": "35c15ca67c5df997c00557db1942ce4ccc36b57594d141bb81639bf63bda599d", "sha256_in_prefix": "35c15ca67c5df997c00557db1942ce4ccc36b57594d141bb81639bf63bda599d", "size_in_bytes": 2161}, {"_path": "include/opentelemetry/semconv/incubating/destination_attributes.h", "path_type": "hardlink", "sha256": "3d1706899a97e6ad12dc292ed7857dc49ddc22e9861393ee28874e176c016eea", "sha256_in_prefix": "3d1706899a97e6ad12dc292ed7857dc49ddc22e9861393ee28874e176c016eea", "size_in_bytes": 1023}, {"_path": "include/opentelemetry/semconv/incubating/device_attributes.h", "path_type": "hardlink", "sha256": "4a47b720eb0409fb708888a54513885edd2c7202f6ceafc07985a9df9b717587", "sha256_in_prefix": "4a47b720eb0409fb708888a54513885edd2c7202f6ceafc07985a9df9b717587", "size_in_bytes": 2748}, {"_path": "include/opentelemetry/semconv/incubating/disk_attributes.h", "path_type": "hardlink", "sha256": "ddef8a11a06a6c4578ad98e679cc2b235b35c00c2930d1736b33d5f0ede81b5c", "sha256_in_prefix": "ddef8a11a06a6c4578ad98e679cc2b235b35c00c2930d1736b33d5f0ede81b5c", "size_in_bytes": 769}, {"_path": "include/opentelemetry/semconv/incubating/dns_attributes.h", "path_type": "hardlink", "sha256": "f3c7d029eacde0211b0ec6e4a99778eef88c6e528e94c64fb5526190b88625af", "sha256_in_prefix": "f3c7d029eacde0211b0ec6e4a99778eef88c6e528e94c64fb5526190b88625af", "size_in_bytes": 861}, {"_path": "include/opentelemetry/semconv/incubating/dns_metrics.h", "path_type": "hardlink", "sha256": "ca4db55cf372702d9ed309dbd4faf17db9cc68f80d30835d5425e715061d3fa6", "sha256_in_prefix": "ca4db55cf372702d9ed309dbd4faf17db9cc68f80d30835d5425e715061d3fa6", "size_in_bytes": 1418}, {"_path": "include/opentelemetry/semconv/incubating/elasticsearch_attributes.h", "path_type": "hardlink", "sha256": "16b452ae50d9bfaa278202cc3a0e9e65ae192dd1fdf6dc73fb1dbc862d57d218", "sha256_in_prefix": "16b452ae50d9bfaa278202cc3a0e9e65ae192dd1fdf6dc73fb1dbc862d57d218", "size_in_bytes": 662}, {"_path": "include/opentelemetry/semconv/incubating/enduser_attributes.h", "path_type": "hardlink", "sha256": "43ac70915d120e45317c530efc05ba71dbc6849ef1838a55bdd7ca18961be0a3", "sha256_in_prefix": "43ac70915d120e45317c530efc05ba71dbc6849ef1838a55bdd7ca18961be0a3", "size_in_bytes": 1649}, {"_path": "include/opentelemetry/semconv/incubating/error_attributes.h", "path_type": "hardlink", "sha256": "f9e8149190343f6cc4d0c92b8a422754646c2a29815b3fb0fa58f7d8ecb431eb", "sha256_in_prefix": "f9e8149190343f6cc4d0c92b8a422754646c2a29815b3fb0fa58f7d8ecb431eb", "size_in_bytes": 2511}, {"_path": "include/opentelemetry/semconv/incubating/event_attributes.h", "path_type": "hardlink", "sha256": "e5a23bc84e113c6acd449b03df77ab8f992f70bc73ff2e72fa48c3fade31379b", "sha256_in_prefix": "e5a23bc84e113c6acd449b03df77ab8f992f70bc73ff2e72fa48c3fade31379b", "size_in_bytes": 705}, {"_path": "include/opentelemetry/semconv/incubating/exception_attributes.h", "path_type": "hardlink", "sha256": "c493e741fce506459fc205b894dfc72c0a6932e4e17ca51735896e4389e6145f", "sha256_in_prefix": "c493e741fce506459fc205b894dfc72c0a6932e4e17ca51735896e4389e6145f", "size_in_bytes": 1406}, {"_path": "include/opentelemetry/semconv/incubating/faas_attributes.h", "path_type": "hardlink", "sha256": "ed33af5542e952dc07e3a5e40fa2feab2acf8a8f2bcd7430d84e3efa2c588747", "sha256_in_prefix": "ed33af5542e952dc07e3a5e40fa2feab2acf8a8f2bcd7430d84e3efa2c588747", "size_in_bytes": 7326}, {"_path": "include/opentelemetry/semconv/incubating/faas_metrics.h", "path_type": "hardlink", "sha256": "0042a2924c22efb518b00829a772a056551b1df37b7af2168275da0bdf3f8f15", "sha256_in_prefix": "0042a2924c22efb518b00829a772a056551b1df37b7af2168275da0bdf3f8f15", "size_in_bytes": 10511}, {"_path": "include/opentelemetry/semconv/incubating/feature_flag_attributes.h", "path_type": "hardlink", "sha256": "790afc0ae0c6449553b7ad4fb22cadf0588cd4124fc15a203aeb5eacbb0e9e5d", "sha256_in_prefix": "790afc0ae0c6449553b7ad4fb22cadf0588cd4124fc15a203aeb5eacbb0e9e5d", "size_in_bytes": 6260}, {"_path": "include/opentelemetry/semconv/incubating/file_attributes.h", "path_type": "hardlink", "sha256": "572740de44f33c7cfbe743bd84204ef8f13ac3d85737a46da42e02feeea2c694", "sha256_in_prefix": "572740de44f33c7cfbe743bd84204ef8f13ac3d85737a46da42e02feeea2c694", "size_in_bytes": 4662}, {"_path": "include/opentelemetry/semconv/incubating/gcp_attributes.h", "path_type": "hardlink", "sha256": "bee3ce51057623fb78620b76b277afdc28ef47e2b2ce8aeddcf0e0458048b17c", "sha256_in_prefix": "bee3ce51057623fb78620b76b277afdc28ef47e2b2ce8aeddcf0e0458048b17c", "size_in_bytes": 6041}, {"_path": "include/opentelemetry/semconv/incubating/gen_ai_attributes.h", "path_type": "hardlink", "sha256": "a040c18c5cdaac2a614c289c2cae78f7fd847b9712ac288dcdd357e51224bd8e", "sha256_in_prefix": "a040c18c5cdaac2a614c289c2cae78f7fd847b9712ac288dcdd357e51224bd8e", "size_in_bytes": 14088}, {"_path": "include/opentelemetry/semconv/incubating/gen_ai_metrics.h", "path_type": "hardlink", "sha256": "9e685bf7ff439009c5dc019c808e43b0c8d3d8dc4c5ccd03c9a362cb974b9b78", "sha256_in_prefix": "9e685bf7ff439009c5dc019c808e43b0c8d3d8dc4c5ccd03c9a362cb974b9b78", "size_in_bytes": 6144}, {"_path": "include/opentelemetry/semconv/incubating/geo_attributes.h", "path_type": "hardlink", "sha256": "f8a4a2cdb1d5337fbbb617d77e728056729f5f5eb30252c2aebd705b34cad204", "sha256_in_prefix": "f8a4a2cdb1d5337fbbb617d77e728056729f5f5eb30252c2aebd705b34cad204", "size_in_bytes": 2242}, {"_path": "include/opentelemetry/semconv/incubating/graphql_attributes.h", "path_type": "hardlink", "sha256": "582890f934a508074a272a7b434c75f6c28f5b24077fc902df24328827bc44fb", "sha256_in_prefix": "582890f934a508074a272a7b434c75f6c28f5b24077fc902df24328827bc44fb", "size_in_bytes": 1243}, {"_path": "include/opentelemetry/semconv/incubating/heroku_attributes.h", "path_type": "hardlink", "sha256": "d2cc637e9b89662d302f25cee00bc15d99503b8b63fe74b54a1e9af3323dec93", "sha256_in_prefix": "d2cc637e9b89662d302f25cee00bc15d99503b8b63fe74b54a1e9af3323dec93", "size_in_bytes": 846}, {"_path": "include/opentelemetry/semconv/incubating/host_attributes.h", "path_type": "hardlink", "sha256": "f36407cbb0ec567f2557184b38edea9e859059dcc0e0d783be6cf8286c9b88bc", "sha256_in_prefix": "f36407cbb0ec567f2557184b38edea9e859059dcc0e0d783be6cf8286c9b88bc", "size_in_bytes": 4138}, {"_path": "include/opentelemetry/semconv/incubating/http_attributes.h", "path_type": "hardlink", "sha256": "6be574b8a29dd2a313cacfc5edf748d01ccfad2012622b1e8e3d5dfa01facd33", "sha256_in_prefix": "6be574b8a29dd2a313cacfc5edf748d01ccfad2012622b1e8e3d5dfa01facd33", "size_in_bytes": 13398}, {"_path": "include/opentelemetry/semconv/incubating/http_metrics.h", "path_type": "hardlink", "sha256": "d73b3425bcfad67a773d8b76f315bbf78a7e8b36dba1b4dbcfbc8e5425b1652f", "sha256_in_prefix": "d73b3425bcfad67a773d8b76f315bbf78a7e8b36dba1b4dbcfbc8e5425b1652f", "size_in_bytes": 15240}, {"_path": "include/opentelemetry/semconv/incubating/hw_attributes.h", "path_type": "hardlink", "sha256": "bf649464de9a45fbd22c76d92d943b9340cfea634501bfa43e296e6951fe619c", "sha256_in_prefix": "bf649464de9a45fbd22c76d92d943b9340cfea634501bfa43e296e6951fe619c", "size_in_bytes": 2735}, {"_path": "include/opentelemetry/semconv/incubating/hw_metrics.h", "path_type": "hardlink", "sha256": "0d46f9460993ca30b53eacc8294d21efa57f6af54222351837095f0620ea9631", "sha256_in_prefix": "0d46f9460993ca30b53eacc8294d21efa57f6af54222351837095f0620ea9631", "size_in_bytes": 13690}, {"_path": "include/opentelemetry/semconv/incubating/k8s_attributes.h", "path_type": "hardlink", "sha256": "6521c3ae84d2faed217e90c8ebffd05e1500b4a79a340fe47a11409445ff24d8", "sha256_in_prefix": "6521c3ae84d2faed217e90c8ebffd05e1500b4a79a340fe47a11409445ff24d8", "size_in_bytes": 14922}, {"_path": "include/opentelemetry/semconv/incubating/k8s_metrics.h", "path_type": "hardlink", "sha256": "d81f78a2a6d9e9ea4df8e027addef302810f246c4c437813ed8bba504fbf852c", "sha256_in_prefix": "d81f78a2a6d9e9ea4df8e027addef302810f246c4c437813ed8bba504fbf852c", "size_in_bytes": 79994}, {"_path": "include/opentelemetry/semconv/incubating/linux_attributes.h", "path_type": "hardlink", "sha256": "e42323034fd01dc6bd01bd4927b699c7742e0e977363d36a4430e827980d0ccd", "sha256_in_prefix": "e42323034fd01dc6bd01bd4927b699c7742e0e977363d36a4430e827980d0ccd", "size_in_bytes": 817}, {"_path": "include/opentelemetry/semconv/incubating/log_attributes.h", "path_type": "hardlink", "sha256": "9cfa83e9052f5164f542c2654d6ba3431b255692420b60a3fc093e812a62dd90", "sha256_in_prefix": "9cfa83e9052f5164f542c2654d6ba3431b255692420b60a3fc093e812a62dd90", "size_in_bytes": 2191}, {"_path": "include/opentelemetry/semconv/incubating/message_attributes.h", "path_type": "hardlink", "sha256": "791c8d375602ee9765c77d10ffa50b9e61f8d7169344a7a0d2245a67a2e8ffe8", "sha256_in_prefix": "791c8d375602ee9765c77d10ffa50b9e61f8d7169344a7a0d2245a67a2e8ffe8", "size_in_bytes": 1912}, {"_path": "include/opentelemetry/semconv/incubating/messaging_attributes.h", "path_type": "hardlink", "sha256": "a909e8250df3fbdc4a6c387f73702cf479f0706689042c637ebe3644bf01cd8e", "sha256_in_prefix": "a909e8250df3fbdc4a6c387f73702cf479f0706689042c637ebe3644bf01cd8e", "size_in_bytes": 16224}, {"_path": "include/opentelemetry/semconv/incubating/messaging_metrics.h", "path_type": "hardlink", "sha256": "8a61c0043c1938f4855a1b9cb189458a45fbdb04991f21de69089f3e2c8d31c0", "sha256_in_prefix": "8a61c0043c1938f4855a1b9cb189458a45fbdb04991f21de69089f3e2c8d31c0", "size_in_bytes": 19301}, {"_path": "include/opentelemetry/semconv/incubating/net_attributes.h", "path_type": "hardlink", "sha256": "b8c7715f84c194f8ff74b813333826855133d61fdce9c258533b1829553ef04c", "sha256_in_prefix": "b8c7715f84c194f8ff74b813333826855133d61fdce9c258533b1829553ef04c", "size_in_bytes": 5678}, {"_path": "include/opentelemetry/semconv/incubating/network_attributes.h", "path_type": "hardlink", "sha256": "ac5430ce0744ab43a24fffba2d502a25dfa87bf77eadec512c55b41c1110bbfc", "sha256_in_prefix": "ac5430ce0744ab43a24fffba2d502a25dfa87bf77eadec512c55b41c1110bbfc", "size_in_bytes": 7506}, {"_path": "include/opentelemetry/semconv/incubating/oci_attributes.h", "path_type": "hardlink", "sha256": "821f0a24f507c389414b5e92d95358e099365a8122bcc98ef1d2c45523a24918", "sha256_in_prefix": "821f0a24f507c389414b5e92d95358e099365a8122bcc98ef1d2c45523a24918", "size_in_bytes": 1091}, {"_path": "include/opentelemetry/semconv/incubating/opentracing_attributes.h", "path_type": "hardlink", "sha256": "cab4e9278dac76734aa49c02fdd64dd0fa2f2a07fb3b5aab0c14794aaad50441", "sha256_in_prefix": "cab4e9278dac76734aa49c02fdd64dd0fa2f2a07fb3b5aab0c14794aaad50441", "size_in_bytes": 1005}, {"_path": "include/opentelemetry/semconv/incubating/os_attributes.h", "path_type": "hardlink", "sha256": "521572659cab81b5dd5714f00a12808aa60d5800714604fadc9ea2670501bb03", "sha256_in_prefix": "521572659cab81b5dd5714f00a12808aa60d5800714604fadc9ea2670501bb03", "size_in_bytes": 2112}, {"_path": "include/opentelemetry/semconv/incubating/otel_attributes.h", "path_type": "hardlink", "sha256": "3d56980323ccad75ffc47f3d8ae6c686fe3c3b205c8a7aafa188ef96cc58b343", "sha256_in_prefix": "3d56980323ccad75ffc47f3d8ae6c686fe3c3b205c8a7aafa188ef96cc58b343", "size_in_bytes": 6441}, {"_path": "include/opentelemetry/semconv/incubating/otel_metrics.h", "path_type": "hardlink", "sha256": "ccb2fe7ce84b2c738c91dff98205f78921ed9a40426207d317e99f4d25853abf", "sha256_in_prefix": "ccb2fe7ce84b2c738c91dff98205f78921ed9a40426207d317e99f4d25853abf", "size_in_bytes": 48477}, {"_path": "include/opentelemetry/semconv/incubating/other_attributes.h", "path_type": "hardlink", "sha256": "dcaf5eb7161a4d3f142dcb466ea0b264c57ca7ded451c2b7cc4d3f24cec4bf05", "sha256_in_prefix": "dcaf5eb7161a4d3f142dcb466ea0b264c57ca7ded451c2b7cc4d3f24cec4bf05", "size_in_bytes": 937}, {"_path": "include/opentelemetry/semconv/incubating/peer_attributes.h", "path_type": "hardlink", "sha256": "6daf4749f24017d1bb7f8d96dbdb93538c674eb415cf5300ccc2667e1d60dd6b", "sha256_in_prefix": "6daf4749f24017d1bb7f8d96dbdb93538c674eb415cf5300ccc2667e1d60dd6b", "size_in_bytes": 743}, {"_path": "include/opentelemetry/semconv/incubating/pool_attributes.h", "path_type": "hardlink", "sha256": "8dda538c5b92140e646e0db8838895946bd8abab052aa8d0378f16149971df4e", "sha256_in_prefix": "8dda538c5b92140e646e0db8838895946bd8abab052aa8d0378f16149971df4e", "size_in_bytes": 779}, {"_path": "include/opentelemetry/semconv/incubating/process_attributes.h", "path_type": "hardlink", "sha256": "88cfb15554d6ea523125c2c5f4e7be85060003e9a14ba676ada23b7125322ea2", "sha256_in_prefix": "88cfb15554d6ea523125c2c5f4e7be85060003e9a14ba676ada23b7125322ea2", "size_in_bytes": 9654}, {"_path": "include/opentelemetry/semconv/incubating/process_metrics.h", "path_type": "hardlink", "sha256": "d15f84064ad74e0e8c212f03589becc7a9122954a132e6d00f6e9cd9ac8a2bc9", "sha256_in_prefix": "d15f84064ad74e0e8c212f03589becc7a9122954a132e6d00f6e9cd9ac8a2bc9", "size_in_bytes": 18320}, {"_path": "include/opentelemetry/semconv/incubating/profile_attributes.h", "path_type": "hardlink", "sha256": "f37ee0a26298bfe75143900f3661908c547f739f87099f1f0c7accce1928307f", "sha256_in_prefix": "f37ee0a26298bfe75143900f3661908c547f739f87099f1f0c7accce1928307f", "size_in_bytes": 2461}, {"_path": "include/opentelemetry/semconv/incubating/rpc_attributes.h", "path_type": "hardlink", "sha256": "363a3178239e6520e9b91f61888c768c2deea4187d89c30e28c8cf58b480a213", "sha256_in_prefix": "363a3178239e6520e9b91f61888c768c2deea4187d89c30e28c8cf58b480a213", "size_in_bytes": 9871}, {"_path": "include/opentelemetry/semconv/incubating/rpc_metrics.h", "path_type": "hardlink", "sha256": "e50e4c53912f91dee4724a81156b1000fedf7456f9501b3f085543ca13b39ba8", "sha256_in_prefix": "e50e4c53912f91dee4724a81156b1000fedf7456f9501b3f085543ca13b39ba8", "size_in_bytes": 11917}, {"_path": "include/opentelemetry/semconv/incubating/security_rule_attributes.h", "path_type": "hardlink", "sha256": "1cb4d7c2f46b9170fbca5337672a8a09100556869d5a90cfd2f5dd7ee56f5e5a", "sha256_in_prefix": "1cb4d7c2f46b9170fbca5337672a8a09100556869d5a90cfd2f5dd7ee56f5e5a", "size_in_bytes": 2064}, {"_path": "include/opentelemetry/semconv/incubating/server_attributes.h", "path_type": "hardlink", "sha256": "34b6b637fed25d19fe44f6011d4758500adba184f90745f54aed1f7086ddfcf4", "sha256_in_prefix": "34b6b637fed25d19fe44f6011d4758500adba184f90745f54aed1f7086ddfcf4", "size_in_bytes": 1187}, {"_path": "include/opentelemetry/semconv/incubating/service_attributes.h", "path_type": "hardlink", "sha256": "28728b6c92732b62dceaf09f3dcb5d071161e84698a3984c0e01e95434b6040d", "sha256_in_prefix": "28728b6c92732b62dceaf09f3dcb5d071161e84698a3984c0e01e95434b6040d", "size_in_bytes": 4131}, {"_path": "include/opentelemetry/semconv/incubating/session_attributes.h", "path_type": "hardlink", "sha256": "66512d6994a93a041b03318787d6b908ecb74466e54d444dcb45f668217fe9c7", "sha256_in_prefix": "66512d6994a93a041b03318787d6b908ecb74466e54d444dcb45f668217fe9c7", "size_in_bytes": 717}, {"_path": "include/opentelemetry/semconv/incubating/source_attributes.h", "path_type": "hardlink", "sha256": "de20f75ab1d48a189ed9776da83c8dfea86a731189fecd49ef0927ce464e0a8a", "sha256_in_prefix": "de20f75ab1d48a189ed9776da83c8dfea86a731189fecd49ef0927ce464e0a8a", "size_in_bytes": 978}, {"_path": "include/opentelemetry/semconv/incubating/system_attributes.h", "path_type": "hardlink", "sha256": "d12cdbe89ebb5295e1c491866080f5c18cc96729382a8447af656b1a66910b40", "sha256_in_prefix": "d12cdbe89ebb5295e1c491866080f5c18cc96729382a8447af656b1a66910b40", "size_in_bytes": 6602}, {"_path": "include/opentelemetry/semconv/incubating/system_metrics.h", "path_type": "hardlink", "sha256": "a46c5d837e7af6c4feacf9b49c19d82033d010cdf0c04581cd4a84c6181d7184", "sha256_in_prefix": "a46c5d837e7af6c4feacf9b49c19d82033d010cdf0c04581cd4a84c6181d7184", "size_in_bytes": 57282}, {"_path": "include/opentelemetry/semconv/incubating/telemetry_attributes.h", "path_type": "hardlink", "sha256": "66d03b9d655a7842345c3e10e1d50356d893eda2f0af303ce477ccc53cfc55e7", "sha256_in_prefix": "66d03b9d655a7842345c3e10e1d50356d893eda2f0af303ce477ccc53cfc55e7", "size_in_bytes": 2836}, {"_path": "include/opentelemetry/semconv/incubating/test_attributes.h", "path_type": "hardlink", "sha256": "de327d9d94416c2e54af1f95cf8393abd57b075710c5cd60250f66169ebe289b", "sha256_in_prefix": "de327d9d94416c2e54af1f95cf8393abd57b075710c5cd60250f66169ebe289b", "size_in_bytes": 1808}, {"_path": "include/opentelemetry/semconv/incubating/thread_attributes.h", "path_type": "hardlink", "sha256": "5f04d4e62654eb347398193dde57a5573313d2939ab9938f0a02a34ef4751de0", "sha256_in_prefix": "5f04d4e62654eb347398193dde57a5573313d2939ab9938f0a02a34ef4751de0", "size_in_bytes": 676}, {"_path": "include/opentelemetry/semconv/incubating/tls_attributes.h", "path_type": "hardlink", "sha256": "ac861d134a5d33560b035e993b90edd5582e1bbcc158206f714707509b430334", "sha256_in_prefix": "ac861d134a5d33560b035e993b90edd5582e1bbcc158206f714707509b430334", "size_in_bytes": 7646}, {"_path": "include/opentelemetry/semconv/incubating/url_attributes.h", "path_type": "hardlink", "sha256": "6bcd4da176b589dbfbe8ee1b8515d464b2b0811348bb9df8a169bdb017834ccf", "sha256_in_prefix": "6bcd4da176b589dbfbe8ee1b8515d464b2b0811348bb9df8a169bdb017834ccf", "size_in_bytes": 7575}, {"_path": "include/opentelemetry/semconv/incubating/user_agent_attributes.h", "path_type": "hardlink", "sha256": "241b860a7188cfe53039161adba9a2ab2c917ac2eb3fbd650b0175bb842f993f", "sha256_in_prefix": "241b860a7188cfe53039161adba9a2ab2c917ac2eb3fbd650b0175bb842f993f", "size_in_bytes": 3348}, {"_path": "include/opentelemetry/semconv/incubating/user_attributes.h", "path_type": "hardlink", "sha256": "bbedaf72c536ca7056eaa2d39aa2c4618c7b8cd28dde5627dd25819620b264c3", "sha256_in_prefix": "bbedaf72c536ca7056eaa2d39aa2c4618c7b8cd28dde5627dd25819620b264c3", "size_in_bytes": 1211}, {"_path": "include/opentelemetry/semconv/incubating/vcs_attributes.h", "path_type": "hardlink", "sha256": "6264255ae8780247a3c390ed5d20746f6d3bd1ff85dc3288350fb56d5362d403", "sha256_in_prefix": "6264255ae8780247a3c390ed5d20746f6d3bd1ff85dc3288350fb56d5362d403", "size_in_bytes": 11825}, {"_path": "include/opentelemetry/semconv/incubating/vcs_metrics.h", "path_type": "hardlink", "sha256": "2f68bf397c6789e62e26d053dc7eaecab5dbb0154195a03e0db5ff8b6415e173", "sha256_in_prefix": "2f68bf397c6789e62e26d053dc7eaecab5dbb0154195a03e0db5ff8b6415e173", "size_in_bytes": 17910}, {"_path": "include/opentelemetry/semconv/incubating/webengine_attributes.h", "path_type": "hardlink", "sha256": "13ce522290064c6cc2ceb32b066858f40e2d8a473a07036d2347190272dc4ebf", "sha256_in_prefix": "13ce522290064c6cc2ceb32b066858f40e2d8a473a07036d2347190272dc4ebf", "size_in_bytes": 863}, {"_path": "include/opentelemetry/semconv/k8s_metrics.h", "path_type": "hardlink", "sha256": "9823ba7aeef6bcc9e372d94326d3f0729a2b0bc4b49933551a0345f3a9dea603", "sha256_in_prefix": "9823ba7aeef6bcc9e372d94326d3f0729a2b0bc4b49933551a0345f3a9dea603", "size_in_bytes": 80938}, {"_path": "include/opentelemetry/semconv/messaging_metrics.h", "path_type": "hardlink", "sha256": "fc69771aa2e5eeb7cba15f6a83175ef02e1a10475c5de29046cda2be1aae8666", "sha256_in_prefix": "fc69771aa2e5eeb7cba15f6a83175ef02e1a10475c5de29046cda2be1aae8666", "size_in_bytes": 19102}, {"_path": "include/opentelemetry/semconv/network_attributes.h", "path_type": "hardlink", "sha256": "c52d04e4e6dcbbd451ad14e47dcce1aa1eea2b8eb731f5c40b2eeb2fbdf92811", "sha256_in_prefix": "c52d04e4e6dcbbd451ad14e47dcce1aa1eea2b8eb731f5c40b2eeb2fbdf92811", "size_in_bytes": 3095}, {"_path": "include/opentelemetry/semconv/otel_attributes.h", "path_type": "hardlink", "sha256": "9809b0b4cdfdac97b2a0ac65e23afd030ea31255129531294c1e4bb67ea8c1d2", "sha256_in_prefix": "9809b0b4cdfdac97b2a0ac65e23afd030ea31255129531294c1e4bb67ea8c1d2", "size_in_bytes": 1449}, {"_path": "include/opentelemetry/semconv/otel_metrics.h", "path_type": "hardlink", "sha256": "fdbdd09685e2bdda9ca32c481daf9120e5297ae2a1374b5980fd0abf70839f2c", "sha256_in_prefix": "fdbdd09685e2bdda9ca32c481daf9120e5297ae2a1374b5980fd0abf70839f2c", "size_in_bytes": 16321}, {"_path": "include/opentelemetry/semconv/process_metrics.h", "path_type": "hardlink", "sha256": "d852443cc64bc43c1a56e751ebe9f2fd7070306abb75e91c18ed1367f2f2ffd3", "sha256_in_prefix": "d852443cc64bc43c1a56e751ebe9f2fd7070306abb75e91c18ed1367f2f2ffd3", "size_in_bytes": 18354}, {"_path": "include/opentelemetry/semconv/rpc_metrics.h", "path_type": "hardlink", "sha256": "ca4f14f05dd156ff3094c77e1bc289a8ab18db1e3c0410c5f328895df5567a0c", "sha256_in_prefix": "ca4f14f05dd156ff3094c77e1bc289a8ab18db1e3c0410c5f328895df5567a0c", "size_in_bytes": 11981}, {"_path": "include/opentelemetry/semconv/schema_url.h", "path_type": "hardlink", "sha256": "b7c8740eb4412846926eaadd2720764c9d1cd460e3784bf6e955a573a2b1155f", "sha256_in_prefix": "b7c8740eb4412846926eaadd2720764c9d1cd460e3784bf6e955a573a2b1155f", "size_in_bytes": 570}, {"_path": "include/opentelemetry/semconv/server_attributes.h", "path_type": "hardlink", "sha256": "34b6b637fed25d19fe44f6011d4758500adba184f90745f54aed1f7086ddfcf4", "sha256_in_prefix": "34b6b637fed25d19fe44f6011d4758500adba184f90745f54aed1f7086ddfcf4", "size_in_bytes": 1187}, {"_path": "include/opentelemetry/semconv/service_attributes.h", "path_type": "hardlink", "sha256": "717b8d477c844b8e767f642fb43504ba2414fb2a627b69529778624605f47539", "sha256_in_prefix": "717b8d477c844b8e767f642fb43504ba2414fb2a627b69529778624605f47539", "size_in_bytes": 1161}, {"_path": "include/opentelemetry/semconv/system_metrics.h", "path_type": "hardlink", "sha256": "f2b8f888329942b7b45fb00e82f86baee1b38ec15a01deeee40b7acee7c026eb", "sha256_in_prefix": "f2b8f888329942b7b45fb00e82f86baee1b38ec15a01deeee40b7acee7c026eb", "size_in_bytes": 57359}, {"_path": "include/opentelemetry/semconv/telemetry_attributes.h", "path_type": "hardlink", "sha256": "019e6e604a31ab692677154fe61df02dcaa05e35a63705f75955cbb3d45c5b83", "sha256_in_prefix": "019e6e604a31ab692677154fe61df02dcaa05e35a63705f75955cbb3d45c5b83", "size_in_bytes": 2264}, {"_path": "include/opentelemetry/semconv/url_attributes.h", "path_type": "hardlink", "sha256": "8df3e3a55ff056756bd6e3c4ddd9dd4efcef13debd70edbe01a7e15585781114", "sha256_in_prefix": "8df3e3a55ff056756bd6e3c4ddd9dd4efcef13debd70edbe01a7e15585781114", "size_in_bytes": 4166}, {"_path": "include/opentelemetry/semconv/user_agent_attributes.h", "path_type": "hardlink", "sha256": "5542f94074d5c6f1abe7c5f105b45121ad54184f08b241afda3b5fb17b53db2e", "sha256_in_prefix": "5542f94074d5c6f1abe7c5f105b45121ad54184f08b241afda3b5fb17b53db2e", "size_in_bytes": 688}, {"_path": "include/opentelemetry/semconv/vcs_metrics.h", "path_type": "hardlink", "sha256": "fd6b838f15c31aa0f38d41cfc38f76cbac3fbb29ec6315bd31f37764b414f780", "sha256_in_prefix": "fd6b838f15c31aa0f38d41cfc38f76cbac3fbb29ec6315bd31f37764b414f780", "size_in_bytes": 17942}, {"_path": "include/opentelemetry/std/shared_ptr.h", "path_type": "hardlink", "sha256": "704e04752d290433a857e8ba8cf8ae889aa80f60aa8e276460b9f0bca7d1626f", "sha256_in_prefix": "704e04752d290433a857e8ba8cf8ae889aa80f60aa8e276460b9f0bca7d1626f", "size_in_bytes": 486}, {"_path": "include/opentelemetry/std/span.h", "path_type": "hardlink", "sha256": "a8ae8a9917cf72e365d8383a1a5033460b39eba49460b9513c9e674027a380ee", "sha256_in_prefix": "a8ae8a9917cf72e365d8383a1a5033460b39eba49460b9513c9e674027a380ee", "size_in_bytes": 2204}, {"_path": "include/opentelemetry/std/string_view.h", "path_type": "hardlink", "sha256": "a1cba6e6df9629a8c8cc037ec60f71297bd3c7ad1f4072ab55015b774678057e", "sha256_in_prefix": "a1cba6e6df9629a8c8cc037ec60f71297bd3c7ad1f4072ab55015b774678057e", "size_in_bytes": 427}, {"_path": "include/opentelemetry/std/type_traits.h", "path_type": "hardlink", "sha256": "b33cd790f1e818b19652895045348d679045546a29eb6ba2dd1e4417f77d4ff5", "sha256_in_prefix": "b33cd790f1e818b19652895045348d679045546a29eb6ba2dd1e4417f77d4ff5", "size_in_bytes": 266}, {"_path": "include/opentelemetry/std/unique_ptr.h", "path_type": "hardlink", "sha256": "170c9eec7838091c1f786e6ee7d4ce135bc2dc80dda8688877958579f8724750", "sha256_in_prefix": "170c9eec7838091c1f786e6ee7d4ce135bc2dc80dda8688877958579f8724750", "size_in_bytes": 486}, {"_path": "include/opentelemetry/std/utility.h", "path_type": "hardlink", "sha256": "6304e557e730ff2ddc753e181c257e10770f33e369dbde6603e13b909d117910", "sha256_in_prefix": "6304e557e730ff2ddc753e181c257e10770f33e369dbde6603e13b909d117910", "size_in_bytes": 1352}, {"_path": "include/opentelemetry/std/variant.h", "path_type": "hardlink", "sha256": "9a28968c1f958821651706cbc25b7c0ef8626dab73ce513a0e5a16b8fecff20d", "sha256_in_prefix": "9a28968c1f958821651706cbc25b7c0ef8626dab73ce513a0e5a16b8fecff20d", "size_in_bytes": 6059}, {"_path": "include/opentelemetry/trace/context.h", "path_type": "hardlink", "sha256": "db5690671ce2601cb31de3f559fca61da5e807c080c253b065bb2dd6a3a62a53", "sha256_in_prefix": "db5690671ce2601cb31de3f559fca61da5e807c080c253b065bb2dd6a3a62a53", "size_in_bytes": 1224}, {"_path": "include/opentelemetry/trace/default_span.h", "path_type": "hardlink", "sha256": "4891d68c246a2a91bf123dba8a6785ae3fb571eea551c638191d4ee86738ce26", "sha256_in_prefix": "4891d68c246a2a91bf123dba8a6785ae3fb571eea551c638191d4ee86738ce26", "size_in_bytes": 2441}, {"_path": "include/opentelemetry/trace/noop.h", "path_type": "hardlink", "sha256": "04f4a697450c049f420f8090385261c88eeb099d75ce0c9d6de667b3e44f140f", "sha256_in_prefix": "04f4a697450c049f420f8090385261c88eeb099d75ce0c9d6de667b3e44f140f", "size_in_bytes": 5179}, {"_path": "include/opentelemetry/trace/propagation/b3_propagator.h", "path_type": "hardlink", "sha256": "8547f18f856224ee36aa646d32af5a439b796b0242f22793ff2b5fc4dcaaeb78", "sha256_in_prefix": "8547f18f856224ee36aa646d32af5a439b796b0242f22793ff2b5fc4dcaaeb78", "size_in_bytes": 7001}, {"_path": "include/opentelemetry/trace/propagation/detail/hex.h", "path_type": "hardlink", "sha256": "9a8b5fe823a8352584686385e89747c8a5f9c44766c9b025d0bfed861384ea23", "sha256_in_prefix": "9a8b5fe823a8352584686385e89747c8a5f9c44766c9b025d0bfed861384ea23", "size_in_bytes": 2627}, {"_path": "include/opentelemetry/trace/propagation/detail/string.h", "path_type": "hardlink", "sha256": "d771b50f64b50afdfd07414df46025025f4ee961b9243757f5b5a53c99f32259", "sha256_in_prefix": "d771b50f64b50afdfd07414df46025025f4ee961b9243757f5b5a53c99f32259", "size_in_bytes": 1243}, {"_path": "include/opentelemetry/trace/propagation/http_trace_context.h", "path_type": "hardlink", "sha256": "49068472e7a690c2e5bccddb17bf1bdb1da361d8442df9915b1f9b06c000682e", "sha256_in_prefix": "49068472e7a690c2e5bccddb17bf1bdb1da361d8442df9915b1f9b06c000682e", "size_in_bytes": 6511}, {"_path": "include/opentelemetry/trace/propagation/jaeger.h", "path_type": "hardlink", "sha256": "362b47690064a8e52fd386dba64459ea206c11c820b053e04d2c91ed19220803", "sha256_in_prefix": "362b47690064a8e52fd386dba64459ea206c11c820b053e04d2c91ed19220803", "size_in_bytes": 3865}, {"_path": "include/opentelemetry/trace/provider.h", "path_type": "hardlink", "sha256": "cdf843eabd41221f5487e72f8ae73d444dee6868ebcde72270dbf3c4daae99f9", "sha256_in_prefix": "cdf843eabd41221f5487e72f8ae73d444dee6868ebcde72270dbf3c4daae99f9", "size_in_bytes": 1493}, {"_path": "include/opentelemetry/trace/scope.h", "path_type": "hardlink", "sha256": "617dcbca134871617c8257de4cf6035168c969f7b556c6f04c204df3273c076b", "sha256_in_prefix": "617dcbca134871617c8257de4cf6035168c969f7b556c6f04c204df3273c076b", "size_in_bytes": 1136}, {"_path": "include/opentelemetry/trace/semantic_conventions.h", "path_type": "hardlink", "sha256": "a5cbd01b8f581aeb6e1a3367f68c484ff52ed12fdcb907d35df9b5c63d940bb5", "sha256_in_prefix": "a5cbd01b8f581aeb6e1a3367f68c484ff52ed12fdcb907d35df9b5c63d940bb5", "size_in_bytes": 186234}, {"_path": "include/opentelemetry/trace/span.h", "path_type": "hardlink", "sha256": "5ce6f80c28a93bd5631dcd5acd01108273a4bedfbcfeae54ff34a5261ea73a04", "sha256_in_prefix": "5ce6f80c28a93bd5631dcd5acd01108273a4bedfbcfeae54ff34a5261ea73a04", "size_in_bytes": 8581}, {"_path": "include/opentelemetry/trace/span_context.h", "path_type": "hardlink", "sha256": "a677fbbba563537e7522d7d96bca49e536a3d533d78e3e295a01d41944836470", "sha256_in_prefix": "a677fbbba563537e7522d7d96bca49e536a3d533d78e3e295a01d41944836470", "size_in_bytes": 3251}, {"_path": "include/opentelemetry/trace/span_context_kv_iterable.h", "path_type": "hardlink", "sha256": "bf98f8f2491b81adb2b4f59ff50a65ca4380c7d6e28ba65f0c8f5fc1dee9f72a", "sha256_in_prefix": "bf98f8f2491b81adb2b4f59ff50a65ca4380c7d6e28ba65f0c8f5fc1dee9f72a", "size_in_bytes": 1523}, {"_path": "include/opentelemetry/trace/span_context_kv_iterable_view.h", "path_type": "hardlink", "sha256": "76ab07bbb777e24053b736445cd7d78f087e77d89ec7af1d61dd812857678027", "sha256_in_prefix": "76ab07bbb777e24053b736445cd7d78f087e77d89ec7af1d61dd812857678027", "size_in_bytes": 3795}, {"_path": "include/opentelemetry/trace/span_id.h", "path_type": "hardlink", "sha256": "b0d4481aaf45207f9389ad5a5fd710662a4bcddaf8e4f151dc83f6a42c1fb5d8", "sha256_in_prefix": "b0d4481aaf45207f9389ad5a5fd710662a4bcddaf8e4f151dc83f6a42c1fb5d8", "size_in_bytes": 1708}, {"_path": "include/opentelemetry/trace/span_metadata.h", "path_type": "hardlink", "sha256": "2bbd09e1cd2d07740cbef77fcb73d9701e7864f3da168294c2c31bc3c2085895", "sha256_in_prefix": "2bbd09e1cd2d07740cbef77fcb73d9701e7864f3da168294c2c31bc3c2085895", "size_in_bytes": 965}, {"_path": "include/opentelemetry/trace/span_startoptions.h", "path_type": "hardlink", "sha256": "7aec4c51d3840de6d0782fa49b63a8cd09f6fba5702ca8d8f6d74607893ac2d8", "sha256_in_prefix": "7aec4c51d3840de6d0782fa49b63a8cd09f6fba5702ca8d8f6d74607893ac2d8", "size_in_bytes": 2537}, {"_path": "include/opentelemetry/trace/trace_flags.h", "path_type": "hardlink", "sha256": "d4884f3d0a43034fbece0c74f4f2dcde291594d3adeba8951393443d58cfeadc", "sha256_in_prefix": "d4884f3d0a43034fbece0c74f4f2dcde291594d3adeba8951393443d58cfeadc", "size_in_bytes": 1916}, {"_path": "include/opentelemetry/trace/trace_id.h", "path_type": "hardlink", "sha256": "c03552ebfd4982ee04c2344d9309ccf9d29dbccfe90dbe6904130f5bad7c5950", "sha256_in_prefix": "c03552ebfd4982ee04c2344d9309ccf9d29dbccfe90dbe6904130f5bad7c5950", "size_in_bytes": 1854}, {"_path": "include/opentelemetry/trace/trace_state.h", "path_type": "hardlink", "sha256": "cdf45b5f689aee2319db4407f339f707e8c3153505eb98dc0d2cd10cd7b3b0cd", "sha256_in_prefix": "cdf45b5f689aee2319db4407f339f707e8c3153505eb98dc0d2cd10cd7b3b0cd", "size_in_bytes": 9850}, {"_path": "include/opentelemetry/trace/tracer.h", "path_type": "hardlink", "sha256": "91166b19ad6037e913d17acd1f708e3736cfdc5c8919829b1b09bfdc1bd37903", "sha256_in_prefix": "91166b19ad6037e913d17acd1f708e3736cfdc5c8919829b1b09bfdc1bd37903", "size_in_bytes": 9049}, {"_path": "include/opentelemetry/trace/tracer_provider.h", "path_type": "hardlink", "sha256": "ea44e64e15e82165831ae585a6fddc3011c8c29b5afcfa08555d582ffc5ceae9", "sha256_in_prefix": "ea44e64e15e82165831ae585a6fddc3011c8c29b5afcfa08555d582ffc5ceae9", "size_in_bytes": 4206}, {"_path": "include/opentelemetry/version.h", "path_type": "hardlink", "sha256": "aa5a98f0f53012e8e9ed52b0a48da459c9c955083bf272798f9fbf6eee733c0e", "sha256_in_prefix": "aa5a98f0f53012e8e9ed52b0a48da459c9c955083bf272798f9fbf6eee733c0e", "size_in_bytes": 865}, {"_path": "lib/pkgconfig/opentelemetry_api.pc", "path_type": "hardlink", "sha256": "4f336f12829b363e3ca4e00cf11188a0a3de13670a7dec06291b964263cec1f9", "sha256_in_prefix": "4f336f12829b363e3ca4e00cf11188a0a3de13670a7dec06291b964263cec1f9", "size_in_bytes": 392}, {"_path": "lib/pkgconfig/opentelemetry_common.pc", "path_type": "hardlink", "sha256": "6a549f2a2eeb22c3d7d1d95f81fb35fd844fcb1eedade20148d39d6e9d995a74", "sha256_in_prefix": "6a549f2a2eeb22c3d7d1d95f81fb35fd844fcb1eedade20148d39d6e9d995a74", "size_in_bytes": 418}, {"_path": "lib/pkgconfig/opentelemetry_logs.pc", "path_type": "hardlink", "sha256": "1f9d1659a632d0dc0e6e1694ff55bd486067fd1f40c7af23533c723752ead037", "sha256_in_prefix": "1f9d1659a632d0dc0e6e1694ff55bd486067fd1f40c7af23533c723752ead037", "size_in_bytes": 396}, {"_path": "lib/pkgconfig/opentelemetry_metrics.pc", "path_type": "hardlink", "sha256": "9e326b376d07a4d54defc204ca6ff656fd4f5c6c8fd61ba0e74c226c487ab8fe", "sha256_in_prefix": "9e326b376d07a4d54defc204ca6ff656fd4f5c6c8fd61ba0e74c226c487ab8fe", "size_in_bytes": 405}, {"_path": "lib/pkgconfig/opentelemetry_resources.pc", "path_type": "hardlink", "sha256": "16e18010cccff780648d4bcd2a4ed9956d18bb1cc60a9ed3504b2b7f511c2dfb", "sha256_in_prefix": "16e18010cccff780648d4bcd2a4ed9956d18bb1cc60a9ed3504b2b7f511c2dfb", "size_in_bytes": 407}, {"_path": "lib/pkgconfig/opentelemetry_trace.pc", "path_type": "hardlink", "sha256": "3fd1bbfdd39a6280ed8bc55ef1f8a095dd72a92fef85a42c77c8f41b81429883", "sha256_in_prefix": "3fd1bbfdd39a6280ed8bc55ef1f8a095dd72a92fef85a42c77c8f41b81429883", "size_in_bytes": 400}, {"_path": "lib/pkgconfig/opentelemetry_version.pc", "path_type": "hardlink", "sha256": "4bd06254a7cdfdf0d76e22a19abfd8402033b1e9d4f546bf37090789be4431c9", "sha256_in_prefix": "4bd06254a7cdfdf0d76e22a19abfd8402033b1e9d4f546bf37090789be4431c9", "size_in_bytes": 399}], "paths_version": 1}, "requested_spec": "None", "sha256": "0e1e062cf75ea4ea898108e2bd1adac7cbf369d95584604bf3424ac8a600125b", "size": 360373, "subdir": "osx-64", "timestamp": 1748592530000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libopentelemetry-cpp-headers-1.21.0-h694c41f_0.conda", "version": "1.21.0"}