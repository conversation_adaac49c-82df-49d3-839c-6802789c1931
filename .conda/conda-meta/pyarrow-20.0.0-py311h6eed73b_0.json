{"build": "py311h6eed73b_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["libarrow-acero 20.0.0.*", "libarrow-dataset 20.0.0.*", "libarrow-substrait 20.0.0.*", "libparquet 20.0.0.*", "pyarrow-core 20.0.0 *_0_*", "python >=3.11,<3.12.0a0", "python_abi 3.11.* *_cp311"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/pyarrow-20.0.0-py311h6eed73b_0", "files": [], "fn": "pyarrow-20.0.0-py311h6eed73b_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/pyarrow-20.0.0-py311h6eed73b_0", "type": 1}, "md5": "2afa637c69a171399ddd8dda3e890d3d", "name": "p<PERSON><PERSON>", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/pyarrow-20.0.0-py311h6eed73b_0.conda", "paths_data": {"paths": [], "paths_version": 1}, "requested_spec": "p<PERSON><PERSON>", "sha256": "e35bdd746728c139e21cdb2ab3bcb26541615677447a8fe5251bb08bed0c0e11", "size": 25825, "subdir": "osx-64", "timestamp": 1746000673000, "url": "https://conda.anaconda.org/conda-forge/osx-64/pyarrow-20.0.0-py311h6eed73b_0.conda", "version": "20.0.0"}