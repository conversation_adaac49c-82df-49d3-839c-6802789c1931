{"build": "py311h7a2b322_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.13", "libcxx >=19", "python >=3.11,<3.12.0a0", "python_abi 3.11.* *_cp311"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/frozenlist-1.7.0-py311h7a2b322_0", "files": ["lib/python3.11/site-packages/frozenlist-1.7.0.dist-info/INSTALLER", "lib/python3.11/site-packages/frozenlist-1.7.0.dist-info/METADATA", "lib/python3.11/site-packages/frozenlist-1.7.0.dist-info/RECORD", "lib/python3.11/site-packages/frozenlist-1.7.0.dist-info/REQUESTED", "lib/python3.11/site-packages/frozenlist-1.7.0.dist-info/WHEEL", "lib/python3.11/site-packages/frozenlist-1.7.0.dist-info/direct_url.json", "lib/python3.11/site-packages/frozenlist-1.7.0.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/frozenlist-1.7.0.dist-info/top_level.txt", "lib/python3.11/site-packages/frozenlist/__init__.py", "lib/python3.11/site-packages/frozenlist/__init__.pyi", "lib/python3.11/site-packages/frozenlist/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/frozenlist/_frozenlist.cpython-311-darwin.so", "lib/python3.11/site-packages/frozenlist/_frozenlist.pyx", "lib/python3.11/site-packages/frozenlist/py.typed"], "fn": "frozenlist-1.7.0-py311h7a2b322_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/frozenlist-1.7.0-py311h7a2b322_0", "type": 1}, "md5": "ad0e6d1df18292f15eab2dee54518d5c", "name": "frozenlist", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/frozenlist-1.7.0-py311h7a2b322_0.conda", "paths_data": {"paths": [{"_path": "lib/python3.11/site-packages/frozenlist-1.7.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "lib/python3.11/site-packages/frozenlist-1.7.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "8b5ed851b564399a4c8490e7c8eec172108b7b222576dc7aaed8f920a5581b40", "sha256_in_prefix": "8b5ed851b564399a4c8490e7c8eec172108b7b222576dc7aaed8f920a5581b40", "size_in_bytes": 18620}, {"_path": "lib/python3.11/site-packages/frozenlist-1.7.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "7e6f470270147fe2ab931887a51be1170cabb8c09a3d0c6359254866813b5a56", "sha256_in_prefix": "7e6f470270147fe2ab931887a51be1170cabb8c09a3d0c6359254866813b5a56", "size_in_bytes": 1171}, {"_path": "lib/python3.11/site-packages/frozenlist-1.7.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "lib/python3.11/site-packages/frozenlist-1.7.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "9a99a83f4370ced80864e3ca8f3c6d5e12203e9375332d371b67792389f5359e", "sha256_in_prefix": "9a99a83f4370ced80864e3ca8f3c6d5e12203e9375332d371b67792389f5359e", "size_in_bytes": 111}, {"_path": "lib/python3.11/site-packages/frozenlist-1.7.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "5e8bc23af308cb6900b0de3eb7e9b0409b02bae8dbecbb88af251d2f3528a34b", "sha256_in_prefix": "5e8bc23af308cb6900b0de3eb7e9b0409b02bae8dbecbb88af251d2f3528a34b", "size_in_bytes": 98}, {"_path": "lib/python3.11/site-packages/frozenlist-1.7.0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "6fd5243e92dd7f98ec69c7ac377728e74905709ff527a5bf98d6d0263c04f5b6", "sha256_in_prefix": "6fd5243e92dd7f98ec69c7ac377728e74905709ff527a5bf98d6d0263c04f5b6", "size_in_bytes": 11332}, {"_path": "lib/python3.11/site-packages/frozenlist-1.7.0.dist-info/top_level.txt", "path_type": "hardlink", "sha256": "8e2bedc6c3d70379cadd60565b62d6e4cb6efc61edf14640d7735e0acd9c2ae0", "sha256_in_prefix": "8e2bedc6c3d70379cadd60565b62d6e4cb6efc61edf14640d7735e0acd9c2ae0", "size_in_bytes": 11}, {"_path": "lib/python3.11/site-packages/frozenlist/__init__.py", "path_type": "hardlink", "sha256": "94ae6c8a4289d1396d76e1510eb20753da34b54ffac2c822e37909d2fb9c04ec", "sha256_in_prefix": "94ae6c8a4289d1396d76e1510eb20753da34b54ffac2c822e37909d2fb9c04ec", "size_in_bytes": 2108}, {"_path": "lib/python3.11/site-packages/frozenlist/__init__.pyi", "path_type": "hardlink", "sha256": "bcc128112d7119e80fb555e80a2f57c9d11e1eccae22af8a75e5f03f93ddb1a0", "sha256_in_prefix": "bcc128112d7119e80fb555e80a2f57c9d11e1eccae22af8a75e5f03f93ddb1a0", "size_in_bytes": 1470}, {"_path": "lib/python3.11/site-packages/frozenlist/__pycache__/__init__.cpython-311.pyc", "path_type": "hardlink", "sha256": "93a74c34e546cffe2448ce196adecf9ef3ffe3aa6f5ebcb80b701aa53898b162", "sha256_in_prefix": "93a74c34e546cffe2448ce196adecf9ef3ffe3aa6f5ebcb80b701aa53898b162", "size_in_bytes": 4354}, {"_path": "lib/python3.11/site-packages/frozenlist/_frozenlist.cpython-311-darwin.so", "file_mode": "binary", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/frozenlist_1752167230519/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehol", "sha256": "1e53cc90edbad8333f94ca489d1bab0ab68e94d733eac4a77f7349dcec317b4f", "sha256_in_prefix": "fc108faf7ae620c080c50471b76e0cb73ad09c3ff75f7a802471a61dec542f57", "size_in_bytes": 87184}, {"_path": "lib/python3.11/site-packages/frozenlist/_frozenlist.pyx", "path_type": "hardlink", "sha256": "b7e6868ee12256dfcc64f049d119eb69abd598f04ae9aaf3de2e3c66f5ee62c5", "sha256_in_prefix": "b7e6868ee12256dfcc64f049d119eb69abd598f04ae9aaf3de2e3c66f5ee62c5", "size_in_bytes": 3708}, {"_path": "lib/python3.11/site-packages/frozenlist/py.typed", "path_type": "hardlink", "sha256": "b28c3db284f03fd4ff80401049587b19bf3ce79874e0dc2686cd967be2518193", "sha256_in_prefix": "b28c3db284f03fd4ff80401049587b19bf3ce79874e0dc2686cd967be2518193", "size_in_bytes": 7}], "paths_version": 1}, "requested_spec": "None", "sha256": "ba999aa4f91a53d1104cf5aa78e318be3323936e5446a26ad1c5f59c85098b10", "size": 50739, "subdir": "osx-64", "timestamp": 1752167403000, "url": "https://conda.anaconda.org/conda-forge/osx-64/frozenlist-1.7.0-py311h7a2b322_0.conda", "version": "1.7.0"}