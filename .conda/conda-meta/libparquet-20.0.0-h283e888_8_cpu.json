{"build": "h283e888_8_cpu", "build_number": 8, "channel": "https://conda.anaconda.org/conda-forge/osx-64", "constrains": [], "depends": ["__osx >=10.14", "libarrow 20.0.0 h7601d43_8_cpu", "libcxx >=18", "libthrift >=0.21.0,<0.21.1.0a0", "openssl >=3.5.0,<4.0a0"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/libparquet-20.0.0-h283e888_8_cpu", "files": ["include/parquet/api/io.h", "include/parquet/api/reader.h", "include/parquet/api/schema.h", "include/parquet/api/writer.h", "include/parquet/arrow/reader.h", "include/parquet/arrow/schema.h", "include/parquet/arrow/test_util.h", "include/parquet/arrow/writer.h", "include/parquet/benchmark_util.h", "include/parquet/bloom_filter.h", "include/parquet/bloom_filter_reader.h", "include/parquet/column_page.h", "include/parquet/column_reader.h", "include/parquet/column_scanner.h", "include/parquet/column_writer.h", "include/parquet/encoding.h", "include/parquet/encryption/crypto_factory.h", "include/parquet/encryption/encryption.h", "include/parquet/encryption/file_key_material_store.h", "include/parquet/encryption/file_key_unwrapper.h", "include/parquet/encryption/file_key_wrapper.h", "include/parquet/encryption/file_system_key_material_store.h", "include/parquet/encryption/key_encryption_key.h", "include/parquet/encryption/key_material.h", "include/parquet/encryption/key_metadata.h", "include/parquet/encryption/key_toolkit.h", "include/parquet/encryption/kms_client.h", "include/parquet/encryption/kms_client_factory.h", "include/parquet/encryption/local_wrap_kms_client.h", "include/parquet/encryption/test_encryption_util.h", "include/parquet/encryption/test_in_memory_kms.h", "include/parquet/encryption/two_level_cache_with_expiration.h", "include/parquet/encryption/type_fwd.h", "include/parquet/exception.h", "include/parquet/file_reader.h", "include/parquet/file_writer.h", "include/parquet/hasher.h", "include/parquet/level_comparison.h", "include/parquet/level_comparison_inc.h", "include/parquet/level_conversion.h", "include/parquet/level_conversion_inc.h", "include/parquet/metadata.h", "include/parquet/page_index.h", "include/parquet/parquet_version.h", "include/parquet/pch.h", "include/parquet/platform.h", "include/parquet/printer.h", "include/parquet/properties.h", "include/parquet/schema.h", "include/parquet/size_statistics.h", "include/parquet/statistics.h", "include/parquet/stream_reader.h", "include/parquet/stream_writer.h", "include/parquet/test_util.h", "include/parquet/type_fwd.h", "include/parquet/types.h", "include/parquet/windows_compatibility.h", "include/parquet/windows_fixup.h", "include/parquet/xxhasher.h", "lib/cmake/Parquet/FindThriftAlt.cmake", "lib/cmake/Parquet/ParquetConfig.cmake", "lib/cmake/Parquet/ParquetConfigVersion.cmake", "lib/cmake/Parquet/ParquetTargets-release.cmake", "lib/cmake/Parquet/ParquetTargets.cmake", "lib/libparquet.2000.0.0.dylib", "lib/libparquet.2000.dylib", "lib/libparquet.dylib", "lib/pkgconfig/parquet.pc"], "fn": "libparquet-20.0.0-h283e888_8_cpu.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/libparquet-20.0.0-h283e888_8_cpu", "type": 1}, "md5": "54f21ccc834af1a4ef4be4a97c2efee4", "name": "lib<PERSON><PERSON>", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/libparquet-20.0.0-h283e888_8_cpu.conda", "paths_data": {"paths": [{"_path": "include/parquet/api/io.h", "path_type": "hardlink", "sha256": "46272ad1dd91e105c78866426e3c59ff417f4262aad08adf4e274dbb93685cf2", "sha256_in_prefix": "46272ad1dd91e105c78866426e3c59ff417f4262aad08adf4e274dbb93685cf2", "size_in_bytes": 847}, {"_path": "include/parquet/api/reader.h", "path_type": "hardlink", "sha256": "be73395c33e7d5356c0c993848381bdd95364dae2f76b4730a90d63bdd2b6079", "sha256_in_prefix": "be73395c33e7d5356c0c993848381bdd95364dae2f76b4730a90d63bdd2b6079", "size_in_bytes": 1204}, {"_path": "include/parquet/api/schema.h", "path_type": "hardlink", "sha256": "2ac349e76f69121edb1946b4acb50271f6a723dad6dae4938ab829bcaab485d7", "sha256_in_prefix": "2ac349e76f69121edb1946b4acb50271f6a723dad6dae4938ab829bcaab485d7", "size_in_bytes": 855}, {"_path": "include/parquet/api/writer.h", "path_type": "hardlink", "sha256": "50965b63c40655132d026a33ce3a0cf539c8e20b2caa534550a097070d887ee2", "sha256_in_prefix": "50965b63c40655132d026a33ce3a0cf539c8e20b2caa534550a097070d887ee2", "size_in_bytes": 1007}, {"_path": "include/parquet/arrow/reader.h", "path_type": "hardlink", "sha256": "f5eb83f61620db193b6b04090e82a80cc360d8d85938c73a391de9bbd5cf608c", "sha256_in_prefix": "f5eb83f61620db193b6b04090e82a80cc360d8d85938c73a391de9bbd5cf608c", "size_in_bytes": 17411}, {"_path": "include/parquet/arrow/schema.h", "path_type": "hardlink", "sha256": "322e7aba5ee2b4d4ba3436cca4a3890ae7e31d5a9a498e7fadbb0d44d2c4e7ad", "sha256_in_prefix": "322e7aba5ee2b4d4ba3436cca4a3890ae7e31d5a9a498e7fadbb0d44d2c4e7ad", "size_in_bytes": 6204}, {"_path": "include/parquet/arrow/test_util.h", "path_type": "hardlink", "sha256": "a3268891593726cf7f2cbe6311b5dbcca21a0a25ccb5c05a790926b4432ddfb4", "sha256_in_prefix": "a3268891593726cf7f2cbe6311b5dbcca21a0a25ccb5c05a790926b4432ddfb4", "size_in_bytes": 20573}, {"_path": "include/parquet/arrow/writer.h", "path_type": "hardlink", "sha256": "a2ca71b55edb21e70a838e96dae689c63e93e458f4071c02c77432d2a26e5701", "sha256_in_prefix": "a2ca71b55edb21e70a838e96dae689c63e93e458f4071c02c77432d2a26e5701", "size_in_bytes": 7276}, {"_path": "include/parquet/benchmark_util.h", "path_type": "hardlink", "sha256": "7a60e96886ed7d43652b4336ccc3fea13cb64b6ada43e2e94142cafc6ef83ad3", "sha256_in_prefix": "7a60e96886ed7d43652b4336ccc3fea13cb64b6ada43e2e94142cafc6ef83ad3", "size_in_bytes": 1758}, {"_path": "include/parquet/bloom_filter.h", "path_type": "hardlink", "sha256": "4c2dcec4ad09dafead1f14ff05bc3b9a562d334eb4dca5e00681d1be6cccf5a0", "sha256_in_prefix": "4c2dcec4ad09dafead1f14ff05bc3b9a562d334eb4dca5e00681d1be6cccf5a0", "size_in_bytes": 14999}, {"_path": "include/parquet/bloom_filter_reader.h", "path_type": "hardlink", "sha256": "eb79291d82ace533eb6d16a69012d9b0361b0fe23d51e56117e47c77b24778b8", "sha256_in_prefix": "eb79291d82ace533eb6d16a69012d9b0361b0fe23d51e56117e47c77b24778b8", "size_in_bytes": 2892}, {"_path": "include/parquet/column_page.h", "path_type": "hardlink", "sha256": "ef0da1403aa4221aa91cb461d8b1e4d2e8e7ba49d23c756dbaf276d4b4befa7d", "sha256_in_prefix": "ef0da1403aa4221aa91cb461d8b1e4d2e8e7ba49d23c756dbaf276d4b4befa7d", "size_in_bytes": 6942}, {"_path": "include/parquet/column_reader.h", "path_type": "hardlink", "sha256": "41a81ac34a418aa0e5f5e4da1becb5a93ece633aa276fca2715fa6d99d8c9996", "sha256_in_prefix": "41a81ac34a418aa0e5f5e4da1becb5a93ece633aa276fca2715fa6d99d8c9996", "size_in_bytes": 18923}, {"_path": "include/parquet/column_scanner.h", "path_type": "hardlink", "sha256": "1de701be1fb3d27ff51c9b03f8621d7061d002f0ce1ca94bce9a41f5106c0fdb", "sha256_in_prefix": "1de701be1fb3d27ff51c9b03f8621d7061d002f0ce1ca94bce9a41f5106c0fdb", "size_in_bytes": 8863}, {"_path": "include/parquet/column_writer.h", "path_type": "hardlink", "sha256": "63d54dd5e26db18990561a4bf543e2586ac76e04836c376cd7d6759efff27ce0", "sha256_in_prefix": "63d54dd5e26db18990561a4bf543e2586ac76e04836c376cd7d6759efff27ce0", "size_in_bytes": 12294}, {"_path": "include/parquet/encoding.h", "path_type": "hardlink", "sha256": "3d810e63a78aea7e0987b507eb71af9e47154f06849ae6b690f0800e61a58805", "sha256_in_prefix": "3d810e63a78aea7e0987b507eb71af9e47154f06849ae6b690f0800e61a58805", "size_in_bytes": 16607}, {"_path": "include/parquet/encryption/crypto_factory.h", "path_type": "hardlink", "sha256": "36eb5bca94f0f7def58eef795245ad5be83c4d89d1f784d66409f1e7e7739cf0", "sha256_in_prefix": "36eb5bca94f0f7def58eef795245ad5be83c4d89d1f784d66409f1e7e7739cf0", "size_in_bytes": 7064}, {"_path": "include/parquet/encryption/encryption.h", "path_type": "hardlink", "sha256": "9ceb05d27f15dfb5473105e24bc3343ad41a9c2fbee0db4e8d6ad5b8eed848e4", "sha256_in_prefix": "9ceb05d27f15dfb5473105e24bc3343ad41a9c2fbee0db4e8d6ad5b8eed848e4", "size_in_bytes": 16680}, {"_path": "include/parquet/encryption/file_key_material_store.h", "path_type": "hardlink", "sha256": "6330153b73361f9bf9173d9bfd6966077184e7055b3049c54cbdd2f573c7ea4d", "sha256_in_prefix": "6330153b73361f9bf9173d9bfd6966077184e7055b3049c54cbdd2f573c7ea4d", "size_in_bytes": 2200}, {"_path": "include/parquet/encryption/file_key_unwrapper.h", "path_type": "hardlink", "sha256": "a41df44adf2518468403135cc270e7946b404d3bdcd66b8cccd3aeb1f82acd3f", "sha256_in_prefix": "a41df44adf2518468403135cc270e7946b404d3bdcd66b8cccd3aeb1f82acd3f", "size_in_bytes": 4635}, {"_path": "include/parquet/encryption/file_key_wrapper.h", "path_type": "hardlink", "sha256": "7765b8c4809b491032eda3dee5129a8613e18890dfbf11d8feffe589fabbc2a6", "sha256_in_prefix": "7765b8c4809b491032eda3dee5129a8613e18890dfbf11d8feffe589fabbc2a6", "size_in_bytes": 3762}, {"_path": "include/parquet/encryption/file_system_key_material_store.h", "path_type": "hardlink", "sha256": "f47d5ecb43b72cbe1d83d5557852cdc6567b56bdbadc955a6472954aee2cf0c2", "sha256_in_prefix": "f47d5ecb43b72cbe1d83d5557852cdc6567b56bdbadc955a6472954aee2cf0c2", "size_in_bytes": 3573}, {"_path": "include/parquet/encryption/key_encryption_key.h", "path_type": "hardlink", "sha256": "d1cdd9ad1b9ddafac2bb9cf9d77a1cc8f63196c3f6920d5f43c9b426aafbd358", "sha256_in_prefix": "d1cdd9ad1b9ddafac2bb9cf9d77a1cc8f63196c3f6920d5f43c9b426aafbd358", "size_in_bytes": 2232}, {"_path": "include/parquet/encryption/key_material.h", "path_type": "hardlink", "sha256": "90f4d222e44560e9c7e0108f201df7cc6f61a790f605afb9919565ab7ac59d12", "sha256_in_prefix": "90f4d222e44560e9c7e0108f201df7cc6f61a790f605afb9919565ab7ac59d12", "size_in_bytes": 6221}, {"_path": "include/parquet/encryption/key_metadata.h", "path_type": "hardlink", "sha256": "3dcd2703d2d6dc573d34b30cc73ee8b1bc3cb22da34a23954c44be27ef51d32d", "sha256_in_prefix": "3dcd2703d2d6dc573d34b30cc73ee8b1bc3cb22da34a23954c44be27ef51d32d", "size_in_bytes": 4003}, {"_path": "include/parquet/encryption/key_toolkit.h", "path_type": "hardlink", "sha256": "1cf69b23ca859c8320c596211e05c2cd85742d4d5ce72275eb18d4c76d48f5bd", "sha256_in_prefix": "1cf69b23ca859c8320c596211e05c2cd85742d4d5ce72275eb18d4c76d48f5bd", "size_in_bytes": 4577}, {"_path": "include/parquet/encryption/kms_client.h", "path_type": "hardlink", "sha256": "0f7e29547ce409b5aa2a73c80587ece9c38dc66b98cf22d24bdf82e76645873d", "sha256_in_prefix": "0f7e29547ce409b5aa2a73c80587ece9c38dc66b98cf22d24bdf82e76645873d", "size_in_bytes": 3151}, {"_path": "include/parquet/encryption/kms_client_factory.h", "path_type": "hardlink", "sha256": "559f7b08c803431c79a195851a9ae35ec68cd61674c0db9d3e6154d7f9678807", "sha256_in_prefix": "559f7b08c803431c79a195851a9ae35ec68cd61674c0db9d3e6154d7f9678807", "size_in_bytes": 1293}, {"_path": "include/parquet/encryption/local_wrap_kms_client.h", "path_type": "hardlink", "sha256": "5d9c6411cb74f93bfddd50e975af6c0e8bb5929f6a91328cc6bdfa6e955c23cb", "sha256_in_prefix": "5d9c6411cb74f93bfddd50e975af6c0e8bb5929f6a91328cc6bdfa6e955c23cb", "size_in_bytes": 3954}, {"_path": "include/parquet/encryption/test_encryption_util.h", "path_type": "hardlink", "sha256": "cc819e50b79338253537e5d81ddbc8a69b61e709e74d81307f687e3ae4dc4194", "sha256_in_prefix": "cc819e50b79338253537e5d81ddbc8a69b61e709e74d81307f687e3ae4dc4194", "size_in_bytes": 5209}, {"_path": "include/parquet/encryption/test_in_memory_kms.h", "path_type": "hardlink", "sha256": "8d873958fb2b87fc1c69a6967237f6dc66e2c9eddafdb760daba3251fba4584b", "sha256_in_prefix": "8d873958fb2b87fc1c69a6967237f6dc66e2c9eddafdb760daba3251fba4584b", "size_in_bytes": 3521}, {"_path": "include/parquet/encryption/two_level_cache_with_expiration.h", "path_type": "hardlink", "sha256": "72e1db5fd801596c9dd083d73558cc987c633f0ee89984e7b3821fe188418123", "sha256_in_prefix": "72e1db5fd801596c9dd083d73558cc987c633f0ee89984e7b3821fe188418123", "size_in_bytes": 5075}, {"_path": "include/parquet/encryption/type_fwd.h", "path_type": "hardlink", "sha256": "74bf2c9f2530363853404d854367570148d36e8117847d8939e850a2f1df8b17", "sha256_in_prefix": "74bf2c9f2530363853404d854367570148d36e8117847d8939e850a2f1df8b17", "size_in_bytes": 955}, {"_path": "include/parquet/exception.h", "path_type": "hardlink", "sha256": "c9ce40de232a33d3f9f619e3b98f155d422817f26f6d954f1ccebfc0fb60e1c2", "sha256_in_prefix": "c9ce40de232a33d3f9f619e3b98f155d422817f26f6d954f1ccebfc0fb60e1c2", "size_in_bytes": 5599}, {"_path": "include/parquet/file_reader.h", "path_type": "hardlink", "sha256": "67546238fef863db4cbfb919ec5597d39211d39ab381239344aad8ef5a4b3891", "sha256_in_prefix": "67546238fef863db4cbfb919ec5597d39211d39ab381239344aad8ef5a4b3891", "size_in_bytes": 11185}, {"_path": "include/parquet/file_writer.h", "path_type": "hardlink", "sha256": "e9f2ba327f8c76243e278a28f014e2fde55556c8657dfa10889cc568b46ba9ca", "sha256_in_prefix": "e9f2ba327f8c76243e278a28f014e2fde55556c8657dfa10889cc568b46ba9ca", "size_in_bytes": 9343}, {"_path": "include/parquet/hasher.h", "path_type": "hardlink", "sha256": "1d26351233c3db1c7f741f47b4083e9572fb841e23f4c0c4d1c02547bbb434e7", "sha256_in_prefix": "1d26351233c3db1c7f741f47b4083e9572fb841e23f4c0c4d1c02547bbb434e7", "size_in_bytes": 5227}, {"_path": "include/parquet/level_comparison.h", "path_type": "hardlink", "sha256": "e73e1f50924f5aaf56eb49760ac008ed3ec4d9ab86603ee6d1fa51e6b7cb9acc", "sha256_in_prefix": "e73e1f50924f5aaf56eb49760ac008ed3ec4d9ab86603ee6d1fa51e6b7cb9acc", "size_in_bytes": 1306}, {"_path": "include/parquet/level_comparison_inc.h", "path_type": "hardlink", "sha256": "af6d3fe91bf92fb52614627af1ff89699e612d76fcef0bd96bcd2164d42817e2", "sha256_in_prefix": "af6d3fe91bf92fb52614627af1ff89699e612d76fcef0bd96bcd2164d42817e2", "size_in_bytes": 2494}, {"_path": "include/parquet/level_conversion.h", "path_type": "hardlink", "sha256": "3acbaa2b5c6252712738b3cac1f9bd5fea574da5d13250c8923de5c066f68202", "sha256_in_prefix": "3acbaa2b5c6252712738b3cac1f9bd5fea574da5d13250c8923de5c066f68202", "size_in_bytes": 9432}, {"_path": "include/parquet/level_conversion_inc.h", "path_type": "hardlink", "sha256": "d2bd867ddfc532274b1850bf6bc920a42f5b9d4b76f8805b027f506d0153ad3a", "sha256_in_prefix": "d2bd867ddfc532274b1850bf6bc920a42f5b9d4b76f8805b027f506d0153ad3a", "size_in_bytes": 14161}, {"_path": "include/parquet/metadata.h", "path_type": "hardlink", "sha256": "591dd90ea24cdb5267d5f93d93c07896bc669f66aa5cb5a964ef70e6dc932a9a", "sha256_in_prefix": "591dd90ea24cdb5267d5f93d93c07896bc669f66aa5cb5a964ef70e6dc932a9a", "size_in_bytes": 20634}, {"_path": "include/parquet/page_index.h", "path_type": "hardlink", "sha256": "02abcb4fa33cd63635dd86e95feb3e84ecc2573d0018d91927da26dffb69e159", "sha256_in_prefix": "02abcb4fa33cd63635dd86e95feb3e84ecc2573d0018d91927da26dffb69e159", "size_in_bytes": 17307}, {"_path": "include/parquet/parquet_version.h", "path_type": "hardlink", "sha256": "62b0cf6102847bd8e6c1a2402728ddc204014c01ad2f3d1a9ad15a0e45aa945c", "sha256_in_prefix": "62b0cf6102847bd8e6c1a2402728ddc204014c01ad2f3d1a9ad15a0e45aa945c", "size_in_bytes": 1164}, {"_path": "include/parquet/pch.h", "path_type": "hardlink", "sha256": "cc87648d94b892e158adadf0a063239af6170b007821a5dda66fe7479373f219", "sha256_in_prefix": "cc87648d94b892e158adadf0a063239af6170b007821a5dda66fe7479373f219", "size_in_bytes": 1249}, {"_path": "include/parquet/platform.h", "path_type": "hardlink", "sha256": "552d331140b8777ecb4269502d067968735a8b045ff10af18b15dd59fef79b94", "sha256_in_prefix": "552d331140b8777ecb4269502d067968735a8b045ff10af18b15dd59fef79b94", "size_in_bytes": 3898}, {"_path": "include/parquet/printer.h", "path_type": "hardlink", "sha256": "857ab3c2e8f9a75a0a6366b066454eabba97c7e46ca28e903c7d684c4f780075", "sha256_in_prefix": "857ab3c2e8f9a75a0a6366b066454eabba97c7e46ca28e903c7d684c4f780075", "size_in_bytes": 1548}, {"_path": "include/parquet/properties.h", "path_type": "hardlink", "sha256": "b97089f4e08ada098e610cbc31260660de64a9fcb0e15931416e5918c6604d52", "sha256_in_prefix": "b97089f4e08ada098e610cbc31260660de64a9fcb0e15931416e5918c6604d52", "size_in_bytes": 48749}, {"_path": "include/parquet/schema.h", "path_type": "hardlink", "sha256": "0a3661da2f5637955ea036cbab2eccd4066da29678dff0bd6e5586dce4f621f5", "sha256_in_prefix": "0a3661da2f5637955ea036cbab2eccd4066da29678dff0bd6e5586dce4f621f5", "size_in_bytes": 18222}, {"_path": "include/parquet/size_statistics.h", "path_type": "hardlink", "sha256": "021afcab2215adef14fc95512c72f405b4a86297498e8d8e51e135503e08d6dc", "sha256_in_prefix": "021afcab2215adef14fc95512c72f405b4a86297498e8d8e51e135503e08d6dc", "size_in_bytes": 4327}, {"_path": "include/parquet/statistics.h", "path_type": "hardlink", "sha256": "d2c93b9285ec96ef8ab950ba0ac4e21550f516efd958f0ff14b85c5c02dab3f8", "sha256_in_prefix": "d2c93b9285ec96ef8ab950ba0ac4e21550f516efd958f0ff14b85c5c02dab3f8", "size_in_bytes": 15176}, {"_path": "include/parquet/stream_reader.h", "path_type": "hardlink", "sha256": "d5698dd2f602a93cf52f06ff0e2e313d64c4f956c2410bb366b6a54a95909b75", "sha256_in_prefix": "d5698dd2f602a93cf52f06ff0e2e313d64c4f956c2410bb366b6a54a95909b75", "size_in_bytes": 8791}, {"_path": "include/parquet/stream_writer.h", "path_type": "hardlink", "sha256": "9f0fefde786b2faf36a336762992959079ceb23c1ec5b9815d357608a72ae184", "sha256_in_prefix": "9f0fefde786b2faf36a336762992959079ceb23c1ec5b9815d357608a72ae184", "size_in_bytes": 7505}, {"_path": "include/parquet/test_util.h", "path_type": "hardlink", "sha256": "8242683a5fcde1c1b72f9eae5d5262d512e20d5065ef7c97d350e4c763e5b7d8", "sha256_in_prefix": "8242683a5fcde1c1b72f9eae5d5262d512e20d5065ef7c97d350e4c763e5b7d8", "size_in_bytes": 31180}, {"_path": "include/parquet/type_fwd.h", "path_type": "hardlink", "sha256": "6c0df499692277049d198339666da678ccdbbb3924e5cd6e478da40ff8503329", "sha256_in_prefix": "6c0df499692277049d198339666da678ccdbbb3924e5cd6e478da40ff8503329", "size_in_bytes": 2964}, {"_path": "include/parquet/types.h", "path_type": "hardlink", "sha256": "2056ca94fd1a6735bc0a7e14d1008819ba156fc84e9c3e94bd21a2e84aa9bef1", "sha256_in_prefix": "2056ca94fd1a6735bc0a7e14d1008819ba156fc84e9c3e94bd21a2e84aa9bef1", "size_in_bytes": 25482}, {"_path": "include/parquet/windows_compatibility.h", "path_type": "hardlink", "sha256": "c481061d6df9e144607483fd038577d374c92fc035224084be9d3c6cc2ac1d35", "sha256_in_prefix": "c481061d6df9e144607483fd038577d374c92fc035224084be9d3c6cc2ac1d35", "size_in_bytes": 897}, {"_path": "include/parquet/windows_fixup.h", "path_type": "hardlink", "sha256": "0e9c960b2c31f1822aa2eba7e8125c80cac714c4c2060a3059d2759a7267436b", "sha256_in_prefix": "0e9c960b2c31f1822aa2eba7e8125c80cac714c4c2060a3059d2759a7267436b", "size_in_bytes": 1052}, {"_path": "include/parquet/xxhasher.h", "path_type": "hardlink", "sha256": "4006bb644ed2dd416d53f568cf7a22dd872521885b86f88991a7cb398822b968", "sha256_in_prefix": "4006bb644ed2dd416d53f568cf7a22dd872521885b86f88991a7cb398822b968", "size_in_bytes": 2074}, {"_path": "lib/cmake/Parquet/FindThriftAlt.cmake", "path_type": "hardlink", "sha256": "b1f23078d7ba308077d6dcfcb34cb3b6699d99e0c32e8dc415b5d9393ecd1d7d", "sha256_in_prefix": "b1f23078d7ba308077d6dcfcb34cb3b6699d99e0c32e8dc415b5d9393ecd1d7d", "size_in_bytes": 6572}, {"_path": "lib/cmake/Parquet/ParquetConfig.cmake", "path_type": "hardlink", "sha256": "04c1ccab7ccc92d91728cf6ed1eae9d11f6ca29fbefc05d73c97be3eb632bf34", "sha256_in_prefix": "04c1ccab7ccc92d91728cf6ed1eae9d11f6ca29fbefc05d73c97be3eb632bf34", "size_in_bytes": 2703}, {"_path": "lib/cmake/Parquet/ParquetConfigVersion.cmake", "path_type": "hardlink", "sha256": "4eb556774449e5062cea02eaaab6b824572fe8dadcdfb258fc6e518fac98006b", "sha256_in_prefix": "4eb556774449e5062cea02eaaab6b824572fe8dadcdfb258fc6e518fac98006b", "size_in_bytes": 2765}, {"_path": "lib/cmake/Parquet/ParquetTargets-release.cmake", "path_type": "hardlink", "sha256": "4b66bf9c381955f50e77e4eb88b08167572d9749d4efb3490e4c5baa909c6bca", "sha256_in_prefix": "4b66bf9c381955f50e77e4eb88b08167572d9749d4efb3490e4c5baa909c6bca", "size_in_bytes": 1208}, {"_path": "lib/cmake/Parquet/ParquetTargets.cmake", "path_type": "hardlink", "sha256": "741df7f8268d216b919451e697122638d2bad5b4ec53cfb47f83f2f06a6816ea", "sha256_in_prefix": "741df7f8268d216b919451e697122638d2bad5b4ec53cfb47f83f2f06a6816ea", "size_in_bytes": 4206}, {"_path": "lib/libparquet.2000.0.0.dylib", "path_type": "hardlink", "sha256": "5748e9e962e6eb915bce40faf4dd1471a34b2becc3739d2ae9100c21ee53bda5", "sha256_in_prefix": "5748e9e962e6eb915bce40faf4dd1471a34b2becc3739d2ae9100c21ee53bda5", "size_in_bytes": 4276704}, {"_path": "lib/libparquet.2000.dylib", "path_type": "softlink", "sha256": "5748e9e962e6eb915bce40faf4dd1471a34b2becc3739d2ae9100c21ee53bda5", "size_in_bytes": 4276704}, {"_path": "lib/libparquet.dylib", "path_type": "softlink", "sha256": "5748e9e962e6eb915bce40faf4dd1471a34b2becc3739d2ae9100c21ee53bda5", "size_in_bytes": 4276704}, {"_path": "lib/pkgconfig/parquet.pc", "file_mode": "text", "path_type": "hardlink", "prefix_placeholder": "/Users/<USER>/miniforge3/conda-bld/apache-arrow_1751095169775/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placeh", "sha256": "e7ea6013310357713a27951ceec045c4fe50490b5b7960f4a6b281573647b6ab", "sha256_in_prefix": "ad2f5cc12cecbe3e37f649d2c92de81e83fe6dae4af7ce222fbe894a7a0aecd5", "size_in_bytes": 1356}], "paths_version": 1}, "requested_spec": "None", "sha256": "b7cb5ad7ce02908c173172e5bb4b651427849f491e1cf4006791dd353aae4a2c", "size": 965148, "subdir": "osx-64", "timestamp": 1751097979000, "url": "https://conda.anaconda.org/conda-forge/osx-64/libparquet-20.0.0-h283e888_8_cpu.conda", "version": "20.0.0"}