{"build": "pyhd8ed1ab_0", "build_number": 0, "channel": "https://conda.anaconda.org/conda-forge/noarch", "constrains": [], "depends": ["protobuf <7.0,>=5.0", "python >=3.10"], "extracted_package_dir": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-proto-1.37.0-pyhd8ed1ab_0", "files": ["lib/python3.11/site-packages/opentelemetry/proto/__init__.py", "lib/python3.11/site-packages/opentelemetry/proto/collector/__init__.py", "lib/python3.11/site-packages/opentelemetry/proto/collector/logs/v1/logs_service_pb2.py", "lib/python3.11/site-packages/opentelemetry/proto/collector/logs/v1/logs_service_pb2.pyi", "lib/python3.11/site-packages/opentelemetry/proto/collector/logs/v1/logs_service_pb2_grpc.py", "lib/python3.11/site-packages/opentelemetry/proto/collector/metrics/__init__.py", "lib/python3.11/site-packages/opentelemetry/proto/collector/metrics/v1/__init__.py", "lib/python3.11/site-packages/opentelemetry/proto/collector/metrics/v1/metrics_service_pb2.py", "lib/python3.11/site-packages/opentelemetry/proto/collector/metrics/v1/metrics_service_pb2.pyi", "lib/python3.11/site-packages/opentelemetry/proto/collector/metrics/v1/metrics_service_pb2_grpc.py", "lib/python3.11/site-packages/opentelemetry/proto/collector/profiles/v1development/profiles_service_pb2.py", "lib/python3.11/site-packages/opentelemetry/proto/collector/profiles/v1development/profiles_service_pb2.pyi", "lib/python3.11/site-packages/opentelemetry/proto/collector/profiles/v1development/profiles_service_pb2_grpc.py", "lib/python3.11/site-packages/opentelemetry/proto/collector/trace/__init__.py", "lib/python3.11/site-packages/opentelemetry/proto/collector/trace/v1/__init__.py", "lib/python3.11/site-packages/opentelemetry/proto/collector/trace/v1/trace_service_pb2.py", "lib/python3.11/site-packages/opentelemetry/proto/collector/trace/v1/trace_service_pb2.pyi", "lib/python3.11/site-packages/opentelemetry/proto/collector/trace/v1/trace_service_pb2_grpc.py", "lib/python3.11/site-packages/opentelemetry/proto/common/__init__.py", "lib/python3.11/site-packages/opentelemetry/proto/common/v1/__init__.py", "lib/python3.11/site-packages/opentelemetry/proto/common/v1/common_pb2.py", "lib/python3.11/site-packages/opentelemetry/proto/common/v1/common_pb2.pyi", "lib/python3.11/site-packages/opentelemetry/proto/logs/v1/logs_pb2.py", "lib/python3.11/site-packages/opentelemetry/proto/logs/v1/logs_pb2.pyi", "lib/python3.11/site-packages/opentelemetry/proto/metrics/__init__.py", "lib/python3.11/site-packages/opentelemetry/proto/metrics/v1/__init__.py", "lib/python3.11/site-packages/opentelemetry/proto/metrics/v1/metrics_pb2.py", "lib/python3.11/site-packages/opentelemetry/proto/metrics/v1/metrics_pb2.pyi", "lib/python3.11/site-packages/opentelemetry/proto/profiles/v1development/profiles_pb2.py", "lib/python3.11/site-packages/opentelemetry/proto/profiles/v1development/profiles_pb2.pyi", "lib/python3.11/site-packages/opentelemetry/proto/py.typed", "lib/python3.11/site-packages/opentelemetry/proto/resource/__init__.py", "lib/python3.11/site-packages/opentelemetry/proto/resource/v1/__init__.py", "lib/python3.11/site-packages/opentelemetry/proto/resource/v1/resource_pb2.py", "lib/python3.11/site-packages/opentelemetry/proto/resource/v1/resource_pb2.pyi", "lib/python3.11/site-packages/opentelemetry/proto/trace/__init__.py", "lib/python3.11/site-packages/opentelemetry/proto/trace/v1/__init__.py", "lib/python3.11/site-packages/opentelemetry/proto/trace/v1/trace_pb2.py", "lib/python3.11/site-packages/opentelemetry/proto/trace/v1/trace_pb2.pyi", "lib/python3.11/site-packages/opentelemetry/proto/version/__init__.py", "lib/python3.11/site-packages/opentelemetry_proto-1.37.0.dist-info/INSTALLER", "lib/python3.11/site-packages/opentelemetry_proto-1.37.0.dist-info/METADATA", "lib/python3.11/site-packages/opentelemetry_proto-1.37.0.dist-info/RECORD", "lib/python3.11/site-packages/opentelemetry_proto-1.37.0.dist-info/REQUESTED", "lib/python3.11/site-packages/opentelemetry_proto-1.37.0.dist-info/WHEEL", "lib/python3.11/site-packages/opentelemetry_proto-1.37.0.dist-info/direct_url.json", "lib/python3.11/site-packages/opentelemetry_proto-1.37.0.dist-info/licenses/LICENSE", "lib/python3.11/site-packages/opentelemetry/proto/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/collector/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/collector/logs/v1/__pycache__/logs_service_pb2.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/collector/logs/v1/__pycache__/logs_service_pb2_grpc.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/collector/metrics/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/collector/metrics/v1/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/collector/metrics/v1/__pycache__/metrics_service_pb2.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/collector/metrics/v1/__pycache__/metrics_service_pb2_grpc.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/collector/profiles/v1development/__pycache__/profiles_service_pb2.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/collector/profiles/v1development/__pycache__/profiles_service_pb2_grpc.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/collector/trace/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/collector/trace/v1/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/collector/trace/v1/__pycache__/trace_service_pb2.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/collector/trace/v1/__pycache__/trace_service_pb2_grpc.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/common/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/common/v1/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/common/v1/__pycache__/common_pb2.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/logs/v1/__pycache__/logs_pb2.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/metrics/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/metrics/v1/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/metrics/v1/__pycache__/metrics_pb2.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/profiles/v1development/__pycache__/profiles_pb2.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/resource/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/resource/v1/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/resource/v1/__pycache__/resource_pb2.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/trace/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/trace/v1/__pycache__/__init__.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/trace/v1/__pycache__/trace_pb2.cpython-311.pyc", "lib/python3.11/site-packages/opentelemetry/proto/version/__pycache__/__init__.cpython-311.pyc"], "fn": "opentelemetry-proto-1.37.0-pyhd8ed1ab_0.conda", "license": "Apache-2.0", "link": {"source": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-proto-1.37.0-pyhd8ed1ab_0", "type": 1}, "md5": "a7706ad50049269511d2ab1a8e31ea59", "name": "opentelemetry-proto", "noarch": "python", "package_tarball_full_path": "/usr/local/Caskroom/miniconda/base/pkgs/opentelemetry-proto-1.37.0-pyhd8ed1ab_0.conda", "package_type": "noarch_python", "paths_data": {"paths": [{"_path": "site-packages/opentelemetry/proto/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/proto/collector/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/proto/collector/logs/v1/logs_service_pb2.py", "path_type": "hardlink", "sha256": "bf3a98f991f639b3f3820e37a387413cf0cf904cd1e2e2adb1178148aa7da575", "sha256_in_prefix": "bf3a98f991f639b3f3820e37a387413cf0cf904cd1e2e2adb1178148aa7da575", "size_in_bytes": 2649}, {"_path": "site-packages/opentelemetry/proto/collector/logs/v1/logs_service_pb2.pyi", "path_type": "hardlink", "sha256": "7f5ae7c8094082fae004f579dc750757f2de7db372664b63b1a0debc18b66c25", "sha256_in_prefix": "7f5ae7c8094082fae004f579dc750757f2de7db372664b63b1a0debc18b66c25", "size_in_bytes": 4942}, {"_path": "site-packages/opentelemetry/proto/collector/logs/v1/logs_service_pb2_grpc.py", "path_type": "hardlink", "sha256": "8f8d8e07fd57071d58b00428bf33f7b69156f99c362a28d12abe4663ce9ddeeb", "sha256_in_prefix": "8f8d8e07fd57071d58b00428bf33f7b69156f99c362a28d12abe4663ce9ddeeb", "size_in_bytes": 4778}, {"_path": "site-packages/opentelemetry/proto/collector/metrics/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/proto/collector/metrics/v1/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/proto/collector/metrics/v1/metrics_service_pb2.py", "path_type": "hardlink", "sha256": "98b9d549154bc260e84234892b11d62903492a7ae1e2b3a3d13bde1d6d03cb62", "sha256_in_prefix": "98b9d549154bc260e84234892b11d62903492a7ae1e2b3a3d13bde1d6d03cb62", "size_in_bytes": 2785}, {"_path": "site-packages/opentelemetry/proto/collector/metrics/v1/metrics_service_pb2.pyi", "path_type": "hardlink", "sha256": "deebcc87285b10fe68df65d01ff974070e631129f419ac479b933c8a820f7219", "sha256_in_prefix": "deebcc87285b10fe68df65d01ff974070e631129f419ac479b933c8a820f7219", "size_in_bytes": 5017}, {"_path": "site-packages/opentelemetry/proto/collector/metrics/v1/metrics_service_pb2_grpc.py", "path_type": "hardlink", "sha256": "59658d92caa517274ffa19beba68dd239e418943294d6c10a355864859163c62", "sha256_in_prefix": "59658d92caa517274ffa19beba68dd239e418943294d6c10a355864859163c62", "size_in_bytes": 4679}, {"_path": "site-packages/opentelemetry/proto/collector/profiles/v1development/profiles_service_pb2.py", "path_type": "hardlink", "sha256": "bbda661cb8466ffd7164250b50e126a42a411c15f9c60c5bec0837029d113c26", "sha256_in_prefix": "bbda661cb8466ffd7164250b50e126a42a411c15f9c60c5bec0837029d113c26", "size_in_bytes": 3129}, {"_path": "site-packages/opentelemetry/proto/collector/profiles/v1development/profiles_service_pb2.pyi", "path_type": "hardlink", "sha256": "7d3f1a993599a97c3565d2eebc98758c8ed79a48b5727335d412c454357ccd0a", "sha256_in_prefix": "7d3f1a993599a97c3565d2eebc98758c8ed79a48b5727335d412c454357ccd0a", "size_in_bytes": 5572}, {"_path": "site-packages/opentelemetry/proto/collector/profiles/v1development/profiles_service_pb2_grpc.py", "path_type": "hardlink", "sha256": "da2c554ce995de40a8dc051e688a5b0aba7a4922ed3dc9336074b191d65f246b", "sha256_in_prefix": "da2c554ce995de40a8dc051e688a5b0aba7a4922ed3dc9336074b191d65f246b", "size_in_bytes": 4836}, {"_path": "site-packages/opentelemetry/proto/collector/trace/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/proto/collector/trace/v1/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/proto/collector/trace/v1/trace_service_pb2.py", "path_type": "hardlink", "sha256": "ffe329abb6b9e85e737eeb7eebb8b8fae99688c88541b883de4974f8aafe97ff", "sha256_in_prefix": "ffe329abb6b9e85e737eeb7eebb8b8fae99688c88541b883de4974f8aafe97ff", "size_in_bytes": 2698}, {"_path": "site-packages/opentelemetry/proto/collector/trace/v1/trace_service_pb2.pyi", "path_type": "hardlink", "sha256": "1b39520c46db78c28acdececbd09cb4d7440a5f53eb1c414678e3a91c46d6cf0", "sha256_in_prefix": "1b39520c46db78c28acdececbd09cb4d7440a5f53eb1c414678e3a91c46d6cf0", "size_in_bytes": 4931}, {"_path": "site-packages/opentelemetry/proto/collector/trace/v1/trace_service_pb2_grpc.py", "path_type": "hardlink", "sha256": "82f4704d4fc69da3464a2e150b902e3933a65effae7a61856113a64d5ed8896f", "sha256_in_prefix": "82f4704d4fc69da3464a2e150b902e3933a65effae7a61856113a64d5ed8896f", "size_in_bytes": 4812}, {"_path": "site-packages/opentelemetry/proto/common/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/proto/common/v1/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/proto/common/v1/common_pb2.py", "path_type": "hardlink", "sha256": "81ab7c87bed1e77a3f4d1740339440db9341510a9e88cfa34fc4185cab68eca6", "sha256_in_prefix": "81ab7c87bed1e77a3f4d1740339440db9341510a9e88cfa34fc4185cab68eca6", "size_in_bytes": 3142}, {"_path": "site-packages/opentelemetry/proto/common/v1/common_pb2.pyi", "path_type": "hardlink", "sha256": "ae861df2af410f30f5a59574df32c39543e9be186519b06261d846d8ecd438a1", "sha256_in_prefix": "ae861df2af410f30f5a59574df32c39543e9be186519b06261d846d8ecd438a1", "size_in_bytes": 10035}, {"_path": "site-packages/opentelemetry/proto/logs/v1/logs_pb2.py", "path_type": "hardlink", "sha256": "164d4c73a1cc452027759f636d37f899f97b0dc84972d428671376bf155e3c4e", "sha256_in_prefix": "164d4c73a1cc452027759f636d37f899f97b0dc84972d428671376bf155e3c4e", "size_in_bytes": 4709}, {"_path": "site-packages/opentelemetry/proto/logs/v1/logs_pb2.pyi", "path_type": "hardlink", "sha256": "69541de834de8da1d64da62976c53932887c03cb5af84ee4234b9665a944be02", "sha256_in_prefix": "69541de834de8da1d64da62976c53932887c03cb5af84ee4234b9665a944be02", "size_in_bytes": 17518}, {"_path": "site-packages/opentelemetry/proto/metrics/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/proto/metrics/v1/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/proto/metrics/v1/metrics_pb2.py", "path_type": "hardlink", "sha256": "dc574a568d66d3572e274eeffd09ce4ed4fed485d349e6437f5062a29b372566", "sha256_in_prefix": "dc574a568d66d3572e274eeffd09ce4ed4fed485d349e6437f5062a29b372566", "size_in_bytes": 9468}, {"_path": "site-packages/opentelemetry/proto/metrics/v1/metrics_pb2.pyi", "path_type": "hardlink", "sha256": "87e09aa69e3459845c1c5470ffc5e756725c61ad19f187c72a3bcc4a4e84934e", "sha256_in_prefix": "87e09aa69e3459845c1c5470ffc5e756725c61ad19f187c72a3bcc4a4e84934e", "size_in_bytes": 55664}, {"_path": "site-packages/opentelemetry/proto/profiles/v1development/profiles_pb2.py", "path_type": "hardlink", "sha256": "909654ff53edba0c0338dd757851ff6831b261ae5523202f81b9a823012ec6b5", "sha256_in_prefix": "909654ff53edba0c0338dd757851ff6831b261ae5523202f81b9a823012ec6b5", "size_in_bytes": 7604}, {"_path": "site-packages/opentelemetry/proto/profiles/v1development/profiles_pb2.pyi", "path_type": "hardlink", "sha256": "806f565d6d9ccbebf123b7e5280a174baffce33962ad518c0d38448316abb349", "sha256_in_prefix": "806f565d6d9ccbebf123b7e5280a174baffce33962ad518c0d38448316abb349", "size_in_bytes": 43189}, {"_path": "site-packages/opentelemetry/proto/py.typed", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/proto/resource/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/proto/resource/v1/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/proto/resource/v1/resource_pb2.py", "path_type": "hardlink", "sha256": "f3689981f1b59f1d172d1efbdb68def69d58e99793558590807e9984441c08f5", "sha256_in_prefix": "f3689981f1b59f1d172d1efbdb68def69d58e99793558590807e9984441c08f5", "size_in_bytes": 1864}, {"_path": "site-packages/opentelemetry/proto/resource/v1/resource_pb2.pyi", "path_type": "hardlink", "sha256": "1aab70047e6c640fda4713cf5ab37e66f71d7ac76a4983bc003f42dc93b40778", "sha256_in_prefix": "1aab70047e6c640fda4713cf5ab37e66f71d7ac76a4983bc003f42dc93b40778", "size_in_bytes": 2745}, {"_path": "site-packages/opentelemetry/proto/trace/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/proto/trace/v1/__init__.py", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry/proto/trace/v1/trace_pb2.py", "path_type": "hardlink", "sha256": "1600fadc0eeea5662bc3469e35edea271ae0371376dc897e81a97c4d363db919", "sha256_in_prefix": "1600fadc0eeea5662bc3469e35edea271ae0371376dc897e81a97c4d363db919", "size_in_bytes": 5493}, {"_path": "site-packages/opentelemetry/proto/trace/v1/trace_pb2.pyi", "path_type": "hardlink", "sha256": "f942b649490d870ad6969bcf9824a5eb12e2ef257efd73033861707920ea1f8d", "sha256_in_prefix": "f942b649490d870ad6969bcf9824a5eb12e2ef257efd73033861707920ea1f8d", "size_in_bytes": 28427}, {"_path": "site-packages/opentelemetry/proto/version/__init__.py", "path_type": "hardlink", "sha256": "420f83b16cfb888dea77dc3e0d220cfd48ae5a7b8b63a49afd9ea2c88e9d3518", "sha256_in_prefix": "420f83b16cfb888dea77dc3e0d220cfd48ae5a7b8b63a49afd9ea2c88e9d3518", "size_in_bytes": 608}, {"_path": "site-packages/opentelemetry_proto-1.37.0.dist-info/INSTALLER", "path_type": "hardlink", "sha256": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "sha256_in_prefix": "d0edee15f91b406f3f99726e44eb990be6e34fd0345b52b910c568e0eef6a2a8", "size_in_bytes": 5}, {"_path": "site-packages/opentelemetry_proto-1.37.0.dist-info/METADATA", "path_type": "hardlink", "sha256": "53bc03c52d2963184f57bce77c1e98087a4f8fdbcf42e6cb795f0b0827f9bf70", "sha256_in_prefix": "53bc03c52d2963184f57bce77c1e98087a4f8fdbcf42e6cb795f0b0827f9bf70", "size_in_bytes": 2315}, {"_path": "site-packages/opentelemetry_proto-1.37.0.dist-info/RECORD", "path_type": "hardlink", "sha256": "de37dbac391bb1136d0daff58cc6a9eabc114422fb9453eb4d45ef4e239149f0", "sha256_in_prefix": "de37dbac391bb1136d0daff58cc6a9eabc114422fb9453eb4d45ef4e239149f0", "size_in_bytes": 7221}, {"_path": "site-packages/opentelemetry_proto-1.37.0.dist-info/REQUESTED", "path_type": "hardlink", "sha256": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "sha256_in_prefix": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "size_in_bytes": 0}, {"_path": "site-packages/opentelemetry_proto-1.37.0.dist-info/WHEEL", "path_type": "hardlink", "sha256": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "sha256_in_prefix": "aad0b0a12256807936d52d4a6f88a1773236ae527564a688bab4e3fe780e8724", "size_in_bytes": 87}, {"_path": "site-packages/opentelemetry_proto-1.37.0.dist-info/direct_url.json", "path_type": "hardlink", "sha256": "24f98ae6977c797dfa7df00d1c5605f1b5be51a79270aded29f175760edd626b", "sha256_in_prefix": "24f98ae6977c797dfa7df00d1c5605f1b5be51a79270aded29f175760edd626b", "size_in_bytes": 115}, {"_path": "site-packages/opentelemetry_proto-1.37.0.dist-info/licenses/LICENSE", "path_type": "hardlink", "sha256": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "sha256_in_prefix": "c71d239df91726fc519c6eb72d318ec65820627232b2f796219e87dcf35d0ab4", "size_in_bytes": 11357}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/collector/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/collector/logs/v1/__pycache__/logs_service_pb2.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/collector/logs/v1/__pycache__/logs_service_pb2_grpc.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/collector/metrics/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/collector/metrics/v1/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/collector/metrics/v1/__pycache__/metrics_service_pb2.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/collector/metrics/v1/__pycache__/metrics_service_pb2_grpc.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/collector/profiles/v1development/__pycache__/profiles_service_pb2.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/collector/profiles/v1development/__pycache__/profiles_service_pb2_grpc.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/collector/trace/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/collector/trace/v1/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/collector/trace/v1/__pycache__/trace_service_pb2.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/collector/trace/v1/__pycache__/trace_service_pb2_grpc.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/common/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/common/v1/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/common/v1/__pycache__/common_pb2.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/logs/v1/__pycache__/logs_pb2.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/metrics/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/metrics/v1/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/metrics/v1/__pycache__/metrics_pb2.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/profiles/v1development/__pycache__/profiles_pb2.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/resource/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/resource/v1/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/resource/v1/__pycache__/resource_pb2.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/trace/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/trace/v1/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/trace/v1/__pycache__/trace_pb2.cpython-311.pyc", "path_type": "pyc_file"}, {"_path": "lib/python3.11/site-packages/opentelemetry/proto/version/__pycache__/__init__.cpython-311.pyc", "path_type": "pyc_file"}], "paths_version": 1}, "requested_spec": "None", "sha256": "900f9e1dd176dcfb4f801830fa8005fee9b394796e66a4501a9cdd48103ae07b", "size": 45727, "subdir": "noarch", "timestamp": 1757680253000, "url": "https://conda.anaconda.org/conda-forge/noarch/opentelemetry-proto-1.37.0-pyhd8ed1ab_0.conda", "version": "1.37.0"}